#!/bin/bash
#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
#
# Apache control script designed to allow an easy command line interface
# to controlling Apache.  Written by <PERSON>, 1997/08/23
#
# The exit codes returned are:
#   XXX this doc is no longer correct now that the interesting
#   XXX functions are handled by httpd
#	0 - operation completed successfully
#	1 -
#	2 - usage error
#	3 - httpd could not be started
#	4 - httpd could not be stopped
#	5 - httpd could not be started during a restart
#	6 - httpd could not be restarted during a restart
#	7 - httpd could not be restarted during a graceful restart
#	8 - configuration syntax error
#
# When multiple arguments are given, only the error from the _last_
# one is reported.  Run "apachectl help" for usage info
#

function usage {
	cat <<!
Usage:

	$(basename $0) [-h] [-H <HOME>] [-U <user>] [-G <group>] [-C <conf>] [-P <profile>] [-E <httpd_path>] [-F] start|stop|restart|graceful|graceful-stop|configtest|status|fullstatus

Options:

	-H <HOME> .......: application user home directory; mandatory if runs as root; default \$HOME
	-U <user> .......: the userid under which the server will answer requests; mandatory if runs as root
	-G <group> ......: group under which the server will answer requests; mandatory if runs as root
	-C <conf> .......: full path to an alternative configuration file (usefull for starting multiple instances); default <HOME>/etc/httpd.conf
	-P <profile> ....: full path to an alternative profile file to source (usefull for starting multiple instances); default <HOME>/etc/profile.sh
	-E <httpd_path>..: full path to httpd executable; default /usr/local/apache/bin/httpd
	-F ..............: run httpd process in foreground with exec without creating a new process
	-h ..............: show usage page

Example:

	$ $(basename $0) restart
	# $(basename $0) -H /home/<USER>

!
	exit $1
}

pCMD=""
pHOME=""
pUSER=""
pGROUP=""
pCONF=""
pPROFILE=""
pHTTPD=""
pFOREGROUD=""

t=$(getopt -o hFU:G:H:C:E:P: -- "$@" ) || exit 1
eval set -- "$t"
unset t

while true; do
    case "$1" in
        -h)
            usage 0
            ;;
        -U)
			shift
            pUSER="$1"
            ;;
        -G)
			shift
            pGROUP="$1"
            ;;
        -P)
			shift
            pPROFILE="$1"
            ;;
        -H)
			shift
            pHOME="$1"
            ;;
        -C)
			shift
            pCONF="$1"
            ;;
        -E)
			shift
            pHTTPD="$1"
            ;;
        -F)
            pFOREGROUND="-DFOREGROUND"
            ;;
		--)
			shift
			pCMD="$1"
			break
			;;
        *)
            echo "$1: errore interno" >&2
            exit 1
	esac
	shift
done

if [[ "${pCMD}" == "" ]]; then
	usage 2
fi
if [[ "$(id -u)" == "0" ]]; then
	if [[ "${pUSER}" == "" ]]; then
		usage 2
	fi
	if [[ "${pGROUP}" == "" ]]; then
		usage 2
	fi
	if [[ "${pHOME}" == "" ]]; then
		usage 2
	fi
else
	if [[ "${pHOME}" == "" ]]; then
		pHOME="${HOME}"
	fi
fi

if [[ $pPROFILE == "" ]]; then
	pPROFILE="${pHOME}/etc/profile.sh"
fi
if [[ -e $pPROFILE ]]; then
	. ${pPROFILE} ${pHOME}
else
	echo "Missing '${pPROFILE}' !"
	exit 1
fi
if [[ "${pUSER}" != "" ]]; then
	export APACHE_USER=${pUSER}
fi
if [[ "${pGROUP}" != "" ]]; then
	export APACHE_GROUP=${pGROUP}
fi

conf=""
if [[ "${pCONF}" != "" ]]; then
	conf="${pCONF}"
else
	conf="${pHOME}/etc/httpd.conf"
fi
if [[ ! -e $conf ]]; then
	echo "Missing '${conf}' !"
	exit 1
fi

#
# |||||||||||||||||||| START CONFIGURATION SECTION  ||||||||||||||||||||
# --------------------                              --------------------
#
# the path to your httpd binary, including options if necessary
HTTPD=$(which httpd)
#
## pick up any necessary environment variables
#if test -f /usr/local/apache/bin/envvars; then
#  . /usr/local/apache/bin/envvars
#fi

#
# a command that outputs a formatted text version of the HTML at the
# url given on the command line.  Designed for lynx, however other
# programs may work.
LYNX="lynx -dump"
#
# the URL to your server's mod_status status page.  If you do not
# have one, then status and fullstatus will not work.
STATUSURL="http://127.0.0.1:$APACHE_PORT/server-status"
#
# Set this variable to a command that increases the maximum
# number of file descriptors allowed per child process. This is
# critical for configurations that use many file descriptors,
# such as mass vhosting, or a multithreaded server.
ULIMIT_MAX_FILES="ulimit -S -n `ulimit -H -n`"
# --------------------                              --------------------
# ||||||||||||||||||||   END CONFIGURATION SECTION  ||||||||||||||||||||

if [ "x$pHTTPD" != "x" ] ; then
    HTTPD=$pHTTPD
fi

# Set the maximum number of file descriptors allowed per child process.
if [ "x$ULIMIT_MAX_FILES" != "x" ] ; then
    $ULIMIT_MAX_FILES
fi

ERROR=0
case $pCMD in
start|stop|restart|graceful|graceful-stop)
    if [[ "${pFOREGROUND}" == "" ]]; then
        $HTTPD -f "${conf}" -k $pCMD
        ERROR=$?
    else
        exec $HTTPD -f "${conf}" -k $pCMD $pFOREGROUND
    fi
    ;;
startssl|sslstart|start-SSL)
    echo The startssl option is no longer supported.
    echo Please edit httpd.conf to include the SSL configuration settings
    echo and then use "apachectl start".
    ERROR=2
    ;;
configtest)
    $HTTPD -f "${conf}" -t
    ERROR=$?
    ;;
status)
    $LYNX $STATUSURL | awk ' /process$/ { print; exit } { print } '
    ;;
fullstatus)
    $LYNX $STATUSURL
    ;;
*)
	echo "Unknown command '${pCMD}'"
	usage 2
esac

exit $ERROR
