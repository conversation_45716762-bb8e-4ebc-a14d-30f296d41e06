#!/bin/env bash

#
# check-perl-syntax.sh
#
# Verifica la sintassi di tutti gli script Perl presenti nel path specificato (o nel path corrente) aventi
# per estensione: .pl .pm .ph .cgi .psgi
# 
# Per ogni file invalido stampa l'esito di perl -c
#

curpath=.
if [[ $# > 0 ]]; then
    curpath=$1
fi

find $curpath -type f \( -name '*.p[mlh]' -o -name '*.psgi' -o -name '*.cgi' \) | while read M
    do
        perl -c $M 1>&2 2>/dev/null || echo $M
    done | while read M
        do
            echo $M
            perl -c $M
            echo
        done