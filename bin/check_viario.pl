#!/usr/bin/env perl


=pod

=head1 NAME

 check_viario.pl - esegue un test sulla validita di dati toponomastici

=head1 SYNOPSIS

 check_viario.pl [ -v|-A|-k|-q|-I|o ] [-i <n>] [ -t <sep> ] [ -T <olo> ] [ -s <query> ] [-c <conn>] [ c[omune] milano f[r[azione]] villanova p[r[ovincia]] mi cap 20123 i[ndirizzo] 'via valtorta,34;A' tel 0131226102 ]
 			-v - verbose 
            -A - non emette gli attributi nei messaggi
            -i <n> - assegna id <n>
            -k - emette il messaggio OK in caso di non errori
            -q - abilita log query
            -p - parametri aggiuntivi per le classi di check (parametro=valore[,parametro=valore)
            	 puo essere specificato anche la variabile d' ambiente CHECK_VIARIO_PARAMS
            	 i valori di -p sovrascrivono i rispettivi valori di CHECK_VIARIO_PARAMS
			-I - non stampa INFO
			-o - stampa i messaggi su STDOUT invece che STDERR
			-T <olo> - formatta gli indirizzi per lo standard dell\' olo <olo> invece di eseguire il check
			-t <sep> - esegue solo split toponomastica e scrive su STDOUT  parttopon denomvia numcivico separati da <sep>
					 - se e' specificata l' opzione T usa <sep> come separatore il cui default e' ';' (punto e virgola) 				
			-s <query> - esegue una query per ricavare i dati;
			            se il valore di <query> e' '-' la query viene letta da stdin
						se il valore di <query> inizia con '$' <query> e\' il nome di una variabile d' ambiente il cui valore e' la query voluta
					    la query specificata deve emettere in output le colonne con questi nomi (non obbligatori):
						ID C[OMUNE] F[R[AZIONE]]  PR[OVINCIA] CAP (I[NDIRIZZO]|PARTTOPON DESCINDIRIZZO NUMCIVICO ESPONENTE COLORE)	T[EL]		

			-c <conn>  - stringa di connesione al db - se non specificata usa la variabile d' ambiente SQLID
			
=head1 DESCRIPTION

 

=head1  ENVIRONMENT 

    Collegamento al Database
            SQLID                                     stringa di collegamento di default se non  specificata l' opzione -c
            ART_DB_RAISEERROR                         solleva una eccezione in caso di errore sql                        
            ART_DB_PRINTERROR                         scrive su STDERR la query sql che ha sollevato un errore
            ART_DB_AUTOCOMMIT                         imposta l' autocommit
            NLS_LANG                                  imposta il territorio e la codifica della lingua (solo db Oracle)
            ORACLE_HOME                               imposta il path per Oracle Client (solo db Oracle)
            ART_DB_DEBUG                              scrive su STDERR le query prima dell' esecuzione
            ART_DBI_DRIVER                            nome driver DBI (default Oracle)
	Parametri Aggiuntivi
			CHECK_VIARIO_PARAMS   				  	  parametri aggiuntivi, � l'equivalente dell' opzione p
=cut


use strict;
use warnings;
use Getopt::Std;
use Data::Dumper;
use SIRTI::DB;
use SIRTI::DB::Oracle;
use SIRTI::Checks::CheckViario;
use SIRTI::Checks::CheckNumtel;
use SIRTI::Toponomastica;
use Data::Dumper;
use SIRTI::Nice;

{
	my %NOT_INFO_KEYS=(
			DB => 1
			,ATTR_VALUE_DEFINED => 1
			,ATTR_VALUE_UNDEF  => 1
			,PARAMS_LENGTH => 1
			,EMIT_MSG_ON_OK	=> 1
	);
	
	sub usage($) {
		my ($b)=$0=~/([^\/]+)$/;
		my $params=SIRTI::Checks::CheckViario::get_params();
		my @params=map {   $_.' => '.$params->{$_} } grep (defined $_,map {  exists $NOT_INFO_KEYS{$_} ? undef : $_  } sort keys %$params);
		my $info=(' 'x14).join("\n".' 'x14,@params);
		print "
$b  [ -h|-v|-A|-k|-q|-I|o ] [-i <n>] [ -t <sep> ] [ -T <olo> ] [ -s <query> ] [-c <conn>] [ c[omune] milano f[r[azione]] villanova p[r[ovincia]] mi cap 20123 i[ndirizzo] 'via valtorta,34;A' tel 0131226102 ]
        -v - verbose 
        -A - non emette gli attributi nei messaggi
        -i <n> - assegna id <n>
        -k - emette il messaggio OK in caso di non errori
        -q - abilita log query
        -p - parametri aggiuntivi per le classi di check (parametro=valore[,parametro=valore)
            	 puo essere specificato anche la variabile d' ambiente CHECK_VIARIO_PARAMS
            	 i valori di -p sovrascrivono i rispettivi valori di CHECK_VIARIO_PARAMS
$info
        -I - non stampa INFO
        -o - stampa i messaggi su STDOUT invece che STDERR
        -T <olo> - formatta gli indirizzi per lo standard dell\' olo <olo> invece di eseguire il check
        -t <sep> - esegue solo split toponomastica e scrive su STDOUT  parttopon denomvia numcivico separati da <sep>
                      - se e' specificata l' opzione T usa <sep> come separatore il cui default e' ';' (punto e virgola)                 
        -s <query> - esegue una query per ricavare i dati;
                 se il valore di <query> e' '-' la query viene letta da stdin
                 se il valore di <query> inizia con '\$' <query> viene considerato come il nome di una variabile d' ambiente il cui valore e' la query voluta
                 la query specificata deve emettere in output le colonne con questi nomi (non obbligatori):
                     ID C[OMUNE] F[R[AZIONE]]  PR[OVINCIA] CAP (I[NDIRIZZO]|PARTTOPON DESCINDIRIZZO NUMCIVICO ESPONENTE COLORE)    T[EL]        
        -c <conn>  - stringa di connesione al db - se non specificata usa la variabile d' ambiente SQLID
        "
	;
	exit $_[0];
	}
}

my $NLS_LANG=undef;

$Data::Dumper::Sortkeys = sub {  return [ sort  keys %{$_[0]} ] };  #ordina le chiavi di un hash in output di  Data::Dumper

sub  trmm($)  {  return $_[0] =~ /^<(\s*)>$/ ?  $1 : $_[0];  }

sub dumper(@) {
	return 	$NLS_LANG=~/utf8/i 
				?    SIRTI::Nice::UTF8_to_nice(Dumper(@_))
				:    Dumper(@_);
}

# main #

my %Opt=();
my %Params=();
getopts ('hc:vAi:kqp:Iot:s:T:',\%Opt) or usage(2);

usage(0) if $Opt{h};

$Opt{c}=$ENV{SQLID} unless defined($Opt{c});

if (!defined($Opt{c})) {
	print STDERR "stringa di connessione non definita\n";
	exit 2;
}


$Params{DB} = new SIRTI::DB::Oracle($Opt{c},defined($Opt{q}) ? { DEBUG => 1 } : {}) ||  SIRTI::Err::db_fatal_error();
$Params{NO_INPUT_FIELDS_INTO_MSG}=1 if defined($Opt{A});
$Params{EMIT_MSG_ON_OK}='OK' if defined($Opt{k});
$NLS_LANG=$Params{DB}->get_params()->{NLS_LANG};
$NLS_LANG=$ENV{NLS_LANG};

$Params{DB}->set_dbms_output(ENABLE => $Opt{q} ? 1 : 0);


$Opt{p}=sub {
	my $fl=0;
	my %h=();
	my $index=0;
	for my $p(@_) {
		next  unless defined($p);
		my @nv=split(',',$p);
		my $fl=0;
		for my $e(@nv) {
			my ($k,$v)=$e=~/^\s*([^=]+)=(\S*)\s*$/;
			unless (defined($k)) {
				print STDERR "$e: parametro=valore ",($index == 0 ? ' variabile CHECK_VIARIO_PARAMS' : 'opzione p'), "variabile  non valido\n";
				$fl=1;
			}
			else {
				$h{$k}=$v;
			}
		}
		++$index;
	}
	return $fl ? undef : \%h;
}->($ENV{CHECK_VIARIO_PARAMS},$Opt{p});

exit 2 unless defined($Opt{p});

$Opt{s}=sub {
	my $q=shift;
	return undef unless defined $q;
	return $ENV{$1} if $q=~/^\$(.*)$/;
	if ($q eq '-') {
		my @a=<STDIN>;
		return join('',@a);
	}
	return $q;
}->($Opt{s});


if (defined($Opt{s})) {
	if (scalar(@ARGV) > 0) {
		print STDERR "i parametri  vengolo letti dalla query (opzione -s) invece che dalla linea di comandi \n";
		exit 2;
	}
}
elsif (scalar(@ARGV) == 0) {
	print STDERR "nessun parametro specificato \n";
	exit 2;
}
elsif (scalar(@ARGV) % 2 != 0) {
	print STDERR "numero dispari elementi  coppie valore \n";
	exit 2;
}

my $query = sub {
	my $sql=
	return $Opt{s} if defined($Opt{s});
			
#	my %d=map { 
#		my $t=uc(trmm($_));
#		$NLS_LANG=~/utf8/i 
#			?    SIRTI::Nice::nice_to_UTF8($t)
#			:    $t;
#	} @ARGV;  #ARGV ritorna i dati in ISO
#
	my %d=map { 
		uc(trmm($NLS_LANG=~/utf8/i   ?    SIRTI::Nice::nice_to_UTF8($_)  :  $_));
	} @ARGV;  #ARGV ritorna i dati in ISO

	for my $k(keys %d) {
		delete $d{$k} if defined($d{$k}) && $d{$k} eq '';
	}

	$d{C}=$d{COMUNE} if !defined($d{C});
	$d{I}=$d{INDIRIZZO} if !defined($d{I});
	$d{F}=$d{FRAZIONE} if !defined($d{F});
	$d{F}=$d{FR} if !defined($d{F});
	$d{P}=$d{PR} if !defined($d{P});
	$d{P}=$d{PROVINCIA} if !defined($d{P});
	$d{T}=$d{TEL} if !defined($d{T});
	$d{ID}=$Opt{i} if defined($Opt{i});

	return 'select '.join(',',map {  $Params{DB}->quote($d{$_}).' '.$_   } qw ( ID C I F P T CAP )).' from dual';
}->();


my $cur=$Params{DB}->create_cursor($query) || SIRTI::Err::db_fatal_error();

my $ck_numtel=new SIRTI::Checks::CheckNumtel(%Params);
my $ck_comun=new SIRTI::Checks::CheckComunario(%Params,%{$Opt{p}});
my $ck_viario=new SIRTI::Checks::CheckViario(%Params,%{$Opt{p}});
my $topo= new SIRTI::Toponomastica( %Params );
my $RC=0;

$Params{factory} = undef; 

LOOP:
while(my $row = $cur->fetchrow_hashref()) {
	my %d=%$row;

	$d{C}=$d{COMUNE} if !defined($d{C});
	$d{I}=$d{INDIRIZZO} if !defined($d{I});
	$d{F}=$d{FRAZIONE} if !defined($d{F});
	$d{F}=$d{FR} if !defined($d{F});
	$d{P}=$d{PR} if !defined($d{P});
	$d{P}=$d{PROVINCIA} if !defined($d{P});
	$d{T}=$d{TEL} if !defined($d{T});
	for my $k(keys %d) {
		$d{$k}='' if SIRTI::Err::nvl($d{$k}) eq '<>';
	}

	if (defined($d{T})) {
		my %v=(
			 CAP 			=> $d{CAP}
			,COMUNE 		=> $d{C}
			,FRAZIONE		=> $d{F}
			,PROVINCIA 		=> $d{P}
			,NUMTELCOMPL	=> $d{T}
			,ID				=> $d{ID}
		);		
		for my $k(keys %d) {
			$d{$k}=undef if length(SIRTI::Err::nvl($d{$k})) == 0;
		}
		print STDERR dumper(\%v),"\n" if defined($Opt{v});
		my $msg=$ck_numtel->do_check(\%v);
		$ck_numtel->print_messages((defined($Opt{o}) ? *STDOUT : *STDERR),sub {  
											$NLS_LANG=~/utf8/i 
											? SIRTI::Nice::UTF8_to_nice($_[0]) 
											: $_[0] 
									}
		);
	}


	my %v=();
	my $ck=undef;
	if (defined($d{I}) || defined($d{PARTTOPON})) {	
		unless  (defined($d{PARTTOPON})) {
			my $x = ${ \$topo->split_from_sv( $d{I}, '', '' ) };
			if ($Opt{t} && !defined($Opt{T})) {
				for my $k (qw/ C F P CAP/) {
					$x->{$k}=$d{$k};
				}
				print join($Opt{t},map {  SIRTI::Err::nvl($x->{$_}) } qw( C F P CAP PARTTOPON DESCINDIRIZZO NUMCIVICO ESPONENTE COLORE NOTE)),"\n";
				next LOOP;
			}
			%v = %$x;
			$v{FREE_ADDR}=$d{I}; #lo splitter ritorna un free address non corretto
		}

		$v{PROVINCIA}	= $d{P};
		$v{CAP}			= $d{CAP};
		$v{COMUNE} 		= $d{C};
		$v{FRAZIONE}	= $d{F};
		$ck=$ck_viario;
	}
	else {
		%v=(
			 CAP => $d{CAP}
			,COMUNE => $d{C}
			,FRAZIONE	=> $d{F}
			,PROVINCIA => $d{P}
		);	
		$ck=$ck_comun;
	}

	$v{ID}=$d{ID};

	for my $k(keys %d) {
		$d{$k}=undef if length(SIRTI::Err::nvl($d{$k})) == 0;
	}

	if (defined($Opt{T})) {
		my $rc=0;
		unless (defined($Params{factory})) {
			eval 'use SIRTI::Toponomastica::Factory';
			if ($@) {
				print STDERR "$@";
				exit 2;
			}
			my $fact_obj=new SIRTI::Toponomastica::Factory(CLASS_SPACE	=> 'CapProfessional');
			$Params{factory}->{splitter}=$fact_obj->factory_splitter(
				DB => $Params{DB}
			);
		}
		
		if (defined($v{FREE_ADDR})) {
			my $msgs=$Params{factory}->{splitter}->do_split_free_address(
																GLOBALID			=> $Opt{i}
																,FORMAT_TYPE		=> $Opt{T}
																,ADDR				=> $v{FREE_ADDR}
													);													
			my $h=$msgs->[0];
			if ($Opt{A}) {
				delete $h->{FREE_ADDR};
				delete $h->{FORMAT_TYPE};
				my %m=map {  ($_,$h->{$_}) } grep(SIRTI::Err::nvl($h->{$_}) !~/^\s*$/,keys(%$h));
				$h=\%m;
			}

			my $f=defined($Opt{o}) ? *STDOUT : *STDERR;
			my $id=delete $h->{ID};
			my $errmsg = delete $h->{MSG};

			print $f $id,':SPLITTER_',$Opt{T},':',SIRTI::Err::nvl($errmsg,$Opt{k} ? 'OK' : '')
					,'(',join(' ',map {  $_.' => '.SIRTI::Err::nvl($h->{$_}) } sort keys %$h),")\n";

			#print $f Dumper($msgs),"\n";
			$RC = 1  if SIRTI::Toponomastica::Factory::iserror($msgs);
		}
		else {
			print STDERR "indirizzo non definito\n";
			$RC = 2;
		}
	}
	else {  #classico 

		print STDERR "DATA: ", Dumper(\%v),"\n" if defined($Opt{v});

		my $msg=$ck->do_check(\%v);

		my $f=defined($Opt{o}) ? *STDOUT : *STDERR;

		$ck->print_messages($f,sub {  
											$NLS_LANG=~/utf8/i 
											? SIRTI::Nice::UTF8_to_nice($_[0]) 
											: $_[0] 
									}
		);

		if (defined($Opt{v}) && !defined($Opt{I})) {
			my $data=$ck->get_data();
			my $d=Dumper($data);
			if (defined($Opt{i})) {
				for my $line(split("\n",$d)) {
					$line=~s/^/$Opt{i}:/;
					print $f $line,"\n";
				}
			}
			else {
				print $f "INFO: ", $d;
			}
		}

		$RC = 1 if $RC == 0 && scalar(@{$ck->get_messages()}) > 0;
	}
}


$Params{DB}->rollback();

exit $RC;
