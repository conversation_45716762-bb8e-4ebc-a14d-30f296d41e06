#!/bin/bash

NOMESCRIPT=$(basename $0)

function cmd() {
       COMMAND=$1
       shift
       DAEMONS=$@
       if [ "$COMMAND" = "list_all" ]; then
          [[ $QUIET != "1" ]] && echo "Eseguo comando \"$COMMAND\"";
          start_stop_daemon --config $STARTSTOPDAEMONCONFIGFILE --list | sort
       elif [ "$COMMAND" = "list" ]; then
          [[ $QUIET != "1" ]] && echo "Eseguo comando \"$COMMAND\"";
          for DAEMON in $(cat $CONFIGFILE | sed 's/#.*//' | sort);
          do
              echo $DAEMON
          done
       elif [ "$COMMAND" = "check_all" ]; then
          [[ $QUIET != "1" ]] && echo "Eseguo comando \"$COMMAND\"";
          for DAEMON in $(start_stop_daemon --config $STARTSTOPDAEMONCONFIGFILE --list | sort);
          do
              start_stop_daemon --config $STARTSTOPDAEMONCONFIGFILE -x --daemon check "$DAEMON";
          done
       elif [ "$COMMAND" = "check_config" ]; then
          [[ $QUIET != "1" ]] && echo "Eseguo comando \"$COMMAND\"";
          TEMPFILEGESTIBILI=$(mktemp)
          TEMPFILECONFIGURATI=$(mktemp)
          $0 list | grep -v 'Eseguo comando' > $TEMPFILEGESTIBILI
          $0 list_all | grep -v 'Eseguo comando' > $TEMPFILECONFIGURATI
          HEADERDONE=0
          for DAEMON in $(cat $TEMPFILEGESTIBILI);
          do
            eval "grep '^$DAEMON\$' $TEMPFILECONFIGURATI" > /dev/null
            RET=$?
            if [[ $RET == "0" ]]; then
                if [[ $HEADERDONE == "0" ]]; then
                    echo "+++ demoni gestibili (elencati in $CONFIGFILE) e configurati (in $STARTSTOPDAEMONCONFIGFILE)"
                    HEADERDONE=1
                fi
                echo $DAEMON
            fi
          done
          HEADERDONE=0
          for DAEMON in $(cat $TEMPFILECONFIGURATI);
          do
            eval "grep '^$DAEMON\$' $TEMPFILEGESTIBILI" > /dev/null
            RET=$?
            if [[ $RET != "0" ]]; then
                if [[ $HEADERDONE == "0" ]]; then
                    echo "--- demoni configurati (in $STARTSTOPDAEMONCONFIGFILE) ma non gestibili (non elencati in $CONFIGFILE)"
                    HEADERDONE=1
                fi
                echo $DAEMON
            fi
          done
          HEADERDONE=0
          for DAEMON in $(cat $TEMPFILEGESTIBILI);
          do
            eval "grep '^$DAEMON\$' $TEMPFILECONFIGURATI" > /dev/null
            RET=$?
            if [[ $RET != "0" ]]; then
                if [[ $HEADERDONE == "0" ]]; then
                    echo "xxx ERRORI: demoni gestibili (elencati in $CONFIGFILE) ma non configurati (in $STARTSTOPDAEMONCONFIGFILE)"
                    HEADERDONE=1
                fi
                echo $DAEMON
            fi
          done
          rm -f $TEMPFILEGESTIBILI
          rm -f $TEMPFILECONFIGURATI
       else
          if [[ $COMMAND == "signal_hup" ]]; then
            COMMAND="signal HUP"
          fi
          LISTA_DEMONI=""
          [[ $QUIET != "1" ]] && echo -n "Eseguo comando \"$COMMAND\"";
          if [[ $DAEMONS == "" ]]; then
              LISTA_DEMONI=$(cat $CONFIGFILE | sed 's/#.*//' | sort)
              [[ $QUIET != "1" ]] && echo
          else
              for D in $DAEMONS;
              do
                  ATTIVABILE=0
                  for DAEMON in $(cat $CONFIGFILE | sed 's/#.*//');
                  do
                    if [[ $DAEMON == $D ]]; then
                        ATTIVABILE=1
                        break
                    fi
                  done
                  if [[ $ATTIVABILE == "0" ]]; then
                      echo
                      echo "Il demone \"$D\" non e' elencato in $CONFIGFILE"
                      exit 3
                  fi
              done
              LISTA_DEMONI=$DAEMONS
              [[ $QUIET != "1" ]] && echo " sui demoni \"$DAEMONS\""
          fi
          for DAEMON in $LISTA_DEMONI;
          do
              start_stop_daemon --config $STARTSTOPDAEMONCONFIGFILE -x --stderr "$DAEMONLOGPATH"/daemon."$DAEMON".err.$CURDATE --daemon $COMMAND "$DAEMON";
          done
       fi
}

function usage {
  echo
  echo "usage: $NOMESCRIPT [arguments] command [demone1[ demone2 [...]]]"
  echo
  echo "   CLI per la gestione dei demoni"
  echo
  echo "     demoni configurati ..... elenco dei demoni configurati (in $STARTSTOPDAEMONCONFIGFILE)"
  echo "                              per l'utilizzo con \"start_stop_daemon\""
  echo "     demoni gestibili ....... elenco dei demoni gestibili con \"$NOMESCRIPT\" (elencati in $CONFIGFILE)"
  echo
  echo "   arguments:"
  echo "     -q ..................... non visualizza la descrizione del comndo che sta per eseguire"
  echo "     -c <file> .............. file di configurazione dei demoni gestibili con \"$NOMESCRIPT\""
  echo "                              default: env DAEMONS_ACTIVE_CONFIG_FILE o $HOME/etc/daemons.active"
  echo "                              valore attuale: $CONFIGFILE"
  echo "     -k <file> .............. file di configurazione dei demoni configurati per l'utilizzo con \"start_stop_daemon\""
  echo "                              default: env SSD_CONFIG_FILE o $HOME/etc/ssd_config"
  echo "                              valore attuale: $STARTSTOPDAEMONCONFIGFILE"
  echo "     -l <file> .............. directory in cui verra' creato il file daemon.nome_demone.err.YYYYMMDD"
  echo "                              default env LOG_CONFIG o $HOME/var/log"
  echo "                              valore attuale $DAEMONLOGPATH"
  echo "     -h ..................... mostra questa schermata d'aiuto"
  echo
  echo "   command:"
  echo "     check .................. verifica lo stato dei demoni gestibili elencati in $CONFIGFILE"
  echo "     start .................. avvia i demoni gestibili elencati in $CONFIGFILE"
  echo "     stop ................... ferma i demoni gestibili elencati in $CONFIGFILE"
  echo "     restart ................ ferma e riavvia i demoni gestibili elencati in $CONFIGFILE"
  echo "     signal_hup ............. invia il segnale HUP ai demoni gestibili elencati in $CONFIGFILE"
  echo "     list ................... elenca i demoni gestibili elencati in $CONFIGFILE"
  echo "     list_all ............... elenca tutti i demoni configurati (in $STARTSTOPDAEMONCONFIGFILE)"
  echo "     check_all .............. verifica lo stato di tutti i demoni configurati (in $STARTSTOPDAEMONCONFIGFILE)"
  echo "     check_config ........... confronta i demoni gestibili elencati in $CONFIGFILE con quelli"
  echo "                              configurati (in $STARTSTOPDAEMONCONFIGFILE)"
  echo "     start_and_check ........ avvia i demoni gestibili elencati in $CONFIGFILE e verifica il loro stato"
  echo "     stop_and_check ......... ferma i demoni gestibili elencati in $CONFIGFILE e verifica il loro stato"
  echo "     restart_and_check ...... ferma e riavvia i demoni gestibili elencati in $CONFIGFILE e verifica il loro stato"
  echo "     signal_hup_and_check ... invia il segnale HUP ai demoni gestibili elencati in $CONFIGFILE e verifica il loro stato"
  echo 
  echo "     [demone1[ demone2 [...]]] ... opzionale. se valorizzato lavora solo sui demoni indicati. I demoni devono"
  echo "                                   essere gestibili, ovvero elencati in $CONFIGFILE"
  echo
}

if [[ -z $DAEMONS_ACTIVE_CONFIG_FILE ]]; then
  CONFIGFILE=$HOME/etc/daemons.active
else
  CONFIGFILE=$DAEMONS_ACTIVE_CONFIG_FILE
fi
if [[ -z $SSD_CONFIG_FILE ]]; then
  STARTSTOPDAEMONCONFIGFILE=$HOME/etc/ssd_config
else
  STARTSTOPDAEMONCONFIGFILE=$SSD_CONFIG_FILE
fi
if [[ -z $LOG_PATH ]]; then
  DAEMONLOGPATH=$HOME/var/log
else
  DAEMONLOGPATH=$LOG_PATH
fi
QUIET=0

while getopts hqc:k:l: flag; do
  case $flag in
    h)
      usage
      exit 0
      ;;
    q)
      QUIET=1
      ;;
    c)
      CONFIGFILE=${OPTARG}
      ;;
    k)
      STARTSTOPDAEMONCONFIGFILE=${OPTARG}
      ;;
    l)
      DAEMONLOGPATH=${OPTARG}
      ;;
    ?)
      usage
      exit 4
      ;;
  esac
done

shift $(( OPTIND - 1 ));

COMMAND=$1
shift
DAEMONS=$@

# echo "command: $COMMAND"
# echo "daemons: $DAEMONS"
# echo "log_path: $DAEMONLOGPATH"
# echo "STARTSTOPDAEMONCONFIGFILE: $STARTSTOPDAEMONCONFIGFILE"

if [[ ! -f $CONFIGFILE ]] && [[ $COMMAND != "list_all" && $COMMAND != "check_all" ]] ; then
    echo "Il file di configurazione dei demoni gestibili $CONFIGFILE non esiste, esco."
    echo "Potete crearlo vuoto utilizzando il comando \"touch $CONFIGFILE\","
    echo "potete crearlo con tutti i demoni configurati utilizzando il comando \"$NOMESCRIPT -q list_all > $CONFIGFILE\","
    echo "potete crearlo e configuralo manualmente oppure automaticamente utilizzando \"daemons_gui.sh\""
    exit 1
fi

if [[ $(cat $CONFIGFILE | sed 's/#.*//' | grep -P '.+' | wc -l) == 0 ]] && [[ $COMMAND != "list_all" && $COMMAND != "check_all" && $COMMAND != "check_config" ]] ; then
    echo "Nessun demone gestibile."
    exit 0
fi

CLIST=();

case "$COMMAND" in
   start)  CLIST+=("start");
   ;;
   stop)   CLIST+=("stop");
   ;;
   restart) CLIST+=("stop"); CLIST+=("start");
   ;;
   check)  CLIST+=("check");
   ;;
   signal_hup)  CLIST+=("signal_hup");
   ;;
   list)   CLIST+=("list");
   ;;
   list_all)   CLIST+=("list_all");
   ;;
   check_all)   CLIST+=("check_all");
   ;;
   check_config)   CLIST+=("check_config");
   ;;
   start_and_check) CLIST+=("start"); CLIST+=("check");
   ;;
   stop_and_check) CLIST+=("stop"); CLIST+=("check");
   ;;
   restart_and_check) CLIST+=("stop"); CLIST+=("start"); CLIST+=("check");
   ;;
   signal_hup_and_check) CLIST+=("signal_hup"); CLIST+=("check");
   ;;
   *) usage
      exit 5
   ;;
esac

for CMD in ${CLIST[@]};
do
      case "$CMD" in
           start) cmd "start" $DAEMONS;
           ;;
           stop)  cmd "stop" $DAEMONS;
           ;;
           check) cmd "check" $DAEMONS;
           ;;
           signal_hup) cmd "signal_hup" $DAEMONS;
           ;;
           list)  cmd "list";
           ;;
           list_all)  cmd "list_all";
           ;;
           check_all)  cmd "check_all";
           ;;
           check_config)  cmd "check_config";
           ;;
      esac
done

exit 0;
