#!/bin/bash

NOMESCRIPT=$(basename $0)

function confirm {

    azione=$1
    dialog --title "Richiesta conferma azione" --yesno "$azione" 0 0

}

function manage_daemon {

    if [[ ! -f $CONFIGFILE ]] ; then
        dialog --msgbox "File $CONFIGFILE non presente" 0 0
        return
    fi

    DAEMON=$1
    TEMPFILECHOOSEACTION=$(mktemp)
    TEMPFILEDAEMONSTATUS=$(mktemp)
    while true
    do
        daemons_ctl.sh -c $CONFIGFILE -k $STARTSTOPDAEMONCONFIGFILE -l $DAEMONLOGPATH check $DAEMON > /dev/null 2> $TEMPFILEDAEMONSTATUS
        DAEMONSTATUS=$(cat $TEMPFILEDAEMONSTATUS)
        SIZE=${#DAEMONSTATUS}
        SIZE=$((SIZE+1))
        CMD="dialog --backtitle \"Gestione demone \"$DAEMON\"\"
            --ok-label \"Seleziona\" \
            --cancel-label \"Torna\" \
            --menu \"${DAEMONSTATUS}\nSeleziona azione\" \
                0 0 0 \
            check \" \" \
            start_and_check \" \" \
            stop_and_check \" \" \
            restart_and_check \" \" \
            signal_hup_and_check \" \" \
            \"get command line\" \" \" \
            \"vim -R daemon_error_log\" \" \""
        XLOG=$(cat $STARTSTOPDAEMONCONFIGFILE | grep -P "^# @$DAEMON:log:" | sed -r "s/^# @$DAEMON:log:([^ ]+)/\1/")
        for XFILE in $XLOG
        do
          XTMP=$(eval "echo -n $XFILE")
          if [[ -n $XFILE ]] && [[ -f $XTMP ]] ; then
              CMD="$CMD \"vim -R $XFILE\" \" \""
          fi
        done
        for XFILE in $XLOG
        do
          XTMP=$(eval "echo -n $XFILE")
          if [[ -n $XFILE ]] && [[ -f $XTMP ]] ; then
              CMD="$CMD \"tail -f $XFILE\" \" \""
          fi
        done
        eval $CMD 2> $TEMPFILECHOOSEACTION
        OPZ=`cat $TEMPFILECHOOSEACTION`
        if [[ $OPZ == "" ]] ; then
            rm -f $TEMPFILEDAEMONSTATUS
            rm -f $TEMPFILECHOOSEACTION
            return
        fi
        if [[ $OPZ == "get command line" ]] ; then
            clear
            echo $(cat $STARTSTOPDAEMONCONFIGFILE | grep -P "^$DAEMON[ \t]+" | sed -r "s/^$DAEMON[ \\t]+arg[ \\t]+//" | tr "\n" " ")
            echo "----------------------------"
            echo "<Premi invio per continuare>"
            read
            continue
        fi
        if [[ $OPZ == "vim -R daemon_error_log" ]] ; then
            ERRORLOGFILENAME=$(ls -1 $DAEMONLOGPATH/daemon.$DAEMON.err.* 2> /dev/null | tail -n 1)
            if [[ $ERRORLOGFILENAME == "" ]] ; then
              dialog --msgbox "Nessun daemon error log presente" 0 0
            elif [[ ! -s $ERRORLOGFILENAME ]] ; then
              dialog --msgbox "Il daemon error log $ERRORLOGFILENAME e' vuoto" 0 0
            else
              dialog --title "$OPZ" --msgbox "Apro con vim in sola lettura il file $ERRORLOGFILENAME\nEsci da vim con :q per tornare al menu precedente." 0 0
              clear
              eval "vim -R $ERRORLOGFILENAME"
            fi
            continue
        fi
        echo $OPZ | grep '^vim -R '
        ISVIM=$?
        if [[ $ISVIM == "0" ]] ; then
          TOVIM=$(echo $OPZ | sed -r "s/^vim -R //")
            dialog --title "$OPZ" --msgbox "Apro con vim in sola lettura il file $TOVIM\nEsci da vim con :q per tornare al menu precedente." 0 0
            clear
            eval "vim -R $TOVIM"
            continue
        fi
        echo $OPZ | grep '^tail -f '
        ISTAIL=$?
        if [[ $ISTAIL == "0" ]] ; then
          TOTAIL=$(echo $OPZ | sed -r "s/^tail -f //")
            clear
            eval "dialog --title \"tail $TOTAIL\" --tailbox $TOTAIL 0 0"
            continue
        fi
        if [[ $OPZ != "check" ]] ; then
            if [[ $CONFIRM == "1" ]]; then
                confirm "$OPZ sul demone $DAEMON"
                RET=$?
            else
                RET=0
            fi
            if [[ $RET != "0" ]]; then
                continue
            fi
        fi
        clear
        daemons_ctl.sh -c $CONFIGFILE -k $STARTSTOPDAEMONCONFIGFILE -l $DAEMONLOGPATH $OPZ $DAEMON
        echo "----------------------------"
        echo "<Premi invio per continuare>"
        read
    done
    rm -f $TEMPFILEDAEMONSTATUS
    rm -f $TEMPFILECHOOSEACTION
}

function manage_daemons {

    if [[ ! -f $CONFIGFILE ]] ; then
        dialog --msgbox "File $CONFIGFILE non presente" 0 0
        return
    fi

    if [[ $(cat $CONFIGFILE | sed 's/#.*//' | grep -P '.+' | wc -l) == 0 ]] ; then
        dialog --msgbox "Nessun demone gestibile. Utilizza la voce di menu \"7 - Seleziona i demoni gestibili\" per selezionarli" 0 0
        return
    fi

    TEMPFILECHOOSEDAEMON=$(mktemp)

    while true
    do
        CMD="dialog --backtitle \"Gestione demoni\"
            --ok-label \"Seleziona\" \
            --cancel-label \"Torna\" \
            --menu \"Seleziona demone\" \
                0 0 0"
        NUM=0
        for DAEMON in $(cat $CONFIGFILE | sed 's/#.*//' | sort)
        do
            CMD=" $CMD \"$DAEMON\" \" \""
        done
        echo $CMD
        eval $CMD 2>$TEMPFILECHOOSEDAEMON
        OPZ=`cat $TEMPFILECHOOSEDAEMON`
        if [[ $OPZ == "" ]] ; then
            rm -f $TEMPFILECHOOSEDAEMON
            return
        fi
        manage_daemon $OPZ
    done
    
    rm -f $TEMPFILECHOOSEDAEMON
}

function select_active_daemons {

    if [[ ! -f $CONFIGFILE ]] ; then
        CONFIGFILEREAD=/dev/null
    else   
        CONFIGFILEREAD=$CONFIGFILE
    fi

    CMD='dialog --backtitle "Seleziona i demoni gestibili" --cancel-label "Torna" --checklist "Seleziona i demoni che devono essere gestibili:" 0 0 0 '

    TEMPFILESELECTACTIVEDAEMONS=$(mktemp)

    for DAEMON in $(start_stop_daemon --list | sort);
    do
        INX="cat $CONFIGFILEREAD | sed 's/#.*//' | grep -P '^${DAEMON}$'"
        eval $INX > /dev/null
        RET=$?
        if [[ $RET == 0 ]] ; then
            VAL="on"
        else
            VAL="off"
        fi
        CMD="$CMD $DAEMON ' ' $VAL "
    done

    eval $CMD 2> $TEMPFILESELECTACTIVEDAEMONS

    RET=$?
    if [[ $RET == 0 ]] ; then
        dialog --title "Confermi la scrittura del file $CONFIGFILE"  --yesno "Scrivo?" 0 0
        case $? in
            0)      DATACORRENTEITA=$(date "+%d/%m/%Y %H:%M:%S")
                    DATACORRENTE=$(date "+%Y%m%d%H%M%S")
                    FILEBACKUP="${CONFIGFILE}.${DATACORRENTE}"
                    BACKUPDONE=0
                    if [[ $DOBACKUP == "1" ]] && [[ -f $CONFIGFILE ]]; then
                        /bin/cp $CONFIGFILE $FILEBACKUP
                        BACKUPDONE=1
                    fi
                    > $CONFIGFILE
                    echo "#" >> $CONFIGFILE
                    echo "# file generato con \"$NOMESCRIPT\" in data $DATACORRENTEITA" >> $CONFIGFILE
                    if [[ $BACKUPDONE == "1" ]]; then
                        echo "# creato backup della versione precedente in $FILEBACKUP" >> $CONFIGFILE
                    fi
                    echo "#" >> $CONFIGFILE
                    for DAEMON in $(cat $TEMPFILESELECTACTIVEDAEMONS);
                    do
                        echo $DAEMON | sed 's/"//g' >> $CONFIGFILE
                    done
                    MSG="File $CONFIGFILE modificato."
                    if [[ $BACKUPDONE == "1" ]]; then
                        MSG="${MSG}\nCreato backup della versione precedente in $FILEBACKUP"
                    fi
                    dialog --msgbox "$MSG" 0 0
                    ;;
            1|255)  dialog --msgbox "Nessuna modifica apportata al file $CONFIGFILE" 0 0
                    ;;
        esac
    else
        dialog --msgbox "Nessuna modifica apportata al file $CONFIGFILE" 0 0
    fi

    rm -f $TEMPFILESELECTACTIVEDAEMONS

}

function usage {
  echo
  echo "usage: $NOMESCRIPT [arguments]"
  echo
  echo "   GUI per la gestione dei demoni:"
  echo
  echo "     demoni configurati ..... elenco dei demoni configurati (in $STARTSTOPDAEMONCONFIGFILE)"
  echo "                              per l'utilizzo con \"start_stop_daemon\""
  echo "     demoni gestibili ....... elenco dei demoni gestibili con \"daemons_ctl.sh\" (elencati in $CONFIGFILE)"
  echo
  echo "   arguments:"
  echo "     -f ..................... non chiede conferma per i comandi dispositivi"
  echo "     -c <file> .............. file di configurazione dei demoni gestibili con \"daemons_ctl.sh\""
  echo "                              default: env DAEMONS_ACTIVE_CONFIG_FILE o $HOME/etc/daemons.active"
  echo "                              valore attuale: $CONFIGFILE"
  echo "     -k <file> .............. file di configurazione dei demoni configurati per l'utilizzo con \"start_stop_daemon\""
  echo "                              default: env SSD_CONFIG_FILE o $HOME/etc/ssd_config"
  echo "                              valore attuale: $STARTSTOPDAEMONCONFIGFILE"
  echo "     -l <file> .............. directory in cui verra' creato il file daemon.nome_demone.err.YYYYMMDD"
  echo "                              default env LOG_CONFIG o $HOME/var/log"
  echo "                              valore attuale $DAEMONLOGPATH"
  echo "     -n ..................... non crea il backup del file $CONFIGFILE quando viene modificato con la voce"
  echo "                              di menu \"Seleziona i demoni gestibili\""
  echo "     -h ..................... mostra questa schermata d'aiuto"
  echo
}

CONFIRM="1"
DOBACKUP="1"
if [[ -z $DAEMONS_ACTIVE_CONFIG_FILE ]]; then
  CONFIGFILE=$HOME/etc/daemons.active
else
  CONFIGFILE=$DAEMONS_ACTIVE_CONFIG_FILE
fi
if [[ -z $SSD_CONFIG_FILE ]]; then
  STARTSTOPDAEMONCONFIGFILE=$HOME/etc/ssd_config
else
  STARTSTOPDAEMONCONFIGFILE=$SSD_CONFIG_FILE
fi
if [[ -z $LOG_PATH ]]; then
  DAEMONLOGPATH=$HOME/var/log
else
  DAEMONLOGPATH=$LOG_PATH
fi

while getopts hfnc:k:l: flag; do
  case $flag in
    h)
      usage
      exit 0
      ;;
    f)
      CONFIRM=""
      ;;
    n)
      DOBACKUP=""
      ;;
    c)
      CONFIGFILE=${OPTARG}
      ;;
    k)
      STARTSTOPDAEMONCONFIGFILE=${OPTARG}
      ;;
    l)
      DAEMONLOGPATH=${OPTARG}
      ;;
    ?)
      usage
      exit 4
      ;;
  esac
done

shift $(( OPTIND - 1 ));

if [[ ! -f $CONFIGFILE ]] ; then
    dialog --msgbox "File $CONFIGFILE non presente.\nPer crearlo utilizzare la voce di menu \"Seleziona demoni gestibili\"" 0 0
fi

TEMPFILEMAIN=$(mktemp)
while true
do
    dialog  --backtitle "Daemon GUI - Console gestione demoni" \
        --ok-label "Seleziona" \
        --cancel-label "Esci" \
        --menu "Menu principale" \
            0 0 0 \
        "1" "Gestione demoni" \
        "2" "Controlla lo stato di tutti i demoni gestibili" \
        "3" "Avvia tutti i demoni gestibili" \
        "4" "Ferma tutti i demoni gestibli" \
        "5" "Riavvia tutti i demoni gestibili" \
        "6" "Invia il segnale HUP a tutti i demoni gestibili" \
        "7" "Seleziona i demoni gestibili" \
        "8" "Verifica il file di configurazione dei demoni gestibili" \
        "9" "Controlla lo stato di tutti i demoni configurati" \
        "L" "Legenda" \
        "Q" "Esci" 2>$TEMPFILEMAIN
    opz=`cat $TEMPFILEMAIN`
    case $opz in
        1) # Gestione demoni
            clear
            manage_daemons
        ;;
        2) # Controlla lo stato di tutti i demoni gestibili
            clear
            daemons_ctl.sh -c $CONFIGFILE -k $STARTSTOPDAEMONCONFIGFILE -l $DAEMONLOGPATH check 
            echo "----------------------------"
            echo "<Premi invio per continuare>"
            read
        ;;
        3) # Avvia tutti i demoni gestibili
            clear
            if [[ $CONFIRM == "1" ]]; then
                confirm "Avvia tutti i demoni gestibili"
                RET=$?
                clear
            else
                RET=0
            fi
            if [[ $RET == "0" ]]; then
                daemons_ctl.sh -c $CONFIGFILE -k $STARTSTOPDAEMONCONFIGFILE -l $DAEMONLOGPATH start_and_check
                echo "----------------------------"
                echo "<Premi invio per continuare>"
                read
            fi
        ;;
        4) # Ferma tutti i demoni gestibli
            clear
            if [[ $CONFIRM == "1" ]]; then
                confirm "Ferma tutti i demoni gestibli"
                RET=$?
                clear
            else
                RET=0
            fi
            if [[ $RET == "0" ]]; then
                daemons_ctl.sh -c $CONFIGFILE -k $STARTSTOPDAEMONCONFIGFILE -l $DAEMONLOGPATH stop_and_check
                echo "----------------------------"
                echo "<Premi invio per continuare>"
                read
            fi
        ;;
        5) # Riavvia tutti i demoni gestibili
            clear
            if [[ $CONFIRM == "1" ]]; then
                confirm "Riavvia tutti i demoni gestibli"
                RET=$?
                clear
            else
                RET=0
            fi
            if [[ $RET == "0" ]]; then
                daemons_ctl.sh -c $CONFIGFILE -k $STARTSTOPDAEMONCONFIGFILE -l $DAEMONLOGPATH restart_and_check
                echo "----------------------------"
                echo "<Premi invio per continuare>"
                read
            fi
        ;;
        6) # Invia il segnale HUP a tutti i demoni gestibili
            clear
            if [[ $CONFIRM == "1" ]]; then
                confirm "Invia il segnale HUP a tutti i demoni gestibili"
                RET=$?
                clear
            else
                RET=0
            fi
            if [[ $RET == "0" ]]; then
                daemons_ctl.sh -c $CONFIGFILE -k $STARTSTOPDAEMONCONFIGFILE -l $DAEMONLOGPATH signal_hup_and_check
                echo "----------------------------"
                echo "<Premi invio per continuare>"
                read
            fi
        ;;
        7) # Seleziona i demoni gestibili
            clear
            select_active_daemons
        ;;
        8) # Verifica il file di configurazione dei demoni gestibili
            clear
            daemons_ctl.sh -c $CONFIGFILE -k $STARTSTOPDAEMONCONFIGFILE -l $DAEMONLOGPATH check_config
            echo "----------------------------"
            echo "<Premi invio per continuare>"
            read
        ;;
        9) # Controlla lo stato di tutti i demoni utilizzabili
            clear
            daemons_ctl.sh -c $CONFIGFILE -k $STARTSTOPDAEMONCONFIGFILE -l $DAEMONLOGPATH check_all
            echo "----------------------------"
            echo "<Premi invio per continuare>"
            read
        ;;
        L) # Legenda
            clear
            dialog --msgbox "GUI per la gestione dei demoni:\n- demoni configurati: elenco dei demoni configurati (in $STARTSTOPDAEMONCONFIGFILE) per l'utilizzo con \"start_stop_daemon\"\n- demoni gestibili: elenco dei demoni gestibili con \"daemons_ctl.sh\" (elencati in $CONFIGFILE)" 0 0
        ;;
        *) # controllo per qualsiasi altro valore esco
            clear
            rm -f $TEMPFILEMAIN
            exit 0
        ;;
    esac
done
