#!/bin/bash

COMMAND=$(basename $0 .sh | sed 's/^daemons_//')

function usage {
  echo
  echo "uso: $0 [arguments] [demone1[ demone2 [...]]]"
  echo
  echo "   shortcut al comando \"daemons_ctl.sh $COMMAND\""
  echo
  echo "     demoni configurati ..... elenco dei demoni configurati (in $STARTSTOPDAEMONCONFIGFILE)"
  echo "                              per l'utilizzo con \"start_stop_daemon\""
  echo "     demoni gestibili ....... elenco dei demoni gestibili con \"daemons_ctl.sh\" (elencati in $CONFIGFILE)"
  echo
  echo "   arguments:"
  echo "     -q ..................... non visualizza la descrizione del comndo che sta per eseguire"
  echo "     -c <file> .............. file di configurazione dei demoni gestibili con \"daemons_ctl.sh\""
  echo "                              default: env DAEMONS_ACTIVE_CONFIG_FILE o $HOME/etc/daemons.active"
  echo "                              valore attuale: $CONFIGFILE"
  echo "     -k <file> .............. file di configurazione dei demoni configurati per l'utilizzo con \"start_stop_daemon\""
  echo "                              default: env SSD_CONFIG_FILE o $HOME/etc/ssd_config"
  echo "                              valore attuale: $STARTSTOPDAEMONCONFIGFILE"
  echo "     -l <file> .............. directory in cui verra' creato il file daemon.nome_demone.err.YYYYMMDD"
  echo "                              default env LOG_CONFIG o $HOME/var/log"
  echo "                              valore attuale $DAEMONLOGPATH"
  echo "     -h ..................... mostra questa schermata d'aiuto"
  echo 
  echo "     [demone1[ demone2 [...]]] ... opzionale. se valorizzato lavora solo sui demoni indicati. I demoni devono"
  echo "                                   essere gestibili, ovvero elencati in $CONFIGFILE"
  echo
}

if [[ -z $DAEMONS_ACTIVE_CONFIG_FILE ]]; then
  CONFIGFILE=$HOME/etc/daemons.active
else
  CONFIGFILE=$DAEMONS_ACTIVE_CONFIG_FILE
fi
if [[ -z $SSD_CONFIG_FILE ]]; then
  STARTSTOPDAEMONCONFIGFILE=$HOME/etc/ssd_config
else
  STARTSTOPDAEMONCONFIGFILE=$SSD_CONFIG_FILE
fi
if [[ -z $LOG_PATH ]]; then
  DAEMONLOGPATH=$HOME/var/log
else
  DAEMONLOGPATH=$LOG_PATH
fi
QUIETART=""

while getopts hqc:k:l: flag; do
  case $flag in
    h)
      usage
      exit 0
      ;;
    q)
      QUIETARG='-q'
      ;;
    c)
      CONFIGFILE=${OPTARG}
      ;;
    k)
      STARTSTOPDAEMONCONFIGFILE=${OPTARG}
      ;;
    l)
      DAEMONLOGPATH=${OPTARG}
      ;;
    ?)
      usage
      exit 4
      ;;
  esac
done

shift $(( OPTIND - 1 ));

DAEMONS=$@

daemons_ctl.sh $QUIETARG -c $CONFIGFILE -k $STARTSTOPDAEMONCONFIGFILE -l $DAEMONLOGPATH $COMMAND $DAEMONS
