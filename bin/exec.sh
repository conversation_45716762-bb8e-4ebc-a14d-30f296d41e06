#!/bin/sh 

#--------------------------------------------
# EXEC
#--------------------------------------------

. /etc/profile

if [[ ! -x "$HOME/etc/profile.sh" ]] ; then
	echo "$HOME/etc/profile.sh not found!"
	exit 1
fi  

. $HOME/etc/profile.sh $HOME

function get_time {  date '+%Y%m%d%H%M%S'; }

function ignore { :;}

function cmd_bg {
	[ "$BG" ] && trap ignore sighup
	local log="$1"
	shift
	local ex="$1"
	shift
	local rc=0
	if [ "$BG" ]; then
		"$ex" "$@" > $log 2>&1
		rc=$?	
	else 
		"$ex" "$@" 2>&1 | tee $log
		rc="$PIPESTATUS"
	fi
	if [ "$MAILADDR_BG" ]; then
		touch $HOME/.muttrc
		echo "$ex: comando finito con rc $rc - file di log: $log" | \
			mutt -F $HOME/.muttrc -s "resoconto elaborazione $ex" -a $log -- $MAILADDR_BG  2>> $log
	fi
	return $rc
}
	
function cmd {
	local ex="$1"
	shift
	local log="${LOG_PATH}/$(basename ${ex})_$(get_time).log"	
	local rc=0

	if [ "$HOLIDAY" != "1" ]; then
		echo -ne "Verifica se si tratta di giornata lavorativa ("
		if [ "$WS" == 1 ]; then
			echo -n "sabato lavorativo"
			PARAM='--ws'
		else
			echo -n "sabato non lavorativo"
		fi
		echo -n ") ... "
		lavorativo $PARAM
		if [ "$?" == "0" ]; then
			echo "OK"
		else
			echo "GIORNATA NON LAVORATIVA ($(date +%d-%m-%Y))"
			return $rc
		fi
	fi
	if [ "$BG" ]; then
		cmd_bg "$log" "$ex" $@ &
		rc=$?
	else
		cmd_bg "$log" "$ex" $@
		rc=$?
	fi
	if [ "$rc" == "0" ]; then
		echo -e -n "\n\nIl comando:\n\n\t$ex\n\ne' in esecuzione"
		if [ "$BG" ]; then
			echo -e -n " in background"
		fi
	else
		echo -e "\n\nL'esecuzione del comando:\n\n\t$ex\n\ne' fallita con exit code=$rc"
	fi
	echo -e "\n\n"
	echo -e "Il resoconto verra' inviato via e-mail ai seguenti recipient:\n\n\t$MAILADDR_BG\n\n"
	return $rc
}


##  main ##

PROG="$0"

function usage {
	cat <<!
$PROG [-h|-n] [-a <ind_posta>] [--] <comando> [<argomenti>..]
    esegue un comando producendo un log file ed inviandolo in allegato via posta elettronica 
    -h - questo help
    -n - non esegue il comando in background
    -a <ind_posta> - invia il log file agli indirizzi di posta <ind_posta>
        se non specificato usa il valore della variabile d'ambiente MAILADDR_BG 
    -x - non esegue il comando nei giorni NON lavorativi
    -y - non esegue il comando nei giorno NON lavorativi (sabato lavorativo)
    -l - percorso dove scrivere il file di log
        se non specificato usa il valore della variabile d'ambiente LOG_PATH 
!
	exit $1
}

t=$(getopt -o hna:xyl: -- "$@" ) || exit 99

eval set -- "$t"
unset t

BG="Y"
HOLIDAY="1"
while true
do
	case "$1" in 
		-h)
			usage 0
			;;
		-n)
			BG=""
			;;
		-x)
			HOLIDAY="0"
			;;

		-y)
			HOLIDAY="0"
			WS="1"
			;;

		-a)
			shift
			MAILADDR_BG="$1"
			;;
		-l)
			shift
			LOG_PATH="$1"
			;;
		--)
			shift
			break;
			;;
		*)
			echo "internal error">&2
			exit 99
	esac
	shift
done

[ $# -eq 0 ] && usage 1
cmd $@






