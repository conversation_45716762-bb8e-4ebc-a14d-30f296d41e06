#!/usr/bin/env perl

use strict;
use warnings;
use Carp 'verbose';
use Data::Dumper;
use Getopt::Long;
Getopt::Long::Configure("bundling");
use File::Basename;
use SIRTI::DB;
use API::ART;
use Tie::IxHash;

###############################################################################
#
#   V A R I A B I L I   E   C O S T A N T I   G L O B A L I
#
###############################################################################

# ENUMerazione degli errori
use enum qw	(:ERROR_=0
  OK=0
  UNKNOWN
  BAD_ARGUMENT
  MISSING_ARGUMENT
  CONNECT_STRING_MISSED
  CURSOR_CREATION
  DB_CONNECTION
  DB_ERROR
  DB_FATAL_ERROR
  DB_IS_ERROR
  NO_SWITCH
  SHOW_USAGE
);

# Script corrente
my $script = basename($0);

$| = 1;    # Non bufferizza STDOUT

# connessione al DB
my $db;
my $api_art;

# Variabili x i parametri in input
my $status        = ERROR_UNKNOWN;
my $tipo_attivita; 
my $conn;           
my $file          = '';
my $cnt_actions   = 0;
my $cnt_status    = 0;
my $debug         = 0;
my $bindings	  = 0;
my $app_base	  = 'API::ART::APP::Activity';
my $artid;
my $art_user;
my $art_password;


###############################################################################
#
#   M A I N
#
###############################################################################

# Controllo SYNOPSIS ----------------------------------------------------------
SWITCH: {

    ##### Switch (opzioni)
    unless (
        GetOptions(
            'h|help'   => sub { $status = ERROR_SHOW_USAGE },
            't|ta=s'   => \$tipo_attivita,
            'c|conn=s' => \$conn,
            'a|ca'     => \$cnt_actions,
            's|cs'     => \$cnt_status,
			'b|bn'	   => \$bindings,
            'd|debug'  => \$debug,
			'artid=s'  => \$artid,
			'user=s'   => \$art_user,
			'pw=s' 	   => \$art_password,
        )
      )
    {
        $status = ERROR_BAD_ARGUMENT;
        last SWITCH;
    }

    # Richiesta visualizzazione usage()
    last SWITCH if $status == ERROR_SHOW_USAGE;

   # Se ci sono ulteriori argomenti (e la sintassi non lo prevede) genero errore
    if ( scalar(@ARGV) > 0 ) {
		print("Parametro non riconosciuto: " . join(',',@ARGV));
        $status = ERROR_BAD_ARGUMENT;
        last SWITCH;
    }

    # Argomenti obbligatori
    unless( ( defined $conn || defined $artid) ) {
        print("Parametro/i di connessione obbligatorio mancante!");
        $status = ERROR_MISSING_ARGUMENT;
        last SWITCH;
    }

	unless ( defined $tipo_attivita ){
		print ("parametro TIPO_ATTIVITA obbligatorio mancante");
		$status = ERROR_MISSING_ARGUMENT;
		last SWITCH;
	}

	if (defined $conn && defined $artid){
        print("Non e' possibile impostare conn ed artid contemporaneamente");
        $status = ERROR_BAD_ARGUMENT;
        last SWITCH;
	}

	if (defined $bindings && !defined $artid){
		print("Il parametro BINDING richiede l'uso di ARTID");
		last SWITCH;
	}

}    # end SWITCH

# MAIN ------------------------------------------------------------------------
MAIN: {

    # Parametri in input corretti?
    &usage()
      && last MAIN
      if grep( /^$status$/,
        ( ERROR_SHOW_USAGE, ERROR_BAD_ARGUMENT, ERROR_MISSING_ARGUMENT ) );

    # connessione al DB

	CONN_DB: {

		if ($conn){
    		$db = new SIRTI::DB(
        		$conn,
        		AUTOCOMMIT => 0,
        		DEBUG      => $debug,
    		);
		}

		if ($artid){

			$api_art = API::ART->new(
    			ARTID    => $artid,
    			USER     => $art_user,
    			PASSWORD => $art_password,
    			AUTOSAVE => 0,
    			DEBUG    => $debug,
			);

			die 'errore: ', $api_art->last_error() unless defined $api_art;

			$db=$api_art->_dbh();
		}

	}

    print get_dot_intestazione();
    print get_dot_archi();
    print get_dot_stati();
    print get_dot_chiusura();

    $status = ERROR_OK;

}

exit $status;

###############################################################################
#
#   F U N Z I O N I
#
###############################################################################

sub usage() {
    print(
        qq(

	Estrazione DOT da Tipo Attivita

Usage:

  $script <OPTIONS>

  Opzioni:

  -c, 	--conn ..................: [*] connect string istanza ART

        connessione tramite artid (alternativo alla connect-string)
        --artid .................:     in alternativa alla connect string
        --user ..................:     utente art
        --pw ....................:     password di accesso
  
  -t,	--ta ....................: [*] tipo attivita

  -a,   --ca ....................:     conta azioni giornaliere
  -s,   --cs ....................:     conta attivita negli stati

  -b,   --bn ....................:     evidenzia binding su stati ed azioni
                                       (richiede l'uso dell'artid)

  Help:
   -h, 	--help ...................: mostra questa schermata

[*] obbligatorio

)
    );
}

sub get_archi {

    my $ta = $db->quote($tipo_attivita);

    return $db->fetchall_hashref(

        qq(
-- ARCHI
select
distinct
(select nome from stati where id_stato=pa.id_stato_iniziale and rownum<2) stato_iniziale,
(select nome from stati where id_stato=pa.id_stato_finale and rownum<2) stato_finale,
(select nome from action where id_action=pa.id_action_permessa and rownum<2) azione
from permission_action pa, gruppi gr, tipi_attivita ta
where pa.id_tipo_attivita=ta.id_tipo_attivita
and ta.nome_tipo_attivita=$ta
)

    );

}

sub get_stati {

    my $ta = $db->quote($tipo_attivita);

    return $db->fetchall_hashref(

        qq(
-- STATI
select distinct
st.nome stato, st.flag_stato_partenza tipo
from permission_action pa, gruppi gr, tipi_attivita ta, stati st
where pa.id_tipo_attivita=ta.id_tipo_attivita
and st.id_stato=pa.id_stato_iniziale
and ta.nome_tipo_attivita=$ta
union
select distinct
st.nome, st.flag_stato_partenza
from permission_action pa, gruppi gr, tipi_attivita ta, stati st
where pa.id_tipo_attivita=ta.id_tipo_attivita
and st.id_stato=pa.id_stato_finale
and ta.nome_tipo_attivita=$ta
)
    );

}

sub qquote { '"' . $_[0] . '"' }

#
# PRODUZIONE CODICE DOT
#

sub get_dot_intestazione {

    qq(
digraph $tipo_attivita {

//
// intestazione
//
	label="$tipo_attivita";
	fontname="Helvetica";
	fontstyle="bold";
	margin=0.0;
	rank=sink;
	rankdir="TB";
	ranksep=1;
	minlen=1;
	nodesep=2;
	quantum=.6;
	graph[ratio=fill,center=1];
	node[shape=ellipse,fontname="Helvetica"];


)

}

sub get_dot_chiusura {

    qq(
	
//
// chiusura
//
	}
)

}

sub get_param_stati {

    my $r = shift;

    my $out = '';

    my %h = (
        shape     => 'ellipse',
        fontcolor => 'black',
        fillcolor => 'grey',
    );

	if ($bindings){
		my $binding_class_status = _test_binding_class('Status', $r->{STATO});
	
		if ( $binding_class_status->{HANDLER} ) {
			$h{penwidth} = 4;
		}

		if ( $binding_class_status->{STATUS} eq 'KO' ){
			$h{penwidth} = 4;
			$h{color} = 'red';
		}
	}

    # stato iniziale
    if ( $r->{TIPO} eq 'X' ) {
        $h{shape} = 'diamond';
    }

    # stato aperta
    if ( $r->{TIPO} eq 'A' ) {
        $h{style} = 'filled';
    }

    # stato finale
    if ( $r->{TIPO} eq 'Q' || $r->{TIPO} eq 'C' ) {
        $h{style} = 'filled';
    }

    join( ',', map { "$_=$h{$_}" } keys %h );

}

sub get_dot_stati {

    my $ta = $db->quote($tipo_attivita);

    my $h = $cnt_status ? get_hash_status_count() : {};

    my $out = qq(

//
//stati
//
);

    my $stati = get_stati();

    for my $r (@$stati) {

        $out .= sprintf(
            "\t%-30s\t\t [%s];\n",
            qquote( $r->{STATO} ),
            get_param_stati($r)
              . (
                $cnt_status
                ? sprintf( ',label="%s\\n[%d]"',
                    $r->{STATO},
                    defined $h->{ $r->{STATO} } ? $h->{ $r->{STATO} } : 0 )
                : ''
              )
        );

    }

    $out .= "\n\n";

    return $out;
}

sub get_dot_archi {

    my $out = qq(

//
// archi
//
);

    my $h = $cnt_actions ? get_hash_actions_count() : {};

    my $archi = get_archi();

    for my $r (@$archi) {

		my $str_attribute='';

		if ($bindings){

			my $binding_class_status = _test_binding_class('Action', $r->{AZIONE});

			$str_attribute = 
				$binding_class_status->{STATUS} eq 'KO'  ? ',penwidth=4,color=red' :
				$binding_class_status->{HANDLER}  		 ? ',penwidth=4' 		   :
				'';
		}

        $out .= sprintf(
            "\t%-30s\t->%-30s [label=%-30s %s];\n",
            qquote( $r->{STATO_INIZIALE} ),
            qquote( $r->{STATO_FINALE} ),
            $cnt_actions
            ? qquote(
                sprintf( "%s\\n[%d]",
                    $r->{AZIONE},
                    defined $h->{ $r->{STATO_INIZIALE} }{ $r->{STATO_FINALE} }
                      { $r->{AZIONE} }
                    ? $h->{ $r->{STATO_INIZIALE} }{ $r->{STATO_FINALE} }
                      { $r->{AZIONE} }
                    : 0 )
              )
            : qquote( $r->{AZIONE} ),
			$str_attribute	
        );

    }

    $out .= "\n\n";

    return $out;
}

sub get_hash_actions_count() {

    my %out;
    my $ta = $db->quote($tipo_attivita);

    my $res = $db->fetchall_hashref(
        qq( 
SELECT azione, stato_iniziale, stato_finale, count(1) cnt FROM (
SELECT
  act.nome azione,
  sr.nome stato_finale,
  (
    SELECT st.nome
      FROM storia_attivita s, stati st
      WHERE s.id_attivita=sa.id_attivita
      AND st.id_stato=s.id_stato_risultante
      AND s.data_esecuzione = (
        SELECT MAX(data_esecuzione)
        from storia_attivita
        WHERE id_attivita=sa.id_attivita
        AND data_esecuzione<sa.data_esecuzione
      )
  ) stato_iniziale
FROM storia_attivita sa, attivita att, tipi_attivita ta, stati sr, action act
WHERE att.id_tipo_attivita=ta.id_tipo_attivita
AND ta.nome_tipo_attivita=$ta
AND att.id_attivita=sa.id_attivita
AND sr.id_stato=sa.id_stato_risultante
AND sa.id_action=act.id_action
AND sa.data_esecuzione > trunc(SYSDATE)
)
GROUP BY azione, stato_iniziale, stato_finale
)
    );

    map {
        $out{
            defined $_->{STATO_INIZIALE}
            ? $_->{STATO_INIZIALE}
            : 'START'
          }{ $_->{STATO_FINALE} }{ $_->{AZIONE} } =
          $_->{CNT}
    } @$res;

    return \%out;

}

sub get_hash_status_count() {
    my %out;
    my $ta  = $db->quote($tipo_attivita);
    my $res = $db->fetchall_hashref(
        qq(
select st.nome stato, count(1) cnt from attivita att, tipi_attivita ta, stati st
where ta.nome_tipo_attivita=$ta
and att.stato_corrente=st.id_stato
and att.id_tipo_attivita=ta.id_tipo_attivita
group by st.nome
)
    );

    map { $out{ $_->{STATO} } = $_->{CNT} } @$res;

    return \%out;
}

# simula memoize
my $test_binding_class_memoize = {};
sub _test_binding_class{
	my ($type, $name) = @_;

	unless (defined ($test_binding_class_memoize->{$type}{$name})){
		$test_binding_class_memoize->{$type}{$name} = 
			_test_binding_class_real($type, $name);
	} 

	return $test_binding_class_memoize->{$type}{$name};
}

sub _test_binding_class_real{
	my ($type, $name) = @_;

	my $class_name = $api_art->get_activity_binding_class_name($tipo_attivita, $type, $name);

	my $out = $api_art->test_instance_by_class_name($class_name);

	print STDERR $class_name, " : ", $out->{STATUS}, $out->{HANDLER} ? ' [BINDED]' : '' , "\n";
	
	return $out;
}
