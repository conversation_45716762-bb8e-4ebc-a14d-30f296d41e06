#!/usr/bin/env perl

use strict;
use Encode::Guess;

if(@ARGV != 1)
{
    print "Usage: perl $0 <file to analyze>\n";
    exit;
}

my @files = glob($ARGV[0]);

foreach my $file (@files)
{

	if (! -f $file ) {
		print STDERR "File does not exist\n";
		exit 4;
	}

	my @fstat = stat($file);

    if (!open(FILE,$file)){
        print STDERR "Cannot open File! $!\n";
        exit 5;
    }
    binmode(FILE);
    if(read(FILE,my $content, $fstat[7])) {
		my $enc = guess_encoding($content);
		
		if(ref($enc)) {
			print uc($enc->name) ,"\n";
		} else {
			print STDERR "Encoding of file $file can't be guessed\n";
			exit 1;
		}
	} else {
		print STDERR "Cannot read from file $file\n";
		exit 2;
	}
	close(FILE);
}

exit 0;


__END__

=head1 NAME

B<getencoding.pl> - Consente di capire l'encoding utilizzato nel file passato come parametro

=head1 SYNOPSIS

getencoding.pl test.txt
returns: utf8

=head1 BUGS

Eventuali bug riscontrati dovranno essere segnalati su B<ART - Global Services>: L<https://www.artnet.sirtisistemi.net/ARTIT/art> aprendo un apposito DEV-TICKET.

=head1 HISTORY

=over

=item Ver. 0.01

Prima release del modulo

=back

=head1 AUTHOR

Gruppo OLO/TLC/PM/NUE/SSDB/SD/LEADS/DBU/.. :)

=cut

