#!/bin/bash

######## /Git/doc/git/html/git-diff.html vedi --diff-filter
##
## --diff-filter=[(A|C|D|M|R|T|U|X|B)…[*]]
## Select only files that are Added (A), Copied (C), Deleted (D), Modified (M), Renamed (R), have their
## type (i.e. regular file, symlink, submodule, …) changed (T), are Unmerged (U), are Unknown (X), or have
##  had their pairing Broken (B). Any combination of the filter characters (including none) can be used.
## When * (All-or-none) is added to the combination, all paths are selected if there is any file that
## matches other criteria in the comparison; if there is no file that matches other criteria, nothing is selected.
##

function usage {
  cat <<!

  Usage:

    $(basename $0) [<opzioni>] PRJ FROM_TAG TO_TAG REPO_GIT

      PRJ........: nome del progetto
      FROM_TAG...: versione del TAG iniziale
      TO_TAG.....: versione del TAG finale
      REPO_GIT...: url del repository GIT del progetto (o local path se viene utilizzata l'opzione -l)

    <opzioni>

      -t ........: utilizzare questa opzione se le tag non hanno come prefisso PRJ
                   utilizzarla se le tag hanno formato X.Y.Z,
                   non utilizzarla se le tag hanno formato PRJ_X.Y.Z
      -j <jira_project_name> ..: indicare il nome del progetto JIRA se diverso da PRJ
      -l ........: il quarto argomento indica un path locale dove è presente una working copy aggiornata
                   del progetto
      -h ........: questo help

    Esempi:

      $(basename $0) ARTDII 0.0.1 0.9.2 ssh://dvmnode001.ict.sirti.net/home/<USER>/git/ARTDII.git
      $(basename $0) -t ADCP 0.1.0-0 0.1.0-1 https://<EMAIL>/SirtiWPL/avviso-di-consegna-pyramide.git
      $(basename $0) -j RTLIT ARTRETELIT 3.1.0-rc.2 4.0.0-RTLIT-2.0 ssh://dvmnode001.ict.sirti.net/home/<USER>/git/ARTDII.git
      $(basename $0) -l COM 7.6.0-0 7.6.0-1 $COM

!
    exit $1
}

#
# Imposta l'installer che dovra' essere copiato in ogni pacchetto
#
INSTALLER="$(dirname $0)/patch_installer.sh"
if [[ ! -e "${INSTALLER}" ]]; then
	echo "Missing ${INSTALLER}!"
	exit 1
fi

NO_PRJ_IN_TAG_NAME=0
JIRA_PROJECT=""
USE_LOCAL_WC=0
t=$(getopt -o hj:lt -- "$@" ) || exit 1
while true
do
	case "$1" in
		-h)
			usage 0
			;;
		-t)
			NO_PRJ_IN_TAG_NAME=1
			;;
		-j)
			if [[ $JIRA_PROJECT = "" ]]; then
				JIRA_PROJECT=$2
			else 
				JIRA_PROJECT="${JIRA_PROJECT}|${2}"
			fi
			shift
			;;
		-l)
			USE_LOCAL_WC=1
			;;
		*)
			break
			;;
	esac
	shift
done

#
# Verifica parametri in input
#
PRJ=$1
FROM_TAG=$2
TO_TAG=$3
REPO_GIT=$4
if [[ $JIRA_PROJECT == "" ]]; then
	JIRA_PROJECT=$PRJ
fi

if [ "$PRJ" == "" -o "$FROM_TAG" == "" -o "$TO_TAG" == "" -o "$REPO_GIT" == "" ]; then
	usage 1
fi

#
# Definisce le variabili principali
#
GIT_CONF_DIR="${HOME}/.gitbuild"
PRJ_DIR="$GIT_CONF_DIR/${PRJ}"
TAG_DIR="$GIT_CONF_DIR/${PRJ}_${TO_TAG}"
PATCH_NAME="${PRJ}_${FROM_TAG}_${TO_TAG}"
PATCH_DIR="$GIT_CONF_DIR/${PATCH_NAME}"
PATCH_TAR="${PATCH_DIR}.tar"

FROM_TAG_NAME=${PRJ}_${FROM_TAG}
if [[ ${NO_PRJ_IN_TAG_NAME} == 1 ]]; then
	FROM_TAG_NAME=${FROM_TAG}
fi
TO_TAG_NAME=${PRJ}_${TO_TAG}
if [[ ${NO_PRJ_IN_TAG_NAME} == 1 ]]; then
	TO_TAG_NAME=${TO_TAG}
fi

#
# Messaggio di riepilogo prima della creazione del pacchetto
#
cat <<!

=================
GIT PATCH CREATOR
=================

Requested patch creation:

	PROJECT .......: ${PRJ}
	GIT REPO ......: ${REPO_GIT}
	FROM TAG ......: ${FROM_TAG}
	TO TAG ........: ${TO_TAG}
	JIRA PROJECT ..: ${JIRA_PROJECT}
	PATCH NAME ....: ${PATCH_NAME}

Output information:

	PROJECT DIR ...: ${PRJ_DIR}
	TAG DIR .......: ${TAG_DIR}
	PATCH DIR .....: ${PATCH_DIR}
	PATCH TAR .....: ${PATCH_TAR}

!

if [[ $USE_LOCAL_WC == "1" ]]; then
	cat <<!
WARNING: you are using a local working copy: are you sure it is up-to-date? (use git fetch to check)

!
fi

cat <<!
Press CTRL+C to cancel or ENTER to start...

!
read


###############################################################################
#
# Inizio creazione pacchetto
#

#
# Verifica che $GIT_CONF_DIR esista
#
if [ ! -d $GIT_CONF_DIR ]; then
	echo "ERROR: base dir '${GIT_CONF_DIR}' does not exist: please create it and retry!"
	exit 1
fi

#
# Verifica che $PATCH_TAR non esista
#
if [ -f $PATCH_TAR ]; then
	echo "ERROR: patch '${PATCH_TAR}' already exists: please remove it and retry!"
	exit 1
fi

echo -n " * Creating PATCH_DIR ... "
if [ -d ${PATCH_DIR} ]; then
	echo "ERROR: patch directory '${PATCH_DIR}' already esists; please, check and retry!"
	exit 1
fi
mkdir ${PATCH_DIR}
[[ "$?" != "0" ]] && exit 1
echo "OK"

echo -n " * Creating TAG_DIR ... "
if [ -d $TAG_DIR ]; then
	echo "ERROR: tag directory '${TAG_DIR}' already exists: please remove it and retry!"
	exit 1
fi
mkdir ${TAG_DIR}
[[ "$?" != "0" ]] && exit 1
echo "OK"

if [[ $USE_LOCAL_WC == "0" ]]; then
	#
	# Verifica che $PRJ_DIR non esista
	#
	if [ -d $PRJ_DIR ]; then
		echo "ERROR: project directory '${PRJ_DIR}' already exists: please remove it and retry!"
		exit 1
	fi
	#
	# Effettua il clone del repository
	#
	echo -n " * Cloning git repository to PRJ_DIR, please wait... "
	git clone --quiet ${REPO_GIT} $PRJ_DIR
	[[ "$?" != "0" ]] && exit 1
	echo "OK"
else
	echo -n " * Using git working copy in REPO_GIT... "
	PRJ_DIR=${REPO_GIT}
	echo "OK"
fi

echo -n " * Switching to project directory... "
pwd=$(pwd)
cd ${PRJ_DIR}
[[ "$?" != "0" ]] && exit 1
echo "OK"

#
# Verifica che le tag esistano
#

git tag -l ${FROM_TAG_NAME} | grep ${FROM_TAG_NAME} > /dev/null
if [[ "$?" != "0" ]]; then
	echo "ERROR: tag '${FROM_TAG}' does not exist!"
	exit 1
fi

git tag -l ${TO_TAG_NAME} | grep ${TO_TAG_NAME} > /dev/null
if [[ "$?" != "0" ]]; then
	echo "ERROR: tag '${TO_TAG}' does not exist!"
	exit 1
fi

#
# Effettua l'export della tag finale
# FIXME: prevedere metodo più efficiente per estrarre solo i file da includere nella patch
#
echo -n " * Exporting final tag to TAG_DIR, please wait... "
git archive --format=tar ${TO_TAG_NAME} | (cd $TAG_DIR/ && tar xf -)
[[ "$?" != "0" ]] && exit 1
echo "OK"

#
# verifica
#
cmd="$(cat <<!
git diff ${FROM_TAG_NAME} ${TO_TAG_NAME} --name-status -- \
	| grep -vP '^(A|M|T|C|D|R\d{3})\t' > /dev/null
!
)"
eval $cmd
if [[ "$?" == "0" ]]; then
	echo "ERROR: unable to proceed cause git diff output entry does not match '^(A|M|T|C|D|R)\\t' regexp! ...sorry, I'm a rookie!!!"
	echo "DIFF OUTPUT:"
	git diff ${FROM_TAG_NAME} ${TO_TAG_NAME} --name-status --
	exit 1
fi

#
# Creazione elenco file modificati
#
echo -n " * Creating MODIFIED file list ... "
cmd="$(cat <<!
git diff ${FROM_TAG_NAME} ${TO_TAG_NAME} --name-status --diff-filter=MT -- \
	| sed -e 's/^..//' \
	| while read F; do echo \$F; done \
	| sort > ${PATCH_DIR}/${PATCH_NAME}_MODIFY.txt
!
)"
eval $cmd
[[ "$?" != "0" ]] && exit 1
echo "OK"

#
# FIXME: da capire se in git esistono...
#
# # Creazione elenco file con permessi modificati (da rimuovere in fase di installazione)
# #
# echo -n " * Looking for TYPE-MODIFIED file list (adding do DELETE list)... "
# cmd="$(cat <<!
# git diff ${FROM_TAG_NAME} ${TO_TAG_NAME} --name-status \
# 	| grep -P '^(MM| M) ' \
# 	| sed -e 's/^..//' \
# 	| while read F; do [[ -f \$F ]] && echo \$F; done \
# 	| sort >> ${PATCH_DIR}/${PATCH_NAME}_DELETE.txt
# !
# )"
# #echo $cmd
# eval $cmd
# [[ "$?" != "0" ]] && exit 1
# echo "OK"
# 	
# #
# # Creazione elenco file con permessi modificati (da rilaciare in fase di installazione)
# #
# echo -n " * Looking for TYPE-MODIFIED file list (adding do ADD list)... "
# cmd="$(cat <<!
# git diff ${FROM_TAG_NAME} ${TO_TAG_NAME} --name-status \
# 	| grep -P '^(MM| M) ' \
# 	| sed -e 's/^..//' \
# 	| while read F; do [[ -f \$F || -d \$F ]] && echo \$F; done \
# 	| sort >> ${PATCH_DIR}/${PATCH_NAME}_ADD.txt
# !
# )"
# #echo $cmd
# eval $cmd
# [[ "$?" != "0" ]] && exit 1
# echo "OK"

#
# Creazione elenco file nuovi
#
echo -n " * Creating ADDED file list ... "
ADD_TEMPFILE=$(mktemp)
cmd="$(cat <<!
git diff ${FROM_TAG_NAME} ${TO_TAG_NAME} --name-status --diff-filter=AC -- \
	| sed -e 's/^..//' \
	| while read F; do echo \$F; done > ${ADD_TEMPFILE}
!
)"
eval $cmd
[[ "$?" != "0" ]] && exit 1
echo "OK"

#
# Creazione elenco file rinominati da aggiungere
#
echo -n " * Creating RENAMED file list to ADD... "
cmd="$(cat <<!
git diff ${FROM_TAG_NAME} ${TO_TAG_NAME} --name-status --diff-filter=R -- \
	| sed -e 's/^.....//' \
	| cut -f 2 \
	| while read F; do echo \$F; done >> ${ADD_TEMPFILE}
!
)"
eval $cmd
[[ "$?" != "0" ]] && exit 1
echo "OK"

#
# ordinamento file ADD
#
echo -n " * Sorting ADDED file list... "
cmd="$(cat <<!
sort ${ADD_TEMPFILE} > ${PATCH_DIR}/${PATCH_NAME}_ADD.txt
!
)"
eval $cmd
[[ "$?" != "0" ]] && exit 1
echo "OK"

rm -f ${ADD_TEMPFILE}

#
# Creazione elenco file da eliminare
#
echo -n " * Creating DELETED file list ... "
DELETE_TEMPFILE=$(mktemp)
cmd="$(cat <<!
git diff ${FROM_TAG_NAME} ${TO_TAG_NAME} --name-status --diff-filter=D -- \
	| sed -e 's/^..//' > ${DELETE_TEMPFILE}
!
)"
eval $cmd
[[ "$?" != "0" ]] && exit 1
echo "OK"

#
# Creazione elenco file rinominati da rimuovere
#
echo -n " * Creating RENAMED file list to DELETE... "
cmd="$(cat <<!
git diff ${FROM_TAG_NAME} ${TO_TAG_NAME} --name-status --diff-filter=R -- \
	| sed -e 's/^.....//' \
	| cut -f 1 \
	| while read F; do echo \$F; done >> ${DELETE_TEMPFILE}
!
)"
eval $cmd
[[ "$?" != "0" ]] && exit 1
echo "OK"

#
# ordinamento file DELETE
#
echo -n " * Sorting DELETED file list... "
cmd="$(cat <<!
sort ${DELETE_TEMPFILE} > ${PATCH_DIR}/${PATCH_NAME}_DELETE.txt
!
)"
eval $cmd
[[ "$?" != "0" ]] && exit 1
echo "OK"

rm -f ${DELETE_TEMPFILE}

#
# Creazione elenco Jira issues coinvolte nella build
#
echo -n " * Creating JIRA ISSUES file list ... "
cmd="$(cat <<!
git log --format=format:'%s%n%b' ${FROM_TAG_NAME}..${TO_TAG_NAME} \
	| grep -P "^(${JIRA_PROJECT})-[0-9]+" \
	| sed -r "s/^(${JIRA_PROJECT})-([0-9]+).*/\\1-\\2/" \
	| sort -u >> ${PATCH_DIR}/${PATCH_NAME}_JIRA_ISSUES.txt
!
)"
eval $cmd
[[ "$?" != "0" ]] && exit 1
echo "OK"
JIRA_ISSUES=$(cat ${PATCH_DIR}/${PATCH_NAME}_JIRA_ISSUES.txt)

#
# Creazione elenco patch sql aggiunte e modificate
#
ADDED_SQL_PATCHES=$(cat ${PATCH_DIR}/${PATCH_NAME}_ADD.txt | grep -i 'sql$')
MODIFIED_SQL_PATCHES=$(cat ${PATCH_DIR}/${PATCH_NAME}_MODIFY.txt | grep -i 'sql$')

#
# Creazione elenco patch sh da lanciare aggiunte
#
ADDED_SH_PATCHES=$(cat ${PATCH_DIR}/${PATCH_NAME}_ADD.txt | grep -i 'share/sql/lanciatore/.*\.sh$')
cat ${PATCH_DIR}/${PATCH_NAME}_ADD.txt | grep -i 'share/sql/lanciatore/.*\.sh$' > ${PATCH_DIR}/${PATCH_NAME}_ADD_SH.txt

echo -n " * Switching to tag directory... "
cd ${TAG_DIR}
[[ "$?" != "0" ]] && exit 1
echo "OK"

#
# Calcolo MD5 dei file modificati e aggiunti
#
echo -n " * Computing MODIFIED file checksum (md5) ... "
cat  ${PATCH_DIR}/${PATCH_NAME}_MODIFY.txt | while read F; do echo $(md5sum "$F"); done > ${PATCH_DIR}/${PATCH_NAME}_MODIFY.md5
[[ "$?" != "0" ]] && exit 1
echo "OK"
echo -n " * Computing ADDED file checksum (md5) ... "
cat  ${PATCH_DIR}/${PATCH_NAME}_ADD.txt | while read F; do echo $(md5sum "$F"); done > ${PATCH_DIR}/${PATCH_NAME}_ADD.md5
[[ "$?" != "0" ]] && exit 1
echo "OK"

#
# Creazione pacchetto di distribuzione
#
echo -n " * Copying installation utility ... "
cp -p ${INSTALLER} ${PATCH_DIR}/install.sh
[[ "$?" != "0" ]] && exit 1
echo "OK"

echo -n " * Creating gzipped file ... "
cat ${PATCH_DIR}/${PATCH_NAME}_MODIFY.txt ${PATCH_DIR}/${PATCH_NAME}_ADD.txt | tar -cvzf ${PATCH_DIR}/${PATCH_NAME}.tar.gz -T - >/dev/null
[[ "$?" != "0" ]] && exit 1
echo "OK"


echo -n " * Creating tarball file ... "
cd $GIT_CONF_DIR >/dev/null 2>&1
tar -cvf ${PATCH_TAR} ${PATCH_NAME} >/dev/null
[[ "$?" != "0" ]] && exit 1
echo "OK"


#
# Rimozione directory temporanee
#
echo -n " * Removing ${PATCH_DIR}... "
rm -rf ${PATCH_DIR} >/dev/null 2>&1
[[ "$?" != "0" ]] && exit 1
echo "OK"

echo -n " * Removing ${TAG_DIR}... "
rm -rf ${TAG_DIR} >/dev/null 2>&1
[[ "$?" != "0" ]] && exit 1
echo "OK"

if [[ $USE_LOCAL_WC == "0" ]]; then
	echo -n " * Removing ${PRJ_DIR}... "
	rm -rf ${PRJ_DIR} >/dev/null 2>&1
	[[ "$?" != "0" ]] && exit 1
	echo "OK"
fi

cd $pwd >/dev/null 2>&1

cat <<!

Process terminated: patch made at ${PATCH_TAR}
!

if [[ $ADDED_SQL_PATCHES != "" ]]; then
	echo
	echo SQL patches added in build:
	echo
	for T in $ADDED_SQL_PATCHES; do
		echo "  - $T"
	done
fi

if [[ $MODIFIED_SQL_PATCHES != "" ]]; then
	echo
	echo SQL patches modified in build:
	echo
	for T in $MODIFIED_SQL_PATCHES; do
		echo "  - $T"
	done
fi

if [[ $ADDED_SH_PATCHES != "" ]]; then
	echo
	echo SH patches added in build:
	echo
	for T in $ADDED_SH_PATCHES; do
		echo "  - $T"
	done
fi

if [[ $JIRA_ISSUES != "" ]]; then
	echo
	echo Issues involved in build:
	echo
	for T in $JIRA_ISSUES; do
		echo "  - $T"
	done
fi

cat <<!

Good luck! ;-)

!


