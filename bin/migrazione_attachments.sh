#!/bin/sh 



function usage {
	cat <<!
$0 [-hvbs] [-w <utente:gruppo>] [ -n <n> ] <dir>
	effettua la migrazione dei file nel repository nellla nuova struttura di directory
	-h - questo aiuto 
	-v - emette il nome del file elaborato
	-b - converte i files da .gz a .bz2 (attenzione: puo richiedere molto tempo)
	-s - non effettua elaborazioni - fa solo una simulazione
	-w - imposta il proprietarie e/o il gruppo nelle sottodirectory create  (lo script deve essere eseguito da root)
	-p - imposta i permessi delle sottodirectory create
	-n <n> - converte non piu di <n> files (gli errori non vengono conteggiati) 
	<dir> - directory radice del repository
!
	exit $1
}


t=$(getopt -o hvbsw:p:n: -- "$@" ) || exit 1
eval set -- "$t"
unset t

CONVERT=""
VERBOSE=""
SYM=""
OWNER=""
PERM=""
N=""

while true
do
	case "$1" in 
		-h)
			usage 0
			;;
		-b)
			CONVERT='Y'
			;;
		-v)
			VERBOSE="Y"
			;;
		-s)
			SYM='(SYM)'
			;;
		-w)
			shift
			OWNER="$1"
			;;
		-p)
			shift;
			PERM="$1"
			;;
		-n)
			shift
			N="$1"
			;;
		--)
			shift
			break;
			;;
		*)
			echo "$1: errore interno">&2
			exit $ERR_SYS
	esac
	shift
done

[ "$UID" -ne 0 -a "$OWNER" ] && { echo "solo root puo impostare il proprietario/gruppo">&2; exit 1; }

if [ "$N"  ]
then
	echo "$N" | grep '^[0-9][0-9]*$' > /dev/null || { echo "$N: valore opzione n non numerico">&2; exit 1; }
fi

dir="$1";
[ "$dir" ] || { echo 'occorre specificare dir. di partenza' >&2; exit 1; }
cd "$dir" || exit $?

if [ "$CONVERT" ]
then
	which gzip  > /dev/null || exit 1
	which bzip2 > /dev/null || exit 1
fi


find . -maxdepth 1 -type f  -name '*.tar.gz' |  (
count_tot=0
count_move=0
while read
do 
	[ "$VERBOSE" ] && echo "elaborazione '$REPLY'"
	count_tot=$(( $count_tot + 1 ))
	[ -r "$REPLY" ] || { echo "$REPLY: file non leggibile" >&2; }
	subdir="$(echo "$REPLY" | perl -ne '/^\.\/\d+-(\d{4})(\d{2})(\d{2})/ && print "$1/$2/$3"')"
	[ "$subdir" ] || { echo "$REPLY: non e' stato possibile ricavare le sottodirectory dal questo nome - il file verra ignorato">&2; continue; }
	cmd="mkdir -p '$subdir'"
	if [ "$SYM" ]
	then
		echo $SYM $cmd >&2
	else
		eval $cmd || { echo "$REPLY: errore crezione subdirs '$subdir'">&2; continue; }
	fi
	if [ "$CONVERT" ]
	then
		outfile="$subdir/$(basename "$REPLY" .gz).bz2"
		cmd="(gzip -dc '$REPLY' | bzip2 -9 >  '$outfile'; exit \${PIPESTATUS[0]}\${PIPESTATUS[1]})"
		if [ "$SYM" ]
		then
			echo $SYM $cmd >&2
		else
			eval "$cmd" || { /bin/rm -f "$outfile"; echo "$outfile: errore creazione"; continue; }
		fi
		cmd="bzip2 -t '$outfile'"
		if [ "$SYM" ]
		then
			echo $SYM $cmd >&2
		else 
			eval "$cmd" || { echo "$outfile: il file non ha superato il test di validita - viene rimosso">&2; continue; }
		fi
		cmd="/bin/rm '$REPLY'"
		if [ "$SYM" ]
		then
			echo $SYM $cmd >&2
		else
			eval "$cmd" ||  { "$REPLY: errore rimozione - il corrispondente file '$outfile'  non viene eliminato"; continue; }
		fi
	else
		cmd="mv '$REPLY' '$subdir'"
		if [ "$SYM" ]
		then
			echo $SYM $cmd >&2
		else
			eval $cmd || { echo "$cmd errore move"; continue; }
		fi			
	fi
	count_move=$(( $count_move + 1 ))
	if [ "$N" ] 
	then 
		[ "$count_move" -ge "$N" ]  && { echo  "uscita per  raggiungimento file convertiti ($N)" >&2; break; }
	fi
done
echo "file letti        : $count_tot"
echo "errori su file    : " $(( $count_tot - $count_move ))
)


if [ -z "$SYM" ]
then
	[ "$OWNER" ] && find . -type d -name '[0123]*' | while read; do chown "$OWNER" "$REPLY"; done
	[ "$PERM" ] && find . -type d -name '[0123]*' | while read; do chmod  "$PERM" "$REPLY"; done 
fi

exit 0


