#!/bin/bash

if [ "$#" == "4" ]; then
	TARGET_DIR=$1
	PRJ=$2
	VERFROM=$3
	VERTO=$4
else
	cat <<!

Sintassi:

	$0  TARGET_DIR  PROJECT  VER_FROM  VER_TO

Esempio:

	$0  /home/<USER>

!
	exit 1
fi

BASE=${PRJ}_${VERFROM}_${VERTO}

TARGET_SYMLINK=${TARGET_DIR}/${PRJ}
TARGET_CUR_DIR=${TARGET_DIR}/${PRJ}_${VERFROM}
TARGET_DEST_DIR=${TARGET_DIR}/${PRJ}_${VERTO}
SOURCE_DIR=$(pwd)
SOURCE_ADD=${SOURCE_DIR}/${BASE}_ADD.txt
SOURCE_ADD_SH=${SOURCE_DIR}/${BASE}_ADD_SH.txt
SOURCE_MODIFY=${SOURCE_DIR}/${BASE}_MODIFY.txt
SOURCE_DELETE=${SOURCE_DIR}/${BASE}_DELETE.txt
SOURCE_TGZ=${SOURCE_DIR}/${BASE}.tar.gz
BACKUP=${SOURCE_DIR}/${BASE}_BACKUP.tar.gz

echo -ne "\n\n\n* Verifica pacchetto... "

for F in ${SOURCE_ADD} ${SOURCE_ADD_SH} ${SOURCE_MODIFY} ${SOURCE_DELETE} ${SOURCE_TGZ}; do
	if [ ! -f ${F} -o ! -r ${F} ]; then
		echo "${F} non leggibile o non trovato!"
		exit 3
	fi
done
for D in ${TARGET_DIR} ${SOURCE_DIR} ${TARGET_CUR_DIR}; do
	if [ ! -d ${D} -o ! -w ${D} ]; then
		echo "${D} non trovata o non scrivibile!"
		exit 2
	fi
done
for S in ${TARGET_SYMLINK}; do
	if [ ! -L ${S} ]; then
		echo "${S} non trovato o non symlink!"
		exit 3
	fi
done
for E in ${BACKUP}; do
	if [ -e ${E} ]; then
		echo "${E} gia' presente!"
		exit 2
	fi
done
for T in ${TARGET_DEST_DIR}; do
	if [ -d ${T} ]; then
		echo "${T} gia' presente!"
		exit 2
	fi
done
echo "OK"

echo -ne "\n\n\n* Premere INVIO per iniziare l'installazione di ${BASE} (CTRL+C per annullare)... "
read

#cat <<EOT

# Directory di rilascio
cd ${TARGET_DIR}
cd ${PRJ}

# Backup PRE-rilascio
echo -e "\n\n\n* Backup PRE-rilascio... "
cat ${SOURCE_MODIFY} ${SOURCE_DELETE} | tar -cvzf ${BACKUP} -T -
if [ "$?" != "0" ]; then
	echo -n "WARNING! Il backup dei file e' terminato con errori, premere INVIO per proseguire comunque o premere CTRL+C per annullare..."
	read
fi


# Eliminazione file obsoleti
echo -ne "\n\n\n* "
if [ -s ${SOURCE_DELETE} ]; then
	echo "Eliminazione file obsoleti... "
	n=0
	for E in $(cat ${SOURCE_DELETE}); do
		if [ ! -e "$E" -a ! -L "$E" ]; then
			echo "${E} non trovato"
			(( n += 1 ))
			continue
		fi
		if [ -f "$E" -o -L "$E" ]; then
			rm -v "$E"
			[[ "$?" != "0" ]] && (( n += 1 ))
			continue
		fi
		if [ -d "$E" ]; then
			rm -vrf "$E"
			[[ "$?" != "0" ]] && (( n += 1 ))
			continue
		fi
		echo "${E} di tipo sconosciuto non eliminato!"
		(( n += 1 ))
	done
	if [ $n -gt 0 ]; then
		echo -n "WARNING! Non e' stato possibile eliminare alcuni dei file previsti, premere INVIO per proseguire comunque o premere CTRL+C per annullare..."
		read
	fi
else
	echo "Nessun file obsoleto da rimuovere"
fi


# Rilascio software
echo -e "\n\n\n* Rilascio software..."
tar -xvzf ${SOURCE_TGZ}
if [ "$?" != "0" ]; then
	echo -n "WARNING! Il rilascio del software e' terminato con errori, premere INVIO per proseguire comunque o premere CTRL+C per annullare..."
	read
fi

echo -e "\n\n\n* Rilocazione directory..."
cd -
rm -v ${PRJ} && mv -v ${PRJ}_${VERFROM} ${PRJ}_${VERTO} && ln -vs ${PRJ}_${VERTO} ${PRJ}

ADDED_SH_PATCHES=$(cat $SOURCE_ADD_SH)

# eseguo le patch sh eventualmente presenti
if [[ $ADDED_SH_PATCHES != "" ]]; then
	echo
	echo SH patches added in build:
	echo
	for T in $ADDED_SH_PATCHES; do
		sh ${TARGET_SYMLINK}/${T}
		rc=$?
		if [ $rc != "0" ]; then
			echo -n "ERRORE nell'esecuzione della patch sh ${TARGET_SYMLINK}/${T}: $rc"
			exit 4
		fi
		if [[ $SQLID != "" ]]; then
			echo Execute procedure MANAGE_DTM
			sqlplus $SQLID << EOF
BEGIN MANAGE_DTM();END;
/
EOF
		fi

	done
fi



# Esecuzione query di rilascio
cat <<!

IMPORTANTE: 

  - verificare necessita' di eseguire il "dataconfig"
  - verificare necessita' di eseguire eventuali patch sql
  - verificare necessita' di riavviare il web-server

!

echo -e "\n\n\n* * *  F I N E ;-) * * *"

#cd ${SOURCE_DIR}

#EOT

exit 0
