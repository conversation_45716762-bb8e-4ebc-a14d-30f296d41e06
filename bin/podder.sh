#!/bin/bash

. $HOME/etc/profile.sh $HOME
DOC_DIR=${1%/}
PROJECT_TITLE=""

shift
USAGE="Usage: $0 doc_dir package1 [packageN]"
which pod2pdf > /dev/null 2>&1 || { echo -e "$USAGE\n\n Errore: pod2pdf non e' installato">&2; exit 4; }
if [[ ! -d $DOC_DIR ]]; then
	echo -e "$USAGE\n\n Errore: Il primo parametro - $DOC_DIR - deve essere una cartella di destinazione valida\n"
	exit 1;
fi
if [[ ! -w $DOC_DIR ]]; then
	echo -e "$USAGE\n\n Errore: La cartella di destinazione $DOC_DIR non e' scrivibile\n"
	exit 2;
fi
if [[ $# -gt 0 ]]; then
	PACKAGES="$@"
else
	echo -e "$USAGE\n\nE' obbligatorio passare la cartella in cui scrivere e almeno un Package da processare\n"
	exit 3;
fi
#PACKAGES="$( echo $PACKAGES | sed 's/\s/\n/g' | sed 's/^\.\///' | sed 's/\.pm$//' | sed 's/\//::/g' | sed 's/^/API::/' )"
PACKAGES="$( echo $PACKAGES | sed 's/\s/\n/g' | sed 's/^\.\///' | sed 's/\.pm$//' | sed 's/\//::/g' | sed 's/::home::.*::share::perl5:://' | sed 's/^//' )"

if [ "$DOC_DIR" -ef "$COM/share/doc" ]; then
	PROJECT_TITLE="COMMON Application Programming Interface - "
fi
if [ "$DOC_DIR" -ef "$T2INT/share/doc" ]; then
	PROJECT_TITLE="Integration Library - "
fi

PODDERTMP=$(mktemp)
PREPODDERTMP=$(mktemp)
echo -e "\nCreazione nuova documentazione, attendere...\n"
for pkg in $PACKAGES
do 
	perl -M$pkg -e '' 2>/dev/null
	rc="$?"
	if [ "$rc" == "0" ]; then
		f=$(echo $pkg | sed s/:/_/g)
		perldoc -u $pkg 2> $PREPODDERTMP >$PODDERTMP
			grep -i 'No documentation found' $PREPODDERTMP >/dev/null 2>&1
			if [ "$?" == "0" ]; then
				echo "Nessuna documentazione trovata nel package $pkg!"
				rm -f $DOC_DIR/${f}.pdf
			else
				enc="$( getencoding.pl $PODDERTMP 2>/dev/null )";
				if [[ "$?" == "0" && "$enc" != "ISO-8859-1" && "$enc" != "ASCII" ]]; then
					iconv -f $enc -t ISO-8859-1 $PODDERTMP | pod2pdf --footer-text "Copyright `date +%G` SIRTI S.p.A." --title "$PROJECT_TITLE $pkg" > $DOC_DIR/${f}.pdf
				else
					pod2pdf --footer-text "Copyright `date +%G` SIRTI S.p.A." --title "$PROJECT_TITLE $pkg" $PODDERTMP > $DOC_DIR/${f}.pdf
				fi
				echo "La documentazione PDF del package $pkg e' stata salvata in $DOC_DIR/${f}.pdf"
			fi
	else
		echo "ERRORE $rc testando la presenza del package $pkg"
	fi
done
echo -e "\nFatto.\n"
rm -r $PREPODDERTMP $PODDERTMP
exit $rc
