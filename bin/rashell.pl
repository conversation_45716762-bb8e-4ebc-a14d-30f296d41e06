#!/usr/bin/env perl

package Rashell;

use strict;

use Data::Dumper;
use Getopt::Long;
Getopt::Long::Configure (qw(bundling no_ignore_case no_auto_abbrev));
use File::Basename;

use base qw(SIRTI::Shell);

use SIRTI::DB::Oracle;
use SIRTI::ART::RemoteActivity::Source;
use SIRTI::ART::RemoteActivity::Target;


my $config = {
	commands => {
    
        open_session => {
			help => "Crea una nuova sessione source o target utilizzando un metodo come argomento" . "

Esempio, utilizzando l'unico metodo disponibile ramqconf:
# 
open_session [using] ramqconf:/usr/share/ramqs/myramqSource.conf

apre una sessione utilizzando modalita' e coda messagggi, indicati in un file di configurazione 
di tipo ramq 
",
			params => {  },
				 
            preparse => 0,            
            
			callback => sub {
				my ( $self, $cmd, $params, $line ) = @_;
			
                if( lc($cmd) ne 'open_session' ) {
                    return $self->manage_error_msg( "ERROR: nome commando '$cmd' non valido" );
                }
                $line =~ /using\s+/gc;
                if( $line !~ /ramqconf:(.+)$/igc ) {
                    return $self->manage_error_msg( "ERROR: metodo di open non valido o mancante" );
                }
                
                my $errMsg;
                my $rconf = $self->_read_ramq_config( $1, \$errMsg );
                
                if( defined $errMsg || !defined $rconf ) {
                    return $self->manage_error_msg( "ERROR: errore leggendo il file di configurazione" . 
                                                     ( defined $errMsg ? $errMsg : "" ) );
                }
             
                my $stype = exists $rconf->{SESSION_TYPE} ? uc($rconf->{SESSION_TYPE}) : "";
                
                if( $stype eq 'TARGET' ) {
                    return $self->_prepare_and_call_open_target_cmd( $cmd, $line, $rconf );
   
                }
                elsif( $stype eq 'SOURCE' ) {
                    return $self->_prepare_and_call_open_source_cmd( $cmd, $line, $rconf );
                }
                
                return $self->manage_error_msg( 
                    "ERROR: ramqconf: parametro SESSION_TYPE mancante o non valido"
                   );
			},
		},
    
		open_source => {
			help => "Crea una nuova sessione source.

Esempi:
# Modalita' Destinatario unico
open_source { CONNECT_STRING : 'user/pwd\@host', SESSION_DESCRIPTION : 'Big-bang test session!!!!', SOURCE_SERVICE : 'source_service', SOURCE_CONTEXT : 'source_context', TARGET_SERVICE : 'target_service', TARGET_CONTEXT : 'target_context' }

# Modalita' Destinatario Multiplo
open_source { CONNECT_STRING : 'user/pwd\@host', SESSION_DESCRIPTION : 'Big-bang test session!!!!', SOURCE_SERVICE : 'source_service', SOURCE_CONTEXT : 'source_context', TARGET : { 'target_service' : [ 'target_context', 'target_context' ], 'target_service' : 'target_context', 'target_service' : 'target_context' } }

# Modalita' Destinatario locale
open_source { CONNECT_STRING : 'user/pwd\@host', SESSION_DESCRIPTION : 'Big-bang test session!!!!', SOURCE_CONTEXT : 'source_context', TARGET_CONTEXT : 'target_context' }
",
			params => {
				MANDATORY => {
					CONNECT_STRING => { help => "Stringa di connessione allo schema remote_activity" },
					SESSION_DESCRIPTION => { help => "Descrizione della sessione corrente" },
					SOURCE_CONTEXT => { help => "IDentificativo del contesto sorgente" },
				},
				OPTIONAL => {
					SOURCE_SERVICE => { help => "IDentificativo del sistema sorgente. Da valorizzare in caso di destinatario unico o destinatario multiplo, da non valorizzare in caso di destinatario locale" },
					TARGET_SERVICE => { help => "IDentificativo del sistema destinatario. Da valorizzare in caso di destinatario unico, da non valorizzare in caso di destinatrio multiplo (vedi parametro TARGET) o di destinatario locale" },
					TARGET_CONTEXT => { help => "IDentificativo del contesto destinatario. Da valorizzare in caso di destinatario unico o destinatario locale, da non valorizzare in caso di destinatrio multiplo (vedi parametro TARGET)" },
					TARGET => { help => "Array associativo che ha come chiavi i servizi target e come valori stringhe (o array) che identificano i contesti target. Da valorizzare in caso di destinatario multiplo, da non valorizzare in caso di destinatario singolo o destinatario locale" },
					USER_ID => { help => "IDentificativo dell'operatore che ha richiesto l'inserimento dell'evento" },
					USER_NAME => { help => "LOGIN_OPERATORE della tabella OPERATORI di ART" },
					DATE_FORMAT => { help => "Formato da utilizzare per la manipolazione delle date (default: \"YYYYMMDDHH24MISS\")" },
				},
			},
			callback => sub {
				my ( $self, $cmd, $params, $line ) = @_;
				$self->merge_params( $cmd, $params );
				return $self->manage_error_msg(
					 "ERROR: hai gia' aperto la sessione, effetture prima la close"
				) if defined $self->{RASHELL_DB_SOURCE};
				$self->{RASHELL_DB_SOURCE} = eval { SIRTI::DB::Oracle->new( $params->{CONNECT_STRING}, DBMS_OUTPUT => $self->{RASHELL_DBMS_OUTPUT} ) };
				return $self->manage_error_msg(
					 "ERROR: Impossibile connettersi al db"
					,"ERROR: Impossibile connettersi al db: $@"
				) unless ( defined $self->{RASHELL_DB_SOURCE} );
				$self->{RASHELL_RA_SOURCE} = eval { SIRTI::ART::RemoteActivity::Source->new(
					 DB                     => $self->{RASHELL_DB_SOURCE}
					,SESSION_DESCRIPTION    => $params->{SESSION_DESCRIPTION}
					,SOURCE_SERVICE         => $params->{SOURCE_SERVICE}
					,SOURCE_CONTEXT         => $params->{SOURCE_CONTEXT}
					,TARGET_SERVICE         => $params->{TARGET_SERVICE}
					,TARGET_CONTEXT         => $params->{TARGET_CONTEXT}
					,TARGET                 => $params->{TARGET}
					,USER_ID                => $params->{USER_ID}
					,USER_NAME              => $params->{USER_NAME}
					,DATE_FORMAT            => $params->{DATE_FORMAT}
				); };
				unless ( defined $self->{RASHELL_RA_SOURCE} ) {
					$self->{RASHELL_DB_SOURCE}->disconnect();
					undef $self->{RASHELL_DB_SOURCE};
					return $self->manage_error_msg(
						 "ERROR: Impossibile instanziare l'oggetto remote activity source"
						,"ERROR: Impossibile instanziare l'oggetto remote activity source: $@"
					)
				}
				return $self->manage_success_msg( "Ho istanziato l'oggetto remote activity source" );
			},
		},
		open_target => {
			help => "Crea una nuova sessione target.
  es.  open_TARGET { CONNECT_STRING : 'user/pwd\@host', SESSION_DESCRIPTION : 'Big-bang test session!!!!', SOURCE_SERVICE : 'source_service', SOURCE_CONTEXT : 'source_context', TARGET_SERVICE : 'target_service', TARGET_CONTEXT : 'target_context' }
",
			params => {
				MANDATORY => {
					CONNECT_STRING => { help => "Stringa di connessione allo schema remote_activity" },
					SESSION_DESCRIPTION => { help => "Descrizione della sessione corrente" },
					SOURCE_SERVICE => { help => "IDentificativo del sistema sorgente" },
					SOURCE_CONTEXT => { help => "IDentificativo del contesto sorgente" },
					TARGET_SERVICE => { help => "IDentificativo del sistema destinatario" },
					TARGET_CONTEXT => { help => "IDentificativo del contesto destinatario" },
				},
				OPTIONAL => {
					RA_DBLINK => { help => "Indica il nome del DBLINK da utilizzare per accedere agli eventi/messaggi; se omesso o valorizzato a undef verra' utilizzato il valore indicato in SOURCE_SERVICE; se impostato a stringa vuota ('') gli eventi/messaggi verranno \"letti\" dallo stesso DB" },
					USER_ID => { help => "IDentificativo dell'operatore che ha richiesto l'inserimento dell'evento" },
					USER_NAME => { help => "LOGIN_OPERATORE della tabella OPERATORI di ART" },
					DATE_FORMAT => { help => "Formato da utilizzare per la manipolazione delle date (default: \"YYYYMMDDHH24MISS\")" },
				},
			},
			callback => sub {
				my ( $self, $cmd, $params, $line ) = @_;
				$self->merge_params( $cmd, $params );
				return $self->manage_error_msg(
					 "ERROR: hai gia' aperto la sessione, effetture prima la close"
				) if defined $self->{RASHELL_DB_TARGET};
				$self->{RASHELL_DB_TARGET} = eval { SIRTI::DB::Oracle->new( $params->{CONNECT_STRING}, DBMS_OUTPUT => $self->{RASHELL_DBMS_OUTPUT} ) };
				return $self->manage_error_msg(
					 "ERROR: Impossibile connettersi al db"
					,"ERROR: Impossibile connettersi al db: $@"
				) unless ( defined $self->{RASHELL_DB_TARGET} );
				$self->{RASHELL_RA_TARGET} = eval { SIRTI::ART::RemoteActivity::Target->new(
					 DB                     => $self->{RASHELL_DB_TARGET}
					,SESSION_DESCRIPTION    => $params->{SESSION_DESCRIPTION}
					,SOURCE_SERVICE         => $params->{SOURCE_SERVICE}
					,SOURCE_CONTEXT         => $params->{SOURCE_CONTEXT}
					,TARGET_SERVICE         => $params->{TARGET_SERVICE}
					,TARGET_CONTEXT         => $params->{TARGET_CONTEXT}
					,RA_DBLINK              => $params->{RA_DBLINK}
					,USER_ID                => $params->{USER_ID}
					,USER_NAME              => $params->{USER_NAME}
					,DATE_FORMAT            => $params->{DATE_FORMAT}
				); };
				unless ( defined $self->{RASHELL_RA_TARGET} ) {
					$self->{RASHELL_DB_TARGET}->disconnect();
					undef $self->{RASHELL_DB_TARGET};
					return $self->manage_error_msg(
						 "ERROR: Impossibile instanziare l'oggetto remote activity target"
						,"ERROR: Impossibile instanziare l'oggetto remote activity target: $@"
					)
				}
				return $self->manage_success_msg( "Ho istanziato l'oggetto remote activity target" );
			},
		},
		insert_event => {
			help => 'Inserisce un nuovo evento.
  es. insert_event { EVENT : "RASHELL_EVENT_1", SOURCE_REF : "rashell_1", DATA : { NOME : "ivan" }, NEED_ACK : 1 }
',
			params => {
				MANDATORY => {
					SOURCE_REF => { help => "Riferimento (univoco o meno) utile al destinatario del messaggio per identificare l'oggetto del messaggio" },
					EVENT => { help => "Un nome (o tipo) di evento riconoscibile dal destinatario del messaggio" },
				},
				OPTIONAL => {
					DATA => { help => "Un elenco di coppie chiave/valore associati al messaggio" },
					SCHEDULE_DATE => { help => "Rappresenta la data (nel formato DATE_FORMAT specificato nel metodo new_source) oltre la quale il messaggio potra' essere \"consumato\" dal destinatario" },
					EXPIRY_DATE => { help => "Rappresenta la data (nel formato DATE_FORMAT specificato nel costruttore di classe) entro la quale il messaggio potra' essere \"consumato\" dal destinatario" },
					NEED_ACK => { help => "Fa in modo che, nel momento in cui viene \"consumato\" il messaggio, venga generato un acknowledge verso il mittente del messaggio stesso; l'unico valore ammesso e' \"1\"" },
				},
			},
			callback => sub {
				my ( $self, $cmd, $params, $line ) = @_;
				$self->merge_params( $cmd, $params );
				return $self->manage_error_msg(
					 "ERROR: Non hai aperto la sessione"
				) unless defined $self->{RASHELL_RA_SOURCE};
				$params->{DATA} = {}
					unless defined $params->{DATA};
				my $ret;
				eval {
					$ret = $self->{RASHELL_RA_SOURCE}->insert(
						 EVENT         => $params->{EVENT}
						,SOURCE_REF    => $params->{SOURCE_REF}
						,DATA          => $params->{DATA}
						,SCHEDULE_DATE => $params->{SCHEDULE_DATE}
						,EXPIRY_DATE   => $params->{EXPIRY_DATE}
						,NEED_ACK      => $params->{NEED_ACK}
					);
				};
				return $self->manage_error_msg(
					 "ERROR: Impossibile inserire la remote activity"
					,"ERROR: Impossibile inserire la remote activity: $@"
				) if ( $@ );
				return $self->manage_error_msg(
					 "ERROR: Impossibile inserire la remote activity"
				) unless $ret;
				return $self->manage_success_msg( "Remote activity inserita con ID $ret" );
			},
		},
		find_pending_events_target => {
			help => "Restituisce l'elenco di ID degli eventi 'pending'.
  es. find_pending_events_target
      find_pending_events_target { EVENT : 'PIPPO' }
      find_pending_events_target { SOURCE_REF : [ 'PIPPO', 'PLUTO' ] }
",
			params => {
				OPTIONAL => {
					EVENT => { help => "Un tipo o una lista di tipi di eventi da ricercare" },
					SOURCE_REF => { help => "Un riferimento o lista di riferimenti dei messaggi da ricercare" },
				},
			},
			callback => sub {
				my $self = shift;
				return $self->_find_pending( 'TARGET', @_ );
			},
		},
		find_pending_events_source => {
			help => "Restituisce l'elenco di ID degli eventi 'pending'.
  es. find_pending_events_source
      find_pending_events_source { EVENT : 'PIPPO' }
      find_pending_events_source { SOURCE_REF : [ 'PIPPO', 'PLUTO' ] }
",
			params => {
				OPTIONAL => {
					SOURCE_REF => { help => "Un riferimento o lista di riferimenti dei messaggi da ricercare" },
					EVENT => { help => "Un tipo o una lista di tipi di eventi da ricercare" },
				},
			},
			callback => sub {
				my $self = shift;
				return $self->_find_pending( 'SOURCE', @_ );
			},
		},
		get_event_target => {
			help => "Visualizza le informazioni relative ad un evento.
  es. get_event_target { ID : 1234 }
",
			params => {
				MANDATORY => {
					ID => { help => "IDentificativo dell'evento/messaggio" },
				},
			},
			callback => sub {
				my $self = shift;
				return $self->_get_event( 'TARGET', @_ );
			},
		},
		get_event_source => {
			help => "Visualizza le informazioni relative ad un evento.
  es. get_event_source { ID : 1234 }
",
			params => {
				MANDATORY => {
					ID => { help => "IDentificativo dell'evento/messaggio" },
				},
			},
			callback => sub {
				my $self = shift;
				return $self->_get_event( 'SOURCE', @_ );
			},
		},
		fetch_event => {
			help => "Marca come \"letto\" (\"consumato\") un evento/messaggio.
  es. fetch_event { ID : 1234, TARGET_REF : 'PIPPO', STATUS : 0, REASON : 'Lavorato ok' }
  es. fetch_event { ID : 1234, TARGET_REF : 'PIPPO', STATUS : 0, REASON : 'Lavorato ok', DATA : { NOME : 'Paperino', COGNOME : 'Paolino' }
",
			params => {
				MANDATORY => {
					ID => { help => "IDentificativo dell'evento/messaggio" },
					TARGET_REF => { help => "Associa all'evento/messaggio un valore \"chiave\" nel contesto target" },
					STATUS => { help => "Indica l'esito della lavorazione; per convenzione, 0 indica che l'event/messaggio e' stato correttamente processato" },
					REASON => { help => "Consente di specificare un commento sulla lavorazione" },
				},
				OPTIONAL => {
					DATA => { help => "Elenco di dati in formato chiave/valore comunicati dal target al mittente" },
				},
			},
			callback => sub {
				my ( $self, $cmd, $params, $line ) = @_;
				$self->merge_params( $cmd, $params );
				return $self->manage_error_msg(
					 "ERROR: Non hai aperto la sessione"
				) unless defined $self->{RASHELL_RA_TARGET};
				my $ret;
				eval {
					$ret = $self->{RASHELL_RA_TARGET}->fetch(
						 ID         => $params->{ID}
						,TARGET_REF => $params->{TARGET_REF}
						,STATUS     => $params->{STATUS}
						,REASON     => $params->{REASON}
						,DATA       => $params->{DATA}
					);
				};
				return $self->manage_error_msg(
					 "ERROR: Impossibile marcare l'evento come lavorato"
					,"ERROR: Impossibile marcare l'evento come lavorato: $@"
				) if ( $@ );
				return $self->manage_error_msg(
					 "ERROR: Impossibile marcare l'evento come lavorato"
				) unless $ret;
				return $self->manage_success_msg( "L'evento e' stato marcato come lavorato" );
			},
		},
		lookup_event => {
			help => "Consente di tracciare l'impossibilita' contingente nel \"leggere\" (\"consumare\") un evento/messaggio.
  es. lookup_event { ID : 1234, REASON : 'Lavorato ok' }
",
			params => {
				MANDATORY => {
					ID => { help => "IDentificativo dell'evento/messaggio" },
					REASON => { help => "Consente di specificare la motivazione della mancata lavorazione" },
				},
			},
			callback => sub {
				my ( $self, $cmd, $params, $line ) = @_;
				$self->merge_params( $cmd, $params );
				return $self->manage_error_msg(
					 "ERROR: Non hai aperto la sessione"
				) unless defined $self->{RASHELL_RA_TARGET};
				my $ret;
				eval {
					$ret = $self->{RASHELL_RA_TARGET}->lookup(
						 ID         => $params->{ID}
						,REASON     => $params->{REASON}
					);
				};
				return $self->manage_error_msg(
					 "ERROR: Impossibile effettuare il lookup dell'evento"
					,"ERROR: Impossibile effettuare il lookup dell'evento: $@"
				) if ( $@ );
				return $self->manage_error_msg(
					 "ERROR: Impossibile effettuare il lookup dell'evento"
				) unless $ret;
				return $self->manage_success_msg( "Il lookup dell'evento e' stato effettuato" );
			},
		},
		cancel_event => {
			help => "Annulla un evento \"pending\".
  es. cancel_event { ID : 1234, REASON : 'Lavorato ok' }
",
			params => {
				MANDATORY => {
					ID => { help => "IDentificativo dell'evento/messaggio" },
					REASON => { help => "Consente di specificare il motivo dell'annullamento del messaggio" },
				},
			},
			callback => sub {
				my ( $self, $cmd, $params, $line ) = @_;
				$self->merge_params( $cmd, $params );
				return $self->manage_error_msg(
					 "ERROR: Non hai aperto la sessione"
				) unless defined $self->{RASHELL_RA_SOURCE};
				my $ret;
				eval {
					$ret = $self->{RASHELL_RA_SOURCE}->cancel(
						 ID         => $params->{ID}
						,REASON     => $params->{REASON}
					);
				};
				return $self->manage_error_msg(
					 "ERROR: Impossibile cancellare l'evento"
					,"ERROR: Impossibile cancellare l'evento: $@"
				) if ( $@ );
				return $self->manage_error_msg(
					 "ERROR: Impossibile cancellare l'evento"
				) unless $ret;
				return $self->manage_success_msg( "L'evento e' stato cancellato" );
			},
		},
		find_pending_acks => {
			help => "restituisce l'elenco degli ID degli eventi per cui l'acknowledge non e' ancora stato gestito.
  es. find_pending_acks
      find_pending_acks { EVENT : 'PIPPO' }
      find_pending_acks { SOURCE_REF : [ 'PIPPO', 'PLUTO' ] }
",
			params => {
				OPTIONAL => {
					SOURCE_REF => { help => "Un riferimento o lista di riferimenti dei messaggi da ricercare" },
					EVENT => { help => "Un tipo o una lista di tipi di eventi da ricercare" },
				},
			},
			callback => sub {
				my $self = shift;
				return $self->_find_pending_acks( 'find_pending_ack', @_ );
			},
		},
		find_pending_target_acks => {
			help => "restituisce l'elenco degli ID degli eventi per cui l'acknowledge dei singoli target non e' ancora stato gestito.
  es. find_pending_target_acks
      find_pending_target_acks { EVENT : 'PIPPO' }
      find_pending_target_acks { SOURCE_REF : [ 'PIPPO', 'PLUTO' ] }
",
			params => {
				OPTIONAL => {
					SOURCE_REF => { help => "Un riferimento o lista di riferimenti dei messaggi da ricercare" },
					EVENT => { help => "Un tipo o una lista di tipi di eventi da ricercare" },
				},
			},
			callback => sub {
				my $self = shift;
				return $self->_find_pending_acks( 'find_pending_target_ack', @_ );
			},
		},
		fetch_ack => {
			help => "Marca come \"letto\" l'acknowledge di un evento \"consumato\".
  es. fetch_ack { ID : 1234 }
",
			params => {
				MANDATORY => {
					ID => { help => "IDentificativo dell'evento/messaggio" },
				},
				OPTIONAL => {
					NOTE => { help => "Consente di specificare un commento della lavorazione" },
				},
			},
			callback => sub {
				my ( $self, $cmd, $params, $line ) = @_;
				$self->merge_params( $cmd, $params );
				return $self->manage_error_msg(
					 "ERROR: Non hai aperto la sessione"
				) unless defined $self->{RASHELL_RA_SOURCE};
				my $ret;
				eval {
					$ret = $self->{RASHELL_RA_SOURCE}->fetch_ack(
						 ID         => $params->{ID}
						,NOTE => $params->{NOTE}
					);
				};
				return $self->manage_error_msg(
					 "ERROR: Impossibile marcare l'ack come lavorato"
					,"ERROR: Impossibile marcare l'ack come lavorato: $@"
				) if ( $@ );
				return $self->manage_error_msg(
					 "ERROR: Impossibile marcare l'ack come lavorato"
				) unless $ret;
				return $self->manage_success_msg( "L'ack e' stato marcato come lavorato" );
			},
		},
		commit_source => {
			help => "Registra le modifiche sul db.
  es. commit_source
",
			callback => sub {
				my $self = shift;
				return $self->_commit( 'SOURCE', @_ );
			},
		},
		commit_target => {
			help => "Registra le modifiche sul db.
  es. commit_target
",
			callback => sub {
				my $self = shift;
				return $self->_commit( 'TARGET', @_ );
			},
		},
		rollback_source => {
			help => "Annulla le modifiche sul db.
  es. rollback_source
",
			callback => sub {
				my $self = shift;
				return $self->_rollback( 'SOURCE', @_ );
			},
		},
		rollback_target => {
			help => "Annulla le modifiche sul db.
  es. rollback_target
",
			callback => sub {
				my $self = shift;
				return $self->_rollback( 'TARGET', @_ );
			},
		},
		close_source => {
			help => "Chiude la sessione source.
  es. close_source
",
			callback => sub {
				my $self = shift;
				return $self->_close( 'SOURCE', @_ );
			},
		},
		close_target => {
			help => "Chiude la sessione target.
  es. close_target
",
			callback => sub {
				my $self = shift;
				return $self->_close( 'TARGET', @_ );
			},
		},
	},
	prompt => 'rashell> ',
};

sub _commit {
		my ( $self, $type, $cmd, $params, $line ) = @_;
		$self->merge_params( $cmd, $params );
		return $self->manage_error_msg(
			 "ERROR: non sei connesso al db"
		) unless defined $self->{"RASHELL_DB_$type"};
		eval { $self->{"RASHELL_DB_$type"}->commit };
		return $self->manage_error_msg(
			 "ERROR: Impossibile registrare le modifiche sul db"
			,"ERROR: Impossibile registrare le modifiche sul db: $@"
		) if $@;
		return $self->manage_success_msg( "Commit effettuata" );
}

sub _rollback {
		my ( $self, $type, $cmd, $params, $line ) = @_;
		$self->merge_params( $cmd, $params );
		return $self->manage_error_msg(
			 "ERROR: non sei connesso al db"
		) unless defined $self->{"RASHELL_DB_$type"};
		eval { $self->{"RASHELL_DB_$type"}->rollback };
		return $self->manage_error_msg(
			 "ERROR: Impossibile effettuare il rollback delle modifiche sul db"
			,"ERROR: Impossibile effettuare il rollback delle modifiche sul db: $@"
		) if $@;
		return $self->manage_success_msg( "Rollback effettuato" );
}

sub _close {
	my ( $self, $type, $cmd, $params, $line ) = @_;
	$self->merge_params( $cmd, $params );
	return $self->manage_error_msg(
		 "ERROR: Non hai ancora aperto la sessione"
	) unless defined $self->{"RASHELL_RA_$type"};
	undef $self->{"RASHELL_RA_$type"};
	$self->{"RASHELL_DB_$type"}->disconnect();
	undef $self->{"RASHELL_DB_$type"};
	return $self->manage_success_msg( "La sessione e' stata chiusa" );
}

sub _find_pending {
	my ( $self, $type, $cmd, $params, $line ) = @_;
	$self->merge_params( $cmd, $params );
	return $self->manage_error_msg(
		 "ERROR: Non hai aperto la sessione"
	) unless defined $self->{"RASHELL_RA_$type"};
	my @pending;
	eval {
		@pending = $self->{"RASHELL_RA_$type"}->find_pending(
			 EVENT         => $params->{EVENT}
			,SOURCE_REF    => $params->{SOURCE_REF}
		);
	};
	return $self->manage_error_msg(
		 "ERROR: Impossibile recuperare gli eventi pending"
		,"ERROR: Impossibile recuperare gli eventi pending: $@"
	) if ( $@ );
	return $self->manage_success_msg( "Nessun evento pending trovato" )
		unless scalar @pending;
	my @headers = ( "ID", "SOURCE_SERVICE", "SOURCE_CONTEXT", "EVENT", "SOURCE_REF" );
	push @headers, ( "TARGET_ID", "TARGET_SERVICE", "TARGET_CONTEXT" )
		if ( $type eq 'SOURCE' );
	my @values = ();
	for ( @headers ) { push @values, "-"x(length); }
	for my $id ( @pending ) {
		my $e = $self->{"RASHELL_RA_$type"}->get_event( ID => $id );
		my $target_id = '';
		my $target_service = '';
		my $target_context = '';
		if ( $type eq 'SOURCE' ) {
			for my $et ( $e->get_targets() ) {
				$target_id .= $et->get_id() . "\n";
				$target_service .= $et->get_session()->{TARGET_SERVICE} . "\n";
				$target_context .= $et->get_session()->{TARGET_CONTEXT} . "\n";
			}
		}
		chomp $target_id;
		chomp $target_service;
		chomp $target_context;
		my @fields = ();
		push @fields, (
			 $id
			,$self->{"RASHELL_RA_$type"}->get_source_service()
			,$self->{"RASHELL_RA_$type"}->get_source_context()
			,$e->get_event_name()
			,$e->get_source_ref()
		);
		push @fields, (
			 $target_id
			,$target_service
			,$target_context
		) if ( $type eq 'SOURCE' );
		push @values, @fields;
	}
	$self->print( "\n" );
	$self->print_array_as_table( \@headers, \@values );
	return $self->manage_success_msg( "Trovati " . scalar( @pending ) . " eventi pending" );
}

sub _get_event {
	my ( $self, $type, $cmd, $params, $line ) = @_;
	$self->merge_params( $cmd, $params );
	return $self->manage_error_msg(
		 "ERROR: Non hai aperto la sessione"
	) unless defined $self->{"RASHELL_RA_$type"};
	my $event;
	eval {
		$event = $self->{"RASHELL_RA_$type"}->get_event(
			 ID         => $params->{ID}
		);
	};
	return $self->manage_error_msg(
		 "ERROR: Impossibile recuperare l'evento"
		,"ERROR: Impossibile recuperare l'evento: $@"
	) if ( $@ );
	return $self->manage_error_msg(
		 "ERROR: Impossibile recuperare l'evento"
	) unless ( defined $event );
	$self->print( "INFO:\n" );
	$self->print( sprintf( "  %-25s: %s\n", 'ID', $event->get_info()->{ID} ) );
	$self->print( sprintf( "  %-25s: %s\n", 'EVENT', $event->get_info()->{EVENT} ) );
	$self->print( sprintf( "  %-25s: %s\n", 'SOURCE_REF', $event->get_info()->{SOURCE_REF} ) );
	$self->print( sprintf( "  %-25s: %s\n", 'INSERT_DATE', $event->get_info()->{INSERT_DATE} ) );
	$self->print( sprintf( "  %-25s: %s\n", 'SCHEDULE_DATE', $event->get_info()->{SCHEDULE_DATE} ) );
	$self->print( sprintf( "  %-25s: %s\n", 'EXPIRY_DATE', $event->get_info()->{EXPIRY_DATE} ) );
	$self->print( sprintf( "  %-25s: %s\n", 'CANCEL_DATE', $event->get_info()->{CANCEL_DATE} ) );
	# $self->print( sprintf( "  %-25s: %s\n", 'ALIVE', $event->get_info()->{ALIVE} ) );
	$self->print( sprintf( "  %-25s: %s\n", 'NEED_ACK', $event->get_info()->{NEED_ACK} ) );
	$self->print( sprintf( "  %-25s: %s\n", 'ALIVE', $event->is_alive() ? 1 : 0 ) );
	$self->print( sprintf( "  %-25s: %s\n", 'PENDING', $event->is_pending() ? 1 : 0 ) );
	if ( !$event->is_pending() && defined $event->get_parent_id() ) {
		$self->print( sprintf( "  %-25s: %s\n", 'FETCH_DATE', $event->get_info()->{FETCH_DATE} ) );
		$self->print( sprintf( "  %-25s: %s\n", 'TARGET_REF', $event->get_info()->{TARGET_REF} ) );
		$self->print( sprintf( "  %-25s: %s\n", 'STATUS', $event->get_info()->{STATUS} ) );
		$self->print( sprintf( "  %-25s: %s\n", 'REASON', $event->get_info()->{REASON} ) );
	}
	if ( scalar keys %{$event->get_data()} ) {
		$self->print( "DATA:\n" );
		while ( my ( $k, $v ) = each %{$event->get_data()} ) {
			$self->print( sprintf( "  %-25s: %s\n", $k, $v ) );
		}
	}
	if ( $event->need_ack() && !$event->is_pending() ) {
		if ( !$event->is_pending() && defined $event->get_parent_id() ) {
			# e' un evento pending figlio
			if ( scalar keys %{$event->get_data_ack()} ) {
				$self->print( "ACK DATA:\n" );
				while ( my ( $k, $v ) = each %{$event->get_data_ack()} ) {
					$self->print( sprintf( "  %-25s: %s\n", $k, $v ) );
				}
			}
		}
		$self->print( "FETCH ACK INFO:\n" );
		if ( defined $event->get_ack() ) {
			# l'ack e' stato fetchato
			$self->print( sprintf( "  %-25s: %s\n", 'FETCHED', 1 ) );
			$self->print( sprintf( "  %-25s: %s\n", 'SESSION_ID', $event->get_ack()->{SESSION_ID} ) );
			$self->print( sprintf( "  %-25s: %s\n", 'FETCH_ACK_DATE', defined $event->get_ack()->{FETCH_ACK_DATE} ? $event->get_ack()->{FETCH_ACK_DATE} : $event->get_ack()->{ACK_DATE} ) );
			$self->print( sprintf( "  %-25s: %s\n", 'NOTE', $event->get_ack()->{NOTE} ) );
		} else {
			$self->print( sprintf( "  %-25s: %s\n", 'FETCHED', 0 ) );
		}
	}
	return $self->manage_success_msg( "Evento recuperato con successo" );
}

sub _find_pending_acks {
	my ( $self, $function, $cmd, $params, $line ) = @_;
	$self->merge_params( $cmd, $params );
	return $self->manage_error_msg(
		 "ERROR: Non hai aperto la sessione"
	) unless defined $self->{RASHELL_RA_SOURCE};
	my @pending;
	eval {
		@pending = $self->{RASHELL_RA_SOURCE}->$function(
			 EVENT         => $params->{EVENT}
			,SOURCE_REF    => $params->{SOURCE_REF}
		);
	};
	return $self->manage_error_msg(
		 "ERROR: Impossibile recuperare gli ack pending"
		,"ERROR: Impossibile recuperare gli ack pending: $@"
	) if ( $@ );
	return $self->manage_success_msg( "Nessun ack pending trovato" )
		unless scalar @pending;
	# FIXME con RIZ
	my @headers = ( "ID" );
	my @values = ();
	for ( @headers ) { push @values, "-"x(length); }
	for my $id ( @pending ) {
		my @fields = ();
		push @fields, (
			 $id
		); 
		push @values, @fields;
	}
	$self->print( "\n" );
	$self->print_array_as_table( \@headers, \@values );
	return $self->manage_success_msg( "Trovati " . scalar( @pending ) . " ack pending" );
}

sub _prepare_and_call_open_target_cmd {
    my ( $self, $cmd, $line, $rconf ) = @_;
    my $errMsg;
    
    if( exists $rconf->{SESSION_DESCRIPTION} && ( ref( \$rconf->{SESSION_DESCRIPTION} ) eq 'SCALAR' ) ) { 
    
        $rconf->{SESSION_DESCRIPTION} = "RaShell [PID: $$]" .
              ( defined $rconf->{SESSION_DESCRIPTION} ? $rconf->{SESSION_DESCRIPTION} : "" );              
    }
    else {
        $rconf->{SESSION_DESCRIPTION} = "RaShell [PID: $$]";
    }

    if( exists $rconf->{DB}->{CONNECT_STRING} ) {
        $rconf->{CONNECT_STRING} = $rconf->{DB}->{CONNECT_STRING};    
    }
     
    #FIXME: DATE_FORMAT
   
    ### merge with ENV : phase 1/2
    for( qw{
          SOURCE_CONTEXT 
          CONNECT_STRING 
          SESSION_DESCRIPTION 
          TARGET_CONTEXT 
          SOURCE_SERVICE 
          TARGET_SERVICE 
          DATE_FORMAT 
          USER_ID 
          USER_NAME 
          RA_DBLINK 
         }
        )
    {
        if( exists $rconf->{$_} ) {
            __merge_env_param( \$rconf->{$_}, \$errMsg );
            if( defined $errMsg ) {
                return $self->manage_error_msg( "ERROR: valorizzando parametro $_ : $errMsg");
            }
        }
    }
    
   
    if( !exists $rconf->{CONNECT_STRING} && exists $rconf->{DB}->{ART} && ( ref( $rconf->{DB}->{ART} ) eq 'HASH' ) ) {
   
        ### merge with ENV : phase 2/2
        for( keys %{$rconf->{DB}->{ART}} ) {
                 __merge_env_param( \$rconf->{DB}->{ART}->{$_}, \$errMsg );
                 if( defined $errMsg ) {
                     return $self->manage_error_msg( "ERROR: valorizzando parametro DB->ART $_ : $errMsg");
                 }
        }
        
        eval { use API::ART; };
        if( $@ ) {
            return $self->manage_error_msg( "ERROR: impossibile utilizzare il package API::ART  : $@" );            
        }
   
        my $artinf = $rconf->{DB}->{ART};   
        if( ref( $artinf ) ne 'HASH' ) {
            return $self->manage_error_msg( "ERROR: parametro ART non valorizzato correttamente");
        }

        my @params = ( );  
        push @params, ( "ARTID",    $artinf->{ID} )       if exists $artinf->{ID};
        push @params, ( "USER",     $artinf->{USER} )     if exists $artinf->{USER};                
        push @params, ( "PASSWORD", $artinf->{PASSWORD} ) if exists $artinf->{PASSWORD};                                 
 
        my $artobj = eval { API::ART->new( @params ); };
        if( $@ || !defined $artobj ) {
            my $exception = $@;
            return $self->manage_error_msg( "ERROR: impossibile instanziare oggetto API::ART " . 
                                             (defined $exception ? (": ".$exception) : "" ) );
        }
        
        $rconf->{CONNECT_STRING} = $artobj->info("CONNECTSTRING");        
        undef $artobj;
    }
   
    return &{$self->config->{commands}->{'open_target'}->{callback}}
                ( 
                   $self, 'open_target', $rconf, $line
                );
}

sub _prepare_and_call_open_source_cmd {
    my ( $self, $cmd, $line, $rconf ) = @_;
    my $errMsg;
    
    if( exists $rconf->{SESSION_DESCRIPTION} && ( ref( \$rconf->{SESSION_DESCRIPTION} ) eq 'SCALAR' ) ) { 
    
        $rconf->{SESSION_DESCRIPTION} = "RaShell [PID: $$]" .
              ( defined $rconf->{SESSION_DESCRIPTION} ? $rconf->{SESSION_DESCRIPTION} : "" );              
    }
    else {
        $rconf->{SESSION_DESCRIPTION} = "RaShell [PID: $$]";
    }

    if( exists $rconf->{DB}->{CONNECT_STRING} ) {
        $rconf->{CONNECT_STRING} = $rconf->{DB}->{CONNECT_STRING};    
    }
    
    #FIXME: DATE_FORMAT
   
    ### merge with ENV : phase 1/3
    for( qw{
           SOURCE_CONTEXT
           CONNECT_STRING
           SESSION_DESCRIPTION
           DATE_FORMAT 
           USER_ID
           USER_NAME 
           TARGET_SERVICE
           TARGET_CONTEXT
           SOURCE_SERVICE 
         }
        )
    {
        if( exists $rconf->{$_} ) {
            __merge_env_param( \$rconf->{$_}, \$errMsg );
            if( defined $errMsg ) {
                return $self->manage_error_msg( "ERROR: valorizzando parametro $_ : $errMsg");
            }
        }
    }
    
    
    if( !exists $rconf->{CONNECT_STRING} && exists $rconf->{DB}->{ART} && ( ref( $rconf->{DB}->{ART} ) eq 'HASH' ) ) {
   
        ### merge with ENV : phase 2/3
        for( keys %{$rconf->{DB}->{ART}} ) {
                 __merge_env_param( \$rconf->{DB}->{ART}->{$_}, \$errMsg );
                 if( defined $errMsg ) {
                     return $self->manage_error_msg( "ERROR: valorizzando parametro DB->ART $_ : $errMsg");
                 }
        }
        
        eval { use API::ART; };
        if( $@ ) {
            return $self->manage_error_msg( "ERROR: impossibile utilizzare il package API::ART  : $@" );            
        }
   
        my $artinf = $rconf->{DB}->{ART};   
        if( ref( $artinf ) ne 'HASH' ) {
            return $self->manage_error_msg( "ERROR: parametro ART non valorizzato correttamente");
        }

        my @params = ( );  
        push @params, ( "ARTID",    $artinf->{ID} )       if exists $artinf->{ID};
        push @params, ( "USER",     $artinf->{USER} )     if exists $artinf->{USER};                
        push @params, ( "PASSWORD", $artinf->{PASSWORD} ) if exists $artinf->{PASSWORD};                                 
 
        my $artobj = eval { API::ART->new( @params ); };
        if( $@ || !defined $artobj ) {
            my $exception = $@;
            return $self->manage_error_msg( "ERROR: impossibile instanziare oggetto API::ART " . 
                                             (defined $exception ? (": ".$exception) : "" ) );
        }
        
        $rconf->{CONNECT_STRING} = $artobj->info("CONNECTSTRING");        
        undef $artobj;
    }
    
    ### merge with ENV : phase 3/3
    if( exists $rconf->{TARGET} && ( ref( $rconf->{TARGET} ) eq 'HASH' ) ) {
        my $THL = $rconf->{TARGET};
        for( keys %$THL ) {
             if( ref( $THL->{$_} ) eq 'ARRAY' ) {
                 for( my $idx = 0; $idx < scalar(@{$THL->{$_}}); $idx++ ) {
                      __merge_env_param( \$THL->{$_}->[$idx], \$errMsg );
                      last if defined $errMsg;
                 }
             }
             else {
                 __merge_env_param( \$THL->{$_}, \$errMsg );
             }
             
             if( defined $errMsg ) {
                 return $self->manage_error_msg( "ERROR: sostituzione valore parametro TARGET non riuscito: $errMsg");
             }
        }
    }
    
    return &{$self->config->{commands}->{'open_source'}->{callback}}
                ( 
                   $self, 'open_source', $rconf, $line
                );
}

sub set_dbms_output { shift->{RASHELL_DBMS_OUTPUT} = 1 }

sub intro { "
rashell -- utility per la gestione delle Remote Activity

Scrivi 'help' per l'elenco dei comandi
Scrivi 'help comando' per la descrizione del comando

" }

sub outro { "" }

# copio i dati di @ARGV che verra' svuotato da Getopt
my @ARGV_BACKUP = @ARGV;
my $command_line = basename($0).' '.join(" ", map { /\s/ ? "'$_'" : $_ } @ARGV);

# ENUMerazione degli errori 
use enum qw	(:ERROR_=0
	OK=0
	SHOW_USAGE=0
	WRONG_OPTIONS
	UNABLE_TO_START_RASHELL
);

sub usage( $ ) {
    my $rc = shift;
    my $file = grep( /^$rc$/, ERROR_OK, ERROR_SHOW_USAGE ) ? *STDOUT : *STDERR;
    print $file "
    rashell -- utility per la gestione delle Remote Activity

    $0  [<opzioni>]

    <opzioni>: 
        --stop-on-error - termina l'elaborazione nel caso in cui un comando restituisca un errore. in genere
              utilizzato con input da stdin
        --debug-db - visualizza le query eseguite sul db
        --output-to-file=<FILENAME> - registra l'output dei comandi sul file indicato
        -h, --help - questo aiuto

    <utilizzo>:
        scrivi 'help' per l'elenco dei comandi
        scrivi 'help comando' per la descrizione del comando
        funziona sia in modalita' interattiva che attraverso lo standard input
        inserisci un comando per linea
        ogni comando, salvo diversamente indicato nell'help, acetta parametri tramite una stringa in formato
          JSON (http://json.org/json-it.html,http://www.w3schools.com/json/default.asp). se non ci sono parametri
          la stringa puo' essere omessa

";
	exit $rc;
}

my ( $help, $stop_on_error, $debug_db, $output_to_file );

GetOptions (
	'help|h'           => \$help,
	'stop-on-error'    => \$stop_on_error,
	'debug-db'         => \$debug_db,
	'output-to-file=s' => \$output_to_file,
) or usage( ERROR_WRONG_OPTIONS );
usage( ERROR_SHOW_USAGE ) if ( $help );

# ripristino @ARGV che e' stato svuotato da Getopt
@ARGV = @ARGV_BACKUP;

# rendo possibile usare la shell sia in maniera interattiva che in pipe da STDIN
my $input = -t STDIN ? undef : \*STDIN;

my %params = (
	 HISTFILE             => glob("~/.rashell_history")
	,SHELL_INPUT_STREAM   => $input
	,SHELL_BLOCK_ON_ERROR => $stop_on_error
);
$params{SHELL_OUTPUT_TO_FILE} = $output_to_file
	if defined $output_to_file;

my $sh = eval { Rashell->new( $config, %params ); };
if ( $@ ) {
	print STDERR "ERROR: Impossibile avviare rashell: $@";
	exit ERROR_UNABLE_TO_START_RASHELL;
}
$sh->set_dbms_output() if $debug_db;
$sh->run( );

exit ERROR_OK;


#### ------------------- Utility ---------------------------------------

sub get_env
{
   my ( $name, $dieflag ) = @_;

   return $ENV{$name} if exists $ENV{$name};
   return undef; 
}

#
# espande il valore di  eventuali variabili esterne (${})
#
# IN:  stringa(SCALAR),  getter( CODEREF )  [ getter = sub( nome_parametro ) { return value } ]
# OUT: stringa(SCALAR)
#
sub expand_string
{
    my $str = shift;
    my $getter = shift;
    my $result = undef;
    my $start = 0;
    my $reps = 0;

    return $str unless( ref( $getter ) eq 'CODE' );
    
    for( ;; $start = pos( $str ) )
    {
		if( $str =~ /\\(.)/gc ) {
			$result .= substr( $str, $start, pos( $str ) - 2 - $start );
		    $result .= $1;
		    $reps++;
		    next;
		}
		
		if( $str =~ /\$\{([^\}]+)\}/gc ) {
		    $result .= substr( $str, $start, pos( $str ) - 3 - length($1) - $start );
			my $name = $1;
			my $dieflag = !( $name =~ s/^\?// ); 
			my $val = &$getter( $name );
                     die( "la variabile '$name' non e' presente nell'environment" )
                            if( !defined( $val ) && $dieflag );
            $result .= ( defined $val ? $val : "" );
		    $reps++;
		    next;
		}
		
		$result .= substr( $str, $start );
		last;
	}	
	return $result;
}
 
sub __merge_env_param
{
   my ( $refPar, $errRef ) = @_;
   
   if( ref( $refPar ) eq 'SCALAR' ) {
       my $result = eval { expand_string( $$refPar, \&get_env) };
	   if( $@ ) {
	       $$errRef = "$@";
	       return 0;
	   }
	   $$refPar = $result;
   }
   return 1;
}

sub _read_ramq_config
{
    my ( $self, $configFile, $errRef ) = @_;
	my $hconf = undef;
	my $errMsg = undef;
	
	my $json = new JSON;
	
	$json->allow_barekey( 1 );
	$json->allow_singlequote( 1 );
	
	#lettura file di conf
	do {{
	   local $/;
	   
	   my $fin;
	   unless( open( $fin, "<$configFile" ) ) {
	       $errMsg = "Impossibile aprire il file di configurazione: $!";
		   last;
	   }
	
	   my $json_text = <$fin>;
	   close( $fin );
	   
	   my $conf = eval { $json->decode( $json_text ) };
	   if( $@ ) {
	       $errMsg = $@;
		   last;
	   }
       $json_text = undef;
	   
	   if( ref( $conf ) ne 'HASH' ) {
           $errMsg = "Formato configurazione non valido";
           last;
       }
        
       $hconf = $conf;
	}};
	
    $$errRef = $errMsg;
	return $hconf;
}
