#!/bin/bash

#
# Imposta l'installer che dovra' essere copiato in ogni pacchetto
#
INSTALLER="$(dirname $0)/patch_installer.sh"
if [[ ! -e "${INSTALLER}" ]]; then
	echo "Missing ${INSTALLER}!"
	exit 1
fi

#
# Verifica parametri in input
#
PRJ=$1
FROM_TAG=$2
TO_TAG=$3
REPO_SVN=$4
if [ "$PRJ" == "" -o "$FROM_TAG" == "" -o "$TO_TAG" == "" ]; then
	cat <<!
	
	Usage:

		$(basename $0) PRJ FROM_TAG TO_TAG REPO_SVN

	dove e':

		PRJ........: nome del progetto
		FROM_TAG...: versione del TAG iniziale
		TO_TAG.....: versione del TAG finale
		REPO_SVN...: directory dei TAG del repository SVN del progetto

	Esempio:

		$(basename $0) O2O 2.2.3 2.2.9 http://svnserver.sirtisistemi.net:10080/svno2o/ART/tags/
	
!
	exit 1
fi

#
# Definisce le variabili principali
#
SVN_CONF_DIR="${HOME}/.subversion"
PRJ_DIR="$SVN_CONF_DIR/${PRJ}"
PATCH_NAME="${PRJ}_${FROM_TAG}_${TO_TAG}"
PATCH_DIR="$SVN_CONF_DIR/${PATCH_NAME}"
PATCH_TAR="${PATCH_DIR}.tar"


#
# Messaggio di riepilogo prima della creazione del pacchetto
#
cat <<!

=================
SVN PATCH CREATOR
=================

Requested patch creation:

	PROJECT .......: ${PRJ}
	SVN REPO ......: ${REPO_SVN}
	FROM TAG ......: ${FROM_TAG}
	TO TAG ........: ${TO_TAG}
	PATCH NAME ....: ${PATCH_NAME}

Output information:

	PROJECT DIR ...: ${PRJ_DIR}
	PATCH DIR .....: ${PATCH_DIR}
	PATCH TAR .....: ${PATCH_TAR}

Press CTRL+C to cancel or ENTER to start...

!
read


###############################################################################
#
# Inizio creazione pacchetto
#

#
# Verifica che $PATCH_TAR non esista
#
if [ -f $PATCH_TAR ]; then
	echo "ERROR: patch '${PATCH_TAR}' already exists: please remove it and retry!"
	exit 1
fi

echo -n " * Creating PATCH_DIR ... "
if [ -d ${PATCH_DIR} ]; then
	echo "ERROR: patch directory '${PATCH_DIR}' already esists; please, check and retry!"
	exit 1
fi
mkdir ${PATCH_DIR}
[[ "$?" != "0" ]] && exit 1
echo "OK"

#
# Verifica che $PRJ_DIR non esista
#
if [ -d $PRJ_DIR ]; then
	echo "ERROR: project directory '${PRJ_DIR}' already exists: please remove it and retry!"
	exit 1
fi

#
# Effettua l'export della tag di destinazione
# FIXME: prevedere metodo più efficiente per estrarre solo i file da includere nella patch
#
echo -n " * Exporting from svn to PRJ_DIR, please wait... "
svn export $SVNACCOUNT ${REPO_SVN}/${PRJ}_${TO_TAG} $PRJ_DIR 1>/dev/null
[[ "$?" != "0" ]] && exit 1
echo "OK"

#echo -n " * Checking project ... "
#if [ ! -d ${PRJ_DIR} ]; then
#	echo "ERROR: project directory '${PRJ_DIR}' does not exist!"
#	exit 1
#fi
echo -n " * Switching to project directory... "
pwd=$(pwd)
cd ${PRJ_DIR}
[[ "$?" != "0" ]] && exit 1
echo "OK"


#
# Determina il base name del repository SVN specificato dall'utente
#
REPO_SVN_GREPPED=$( echo ${REPO_SVN} | sed -e 's/\/$//' | sed -e 's/\//\\\//g' | sed -e 's/\./\\\./g' )


#
# Creazione elenco file modificati
#
echo -n " * Creating MODIFIED file list ... "
cmd="$(cat <<!
svn diff \$SVNACCOUNT \
	--old=${REPO_SVN}/${PRJ}_${FROM_TAG} \
	--new=${REPO_SVN}/${PRJ}_${TO_TAG} \
	--summarize \
	| grep '^M ' \
	| sed -e 's/^........${REPO_SVN_GREPPED}\/${PRJ}_[^\/]*\///g' \
	| while read F; do [[ -f \$F ]] && echo \$F; done \
	| sort > ${PATCH_DIR}/${PATCH_NAME}_MODIFY.txt
!
)"
#echo $cmd
eval $cmd
[[ "$?" != "0" ]] && exit 1
echo "OK"

	
#
# Creazione elenco file con permessi modificati (da rimuovere in fase di installazione)
#
echo -n " * Looking for TYPE-MODIFIED file list (adding do DELETE list)... "
cmd="$(cat <<!
svn diff \$SVNACCOUNT \
	--old=${REPO_SVN}/${PRJ}_${FROM_TAG} \
	--new=${REPO_SVN}/${PRJ}_${TO_TAG} \
	--summarize \
	| grep -P '^(MM| M) ' \
	| sed -e 's/^........${REPO_SVN_GREPPED}\/${PRJ}_[^\/]*\///g' \
	| while read F; do [[ -f \$F ]] && echo \$F; done \
	| sort >> ${PATCH_DIR}/${PATCH_NAME}_DELETE.txt
!
)"
#echo $cmd
eval $cmd
[[ "$?" != "0" ]] && exit 1
echo "OK"
	
#
# Creazione elenco file con permessi modificati (da rilaciare in fase di installazione)
#
echo -n " * Looking for TYPE-MODIFIED file list (adding do ADD list)... "
cmd="$(cat <<!
svn diff \$SVNACCOUNT \
	--old=${REPO_SVN}/${PRJ}_${FROM_TAG} \
	--new=${REPO_SVN}/${PRJ}_${TO_TAG} \
	--summarize \
	| grep -P '^(MM| M) ' \
	| sed -e 's/^........${REPO_SVN_GREPPED}\/${PRJ}_[^\/]*\///g' \
	| while read F; do [[ -f \$F || -d \$F ]] && echo \$F; done \
	| sort >> ${PATCH_DIR}/${PATCH_NAME}_ADD.txt
!
)"
#echo $cmd
eval $cmd
[[ "$?" != "0" ]] && exit 1
echo "OK"
	
	
#
# Creazione elenco file nuovi
#
echo -n " * Creating ADDED file list ... "
cmd="$(cat <<!
svn diff \$SVNACCOUNT \
	--old=${REPO_SVN}/${PRJ}_${FROM_TAG} \
	--new=${REPO_SVN}/${PRJ}_${TO_TAG} \
	--summarize \
	| grep '^A ' \
	| sed -e 's/^........${REPO_SVN_GREPPED}\/${PRJ}_[^\/]*\///g' \
	| while read F; do [[ -f \$F || -d \$F ]] && echo \$F; done \
	| sort >> ${PATCH_DIR}/${PATCH_NAME}_ADD.txt
!
)"
#echo $cmd
eval $cmd
[[ "$?" != "0" ]] && exit 1
echo "OK"


#
# Creazione elenco file da eliminare
#
echo -n " * Creating DELETED file list ... "
cmd="$(cat <<!
svn diff \$SVNACCOUNT \
	--old=${REPO_SVN}/${PRJ}_${FROM_TAG} \
	--new=${REPO_SVN}/${PRJ}_${TO_TAG} \
	--summarize \
	| grep '^D ' \
	| sed -e 's/^........${REPO_SVN_GREPPED}\/${PRJ}_[^\/]*\///g' \
	| sort >> ${PATCH_DIR}/${PATCH_NAME}_DELETE.txt
!
)"
#echo $cmd
eval $cmd
[[ "$?" != "0" ]] && exit 1
echo "OK"


#
# Calcolo MD5 dei file modificati e aggiunti
#
echo -n " * Computing MODIFIED file checksum (md5) ... "
cat  ${PATCH_DIR}/${PATCH_NAME}_MODIFY.txt | while read F; do if [[ -f $F ]]; then echo $(md5sum $F); fi; done > ${PATCH_DIR}/${PATCH_NAME}_MODIFY.md5
[[ "$?" != "0" ]] && exit 1
echo "OK"
echo -n " * Computing ADDED file checksum (md5) ... "
cat  ${PATCH_DIR}/${PATCH_NAME}_ADD.txt | while read F; do if [[ -f $F ]]; then echo $(md5sum $F); fi; done > ${PATCH_DIR}/${PATCH_NAME}_ADD.md5
[[ "$?" != "0" ]] && exit 1
echo "OK"


#
# Creazione pacchetto di distribuzione
#
echo -n " * Copying installation utility ... "
cp -p ${INSTALLER} ${PATCH_DIR}/install.sh
[[ "$?" != "0" ]] && exit 1
echo "OK"


echo -n " * Creating gzipped file ... "
cat ${PATCH_DIR}/${PATCH_NAME}_MODIFY.txt ${PATCH_DIR}/${PATCH_NAME}_ADD.txt | tar -cvzf ${PATCH_DIR}/${PATCH_NAME}.tar.gz -T - >/dev/null
[[ "$?" != "0" ]] && exit 1
echo "OK"


echo -n " * Creating tarball file ... "
cd $SVN_CONF_DIR >/dev/null 2>&1
tar -cvf ${PATCH_TAR} ${PATCH_NAME} >/dev/null
[[ "$?" != "0" ]] && exit 1
echo "OK"


#
# Rimozione directory temporanee
#
echo -n " * Removing ${PATCH_DIR}... "
rm -rf ${PATCH_DIR} >/dev/null 2>&1
[[ "$?" != "0" ]] && exit 1
echo "OK"

echo -n " * Removing ${PRJ_DIR}... "
rm -rf ${PRJ_DIR} >/dev/null 2>&1
[[ "$?" != "0" ]] && exit 1
echo "OK"


cd $pwd >/dev/null 2>&1


cat <<!

Process terminated: patch made at ${PATCH_TAR}

Good luck! ;-)

!


