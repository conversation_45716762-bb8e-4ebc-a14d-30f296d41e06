---
CIL:
  ROOT_NODE: ns0:OrchestratorRequestToBizTalk
  NAMESPACES:
    - NAMESPACE: http://www.sirti.it/BizTalk/Application/ComunicazioniInizioLavoriTelecom
      PREFIX: ns0
  CONF:
    - NAME: documentNumber
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/documentNumber
        PATH: '["system"]["properties"]["propositionNumber"]'
    - NAME: prog
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/prog
        PATH: '["properties"]["progCIL"]'
    - NAME: dateDocCIL
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/dateDocCIL
        PATH: '["properties"]["dateDocCIL"]'
    - NAME: networkId
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/networkId
        PATH: '["system"]["properties"]["networkId"]'
    - NAME: operation
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/operation
        PATH: '["system"]["properties"]["operation"]'
    - NAME: supplierId
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/supplierId
        PATH: '["properties"]["supplierId"]'
    - NAME: supplierDescription
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/supplierDescription
        PATH: '["properties"]["supplierDescription"]'
    - NAME: ftcName
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/ftcName
        PATH: '["properties"]["ftcName"]'
    - NAME: centralOf
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/centralOf
        PATH: '["properties"]["centralOf"]'
    - NAME: technology
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/technology
        PATH: '["properties"]["technology"]'
    - NAME: propositionNumber
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/propositionNumber
        PATH: '["system"]["properties"]["propositionNumber"]'
    - NAME: customerAssignmentDateActual
      SUB: set_customer_assignment_date_actual
      ARGS:
        XPATH: heading/customerAssignmentDateActual
        PATH: '["properties"]["customerAssignmentDateActual"]'
    - NAME: execProg
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/execProg
        PATH: '["properties"]["execProg"]'
    - NAME: execProgRev
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/execProgRev
        PATH: '["properties"]["execProgRev"]'
    - NAME: startDate
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/startDate
        PATH: '["properties"]["startDate"]'
    - NAME: presumedEndDate
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/presumedEndDate
        PATH: '["properties"]["presumedEndDate"]'
    - NAME: technicalArea
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/technicalArea
        PATH: '["properties"]["technicalArea"]'
    - NAME: zeroCostActivity
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/zeroCostActivity
        PATH: '["properties"]["zeroCostActivity"]'
    - NAME: zeroCostActivityDate
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/zeroCostActivityDate
        PATH: '["properties"]["zeroCostActivityDate"]'
    - NAME: activityNote
      SUB: set_activity_note
      ARGS:
        XPATH: heading/activityNote
    - NAME: versionDescription
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/versionDescription
        PATH: '["properties"]["versionDescription"]'
    - NAME: safetyAccountableName
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/safetyAccountableName
        PATH: '["properties"]["safetyAccountableName"]'
    - NAME: safetyAccountableSurname
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/safetyAccountableSurname
        PATH: '["properties"]["safetyAccountableSurname"]'
    - NAME: accountableName
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/accountableName
        PATH: '["properties"]["accountableName"]'
    - NAME: accountableSurname
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/accountableSurname
        PATH: '["properties"]["accountableSurname"]'
    - NAME: accountablePhoneNumber
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/accountablePhoneNumber
        PATH: '["properties"]["accountablePhoneNumber"]'
    - NAME: accountableDate
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/accountableDate
        PATH: '["properties"]["accountableDate"]'
    - NAME: subAccountableName
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/subAccountableName
        PATH: '["properties"]["subAccountableName"]'
    - NAME: subAccountableSurname
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/subAccountableSurname
        PATH: '["properties"]["subAccountableSurname"]'
    - NAME: subPhone
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/subPhone
        PATH: '["properties"]["subPhone"]'
    - NAME: subDate
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/subDate
        PATH: '["properties"]["subDate"]'
    - NAME: subFax
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/subFax
        PATH: '["properties"]["subFax"]'
    - NAME: subDescription
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/subDescription
        PATH: '["properties"]["subDescription"]'
    - NAME: testRequest
      SUB: set_test_request
      ARGS:
        XPATH: heading/testRequest
        PATH: '["properties"]["testRequest"]'
CUI:
  ROOT_NODE: ns0:OrchestratorRequestToBizTalk
  NAMESPACES:
    - NAMESPACE: http://www.sirti.it/BizTalk/Application/ComunicazioniUltimazioneImpiantoTelecom
      PREFIX: ns0
  CONF:
    - NAME: documentNumber
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/documentNumber
        PATH: '["system"]["properties"]["propositionNumber"]'
    - NAME: prog
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/prog
        PATH: '["properties"]["progCUI"]'
    - NAME: dateDocCUI
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/dateDocCUI
        PATH: '["properties"]["dateDocCUI"]'
    - NAME: networkId
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/networkId
        PATH: '["system"]["properties"]["networkId"]'
    - NAME: operation
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/operation
        PATH: '["system"]["properties"]["operation"]'
    - NAME: supplierId
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/supplierId
        PATH: '["properties"]["supplierId"]'
    - NAME: supplierDescription
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/supplierDescription
        PATH: '["properties"]["supplierDescription"]'
    - NAME: cluName
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/cluName
        PATH: '["properties"]["cluName"]'
    - NAME: centralOf
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/centralOf
        PATH: '["properties"]["centralOf"]'
    - NAME: technology
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/technology
        PATH: '["properties"]["technology"]'
    - NAME: propositionNumber
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/propositionNumber
        PATH: '["system"]["properties"]["propositionNumber"]'
    - NAME: customerAssignmentDateActual
      SUB: set_customer_assignment_date_actual
      ARGS:
        XPATH: heading/customerAssignmentDateActual
        PATH: '["properties"]["customerAssignmentDateActual"]'
    - NAME: execProg
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/execProg
        PATH: '["properties"]["execProg"]'
    - NAME: execProgRev
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/execProgRev
        PATH: '["properties"]["execProgRev"]'
    - NAME: activityNote
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/activityNote
        PATH: '["properties"]["activityNote"]'
    - NAME: endDate
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/endDate
        PATH: '["properties"]["endDate"]'
    - NAME: shipmentType
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/shipmentType
        PATH: '["properties"]["shipmentType"]'
    - NAME: particularNotes
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/particularNotes
        PATH: '["properties"]["particularNotes"]'
    - NAME: safetyAccountableName
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/safetyAccountableName
        PATH: '["properties"]["safetyAccountableName"]'
    - NAME: safetyAccountableSurname
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/safetyAccountableSurname
        PATH: '["properties"]["safetyAccountableSurname"]'
    - NAME: partyAtTest
      SUB: set_party_at_test
      ARGS:
        XPATH: heading/partyAtTest
        PATH: '["properties"]["partyAtTest"]'
    - NAME: moiValue
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/moiValue
        PATH: '["properties"]["moiValue"]'
        IS_CURRENCY: 1
    - NAME: fornitureValue
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/fornitureValue
        PATH: '["properties"]["fornitureValue"]'
        IS_CURRENCY: 1
    - NAME: advancementPercent
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/advancementPercent
        PATH: '["properties"]["advancementPercent"]'
    - NAME: testDate
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/testDate
        PATH: '["properties"]["testDate"]'
    - NAME: details
      SUB: set_data_collection
      ARGS:
        COLLECTION_NODE_XPATH: details
        CHILD_NODE_XPATH: detail
        CHILD_CONTENT_ORDER:
          - name
          - surname
          - businessRole
          - badge
          - companyCard
          - note
        PATH: '["properties"]["people"]'
RCI:
  ROOT_NODE: ns0:OrchestratorRequestToBizTalk
  NAMESPACES:
    - NAMESPACE: http://www.sirti.it/BizTalk/Application/RichiesteCollaudoImpiantoTelecom
      PREFIX: ns0
  CONF:
    - NAME: documentNumber
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/documentNumber
        PATH: '["system"]["properties"]["propositionNumber"]'
    - NAME: prog
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/prog
        PATH: '["properties"]["progRCI"]'
    - NAME: dateDocRCI
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/dateDocRCI
        PATH: '["properties"]["dateDocRCI"]'
    - NAME: networkId
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/networkId
        PATH: '["system"]["properties"]["networkId"]'
    - NAME: operation
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/operation
        PATH: '["system"]["properties"]["operation"]'
    - NAME: supplierId
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/supplierId
        PATH: '["properties"]["supplierId"]'
    - NAME: supplierDescription
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/supplierDescription
        PATH: '["properties"]["supplierDescription"]'
    - NAME: centralOf
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/centralOf
        PATH: '["properties"]["centralOf"]'
    - NAME: execProg
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/execProg
        PATH: '["properties"]["execProg"]'
    - NAME: execProgRev
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/execProgRev
        PATH: '["properties"]["execProgRev"]'
    - NAME: endDate
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/endDate
        PATH: '["properties"]["endDate"]'
    - NAME: shipmentType
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/shipmentType
        PATH: '["properties"]["shipmentType"]'
    - NAME: activityNote
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/activityNote
        PATH: '["properties"]["activityNote"]'
    - NAME: testDate
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/testDate
        PATH: '["properties"]["testDate"]'
        SUFFIX: ' 00:00:00'
    - NAME: safetyAccountableName
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/safetyAccountableName
        PATH: '["properties"]["safetyAccountableName"]'
    - NAME: safetyAccountableSurname
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/safetyAccountableSurname
        PATH: '["properties"]["safetyAccountableSurname"]'
    - NAME: tdta0021
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/tdta0021
        PATH: '["properties"]["tdta0021"]'
    - NAME: tdta0022
      SUB: set_data_from_path
      ARGS:
        XPATH: heading/tdta0022
        PATH: '["properties"]["tdta0022"]'
