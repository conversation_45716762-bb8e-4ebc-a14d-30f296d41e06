#
# per utilizzare questa configurazione e' necessario avere impostato la variabile di environment LOG_PATH
#
log4perl.logger.rootLogger              = FATAL, ErrScreen

log4perl.logger.COMMON::BIN::AgingHandler   = DEBUG, AgingHandlerLog, Screen, FullLog
log4perl.logger.COMMON::LIB             = WARN,  FullLog

log4perl.appender.ErrScreen = Log::Dispatch::Screen
log4perl.appender.ErrScreen.stderr = 1
log4perl.appender.ErrScreen.Threshold = FATAL
log4perl.appender.ErrScreen.layout = Log::Log4perl::Layout::PatternLayout
log4perl.appender.ErrScreen.layout.ConversionPattern = %d %p> %F{1}:%L %M - %m%n

log4perl.appender.Screen = Log::Dispatch::Screen
log4perl.appender.Screen.Threshold = INFO
log4perl.appender.Screen.stderr = 0
log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
log4perl.appender.Screen.layout.ConversionPattern = %p> %m%n

log4perl.appender.AgingHandlerLog = Log::Dispatch::File
log4perl.appender.AgingHandlerLog.Threshold = INFO
log4perl.appender.AgingHandlerLog.filename = sub { use Date::Calc qw( Today ); mkdir ( ( $ENV{LOG_PATH} || '' ) . '/' . 'AgingHandler' .'/' ); ( $ENV{LOG_PATH} || '' ) . '/' . 'AgingHandler' .'/' . 'aging_handler.log' . '.' . sprintf("%04d%02d%02d", Today) }
log4perl.appender.AgingHandlerLog.mode = append
log4perl.appender.AgingHandlerLog.layout = Log::Log4perl::Layout::PatternLayout
log4perl.appender.AgingHandlerLog.layout.ConversionPattern = %d %p> %m%n

log4perl.appender.FullLog = Log::Dispatch::File
log4perl.appender.FullLog.Threshold = DEBUG
log4perl.appender.FullLog.filename = sub { use Date::Calc qw( Today ); mkdir ( ( $ENV{LOG_PATH} || '' ) . '/' . 'AgingHandler' .'/'); ( $ENV{LOG_PATH} || '' ) . '/' . 'AgingHandler' .'/' . 'aging_handler.full.log' . '.' . sprintf("%04d%02d%02d", Today) }
log4perl.appender.FullLog.mode = append
log4perl.appender.FullLog.layout = Log::Log4perl::Layout::PatternLayout
log4perl.appender.FullLog.layout.ConversionPattern = %d %p> %F{1}:%L %M - %m%n

