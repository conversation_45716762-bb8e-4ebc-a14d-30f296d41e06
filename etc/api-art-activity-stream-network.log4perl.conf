#
# per utilizzare questa configurazione e' necessario avere impostato la variabile di environment LOG_PATH
#

log4perl.logger.COMMON::BIN::api-art-activity-stream-network = DEBUG, SummaryLog, Screen, DetailLog
log4perl.logger.COMMON::LIB                          = WARN,  DetailLog

log4perl.additivity.COMMON::BIN::api-art-activity-stream-network = 0
log4perl.additivity.COMMON::LIB                          = 0

log4perl.appender.ErrScreen = Log::Dispatch::Screen
log4perl.appender.ErrScreen.stderr = 1
log4perl.appender.ErrScreen.Threshold = FATAL
log4perl.appender.ErrScreen.layout = Log::Log4perl::Layout::PatternLayout
log4perl.appender.ErrScreen.layout.ConversionPattern = %d %p> %F{1}:%L %M - %m%n

log4perl.appender.Screen = Log::Dispatch::Screen
log4perl.appender.Screen.Threshold = INFO
log4perl.appender.Screen.stderr = 0
log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
log4perl.appender.Screen.layout.ConversionPattern = %p> %m%n

log4perl.appender.SummaryLog = Log::Dispatch::File
log4perl.appender.SummaryLog.Threshold = INFO
log4perl.appender.SummaryLog.filename = sub { use Date::Calc qw( Today_and_Now ); ( $ENV{LOG_PATH} || '' ) . '/' . 'api-art-activity-stream-network.log' . '.' . sprintf("%04d%02d%02d", Today_and_Now) }
log4perl.appender.SummaryLog.mode = append
log4perl.appender.SummaryLog.layout = Log::Log4perl::Layout::PatternLayout
log4perl.appender.SummaryLog.layout.ConversionPattern = %d %p> %A %m%n
log4perl.appender.SummaryLog.layout.cspec.A = sub { my $file_name = basename($0); $file_name }

log4perl.appender.DetailLog = Log::Dispatch::File
log4perl.appender.DetailLog.Threshold = DEBUG
log4perl.appender.DetailLog.filename = sub { use Date::Calc qw( Today_and_Now ); ( $ENV{LOG_PATH} || '' ) . '/' . 'api-art-activity-stream-network_full.log' . '.' . sprintf("%04d%02d%02d", Today_and_Now) }
log4perl.appender.DetailLog.mode = append
log4perl.appender.DetailLog.layout = Log::Log4perl::Layout::PatternLayout
log4perl.appender.DetailLog.layout.ConversionPattern = %d %p> %A %L %M %m%n
log4perl.appender.DetailLog.layout.cspec.A = sub { my $file_name = basename($0); $file_name }

