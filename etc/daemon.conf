#
#file di configurazione di start_stop_daemon
#
# N.B: l'allineamento utenti non lavora in pull come usuale, ma lavora in push verso ARTSOEN
# @artsoen_users_management:log:$LOG_PATH/ArtSoEnUsersManagement/users_management.log.$CURDATE
# @artsoen_users_management:log:$LOG_PATH/ArtSoEnUsersManagement/users_management.full.log.$CURDATE
artsoen_users_management              arg   users_management
artsoen_users_management              arg   --artid=${ARTSOEN_ARTID}
artsoen_users_management              arg   --api-user=${ARTSOEN_ADMIN_USER}
artsoen_users_management              arg   --api-password=${ARTSOEN_ADMIN_PASSWORD}
artsoen_users_management              arg   --log-config=${ETC}/artsoen_users_management.log4perl.conf
artsoen_users_management              arg   --driver=API::ART::Driver::Users::Baseline
artsoen_users_management              arg   --oBaselineConnectString=${SQLID_WWW}
artsoen_users_management              arg   --project-name=ARTSOEN
artsoen_users_management              arg   --manage=C
artsoen_users_management              arg   --manage=U
artsoen_users_management              arg   --manage=D
artsoen_users_management              arg   --notify-email=${MAILADDR_BG}
artsoen_users_management              arg   --daemon=60
artsoen_users_management              arg   --transaction-mode=c
#
# @users_management:log:$LOG_PATH/UsersManagement/users_management.log.$CURDATE
# @users_management:log:$LOG_PATH/UsersManagement/users_management.full.log.$CURDATE
##### per DB2
#users_management              arg   users_management
#users_management              arg   --log-config=${COM}/etc/users_management.log4perl.conf
#users_management              arg   --driver=API::ART::Driver::Users::SIA
#users_management              arg   --oDb2Name=${SIA_DB2_NAME}
#users_management              arg   --oDb2Host=${SIA_DB2_HOST}
#users_management              arg   --oDb2Port=${SIA_DB2_PORT}
#users_management              arg   --oDb2Protocol=${SIA_DB2_PROTOCOL}
#users_management              arg   --oDb2Uid=${SIA_DB2_UID}
#users_management              arg   --oDb2Pwd=${SIA_DB2_PWD}
#users_management              arg   --oInternalResource=${SIA_INTERNAL_RESOURCE}
#users_management              arg   --oExternalResource=${SIA_EXTERNAL_RESOURCE}
#users_management              arg   --camelize-names
#users_management              arg   --project-name=${NAMESPACE}
#users_management              arg   --manage=C
#users_management              arg   --manage=U
##users_management              arg   --manage=D
#users_management              arg   --notify-email=${MAILADDR_BG}
#users_management              arg   --daemon=7200
#users_management              arg   --transaction-mode=c
#### per ORACLE
users_management              arg   users_management
users_management              arg   --log-config=${COM}/etc/users_management.log4perl.conf
users_management              arg   --driver=API::ART::Driver::Users::SIA_ORACLE
users_management              arg   --oDBSiaConnectString=${SQLID_ANAGRAFICHE}
users_management              arg   --oInternalResource=${SIA_INTERNAL_RESOURCE}
users_management              arg   --oExternalResource=${SIA_EXTERNAL_RESOURCE}
users_management              arg   --oExternalResource=${SIA_EXTERNAL_RESOURCE_TIM}
users_management              arg   --camelize-names
users_management              arg   --project-name=${NAMESPACE}
users_management              arg   --manage=C
users_management              arg   --manage=U
users_management              arg   --manage=D
users_management              arg   --notify-email=${MAILADDR_BG}
users_management              arg   --daemon=7200
users_management              arg   --transaction-mode=c
#
# @ENEL_FTTH_crea_nuovi_progetti:log:$LOG_PATH/ENEL_FTTH_crea_nuovi_progetti.log.$CURDATE
# @ENEL_FTTH_crea_nuovi_progetti:log:$LOG_PATH/ENEL_FTTH_crea_nuovi_progetti.full.log.$CURDATE
ENEL_FTTH_crea_nuovi_progetti              arg   crea_nuovi_progetti
ENEL_FTTH_crea_nuovi_progetti              arg   ENEL
ENEL_FTTH_crea_nuovi_progetti              arg   FTTH
ENEL_FTTH_crea_nuovi_progetti              arg   --daemon=1800
ENEL_FTTH_crea_nuovi_progetti              arg   --transaction-mode=c
#
# @new_tt_ack:log:$LOG_PATH/NewTTAck/consumer.log.$CURDATE
# @new_tt_ack:log:$LOG_PATH/NewTTAck/ramq.log.$CURDATE
# @new_tt_ack:log:$LOG_PATH/NewTTAck/ramq_full.log.$CURDATE
new_tt_ack          arg   ramq
new_tt_ack          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
new_tt_ack          arg   --daemon=30
new_tt_ack          arg   --transaction-mode=c
new_tt_ack          arg   ${ETC}/ramq/TT/NewTTAck.conf 
#
# @network:log:$LOG_PATH/Network/consumer.log.$CURDATE
# @network:log:$LOG_PATH/Network/ramq.log.$CURDATE
# @network:log:$LOG_PATH/Network/ramq_full.log.$CURDATE
network          arg   ramq
network          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
network          arg   --daemon=300
network          arg   --transaction-mode=c
network          arg   ${ETC}/ramq/NETWORK/Network.conf
#
# @network_response:log:$LOG_PATH/NetworkResponse/consumer.log.$CURDATE
# @network_response:log:$LOG_PATH/NetworkResponse/ramq.log.$CURDATE
# @network_response:log:$LOG_PATH/NetworkResponse/ramq_full.log.$CURDATE
network_response          arg   ramq
network_response          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
network_response          arg   --daemon=300
network_response          arg   --transaction-mode=c
network_response          arg   ${ETC}/ramq/NETWORK/NetworkResponse.conf
#
# @network_works_report:log:$LOG_PATH/NetworkWorksReport/consumer.log.$CURDATE
# @network_works_report:log:$LOG_PATH/NetworkWorksReport/ramq.log.$CURDATE
# @network_works_report:log:$LOG_PATH/NetworkWorksReport/ramq_full.log.$CURDATE
network_works_report          arg   ramq
network_works_report          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
network_works_report          arg   ${ETC}/ramq/NETWORK/NetworkWorksReport.conf
network_works_report          arg   --daemon=300
network_works_report          arg   --transaction-mode=c
#
# @network_works_notify_open:log:$LOG_PATH/NetworkWorksNotifyOpen/consumer.log.$CURDATE
# @network_works_notify_open:log:$LOG_PATH/NetworkWorksNotifyOpen/ramq.log.$CURDATE
# @network_works_notify_open:log:$LOG_PATH/NetworkWorksNotifyOpen/ramq_full.log.$CURDATE
network_works_notify_open          arg   ramq
network_works_notify_open          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
network_works_notify_open          arg   --daemon=3
network_works_notify_open          arg   --transaction-mode=c
network_works_notify_open          arg   ${ETC}/ramq/NETWORK/NetworkWorksNotifyOpen.conf
#
# @network_works_notify_close:log:$LOG_PATH/NetworkWorksNotifyClose/consumer.log.$CURDATE
# @network_works_notify_close:log:$LOG_PATH/NetworkWorksNotifyClose/ramq.log.$CURDATE
# @network_works_notify_close:log:$LOG_PATH/NetworkWorksNotifyClose/ramq_full.log.$CURDATE
network_works_notify_close          arg   ramq
network_works_notify_close          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
network_works_notify_close          arg   --daemon=3
network_works_notify_close          arg   --transaction-mode=c
network_works_notify_close          arg   ${ETC}/ramq/NETWORK/NetworkWorksNotifyClose.conf
#
# @network_out_requests:log:$LOG_PATH/NetworkOutRequests/consumer.log.$CURDATE
# @network_out_requests:log:$LOG_PATH/NetworkOutRequests/ramq.log.$CURDATE
# @network_out_requests:log:$LOG_PATH/NetworkOutRequests/ramq_full.log.$CURDATE
network_out_requests      arg   ramq
network_out_requests      arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
network_out_requests      arg   --daemon=300
network_out_requests      arg   --transaction-mode=c
network_out_requests      arg   ${ETC}/ramq/NETWORK/OutRequests.conf
#
# @network_public_permits_notify_open:log:$LOG_PATH/NetworkPublicPermitsNotifyOpen/consumer.log.$CURDATE
# @network_public_permits_notify_open:log:$LOG_PATH/NetworkPublicPermitsNotifyOpen/ramq.log.$CURDATE
# @network_public_permits_notify_open:log:$LOG_PATH/NetworkPublicPermitsNotifyOpen/ramq_full.log.$CURDATE
network_public_permits_notify_open          arg   ramq
network_public_permits_notify_open          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
network_public_permits_notify_open          arg   --daemon=3
network_public_permits_notify_open          arg   --transaction-mode=c
network_public_permits_notify_open          arg   ${ETC}/ramq/NETWORK/NetworkPublicPermitsNotifyOpen.conf
#
# @network_public_permits_notify_close:log:$LOG_PATH/NetworkPublicPermitsNotifyClose/consumer.log.$CURDATE
# @network_public_permits_notify_close:log:$LOG_PATH/NetworkPublicPermitsNotifyClose/ramq.log.$CURDATE
# @network_public_permits_notify_close:log:$LOG_PATH/NetworkPublicPermitsNotifyClose/ramq_full.log.$CURDATE
network_public_permits_notify_close          arg   ramq
network_public_permits_notify_close          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
network_public_permits_notify_close          arg   --daemon=3
network_public_permits_notify_close          arg   --transaction-mode=c
network_public_permits_notify_close          arg   ${ETC}/ramq/NETWORK/NetworkPublicPermitsNotifyClose.conf
#
# @network_private_permits_notify_open:log:$LOG_PATH/NetworkPrivatePermitsNotifyOpen/consumer.log.$CURDATE
# @network_private_permits_notify_open:log:$LOG_PATH/NetworkPrivatePermitsNotifyOpen/ramq.log.$CURDATE
# @network_private_permits_notify_open:log:$LOG_PATH/NetworkPrivatePermitsNotifyOpen/ramq_full.log.$CURDATE
network_private_permits_notify_open          arg   ramq
network_private_permits_notify_open          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
network_private_permits_notify_open          arg   --daemon=3
network_private_permits_notify_open          arg   --transaction-mode=c
network_private_permits_notify_open          arg   ${ETC}/ramq/NETWORK/NetworkPrivatePermitsNotifyOpen.conf
#
# @network_private_permits_notify_close:log:$LOG_PATH/NetworkPrivatePermitsNotifyClose/consumer.log.$CURDATE
# @network_private_permits_notify_close:log:$LOG_PATH/NetworkPrivatePermitsNotifyClose/ramq.log.$CURDATE
# @network_private_permits_notify_close:log:$LOG_PATH/NetworkPrivatePermitsNotifyClose/ramq_full.log.$CURDATE
network_private_permits_notify_close          arg   ramq
network_private_permits_notify_close          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
network_private_permits_notify_close          arg   --daemon=3
network_private_permits_notify_close          arg   --transaction-mode=c
network_private_permits_notify_close          arg   ${ETC}/ramq/NETWORK/NetworkPrivatePermitsNotifyClose.conf
#
# @network_roe_notify_open:log:$LOG_PATH/NetworkROENotifyOpen/consumer.log.$CURDATE
# @network_roe_notify_open:log:$LOG_PATH/NetworkROENotifyOpen/ramq.log.$CURDATE
# @network_roe_notify_open:log:$LOG_PATH/NetworkROENotifyOpen/ramq_full.log.$CURDATE
network_roe_notify_open          arg   ramq
network_roe_notify_open          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
network_roe_notify_open          arg   --daemon=3
network_roe_notify_open          arg   --transaction-mode=c
network_roe_notify_open          arg   ${ETC}/ramq/NETWORK/NetworkROENotifyOpen.conf
#
# @network_roe_notify_close:log:$LOG_PATH/NetworkROENotifyClose/consumer.log.$CURDATE
# @network_roe_notify_close:log:$LOG_PATH/NetworkROENotifyClose/ramq.log.$CURDATE
# @network_roe_notify_close:log:$LOG_PATH/NetworkROENotifyClose/ramq_full.log.$CURDATE
network_roe_notify_close          arg   ramq
network_roe_notify_close          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
network_roe_notify_close          arg   --daemon=3
network_roe_notify_close          arg   --transaction-mode=c
network_roe_notify_close          arg   ${ETC}/ramq/NETWORK/NetworkROENotifyClose.conf
#
# @roe_works_notify_open:log:$LOG_PATH/ROEWorksNotifyOpen/consumer.log.$CURDATE
# @roe_works_notify_open:log:$LOG_PATH/ROEWorksNotifyOpen/ramq.log.$CURDATE
# @roe_works_notify_open:log:$LOG_PATH/ROEWorksNotifyOpen/ramq_full.log.$CURDATE
roe_works_notify_open          arg   ramq
roe_works_notify_open          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
roe_works_notify_open          arg   --daemon=3
roe_works_notify_open          arg   --transaction-mode=c
roe_works_notify_open          arg   ${ETC}/ramq/ROE/ROEWorksNotifyOpen.conf
#
# @roe_works_notify_close:log:$LOG_PATH/ROEWorksNotifyClose/consumer.log.$CURDATE
# @roe_works_notify_close:log:$LOG_PATH/ROEWorksNotifyClose/ramq.log.$CURDATE
# @roe_works_notify_close:log:$LOG_PATH/ROEWorksNotifyClose/ramq_full.log.$CURDATE
roe_works_notify_close          arg   ramq
roe_works_notify_close          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
roe_works_notify_close          arg   --daemon=3
roe_works_notify_close          arg   --transaction-mode=c
roe_works_notify_close          arg   ${ETC}/ramq/ROE/ROEWorksNotifyClose.conf
#
# @roe_public_permits_notify_open:log:$LOG_PATH/ROEPublicPermitsNotifyOpen/consumer.log.$CURDATE
# @roe_public_permits_notify_open:log:$LOG_PATH/ROEPublicPermitsNotifyOpen/ramq.log.$CURDATE
# @roe_public_permits_notify_open:log:$LOG_PATH/ROEPublicPermitsNotifyOpen/ramq_full.log.$CURDATE
roe_public_permits_notify_open          arg   ramq
roe_public_permits_notify_open          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
roe_public_permits_notify_open          arg   --daemon=3
roe_public_permits_notify_open          arg   --transaction-mode=c
roe_public_permits_notify_open          arg   ${ETC}/ramq/ROE/ROEPublicPermitsNotifyOpen.conf
#
# @roe_public_permits_notify_close:log:$LOG_PATH/ROEPublicPermitsNotifyClose/consumer.log.$CURDATE
# @roe_public_permits_notify_close:log:$LOG_PATH/ROEPublicPermitsNotifyClose/ramq.log.$CURDATE
# @roe_public_permits_notify_close:log:$LOG_PATH/ROEPublicPermitsNotifyClose/ramq_full.log.$CURDATE
roe_public_permits_notify_close          arg   ramq
roe_public_permits_notify_close          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
roe_public_permits_notify_close          arg   --daemon=3
roe_public_permits_notify_close          arg   --transaction-mode=c
roe_public_permits_notify_close          arg   ${ETC}/ramq/ROE/ROEPublicPermitsNotifyClose.conf
#
# @roe_private_permits_notify_open:log:$LOG_PATH/ROEPrivatePermitsNotifyOpen/consumer.log.$CURDATE
# @roe_private_permits_notify_open:log:$LOG_PATH/ROEPrivatePermitsNotifyOpen/ramq.log.$CURDATE
# @roe_private_permits_notify_open:log:$LOG_PATH/ROEPrivatePermitsNotifyOpen/ramq_full.log.$CURDATE
roe_private_permits_notify_open          arg   ramq
roe_private_permits_notify_open          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
roe_private_permits_notify_open          arg   --daemon=3
roe_private_permits_notify_open          arg   --transaction-mode=c
roe_private_permits_notify_open          arg   ${ETC}/ramq/ROE/ROEPrivatePermitsNotifyOpen.conf
#
# @roe_private_permits_notify_close:log:$LOG_PATH/ROEPrivatePermitsNotifyClose/consumer.log.$CURDATE
# @roe_private_permits_notify_close:log:$LOG_PATH/ROEPrivatePermitsNotifyClose/ramq.log.$CURDATE
# @roe_private_permits_notify_close:log:$LOG_PATH/ROEPrivatePermitsNotifyClose/ramq_full.log.$CURDATE
roe_private_permits_notify_close          arg   ramq
roe_private_permits_notify_close          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
roe_private_permits_notify_close          arg   --daemon=3
roe_private_permits_notify_close          arg   --transaction-mode=c
roe_private_permits_notify_close          arg   ${ETC}/ramq/ROE/ROEPrivatePermitsNotifyClose.conf
#
# @loader_management:log:$LOG_PATH/loader_management.log.$CURDATE
# @loader_management:log:$LOG_PATH/loader_management.full.log.$CURDATE
loader_management              arg   loader_management
loader_management              arg   --daemon=60
loader_management              arg   --transaction-mode=c
#
# @loader_test_data_management:log:$LOG_PATH/loader_test_data_management.log.$CURDATE
# @loader_test_data_management:log:$LOG_PATH/loader_test_data_management.full.log.$CURDATE
loader_test_data_management              arg   loader_test_data_management
loader_test_data_management              arg   --daemon=60
loader_test_data_management              arg   --transaction-mode=c
#
# @api_art_notificationService:log:$LOG_PATH/WPSOCORE_MQ_Consumer_Activity_NotificationService/consumer.log.$CURDATE
# @api_art_notificationService:log:$LOG_PATH/WPSOCORE_MQ_Consumer_Activity_NotificationService/ramq.log.$CURDATE
# @api_art_notificationService:log:$LOG_PATH/WPSOCORE_MQ_Consumer_Activity_NotificationService/ramq_full.log.$CURDATE
api_art_notificationService          arg   ramq
api_art_notificationService          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
api_art_notificationService          arg   --daemon=30
api_art_notificationService          arg   --transaction-mode=c
api_art_notificationService          arg   ${ETC}/notification_service_ramq.conf
#
# @preemptive_maintenance_works_notify_open:log:$LOG_PATH/PreemptiveMaintenanceWorksNotifyOpen/consumer.log.$CURDATE
# @preemptive_maintenance_works_notify_open:log:$LOG_PATH/PreemptiveMaintenanceWorksNotifyOpen/ramq.log.$CURDATE
# @preemptive_maintenance_works_notify_open:log:$LOG_PATH/PreemptiveMaintenanceWorksNotifyOpen/ramq_full.log.$CURDATE
preemptive_maintenance_works_notify_open          arg   ramq
preemptive_maintenance_works_notify_open          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
preemptive_maintenance_works_notify_open          arg   --daemon=3
preemptive_maintenance_works_notify_open          arg   --transaction-mode=c
preemptive_maintenance_works_notify_open          arg   ${ETC}/ramq/MANUTENZIONE_PREVENTIVA/PreemptiveMaintenanceWorksNotifyOpen.conf
#
# @preemptive_maintenance_works_notify_close:log:$LOG_PATH/PreemptiveMaintenanceWorksNotifyClose/consumer.log.$CURDATE
# @preemptive_maintenance_works_notify_close:log:$LOG_PATH/PreemptiveMaintenanceWorksNotifyClose/ramq.log.$CURDATE
# @preemptive_maintenance_works_notify_close:log:$LOG_PATH/PreemptiveMaintenanceWorksNotifyClose/ramq_full.log.$CURDATE
preemptive_maintenance_works_notify_close          arg   ramq
preemptive_maintenance_works_notify_close          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
preemptive_maintenance_works_notify_close          arg   --daemon=3
preemptive_maintenance_works_notify_close          arg   --transaction-mode=c
preemptive_maintenance_works_notify_close          arg   ${ETC}/ramq/MANUTENZIONE_PREVENTIVA/PreemptiveMaintenanceWorksNotifyClose.conf
#
# @corrective_maintenance_works_notify_open:log:$LOG_PATH/CorrectiveMaintenanceWorksNotifyOpen/consumer.log.$CURDATE
# @corrective_maintenance_works_notify_open:log:$LOG_PATH/CorrectiveMaintenanceWorksNotifyOpen/ramq.log.$CURDATE
# @corrective_maintenance_works_notify_open:log:$LOG_PATH/CorrectiveMaintenanceWorksNotifyOpen/ramq_full.log.$CURDATE
corrective_maintenance_works_notify_open          arg   ramq
corrective_maintenance_works_notify_open          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
corrective_maintenance_works_notify_open          arg   --daemon=3
corrective_maintenance_works_notify_open          arg   --transaction-mode=c
corrective_maintenance_works_notify_open          arg   ${ETC}/ramq/MANUTENZIONE_CORRETTIVA/CorrectiveMaintenanceWorksNotifyOpen.conf
#
# @corrective_maintenance_works_notify_close:log:$LOG_PATH/CorrectiveMaintenanceWorksNotifyClose/consumer.log.$CURDATE
# @corrective_maintenance_works_notify_close:log:$LOG_PATH/CorrectiveMaintenanceWorksNotifyClose/ramq.log.$CURDATE
# @corrective_maintenance_works_notify_close:log:$LOG_PATH/CorrectiveMaintenanceWorksNotifyClose/ramq_full.log.$CURDATE
corrective_maintenance_works_notify_close          arg   ramq
corrective_maintenance_works_notify_close          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
corrective_maintenance_works_notify_close          arg   --daemon=3
corrective_maintenance_works_notify_close          arg   --transaction-mode=c
corrective_maintenance_works_notify_close          arg   ${ETC}/ramq/MANUTENZIONE_CORRETTIVA/CorrectiveMaintenanceWorksNotifyClose.conf
#
# @extraordinary_maintenance_works_notify_open:log:$LOG_PATH/ExtraordinaryMaintenanceWorksNotifyOpen/consumer.log.$CURDATE
# @extraordinary_maintenance_works_notify_open:log:$LOG_PATH/ExtraordinaryMaintenanceWorksNotifyOpen/ramq.log.$CURDATE
# @extraordinary_maintenance_works_notify_open:log:$LOG_PATH/ExtraordinaryMaintenanceWorksNotifyOpen/ramq_full.log.$CURDATE
extraordinary_maintenance_works_notify_open          arg   ramq
extraordinary_maintenance_works_notify_open          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
extraordinary_maintenance_works_notify_open          arg   --daemon=3
extraordinary_maintenance_works_notify_open          arg   --transaction-mode=c
extraordinary_maintenance_works_notify_open          arg   ${ETC}/ramq/MANUTENZIONE_STRAORDINARIA/ExtraordinaryMaintenanceWorksNotifyOpen.conf
#
# @extraordinary_maintenance_works_notify_close:log:$LOG_PATH/ExtraordinaryMaintenanceWorksNotifyClose/consumer.log.$CURDATE
# @extraordinary_maintenance_works_notify_close:log:$LOG_PATH/ExtraordinaryMaintenanceWorksNotifyClose/ramq.log.$CURDATE
# @extraordinary_maintenance_works_notify_close:log:$LOG_PATH/ExtraordinaryMaintenanceWorksNotifyClose/ramq_full.log.$CURDATE
extraordinary_maintenance_works_notify_close          arg   ramq
extraordinary_maintenance_works_notify_close          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
extraordinary_maintenance_works_notify_close          arg   --daemon=3
extraordinary_maintenance_works_notify_close          arg   --transaction-mode=c
extraordinary_maintenance_works_notify_close          arg   ${ETC}/ramq/MANUTENZIONE_STRAORDINARIA/ExtraordinaryMaintenanceWorksNotifyClose.conf
#
# @external_sync:log:$LOG_PATH/ExternalSync/consumer.log.$CURDATE
# @external_sync:log:$LOG_PATH/ExternalSync/ramq.log.$CURDATE
# @external_sync:log:$LOG_PATH/ExternalSync/ramq_full.log.$CURDATE
external_sync          arg   ramq
external_sync          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
external_sync          arg   --daemon=300
external_sync          arg   --transaction-mode=c
external_sync          arg   ${ETC}/ramq/ExternalSync.conf
#
#
# @external_sync_ack_handler:log:$LOG_PATH/external_sync_ack_handler.log.$CURDATE
# @external_sync_ack_handler:log:$LOG_PATH/external_sync_ack_handler.full.log.$CURDATE
external_sync_ack_handler              arg   external_sync_ack_handler
external_sync_ack_handler              arg   --daemon=300
external_sync_ack_handler              arg   --transaction-mode=c
#
# @ods_management:log:$LOG_PATH/ods_management.log.$CURDATE
# @ods_management:log:$LOG_PATH/ods_management.full.log.$CURDATE
ods_management              arg   ods_management
ods_management              arg   --daemon=900
ods_management              arg   --transaction-mode=c
#
# @ods_linked_management:log:$LOG_PATH/ods_linked_management.log.$CURDATE
# @ods_linked_management:log:$LOG_PATH/ods_linked_management.full.log.$CURDATE
ods_linked_management              arg   ods_linked_management
ods_linked_management              arg   --daemon=900
ods_linked_management              arg   --transaction-mode=c
#
# @service_management:log:$LOG_PATH/service_management.log.$CURDATE
# @service_management:log:$LOG_PATH/service_management.full.log.$CURDATE
service_management              arg   service_management
service_management              arg   --daemon=60
service_management              arg   --transaction-mode=c
#
# @api_art_emailservice:log:$LOG_PATH/API_ART_EmailService/consumer.log.$CURDATE
# @api_art_emailservice:log:$LOG_PATH/API_ART_EmailService/ramq.log.$CURDATE
# @api_art_emailservice:log:$LOG_PATH/API_ART_EmailService/ramq_full.log.$CURDATE
api_art_emailservice          arg   ramq
api_art_emailservice          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
api_art_emailservice          arg   --daemon=30
api_art_emailservice          arg   --transaction-mode=c
api_art_emailservice          arg   ${ETC}/email_service_ramq.conf
#
# @api_art_activity_system_property_counter:log:$LOG_PATH/API_ART_System_PropertyCounter/consumer.log.$CURDATE
# @api_art_activity_system_property_counter:log:$LOG_PATH/API_ART_System_PropertyCounter/ramq.log.$CURDATE
# @api_art_activity_system_property_counter:log:$LOG_PATH/API_ART_System_PropertyCounter/ramq_full.log.$CURDATE
api_art_activity_system_property_counter          arg   ramq
api_art_activity_system_property_counter          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
api_art_activity_system_property_counter          arg   --daemon=300
api_art_activity_system_property_counter          arg   --transaction-mode=c
api_art_activity_system_property_counter          arg   ${ETC}/activity_system_property_counter.conf
#
#
# @roe_recupera_coordinate:log:$LOG_PATH/roe_recupera_coordinate.log.$CURDATE
# @roe_recupera_coordinate:log:$LOG_PATH/roe_recupera_coordinate.full.log.$CURDATE
roe_recupera_coordinate              arg   roe_recupera_coordinate
roe_recupera_coordinate              arg   --daemon=1800
roe_recupera_coordinate              arg   --transaction-mode=c
#
#
# @pte_works_notify_open:log:$LOG_PATH/PTEWorksNotifyOpen/consumer.log.$CURDATE
# @pte_works_notify_open:log:$LOG_PATH/PTEWorksNotifyOpen/ramq.log.$CURDATE
# @pte_works_notify_open:log:$LOG_PATH/PTEWorksNotifyOpen/ramq_full.log.$CURDATE
pte_works_notify_open          arg   ramq
pte_works_notify_open          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
pte_works_notify_open          arg   --daemon=3
pte_works_notify_open          arg   --transaction-mode=c
pte_works_notify_open          arg   ${ETC}/ramq/LC02/PTEWorksNotifyOpen.conf
#
# @pte_works_notify_close:log:$LOG_PATH/PTEWorksNotifyClose/consumer.log.$CURDATE
# @pte_works_notify_close:log:$LOG_PATH/PTEWorksNotifyClose/ramq.log.$CURDATE
# @pte_works_notify_close:log:$LOG_PATH/PTEWorksNotifyClose/ramq_full.log.$CURDATE
pte_works_notify_close          arg   ramq
pte_works_notify_close          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
pte_works_notify_close          arg   --daemon=3
pte_works_notify_close          arg   --transaction-mode=c
pte_works_notify_close          arg   ${ETC}/ramq/LC02/PTEWorksNotifyClose.conf
#
# @pte_private_permits_notify_open:log:$LOG_PATH/PTEPrivatePermitsNotifyOpen/consumer.log.$CURDATE
# @pte_private_permits_notify_open:log:$LOG_PATH/PTEPrivatePermitsNotifyOpen/ramq.log.$CURDATE
# @pte_private_permits_notify_open:log:$LOG_PATH/PTEPrivatePermitsNotifyOpen/ramq_full.log.$CURDATE
pte_private_permits_notify_open          arg   ramq
pte_private_permits_notify_open          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
pte_private_permits_notify_open          arg   --daemon=3
pte_private_permits_notify_open          arg   --transaction-mode=c
pte_private_permits_notify_open          arg   ${ETC}/ramq/LC02/PTEPrivatePermitsNotifyOpen.conf
#
# @pte_private_permits_notify_close:log:$LOG_PATH/PTEPrivatePermitsNotifyClose/consumer.log.$CURDATE
# @pte_private_permits_notify_close:log:$LOG_PATH/PTEPrivatePermitsNotifyClose/ramq.log.$CURDATE
# @pte_private_permits_notify_close:log:$LOG_PATH/PTEPrivatePermitsNotifyClose/ramq_full.log.$CURDATE
pte_private_permits_notify_close          arg   ramq
pte_private_permits_notify_close          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
pte_private_permits_notify_close          arg   --daemon=3
pte_private_permits_notify_close          arg   --transaction-mode=c
pte_private_permits_notify_close          arg   ${ETC}/ramq/LC02/PTEPrivatePermitsNotifyClose.conf
#
# @fibercop_notifiche:log:$LOG_PATH/fibercop_notifiche.log.$CURDATE
# @fibercop_notifiche:log:$LOG_PATH/fibercop_notifiche.full.log.$CURDATE
fibercop_notifiche          arg   fibercop_notifiche
fibercop_notifiche          arg   --daemon=150
fibercop_notifiche          arg   --transaction-mode=c
#
# @api_art_activity_source_external_sync:log:$LOG_PATH/api-art-activity-source-EXTERNAL_SYNC.log.$CURDATE
# @api_art_activity_source_external_sync:log:$LOG_PATH/api-art-activity-source-EXTERNAL_SYNC_full.log.$CURDATE
api_art_activity_source_external_sync   arg   api-art-activity-source
api_art_activity_source_external_sync   arg   --daemon=1
api_art_activity_source_external_sync   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_external_sync   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_external_sync   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_external_sync   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_external_sync   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_external_sync   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_external_sync   arg   --log-idle=100
api_art_activity_source_external_sync   arg   --log-level=INFO
api_art_activity_source_external_sync   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_external_sync   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_external_sync   arg   --activity-type-name=EXTERNAL_SYNC
#
# @api_art_activity_source_lc01:log:$LOG_PATH/api-art-activity-source-LC01.log.$CURDATE
# @api_art_activity_source_lc01:log:$LOG_PATH/api-art-activity-source-LC01_full.log.$CURDATE
api_art_activity_source_lc01   arg   api-art-activity-source
api_art_activity_source_lc01   arg   --daemon=1
api_art_activity_source_lc01   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_lc01   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_lc01   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_lc01   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_lc01   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_lc01   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_lc01   arg   --log-idle=100
api_art_activity_source_lc01   arg   --log-level=INFO
api_art_activity_source_lc01   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_lc01   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_lc01   arg   --activity-type-name=LC01
#
# @api_art_activity_source_lc02:log:$LOG_PATH/api-art-activity-source-LC02.log.$CURDATE
# @api_art_activity_source_lc02:log:$LOG_PATH/api-art-activity-source-LC02_full.log.$CURDATE
api_art_activity_source_lc02   arg   api-art-activity-source
api_art_activity_source_lc02   arg   --daemon=1
api_art_activity_source_lc02   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_lc02   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_lc02   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_lc02   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_lc02   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_lc02   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_lc02   arg   --log-idle=100
api_art_activity_source_lc02   arg   --log-level=INFO
api_art_activity_source_lc02   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_lc02   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_lc02   arg   --activity-type-name=LC02
#
# @api_art_activity_source_lc03:log:$LOG_PATH/api-art-activity-source-LC03.log.$CURDATE
# @api_art_activity_source_lc03:log:$LOG_PATH/api-art-activity-source-LC03_full.log.$CURDATE
api_art_activity_source_lc03   arg   api-art-activity-source
api_art_activity_source_lc03   arg   --daemon=1
api_art_activity_source_lc03   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_lc03   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_lc03   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_lc03   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_lc03   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_lc03   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_lc03   arg   --log-idle=100
api_art_activity_source_lc03   arg   --log-level=INFO
api_art_activity_source_lc03   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_lc03   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_lc03   arg   --activity-type-name=LC03
#
# @api_art_activity_source_lc04:log:$LOG_PATH/api-art-activity-source-LC04.log.$CURDATE
# @api_art_activity_source_lc04:log:$LOG_PATH/api-art-activity-source-LC04_full.log.$CURDATE
api_art_activity_source_lc04   arg   api-art-activity-source
api_art_activity_source_lc04   arg   --daemon=1
api_art_activity_source_lc04   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_lc04   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_lc04   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_lc04   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_lc04   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_lc04   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_lc04   arg   --log-idle=100
api_art_activity_source_lc04   arg   --log-level=INFO
api_art_activity_source_lc04   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_lc04   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_lc04   arg   --activity-type-name=LC04
#
# @api_art_activity_source_lc05:log:$LOG_PATH/api-art-activity-source-LC05.log.$CURDATE
# @api_art_activity_source_lc05:log:$LOG_PATH/api-art-activity-source-LC05_full.log.$CURDATE
api_art_activity_source_lc05   arg   api-art-activity-source
api_art_activity_source_lc05   arg   --daemon=1
api_art_activity_source_lc05   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_lc05   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_lc05   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_lc05   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_lc05   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_lc05   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_lc05   arg   --log-idle=100
api_art_activity_source_lc05   arg   --log-level=INFO
api_art_activity_source_lc05   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_lc05   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_lc05   arg   --activity-type-name=LC05
#
# @api_art_activity_source_loader:log:$LOG_PATH/api-art-activity-source-LOADER.log.$CURDATE
# @api_art_activity_source_loader:log:$LOG_PATH/api-art-activity-source-LOADER_full.log.$CURDATE
api_art_activity_source_loader   arg   api-art-activity-source
api_art_activity_source_loader   arg   --daemon=1
api_art_activity_source_loader   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_loader   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_loader   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_loader   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_loader   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_loader   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_loader   arg   --log-idle=100
api_art_activity_source_loader   arg   --log-level=INFO
api_art_activity_source_loader   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_loader   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_loader   arg   --activity-type-name=LOADER
#
# @api_art_activity_source_loader_dati_collaudo:log:$LOG_PATH/api-art-activity-source-LOADER_DATI_COLLAUDO.log.$CURDATE
# @api_art_activity_source_loader_dati_collaudo:log:$LOG_PATH/api-art-activity-source-LOADER_DATI_COLLAUDO_full.log.$CURDATE
api_art_activity_source_loader_dati_collaudo   arg   api-art-activity-source
api_art_activity_source_loader_dati_collaudo   arg   --daemon=1
api_art_activity_source_loader_dati_collaudo   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_loader_dati_collaudo   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_loader_dati_collaudo   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_loader_dati_collaudo   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_loader_dati_collaudo   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_loader_dati_collaudo   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_loader_dati_collaudo   arg   --log-idle=100
api_art_activity_source_loader_dati_collaudo   arg   --log-level=INFO
api_art_activity_source_loader_dati_collaudo   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_loader_dati_collaudo   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_loader_dati_collaudo   arg   --activity-type-name=LOADER_DATI_COLLAUDO
#
# @api_art_activity_source_manutenzione_correttiva:log:$LOG_PATH/api-art-activity-source-MANUTENZIONE_CORRETTIVA.log.$CURDATE
# @api_art_activity_source_manutenzione_correttiva:log:$LOG_PATH/api-art-activity-source-MANUTENZIONE_CORRETTIVA_full.log.$CURDATE
api_art_activity_source_manutenzione_correttiva   arg   api-art-activity-source
api_art_activity_source_manutenzione_correttiva   arg   --daemon=1
api_art_activity_source_manutenzione_correttiva   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_manutenzione_correttiva   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_manutenzione_correttiva   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_manutenzione_correttiva   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_manutenzione_correttiva   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_manutenzione_correttiva   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_manutenzione_correttiva   arg   --log-idle=100
api_art_activity_source_manutenzione_correttiva   arg   --log-level=INFO
api_art_activity_source_manutenzione_correttiva   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_manutenzione_correttiva   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_manutenzione_correttiva   arg   --activity-type-name=MANUTENZIONE_CORRETTIVA
#
# @api_art_activity_source_manutenzione_correttiva_giunzione:log:$LOG_PATH/api-art-activity-source-MANUTENZIONE_CORRETTIVA_GIUNZIONE.log.$CURDATE
# @api_art_activity_source_manutenzione_correttiva_giunzione:log:$LOG_PATH/api-art-activity-source-MANUTENZIONE_CORRETTIVA_GIUNZIONE_full.log.$CURDATE
api_art_activity_source_manutenzione_correttiva_giunzione   arg   api-art-activity-source
api_art_activity_source_manutenzione_correttiva_giunzione   arg   --daemon=1
api_art_activity_source_manutenzione_correttiva_giunzione   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_manutenzione_correttiva_giunzione   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_manutenzione_correttiva_giunzione   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_manutenzione_correttiva_giunzione   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_manutenzione_correttiva_giunzione   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_manutenzione_correttiva_giunzione   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_manutenzione_correttiva_giunzione   arg   --log-idle=100
api_art_activity_source_manutenzione_correttiva_giunzione   arg   --log-level=INFO
api_art_activity_source_manutenzione_correttiva_giunzione   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_manutenzione_correttiva_giunzione   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_manutenzione_correttiva_giunzione   arg   --activity-type-name=MANUTENZIONE_CORRETTIVA_GIUNZIONE
#
# @api_art_activity_source_manutenzione_preventiva:log:$LOG_PATH/api-art-activity-source-MANUTENZIONE_PREVENTIVA.log.$CURDATE
# @api_art_activity_source_manutenzione_preventiva:log:$LOG_PATH/api-art-activity-source-MANUTENZIONE_PREVENTIVA.log.$CURDATE
api_art_activity_source_manutenzione_preventiva   arg   api-art-activity-source
api_art_activity_source_manutenzione_preventiva   arg   --daemon=1
api_art_activity_source_manutenzione_preventiva   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_manutenzione_preventiva   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_manutenzione_preventiva   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_manutenzione_preventiva   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_manutenzione_preventiva   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_manutenzione_preventiva   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_manutenzione_preventiva   arg   --log-idle=100
api_art_activity_source_manutenzione_preventiva   arg   --log-level=INFO
api_art_activity_source_manutenzione_preventiva   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_manutenzione_preventiva   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_manutenzione_preventiva   arg   --activity-type-name=MANUTENZIONE_PREVENTIVA
#
# @api_art_activity_source_manutenzione_straordinaria:log:$LOG_PATH/api-art-activity-source-MANUTENZIONE_STRAORDINARIA.log.$CURDATE
# @api_art_activity_source_manutenzione_straordinaria:log:$LOG_PATH/api-art-activity-source-MANUTENZIONE_STRAORDINARIA.log.$CURDATE
api_art_activity_source_manutenzione_straordinaria   arg   api-art-activity-source
api_art_activity_source_manutenzione_straordinaria   arg   --daemon=1
api_art_activity_source_manutenzione_straordinaria   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_manutenzione_straordinaria   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_manutenzione_straordinaria   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_manutenzione_straordinaria   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_manutenzione_straordinaria   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_manutenzione_straordinaria   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_manutenzione_straordinaria   arg   --log-idle=100
api_art_activity_source_manutenzione_straordinaria   arg   --log-level=INFO
api_art_activity_source_manutenzione_straordinaria   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_manutenzione_straordinaria   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_manutenzione_straordinaria   arg   --activity-type-name=MANUTENZIONE_STRAORDINARIA
#
# @api_art_activity_source_network:log:$LOG_PATH/api-art-activity-source-NETWORK.log.$CURDATE
# @api_art_activity_source_network:log:$LOG_PATH/api-art-activity-source-NETWORK.log.$CURDATE
api_art_activity_source_network   arg   api-art-activity-source
api_art_activity_source_network   arg   --daemon=1
api_art_activity_source_network   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_network   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_network   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_network   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_network   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_network   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_network   arg   --log-idle=100
api_art_activity_source_network   arg   --log-level=INFO
api_art_activity_source_network   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_network   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_network   arg   --activity-type-name=NETWORK
#
# @api_art_activity_source_rapporto_lavori:log:$LOG_PATH/api-art-activity-source-RAPPORTO_LAVORI.log.$CURDATE
# @api_art_activity_source_rapporto_lavori:log:$LOG_PATH/api-art-activity-source-RAPPORTO_LAVORI.log.$CURDATE
api_art_activity_source_rapporto_lavori   arg   api-art-activity-source
api_art_activity_source_rapporto_lavori   arg   --daemon=1
api_art_activity_source_rapporto_lavori   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_rapporto_lavori   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_rapporto_lavori   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_rapporto_lavori   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_rapporto_lavori   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_rapporto_lavori   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_rapporto_lavori   arg   --log-idle=100
api_art_activity_source_rapporto_lavori   arg   --log-level=INFO
api_art_activity_source_rapporto_lavori   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_rapporto_lavori   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_rapporto_lavori   arg   --activity-type-name=RAPPORTO_LAVORI
#
# @api_art_activity_source_rapporto_lavori_parziale:log:$LOG_PATH/api-art-activity-source-RAPPORTO_LAVORI_PARZIALE.log.$CURDATE
# @api_art_activity_source_rapporto_lavori_parziale:log:$LOG_PATH/api-art-activity-source-RAPPORTO_LAVORI_PARZIALE.log.$CURDATE
api_art_activity_source_rapporto_lavori_parziale   arg   api-art-activity-source
api_art_activity_source_rapporto_lavori_parziale   arg   --daemon=1
api_art_activity_source_rapporto_lavori_parziale   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_rapporto_lavori_parziale   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_rapporto_lavori_parziale   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_rapporto_lavori_parziale   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_rapporto_lavori_parziale   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_rapporto_lavori_parziale   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_rapporto_lavori_parziale   arg   --log-idle=100
api_art_activity_source_rapporto_lavori_parziale   arg   --log-level=INFO
api_art_activity_source_rapporto_lavori_parziale   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_rapporto_lavori_parziale   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_rapporto_lavori_parziale   arg   --activity-type-name=RAPPORTO_LAVORI_PARZIALE
#
# @api_art_activity_source_roe:log:$LOG_PATH/api-art-activity-source-ROE.log.$CURDATE
# @api_art_activity_source_roe:log:$LOG_PATH/api-art-activity-source-ROE.log.$CURDATE
api_art_activity_source_roe   arg   api-art-activity-source
api_art_activity_source_roe   arg   --daemon=1
api_art_activity_source_roe   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_roe   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_roe   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_roe   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_roe   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_roe   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_roe   arg   --log-idle=100
api_art_activity_source_roe   arg   --log-level=INFO
api_art_activity_source_roe   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_roe   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_roe   arg   --activity-type-name=ROE
#
# @api_art_activity_source_tt:log:$LOG_PATH/api-art-activity-source-TT.log.$CURDATE
# @api_art_activity_source_tt:log:$LOG_PATH/api-art-activity-source-TT.log.$CURDATE
api_art_activity_source_tt   arg   api-art-activity-source
api_art_activity_source_tt   arg   --daemon=1
api_art_activity_source_tt   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_tt   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_tt   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_tt   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_tt   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_tt   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_tt   arg   --log-idle=100
api_art_activity_source_tt   arg   --log-level=INFO
api_art_activity_source_tt   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_tt   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_tt   arg   --activity-type-name=TT
#
# @api_art_activity_source_work_order:log:$LOG_PATH/api-art-activity-source-WORK_ORDER.log.$CURDATE
# @api_art_activity_source_work_order:log:$LOG_PATH/api-art-activity-source-WORK_ORDER.log.$CURDATE
api_art_activity_source_work_order   arg   api-art-activity-source
api_art_activity_source_work_order   arg   --daemon=1
api_art_activity_source_work_order   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_work_order   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_work_order   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_work_order   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_work_order   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_work_order   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_work_order   arg   --log-idle=100
api_art_activity_source_work_order   arg   --log-level=INFO
api_art_activity_source_work_order   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_work_order   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_work_order   arg   --activity-type-name=WORK_ORDER
#
# @api_art_activity_source_manutenzione_preventiva_01:log:$LOG_PATH/api-art-activity-source-MANUTENZIONE_PREVENTIVA_01.log.$CURDATE
# @api_art_activity_source_manutenzione_preventiva_01:log:$LOG_PATH/api-art-activity-source-MANUTENZIONE_PREVENTIVA_01.log.$CURDATE
api_art_activity_source_manutenzione_preventiva_01   arg   api-art-activity-source
api_art_activity_source_manutenzione_preventiva_01   arg   --daemon=1
api_art_activity_source_manutenzione_preventiva_01   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_manutenzione_preventiva_01   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_manutenzione_preventiva_01   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_manutenzione_preventiva_01   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_manutenzione_preventiva_01   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_manutenzione_preventiva_01   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_manutenzione_preventiva_01   arg   --log-idle=100
api_art_activity_source_manutenzione_preventiva_01   arg   --log-level=INFO
api_art_activity_source_manutenzione_preventiva_01   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_manutenzione_preventiva_01   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_manutenzione_preventiva_01   arg   --activity-type-name=MANUTENZIONE_PREVENTIVA_01
#
# @api_art_activity_source_manutenzione_straordinaria_01:log:$LOG_PATH/api-art-activity-source-MANUTENZIONE_STRAORDINARIA_01.log.$CURDATE
# @api_art_activity_source_manutenzione_straordinaria_01:log:$LOG_PATH/api-art-activity-source-MANUTENZIONE_STRAORDINARIA_01.log.$CURDATE
api_art_activity_source_manutenzione_straordinaria_01   arg   api-art-activity-source
api_art_activity_source_manutenzione_straordinaria_01   arg   --daemon=1
api_art_activity_source_manutenzione_straordinaria_01   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_manutenzione_straordinaria_01   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_manutenzione_straordinaria_01   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_manutenzione_straordinaria_01   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_manutenzione_straordinaria_01   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_manutenzione_straordinaria_01   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_manutenzione_straordinaria_01   arg   --log-idle=100
api_art_activity_source_manutenzione_straordinaria_01   arg   --log-level=INFO
api_art_activity_source_manutenzione_straordinaria_01   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_manutenzione_straordinaria_01   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_manutenzione_straordinaria_01   arg   --activity-type-name=MANUTENZIONE_STRAORDINARIA_01
#
# @api_art_activity_source_wo001:log:$LOG_PATH/api-art-activity-source-WO001.log.$CURDATE
# @api_art_activity_source_wo001:log:$LOG_PATH/api-art-activity-source-WO001.log.$CURDATE
api_art_activity_source_wo001   arg   api-art-activity-source
api_art_activity_source_wo001   arg   --daemon=1
api_art_activity_source_wo001   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_wo001   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_wo001   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_wo001   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_wo001   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_wo001   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_wo001   arg   --log-idle=100
api_art_activity_source_wo001   arg   --log-level=INFO
api_art_activity_source_wo001   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_wo001   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_wo001   arg   --activity-type-name=WO001
#
# @api_art_activity_source_wo002:log:$LOG_PATH/api-art-activity-source-WO002.log.$CURDATE
# @api_art_activity_source_wo002:log:$LOG_PATH/api-art-activity-source-WO002.log.$CURDATE
api_art_activity_source_wo002   arg   api-art-activity-source
api_art_activity_source_wo002   arg   --daemon=1
api_art_activity_source_wo002   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_wo002   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_wo002   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_wo002   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_wo002   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_wo002   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_wo002   arg   --log-idle=100
api_art_activity_source_wo002   arg   --log-level=INFO
api_art_activity_source_wo002   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_wo002   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_wo002   arg   --activity-type-name=WO002
#
# @api_art_activity_stream_external_sync:log:$LOG_PATH/api-art-activity-stream-external-sync.log.$CURDATE
# @api_art_activity_stream_external_sync:log:$LOG_PATH/api-art-activity-stream-external-sync_full.log.$CURDATE
api_art_activity_stream_external_sync   arg   api-art-activity-stream-external-sync
api_art_activity_stream_external_sync   arg   --daemon=1
api_art_activity_stream_external_sync   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_external_sync   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_stream_external_sync   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_stream_external_sync   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_stream_external_sync   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_external_sync   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_external_sync   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_external_sync   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_external_sync   arg   --exclude-history
api_art_activity_stream_external_sync   arg   --log-idle=100
api_art_activity_stream_external_sync   arg   --log-level=INFO
api_art_activity_stream_external_sync   arg   --log-config=${ETC}/api-art-activity-stream-external-sync.log4perl.conf
api_art_activity_stream_external_sync   arg   --client-id=${ART_INSTANCE_NAME}-external-sync
api_art_activity_stream_external_sync   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-EXTERNAL_SYNC
api_art_activity_stream_external_sync   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_lc01:log:$LOG_PATH/api-art-activity-stream-lc01.log.$CURDATE
# @api_art_activity_stream_lc01:log:$LOG_PATH/api-art-activity-stream-lc01_full.log.$CURDATE
api_art_activity_stream_lc01   arg   api-art-activity-stream-lc01
api_art_activity_stream_lc01   arg   --daemon=1
api_art_activity_stream_lc01   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_lc01   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_stream_lc01   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_stream_lc01   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_stream_lc01   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_lc01   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_lc01   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_lc01   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_lc01   arg   --exclude-history
api_art_activity_stream_lc01   arg   --log-idle=100
api_art_activity_stream_lc01   arg   --log-level=INFO
api_art_activity_stream_lc01   arg   --log-config=${ETC}/api-art-activity-stream-lc01.log4perl.conf
api_art_activity_stream_lc01   arg   --client-id=${ART_INSTANCE_NAME}-lc01
api_art_activity_stream_lc01   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-LC01
api_art_activity_stream_lc01   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_lc02:log:$LOG_PATH/api-art-activity-stream-lc02.log.$CURDATE
# @api_art_activity_stream_lc02:log:$LOG_PATH/api-art-activity-stream-lc02_full.log.$CURDATE
api_art_activity_stream_lc02   arg   api-art-activity-stream-lc02
api_art_activity_stream_lc02   arg   --daemon=1
api_art_activity_stream_lc02   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_lc02   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_stream_lc02   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_stream_lc02   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_stream_lc02   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_lc02   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_lc02   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_lc02   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_lc02   arg   --exclude-history
api_art_activity_stream_lc02   arg   --log-idle=100
api_art_activity_stream_lc02   arg   --log-level=INFO
api_art_activity_stream_lc02   arg   --log-config=${ETC}/api-art-activity-stream-lc02.log4perl.conf
api_art_activity_stream_lc02   arg   --client-id=${ART_INSTANCE_NAME}-lc02
api_art_activity_stream_lc02   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-LC02
api_art_activity_stream_lc02   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_lc03:log:$LOG_PATH/api-art-activity-stream-lc03.log.$CURDATE
# @api_art_activity_stream_lc03:log:$LOG_PATH/api-art-activity-stream-lc03_full.log.$CURDATE
api_art_activity_stream_lc03   arg   api-art-activity-stream-lc03
api_art_activity_stream_lc03   arg   --daemon=1
api_art_activity_stream_lc03   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_lc03   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_stream_lc03   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_stream_lc03   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_stream_lc03   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_lc03   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_lc03   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_lc03   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_lc03   arg   --exclude-history
api_art_activity_stream_lc03   arg   --log-idle=100
api_art_activity_stream_lc03   arg   --log-level=INFO
api_art_activity_stream_lc03   arg   --log-config=${ETC}/api-art-activity-stream-lc03.log4perl.conf
api_art_activity_stream_lc03   arg   --client-id=${ART_INSTANCE_NAME}-lc03
api_art_activity_stream_lc03   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-LC03
api_art_activity_stream_lc03   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_lc04:log:$LOG_PATH/api-art-activity-stream-lc04.log.$CURDATE
# @api_art_activity_stream_lc04:log:$LOG_PATH/api-art-activity-stream-lc04_full.log.$CURDATE
api_art_activity_stream_lc04   arg   api-art-activity-stream-lc04
api_art_activity_stream_lc04   arg   --daemon=1
api_art_activity_stream_lc04   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_lc04   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_stream_lc04   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_stream_lc04   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_stream_lc04   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_lc04   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_lc04   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_lc04   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_lc04   arg   --exclude-history
api_art_activity_stream_lc04   arg   --log-idle=100
api_art_activity_stream_lc04   arg   --log-level=INFO
api_art_activity_stream_lc04   arg   --log-config=${ETC}/api-art-activity-stream-lc04.log4perl.conf
api_art_activity_stream_lc04   arg   --client-id=${ART_INSTANCE_NAME}-lc04
api_art_activity_stream_lc04   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-LC04
api_art_activity_stream_lc04   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_lc05:log:$LOG_PATH/api-art-activity-stream-lc05.log.$CURDATE
# @api_art_activity_stream_lc05:log:$LOG_PATH/api-art-activity-stream-lc05_full.log.$CURDATE
api_art_activity_stream_lc05   arg   api-art-activity-stream-lc05
api_art_activity_stream_lc05   arg   --daemon=1
api_art_activity_stream_lc05   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_lc05   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_stream_lc05   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_stream_lc05   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_stream_lc05   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_lc05   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_lc05   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_lc05   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_lc05   arg   --exclude-history
api_art_activity_stream_lc05   arg   --log-idle=100
api_art_activity_stream_lc05   arg   --log-level=INFO
api_art_activity_stream_lc05   arg   --log-config=${ETC}/api-art-activity-stream-lc05.log4perl.conf
api_art_activity_stream_lc05   arg   --client-id=${ART_INSTANCE_NAME}-lc05
api_art_activity_stream_lc05   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-LC05
api_art_activity_stream_lc05   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_loader:log:$LOG_PATH/api-art-activity-stream-loader.log.$CURDATE
# @api_art_activity_stream_loader:log:$LOG_PATH/api-art-activity-stream-loader_full.log.$CURDATE
api_art_activity_stream_loader   arg   api-art-activity-stream-loader
api_art_activity_stream_loader   arg   --daemon=1
api_art_activity_stream_loader   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_loader   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_stream_loader   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_stream_loader   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_stream_loader   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_loader   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_loader   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_loader   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_loader   arg   --exclude-history
api_art_activity_stream_loader   arg   --log-idle=100
api_art_activity_stream_loader   arg   --log-level=INFO
api_art_activity_stream_loader   arg   --log-config=${ETC}/api-art-activity-stream-loader.log4perl.conf
api_art_activity_stream_loader   arg   --client-id=${ART_INSTANCE_NAME}-loader
api_art_activity_stream_loader   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-LOADER
api_art_activity_stream_loader   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_loader_dati_collaudo:log:$LOG_PATH/api-art-activity-stream-loader-dati-collaudo.log.$CURDATE
# @api_art_activity_stream_loader_dati_collaudo:log:$LOG_PATH/api-art-activity-stream-loader-dati-collaudo_full.log.$CURDATE
api_art_activity_stream_loader_dati_collaudo   arg   api-art-activity-stream-loader-dati-collaudo
api_art_activity_stream_loader_dati_collaudo   arg   --daemon=1
api_art_activity_stream_loader_dati_collaudo   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_loader_dati_collaudo   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_stream_loader_dati_collaudo   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_stream_loader_dati_collaudo   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_stream_loader_dati_collaudo   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_loader_dati_collaudo   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_loader_dati_collaudo   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_loader_dati_collaudo   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_loader_dati_collaudo   arg   --exclude-history
api_art_activity_stream_loader_dati_collaudo   arg   --log-idle=100
api_art_activity_stream_loader_dati_collaudo   arg   --log-level=INFO
api_art_activity_stream_loader_dati_collaudo   arg   --log-config=${ETC}/api-art-activity-stream-loader-dati-collaudo.log4perl.conf
api_art_activity_stream_loader_dati_collaudo   arg   --client-id=${ART_INSTANCE_NAME}-loader-dati-collaudo
api_art_activity_stream_loader_dati_collaudo   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-LOADER_DATI_COLLAUDO
api_art_activity_stream_loader_dati_collaudo   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_manutenzione_correttiva:log:$LOG_PATH/api-art-activity-stream-manutenzione-correttiva.log.$CURDATE
# @api_art_activity_stream_manutenzione_correttiva:log:$LOG_PATH/api-art-activity-stream-manutenzione-correttiva_full.log.$CURDATE
api_art_activity_stream_manutenzione_correttiva   arg   api-art-activity-stream-manutenzione-correttiva
api_art_activity_stream_manutenzione_correttiva   arg   --daemon=1
api_art_activity_stream_manutenzione_correttiva   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_manutenzione_correttiva   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_stream_manutenzione_correttiva   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_stream_manutenzione_correttiva   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_stream_manutenzione_correttiva   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_manutenzione_correttiva   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_manutenzione_correttiva   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_manutenzione_correttiva   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_manutenzione_correttiva   arg   --exclude-history
api_art_activity_stream_manutenzione_correttiva   arg   --log-idle=100
api_art_activity_stream_manutenzione_correttiva   arg   --log-level=INFO
api_art_activity_stream_manutenzione_correttiva   arg   --log-config=${ETC}/api-art-activity-stream-manutenzione-correttiva.log4perl.conf
api_art_activity_stream_manutenzione_correttiva   arg   --client-id=${ART_INSTANCE_NAME}-manutenzione-correttiva
api_art_activity_stream_manutenzione_correttiva   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-MANUTENZIONE_CORRETTIVA
api_art_activity_stream_manutenzione_correttiva   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_manutenzione_correttiva_giunzione:log:$LOG_PATH/api-art-activity-stream-manutenzione-correttiva-giunzione.log.$CURDATE
# @api_art_activity_stream_manutenzione_correttiva_giunzione:log:$LOG_PATH/api-art-activity-stream-manutenzione-correttiva-giunzione_full.log.$CURDATE
api_art_activity_stream_manutenzione_correttiva_giunzione   arg   api-art-activity-stream-manutenzione-correttiva-giunzione
api_art_activity_stream_manutenzione_correttiva_giunzione   arg   --daemon=1
api_art_activity_stream_manutenzione_correttiva_giunzione   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_manutenzione_correttiva_giunzione   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_stream_manutenzione_correttiva_giunzione   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_stream_manutenzione_correttiva_giunzione   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_stream_manutenzione_correttiva_giunzione   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_manutenzione_correttiva_giunzione   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_manutenzione_correttiva_giunzione   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_manutenzione_correttiva_giunzione   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_manutenzione_correttiva_giunzione   arg   --exclude-history
api_art_activity_stream_manutenzione_correttiva_giunzione   arg   --log-idle=100
api_art_activity_stream_manutenzione_correttiva_giunzione   arg   --log-level=INFO
api_art_activity_stream_manutenzione_correttiva_giunzione   arg   --log-config=${ETC}/api-art-activity-stream-manutenzione-correttiva-giunzione.log4perl.conf
api_art_activity_stream_manutenzione_correttiva_giunzione   arg   --client-id=${ART_INSTANCE_NAME}-manutenzione-correttiva-giunzione
api_art_activity_stream_manutenzione_correttiva_giunzione   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-MANUTENZIONE_CORRETTIVA_GIUNZIONE
api_art_activity_stream_manutenzione_correttiva_giunzione   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_manutenzione_preventiva:log:$LOG_PATH/api-art-activity-stream-manutenzione-preventiva.log.$CURDATE
# @api_art_activity_stream_manutenzione_preventiva:log:$LOG_PATH/api-art-activity-stream-manutenzione-preventiva_full.log.$CURDATE
api_art_activity_stream_manutenzione_preventiva   arg   api-art-activity-stream-manutenzione-preventiva
api_art_activity_stream_manutenzione_preventiva   arg   --daemon=1
api_art_activity_stream_manutenzione_preventiva   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_manutenzione_preventiva   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_stream_manutenzione_preventiva   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_stream_manutenzione_preventiva   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_stream_manutenzione_preventiva   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_manutenzione_preventiva   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_manutenzione_preventiva   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_manutenzione_preventiva   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_manutenzione_preventiva   arg   --exclude-history
api_art_activity_stream_manutenzione_preventiva   arg   --log-idle=100
api_art_activity_stream_manutenzione_preventiva   arg   --log-level=INFO
api_art_activity_stream_manutenzione_preventiva   arg   --log-config=${ETC}/api-art-activity-stream-manutenzione-preventiva.log4perl.conf
api_art_activity_stream_manutenzione_preventiva   arg   --client-id=${ART_INSTANCE_NAME}-manutenzione-preventiva
api_art_activity_stream_manutenzione_preventiva   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-MANUTENZIONE_PREVENTIVA
api_art_activity_stream_manutenzione_preventiva   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_manutenzione_straordinaria:log:$LOG_PATH/api-art-activity-stream-manutenzione-straordinaria.log.$CURDATE
# @api_art_activity_stream_manutenzione_straordinaria:log:$LOG_PATH/api-art-activity-stream-manutenzione-straordinaria_full.log.$CURDATE
api_art_activity_stream_manutenzione_straordinaria   arg   api-art-activity-stream-manutenzione-straordinaria
api_art_activity_stream_manutenzione_straordinaria   arg   --daemon=1
api_art_activity_stream_manutenzione_straordinaria   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_manutenzione_straordinaria   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_stream_manutenzione_straordinaria   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_stream_manutenzione_straordinaria   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_stream_manutenzione_straordinaria   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_manutenzione_straordinaria   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_manutenzione_straordinaria   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_manutenzione_straordinaria   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_manutenzione_straordinaria   arg   --exclude-history
api_art_activity_stream_manutenzione_straordinaria   arg   --log-idle=100
api_art_activity_stream_manutenzione_straordinaria   arg   --log-level=INFO
api_art_activity_stream_manutenzione_straordinaria   arg   --log-config=${ETC}/api-art-activity-stream-manutenzione-straordinaria.log4perl.conf
api_art_activity_stream_manutenzione_straordinaria   arg   --client-id=${ART_INSTANCE_NAME}-manutenzione-straordinaria
api_art_activity_stream_manutenzione_straordinaria   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-MANUTENZIONE_STRAORDINARIA
api_art_activity_stream_manutenzione_straordinaria   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_network:log:$LOG_PATH/api-art-activity-stream-network.log.$CURDATE
# @api_art_activity_stream_network:log:$LOG_PATH/api-art-activity-stream-network_full.log.$CURDATE
api_art_activity_stream_network   arg   api-art-activity-stream-network
api_art_activity_stream_network   arg   --daemon=1
api_art_activity_stream_network   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_network   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_stream_network   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_stream_network   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_stream_network   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_network   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_network   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_network   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_network   arg   --exclude-history
api_art_activity_stream_network   arg   --log-idle=100
api_art_activity_stream_network   arg   --log-level=INFO
api_art_activity_stream_network   arg   --log-config=${ETC}/api-art-activity-stream-network.log4perl.conf
api_art_activity_stream_network   arg   --client-id=${ART_INSTANCE_NAME}-network
api_art_activity_stream_network   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-NETWORK
api_art_activity_stream_network   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_rapporto_lavori:log:$LOG_PATH/api-art-activity-stream-rapporto-lavori.log.$CURDATE
# @api_art_activity_stream_rapporto_lavori:log:$LOG_PATH/api-art-activity-stream-rapporto-lavori_full.log.$CURDATE
api_art_activity_stream_rapporto_lavori   arg   api-art-activity-stream-rapporto-lavori
api_art_activity_stream_rapporto_lavori   arg   --daemon=1
api_art_activity_stream_rapporto_lavori   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_rapporto_lavori   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_stream_rapporto_lavori   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_stream_rapporto_lavori   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_stream_rapporto_lavori   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_rapporto_lavori   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_rapporto_lavori   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_rapporto_lavori   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_rapporto_lavori   arg   --exclude-history
api_art_activity_stream_rapporto_lavori   arg   --log-idle=100
api_art_activity_stream_rapporto_lavori   arg   --log-level=INFO
api_art_activity_stream_rapporto_lavori   arg   --log-config=${ETC}/api-art-activity-stream-rapporto-lavori.log4perl.conf
api_art_activity_stream_rapporto_lavori   arg   --client-id=${ART_INSTANCE_NAME}-rapporto-lavori
api_art_activity_stream_rapporto_lavori   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-RAPPORTO_LAVORI
api_art_activity_stream_rapporto_lavori   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_rapporto_lavori_parziale:log:$LOG_PATH/api-art-activity-stream-rapporto-lavori-parziale.log.$CURDATE
# @api_art_activity_stream_rapporto_lavori_parziale:log:$LOG_PATH/api-art-activity-stream-rapporto-lavori-parziale_full.log.$CURDATE
api_art_activity_stream_rapporto_lavori_parziale   arg   api-art-activity-stream-rapporto-lavori-parziale
api_art_activity_stream_rapporto_lavori_parziale   arg   --daemon=1
api_art_activity_stream_rapporto_lavori_parziale   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_rapporto_lavori_parziale   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_stream_rapporto_lavori_parziale   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_stream_rapporto_lavori_parziale   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_stream_rapporto_lavori_parziale   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_rapporto_lavori_parziale   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_rapporto_lavori_parziale   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_rapporto_lavori_parziale   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_rapporto_lavori_parziale   arg   --exclude-history
api_art_activity_stream_rapporto_lavori_parziale   arg   --log-idle=100
api_art_activity_stream_rapporto_lavori_parziale   arg   --log-level=INFO
api_art_activity_stream_rapporto_lavori_parziale   arg   --log-config=${ETC}/api-art-activity-stream-rapporto-lavori-parziale.log4perl.conf
api_art_activity_stream_rapporto_lavori_parziale   arg   --client-id=${ART_INSTANCE_NAME}-rapporto-lavori-parziale
api_art_activity_stream_rapporto_lavori_parziale   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-RAPPORTO_LAVORI_PARZIALE
api_art_activity_stream_rapporto_lavori_parziale   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_roe:log:$LOG_PATH/api-art-activity-stream-roe.log.$CURDATE
# @api_art_activity_stream_roe:log:$LOG_PATH/api-art-activity-stream-roe_full.log.$CURDATE
api_art_activity_stream_roe   arg   api-art-activity-stream-roe
api_art_activity_stream_roe   arg   --daemon=1
api_art_activity_stream_roe   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_roe   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_stream_roe   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_stream_roe   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_stream_roe   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_roe   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_roe   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_roe   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_roe   arg   --exclude-history
api_art_activity_stream_roe   arg   --log-idle=100
api_art_activity_stream_roe   arg   --log-level=INFO
api_art_activity_stream_roe   arg   --log-config=${ETC}/api-art-activity-stream-roe.log4perl.conf
api_art_activity_stream_roe   arg   --client-id=${ART_INSTANCE_NAME}-roe
api_art_activity_stream_roe   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-ROE
api_art_activity_stream_roe   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_tt:log:$LOG_PATH/api-art-activity-stream-tt.log.$CURDATE
# @api_art_activity_stream_tt:log:$LOG_PATH/api-art-activity-stream-tt_full.log.$CURDATE
api_art_activity_stream_tt   arg   api-art-activity-stream-tt
api_art_activity_stream_tt   arg   --daemon=1
api_art_activity_stream_tt   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_tt   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_stream_tt   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_stream_tt   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_stream_tt   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_tt   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_tt   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_tt   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_tt   arg   --exclude-history
api_art_activity_stream_tt   arg   --log-idle=100
api_art_activity_stream_tt   arg   --log-level=INFO
api_art_activity_stream_tt   arg   --log-config=${ETC}/api-art-activity-stream-tt.log4perl.conf
api_art_activity_stream_tt   arg   --client-id=${ART_INSTANCE_NAME}-tt
api_art_activity_stream_tt   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-TT
api_art_activity_stream_tt   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_wo001:log:$LOG_PATH/api-art-activity-stream-wo001.log.$CURDATE
# @api_art_activity_stream_wo001:log:$LOG_PATH/api-art-activity-stream-wo001_full.log.$CURDATE
api_art_activity_stream_wo001   arg   api-art-activity-stream-wo001
api_art_activity_stream_wo001   arg   --daemon=1
api_art_activity_stream_wo001   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_wo001   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_stream_wo001   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_stream_wo001   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_stream_wo001   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_wo001   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_wo001   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_wo001   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_wo001   arg   --exclude-history
api_art_activity_stream_wo001   arg   --log-idle=100
api_art_activity_stream_wo001   arg   --log-level=INFO
api_art_activity_stream_wo001   arg   --log-config=${ETC}/api-art-activity-stream-wo001.log4perl.conf
api_art_activity_stream_wo001   arg   --client-id=${ART_INSTANCE_NAME}-wo001
api_art_activity_stream_wo001   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-WO001
api_art_activity_stream_wo001   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_wo002:log:$LOG_PATH/api-art-activity-stream-wo002.log.$CURDATE
# @api_art_activity_stream_wo002:log:$LOG_PATH/api-art-activity-stream-wo002_full.log.$CURDATE
api_art_activity_stream_wo002   arg   api-art-activity-stream-wo002
api_art_activity_stream_wo002   arg   --daemon=1
api_art_activity_stream_wo002   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_wo002   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_stream_wo002   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_stream_wo002   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_stream_wo002   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_wo002   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_wo002   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_wo002   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_wo002   arg   --exclude-history
api_art_activity_stream_wo002   arg   --log-idle=100
api_art_activity_stream_wo002   arg   --log-level=INFO
api_art_activity_stream_wo002   arg   --log-config=${ETC}/api-art-activity-stream-wo002.log4perl.conf
api_art_activity_stream_wo002   arg   --client-id=${ART_INSTANCE_NAME}-wo002
api_art_activity_stream_wo002   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-WO002
api_art_activity_stream_wo002   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_work_order:log:$LOG_PATH/api-art-activity-stream-work-order.log.$CURDATE
# @api_art_activity_stream_work_order:log:$LOG_PATH/api-art-activity-stream-work-order_full.log.$CURDATE
api_art_activity_stream_work_order   arg   api-art-activity-stream-work-order
api_art_activity_stream_work_order   arg   --daemon=1
api_art_activity_stream_work_order   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_work_order   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_stream_work_order   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_stream_work_order   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_stream_work_order   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_work_order   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_work_order   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_work_order   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_work_order   arg   --exclude-history
api_art_activity_stream_work_order   arg   --log-idle=100
api_art_activity_stream_work_order   arg   --log-level=INFO
api_art_activity_stream_work_order   arg   --log-config=${ETC}/api-art-activity-stream-work-order.log4perl.conf
api_art_activity_stream_work_order   arg   --client-id=${ART_INSTANCE_NAME}-work-order
api_art_activity_stream_work_order   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-WORK_ORDER
api_art_activity_stream_work_order   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_manutenzione_preventiva_01:log:$LOG_PATH/api-art-activity-stream-manutenzione-preventiva-01.log.$CURDATE
# @api_art_activity_stream_manutenzione_preventiva_01:log:$LOG_PATH/api-art-activity-stream-manutenzione-preventiva-01_full.log.$CURDATE
api_art_activity_stream_manutenzione_preventiva_01   arg   api-art-activity-stream-manutenzione-preventiva-01
api_art_activity_stream_manutenzione_preventiva_01   arg   --daemon=1
api_art_activity_stream_manutenzione_preventiva_01   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_manutenzione_preventiva_01   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_stream_manutenzione_preventiva_01   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_stream_manutenzione_preventiva_01   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_stream_manutenzione_preventiva_01   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_manutenzione_preventiva_01   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_manutenzione_preventiva_01   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_manutenzione_preventiva_01   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_manutenzione_preventiva_01   arg   --exclude-history
api_art_activity_stream_manutenzione_preventiva_01   arg   --log-idle=100
api_art_activity_stream_manutenzione_preventiva_01   arg   --log-level=INFO
api_art_activity_stream_manutenzione_preventiva_01   arg   --log-config=${ETC}/api-art-activity-stream-manutenzione-preventiva-01.log4perl.conf
api_art_activity_stream_manutenzione_preventiva_01   arg   --client-id=${ART_INSTANCE_NAME}-manutenzione-preventiva
api_art_activity_stream_manutenzione_preventiva_01   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-MANUTENZIONE_PREVENTIVA_01
api_art_activity_stream_manutenzione_preventiva_01   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_manutenzione_straordinaria_01:log:$LOG_PATH/api-art-activity-stream-manutenzione-straordinaria-01.log.$CURDATE
# @api_art_activity_stream_manutenzione_straordinaria_01:log:$LOG_PATH/api-art-activity-stream-manutenzione-straordinaria-01_full.log.$CURDATE
api_art_activity_stream_manutenzione_straordinaria_01   arg   api-art-activity-stream-manutenzione-straordinaria-01
api_art_activity_stream_manutenzione_straordinaria_01   arg   --daemon=1
api_art_activity_stream_manutenzione_straordinaria_01   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_manutenzione_straordinaria_01   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_stream_manutenzione_straordinaria_01   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_stream_manutenzione_straordinaria_01   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_stream_manutenzione_straordinaria_01   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_manutenzione_straordinaria_01   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_manutenzione_straordinaria_01   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_manutenzione_straordinaria_01   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_manutenzione_straordinaria_01   arg   --exclude-history
api_art_activity_stream_manutenzione_straordinaria_01   arg   --log-idle=100
api_art_activity_stream_manutenzione_straordinaria_01   arg   --log-level=INFO
api_art_activity_stream_manutenzione_straordinaria_01   arg   --log-config=${ETC}/api-art-activity-stream-manutenzione-straordinaria-01.log4perl.conf
api_art_activity_stream_manutenzione_straordinaria_01   arg   --client-id=${ART_INSTANCE_NAME}-manutenzione-straordinaria-01
api_art_activity_stream_manutenzione_straordinaria_01   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-MANUTENZIONE_STRAORDINARIA_01
api_art_activity_stream_manutenzione_straordinaria_01   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_attachments_source:log:$LOG_PATH/api-art-activity-attachments-source.log.$CURDATE
# @api_art_activity_attachments_source:log:$LOG_PATH/api-art-activity-attachments-source_full.log.$CURDATE
api_art_activity_attachments_source   arg   api-art-activity-attachments-source
api_art_activity_attachments_source   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_attachments_source   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_attachments_source   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_attachments_source   arg   --art-index=${WPSO_APPLICATION_NAME}
api_art_activity_attachments_source   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_attachments_source   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_attachments_source   arg   --log-level=INFO
api_art_activity_attachments_source   arg   --config=${COM}/share/schemas/net/sirti/ict/art/api/api-art-activity-attachments.json
api_art_activity_attachments_source   arg   --client-id=${ART_INSTANCE_NAME}
api_art_activity_attachments_source   arg   --daemon=1
#
# @api_art_activity_source_shrink:log:$LOG_PATH/api-art-activity-source-shrink.log.$CURDATE
# @api_art_activity_source_shrink:log:$LOG_PATH/api-art-activity-source-shrink_full.log.$CURDATE
api_art_activity_source_shrink   arg   api-art-activity-source-shrink
api_art_activity_source_shrink   arg   --art-id=${WPSOCORE_ARTID}
api_art_activity_source_shrink   arg   --art-user-name=${WPSOCORE_SCRIPT_USER}
api_art_activity_source_shrink   arg   --art-user-pwd=${WPSOCORE_SCRIPT_PASSWORD}
api_art_activity_source_shrink   arg   --log-level=INFO
api_art_activity_source_shrink   arg   --daemon=3600
api_art_activity_source_shrink   arg   --transaction-mode=c
api_art_activity_source_shrink   arg   --max-rows=1000000
#
# @lc02_booking_ack:log:$LOG_PATH/LC02BookingACK/consumer.log.$CURDATE
# @lc02_booking_ack:log:$LOG_PATH/LC02BookingACK/ramq.log.$CURDATE
# @lc02_booking_ack:log:$LOG_PATH/LC02BookingACK/ramq_full.log.$CURDATE
lc02_booking_ack          arg   ramq
lc02_booking_ack          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
lc02_booking_ack          arg   ${ETC}/ramq/LC02/BookingACK.conf
lc02_booking_ack          arg   --daemon=5
lc02_booking_ack          arg   --transaction-mode=c
#
# @network_bot_speedark_response:log:$LOG_PATH/NetworkBOTSpeedarkResponse/consumer.log.$CURDATE
# @network_bot_speedark_response:log:$LOG_PATH/NetworkBOTSpeedarkResponse/ramq.log.$CURDATE
# @network_bot_speedark_response:log:$LOG_PATH/NetworkBOTSpeedarkResponse/ramq_full.log.$CURDATE
network_bot_speedark_response          arg   ramq
network_bot_speedark_response          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
network_bot_speedark_response          arg   ${ETC}/ramq/NETWORK/NetworkBOTSpeedarkResponse.conf
network_bot_speedark_response          arg   --daemon=600
network_bot_speedark_response          arg   --transaction-mode=c
#
# @network_documentation_timeout:log:$LOG_PATH/NetworkDocumentationTimeout/consumer.log.$CURDATE
# @network_documentation_timeout:log:$LOG_PATH/NetworkDocumentationTimeout/ramq.log.$CURDATE
# @network_documentation_timeout:log:$LOG_PATH/NetworkDocumentationTimeout/ramq_full.log.$CURDATE
network_documentation_timeout          arg   ramq
network_documentation_timeout          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
network_documentation_timeout          arg   ${ETC}/ramq/NETWORK/NetworkDocumentationTimeout.conf
network_documentation_timeout          arg   --daemon=300
network_documentation_timeout          arg   --transaction-mode=c
#
# @network_documentation_pending:log:$LOG_PATH/NetworkDocumentationPending/consumer.log.$CURDATE
# @network_documentation_pending:log:$LOG_PATH/NetworkDocumentationPending/ramq.log.$CURDATE
# @network_documentation_pending:log:$LOG_PATH/NetworkDocumentationPending/ramq_full.log.$CURDATE
network_documentation_pending          arg   ramq
network_documentation_pending          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
network_documentation_pending          arg   ${ETC}/ramq/NETWORK/NetworkDocumentationPending.conf
network_documentation_pending          arg   --daemon=300
network_documentation_pending          arg   --transaction-mode=c
#
# @network_booking_ack:log:$LOG_PATH/NetworkBookingACK/consumer.log.$CURDATE
# @network_booking_ack:log:$LOG_PATH/NetworkBookingACK/ramq.log.$CURDATE
# @network_booking_ack:log:$LOG_PATH/NetworkBookingACK/ramq_full.log.$CURDATE
network_booking_ack          arg   ramq
network_booking_ack          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
network_booking_ack          arg   ${ETC}/ramq/NETWORK/NetworkBookingACK.conf
network_booking_ack          arg   --daemon=5
network_booking_ack          arg   --transaction-mode=c
#
# @corrective_maintenance_booking_ack:log:$LOG_PATH/CorrectiveMaintenanceBookingACK/consumer.log.$CURDATE
# @corrective_maintenance_booking_ack:log:$LOG_PATH/CorrectiveMaintenanceBookingACK/ramq.log.$CURDATE
# @corrective_maintenance_booking_ack:log:$LOG_PATH/CorrectiveMaintenanceBookingACK/ramq_full.log.$CURDATE
corrective_maintenance_booking_ack          arg   ramq
corrective_maintenance_booking_ack          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
corrective_maintenance_booking_ack          arg   ${ETC}/ramq/MANUTENZIONE_CORRETTIVA/CorrectiveMaintenanceBookingACK.conf
corrective_maintenance_booking_ack          arg   --daemon=5
corrective_maintenance_booking_ack          arg   --transaction-mode=c
#
# @rashrink:log:$LOG_PATH/rashrink.log.$CURDATE
rashrink          arg   rashrink
rashrink          arg   --connect-string=${SQLID_RA}
rashrink          arg   --retention=1
rashrink          arg   --max-sessions=100
rashrink          arg   --runs=0
rashrink          arg   --delay=30
rashrink          arg   --force
rashrink          arg   --do-commit
#
# @field_service:log:$LOG_PATH/FieldService/consumer.log.$CURDATE
# @field_service:log:$LOG_PATH/FieldService/ramq.log.$CURDATE
# @field_service:log:$LOG_PATH/FieldService/ramq_full.log.$CURDATE
field_service          arg   ramq
field_service          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
field_service          arg   --daemon=300
field_service          arg   --transaction-mode=c
field_service          arg   ${ETC}/ramq/FieldService.conf
#
# @repository_notice:log:$LOG_PATH/RepositoryNotice/consumer.log.$CURDATE
# @repository_notice:log:$LOG_PATH/RepositoryNotice/ramq.log.$CURDATE
# @repository_notice:log:$LOG_PATH/RepositoryNotice/ramq_full.log.$CURDATE
repository_notice          arg   ramq
repository_notice          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
repository_notice          arg   --daemon=300
repository_notice          arg   --transaction-mode=c
repository_notice          arg   ${ETC}/ramq/RepositoryNotice.conf
#
# @remote_media_notifications:log:$LOG_PATH/RemoteMediaNotifications/consumer.log.$CURDATE
# @remote_media_notifications:log:$LOG_PATH/RemoteMediaNotifications/ramq.log.$CURDATE
# @remote_media_notifications:log:$LOG_PATH/RemoteMediaNotifications/ramq_full.log.$CURDATE
remote_media_notifications   arg   ramq
remote_media_notifications   arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
remote_media_notifications   arg   --daemon=30
remote_media_notifications   arg   --transaction-mode=c
remote_media_notifications   arg   ${ETC}/ramq/RemoteMediaNotifications.conf
#
# @preemptive_maintenance_01_works_notify_open:log:$LOG_PATH/PreemptiveMaintenance01WorksNotifyOpen/consumer.log.$CURDATE
# @preemptive_maintenance_01_works_notify_open:log:$LOG_PATH/PreemptiveMaintenance01WorksNotifyOpen/ramq.log.$CURDATE
# @preemptive_maintenance_01_works_notify_open:log:$LOG_PATH/PreemptiveMaintenance01WorksNotifyOpen/ramq_full.log.$CURDATE
preemptive_maintenance_01_works_notify_open          arg   ramq
preemptive_maintenance_01_works_notify_open          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
preemptive_maintenance_01_works_notify_open          arg   --daemon=3
preemptive_maintenance_01_works_notify_open          arg   --transaction-mode=c
preemptive_maintenance_01_works_notify_open          arg   ${ETC}/ramq/MANUTENZIONE_PREVENTIVA_01/PreemptiveMaintenance01WorksNotifyOpen.conf
#
# @preemptive_maintenance_01_works_notify_close:log:$LOG_PATH/PreemptiveMaintenance01WorksNotifyClose/consumer.log.$CURDATE
# @preemptive_maintenance_01_works_notify_close:log:$LOG_PATH/PreemptiveMaintenance01WorksNotifyClose/ramq.log.$CURDATE
# @preemptive_maintenance_01_works_notify_close:log:$LOG_PATH/PreemptiveMaintenance01WorksNotifyClose/ramq_full.log.$CURDATE
preemptive_maintenance_01_works_notify_close          arg   ramq
preemptive_maintenance_01_works_notify_close          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
preemptive_maintenance_01_works_notify_close          arg   --daemon=3
preemptive_maintenance_01_works_notify_close          arg   --transaction-mode=c
preemptive_maintenance_01_works_notify_close          arg   ${ETC}/ramq/MANUTENZIONE_PREVENTIVA_01/PreemptiveMaintenance01WorksNotifyClose.conf
#
# @extraordinary_maintenance_01_works_notify_open:log:$LOG_PATH/ExtraordinaryMaintenance01WorksNotifyOpen/consumer.log.$CURDATE
# @extraordinary_maintenance_01_works_notify_open:log:$LOG_PATH/ExtraordinaryMaintenance01WorksNotifyOpen/ramq.log.$CURDATE
# @extraordinary_maintenance_01_works_notify_open:log:$LOG_PATH/ExtraordinaryMaintenance01WorksNotifyOpen/ramq_full.log.$CURDATE
extraordinary_maintenance_01_works_notify_open          arg   ramq
extraordinary_maintenance_01_works_notify_open          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
extraordinary_maintenance_01_works_notify_open          arg   --daemon=3
extraordinary_maintenance_01_works_notify_open          arg   --transaction-mode=c
extraordinary_maintenance_01_works_notify_open          arg   ${ETC}/ramq/MANUTENZIONE_STRAORDINARIA_01/ExtraordinaryMaintenance01WorksNotifyOpen.conf
#
# @extraordinary_maintenance_01_works_notify_close:log:$LOG_PATH/ExtraordinaryMaintenance01WorksNotifyClose/consumer.log.$CURDATE
# @extraordinary_maintenance_01_works_notify_close:log:$LOG_PATH/ExtraordinaryMaintenance01WorksNotifyClose/ramq.log.$CURDATE
# @extraordinary_maintenance_01_works_notify_close:log:$LOG_PATH/ExtraordinaryMaintenance01WorksNotifyClose/ramq_full.log.$CURDATE
extraordinary_maintenance_01_works_notify_close          arg   ramq
extraordinary_maintenance_01_works_notify_close          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
extraordinary_maintenance_01_works_notify_close          arg   --daemon=3
extraordinary_maintenance_01_works_notify_close          arg   --transaction-mode=c
extraordinary_maintenance_01_works_notify_close          arg   ${ETC}/ramq/MANUTENZIONE_STRAORDINARIA_01/ExtraordinaryMaintenance01WorksNotifyClose.conf
#
# @ap_notify_generic_event:log:$LOG_PATH/ApNotifyGenericEvent/consumer.log.$CURDATE
# @ap_notify_generic_event:log:$LOG_PATH/ApNotifyGenericEvent/ramq.log.$CURDATE
# @ap_notify_generic_event:log:$LOG_PATH/ApNotifyGenericEvent/ramq_full.log.$CURDATE
ap_notify_generic_event          arg   ramq
ap_notify_generic_event          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
ap_notify_generic_event          arg   ${ETC}/ramq/ApNotifyGenericEvent.conf
ap_notify_generic_event          arg   --daemon=5
ap_notify_generic_event          arg   --transaction-mode=c