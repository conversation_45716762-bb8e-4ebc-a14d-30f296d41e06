
{
   ID_TAG              : "MQConsumer",
   
   SESSION_TYPE        : "TARGET",
 
   SESSION_DESCRIPTION : "test inoltro sms",

   CLASS               : "SIRTI::SMS::MQ::Consumer",

   ACTIVITY_TIME_RANGE : "09:23:00--23:27:40",

   SOURCE_SERVICE      : "TEST_INVIO_SMS_S",
   SOURCE_CONTEXT      : "INVIO_SMS",
 
   TARGET_SERVICE      : "TEST_INVIO_SMS_T",
   TARGET_CONTEXT      : "INVIO_SMS",  
   
   USE_LOOKUP_TABLE    : "no",

   RA_DBLINK : "",

   DB : {

        CONNECT_STRING : "remote_activity/remote_activity@dleadst2",
     
        COMMIT_MODE    : "session"
   },
   
  
   
   WORK_CONTEXT : {
     
   }
}

