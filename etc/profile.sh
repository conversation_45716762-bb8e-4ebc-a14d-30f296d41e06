#!/bin/bash

function _warn {
	echo -e "\n*** WARNING ***\n$1\n"
}

# usage: _dir_check HOME VAR ETC ...
function _dir_check {
	for P in $*
	do
		eval X='$'$P
		[[ -d "$X" ]] || _warn "Missing directory $P=$X"
		unset X
	done
	unset P
}
function _file_check {
	for P in $*
	do
		eval X='$'$P
		[[ -r "$X" ]] || _warn "Missing or non readable file $P=$X"
		unset X
	done
	unset P
}

#
#  definizione dell' ambiente
#

if [[ "$1" != "" ]]; then
	if [[ ! -d "$1" ]]; then
		_warn "'$1' not a directory!"
	fi
	export HOME="$1"
fi

if [[ "$HOME" == "" ]]; then
	_warn "Environment HOME not defined"
fi
export CURDATE=$(date +%Y%m%d)

export NAMESPACE="WPSOCORE"
export ROOT="$HOME/$NAMESPACE"
export ETC="$ROOT/etc"
export BIN="$ROOT/bin"
export SHARE="$ROOT/share"
export VAR="$ROOT/var"
export TMP="$ROOT/tmp"
export TMPDIR="$TMP"
export LIB="$ROOT/lib"
export LIB64="$ROOT/lib64"
export BINDING_ACTIVITY_PATH="$SHARE/perl5/API/ART/APP/Activity"
export PERLAPPL="$SHARE/script"
export LOG_PATH="$VAR/logs"
export LOCK_PATH="$VAR/lock"
export WWW="$SHARE/www"
export COM="$HOME/COM"
export REPOS="${VAR}/repository"

export ART_APPLICATION_NAME=$NAMESPACE
export ART_ENVIRONMENT="development"
export ART_HOME="$HOME/ART/Appl"
export ART_LIB="$ART_HOME"
export ART_REPOSITORY="$VAR/art_repository"
export ART_REPOSITORY_EMAIL="$ART_REPOSITORY/email"
export ART_REPOSITORY_TMP="$ART_REPOSITORY/tmp"
export ART_REPOSITORY_SHARED_RESOURCES="$ART_REPOSITORY/shared_resources"
export ART_REPOSITORY_TMP_TMPFILES="$ART_REPOSITORY_TMP/tmpfiles"
export ART_BINDING_SUDO_GROUP="ADMIN"

###### ORACLE ################################################################
export LANG=it_IT.UTF8
export NLS_LANG="ITALIAN_ITALY.AL32UTF8"
export NLS_DATE_FORMAT='dd/mm/yyyy hh24:mi:ss'
export NLS_TIMESTAMP_TZ_FORMAT='yyyy-mm-dd"T"hh24:mi:ss.fftzh:tzm'
export LD_LIBRARY_PATH="${COM}/lib:${LIB64}:${LIB}:${LD_LIBRARY_PATH}"
export PERL="$(which perl) -MCarp=verbose"

###### CONTROLLO DIRECTORIES #################################################
_dir_check ROOT ETC BIN SHARE VAR TMP PERLAPPL LOG_PATH LOCK_PATH WWW COM REPOS ART_REPOSITORY ART_REPOSITORY_EMAIL ART_REPOSITORY_TMP ART_REPOSITORY_SHARED_RESOURCES ART_REPOSITORY_TMP_TMPFILES


###### PERL ##################################################################
P5L=""
for P in \
	${SHARE}/perl5 \
	${COM}/share/perl5 \
	$ART_LIB
do
	[ -d "$P" ] && P5L="${P5L}:${P}"
done
unset P

P5L=${P5L:1}
if [[ "$PERL5LIB" != "" ]] ; then
	PERL5LIB="${P5L}:${PERL5LIB}"
else
	PERL5LIB="${P5L}"
fi
export PERL5LIB

export PATH="$BIN:${COM}/bin:${HOME}/opt/wkhtmltox/bin:$PATH"

# ART WEB ENVIRONMENT
export ART_WWW="$ART_HOME/web"
export APACHE_ART_PORT=10085
export APACHE_ART_USER=$(id -nu)
export APACHE_ART_GROUP=$(id -ng)
export APACHE_ART_BASE="$VAR/apache"
export APACHE_ART_SESSION_PATH="$APACHE_ART_BASE/session/WPSOCOREART"
export APACHE_ART_LOG_PATH="$APACHE_ART_BASE/logs/WPSOCOREART/$(hostname)"
export APACHE_ART_PID_PATH="$APACHE_ART_LOG_PATH"
export APACHE_ART_PID_FILE="$APACHE_ART_PID_PATH/httpd.WPSOCOREART.pid"
export APACHE_ART_LOCK_PATH="$APACHE_ART_BASE/lock/$(hostname)"
export APACHE_ART_LOCK_FILE="$APACHE_ART_LOCK_PATH/httpd.WPSOCOREART.lock"
export SSO_APPLICATION_NAME="wpsocorelegacy"

_dir_check ART_WWW APACHE_ART_BASE APACHE_ART_SESSION_PATH APACHE_ART_LOG_PATH APACHE_ART_PID_PATH APACHE_ART_LOCK_PATH

# WSART WEB ENVIRONMENT
export DANCER_ENVIRONMENT="$ART_ENVIRONMENT.$(hostname)"
export DANCER_CONFDIR="$ETC/WSART/"
export WSART_HOME="$ROOT/share/perl5/WebService/WSART"
export WSART_SESSION_DURATION="7200" # il valore della DURATION deve essere uguale alla chiave ART.TIMEOUT_SESSIONI in CONFIG_ART
export ART_WS_ROUTES_PREFIX="/wpso/core/api/art" # default /api/art
export APACHE_WSART_PORT=10087
export APACHE_WSART_USER=$(id -nu)
export APACHE_WSART_GROUP=$(id -ng)
export APACHE_WSART_BASE="$VAR/apache"
export APACHE_WSART_SESSION_PATH="$APACHE_WSART_BASE/session/WSART/$(hostname)"
export APACHE_WSART_LOG_PATH="$APACHE_WSART_BASE/logs/WSART/$(hostname)"
export APACHE_WSART_PID_FILE="$APACHE_WSART_LOG_PATH/httpd.WSART.pid"
export APACHE_WSART_LOCK_FILE="$VAR/lock/$(hostname)/httpd.WSART.lock"
export ART_WS_AUTH_BY_JWT="1"
export ART_WS_AUTH_BY_JWT_MODE="slave"
export ART_WS_AUTH_BY_JWT_SESSION_EXPIRE="" # numero di secondi di inattività dopo i quali la sessione scade, solo in modalità peer
export ART_WS_AUTH_BY_JWT_CACHE_EXPIRE="3600" # numero di secondi di inattività dopo i quali la cache viene cancellata
export ART_WS_AUTH_BY_PROXY=1
export ART_ALLOW_LOGIN_WITHOUT_PASSWORD=1
export ART_WS_CACHE_MEMCACHED_SERVER="drvaswp001.corp.sirti.net:11219,drvaswp001.corp.sirti.net:11214"
#export ART_WS_CACHE_MEMCACHED_SERVER="drvaswp001.corp.sirti.net:11219"
export ART_WS_SESSION_EXPIRE="86400" # numero di secondi in un giorno
export ART_WS_CACHE_EXPIRE="604800" # numero di secondi in una settimana
#export ART_WS_CORS_ALLOWED_ORIGINS=" http://localhost:9000   , google.com,http://drvaswp001.corp.sirti.net:10121,http://drvaswp001.corp.sirti.net:10160,http://localhost:9001,http://drvaswp001.corp.sirti.net:10123,http://drvaswp001.corp.sirti.net:10172,http://dlvgwpol001.corp.sirti.net:10172"
export ART_WS_CORS_ALLOWED_ORIGINS=""

_dir_check WSART_HOME APACHE_WSART_BASE APACHE_WSART_SESSION_PATH APACHE_WSART_LOG_PATH

export WPSOCOREWS_WS_ROUTES_PREFIX='/wpso/core' # default /wpso/core

export WPSO_HOMEPAGE="https://services.sirti.net/authp/app/wpso"
export WPSOUI_HOMEPAGE="${WPSO_HOMEPAGE}/ui"
export WPSO_FIELD_SERVICE_USER="FIELD_SERVICE_USER"
export WPSO_FIELD_SERVICE_EVENT_USER="FIELD_SERVICE_EVENT_USER"

# NETWORKGWWS WEB ENVIRONMENT
export NETWORKGWWS_DANCER_ENVIRONMENT="$ART_ENVIRONMENT.$(hostname)" # SetEnv DANCER_ENVIRONMENT
export NETWORKGWWS_DANCER_CONFDIR="$ETC/NETWORKGWWS/" # SetEnv DANCER_CONFDIR
export NETWORKGWWS_HOME="$ROOT/share/perl5/WebService/WS"
export NETWORKGWWS_ROUTES_PREFIX="/networkgwws/rest" # SetEnv WS_ROUTES_PREFIX - default /rest
export NETWORKGWWS_SUPPORT_DB=1 # SetEnv WS_SUPPORT_DB
export APACHE_NETWORKGWWS_PORT=10095
export APACHE_NETWORKGWWS_USER=$(id -nu)
export APACHE_NETWORKGWWS_GROUP=$(id -ng)
export APACHE_NETWORKGWWS_BASE="$VAR/apache"
export APACHE_NETWORKGWWS_SESSION_PATH="$APACHE_NETWORKGWWS_BASE/session/NETWORKGWWS/$(hostname)"
export APACHE_NETWORKGWWS_LOG_PATH="$APACHE_NETWORKGWWS_BASE/logs/NETWORKGWWS/$(hostname)"
export APACHE_NETWORKGWWS_PID_FILE="$APACHE_NETWORKGWWS_LOG_PATH/httpd.NETWORKGWWS.pid"
export APACHE_NETWORKGWWS_LOCK_FILE="$VAR/lock/$(hostname)/httpd.NETWORKGWWS.lock"

_dir_check NETWORKGWWS_HOME APACHE_NETWORKGWWS_BASE APACHE_NETWORKGWWS_SESSION_PATH APACHE_NETWORKGWWS_LOG_PATH

export NETWORKGWWS_WS_ROUTES_PREFIX="/networkgwws/networkgw" # default /networkgwws/networkgw

# FSGWS WEB ENVIRONMENT
export FSGWWS_DANCER_ENVIRONMENT="$ART_ENVIRONMENT.$(hostname)" # SetEnv DANCER_ENVIRONMENT
export FSGWWS_DANCER_CONFDIR="$ETC/FSGWWS/" # SetEnv DANCER_CONFDIR
export FSGWWS_HOME="$ROOT/share/perl5/WebService/WS"
export FSGWWS_ROUTES_PREFIX="/wpsogwws/rest" # SetEnv WS_ROUTES_PREFIX - default /rest
export FSGWWS_SUPPORT_DB=1 # SetEnv WS_SUPPORT_DB
export APACHE_FSGWWS_PORT=10091
export APACHE_FSGWWS_USER=$(id -nu)
export APACHE_FSGWWS_GROUP=$(id -ng)
export APACHE_FSGWWS_BASE="$VAR/apache"
export APACHE_FSGWWS_SESSION_PATH="$APACHE_FSGWWS_BASE/session/FSGWWS/$(hostname)"
export APACHE_FSGWWS_LOG_PATH="$APACHE_FSGWWS_BASE/logs/FSGWWS/$(hostname)"
export APACHE_FSGWWS_PID_FILE="$APACHE_FSGWWS_LOG_PATH/httpd.FSGWWS.pid"
export APACHE_FSGWWS_LOCK_FILE="$VAR/lock/$(hostname)/httpd.FSGWWS.lock"

_dir_check FSGWWS_HOME APACHE_FSGWWS_BASE APACHE_FSGWWS_SESSION_PATH APACHE_FSGWWS_LOG_PATH

export FSGWWS_WS_ROUTES_PREFIX="/wpsogwws/wpsogw" # default /networkgwws/networkgw

# secret interna di FSGWWS
export FSGWWS_API_KEY_JWT_SECRET="fsgwsnfssecret"

export FSGWWS_LOG4PERL_CONF="$ETC/fsgwws.log4perl.conf"

# SPEEDARKBOT ENVIRONMENT
export SPEEDARKBOT_REPOSITORY="$REPOS/SpeedArkBot"
export SPEEDARKBOT_COOKIES="$SPEEDARKBOT_REPOSITORY/Cookies"
export SPEEDARKBOT_DOWNLOADS="$SPEEDARKBOT_REPOSITORY/Downloads"
export SPEEDARKBOT_SNAPSHOTS="$TMP"
_dir_check SPEEDARKBOT_REPOSITORY SPEEDARKBOT_COOKIES SPEEDARKBOT_DOWNLOADS SPEEDARKBOT_SNAPSHOTS

###############################################################################
#
# FINE SEZIONE CONDIVISIBILE
#
###############################################################################

export MAILADDR_BG="<EMAIL>"

export SPEEDARKBOT_NOTIFICATION_CC="<EMAIL>"

export WPSOCORE_ARTID="SIRTI_WPSOCORE_C"
export WPSOCORE_SCRIPT_USER="root" #WPSOCORE_SCRIPT_USER
export WPSOCORE_SCRIPT_PASSWORD="pippo123"
if [[ "$WPSOCORE_SCRIPT_PASSWORD" == "" ]]; then
	_warn "Missing WPSOCORE_SCRIPT_PASSWORD"
fi

export WPSOCORE_ADMIN_USER="root"
export WPSOCORE_ADMIN_PASSWORD="pippo123"
if [[ "$WPSOCORE_ADMIN_PASSWORD" == "" ]]; then
        _warn "Missing WPSOCORE_ADMIN_PASSWORD"
fi

export ARTID=$WPSOCORE_ARTID
export ART_SCRIPT_USER=$WPSOCORE_SCRIPT_USER
export ART_SCRIPT_PASSWORD=$WPSOCORE_SCRIPT_PASSWORD

export ART_DEFAULT_MAIL_SENDER="\"$ART_APPLICATION_NAME\" <noreply-`id -nu`@sirti.net>"
export ART_DEFAULT_REPLY_TO=$ART_DEFAULT_MAIL_SENDER
export ART_EMAIL_SERVICE_DONT_SEND=1 # se true il demone di invio mail esegue il fetch delle RA ma non invia le email

export ART_DB_DEBUG=0

# disabilita uso delle PW criptate
export ART_NO_DB_PWD=1

export ART_API_KEY_JWT_SECRET="qwsdfvbnmk123"

# BOTGWWS WEB ENVIRONMENT
export BOTGWWS_ARTID=$WPSOCORE_ARTID
export BOTGWWS_SCRIPT_USER=$WPSOCORE_SCRIPT_USER
export BOTGWWS_SCRIPT_PASSWORD=$WPSOCORE_SCRIPT_PASSWORD
export BOTGWWS_DANCER_ENVIRONMENT="$ART_ENVIRONMENT.$(hostname)" # SetEnv DANCER_ENVIRONMENT
export BOTGWWS_DANCER_CONFDIR="$ETC/BOTGWWS/" # SetEnv DANCER_CONFDIR
export BOTGWWS_HOME="$ROOT/share/perl5/WebService/WS"
export BOTGWWS_ROUTES_PREFIX="/botgwws/rest" # SetEnv WS_ROUTES_PREFIX - default /rest
export BOTGWWS_SUPPORT_DB=1 # SetEnv WS_SUPPORT_DB
export BOTGWWS_REPOSITORY_SHARED_RESOURCES=$ART_REPOSITORY_SHARED_RESOURCES
export APACHE_BOTGWWS_PORT=10096
export APACHE_BOTGWWS_USER=$(id -nu)
export APACHE_BOTGWWS_GROUP=$(id -ng)
export APACHE_BOTGWWS_BASE="$VAR/apache"
export APACHE_BOTGWWS_SESSION_PATH="$APACHE_BOTGWWS_BASE/session/BOTGWWS/$(hostname)"
export APACHE_BOTGWWS_LOG_PATH="$APACHE_BOTGWWS_BASE/logs/BOTGWWS/$(hostname)"
export APACHE_BOTGWWS_PID_FILE="$APACHE_BOTGWWS_LOG_PATH/httpd.BOTGWWS.pid"
export APACHE_BOTGWWS_LOCK_FILE="$VAR/lock/$(hostname)/httpd.BOTGWWS.lock"

_dir_check BOTGWWS_HOME APACHE_BOTGWWS_BASE APACHE_BOTGWWS_SESSION_PATH APACHE_BOTGWWS_LOG_PATH BOTGWWS_REPOSITORY_SHARED_RESOURCES

export BOTGWWS_WS_ROUTES_PREFIX="/botgwws/botgw" # default /botgwws/botgw

export BOTGWWS_URL_FOR_UPLOAD_FILES="http://$(hostname).ict.sirti.net:${APACHE_BOTGWWS_PORT}${BOTGWWS_WS_ROUTES_PREFIX}/files"
#export BOTGWWS_URL_FOR_UPLOAD_FILES="http://pvmas090.ict.sirti.net:${APACHE_BOTGWWS_PORT}${BOTGWWS_WS_ROUTES_PREFIX}/files"

# Configurazione Notifiche FiberCop
export FIBERCOP_NOTIFICHE_DELAY=30
export FIBERCOP_NOTIFICHE_DBTEAM='<EMAIL>'

#configurazione manutenzione correttiva con disservizio
export MC_DISSERVIZIO_WBE=TETI269IT0001
export MC_DISSERVIZIO_MACROACTIVITY=CANMT
export MC_DISSERVIZIO_ACTIVITY=02

#configurazione manutenzione preventiva 01
export MP01_FINE_MANUTENZIONE_RECIPIENTS="<EMAIL>"
export MS01_FINE_MANUTENZIONE_RECIPIENTS="<EMAIL>"

# Configurazione FSGW (WPSOGWWS)
#export WPSOGWWS_NFS_ENDPOINT="https://services.sirti.net/sirti/api/private/wpsogwws/wpsogw"
#export WPSOGWWS_NFS_API_KEY='eyJhbGciOiJIUzI1NiJ9.eyJ1c2VybmFtZSI6IkZJRUxEX1NFUlZJQ0VfVVNFUiJ9.Z21PiX6x6ISt1EP1hZ-zhHzncVVf2HMEgQC8NGEm3PQ'
export WPSOGWWS_NFS_ENDPOINT='http://drvaswp001.corp.sirti.net:10091/wpsogwws/wpsogw'
export WPSOGWWS_NFS_API_KEY='eyJhbGciOiJIUzI1NiJ9.eyJ1c2VybmFtZSI6IkZTX1VTRVIifQ.VuZH2yQ_XsZ1JskctLqXYOGQR26PPlidbz886WeZYBk'
export WPSOGWWS_NFS_MAX_RETRIES=10
export WPSOGWWS_NFS_RETRY_DELAY_SECONDS=1800

# sezione SQLID
#export SQLID_WWW=core_art/core_art@penftthcore
#export SQLID_SUPPORT=core/core@penftthcore
#export SQLID_RA=REMOTE_ACTIVITY/rmact@penftthcore
#export SQLID_REPORT=core_rpt/c0r3_rp7@penftthcore
#export SQLID=$SQLID_WWW
#export SQLID_WPSO=en_ftth/en_ftth@penftth
#export SQLID_EXTERNAL_SYNC='CS01_EXCHANGE/pw4cs01exchange$@penftthcore'
#export SQLID_ANAGRAFICHE='client_wp/wpclnt1_$1@panagrafiche'
#export SQLID_SPEEDARKBOT='bot/PWb0134t$@penftthcore'
#export SQLID_FSGW='fsgw/Fsgw!12345@penftthcore'
export SQLID_WWW=core_art/core_art@dwpsocore
export SQLID_SUPPORT=core/core@dwpsocore
export SQLID_RA=REMOTE_ACTIVITY/remote_activity@dwpsocore
export SQLID_REPORT=core_rpt/core_rpt@dwpsocore
export SQLID=$SQLID_WWW
export SQLID_WPSO=en_ftth/en_ftth@dadvlog
export SQLID_EXTERNAL_SYNC='CS01_EXCHANGE/Exchang$01@dwpsocore'
export SQLID_ANAGRAFICHE='client_wp/wpclnt1@danagrafiche'
export SQLID_SPEEDARKBOT='bot/B0t12345$@dwpsocore'
export SQLID_FSGW='fsgw/Fsg3@dwpsocore'


export LOG4PERL_CONF="$ETC/wpsocore.log4perl.conf"
export FULL_LOG_FILE=""
export SSD_CONFIG_FILE="$ETC/daemon.conf"
export DAEMONS_ACTIVE_CONFIG_FILE="$ETC/daemons.active"
_file_check LOG4PERL_CONF SSD_CONFIG_FILE

export ART_AUTH_TYPE="LOCAL"
export RADIUS_SERVER="***********" # intranet
export RADIUS_SECRET="abcdfef15032012abcdfef" # intranet
export RADIUS_DOMAIN="@corp.sirti.net" # intranet
export RADIUS_SERVER_EXT="***********" # extranet
export RADIUS_SECRET_EXT="ghilmnp22102014ghilmnp" # extranet
export RADIUS_DOMAIN_EXT="@partner.sirti.net" # extranet
export RADIUS_SERVER_BKP="***********" # intranet bkp
export RADIUS_SECRET_BKP="abcdfef15032012abcdfef" # intranet bkp
export RADIUS_SERVER_EXT_BKP="***********" # extranet bkp
export RADIUS_SECRET_EXT_BKP="ghilmnp22102014ghilmnp" # extranet bkp

#export CLAMAV_DAEMON_PORT=/run/clamd.scan/clamd.sock
export CLAMAV_HOST=prvmrrclam001.corp.sirti.net
export CLAMAV_PORT=3310

# per DB2
export SIA_DB2_NAME="DB2DRDA"
export SIA_DB2_HOST="************"
export SIA_DB2_PORT="50000"
export SIA_DB2_PROTOCOL="TCPIP"
export SIA_DB2_UID="USRJBO1"
export SIA_DB2_PWD="USRJBO1"
# sia per DB2 che ORACLE
export SIA_INTERNAL_RESOURCE="WPSOOFI"
export SIA_EXTERNAL_RESOURCE="WPSOOFE"
export SIA_EXTERNAL_RESOURCE_TIM="WPSOTIME"

export SESSION_AUDIT_DAYS_AGO=1
export SESSION_AUDIT_LOG_FILE="$APACHE_WSART_LOG_PATH/access_log"
export SESSION_AUDIT_EXCLUDE_USERS="ROOT|WPSOCORE|WPSOAP|WPSOWORKS"
export SESSION_AUDIT_OUTPUT_DIR=$VAR/repository/session_audit
export SESSION_AUDIT_APPLICATION_NAME="WPSOOFI"
export SESSION_AUDIT_FILE_PREFIX="WPSOOF_$(hostname)"
export SESSION_AUDIT_UPLOAD_SERVER="ftp2.sirti.net"
export SESSION_AUDIT_UPLOAD_DIR="WPSOOF/"
export SESSION_AUDIT_UPLOAD_USER="WORKINGPLUS"
export SESSION_AUDIT_UPLOAD_PASSWORD="PW4passwp+"

_dir_check SESSION_AUDIT_OUTPUT_DIR

export SERVICE_INSTANCES="WPSOCORE,WPSOAP,WPSOWORKS"
export WPSOCORE_USER_SID="5b37aba606dbec6abf7d0307377cdc85"
export WPSOCORE_USER_USERNAME="wpsocore"
export WPSOCORE_USER_PASSWORD="wpsocore123"
if [[ "$WPSOCORE_USER_PASSWORD" == "" ]]; then
	_warn "Missing WPSOCORE_USER_PASSWORD"
fi
export WPSOAP_USER_SID="de6c2b5d131218d6543241e4c9adc4ca"
export WPSOAP_USER_USERNAME="wpsoap"
export WPSOAP_USER_PASSWORD="wpsoap123"
if [[ "$WPSOAP_USER_PASSWORD" == "" ]]; then
	_warn "Missing WPSOAP_USER_PASSWORD"
fi
export WPSOWORKS_USER_SID="81ae39a935d496e8bef2320a3451181f"
export WPSOWORKS_USER_USERNAME="wpsoworks"
export WPSOWORKS_USER_PASSWORD="wpsoworks123"
if [[ "$WPSOWORKS_USER_PASSWORD" == "" ]]; then
	_warn "Missing WPSOWORKS_USER_PASSWORD"
fi

export WPSO_APPLICATION_NAME="wdso"

export WS_BASE_URL="http://dlvgwpol001.ict.sirti.net:10172"
# export SINFO_GET_PROJECTS_RESOURCE="/sirti/api/private/sinfo/customers/%s/contracts/%s/design/projects"
# export SINFO_GET_PROJECT_RESOURCE="/sirti/api/private/sinfo/customers/%s/contracts/%s/design/projects/%d"

export ARTSOEN_ARTID="SIRTI_ARTSOEN"
export ARTSOEN_ADMIN_USER="root"
export ARTSOEN_ADMIN_PASSWORD="pippo123"
if [[ "$ARTSOEN_ADMIN_PASSWORD" == "" ]]; then
	_warn "Missing ARTSOEN_ADMIN_PASSWORD"
fi

export CIL_REQUESTS_ENDPOINT="/sirti/api/private/timgwws/bizorcservice/cil.svc/"
export CUI_REQUESTS_ENDPOINT="/sirti/api/private/timgwws/bizorcservice/cui.svc/"
export RCI_REQUESTS_ENDPOINT="/sirti/api/private/timgwws/bizorcservice/rci.svc/"
export NETWORK_RESPONSE_ENABLE_MAIL_INCONSISTENT_STATUS=0
export NETWORK_RESPONSE_RECIPIENTS_MAIL_INCONSISTENT_STATUS="<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
export NETWORK_CREATION_DOCUMENTS_URL="${WPSOUI_HOMEPAGE}/#/OC01/TIM/CREATION/NETWORKS/%s/DOCUMENTATION_SECTION/activities"

export NETWORK_DISABLE_TIM_INTEGRATION=0

# espresso in secondi
export NETWORK_DOCUMENTATION_TIMEOUT=172800
export NETWORK_DOCUMENTATION_PENDING=86400
export NETWORK_DISABLE_AUTOMATIC_DOCUMENTATION=1

export NETWORK_5GSITES_RESOURCE="/sirti/api/private/wpso/customers/TIM/contracts/CREATION/so/sites"
export NETWORK_5GSITES_API_KEY="eyJhbGciOiJIUzI1NiJ9.eyJ1c2VybmFtZSI6IlJPT1QifQ.Uas_xpF7qk80lZjo5mGQ05CveqBNpyfbX7ye97HxYSE"
if [[ "$NETWORK_5GSITES_API_KEY" == "" ]]; then
	_warn "Missing NETWORK_5GSITES_API_KEY"
fi

# elenco macchine
export WPSOCORE_MACHINES_OSS='drvaswp001'
export WPSOCORE_MACHINES_AS='drvaswp001'

export GMAP_CLIENT="gme-sirtispa"
export GMAP_KEY="5O8ypjMBVY5U_dtcwrhtwV243NY="
if [[ "$GMAP_KEY" == "" ]]; then
	_warn "Missing GMAP_KEY"
fi

export SIRTI_CONTACT_CENTER="+39 02.9588.1"

# media notifications
export MEDIA_NOTIFICATIONS_PROVIDER='CARTELLA_DIGITALE'
export MEDIA_NOTIFICATIONS_CARTELLA_DIGITALE_USERNAME='SIRTI\AA00028'
export MEDIA_NOTIFICATIONS_CARTELLA_DIGITALE_PASSWORD='PW4art2si92'
export MEDIA_NOTIFICATIONS_CARTELLA_DIGITALE_PROTOCOL='http'
export MEDIA_NOTIFICATIONS_CARTELLA_DIGITALE_URL='dvmwaspws.corp.sirti.net/webservice/si90ws/vistaplusarchive.asmx/GetDocumentFile?VistaplusTransactionId=%s&FileIndex=%s&SocietaCode=%s'

# test data loader
export TEST_DATA_MAXIMUM_LOAD_ERROR="50"
export TEST_DATA_TOLLERANCE_DIFF_PERCENTAGE="8"
export TEST_DATA_LOADERS_PATH="${REPOS}/test-data-loaders"
mkdir -p "${TEST_DATA_LOADERS_PATH}/IN"
mkdir -p "${TEST_DATA_LOADERS_PATH}/ELABORATI"

### sezione ELK ###
export KAFKA_HOST="drvkkapp001.corp.sirti.net"
export KAFKA_PORT="9092"
export KAFKA_CONNECT_PROTOCOL="http"
export KAFKA_CONNECT_HOST="drvkkapp001.corp.sirti.net"
export KAFKA_CONNECT_PORT="8083"
export KAFKA_REST_PROTOCOL="http"
export KAFKA_REST_HOST="drvkkapp001.corp.sirti.net"
export KAFKA_REST_PORT="8082"
export KAFKA_SCHEMA_REGISTRY_PROTOCOL="http"
export KAFKA_SCHEMA_REGISTRY_HOST="drvkkapp001.corp.sirti.net"
export KAFKA_SCHEMA_REGISTRY_PORT="8081"
# export KAFKA_TOPIC_PARTITIONS=5

export CONNECT_JKS_TRUSTSTORE="/etc/kafka/connect-ca/elastic.truststore.drvkkapp002.jks"
export CONNECT_JKS_TRUSTSTORE_PASSWORD="pippo123"
export ELASTICSEARCH_PROTOCOL="https"
export ELASTICSEARCH_HOST="drvkkapp002.corp.sirti.net"
export ELASTICSEARCH_PORT="9200"
export ELASTICSEARCH_USERNAME="wdso"
export ELASTICSEARCH_PASSWORD="pippo123"
if [[ "$ELASTICSEARCH_PASSWORD" == "" ]]; then
	_warn "Missing ELASTICSEARCH_PASSWORD"
fi

export ART_INSTANCE_NAME="wdsocore"
export STREAMER_FREQUENCY=1

export ELASTICSEARCH_NODES="$ELASTICSEARCH_PROTOCOL://$ELASTICSEARCH_USERNAME:$ELASTICSEARCH_PASSWORD@$ELASTICSEARCH_HOST:$ELASTICSEARCH_PORT"
export ELASTICSEARCH_SYNC_DELTA_TIME="60" #tempo in secondi
export ELASTICSEARCH_INSTANCE_NAME="$ART_INSTANCE_NAME"
export ELASTICSEARCH_CONFIG_FILE="$ETC/restART-config.ELK.yml"

# Gestione snapshot e backup Elasticsearch
export ELASTICSEARCH_SNAPSHOT_USERNAME="snapshot_user"
export ELASTICSEARCH_SNAPSHOT_PASSWORD="pippo123"
if [[ "$ELASTICSEARCH_SNAPSHOT_PASSWORD" == "" ]]; then
	_warn "Missing ELASTICSEARCH_SNAPSHOT_PASSWORD"
fi
export ELASTICSEARCH_SNAPSHOT_PATTERN="wpso"
export ELASTICSEARCH_SNAPSHOT_REPOSITORY="pippo123"
if [[ "$ELASTICSEARCH_SNAPSHOT_REPOSITORY" == "" ]]; then
	_warn "Missing ELASTICSEARCH_SNAPSHOT_REPOSITORY"
fi
export ELASTICSEARCH_SNAPSHOT_URL=${ELASTICSEARCH_PROTOCOL}://${ELASTICSEARCH_SNAPSHOT_USERNAME}:${ELASTICSEARCH_SNAPSHOT_PASSWORD}@${ELASTICSEARCH_HOST}:${ELASTICSEARCH_PORT}

### FINE sezione EKK ###

[[ -r "$ETC/email_service_ramq.conf" ]] || _warn "Missing $ETC/email_service_ramq.conf file: create it as a symlink to email_service to ramq.conf file (${COM}/etc/ramq/API_ART/EmailService/ramq.conf)"

[[ -r "$ETC/activity_system_property_counter.conf" ]] || _warn "Missing $ETC/activity_system_property_counter.conf file: create it as a symlink to email_service to ramq.conf file (${COM}/etc/ramq/API_ART/System/PropertyCounter/ramq.conf)"

#
# Esegue una query sul db specificato dalla	connect-string
#
function oracle_query {
	local cs="$1" #	connect-string
	[[ "$cs" ==	"" ]] && echo -e "Missing connect-string!\n\nUsage: oracle_query CONNECT_STRING QUERY" && return 1
	local sql="$2" # query
	if [[ "$sql" == "" ]]; then
		# se la query non e' stata passata tento di leggere da STDIN
		sql=$(
			while read; do
				echo $REPLY
			done
		)
	fi
	[[ "$sql" == ""	]] && echo -e "Missing query!\n\nUsage: oracle_query CONNECT_STRING QUERY" && return 1
	local t="$TMP/$$_$(date +%Y%m%d%H%M%S%N).tmp"
	local RC=0
	(
		cat	<<!
			set	line 10000
			set	feedback off
			set	heading	off
			set	autoprint off
			set	echo off
			set	headsep	off
			set	newpage	none
			set	serverout off
			set	showmode off
			set	sqlnumber off
			set	termout	off
			set	trimspool on
			set	verify off

			$sql
			;

!
	) |	 sqlplus -S	$cs	> $t ||	RC=$?

	[[ "$RC" ==	"0"	]] && grep 'ERROR at line' $t >	/dev/null && { cat $t  >&2;	RC=$ERR_SYS; }
	[[ "$RC" ==	"0"	]] && grep '^ORA-' $t >	/dev/null && { cat $t  >&2;	RC=$ERR_SYS; }
	[[ "$RC" ==	"0"	]] && cat $t
	[ "$RC" -ne 0 ] && cat $t >&2
	rm -f $t
	return $RC
}

# Cancello le funzioni per non essere disponibili nel prompt
unset -f _dir_check
unset -f _file_check
