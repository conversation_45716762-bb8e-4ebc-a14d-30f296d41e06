#
#file di configurazione di start_stop_daemon  per API_ART_Activity_NotificationService
#
# @api_art_activity_notificationservice:log:$LOG_PATH/API_ART_Activity_NotificationService/consumer.log.$CURDATE
# @api_art_activity_notificationservice:log:$LOG_PATH/API_ART_Activity_NotificationService/ramq.log.$CURDATE
# @api_art_activity_notificationservice:log:$LOG_PATH/API_ART_Activity_NotificationService/ramq_full.log.$CURDATE
api_art_activity_notificationservice          arg   ramq
api_art_activity_notificationservice          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
api_art_activity_notificationservice          arg   --daemon=300
api_art_activity_notificationservice          arg   --transaction-mode=c
api_art_activity_notificationservice          arg   ${COM}/etc/ramq/API_ART/Activity/NotificationService/ramq.conf
#
