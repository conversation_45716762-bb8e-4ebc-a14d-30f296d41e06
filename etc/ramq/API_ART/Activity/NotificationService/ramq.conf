
{
  	ID_TAG				:	"API_ART_Activity_NotificationService",
   
  	SESSION_TYPE		:	"TARGET",
 
	SESSION_DESCRIPTION	:	"API::ART::Activity Notification Service Consumer",

	CLASS				:	"API::ART::Activity::MQ::Consumer::NotificationService",

	SOURCE_CONTEXT		:	"${ARTID}",
	TARGET_CONTEXT		:	"API::ART::Act::N",
	
	DB : {
		ART : {
			ID : "${ARTID}",
			USER : "${ART_SCRIPT_USER}",
			PASSWORD : "${ART_SCRIPT_PASSWORD}",
			DEBUG : "${ART_DB_DEBUG}",
			AUTOSAVE : 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE : "no",
	
	WORK_CONTEXT        : {
		RESTART_HOMEPAGE : "${RESTART_HOMEPAGE}",
		ART_APPLICATION_NAME : "${ART_APPLICATION_NAME}",
		DATE_FORMAT : "${NLS_DATE_FORMAT}",
		TIMESTAMP_TZ_FORMAT : "${NLS_TIMESTAMP_TZ_FORMAT}",
		QUEUE_SOURCE_CONTEXT : "${ARTID}",
		QUEUE_TARGET_CONTEXT :	"API::ART::Act::N"
	}
}


