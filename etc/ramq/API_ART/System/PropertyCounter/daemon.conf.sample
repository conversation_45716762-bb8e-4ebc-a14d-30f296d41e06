#
#file di configurazione di start_stop_daemon  per API_ART_Activity_NotificationService
#
# @api_art_activity_system_property_counter:log:$LOG_PATH/API_ART_System_PropertyCounter/consumer.log.$CURDATE
# @api_art_activity_system_property_counter:log:$LOG_PATH/API_ART_System_PropertyCounter/ramq.log.$CURDATE
# @api_art_activity_system_property_counter:log:$LOG_PATH/API_ART_System_PropertyCounter/ramq_full.log.$CURDATE
api_art_activity_system_property_counter          arg   ramq
api_art_activity_system_property_counter          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
api_art_activity_system_property_counter          arg   --daemon=300
api_art_activity_system_property_counter          arg   --transaction-mode=c
api_art_activity_system_property_counter          arg   ${COM}/etc/ramq/API_ART/System/PropertyCounter/ramq.conf
#
