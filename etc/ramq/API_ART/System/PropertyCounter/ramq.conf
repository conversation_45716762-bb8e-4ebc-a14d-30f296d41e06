
{
  	ID_TAG				:	"API_ART_System_PropertyCounter",
   
  	SESSION_TYPE		:	"TARGET",
 
	SESSION_DESCRIPTION	:	"API::ART::System Property Counter Service Consumer",

	CLASS				:	"API::ART::System::MQ::Consumer::PropertyCounter",

	SOURCE_CONTEXT		:	"${ARTID}",
	TARGET_CONTEXT		:	"API::ART::System",
	
	DB : {
		ART : {
			ID : "${ARTID}",
			USER : "${ART_SCRIPT_USER}",
			PASSWORD : "${ART_SCRIPT_PASSWORD}",
			DEBUG : "${ART_DB_DEBUG}",
			AUTOSAVE : 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE : "no",
	
	WORK_CONTEXT        : {
		ART_APPLICATION_NAME : "${ART_APPLICATION_NAME}",
		DATE_FORMAT : "${NLS_DATE_FORMAT}",
		TIMESTAMP_TZ_FORMAT : "${NLS_TIMESTAMP_TZ_FORMAT}",
		QUEUE_SOURCE_CONTEXT : "${ARTID}",
		QUEUE_TARGET_CONTEXT :	"API::ART::System"
	}
}


