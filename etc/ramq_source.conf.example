
{
   ID_TAG              : "EXAMPLE_SOURCE",
   
   SESSION_TYPE        : "SOURCE",
 
   SESSION_DESCRIPTION : "test ramq source",

   CLASS               : "SIRTI::Queue::ExampleAckConsumer",

   SOURCE_SERVICE      : "RASHELL_S",
   SOURCE_CONTEXT      : "RASHELL",
 
    
   TARGET : {
           RASHELL_T1      : "RASHELL",
	   RASHELL_T2      : "RASHELL"  
   },

   DB  : {
   
        CONNECT_STRING      : "${SQLID}",
        COMMIT_MODE         : "session"
   },

   WORK_CONTEXT        : { }
}


