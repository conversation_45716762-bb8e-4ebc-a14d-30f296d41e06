
{
   ID_TAG              : "EXAMPLE_TARGET",
   
   SESSION_TYPE        : "TARGET",
 
   SESSION_DESCRIPTION : "test ramq target",

   CLASS               : "SIRTI::Queue::ExampleConsumer",

   SOURCE_SERVICE      : "RASHELL_S",
   SOURCE_CONTEXT      : "RASHELL",
 
   TARGET_SERVICE      : "RASHELL_T1",
   TARGET_CONTEXT      : "RASHELL",  
  
   RA_DBLINK           : null,
   
   USE_LOOKUP_TABLE    : "yes",

   
 
   DB : {

        CONNECT_STRING : "remote_activity/remote_activity@dleadst2",

        _ART : {
                 ID :       "$_{ARTID}",
                 USER :     "$_{ARTUSER}",
                 PASSWORD : "$_{ARTPASSWORD}"
        },

        COMMIT_MODE         : "session"
   },

   WORK_CONTEXT        : { }
}


