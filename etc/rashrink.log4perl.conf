#
# per utilizzare questa configurazione e' necessario avere impostato la variabile di environment LOG_PATH
#

log4perl.rootLogger                   = DEBUG, ConsumerLog, Screen, ErrScreen
log4perl.logger.COMMON::BIN::rashrink     = DEBUG, Screen

log4perl.additivity.COMMON::BIN::rashrink = 0
log4perl.additivity.COMMON::LIB       = 0

log4perl.appender.ErrScreen = Log::Dispatch::Screen
log4perl.appender.ErrScreen.stderr = 1
log4perl.appender.ErrScreen.Threshold = FATAL
log4perl.appender.ErrScreen.layout = Log::Log4perl::Layout::PatternLayout
log4perl.appender.ErrScreen.layout.ConversionPattern = %d %p> %F{1}:%L %M - %m%n

log4perl.appender.Screen = Log::Dispatch::Screen
log4perl.appender.Screen.Threshold = INFO
log4perl.appender.Screen.stderr = 0
log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
log4perl.appender.Screen.layout.ConversionPattern = %p> %m%n

log4perl.appender.ConsumerLog = Log::Dispatch::File
log4perl.appender.ConsumerLog.Threshold = INFO
log4perl.appender.ConsumerLog.filename = sub { use Date::Calc qw( Today ); ( $ENV{LOG_PATH} || '' ) . '/' . 'rashrink.log' . '.' . sprintf("%04d%02d%02d", Today) }
log4perl.appender.ConsumerLog.mode = append
log4perl.appender.ConsumerLog.layout = Log::Log4perl::Layout::PatternLayout
log4perl.appender.ConsumerLog.layout.ConversionPattern = %d %p> %m%n
