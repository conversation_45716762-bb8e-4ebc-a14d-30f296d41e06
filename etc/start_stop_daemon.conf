#esempio file di configurazione di start_stop_daemon
mycmd   arg   /bin/ls -l
mycmd   env   pippo="1"
mycmd   env   pluto='2'
mycmd   env   paperino=3
mycmd   env   topolino=4
mycmd   inherit PATH

#mycmd	clean 1
#
#
##### posso eseguire start_stop_daemon -d start mycmd 
#  per eseguire ls -l come demone
#
#  arg definisce una stringa od un array di argomenti (se piu di una riga) 
#      il primo argomento è¨ il nome del comando 
#
#  env imposta le variabili d'ambiemte da passare al demone
#  inherit eredita il valore (ha senso solo con clean=1) 
#  clean elimina tutte le variabili d' ambiente: solo quelle impostate in env o inherit vengono passate al demone
#
#



