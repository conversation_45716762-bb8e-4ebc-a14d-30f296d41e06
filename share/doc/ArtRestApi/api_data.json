[{"type": "put", "url": "/api/art/activities/:ID/assignee", "title": "Assign/Deassign", "name": "activityAssign", "group": "Activity", "description": "<p>Assign activity</p> ", "examples": [{"title": "Example usage:", "content": "PUT http://localhost/api/art/activities/2104/assignee\nContent-Type: application/json\n\n{\n  \"description\" : \"assign!\",\n  \"destUser\": \"root\"\n}", "type": "HTTP"}], "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String", "optional": false, "field": "destUser", "description": "<p>If not <code>null</code> is the username of the user to whom the activity must be assigned, if <code>null</code> the activity is deassigned.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "description", "description": "<p>Description of the assignment/deassignment.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "assignNotification", "defaultValue": "0", "description": "<p>If <code>1</code> the user will receive a notification email.</p> "}]}}, "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 204 No content", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity", "header": {"fields": {"Header": [{"group": "Header", "type": "String", "optional": false, "field": "Content-Type", "description": "<p>It must be set to <code>application/json</code>.</p> "}]}}}, {"type": "get", "url": "/api/art/activities/:ID/assignee/users", "title": "Assignee Users List", "name": "activityAssignGetUsers", "group": "Activity", "description": "<p>List all the users whom the activity <code>:ID</code> can be assigned</p> ", "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/activities/5704/assignee/users\nAccept: application/json", "type": "HTTP"}], "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n  {\n    \"email\": \"i.<PERSON><EMAIL>\",\n    \"firstName\": \"IVAN\",\n    \"disabled\": false,\n    \"serviceUser\": false,\n    \"groups\": [\n      \"TECHNICAL_SUPPORT\"\n    ],\n    \"mobilePhone\": null,\n    \"lastName\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n    \"username\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\"\n  }\n]", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity"}, {"type": "get", "url": "/api/art/activities/:ID/canDoAction/:ACTION", "title": "CanDoAction", "name": "activityCanDoAction", "group": "Activity", "description": "<p>Check if a step can be done</p> ", "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/activities/5704/canDoAction/FREEZE\nAccept: application/json", "type": "HTTP"}], "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 204 No content", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity"}, {"type": "get", "url": "/api/art/activities/:ID/destUsers/:ACTION", "title": "destUsers", "name": "activityDestUsers", "group": "Activity", "description": "<p>List all the users whom the activity <code>:ID</code> can be assigned by the action <code>:ACTION</code></p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String", "optional": true, "field": "status", "description": "<p>Set the initial status for the action defined.</p> "}]}}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/activities/5704/destUsers/FREEZE\nAccept: application/json", "type": "HTTP"}], "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n  {\n    \"email\": \"i.<PERSON><EMAIL>\",\n    \"firstName\": \"IVAN\",\n    \"disabled\": false,\n    \"serviceUser\": false,\n    \"groups\": [\n      \"TECHNICAL_SUPPORT\"\n    ],\n    \"mobilePhone\": null,\n    \"lastName\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n    \"username\": \"<PERSON><PERSON><PERSON><PERSON><PERSON>\"\n  }\n]", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity"}, {"type": "get", "url": "/api/art/activities/:ID/history", "title": "History", "name": "activityHistory", "group": "Activity", "description": "<p>Returns activity history</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "mergeChildren", "defaultValue": "0", "description": "<p>If <code>1</code> will be returned also objects representing child activities.</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous", "description": "<p>Object rappresenting the step or the child activity.</p> "}, {"group": "Success 200", "type": "String", "allowedValues": ["TRANSITION", "ACTIVITY"], "optional": false, "field": "anonymous.objType", "description": "<p>Whether the object represent a transition or a child activity.</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "anonymous.id", "description": "<p>the Id of the transition or of the child activity.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.description", "description": "<p>Description of the transition or of the child activity.</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.transitionDate", "description": "<p>If <code>objType=TRANSITION</code>, the date of the transition.</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.fromStatus", "description": "<p>If <code>objType=TRANSITION</code>, beginning status of the transition.</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.action", "description": "<p>If <code>objType=TRANSITION</code>, the action done in the transition.</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.username", "description": "<p>If <code>objType=TRANSITION</code>, the user performing activity transition.</p> "}, {"group": "Success 200", "type": "Object", "optional": true, "field": "anonymous.usernameObj", "description": "<p>If <code>objType=TRANSITION</code>, object rappresenting the user performing activity transition in the same format returned by API <strong>Activity - Get User Info</strong> <code>GET /api/art/instance/users</code>.</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.toStatus", "description": "<p>If <code>objType=TRANSITION</code>, ending status of the transition.</p> "}, {"group": "Success 200", "type": "String", "allowedValues": ["PIC", "DEPIC", "DEPIC_NO_PERMESSI", "ASSEGNAZIONE", "RIASSEGNAZIONE", "ASSEGNAZIONE_VIRTUALE", "DEASSEGNAZIONE_VIRTUALE", "RIASSEGNAZIONE_VIRTUALE"], "optional": true, "field": "anonymous.currentUserChangeReason", "description": "<p>If <code>objType=TRANSITION</code>, if not null the reason why the current user is changed.</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.currentUsername", "description": "<p>If <code>objType=TRANSITION</code>, defined only if <code>currentUserChangeReason</code> is not null, is the user in charge of the activity.</p> "}, {"group": "Success 200", "type": "Object", "optional": true, "field": "anonymous.currentUsernameObj", "description": "<p>If <code>objType=TRANSITION</code>, defined only if <code>currentUserChangeReason</code> is not null, is the object rappresenting the user in charge of the activity in the same format returned by API <strong>Activity - Get User Info</strong> <code>GET /api/art/instance/users</code>.</p> "}, {"group": "Success 200", "type": "Various", "optional": true, "field": "anonymous.properties", "description": "<ul><li>If <code>objType=TRANSITION</code>, <code>Object[]</code> rappresenting the properties of the step</li><li>If <code>objType=ACTIVITY</code>, <code>String[]</code> representing the property types related to the activity.</ul>"}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.properties.name", "description": "<p>If <code>objType=TRANSITION</code>, the name of the property.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.properties.value", "description": "<p>If <code>objType=TRANSITION</code>, the value of the property.</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "anonymous.properties.order", "description": "<p>If <code>objType=TRANSITION</code>, the order of the property. <code>null</code> if the property isn&#39;t still used by the action.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.properties.validity", "description": "<p>If <code>objType=TRANSITION</code>, <code>true</code> if the property is still used by the action, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Object[]", "optional": true, "field": "anonymous.attachments", "description": "<p>If <code>objType=TRANSITION</code>, the list of the attachments in the same format returned by API <strong>Activity - Attachment list</strong> <code>GET /api/art/activities/:ID/attachments</code>.</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.type", "description": "<p>If <code>objType=ACTIVITY</code>, the activity type.</p> "}, {"group": "Success 200", "type": "Number", "optional": true, "field": "anonymous.systemId", "description": "<p>If <code>objType=ACTIVITY</code>, the id of the system to whom the activity is associated.</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.systemType", "description": "<p>If <code>objType=ACTIVITY</code>, he type of the system to whom the activity is associated.</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.status", "description": "<p>If <code>objType=ACTIVITY</code>, he current status of the activity.</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.owner", "description": "<p>If <code>objType=ACTIVITY</code>, he username of the user that created the activity.</p> "}, {"group": "Success 200", "type": "Object", "optional": true, "field": "anonymous.ownerObj", "description": "<p>If <code>objType=ACTIVITY</code>, the object of the user in the same format returned by API <strong>Activity - Get User Info</strong> <code>GET /api/art/instance/users</code>.</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.currentUser", "description": "<p>If <code>objType=ACTIVITY</code>, he username of the user to whom the activity is assigned if any, <code>null</code> otherwhise.</p> "}, {"group": "Success 200", "type": "Object", "optional": true, "field": "anonymous.currentUserObj", "description": "<p>If <code>objType=ACTIVITY</code>, the object of the user in the same format returned by API <strong>Activity - Get User Info</strong> <code>GET /api/art/instance/users</code>.</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.creationUser", "description": "<p>If <code>objType=ACTIVITY</code>, he username of the user that created the activity</p> "}, {"group": "Success 200", "type": "Object", "optional": true, "field": "anonymous.creationUserObj", "description": "<p>If <code>objType=ACTIVITY</code>, the object of the user that created the activity in the same format returned by API <strong>Activity - Get User Info</strong> <code>GET /api/art/instance/users</code>.</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.creationDate", "description": "<p>If <code>objType=ACTIVITY</code>, he date when the activity has been created.</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.lastVarDate", "description": "<p>If <code>objType=ACTIVITY</code>, he date of the last variation of the activity.</p> "}, {"group": "Success 200", "type": "Number", "optional": true, "field": "anonymous.parentId", "description": "<p>If <code>objType=ACTIVITY</code>, he id of the activity parent if any, <code>null</code> otherwhise.</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.dateNextStep", "description": "<p>If <code>objType=ACTIVITY</code>, he minimum date available for the next step.</p> "}, {"group": "Success 200", "type": "Number[]", "optional": true, "field": "anonymous.children", "description": "<p>If <code>objType=ACTIVITY</code>, the children id list.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": true, "field": "anonymous.isLocked", "description": "<p>If <code>objType=ACTIVITY</code>, <code>true</code> if the activity is locked, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": true, "field": "anonymous.isLockedByMyself", "description": "<p>If <code>objType=ACTIVITY</code>, <code>true</code> if the activity is locked by current session, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Number", "optional": true, "field": "anonymous.lastTransitionId", "description": "<p>If <code>objType=ACTIVITY</code>, it is the id of the last transition of the activity</p> "}, {"group": "Success 200", "type": "Number", "optional": true, "field": "anonymous.lastUpdateId", "description": "<p>If <code>objType=ACTIVITY</code>, it is the id of the last update of the activity</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.lastUpdateTimestamp", "description": "<p>If <code>objType=ACTIVITY</code>, it is the timestamp of the last update of the activity</p> "}, {"group": "Success 200", "type": "Number", "optional": true, "field": "anonymous.version", "description": "<p>If <code>objType=ACTIVITY</code>, it is the version number of the activity</p> "}, {"group": "Success 200", "type": "Object", "optional": true, "field": "anonymous.systemObj", "description": "<p>If <code>objType=ACTIVITY</code>, the object of the info of the system to whom the activity is associated in the same format returned by API <strong>System - Info</strong> <code>GET /api/art/systems/:ID/info</code> plus the key <code>id</code>.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n  {\n    \"objType\":\"TRANSITION\",\n    \"id\":20150428112218,\n    \"attachments\":[\n      {\n        \"owner\":\"ROOT\",\n        \"sequence\":0,\n        \"fileName\":\"master_preferences\",\n        \"transitionDate\":\"2019-01-08T12:23:18.*********+01:00\",\n        \"transitionId\":20190108122318,\n        \"size\":75,\n        \"description\":null,\n        \"title\": null,\n        \"revision\": null,\n        \"refDate\": null,\n        \"docType\": null,\n        \"downloadCount\":null,\n        \"ownerFirstName\":\"ROOT\",\n        \"ownerLastName\":\"ROOT\"\n      }\n    ],\n    \"fromStatus\":\"START\",\n    \"currentUsername\":null,\n    \"currentUsernameObj\":null,\n    \"transitionDate\":\"2015-04-28T11:22:18.*********+02:00\",\n    \"description\":\"Apertura Attivita\",\n    \"toStatus\":\"OPEN\",\n    \"username\":\"BIDMANAGER\",\n    \"usernameObj\": {\n      \"email\": null,\n      \"firstName\": \"ROOT\",\n      \"disabled\": false,\n      \"serviceUser\": false,\n      \"groups\": [\n        \"ROOT\"\n      ],\n      \"mobilePhone\": null,\n      \"lastName\": \"ROOT\",\n      \"username\": \"ROOT\"\n    },\n    \"properties\":[\n      {\n        \"validity\":true,\n        \"order\":0,\n        \"value\":\"IC201504281101\",\n        \"name\":\"IC_NUMBER\"\n      },\n      {\n        \"validity\":true,\n        \"order\":1,\n        \"value\":\"fai tu!\",\n        \"name\":\"ENGAGE_DESCRIPTION\"\n      },\n      {\n        \"validity\":true,\n        \"order\":5,\n        \"value\":\"PROJECT_COMMESSA_1\",\n        \"name\":\"PROJECT_COMMESSA\"\n      }\n    ],\n    \"action\":\"OPEN\"\n  },\n  {\n    \"objType\": \"ACTIVITY\",\n    \"id\": 6247,\n    \"isLockedByMyself\": false,\n    \"lastTransitionId\": 10,\n    \"lastUpdateId\": 20,\n    \"lastUpdateTimestamp\": \"2015-04-17T10:57:35.*********+02:00\",\n    \"version\": 10,\n    \"status\": \"PRONTA_PER_ASSEGNAZIONE\",\n    \"children\": [],\n    \"currentUserObj\": null,\n    \"owner\": \"ROOT\",\n    \"currentUser\": null,\n    \"lastVarDate\": \"2015-04-28T11:24:02.*********+02:00\",\n    \"systemType\": \"STEP\",\n    \"isLocked\": false,\n    \"description\": \"Finestra di manutenzione per STEP_ID = 18211\",\n    \"properties\": [\n      \"DATA_PREVISTA_ATTIVITA\",\n      \"CIRCUITI_DA_MODIFICARE\",\n      \"CLIENTE\",\n      \"ORE_ATTESA_SILENZIO_ASSENSO\",\n      \"STEP_ID\",\n      \"NOTE\",\n      \"DATA_INIZIO_FDM\",\n      \"FDM_CONCORDATA\",\n      \"TITOLO\",\n      \"DATA_FINE_FDM\",\n      \"MOTIVO_INTERVENTO\",\n      \"IMPATTO_SUL_SERVIZIO\",\n      \"TIMEOUT_SILENZIO_ASSENSO\",\n      \"MOTIVAZIONE\",\n      \"LISTA_COD_CLIENTI\",\n      \"ERMES_ID\",\n      \"OWNER_RICHIESTA_FDM\",\n      \"TT_ID\",\n      \"LISTA_CLIENTI\"\n    ],\n    \"creationDate\": \"2015-04-28T11:24:01.*********+02:00\",\n    \"parentId\": 6744,\n    \"creationUser\": \"ROOT\",\n    \"dateNextStep\": \"2015-04-28T11:24:02.*********+02:00\",\n    \"systemId\": 2579,\n    \"type\": \"FDM\",\n    \"ownerObj\": {\n      \"email\": \"<EMAIL>\",\n      \"firstName\": \"ROOT\",\n      \"disabled\": false,\n      \"serviceUser\": false,\n      \"groups\": [\n        \"ROOT\"\n      ],\n      \"mobilePhone\": null,\n      \"lastName\": \"ROOT\",\n      \"username\": \"ROOT\"\n    },\n    \"creationUserObj\": {\n      \"email\": \"<EMAIL>\",\n      \"firstName\": \"ROOT\",\n      \"disabled\": false,\n      \"serviceUser\": false,\n      \"groups\": [\n        \"ROOT\"\n      ],\n      \"mobilePhone\": null,\n      \"lastName\": \"ROOT\",\n      \"username\": \"ROOT\"\n    },\n    \"systemObj\": {\n      \"disabled\": false,\n      \"disablingDate\": null,\n      \"endingDate\": null,\n      \"active\": true,\n      \"description\": \"18211\",\n      \"properties\": [\n        \"ESITO_TEST_ACCETTAZIONE\"\n      ],\n      \"ended\": false,\n      \"creationDate\": \"2015-12-22T11:55:32.*********+01:00\",\n      \"groups\": [\n        \"ADMIN\",\n        \"CUSTOMER_USER\",\n        \"ROOT\",\n        \"SIRTI_USER\"\n      ],\n      \"type\": \"STEP\",\n      \"id\": 2579\n    }\n  },\n  {\n    \"objType\":\"TRANSITION\",\n    \"id\":20150428112402,\n    \"attachments\":[\n\n    ],\n    \"fromStatus\":\"OPEN\",\n    \"currentUsername\":null,\n    \"transitionDate\":\"2015-04-28T11:24:02.*********+02:00\",\n    \"description\":\"asasas\",\n    \"toStatus\":\"IN_PROGRESS\",\n    \"username\":\"HR2\",\n    \"properties\":[\n\n    ],\n    \"action\":\"TAKE_IN_CHARGE\"\n  }\n]", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/activities/5704/history?mergeChildren=1\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity"}, {"type": "get", "url": "/api/art/activities/:ID/info", "title": "Info", "name": "activityInfo", "group": "Activity", "description": "<p>Returns activity infos</p> ", "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "String", "optional": false, "field": "description", "description": "<p>The description of the activity.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "type", "description": "<p>The activity type.</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "systemId", "description": "<p>The id of the system to whom the activity is associated.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "systemType", "description": "<p>The type of the system to whom the activity is associated.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "status", "description": "<p>The current status of the activity.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "owner", "description": "<p>The username of the user that made the last variation to the activity.</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "ownerObj", "description": "<p>the object of the user in the same format returned by API <strong>Activity - Get User Info</strong> <code>GET /api/art/instance/users</code>.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "currentUser", "description": "<p>The username of the user to whom the activity is assigned if any, <code>null</code> otherwhise.</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "currentUserObj", "description": "<p>the object of the user in the same format returned by API <strong>Activity - Get User Info</strong> <code>GET /api/art/instance/users</code>.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "creationUser", "description": "<p>The username of the user that created the activity</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "creationUserObj", "description": "<p>the object of the user that created the activity in the same format returned by API <strong>Activity - Get User Info</strong> <code>GET /api/art/instance/users</code>.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "creationDate", "description": "<p>The date when the activity has been created.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "lastVarDate", "description": "<p>The date of the last variation of the activity.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "properties", "description": "<p>The property types related to the activity.</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "parentId", "description": "<p>The id of the activity parent if any, <code>null</code> otherwhise.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "dateNextStep", "description": "<p>The minimum date available for the next step.</p> "}, {"group": "Success 200", "type": "Number[]", "optional": false, "field": "children", "description": "<p>The children id list.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "isLocked", "description": "<p><code>true</code> if the activity is locked, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "isLockedByMyself", "description": "<p><code>true</code> if the activity is locked by current session, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "active", "description": "<p><code>true</code> if the activity is active, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "lastTransitionId", "description": "<p>It is the id of the last transition of the activity</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "lastUpdateId", "description": "<p>It is the id of the last update of the activity</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "lastUpdateTimestamp", "description": "<p>It is the timestamp of the last update of the activity</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "version", "description": "<p>It is the version number of the activity</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "isClosed", "description": "<p><code>true</code> if the activity is closed, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "closureDate", "description": "<p>The closure date of the activity.</p> "}, {"group": "Success 200", "type": "Object[]", "optional": false, "field": "attachments", "description": "<p>The list of the attachments in the same format returned by API <strong>Activity - Attachment list</strong> <code>GET /api/art/activities/:ID/attachments</code>.</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "aging<PERSON><PERSON><PERSON>", "description": "<p>the object of the aging</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "agingObj.calendarId", "description": "<p>The identifier of the calendar</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "agingObj.<PERSON><PERSON><PERSON><PERSON>", "description": "<p>The label of the calendar</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "agingObj.calendarDescription", "description": "<p>The description of the calendar</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "agingObj.update", "description": "<p><code>true</code> if the aging&#39;s activity can be updated, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "agingObj.seconds", "description": "<p>The value of the aging in seconds</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "agingObj.interval", "description": "<p>The value of the aging as interval</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "agingObj.lastVarDate", "description": "<p>The date of the last variation date of the aging</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "agingObj.targetLevel", "description": "<p>The level of the target SLA</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "agingObj.targetLabel", "description": "<p>The label of the target SLA</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "agingObj.sla", "description": "<p>the object of the sla</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "agingObj.sla.targetFrom", "description": "<p>: Start value of the sla</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "agingObj.sla.targetTo", "description": "<p>: End value of the sla</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "agingObj.sla.targetLabel", "description": "<p>: Label of the sla</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "agingObj.sla.targetLevel", "description": "<p>: Level of the sla</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "agingObj.sla.insertDate", "description": "<p>: Insert date of the sla</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n     \"agingObj\":{\n       \"calendarId\": 11,\n       \"calendarLabel\": \"H08-20_1-6\"\n       \"calendarDescription\": \"Mon-Sat 08-20\"\n       \"update\": true,\n       \"seconds\": 72,\n       \"interval\": \"+0 00:01\",\n       \"lastVarDate\": \"2015-04-17T10:57:35.*********+02:00\",\n       \"sla\": [\n         {\n           \"targetFrom\": 900,\n           \"targetTo\": null,\n           \"targetLabel\": \"Soglia allarme\",\n           \"targetLevel\": \"DANGER\",\n           \"InsertDate\": \"2018-06-29T15:27:07.*********+02:00\"\n         }\n       ]\n     },\n     \"owner\" : \"ROOT\",\n     \"ownerObj\": {\n       \"email\": null,\n       \"firstName\": \"ROOT\",\n       \"disabled\": false,\n       \"serviceUser\": false,\n       \"groups\": [\n         \"ROOT\"\n       ],\n       \"mobilePhone\": null,\n       \"lastName\": \"ROOT\",\n       \"username\": \"ROOT\"\n     },\n     \"currentUser\" : null,\n     \"currentUserObj\": null,\n     \"lastVarDate\" : \"2015-04-17T10:57:35.*********+02:00\",\n     \"systemType\" : \"PTE_MANAGEMENT\",\n     \"description\" : \"IC1\",\n     \"properties\" : [\n       \"SUPERVISOR\",\n       \"FTE\",\n       \"ESTIMATED_EFFORT_OUTLOOK\"\n     ],\n     \"status\" : \"OPEN\",\n     \"creationDate\" : \"2015-04-17T10:57:35.*********+02:00\",\n     \"closureDate\" : \"2015-04-17T10:57:35.*********+02:00\",\n     \"isClosed\" : true,\n     \"parentId\" : null,\n     \"dateNextStep\" : \"2015-04-17T10:57:35.*********+02:00\",\n     \"type\" : \"EPEL_IC\",\n     \"systemId\" : 2096,\n     \"children\" : [],\n     \"isLocked\" : true,\n     \"isLockedByMyself\": false,\n\t \"active\": true,\n     \"lastTransitionId\": 10,\n     \"lastUpdateId\": 20,\n     \"lastUpdateTimestamp\": \"2015-04-17T10:57:35.*********+02:00\",\n     \"version\": 10,\n     \"creationUser\": \"ROOT\",\n     \"creationUserObj\": {\n       \"email\": null,\n       \"firstName\": \"ROOT\",\n       \"disabled\": false,\n       \"serviceUser\": false,\n       \"groups\": [\n         \"ROOT\"\n       ],\n       \"mobilePhone\": null,\n       \"lastName\": \"ROOT\",\n       \"username\": \"ROOT\"\n     }\n}", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/activities/5704/info\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity"}, {"type": "put", "url": "/api/art/activities/:ID/lock", "title": "Lock Activity", "name": "activityLock", "group": "Activity", "description": "<p>Lock Activity</p> ", "examples": [{"title": "Example usage:", "content": "PUT http://localhost/api/art/activities/2104/lock", "type": "HTTP"}], "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 201 Created", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity", "header": {"fields": {"Header": [{"group": "Header", "type": "String", "optional": false, "field": "Content-Type", "description": "<p>It must be set to <code>application/json</code>.</p> "}]}}}, {"type": "get", "url": "/api/art/activities/:ID/properties", "title": "Properties", "name": "activityProperties", "group": "Activity", "description": "<p>Returns an object representing the properties of the activity, where the keys are the NAMEs of the properties.</p> ", "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "String", "optional": false, "field": "NAME", "description": "<p>The value of the property named NAME.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n   \"SUPERVISOR\" : \"a.l<PERSON><PERSON><PERSON>@sirti.it\"\n}", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/activities/5711/properties\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity"}, {"type": "delete", "url": "/api/art/activities/:ID/lock", "title": "Unlock Activity", "name": "activityUnlock", "group": "Activity", "description": "<p>Unlock Activity</p> ", "examples": [{"title": "Example usage:", "content": "DELETE http://localhost/api/art/activities/2104/lock", "type": "HTTP"}], "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 204 No content", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity", "header": {"fields": {"Header": [{"group": "Header", "type": "String", "optional": false, "field": "Content-Type", "description": "<p>It must be set to <code>application/json</code>.</p> "}]}}}, {"type": "put", "url": "/api/art/activities/:ID/activityProperties", "title": "Upsert Activity Properties", "name": "activityUpsertProperties", "group": "Activity", "description": "<p>Upsert activity properties. <strong>Note</strong>: you can update only properties defined as Activity Properties</p> ", "examples": [{"title": "Example usage:", "content": "PUT http://localhost/api/art/activities/2104/activityProperties\nContent-Type: application/json\n\n{\n  \"properties\" : {\n  \t\"SUPERVISOR\" : \"a.l<PERSON><PERSON><PERSON>@sirti.it\",\n  \t\"IC_NUMBER\" : null\n  }\n}", "type": "HTTP"}], "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "Object", "optional": false, "field": "properties", "description": "<p>An object representing the properties, where the keys are the NAMEs of the properties.    To delete a property set the value to <code>null</code></p> "}, {"group": "Parameter", "type": "String", "optional": false, "field": "properties.NAME", "description": "<p>The name of the property.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "description", "description": "<p>Description of the action.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "date", "defaultValue": "Now()", "description": "<p>Operation date - <strong><em> NYI </em></strong></p> "}]}}, "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 204 No content", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity", "header": {"fields": {"Header": [{"group": "Header", "type": "String", "optional": false, "field": "Content-Type", "description": "<p>It must be set to <code>application/json</code>.</p> "}]}}}, {"type": "post", "url": "/activities/:ID/attachments", "title": "Add activity attachments", "name": "addActivityAttachments", "group": "Activity", "description": "<p>Add activity attachments</p> ", "examples": [{"title": "Example usage:", "content": "POST /api/art/activities/5711/attachments HTTP/1.1\nContent-Type: application/json", "type": "HTTP"}], "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String[]", "optional": true, "field": "attachments", "description": "<p>Id of temporary file to attach to the activity. The file must have been uploaded with   <code>POST /api/art/tmpfiles</code> previously.</p> "}]}}, "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 204 No content", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity", "header": {"fields": {"Header": [{"group": "Header", "type": "String", "optional": false, "field": "Content-Type", "description": "<p>It must be set to <code>application/json</code>.</p> "}]}}}, {"type": "get", "url": "/api/art/activities/:ID/attachments", "title": "Attachment list", "name": "attachmentList", "group": "Activity", "description": "<p>Get activity attachment list, where <code>ID</code> is the id of the activity.</p> ", "examples": [{"title": "Example usage:", "content": "GET /api/art/activities/6151/attachments HTTP/1.1", "type": "HTTP"}], "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous", "description": "<p>Object rappresenting the attachment.</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "anonymous.transitionId", "description": "<p>The id of the transition of the attachment.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.transitionDate", "description": "<p>The date of the transition of the attachment.</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "anonymous.sequence", "description": "<p>The ordinal number of the attachment.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.fileName", "description": "<p>The name of the attachment.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.title", "description": "<p>The title of the attachment (can be null).</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.description", "description": "<p>The description of the attachment (can be null).</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.revision", "description": "<p>The revision of the attachment (can be null).</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.refDate", "description": "<p>The reference date of the attachment in the format YYYY-MM-DD (can be null).</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.docType", "description": "<p>The document type of the attachment (can be null).</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "anonymous.size", "description": "<p>The size of the attachment.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.owner", "description": "<p>The username of the user that uploaded the attachment.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.ownerFirstName", "description": "<p>The firstname of the user that uploaded the attachment.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.ownerLastName", "description": "<p>The lastname of the user that uploaded the attachment.</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "anonymous.downloadCount", "description": "<p>The number of times the hattachment has been downloaded (can be <code>null</code>).</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n  {\n    \"owner\":\"ROOT\",\n    \"sequence\":0,\n    \"fileName\":\"SCARTI%20NPD2%20NOTIFICA_VS_DONOR_DONATING%20del%2029122014.xls\",\n    \"transitionDate\":\"2015-05-20T11:04:11.*********+02:00\",\n    \"transitionId\":20150520110411,\n    \"title\":null,\n    \"description\":null,\n    \"revisione\":null,\n    \"refDate\":null,\n    \"docType\":null,\n    \"size\":84480,\n    \"downloadCount\":null,\n    \"ownerFirstName\":\"ROOT\",\n    \"ownerLastName\":\"ROOT\"\n  },\n  {\n    \"owner\":\"ROOT\",\n    \"sequence\":1,\n    \"fileName\":\"TL22013032007.xml\",\n    \"transitionDate\":\"2015-05-20T11:04:11.*********+02:00\",\n    \"transitionId\":20150520110411,\n    \"title\":null,\n    \"description\":null,\n    \"revisione\":null,\n    \"refDate\":null,\n    \"docType\":null,\n    \"size\":7109,\n    \"downloadCount\":null,\n    \"ownerFirstName\":\"ROOT\",\n    \"ownerLastName\":\"ROOT\"\n  }\n]", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity"}, {"type": "options", "url": "/activities/:ID/attachments", "title": "Check methods attachments", "name": "checkMethodsAttachment", "group": "Activity", "description": "<p>Check the methods availabe for attachments, where <code>ID</code> is the id of the activity.</p> ", "examples": [{"title": "Example usage:", "content": "OPTIONS /api/art/activities/6151/attachments HTTP/1.1", "type": "HTTP"}], "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\nAllow: POST,OPTIONS", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity"}, {"type": "options", "url": "/api/art/activities/:ID/history/:TRANSITION_ID/attachments/:SEQUENCE", "title": "Check methods attachment", "name": "checkMethodsTransitionAttachment", "group": "Activity", "description": "<p>Check the methods availabe for transition attachment, where <code>ID</code> is the id of the activity, <code>TRANSITION_ID</code> is the id of the transition (see <strong>Activity - History</strong>)   and <code>SEQUENCE</code> is the nth zero-based file attached to the step.</p> ", "examples": [{"title": "Example usage:", "content": "OPTIONS /api/art/activities/6151/history/20150520110411/attachments/0 HTTP/1.1", "type": "HTTP"}], "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\nAllow: GET,DELETE,OPTIONS", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity"}, {"type": "post", "url": "/api/art/activities", "title": "Create", "name": "createActivity", "group": "Activity", "description": "<p>Create activity</p> ", "examples": [{"title": "Example usage:", "content": "POST /api/art/activities HTTP/1.1\nContent-Type: application/json\nAccept: application/json\n\n{\n  \"type\" : \"EPEL_IC\",\n  \"description\" : \"IC1\",\n  \"systemId\": 2104, \n  \"properties\" : {\n    \"SUPERVISOR\" : \"a.<PERSON><PERSON><PERSON><PERSON>@sirti.it\",\n    \"IC_NUMBER\" : \"75884\"\n  }\n}", "type": "HTTP"}], "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String", "optional": false, "field": "type", "description": "<p>Type of the activity to create.</p> "}, {"group": "Parameter", "type": "String", "optional": false, "field": "description", "description": "<p>Description of the activity.</p> "}, {"group": "Parameter", "type": "Number", "optional": true, "field": "systemId", "description": "<p>Id of the system to create the activity on. If you don&#39;t specify this param, you must   use <code>system</code> param.</p> "}, {"group": "Parameter", "type": "Object", "optional": true, "field": "system", "description": "<p>New system where the activity will be created on. If you don&#39;t specify this param, you must   use <code>systemId</code> param.</p> "}, {"group": "Parameter", "type": "String", "optional": false, "field": "system.type", "description": "<p>Type of the system to create.</p> "}, {"group": "Parameter", "type": "String", "optional": false, "field": "system.objectType", "description": "<p>Object Type of the system to create.</p> "}, {"group": "Parameter", "type": "String", "optional": false, "field": "system.description", "description": "<p>Description of the system.</p> "}, {"group": "Parameter", "type": "String[]", "optional": false, "field": "system.groups", "description": "<p>Groups that must have visibility on the just created system.</p> "}, {"group": "Parameter", "type": "Object", "optional": true, "field": "system.properties", "description": "<p>An object representing the properties, where the keys are the NAMEs of the properties.</p> "}, {"group": "Parameter", "type": "String", "optional": false, "field": "system.properties.NAME", "description": "<p>The name of the property.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "system.date", "defaultValue": "Now()", "description": "<p>Creation date - <strong><em> NYI </em></strong></p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "attachments", "description": "<p>Id of temporary file to attach to the activity. The file must have been uploaded with   <code>POST /api/art/tmpfiles</code> previously.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "date", "defaultValue": "Now()", "description": "<p>Operation date - <strong><em> NYI </em></strong></p> "}, {"group": "Parameter", "type": "Object", "optional": true, "field": "properties", "description": "<p>An object representing the transition properties, where the keys are the NAMEs of the properties.</p> "}, {"group": "Parameter", "type": "String", "optional": false, "field": "properties.NAME", "description": "<p>The name of the transition property.</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Number", "optional": false, "field": "id", "description": "<p>Id of the activity created.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"id\" : 31722454\n}", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity", "header": {"fields": {"Header": [{"group": "Header", "type": "String", "optional": false, "field": "Content-Type", "description": "<p>It must be set to <code>application/json</code>.</p> "}]}}}, {"type": "delete", "url": "/api/art/activities/:ID/history/:TRANSITION_ID/attachments/:SEQUENCE", "title": "Delete attachment", "name": "deleteTransitionAttachment", "group": "Activity", "description": "<p>Delete transition attachment, where <code>ID</code> is the id of the activity, <code>TRANSITION_ID</code> is the id of the transition (see <strong>Activity - History</strong>)   and <code>SEQUENCE</code> is the nth zero-based file attached to the step.</p> ", "examples": [{"title": "Example usage:", "content": "DELETE /api/art/activities/6151/history/20150520110411/attachments/0 HTTP/1.1", "type": "HTTP"}], "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 204 No content", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity"}, {"type": "get", "url": "/api/art/activities/:ID/history/:TRANSITION_ID/attachments/:SEQUENCE", "title": "Download attachment", "name": "downloadTransitionAttachment", "group": "Activity", "description": "<p>Download transition attachment, where <code>ID</code> is the id of the activity, <code>TRANSITION_ID</code> is the id of the transition (see <strong>Activity - History</strong>)   and <code>SEQUENCE</code> is the nth zero-based file attached to the step.</p> ", "examples": [{"title": "Example usage:", "content": "GET /api/art/activities/6151/history/20150520110411/attachments/0 HTTP/1.1", "type": "HTTP"}], "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "File", "optional": false, "field": "anonymous", "description": "<p>The binary file.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\nContent-Disposition: attachment; filename=\"Requirements - rev.0.04.docx\"\nContent-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document\nContent-Length: 19472", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity"}, {"type": "get", "url": "/api/art/activities", "title": "Find", "name": "getActivities", "group": "Activity", "description": "<p>Returns an array of objects describing the activities matching the specified criteria.</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "cache", "allowedValues": ["1", "0"], "optional": true, "field": "cache", "defaultValue": "1", "description": "<p>If <code>0</code> instead of returning cached activities, it returns &quot;fresh&quot; activities</p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "type", "description": "<p>Filter activities of these types. Overwritten by param type_*</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "type_equal", "description": "<p>Filter activities of one type.</p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "type_in", "description": "<p>Filter activities given a list of types.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "type_begins", "description": "<p>Filter activities with type name that begins with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "type_ends", "description": "<p>Filter activities with type name that ends with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "type_contains", "description": "<p>Filter activities with type name that contains with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "id_equal", "description": "<p>Filter activities given an id.</p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "id_in", "description": "<p>Filter activities given a list of ids.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "id_begins", "description": "<p>Filter activities with id that begins with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "id_ends", "description": "<p>Filter activities with id that ends with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "id_contains", "description": "<p>Filter activities with id that contains with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "status_equal", "description": "<p>Filter activities in one status.</p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "status_in", "description": "<p>Filter activities given a list of status.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "status_begins", "description": "<p>Filter activities with status that begins with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "status_ends", "description": "<p>Filter activities with status that ends with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "status_contains", "description": "<p>Filter activities with stuats that contains with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "description_equal", "description": "<p>Filter activities with a defined description.</p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "description_in", "description": "<p>Filter activities given a list of descriptions.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "description_begins", "description": "<p>Filter activities with description that begins with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "description_ends", "description": "<p>Filter activities with description that ends with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "description_contains", "description": "<p>Filter activities with description that contains with passed value.</p> "}, {"group": "Parameter", "type": "Number[]", "optional": true, "field": "systemId", "description": "<p>Filter activities created on these systems.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "systemType_equal", "description": "<p>Filter activities with system of given type.</p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "systemType_in", "description": "<p>Filter activities given a list of system types.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "systemType_begins", "description": "<p>Filter activities with system type name that begins with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "systemType_ends", "description": "<p>Filter activities with system type name that ends with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "systemType_contains", "description": "<p>Filter activities system with type name that contains with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "systemDescription_equal", "description": "<p>Filter activities given a system description.</p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "systemDescription_in", "description": "<p>Filter activities given a list of system descriptions.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "systemDescription_begins", "description": "<p>Filter activities with system description that begins with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "systemDescription_ends", "description": "<p>Filter activities with system description that ends with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "systemDescription_contains", "description": "<p>Filter activities with system description that contains with passed value.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1", "-1"], "optional": true, "field": "active", "defaultValue": "0", "description": "<p><code>0</code> all activities, <code>1</code> only the tickets in a NON final status, <code>-1</code> only the tickets in a final status.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "showOnlyWithVisibility", "defaultValue": "0", "description": "<p><code>0</code> all activities: some element can be null if the user has not visibility on the activity, <code>1</code> only the tickets that user can view.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "assignedToMe", "description": "<p><code>0</code> all activities, <code>1</code> only the tickets that user is assignee</p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "assignedTo", "description": "<p>Filter activities given a list of assignees.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "createdByMe", "description": "<p><code>0</code> all activities, <code>1</code> only the tickets created by the user</p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "created<PERSON>y", "description": "<p>Filter activities given a list of creation users.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "caseSensitive", "defaultValue": "1", "description": "<p><code>0</code> execute the search in non-CASE_SENSITIVE mode, <code>1</code> execute the search in CASE_SENSITIVE mode.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "ap_NAME_equal", "description": "<p>Where NAME is the name of the property to filter. Can be repeated for more then one property name. An exact search of the value will be done.</p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "ap_NAME_in", "description": "<p>Where NAME is the name of the property to filter. Can be repeated for more then one property name. An exact search of the given list of the values will be done.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "ap_NAME_begins", "description": "<p>Where NAME is the name of the property to filter. Filter activities with property that begins with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "ap_NAME_ends", "description": "<p>Where NAME is the name of the property to filter. Filter activities with property that ends with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "ap_NAME_contains", "description": "<p>Where NAME is the name of the property to filter. Filter activities with property that contains with passed value.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["1"], "optional": true, "field": "ap_NAME_isNull", "description": "<p>Where NAME is the name of the property to filter. A search of the NAME with value <code>null</code> will be done.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["1"], "optional": true, "field": "ap_NAME_isNotNull", "description": "<p>Where NAME is the name of the property to filter. A search of the NAME with value <code>not null</code> will be done.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "includeSystem", "defaultValue": "0", "description": "<p>Include complete information about system.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "availableActions", "defaultValue": "0", "description": "<p>Include the available actions for current status and user profile.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "excludeInfo", "defaultValue": "0", "description": "<p>Exclude activity info.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "excludeProperties", "defaultValue": "0", "description": "<p>Exclude activity properties.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "sp_NAME_equal", "description": "<p>Where NAME is the name of the system property to filter. Can be repeated for more then one system property name. An exact search of the value will be done.</p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "sp_NAME_in", "description": "<p>Where NAME is the name of the system property to filter. Can be repeated for more then one system property name. An exact search of the given list of the values will be done.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "sp_NAME_begins", "description": "<p>Where NAME is the name of the system property to filter. Filter activities with system property that begins with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "sp_NAME_ends", "description": "<p>Where NAME is the name of the system property to filter. Filter activities system with property that ends with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "sp_NAME_contains", "description": "<p>Where NAME is the name of the system property to filter. Filter activities with system property that contains with passed value.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["1"], "optional": true, "field": "sp_NAME_isNull", "description": "<p>Where NAME is the name of the system property to filter. A search of the NAME with value <code>null</code> will be done.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["1"], "optional": true, "field": "sp_NAME_isNotNull", "description": "<p>Where NAME is the name of the system property to filter. A search of the NAME with value <code>not null</code> will be done.</p> "}, {"group": "Parameter", "type": "Miscellaneous", "optional": true, "field": "parent_UPLEVEL_KEY", "description": "<p>Using these keys you can filter over parent activity.<br>  <code>UPLEVEL</code> specifies how many   levels the search must go up  (for example: <code>1</code> mean <em>father</em>, <code>2</code> mean <em>grandfather</em>, etc.).<br>  <code>KEY</code> specifies the kind of filter to apply: you can use one of the filter previously defined for the activity.<br>  As an example: for finding an activity whom the current status of the father is <code>IN_PROGRESS</code> you must add in the query   string <code>parent_1_status_equal=IN_PROGRESS</code>.</p> "}, {"group": "Parameter", "type": "String[]", "allowedValues": ["[-]id", "[-]type", "[-]systemType", "[-]systemDescription", "[-]creationDate", "[-]description", "[-]status", "[-]lastVarDate", "[-]ap_NAME}", "[-]sp_NAME}", "[-]parent_UPLEVEL_KEY"], "optional": true, "field": "sort", "description": "<p>If present, it define sort fields. If the field is prefixed by <code>-</code> the sort order is descending.  example:</p> <pre><code>sort=type&amp;sort=-id&amp;sort=-ap_COMPETENCE_CENTER </code></pre>"}, {"group": "Parameter", "type": "Number", "optional": true, "field": "limit", "description": "<p>Maximum number of results</p> "}, {"group": "Parameter", "type": "Number", "optional": true, "field": "skip", "description": "<p>Skip the first <code>skip</code> records from the the results set</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Number", "optional": false, "field": "id", "description": "<p>The id of the activity found.</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "info", "description": "<p>The info of the activity found in the same format returned by API <strong>Activity - Info</strong> <code>GET /api/art/activities/:ID/info</code>.</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "properties", "description": "<p>The properties of the activity found in the same format returned by API <strong>Activity - Properties</strong> <code>GET /api/art/activities/:ID/properties</code>.</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "system", "description": "<p>The system object in the same format returned by API <strong>System - Find</strong> <code>GET /api/art/systems</code>. It&#39;s present only if param <code>includeSystem=1</code></p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "availableActions", "description": "<p>An array with the available actions for current status and user profile. It&#39;s present only if param <code>includeAvailableActions=1</code></p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n   {\n      \"id\" : 5699,\n      \"info\" : {\n         \"owner\" : \"ROOT\",\n         \"ownerObj\": {\n           \"email\": null,\n           \"firstName\": \"ROOT\",\n           \"disabled\": false,\n           \"serviceUser\": false,\n           \"groups\": [\n             \"ROOT\"\n           ],\n           \"mobilePhone\": null,\n           \"lastName\": \"ROOT\",\n           \"username\": \"ROOT\"\n         },\n         \"currentUser\" : null,\n         \"currentUserObj\": null,\n         \"lastVarDate\" : \"2015-04-17T10:57:35.*********+02:00\",\n         \"systemType\" : \"PTE_MANAGEMENT\",\n         \"description\" : \"IC1\",\n         \"properties\" : [\n           \"SUPERVISOR\",\n           \"FTE\",\n           \"ESTIMATED_EFFORT_OUTLOOK\"\n         ],\n         \"status\" : \"OPEN\",\n         \"creationDate\" : \"2015-04-17T10:57:35.*********+02:00\",\n         \"parentId\" : null,\n         \"dateNextStep\" : \"2015-04-17T10:57:35.*********+02:00\",\n         \"type\" : \"EPEL_IC\",\n         \"systemId\" : 2096,\n         \"children\" : [],\n         \"isLocked\" : true,\n         \"isLockedByMyself\": false,\n\t\t \"active\": true,\n\t\t \"lastTransitionId\": 10,\n\t\t \"lastUpdateId\": 20,\n\t\t \"lastUpdateTimestamp\": \"2015-04-17T10:57:35.*********+02:00\",\n\t\t \"version\": 10,\n         \"creationUser\": \"ROOT\",\n         \"creationUserObj\": {\n           \"email\": null,\n           \"firstName\": \"ROOT\",\n           \"disabled\": false,\n           \"serviceUser\": false,\n           \"groups\": [\n             \"ROOT\"\n           ],\n           \"mobilePhone\": null,\n           \"lastName\": \"ROOT\",\n           \"username\": \"ROOT\"\n         }\n      },\n      \"properties\" : {\n         \"ESTIMATED_EFFORT_OUTLOOK\" : \"75\",\n         \"FTE\" : null,\n         \"SUPERVISOR\" : \"<EMAIL>\"\n      }\n   }\n]", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/activities?ap_ID_RISORSA_in=U002313254&ap_DN_eqaul=**********&ap_ID_ULLCO_like=C%00008734&type=ULL_LINEA_NON_ATTIVA&type=ULL_LINEA_ATTIVA_NP\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity"}, {"type": "get", "url": "/api/art/activities", "title": "Find Extended", "name": "getActivitiesExtended", "group": "Activity", "description": "<p>Find activities extended. It accepts all the parameters of API <strong>Activity - Find</strong>, plus the parameter <code>extendedOutput</code>.</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "Number", "allowedValues": ["1", "0"], "optional": false, "field": "extendedOutput", "description": "<p>If <code>1</code> instead of returning an array of objects describing the activities, it returns an hash: the key <code>results</code> contains an array of activity objects,  the key <code>count</code> contains the total count of the results excluding limit and skip parameters. If <code>0</code> has the same behaviour of API <strong>Activity - Find</strong></p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "onlyCount", "description": "<p><code>0</code> returns an hash with the keys <code>count</code> e <code>results</code>, <code>1</code> returns an hash with only the key <code>count</code>.</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous", "description": "<p>Object rappresenting the search</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "anonymous.count", "description": "<p>The total count of the activities found.</p> "}, {"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous.results", "description": "<p>Objects of the activities found.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"count\":69,\n  \"results\":[\n    {\n      \"info\":{\n         \"owner\" : \"ROOT\",\n         \"ownerObj\": {\n           \"email\": null,\n           \"firstName\": \"ROOT\",\n           \"disabled\": false,\n           \"serviceUser\": false,\n           \"groups\": [\n             \"ROOT\"\n           ],\n           \"mobilePhone\": null,\n           \"lastName\": \"ROOT\",\n           \"username\": \"ROOT\"\n         },\n         \"agingObj\":{\n           \"calendarId\": 11,\n           \"calendarLabel\": \"H08-20_1-6\"\n           \"calendarDescription\": \"Mon-Sat 08-20\"\n           \"update\": true,\n           \"seconds\": 72,\n           \"interval\": \"+0 00:01\",\n           \"lastVarDate\": \"2015-04-17T10:57:35.*********+02:00\",\n           \"sla\": [\n             {\n               \"targetFrom\": 900,\n               \"targetTo\": null,\n               \"targetLabel\": \"Soglia allarme\",\n               \"targetLevel\": \"DANGER\",\n               \"InsertDate\": \"2018-06-29T15:27:07.*********+02:00\"\n             }\n           ]\n         },\n         \"currentUser\" : null,\n         \"currentUserObj\": null,\n         \"lastVarDate\" : \"2015-04-17T10:57:35.*********+02:00\",\n         \"systemType\" : \"PTE_MANAGEMENT\",\n         \"description\" : \"IC1\",\n         \"properties\" : [\n           \"SUPERVISOR\",\n           \"FTE\",\n           \"ESTIMATED_EFFORT_OUTLOOK\"\n         ],\n         \"status\" : \"OPEN\",\n         \"creationDate\" : \"2015-04-17T10:57:35.*********+02:00\",\n         \"parentId\" : null,\n         \"dateNextStep\" : \"2015-04-17T10:57:35.*********+02:00\",\n         \"type\" : \"EPEL_IC\",\n         \"systemId\" : 2096,\n         \"children\" : [],\n         \"isLocked\" : true,\n         \"isLockedByMyself\": false,\n\t\t \"active\": true,\n\t\t \"lastTransitionId\": 10,\n\t\t \"lastUpdateId\": 20,\n\t\t \"lastUpdateTimestamp\": \"2015-04-17T10:57:35.*********+02:00\",\n\t\t \"version\": 10,\n         \"creationUser\": \"ROOT\",\n         \"creationUserObj\": {\n           \"email\": null,\n           \"firstName\": \"ROOT\",\n           \"disabled\": false,\n           \"serviceUser\": false,\n           \"groups\": [\n             \"ROOT\"\n           ],\n           \"mobilePhone\": null,\n           \"lastName\": \"ROOT\",\n           \"username\": \"ROOT\"\n         }\n      },\n      \"id\":6042,\n      \"properties\":{\n        \"INCREMENTAL_EFFORT\":null,\n        \"RESOURCE\":\"<EMAIL>\",\n        \"RESOURCE_ENGAGE_SHORT_DESC\":\"short desc\",\n        \"COMPETENCE_CENTER_MANAGER\":\"<EMAIL>\",\n        \"SCOPE\":\"PTE_MANAGEMENT\",\n        \"REASON\":\"errore\",\n        \"SUPERVISOR\":\"<EMAIL>\",\n        \"RESOURCE_ENGAGE_START_DATE\":\"2015-04-16T00:00:00.*********+02:00\",\n        \"RESOURCE_ENGAGE_END_DATE\":\"2015-04-16T00:00:00.*********+02:00\",\n        \"CATEGORY_LIST\":\"[\\\"pippo\\\",\\\"pluto\\\"]\",\n        \"ACCOUNTED_EFFORT\":\"25\",\n        \"ENGAGE_DESCRIPTION\":\"Preparazione documento tecnico\",\n        \"RESOURCE_LIST\":\"[\\\"<EMAIL>\\\"]\",\n        \"RESOURCE_LIST_BL\":\"[]\",\n        \"ESTIMATED_EFFORT_OUTLOOK\":\"640\",\n        \"RESOURCE_ENGAGE_DESC\":\"desc\",\n        \"TOTAL_ESTIMATED_EFFORT\":\"45.5\",\n        \"ID_EPRL\":\"6052\",\n        \"ESTIMATED_EFFORT\":\"45.5\",\n        \"END_DATE\":\"2015-05-28T22:00:00.000Z\",\n        \"RESOURCE_REVOKE\":\"<EMAIL>\",\n        \"TOTAL_ACCOUNTED_EFFORT\":\"25\",\n        \"PROJECT_COMMESSA\":\"COMMESSA_5041\",\n        \"START_DATE\":\"2015-05-03T22:00:00.000Z\",\n        \"RESOURCE_LIST_OG\":\"[\\\"<EMAIL>\\\"]\"\n      }\n    }\n  ]\n}", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/activities?extendedOutput=1&ap_ID_RISORSA_in=U002313254&ap_DN_equal=**********&type=ULL_LINEA_ATTIVA_NP\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity"}, {"type": "get", "url": "/api/art/activities", "title": "Find for export", "name": "getActivitiesForExport", "group": "Activity", "description": "<p>Find activities for export. It accepts all the parameters of API <strong>Activity - Find</strong>, plus the parameter <code>export</code>.</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String", "allowedValues": ["xlsx", "csv"], "optional": false, "field": "export", "description": "<p>Set the format of the file to download</p> <p>NOTE: Param <code>type</code>, <code>id_equal</code> or <code>type_equal</code> is mandatory</p> "}]}}, "examples": [{"title": "Example usage:", "content": "GET /api/art/activities?export=xlsx&type_equal=NETWORK", "type": "HTTP"}], "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "File", "optional": false, "field": "anonymous", "description": "<p>The binary file.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\nContent-Disposition: attachment; filename=\"export_20190401172524.xlsx\"\nContent-Length: 34114\nContent-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity"}, {"type": "get", "url": "/api/art/activities", "title": "Find GridOutput", "name": "getActivitiesGridOutput", "group": "Activity", "description": "<p>Find activities extended. It accepts all the parameters of API <strong>Activity - Find</strong>, plus the parameter <code>gridOutput</code>.</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "Number", "allowedValues": ["1", "0"], "optional": false, "field": "gridOutput", "description": "<p>If <code>1</code> instead of returning an array of objects describing the activities, it returns an hash: the key <code>results</code> contains an array of activity objects,  the key <code>count</code> contains the total count of the results excluding limit and skip parameters. If <code>0</code> has the same behaviour of API <strong>Activity - Find</strong></p> <p>NOTE: Param <code>type</code>, <code>id_equal</code> or <code>type_equal</code> is mandatory</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous", "description": "<p>Object rappresenting the search</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "anonymous.count", "description": "<p>The total count of the activities found.</p> "}, {"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous.results", "description": "<p>Objects of the activities found.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"count\": 1,\n  \"results\": {\n    \"data\": [\n      {\n        \"[\\\"info\\\"][\\\"creationDate\\\"]\": \"2018-10-31T13:33:53.*********+01:00\",\n        \"[\\\"properties\\\"][\\\"networkId\\\"]\": \"000290fjd909\"\n      }\n    ],\n    \"header\": [\n      {\n        \"name\": \"DATA_CREAZIONE\",\n        \"type\": \"date\",\n        \"hidden\": false,\n        \"checkboxFilter\": false,\n        \"id\": \"[\\\"info\\\"][\\\"creationDate\\\"]\"\n      },\n      {\n        \"name\": \"networkId\",\n        \"type\": \"string\",\n        \"hidden\": false,\n        \"checkboxFilter\": false,\n        \"id\": \"[\\\"properties\\\"][\\\"networkId\\\"]\"\n      }\n    ]\n  }\n}", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/activities?gridOutput=1&type_equal=NETWORK&ap_ID_RISORSA_in=U002313254&ap_DN_equal=**********\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity"}, {"type": "get", "url": "/api/art/activities", "title": "Find ids", "name": "getActivitiesIds", "group": "Activity", "description": "<p>Find activities ids. It accepts all the parameters of API <strong>Activity - Find</strong>, plus the parameter <code>onlyids</code>.</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "Number", "allowedValues": ["1", "0"], "optional": false, "field": "onlyids", "description": "<p>If <code>1</code> instead of returning an array of objects describing the activities, it returns an array of activity ids,   if <code>0</code> has the same behaviour of API <strong>Activity - Find</strong></p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Number[]", "optional": false, "field": "id", "description": "<p>Ids of the activities found.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n  1356,\n  9281\n]", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/activities?onlyids=1&ap_ID_RISORSA_in=U002313254&ap_DN_equal=**********&type=ULL_LINEA_ATTIVA_NP\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity"}, {"type": "get", "url": "/api/art/activities", "title": "Find ids extended", "name": "getActivitiesIdsExtended", "group": "Activity", "description": "<p>Find activities ids extended. It accepts all the parameters of API <strong>Activity - Find Ids</strong>, plus the parameter <code>extendedOutput</code>.</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "Number", "allowedValues": ["1", "0"], "optional": false, "field": "extendedOutput", "description": "<p>If <code>1</code> instead of returning an array of ids describing the activities, it returns an hash: the key <code>results</code> contains an array of activity ids,  the key <code>count</code> contains the total count of the results excluding limit and skip parameters. If <code>0</code> has the same behaviour of API <strong>Activity - Find Ids</strong></p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "onlyCount", "description": "<p><code>0</code> returns an hash with the keys <code>count</code> e <code>results</code>, <code>1</code> returns an hash with only the key <code>count</code>.</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous", "description": "<p>Object rappresenting the search</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "anonymous.count", "description": "<p>The total count of the activities found.</p> "}, {"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous.results", "description": "<p>Ids of the activities found.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"count\":69,\n  \"results\":[\n    \"6042\",\n    \"6043\"\n  ]\n}", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/activities?onlyids=1&extendedOutput=1&ap_DN_equal=**********&type=ULL_LINEA_ATTIVA_NP\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity"}, {"type": "get", "url": "/activities/:ID", "title": "Get", "name": "getActivity", "group": "Activity", "description": "<p>Returns an object describing the activity.</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "includeSystem", "defaultValue": "0", "description": "<p>Include complete information about system.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "includeAvailableActions", "defaultValue": "0", "description": "<p>Include the available actions for current status and user profile.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "excludeInfo", "defaultValue": "0", "description": "<p>Exclude activity info.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "excludeProperties", "defaultValue": "0", "description": "<p>Exclude activity properties.</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Number", "optional": false, "field": "id", "description": "<p>The id of the activity found.</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "info", "description": "<p>The info of the activity found in the same format returned by API <strong>Activity - Info</strong> <code>GET /api/art/activities/:ID/info</code>. Available only if param <code>excludeInfo=0</code> (default).</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "properties", "description": "<p>The properties of the activity found in the same format returned by API <strong>Activity - Properties</strong> <code>GET /api/art/activities/:ID/properties</code>. Available only if param <code>excludeProperties=0</code> (default).</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "system", "description": "<p>The system object in the same format returned by API <strong>System - Find</strong> <code>GET /api/art/systems</code>. Available only if param <code>includeSystem=1</code></p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "availableActions", "description": "<p>An array with the available actions for current status and user profile. Available only if param <code>includeAvailableActions=1</code></p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"info\": {\n     \"owner\" : \"ROOT\",\n     \"ownerObj\": {\n       \"email\": null,\n       \"firstName\": \"ROOT\",\n       \"disabled\": false,\n       \"serviceUser\": false,\n       \"groups\": [\n         \"ROOT\"\n       ],\n       \"mobilePhone\": null,\n       \"lastName\": \"ROOT\",\n       \"username\": \"ROOT\"\n     },\n     \"currentUser\" : null,\n     \"currentUserObj\": null,\n     \"lastVarDate\" : \"2015-04-17T10:57:35.*********+02:00\",\n     \"systemType\" : \"PTE_MANAGEMENT\",\n     \"description\" : \"IC1\",\n     \"properties\" : [\n       \"SUPERVISOR\",\n       \"FTE\",\n       \"ESTIMATED_EFFORT_OUTLOOK\"\n     ],\n     \"attachments\": [],\n     \"status\" : \"OPEN\",\n     \"creationDate\" : \"2015-04-17T10:57:35.*********+02:00\",\n     \"parentId\" : null,\n     \"dateNextStep\" : \"2015-04-17T10:57:35.*********+02:00\",\n     \"type\" : \"EPEL_IC\",\n     \"systemId\" : 2096,\n     \"children\" : [],\n     \"isLocked\" : true,\n     \"isLockedByMyself\": false,\n\t \"active\": true,\n     \"lastTransitionId\": 10,\n     \"lastUpdateId\": 20,\n     \"lastUpdateTimestamp\": \"2015-04-17T10:57:35.*********+02:00\",\n     \"version\": 10,\n     \"creationUser\": \"ROOT\",\n     \"creationUserObj\": {\n       \"email\": null,\n       \"firstName\": \"ROOT\",\n       \"disabled\": false,\n       \"serviceUser\": false,\n       \"groups\": [\n         \"ROOT\"\n       ],\n       \"mobilePhone\": null,\n       \"lastName\": \"ROOT\",\n       \"username\": \"ROOT\"\n     }\n  },\n  \"system\": {\n    \"info\": {\n      \"disabled\": false,\n      \"disablingDate\": null,\n      \"endingDate\": null,\n      \"active\": true,\n      \"description\": \"**********\",\n      \"properties\": [\n        \"CUSTOMER\",\n        \"IC_DESCRIPTION\",\n        \"INITIATIVE_NUMBER\",\n        \"PROJECT_COMMESSA\"\n      ],\n      \"ended\": false,\n      \"creationDate\": \"2015-06-03T08:33:00.*********+02:00\",\n      \"groups\": [\n        \"ADMIN\",\n        \"EPEL_MANAGER\",\n        \"GUEST\",\n        \"ROOT\",\n        \"SUPERVISOR_AD-IIN\"\n      ],\n      \"type\": \"PTE_MANAGEMENT\"\n    },\n    \"id\": 3165,\n    \"properties\": {\n      \"INITIATIVE_NUMBER\": null,\n      \"PROJECT_COMMESSA\": null,\n      \"CUSTOMER\": null,\n      \"IC_DESCRIPTION\": null\n    }\n  },\n  \"id\": 6505,\n  \"properties\": {\n    \"FLAG_ATI\": null,\n    \"CC\": null,\n    \"ID_EPCCL\": null,\n    \"IC_TECHNICAL_COORDINATOR\": \"<EMAIL>\",\n    \"INCREMENTAL_EFFORT\": null,\n    \"IC_TYPE\": null,\n    \"OUTCOME_QUARTER\": null,\n    \"TRANCHE\": null,\n    \"TRANCHE_DESCRIPTION\": null,\n    \"CIG\": null,\n    \"FINAL_CUSTOMER\": null,\n    \"3RDP\": null,\n    \"COMPANY\": null,\n    \"BID_DEADLINE_PRESENTATION\": null,\n    \"INCREMENTAL_ACCOUNTED_EFFORT\": null,\n    \"CUSTOMER\": \"myself\",\n    \"IC_TECHNICAL_COORDINATOR_NAME\": \"BELOTTI, RIZZARDO\",\n    \"PROJECT_COMMESSA_LIST\": [\n      \"76484 - ATTIVITÀ PROPOSITIVA PER OP E GC CS (12647)\",\n      \"76486 - ATTIVITÀ PROPOSITIVA PER RFI E ITALFERR (12649)\",\n      \"59123 - A2M:MANUT.FIL.LECCE (12393)\"\n    ],\n    \"ID_EPCCL_3RDP_REVOKE\": null,\n    \"REASON\": null,\n    \"CC_SYSTEM_ID\": null,\n    \"SUPERVISOR\": \"<EMAIL>\",\n    \"SUPERVISOR_NAME\": \"BELOTTI, RIZZARDO\",\n    \"INCREMENTAL_ESTIMATED_EFFORT\": null,\n    \"IC_DESCRIPTION\": \"test mail technical coordinator\",\n    \"COMPETENCE_CENTER_LIST\": [\n      \"CC-TEST1\"\n    ],\n    \"3RDP_EMAIL\": null,\n    \"DEPARTMENT\": null,\n    \"IC_NUMBER\": \"**********\",\n    \"ID_EPCCL_3RDP\": null,\n    \"CATEGORY_LIST\": [\n      \"Illuminazione\",\n      \"Impianti Elettrici\",\n      \"IMPIANTI SPECIALI\"\n    ],\n    \"ACCOUNTED_EFFORT\": null,\n    \"ENGAGE_DESCRIPTION\": null,\n    \"ESTIMATED_EFFORT_OUTLOOK\": null,\n    \"3RDP_LIST\": [\n      \"pippo\"\n    ],\n    \"CUP\": null,\n    \"ID_EPCCL_REVOKE\": null,\n    \"ESTIMATED_EFFORT\": null,\n    \"TOTAL_ESTIMATED_EFFORT\": null,\n    \"FLAG_SECRET\": null,\n    \"IC_PARENT\": null,\n    \"IC_OUTCOME_PERIOD\": null,\n    \"END_DATE\": null,\n    \"IC_AGGREGATED_STATUS\": null,\n    \"IC_SALES_FORCE_LINK\": null,\n    \"COMPETENCE_CENTER\": null,\n    \"IC_STATUS\": null,\n    \"TOTAL_ACCOUNTED_EFFORT\": null,\n    \"IC_RECORD_TYPE\": null,\n    \"PROJECT_COMMESSA\": null,\n    \"EXECUTIVE_MANAGEMENT\": null,\n    \"START_DATE\": null,\n    \"PROJECT_COMMESSA_SYSTEM_ID\": null,\n    \"TOTAL_ESTIMATED_EFFORT_OUTLOOK\": null,\n    \"IC_OWNER\": null,\n    \"INCREMENTAL_EST_EFF_OUTLOOK\": null\n  }\n}", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/activities/1234\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity"}, {"type": "get", "url": "/api/art/activities/:ID/children", "title": "Find children", "name": "getActivityChildren", "group": "Activity", "description": "<p>Returns an array of objects describing the children activities matching the specified criteria.</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String[]", "optional": true, "field": "type", "description": "<p>Filter activities of these types.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "includeSystem", "defaultValue": "0", "description": "<p>Include complete information about system.</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Number", "optional": false, "field": "id", "description": "<p>The id of the activity found.</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "info", "description": "<p>The info of the activity found in the same format returned by API <strong>Activity - Info</strong> <code>GET /api/art/activities/:ID/info</code>.</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "properties", "description": "<p>The properties of the activity found in the same format returned by API <strong>Activity - Properties</strong> <code>GET /api/art/activities/:ID/properties</code>.</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "system", "description": "<p>The system object in the same format returned by API <strong>System - Find</strong> <code>GET /api/art/systems</code>. It&#39;s present only if param <code>includeSystem=1</code></p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n   {\n      \"id\" : 5699,\n      \"info\" : {\n        \"owner\" : \"ROOT\",\n        \"ownerObj\": {\n          \"email\": null,\n          \"firstName\": \"ROOT\",\n          \"disabled\": false,\n          \"serviceUser\": false,\n          \"groups\": [\n            \"ROOT\"\n          ],\n          \"mobilePhone\": null,\n          \"lastName\": \"ROOT\",\n          \"username\": \"ROOT\"\n        },\n        \"currentUser\" : null,\n        \"currentUserObj\": null,\n        \"lastVarDate\" : \"2015-04-17T10:57:35.*********+02:00\",\n        \"systemType\" : \"PTE_MANAGEMENT\",\n        \"description\" : \"IC1\",\n        \"properties\" : [\n          \"SUPERVISOR\",\n          \"FTE\",\n          \"ESTIMATED_EFFORT_OUTLOOK\"\n        ],\n        \"status\" : \"OPEN\",\n        \"creationDate\" : \"2015-04-17T10:57:35.*********+02:00\",\n        \"parentId\" : null,\n        \"dateNextStep\" : \"2015-04-17T10:57:35.*********+02:00\",\n        \"type\" : \"EPEL_IC\",\n        \"systemId\" : 2096,\n        \"children\" : [],\n        \"isLocked\" : true,\n        \"isLockedByMyself\": false,\n\t\t\"active\": true,\n        \"lastTransitionId\": 10,\n        \"lastUpdateId\": 20,\n        \"lastUpdateTimestamp\": \"2015-04-17T10:57:35.*********+02:00\",\n        \"version\": 10,\n        \"creationUser\": \"ROOT\",\n        \"creationUserObj\": {\n          \"email\": null,\n          \"firstName\": \"ROOT\",\n          \"disabled\": false,\n          \"serviceUser\": false,\n          \"groups\": [\n            \"ROOT\"\n          ],\n          \"mobilePhone\": null,\n          \"lastName\": \"ROOT\",\n          \"username\": \"ROOT\"\n        }\n      },\n      \"properties\" : {\n         \"ESTIMATED_EFFORT_OUTLOOK\" : \"75\",\n         \"FTE\" : null,\n         \"SUPERVISOR\" : \"<EMAIL>\"\n      },\n      \"isLocked\" : false,\n      \"isLockedByMyself\" : false,\n\t  \"active\" : true,\n      \"lastTransitionId\": 10,\n      \"lastUpdateId\": 20,\n      \"lastUpdateTimestamp\": \"2015-04-17T10:57:35.*********+02:00\",\n      \"version\": 10\n   }\n]", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/activities/5884/children/?type=EPCCL\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity"}, {"type": "get", "url": "/activities/:ID/hierarchy", "title": "Get Hierarchy", "name": "getActivityHierarchy", "group": "Activity", "description": "<p>Return a recursive object describing the tree of the activity hierarchy</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "includeSystem", "defaultValue": "0", "description": "<p>Include complete information about system.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "includeAvailableActions", "defaultValue": "0", "description": "<p>Include the available actions for current status and user profile.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "excludeInfo", "defaultValue": "0", "description": "<p>Exclude activity info.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "excludeProperties", "defaultValue": "0", "description": "<p>Exclude activity properties.</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object", "optional": false, "field": "activity", "description": "<p>The activity in the same format returned by API <strong>Activity - Get</strong> <code>GET /api/art/activities/:ID</code>.</p> "}, {"group": "Success 200", "type": "Object[]", "optional": false, "field": "children", "description": "<p>An array of recursive objects of this type</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"children\": [\n    {\n      \"children\": [],\n      \"activity\": {\n        \"id\": 6750\n      }\n    },\n    {\n      \"children\": [\n      {\n        \"children\": [],\n        \"activity\": {\n          \"id\": 6804\n        }\n      },\n      {\n        \"children\": [],\n        \"activity\": {\n          \"id\": 6809\n        }\n      }\n    ],\n      \"activity\": {\n        \"id\": 6702\n      }\n    },\n    {\n      \"children\": [],\n      \"activity\": {\n        \"id\": 6701\n      }\n    }\n  ],\n  \"activity\": {\n    \"id\": 6693\n  }\n}", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/activities/6693/hierarchy?excludeInfo=1&excludeProperties=1\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity"}, {"type": "get", "url": "/api/art/activities/:ID/activityProperties", "title": "Activity properties", "name": "getActivityProperties", "group": "Activity", "description": "<p>Return the activity properties, showing the organization of the groups of them.</p> ", "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous", "description": "<p>Object representing the activity properties group</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.group", "description": "<p>Name of the group</p> "}, {"group": "Success 200", "type": "Number", "optional": true, "field": "anonymous.id", "description": "<p>Id of the group</p> "}, {"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous.properties", "description": "<p>Object representing the activity property</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.properties.name", "description": "<p>Name of the property</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.properties.value", "description": "<p>Value of the property</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.properties.notYetUsed", "description": "<p><code>false</code> if the activity made a step where the property is defined, <code>true</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.properties.readOnly", "description": "<p><code>true</code> if the property is readOnly, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.properties.expired", "description": "<p><code>false</code> if the property is no longer associated to at least an action of the activity type, <code>true</code> otherwise.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n  {\n    \"group\":\"ENGAGEMENT_DETAILS\",\n    \"id\":\"1\",\n    \"properties\":[\n      {\n        \"notYetUsed\":false,\n        \"expired\":false,\n        \"value\":\"<EMAIL>\",\n        \"name\":\"COMPETENCE_CENTER_MANAGER\",\n        \"readOnly\":true\n      },\n      {\n        \"notYetUsed\":false,\n        \"expired\":false,\n        \"value\":\"200\",\n        \"name\":\"ESTIMATED_EFFORT_OUTLOOK\",\n        \"readOnly\":true\n      },\n      {\n        \"notYetUsed\":false,\n        \"expired\":false,\n        \"value\":\"<EMAIL>\",\n        \"name\":\"SUPERVISOR\",\n        \"readOnly\":true\n      },\n      {\n        \"notYetUsed\":false,\n        \"expired\":false,\n        \"value\":\"76477\",\n        \"name\":\"PROJECT_COMMESSA\",\n        \"readOnly\":true\n      }\n    ]\n  },\n  {\n    \"group\":\"CATEGORIZATION\",\n    \"id\":\"3\",\n    \"properties\":[\n      {\n        \"notYetUsed\":false,\n        \"expired\":false,\n        \"value\":\"[\\\"Proposing\\\"]\",\n        \"name\":\"CATEGORY_LIST\",\n        \"readOnly\":false\n      }\n    ]\n  },\n  {\n    \"group\":\"WIP\",\n    \"id\":\"2\",\n    \"properties\":[\n      {\n        \"notYetUsed\":false,\n        \"expired\":false,\n        \"value\":\"[\\\"<EMAIL>\\\",\\\"<EMAIL>\\\",\\\"<EMAIL>\\\"]\",\n        \"name\":\"RESOURCE_LIST\",\n        \"readOnly\":true\n      },\n      {\n        \"notYetUsed\":false,\n        \"expired\":false,\n        \"value\":\"[\\\"<EMAIL>\\\"]\",\n        \"name\":\"RESOURCE_LIST_BL\",\n        \"readOnly\":true\n      },\n      {\n        \"notYetUsed\":false,\n        \"expired\":false,\n        \"value\":\"[\\\"<EMAIL>\\\",\\\"<EMAIL>\\\"]\",\n        \"name\":\"RESOURCE_LIST_OG\",\n        \"readOnly\":true\n      },\n      {\n        \"notYetUsed\":false,\n        \"expired\":false,\n        \"value\":\"226\",\n        \"name\":\"TOTAL_ESTIMATED_EFFORT\",\n        \"readOnly\":false\n      },\n      {\n        \"notYetUsed\":false,\n        \"expired\":false,\n        \"value\":\"169\",\n        \"name\":\"TOTAL_ACCOUNTED_EFFORT\",\n        \"readOnly\":false\n      }\n    ]\n  }\n]", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/activities/2573/activityProperties\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity"}, {"type": "post", "url": "/api/art/activities/:ID/history", "title": "Step", "name": "stepActivity", "group": "Activity", "description": "<p>Step activity</p> ", "examples": [{"title": "Example usage:", "content": "POST /api/art/activities/5711/history HTTP/1.1\nContent-Type: application/json\n\n{\n  \"action\" : \"INITIALIZE\",\n  \"description\" : \"step!\",\n  \"destUser\": \"root\", \n  \"properties\" : {\n    \"SUPERVISOR\" : \"a.<PERSON><PERSON><PERSON><PERSON>@sirti.it\",\n    \"IC_NUMBER\" : \"75884\"\n  }\n}", "type": "HTTP"}], "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String", "optional": false, "field": "action", "description": "<p>Name of the action.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "description", "defaultValue": "Azione", "description": "<p>eseguita con &#39;NOME_SCRIPT&#39;] Description of the step.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "destUser", "description": "<p>The username of the user to whom the activity must be assigned.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "assignNotification", "defaultValue": "0", "description": "<p>If <code>1</code> the user to whom the activity must be assigned will receive a notification email.</p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "attachments", "description": "<p>Id of temporary file to attach to the activity. The file must have been uploaded with   <code>POST /api/art/tmpfiles</code> previously.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "saveAllProperties", "defaultValue": "1", "description": "<p>If <code>0</code> the properties that reamain unchanged after the step aren&#39;t saved.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "ignoreFinalStatus", "defaultValue": "0", "description": "<p>If <code>1</code> the checks about the possibility to close the activity are skipped.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "virtual", "defaultValue": "0", "description": "<p>If <code>1</code> execute a virtual action.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "destStatus", "description": "<p>The destination status of the action: ignored if the action is not a virtual action.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "date", "defaultValue": "Now()", "description": "<p>Operation date - <strong><em> NYI </em></strong></p> "}, {"group": "Parameter", "type": "Object", "optional": true, "field": "properties", "description": "<p>An object representing the transition properties, where the keys are the NAMEs of the properties.</p> "}, {"group": "Parameter", "type": "String", "optional": false, "field": "properties.NAME", "description": "<p>The name of the transition property.</p> "}]}}, "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 204 No content", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Activity", "header": {"fields": {"Header": [{"group": "Header", "type": "String", "optional": false, "field": "Content-Type", "description": "<p>It must be set to <code>application/json</code>.</p> "}]}}}, {"type": "get", "url": "/dashboards/:OBJECT_TYPE/:TYPE", "title": "Configuration", "name": "dashboardsConfig", "group": "Dashboards", "description": "<p>Get dashboard config for :OBJECT_TYPE and :TYPE</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "tracking", "defaultValue": "0", "description": "<p><code>1</code> created by the user, <code>0</code> all activities.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "mine", "defaultValue": "0", "description": "<p><code>1</code> owner by the user, <code>0</code> all activities.</p> "}]}}, "examples": [{"title": "Example usage:", "content": "GET /dashboards/activities/WO HTTP/1.1", "type": "HTTP"}], "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Number", "optional": false, "field": "count", "description": "<p>The count of the activity.</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "context", "description": "<p>The &#39;id&#39; context</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "label", "description": "<p>The label for the content</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n  {\n    \"count\": \"20\",\n    \"context\": \"01_pre_tec\",\n    \"label\": \"Prenalisi Tecnica\"\n  },\n  {\n    \"count\": \"15\",\n    \"context\": \"02_block\",\n    \"label\": \"Attivita' in attesa di sblocco\"\n  }\n]", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Dashboards"}, {"type": "get", "url": "/dashboards/:OBJECT_TYPE/:TYPE/:CONTEXT", "title": "Data", "name": "dashboardsData", "group": "Dashboards", "description": "<p>Get dashboard data for :OBJECT_TYPE, :TYPE and :CONTEXT</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "tracking", "defaultValue": "0", "description": "<p><code>1</code> created by the user, <code>0</code> all activities.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "mine", "defaultValue": "0", "description": "<p><code>1</code> owner by the user, <code>0</code> all activities.</p> "}, {"group": "Parameter", "type": "String[]", "allowedValues": ["[-]id", "[-]type", "[-]creationDate", "[-]disablingDate", "[-]endingDate", "[-]description", "[-]sp_NAME"], "optional": true, "field": "sort", "description": "<p>If present, it define sort fields. If the field is prefixed by <code>-</code> the sort order is descending.</p> <p> example:</p> <pre><code>sort=type&amp;sort=-id&amp;sort=-sp_COMPETENCE_CERTER </code></pre>"}, {"group": "Parameter", "type": "Number", "optional": true, "field": "limit", "description": "<p>Maximum number of results</p> "}, {"group": "Parameter", "type": "Number", "optional": true, "field": "skip", "description": "<p>Skip the first <code>skip</code> records from the the results set</p> "}]}}, "examples": [{"title": "Example usage:", "content": "GET /dashboards/activities/WO/01_pre_tec HTTP/1.1", "type": "HTTP"}], "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Number", "optional": false, "field": "count", "description": "<p>The total count of the objects found</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "results", "description": "<p>Results</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "results.data", "description": "<p>Data in SIRTI::Reports Extended format</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "results.header", "description": "<p>Header in SIRTI::Reports Extended format</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"count\":1,\n  \"results\":{\n    \"data\":[\n      {\n        \"info_creationDate_2\":\"2017-07-26T16:21:59.*********+02:00\",\n        \"properties_requestContext_19\":null,\n        \"info_agingObj_lastVarDate_7\":null,\n        \"properties_permitsAreaId_14\":\"3\",\n        \"properties_requestType_15\":\"As-Built\",\n        \"info_isLocked_12\":0,\n        \"info_description_1\":\"test\",\n        \"info_lastVarDate_4\":\"2017-09-26T16:11:48.*********+02:00\",\n        \"info_agingObj_seconds_6\":null,\n        \"info_creationUserObj_username_10\":\"ROOT\",\n        \"info_creationUserObj_fullName_3\":\"ROOT ROOT\",\n        \"info_currentUserObj_fullName_9\":\"ROOT ROOT\",\n        \"properties_authority_17\":\"Società GAS\",\n        \"properties_projectId_13\":\"862\",\n        \"info_currentUserObj_username_11\":\"ROOT\",\n        \"properties_requestor_18\":null,\n        \"info_interval_8\":\"+413 17:24\",\n        \"properties_asBuiltId_16\":null,\n        \"id_0\":5739,\n        \"info_ownerObj_fullName_5\":\"ROOT ROOT\"\n      }\n    ],\n    \"header\":[\n      {\n        \"name\":\"Id\",\n        \"position\":0,\n        \"path\":\"[\\\"id\\\"]\",\n        \"description\":\"Id\",\n        \"type\":\"number\",\n        \"hidden\":false,\n        \"checkboxFilter\":false,\n        \"id\":\"id_0\"\n      },\n      {\n        \"name\":\"Description\",\n        \"position\":1,\n        \"path\":\"[\\\"info\\\"][\\\"description\\\"]\",\n        \"description\":\"Description\",\n        \"type\":\"string\",\n        \"hidden\":false,\n        \"checkboxFilter\":false,\n        \"id\":\"info_description_1\"\n      },\n      {\n        \"name\":\"Creation Date\",\n        \"position\":2,\n        \"path\":\"[\\\"info\\\"][\\\"creationDate\\\"]\",\n        \"description\":\"Creation Date\",\n        \"type\":\"date\",\n        \"hidden\":false,\n        \"checkboxFilter\":false,\n        \"id\":\"info_creationDate_2\"\n      },\n      {\n        \"name\":\"Created By\",\n        \"position\":3,\n        \"path\":\"[\\\"info\\\"][\\\"creationUserObj\\\"][\\\"fullName\\\"]\",\n        \"description\":\"Created By\",\n        \"type\":\"string\",\n        \"hidden\":false,\n        \"checkboxFilter\":false,\n        \"id\":\"info_creationUserObj_fullName_3\"\n      },\n      {\n        \"name\":\"Last Modify Date\",\n        \"position\":4,\n        \"path\":\"[\\\"info\\\"][\\\"lastVarDate\\\"]\",\n        \"description\":\"Last Modify Date\",\n        \"type\":\"date\",\n        \"hidden\":false,\n        \"checkboxFilter\":false,\n        \"id\":\"info_lastVarDate_4\"\n      },\n      {\n        \"name\":\"Modified By\",\n        \"position\":5,\n        \"path\":\"[\\\"info\\\"][\\\"ownerObj\\\"][\\\"fullName\\\"]\",\n        \"description\":\"Modified By\",\n        \"type\":\"string\",\n        \"hidden\":false,\n        \"checkboxFilter\":false,\n        \"id\":\"info_ownerObj_fullName_5\"\n      },\n      {\n        \"name\":\"Aging\",\n        \"position\":6,\n        \"path\":\"[\\\"info\\\"][\\\"agingObj\\\"][\\\"seconds\\\"]\",\n        \"description\":\"Aging\",\n        \"type\":\"number\",\n        \"hidden\":false,\n        \"checkboxFilter\":false,\n        \"id\":\"info_agingObj_seconds_6\"\n      },\n      {\n        \"name\":\"Last aging update date\",\n        \"position\":7,\n        \"path\":\"[\\\"info\\\"][\\\"agingObj\\\"][\\\"lastVarDate\\\"]\",\n        \"description\":\"Last aging update date\",\n        \"type\":\"date\",\n        \"hidden\":false,\n        \"checkboxFilter\":false,\n        \"id\":\"info_agingObj_lastVarDate_7\"\n      },\n      {\n        \"name\":\"Interval\",\n        \"position\":8,\n        \"path\":\"[\\\"info\\\"][\\\"interval\\\"]\",\n        \"description\":\"Interval\",\n        \"type\":\"string\",\n        \"hidden\":false,\n        \"checkboxFilter\":false,\n        \"id\":\"info_interval_8\"\n      },\n      {\n        \"name\":\"Managed By\",\n        \"position\":9,\n        \"path\":\"[\\\"info\\\"][\\\"currentUserObj\\\"][\\\"fullName\\\"]\",\n        \"description\":\"Managed By\",\n        \"type\":\"string\",\n        \"hidden\":false,\n        \"checkboxFilter\":false,\n        \"id\":\"info_currentUserObj_fullName_9\"\n      },\n      {\n        \"name\":\"CreationUserLogin\",\n        \"position\":10,\n        \"path\":\"[\\\"info\\\"][\\\"creationUserObj\\\"][\\\"username\\\"]\",\n        \"description\":\"CreationUserLogin\",\n        \"type\":\"string\",\n        \"hidden\":true,\n        \"checkboxFilter\":false,\n        \"id\":\"info_creationUserObj_username_10\"\n      },\n      {\n        \"name\":\"CurrentUserLogin\",\n        \"position\":11,\n        \"path\":\"[\\\"info\\\"][\\\"currentUserObj\\\"][\\\"username\\\"]\",\n        \"description\":\"CurrentUserLogin\",\n        \"type\":\"string\",\n        \"hidden\":true,\n        \"checkboxFilter\":false,\n        \"id\":\"info_currentUserObj_username_11\"\n      },\n      {\n        \"name\":\"IsLocked\",\n        \"position\":12,\n        \"path\":\"[\\\"info\\\"][\\\"isLocked\\\"]\",\n        \"description\":\"IsLocked\",\n        \"type\":\"number\",\n        \"hidden\":true,\n        \"checkboxFilter\":false,\n        \"id\":\"info_isLocked_12\"\n      },\n      {\n        \"name\":\"projectId\",\n        \"position\":13,\n        \"path\":\"[\\\"properties\\\"][\\\"projectId\\\"]\",\n        \"description\":\"projectId\",\n        \"type\":\"string\",\n        \"hidden\":false,\n        \"checkboxFilter\":false,\n        \"id\":\"properties_projectId_13\"\n      },\n      {\n        \"name\":\"permitsAreaId\",\n        \"position\":14,\n        \"path\":\"[\\\"properties\\\"][\\\"permitsAreaId\\\"]\",\n        \"description\":\"permitsAreaId\",\n        \"type\":\"string\",\n        \"hidden\":false,\n        \"checkboxFilter\":false,\n        \"id\":\"properties_permitsAreaId_14\"\n      },\n      {\n        \"name\":\"requestType\",\n        \"position\":15,\n        \"path\":\"[\\\"properties\\\"][\\\"requestType\\\"]\",\n        \"description\":\"requestType\",\n        \"type\":\"string\",\n        \"hidden\":false,\n        \"checkboxFilter\":false,\n        \"id\":\"properties_requestType_15\"\n      },\n      {\n        \"name\":\"asBuiltId\",\n        \"position\":16,\n        \"path\":\"[\\\"properties\\\"][\\\"asBuiltId\\\"]\",\n        \"description\":\"asBuiltId\",\n        \"type\":\"string\",\n        \"hidden\":false,\n        \"checkboxFilter\":false,\n        \"id\":\"properties_asBuiltId_16\"\n      },\n      {\n        \"name\":\"authority\",\n        \"position\":17,\n        \"path\":\"[\\\"properties\\\"][\\\"authority\\\"]\",\n        \"description\":\"authority\",\n        \"type\":\"string\",\n        \"hidden\":false,\n        \"checkboxFilter\":false,\n        \"id\":\"properties_authority_17\"\n      },\n      {\n        \"name\":\"requestor\",\n        \"position\":18,\n        \"path\":\"[\\\"properties\\\"][\\\"requestor\\\"]\",\n        \"description\":\"requestor\",\n        \"type\":\"string\",\n        \"hidden\":false,\n        \"checkboxFilter\":false,\n        \"id\":\"properties_requestor_18\"\n      },\n      {\n        \"name\":\"requestContext\",\n        \"position\":19,\n        \"path\":\"[\\\"properties\\\"][\\\"requestContext\\\"]\",\n        \"description\":\"requestContext\",\n        \"type\":\"string\",\n        \"hidden\":false,\n        \"checkboxFilter\":false,\n        \"id\":\"properties_requestContext_19\"\n      }\n    ]\n  }\n}", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Dashboards"}, {"type": "get", "url": "/dashboards/:OBJECT_TYPE/:TYPE/:CONTEXT", "title": "Data for export", "name": "dashboardsDataForExport", "group": "Dashboards", "description": "<p>Get dashboard data for :OBJECT_TYPE, :TYPE and :CONTEXT for export. It accepts all the parameters of API <strong>Dashboards - Data</strong>, plus the parameter <code>export</code>.</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String", "allowedValues": ["xlsx", "csv"], "optional": false, "field": "export", "description": "<p>Set the format of the file to download</p> "}]}}, "examples": [{"title": "Example usage:", "content": "GET /dashboards/activities/WO/01_pre_tec?export=xlsx HTTP/1.1", "type": "HTTP"}], "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "File", "optional": false, "field": "anonymous", "description": "<p>The binary file.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\nContent-Disposition: attachment; filename=\"export_20190401172524.xlsx\"\nContent-Length: 34114\nContent-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Dashboards"}, {"type": "get", "url": "/api/art/instance/types/activities/:TYPE/properties", "title": "Activity type properties", "name": "instanceTypesActivitiesTypeProperties", "group": "Deprecated", "description": "<p>Return activity type properties</p> <p><strong>DEPRECATED</strong>: use route <strong>Activity properties</strong></p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String", "optional": true, "field": "action", "description": "<p>Returns the property informations suitable only for the action.</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous", "description": "<p>The property informations.</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "anonymous.NAME", "description": "<p>The NAME of the property.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.NAME.type", "description": "<p>The type of the property.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.NAME.label", "description": "<p>The label of the property.</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "anonymous.NAME.predefinedValue", "description": "<p>The predefined value of the property</p> "}, {"group": "Success 200", "type": "string", "optional": false, "field": "anonymous.NAME.predefinedValue.type", "description": "<p>The type of predefined value of the property</p> "}, {"group": "Success 200", "type": "string", "optional": true, "field": "anonymous.NAME.predefinedValue.value", "description": "<p>The value of predefined value of the property if applicable</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.hint", "description": "<p>The hint of the property</p> "}, {"group": "Success 200", "type": "Object[]", "optional": true, "field": "anonymous.NAME.values", "description": "<p>TBD</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.target", "description": "<p>TBD</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.template", "description": "<p>TBD</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.groupBy", "description": "<p>TBD</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.context", "description": "<p>TBD</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.contextKey", "description": "<p>TBD: defined only if context defined</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.displayProperty", "description": "<p>TBD: defined only if context defined</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.keyProperty", "description": "<p>TBD: defined only if context defined</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.minLength", "description": "<p>TBD: defined only if context defined</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.addFromAutocompleteOnly", "description": "<p>TBD: defined only if context defined</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.queryKey", "description": "<p>TBD: defined only if context defined</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.responseDataKey", "description": "<p>TBD: defined only if context defined</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"forecastEndDate\":{\n    \"predefinedValue\":null,\n    \"label\":\"Data prevista FL\",\n    \"type\":\"DAY\"\n  },\n  \"execProgRev\":{\n    \"predefinedValue\":{\n      \"value\":\"0\",\n      \"type\":\"const\"\n    },\n    \"label\":\"Revisione progetto esecutivo\",\n    \"type\":\"NUMBER\"\n  },\n  \"accountablePhoneNumber\":{\n    \"predefinedValue\":{\n      \"value\":\"[\\\"info\\\"][\\\"currentUserObj\\\"][\\\"mobilePhone\\\"]\",\n      \"type\":\"bracketNotation\"\n    },\n    \"label\":\"Telefono responsabile attività\",\n    \"type\":\"STRING\"\n  },\n  \"dateDocCIL\":{\n    \"predefinedValue\":{\n      \"type\":\"now\"\n    },\n    \"label\":\"Data emissione CIL\",\n    \"type\":\"DAY\"\n  }\n}", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/instance/types/activities/EPEL_IC/properties\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Deprecated"}, {"type": "post", "url": "/api/art/favorites", "title": "Create Favorite", "name": "createFavorites", "group": "Favorites", "description": "<p>Create new favorite</p> ", "examples": [{"title": "Example usage:", "content": "POST http://localhost/api/art/favorites\nContent-Type: application/json\nAccept: application/json\n\n{\n  \"description\" : \"DII\",\n  \"url\" : \"/artsoen/home\",\n  \"additionalKeys\" : {\n    \"contracts\" : [\"FTTH\",\"CREATION\"],\n    \"customer\": \"TIM\"\n  },\n  \n}", "type": "HTTP"}], "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String", "optional": false, "field": "description", "description": "<p>Description of the favorite to create.</p> "}, {"group": "Parameter", "type": "String", "optional": false, "field": "url", "description": "<p>Url of the favorite to create.</p> "}, {"group": "Parameter", "type": "Object", "optional": false, "field": "additionalKeys", "description": "<p>Hash with the addition keys for the favorite to create</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Number", "optional": false, "field": "id", "description": "<p>The id of the favorite created.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "description", "description": "<p>The description of the favorite created.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "url", "description": "<p>The URL of the favorite created.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "creationDate", "description": "<p>The creation date of the favorite created.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "lastUpdateDate", "description": "<p>The last update date of the favorite created.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "cancelDate", "description": "<p>The cancel date of the favorite created.</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "additionalKeys", "description": "<p>The additional keys of the favorite created.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 201 CREATED\n\n{\n  \"id\": 1,\n  \"cancelDate\": null,\n  \"lastUpdateDate\": \"2021-07-01T09:27:27.*********+02:00\",\n  \"creationDate\": \"2021-07-01T09:27:27.*********+02:00\",\n  \"url\": \"www.google.com\",\n  \"additionalKeys\": {\n    \"paperino\": [\n      \"qui\",\n      \"quo\",\n      \"qua\"\n    ],\n    \"pippo\": \"pluto\"\n  },\n  \"description\": \"test\"\n}", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Favorites"}, {"type": "del", "url": "/api/art/favorites/:ID", "title": "Delete Favorite", "name": "deleteFavorite", "group": "Favorites", "description": "<p>Delete favorite</p> ", "examples": [{"title": "Example usage:", "content": "DELETE http://localhost/api/art/favorites/1\nContent-Type: application/json", "type": "HTTP"}], "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"id\": 1,\n  cancelDate\": \"2021-07-01T09:57:27.*********+02:00\",\n  \"lastUpdateDate\": \"2021-07-01T09:27:27.*********+02:00\",\n  \"creationDate\": \"2021-07-01T09:27:27.*********+02:00\",\n  \"url\": \"www.google.com\",\n  \"additionalKeys\": {\n    \"paperino\": [\n      \"qui\",\n      \"quo\",\n      \"qua\"\n    ],\n    \"pippo\": \"pluto\"\n  },\n  \"description\": \"test\"\n}", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Favorites", "header": {"fields": {"Header": [{"group": "Header", "type": "String", "optional": false, "field": "Content-Type", "description": "<p>It must be set to <code>application/json</code>.</p> "}]}}}, {"type": "get", "url": "/api/art/favorites/:ID", "title": "Get Favorite", "name": "getFavorite", "group": "Favorites", "description": "<p>Get favorite</p> ", "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/favorites/:ID\nAccept: application/json", "type": "HTTP"}], "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"id\": 1,\n  cancelDate\": null,\n  \"lastUpdateDate\": \"2021-07-01T09:27:27.*********+02:00\",\n  \"creationDate\": \"2021-07-01T09:27:27.*********+02:00\",\n  \"url\": \"www.google.com\",\n  \"additionalKeys\": {\n    \"paperino\": [\n      \"qui\",\n      \"quo\",\n      \"qua\"\n    ],\n    \"pippo\": \"pluto\"\n  },\n  \"description\": \"test\"\n}", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Favorites"}, {"type": "get", "url": "/api/art/favorites", "title": "Get Favorites", "name": "getFavorites", "group": "Favorites", "description": "<p>Get favorites</p> ", "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/favorites\nAccept: application/json", "type": "HTTP"}], "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n  {\n    \"id\": 1,\n\t\"cancelDate\": null,\n    \"lastUpdateDate\": \"2021-07-01T09:27:27.*********+02:00\",\n    \"creationDate\": \"2021-07-01T09:27:27.*********+02:00\",\n    \"url\": \"www.google.com\",\n    \"additionalKeys\": {\n      \"paperino\": [\n        \"qui\",\n        \"quo\",\n        \"qua\"\n      ],\n      \"pippo\": \"pluto\"\n    },\n    \"description\": \"test\"\n  }\n]", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Favorites"}, {"type": "put", "url": "/api/art/favorites/:ID", "title": "Update Favorite", "name": "updateFavorite", "group": "Favorites", "description": "<p>Update favorite</p> ", "examples": [{"title": "Example usage:", "content": "PUT http://localhost/api/art/favorites/1\nContent-Type: application/json\n\n{\n  \"url\": \"www.sirti.it\",\n  \"description\": \"SIRTI\",\n  \"additionalKeys\": {}\n}", "type": "HTTP"}], "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String", "optional": false, "field": "description", "description": "<p>Description of the favorite to update.</p> "}, {"group": "Parameter", "type": "String", "optional": false, "field": "url", "description": "<p>Url of the favorite to update.</p> "}, {"group": "Parameter", "type": "Object", "optional": false, "field": "additionalKeys", "description": "<p>Hash with the addition keys for the favorite to update</p> "}]}}, "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 204 No content", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Favorites", "header": {"fields": {"Header": [{"group": "Header", "type": "String", "optional": false, "field": "Content-Type", "description": "<p>It must be set to <code>application/json</code>.</p> "}]}}}, {"type": "get", "url": "/api/art/instance/types/activityProperties/groups", "title": "Activity properties groups", "name": "instanceActivityPropertiesGroups", "group": "Instance", "description": "<p>Return the activity properties groups.</p> ", "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous", "description": "<p>Group informations</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "anonymous.id", "description": "<p>The id of the group.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.name", "description": "<p>The name of the group.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n  {\n    \"id\": 1,\n    \"name\": \"Gruppo 1\"\n  },\n  {\n    \"id\": 2,\n    \"name\": \"Gruppo 2\"\n  }\n]", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/instance/types/activityProperties/groups\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Instance"}, {"type": "put", "url": "/api/art/instance/users/:user/groups/:group", "title": "Add User Group", "name": "instanceAddUserGroup", "group": "Instance", "description": "<p>Add user group</p> ", "examples": [{"title": "Example usage:", "content": "PUT http://localhost/api/art/instance/users/LIVRAGH/groups/AT\nContent-Type: application/json\nAccept: application/json\n\n{\n}", "type": "HTTP"}], "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "String", "optional": false, "field": "username", "description": "<p>The username of the user logged in.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "admin", "description": "<p><code>true</code> if the user is an admin, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "userAdmin", "description": "<p><code>true</code> if the user is an user admin, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "root", "description": "<p><code>true</code> if the user is an user root, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "firstName", "description": "<p>The fist name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "lastName", "description": "<p>The last name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "fullName", "description": "<p>The full name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "email", "description": "<p>The email address of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "mobilePhone", "description": "<p>The mobile phone number of the user logged in.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "groups", "description": "<p>The groups the user belongs to.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "roles", "description": "<p>The roles the user belongs to.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "serviceUser", "description": "<p><code>true</code> if the user is a service user, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "disabled", "description": "<p><code>true</code> if the user is a disabled user, <code>false</code> otherwise.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"username\": \"<PERSON><PERSON><PERSON>\",\n  \"admin\" : false,\n  \"userAdmin\" : false,\n  \"root\" : false,\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"fullName\": \"<PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"mobilePhone\": null,\n  \"groups\": [\n    \"USER\",\n    \"PM\"\n  ],\n  \"roles\": [\n    \"AREA_MANAGER\"\n  ],\n  \"serviceUser\" : false,\n  \"disabled\"\t: false,\n}", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Instance"}, {"type": "put", "url": "/api/art/instance/users/:user/roles/:role", "title": "Add User Role", "name": "instanceAddUserRole", "group": "Instance", "description": "<p>Add user role</p> ", "examples": [{"title": "Example usage:", "content": "PUT http://localhost/api/art/instance/users/LIVAGH/roles/AT\nContent-Type: application/json\nAccept: application/json\n\n{\n}", "type": "HTTP"}], "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "String", "optional": false, "field": "username", "description": "<p>The username of the user logged in.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "admin", "description": "<p><code>true</code> if the user is an admin, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "userAdmin", "description": "<p><code>true</code> if the user is an user admin, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "root", "description": "<p><code>true</code> if the user is an user root, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "firstName", "description": "<p>The fist name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "lastName", "description": "<p>The last name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "fullName", "description": "<p>The full name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "email", "description": "<p>The email address of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "mobilePhone", "description": "<p>The mobile phone number of the user logged in.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "groups", "description": "<p>The groups the user belongs to.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "roles", "description": "<p>The roles the user belongs to.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "serviceUser", "description": "<p><code>true</code> if the user is a service user, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "disabled", "description": "<p><code>true</code> if the user is a disabled user, <code>false</code> otherwise.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"username\": \"<PERSON><PERSON><PERSON>\",\n  \"admin\" : false,\n  \"userAdmin\" : false,\n  \"root\" : false,\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"fullName\": \"<PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"mobilePhone\": null,\n  \"groups\": [\n    \"USER\",\n    \"PM\"\n  ],\n  \"roles\": [\n    \"AREA_MANAGER\"\n  ],\n  \"serviceUser\" : false,\n  \"disabled\"\t: false,\n}", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Instance"}, {"type": "post", "url": "/api/art/instance/users", "title": "Create User", "name": "instanceCreateUser", "group": "Instance", "description": "<p>Create new user</p> ", "examples": [{"title": "Example usage:", "content": "POST http://localhost/api/art/instance/users\nContent-Type: application/json\nAccept: application/json\n\n{\n  \"username\" : \"DII\",\n  \"firstName\" : \"DII\",\n  \"lastName\" : \"PTE_Management IC1\",\n  \"groups\" : [\"ADMIN\",\"PM1\",\"PM\"],\n  \"password\": \"pippo\"\n}", "type": "HTTP"}], "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String", "optional": false, "field": "username", "description": "<p>Username of the user to create.</p> "}, {"group": "Parameter", "type": "String", "optional": false, "field": "password", "description": "<p>Password of the user to create.</p> "}, {"group": "Parameter", "type": "String", "optional": false, "field": "firstName", "description": "<p>First Name of the user to create.</p> "}, {"group": "Parameter", "type": "String", "optional": false, "field": "lastName", "description": "<p>Last Name of the user to create.</p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "groups", "description": "<p>Groups of the user to create. You can use it unless you use <code>roles</code> or <code>roleAliases</code></p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "roles", "description": "<p>Roles of the user to create. You can use it unless you use <code>groups</code> or <code>roleAliases</code></p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "roleAliases", "description": "<p>Aliases of the roles of the user to create. You can use it unless you use <code>roles</code> or <code>groups</code></p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "email", "description": "<p>The email of the user to create.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "mobilePhone", "description": "<p>The mobile phone of the user to create.</p> "}, {"group": "Parameter", "type": "Boolean", "optional": true, "field": "serviceUser", "defaultValue": "true", "description": "<p><true> if the user to create must be a serviceUser, <false>otherwise</false></p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "String", "optional": false, "field": "username", "description": "<p>The username of the user logged in.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "admin", "description": "<p><code>true</code> if the user is an admin, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "userAdmin", "description": "<p><code>true</code> if the user is an user admin, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "root", "description": "<p><code>true</code> if the user is an user root, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "firstName", "description": "<p>The fist name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "lastName", "description": "<p>The last name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "fullName", "description": "<p>The full name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "email", "description": "<p>The email address of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "mobilePhone", "description": "<p>The mobile phone number of the user logged in.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "groups", "description": "<p>The groups the user belongs to.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "roles", "description": "<p>The roles the user belongs to.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "serviceUser", "description": "<p><code>true</code> if the user is a service user, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "disabled", "description": "<p><code>true</code> if the user is a disabled user, <code>false</code> otherwise.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 201 CREATED\n\n{\n  \"username\": \"<PERSON><PERSON><PERSON>\",\n  \"admin\" : false,\n  \"userAdmin\" : false,\n  \"root\" : false,\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"fullName\": \"<PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"mobilePhone\": null,\n  \"groups\": [\n    \"USER\",\n    \"PM\"\n  ],\n  \"roles\": [\n    \"AREA_MANAGER\"\n  ],\n  \"serviceUser\" : false,\n  \"disabled\"\t: false,\n}", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Instance"}, {"type": "del", "url": "/api/art/instance/users/:user/groups/:group", "title": "Delete User Group", "name": "instanceDeleteUserGroup", "group": "Instance", "description": "<p>Delete user group</p> ", "examples": [{"title": "Example usage:", "content": "DELETE http://localhost/api/art/instance/users/LIVRAGH/groups/AT\nContent-Type: application/json\nAccept: application/json\n\n{\n}", "type": "HTTP"}], "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "String", "optional": false, "field": "username", "description": "<p>The username of the user logged in.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "admin", "description": "<p><code>true</code> if the user is an admin, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "userAdmin", "description": "<p><code>true</code> if the user is an user admin, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "root", "description": "<p><code>true</code> if the user is an user root, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "firstName", "description": "<p>The fist name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "lastName", "description": "<p>The last name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "fullName", "description": "<p>The full name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "email", "description": "<p>The email address of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "mobilePhone", "description": "<p>The mobile phone number of the user logged in.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "groups", "description": "<p>The groups the user belongs to.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "roles", "description": "<p>The roles the user belongs to.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "serviceUser", "description": "<p><code>true</code> if the user is a service user, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "disabled", "description": "<p><code>true</code> if the user is a disabled user, <code>false</code> otherwise.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"username\": \"<PERSON><PERSON><PERSON>\",\n  \"admin\" : false,\n  \"userAdmin\" : false,\n  \"root\" : false,\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"fullName\": \"<PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"mobilePhone\": null,\n  \"groups\": [\n    \"USER\",\n    \"PM\"\n  ],\n  \"roles\": [\n    \"AREA_MANAGER\"\n  ],\n  \"serviceUser\" : false,\n  \"disabled\"\t: false,\n}", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Instance"}, {"type": "del", "url": "/api/art/instance/users/:user/roles/:role", "title": "Delete User Role", "name": "instanceDeleteUserRole", "group": "Instance", "description": "<p>Delete user role</p> ", "examples": [{"title": "Example usage:", "content": "DELETE http://localhost/api/art/instance/users/LIVRAGH/roles/AT\nContent-Type: application/json\nAccept: application/json\n\n{\n}", "type": "HTTP"}], "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "String", "optional": false, "field": "username", "description": "<p>The username of the user logged in.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "admin", "description": "<p><code>true</code> if the user is an admin, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "userAdmin", "description": "<p><code>true</code> if the user is an user admin, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "root", "description": "<p><code>true</code> if the user is an user root, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "firstName", "description": "<p>The fist name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "lastName", "description": "<p>The last name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "fullName", "description": "<p>The full name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "email", "description": "<p>The email address of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "mobilePhone", "description": "<p>The mobile phone number of the user logged in.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "groups", "description": "<p>The groups the user belongs to.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "roles", "description": "<p>The roles the user belongs to.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "serviceUser", "description": "<p><code>true</code> if the user is a service user, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "disabled", "description": "<p><code>true</code> if the user is a disabled user, <code>false</code> otherwise.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"username\": \"<PERSON><PERSON><PERSON>\",\n  \"admin\" : false,\n  \"userAdmin\" : false,\n  \"root\" : false,\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"fullName\": \"<PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"mobilePhone\": null,\n  \"groups\": [\n    \"USER\",\n    \"PM\"\n  ],\n  \"roles\": [\n    \"AREA_MANAGER\"\n  ],\n  \"serviceUser\" : false,\n  \"disabled\"\t: false,\n}", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Instance"}, {"type": "get", "url": "/api/art/instance/authenticatedUser", "title": "Get Authenticated User", "name": "instanceGetAuthenticatedUser", "group": "Instance", "description": "<p>Return info about users</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "Number", "allowedValues": ["1", "0"], "optional": true, "field": "includeToken", "defaultValue": "0", "description": "<p>If <code>1</code> the session token is also returned. The output format is the same as <a href=\"#api-Session-Login\">Session - Login</a></p> "}]}}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/instance/authenticatedUser\nAccept: application/json", "type": "HTTP"}], "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "String", "optional": false, "field": "username", "description": "<p>The username of the user logged in.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "admin", "description": "<p><code>true</code> if the user is an admin, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "userAdmin", "description": "<p><code>true</code> if the user is an user admin, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "root", "description": "<p><code>true</code> if the user is an user root, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "firstName", "description": "<p>The fist name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "lastName", "description": "<p>The last name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "fullName", "description": "<p>The full name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "email", "description": "<p>The email address of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "mobilePhone", "description": "<p>The mobile phone number of the user logged in.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "groups", "description": "<p>The groups the user belongs to.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "roles", "description": "<p>The roles the user belongs to.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "serviceUser", "description": "<p><code>true</code> if the user is a service user, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "disabled", "description": "<p><code>true</code> if the user is a disabled user, <code>false</code> otherwise.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"username\": \"<PERSON><PERSON><PERSON>\",\n  \"admin\" : false,\n  \"userAdmin\" : false,\n  \"root\" : false,\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"fullName\": \"<PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"mobilePhone\": null,\n  \"groups\": [\n    \"USER\",\n    \"PM\"\n  ],\n  \"roles\": [\n    \"AREA_MANAGER\"\n  ],\n  \"serviceUser\" : false,\n  \"disabled\"\t: false,\n}", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Instance"}, {"type": "get", "url": "/api/art/instance/groups", "title": "Get Groups", "name": "instanceGetGroups", "group": "Instance", "description": "<p>Return info about groups</p> ", "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/instance/groups\nAccept: application/json", "type": "HTTP"}], "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous", "description": "<p>The group informations.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.id", "description": "<p>The id of the group.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.label", "description": "<p>The description of the group.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.admin", "description": "<p><code>true</code> if the group is an admin, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.root", "description": "<p><code>true</code> if the group is a group root, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.autogenerated", "description": "<p><code>true</code> if the group is an autogenerated group, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.private", "description": "<p><code>true</code> if the group is a private group, <code>false</code> otherwise.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n    {\n        \"admin\": false,\n        \"autogenerated\": false,\n        \"private\": false,\n        \"label\": \"Assistente Tecnico\",\n        \"id\": \"AT\",\n        \"root\": false\n    },\n    {\n        \"admin\": false,\n        \"autogenerated\": true,\n        \"private\": false,\n        \"label\": \"0000000033 - SEA SPA\",\n        \"id\": \"SERVICE_0000000033\",\n        \"root\": false\n    }\n]", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Instance"}, {"type": "get", "url": "/api/art/instance/roles", "title": "Get Roles", "name": "instanceGetRoles", "group": "Instance", "description": "<p>Return info about roles</p> ", "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/instance/roles\nAccept: application/json", "type": "HTTP"}], "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous", "description": "<p>The group informations.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.id", "description": "<p>The id of the role.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.label", "description": "<p>The description of the role.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.creationDate", "description": "<p>The date when the role has been created.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "anonymous.aliases", "description": "<p>The list of the aliases of the role.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "anonymous.groups", "description": "<p>The list of the groups of the role.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n    {\n        \"creationDate\": \"2020-07-08T11:39:53.*********+02:00\",\n        \"aliases\": [\n            \"MINERVAI_ADMIN\"\n        ],\n        \"groups\": [\n            \"ADMIN\"\n        ],\n        \"label\": \"Amministratore di sistema\",\n        \"id\": \"I_ADMIN\"\n    },\n    {\n        \"creationDate\": \"2020-07-08T11:39:53.*********+02:00\",\n        \"aliases\": [\n            \"MINERVAI_NOC\"\n        ],\n        \"groups\": [\n            \"NOC\"\n        ],\n        \"label\": \"Utente NOC\",\n        \"id\": \"I_NOC\"\n    }\n]", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Instance"}, {"type": "get", "url": "/api/art/instance/users", "title": "Get Users", "name": "instanceGetUsers", "group": "Instance", "description": "<p>Return info about users</p> ", "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/instance/users\nAccept: application/json", "type": "HTTP"}], "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String[]", "optional": true, "field": "roles", "description": "<p>Return info of the users filtered by role(s).</p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "groups", "description": "<p>Return info of the users filtered by group(s).</p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "users", "description": "<p>Return info of the users filtered by user(s).</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous", "description": "<p>The user informations.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.username", "description": "<p>The username of the user logged in.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.admin", "description": "<p><code>true</code> if the user is an admin, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.userAdmin", "description": "<p><code>true</code> if the user is an user admin, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.root", "description": "<p><code>true</code> if the user is an user root, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.firstName", "description": "<p>The fist name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.lastName", "description": "<p>The last name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.fullName", "description": "<p>The full name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.email", "description": "<p>The email address of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.mobilePhone", "description": "<p>The mobile phone number of the user logged in.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "anonymous.groups", "description": "<p>The groups the user belongs to.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "anonymous.roles", "description": "<p>The roles the user belongs to.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.serviceUser", "description": "<p><code>true</code> if the user is a service user, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.disabled", "description": "<p><code>true</code> if the user is a disabled user, <code>false</code> otherwise.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n{\n  \"username\": \"<PERSON><PERSON><PERSON>\",\n  \"admin\" : false,\n  \"userAdmin\" : false,\n  \"root\" : false,\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"fullName\": \"<PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"mobilePhone\": null,\n  \"groups\": [\n    \"USER\",\n    \"PM\"\n  ],\n  \"roles\": [\n    \"AREA_MANAGER\"\n  ],\n  \"serviceUser\" : false,\n  \"disabled\"\t: false,\n}\n]", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Instance"}, {"type": "put", "url": "/api/art/instance/types/activities/:TYPE/activityProperties", "title": "Modify activity type activity properties", "name": "instanceModifyActivityProperties", "group": "Instance", "description": "<p>Set the activity properties designed for the activity TYPE, with the organization of the groups of them.</p> ", "examples": [{"title": "Example usage:", "content": "PUT http://localhost/api/art/instance/types/activities/EPCCL/activityProperties\nContent-Type: application/json\n\n{\n  \"ap\": [\n    {\n      \"group\": \"Predefiniti\",\n      \"properties\": [\n        {\n          \"name\": \"DATA_RICHIESTA\",\n          \"readOnly\": true,\n          \"nullable\": false\n        }\n      ]\n    },\n    {\n      \"group\": \"Richiesta\",\n      \"properties\": [\n        {\n          \"name\": \"ID_PRATICA_SOFIA\",\n          \"readOnly\": true,\n          \"nullable\": true\n        }\n      ]\n    },\n    {\n      \"properties\": [\n        {\n          \"name\": \"TIPO_INTERVENTO\",\n          \"readOnly\": false,\n          \"nullable\": false\n        },\n        {\n          \"name\": \"NUMERO_CODA\",\n          \"readOnly\": false,\n          \"nullable\": false\n        }\n      ]\n    }\n  ]\n}", "type": "HTTP"}], "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "Object[]", "optional": false, "field": "ap", "description": "<p>Object representing the activity properties group</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "ap.group", "description": "<p>Name of the group</p> "}, {"group": "Parameter", "type": "Object[]", "optional": false, "field": "ap.properties", "description": "<p>Object representing the activity property</p> "}, {"group": "Parameter", "type": "String", "optional": false, "field": "ap.properties.name", "description": "<p>Name of the property</p> "}, {"group": "Parameter", "type": "Boolean", "optional": false, "field": "ap.properties.readOnly", "description": "<p><code>true</code> if the property is readOnly, <code>false</code> otherwise</p> "}, {"group": "Parameter", "type": "Boolean", "optional": false, "field": "ap.properties.nullable", "description": "<p><code>true</code> if the property can be set to null, <code>false</code> otherwise</p> "}]}}, "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 204 No content", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Instance", "header": {"fields": {"Header": [{"group": "Header", "type": "String", "optional": false, "field": "Content-Type", "description": "<p>It must be set to <code>application/json</code>.</p> "}]}}}, {"type": "get", "url": "/api/art/instance/types/activities/:TYPE/activityProperties", "title": "Activity type activity properties", "name": "instanceTypeActivityTypeActivityProperties", "group": "Instance", "description": "<p>Return the activity properties designed for the activity TYPE, showing the organization of the groups of them.</p> ", "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous", "description": "<p>Object representing the activity properties group</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.group", "description": "<p>Name of the group</p> "}, {"group": "Success 200", "type": "Number", "optional": true, "field": "anonymous.id", "description": "<p>Id of the group</p> "}, {"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous.properties", "description": "<p>Object representing the activity property</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.properties.name", "description": "<p>Name of the property</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.properties.readOnly", "description": "<p><code>true</code> if the property is readOnly, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.properties.expired", "description": "<p><code>false</code> if the property is no longer associated to at least an action of the activity type, <code>true</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.properties.nullable", "description": "<p><code>true</code> if the property can be set to null, <code>false</code> otherwise.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n  {\n    \"group\": \"GROUP_1\"\n    ,\"id\" : 1\n    ,\"properties\": [\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"END_DATE\",\n        \"nullable\":false\n      },\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"ENGAGE_DESCRIPTION\",\n        \"nullable\":false\n      },\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"ESTIMATED_EFFORT\",\n        \"nullable\":false\n      },\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"ESTIMATED_EFFORT_OUTLOOK\",\n        \"nullable\":false\n      },\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"ID_EPRL\",\n        \"nullable\":false\n      },\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"PROJECT_COMMESSA\",\n        \"nullable\":false\n      },\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"REASON\",\n        \"nullable\":false\n      },\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"RESOURCE\",\n        \"nullable\":false\n      },\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"RESOURCE_ENGAGE_DESC\",\n        \"nullable\":false\n      },\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"RESOURCE_ENGAGE_END_DATE\",\n        \"nullable\":false\n      },\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"RESOURCE_ENGAGE_SHORT_DESC\",\n        \"nullable\":false\n      },\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"RESOURCE_ENGAGE_START_DATE\",\n        \"nullable\":false\n      },\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"RESOURCE_LIST\",\n        \"nullable\":false\n      },\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"RESOURCE_LIST_BL\",\n        \"nullable\":false\n      },\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"RESOURCE_LIST_OG\",\n        \"nullable\":false\n      },\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"RESOURCE_REVOKE\",\n        \"nullable\":false\n      },\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"SCOPE\",\n        \"nullable\":false\n      },\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"START_DATE\",\n        \"nullable\":false\n      },\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"SUPERVISOR\",\n        \"nullable\":false\n      },\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"TOTAL_ACCOUNTED_EFFORT\",\n        \"nullable\":false\n      },\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"TOTAL_ESTIMATED_EFFORT\",\n        \"nullable\":false\n      }\n    ]\n  },\n  {\n  \t\"properties\":[\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"ACCOUNTED_EFFORT\",\n        \"nullable\":false\n      },\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"CATEGORY_LIST\",\n        \"nullable\":false\n      },\n      {\n        \"expired\":false,\n        \"readOnly\":false,\n        \"name\":\"COMPETENCE_CENTER_MANAGER\",\n        \"nullable\":false\n      }\n    ]\n  }\n]", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/instance/types/activities/EPCCL/activityProperties\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Instance"}, {"type": "get", "url": "/api/art/instance/types/activities", "title": "Activity types list", "name": "instanceTypesActivities", "group": "Instance", "description": "<p>Return activity types list</p> ", "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous", "description": "<p>The activity type informations.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.name", "description": "<p>The name of the activity type.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.description", "description": "<p>The description of the activity type.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.canAssign", "description": "<p><code>true</code> if the activity can be assigned and the user has privilige to assigne the activity to a user, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.canDeassign", "description": "<p><code>true</code> if the activity can be deassigned and the user has privilige to deassigne the activity to a user, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.hasDashboard", "description": "<p><code>true</code> if the activity type provides dashboard, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.hasReport", "description": "<p><code>true</code> if the activity type provides report, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.UIroute", "description": "<p>The UI route of the activity type.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.documentTypes", "description": "<p>The array with the document types available for the activity type.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.canUpdateActivityProperties", "description": "<p><code>true</code> if the user can update activity property, <code>false</code> otherwise.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n  {\n    \"name\": \"FDM\",\n    \"description\": \"Finestra di Manutenzione\",\n    \"canAssign\": true,\n    \"canDeassign\": false,\n    \"hasDashboard\": false,\n    \"hasReport\": false,\n    \"UIroute\": undef,\n    \"documentTypes\": [],\n    \"canUpdateActivityProperties\": true\n  },\n  {\n    \"name\": \"WO\",\n    \"description\": \"WO\",\n    \"canAssign\": true,\n    \"canDeassign\": false,\n    \"hasDashboard\": true,\n    \"hasReport\": false,\n    \"UIroute\": undef,\n    \"documentTypes\": [],\n    \"canUpdateActivityProperties\": false\n  },\n  {\n    \"name\": \"RR\",\n    \"description\": \"Richiesta Risorse\",\n    \"canAssign\": true,\n    \"canDeassign\": false,\n    \"hasDashboard\": true,\n    \"hasReport\": false,\n    \"UIroute\": undef,\n    \"documentTypes\": [],\n    \"canUpdateActivityProperties\": true\n  },\n  {\n    \"name\": \"KARTATT\",\n    \"description\": \"gestione kart\",\n    \"canAssign\": true,\n    \"canDeassign\": false,\n    \"hasDashboard\": true,\n    \"hasReport\": false,\n    \"UIroute\": undef,\n    \"documentTypes\": [],\n    \"canUpdateActivityProperties\": false\n  },\n  {\n    \"name\": \"WOB\",\n    \"description\": \"WorkOrder Bloccante\",\n    \"canAssign\": true,\n    \"canDeassign\": false,\n    \"hasDashboard\": true,\n    \"hasReport\": false,\n    \"UIroute\": undef,\n    \"documentTypes\": [\n      {\n        \"id\": 1,\n        \"documentTypeId\": \"Altro\",\n        \"description\": \"Documento generico\" \n      }\n    ],\n    \"canUpdateActivityProperties\": false\n  }\n]", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/instance/types/activities\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Instance"}, {"type": "get", "url": "/api/art/instance/types/activities/properties", "title": "Activity properties", "name": "instanceTypesActivitiesProperties", "group": "Instance", "description": "<p>Return activity properties</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String", "optional": true, "field": "type", "description": "<p>Returns the property informations suitable only for the specific activity type.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "action", "description": "<p>Returns the property informations suitable only for the action; you can use it only if defined type param.</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous", "description": "<p>The property informations.</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "anonymous.NAME", "description": "<p>The NAME of the property.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.NAME.type", "description": "<p>The type of the property.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.NAME.label", "description": "<p>The label of the property.</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "anonymous.NAME.predefinedValue", "description": "<p>The predefined value of the property</p> "}, {"group": "Success 200", "type": "string", "optional": false, "field": "anonymous.NAME.predefinedValue.type", "description": "<p>The type of predefined value of the property</p> "}, {"group": "Success 200", "type": "string", "optional": true, "field": "anonymous.NAME.predefinedValue.value", "description": "<p>The value of predefined value of the property if applicable</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.hint", "description": "<p>The hint of the property</p> "}, {"group": "Success 200", "type": "Object[]", "optional": true, "field": "anonymous.NAME.values", "description": "<p>TBD</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.target", "description": "<p>TBD</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.template", "description": "<p>TBD</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.groupBy", "description": "<p>TBD</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.context", "description": "<p>TBD</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.contextKey", "description": "<p>TBD: defined only if context defined</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.displayProperty", "description": "<p>TBD: defined only if context defined</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.keyProperty", "description": "<p>TBD: defined only if context defined</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.minLength", "description": "<p>TBD: defined only if context defined</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.addFromAutocompleteOnly", "description": "<p>TBD: defined only if context defined</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.queryKey", "description": "<p>TBD: defined only if context defined</p> "}, {"group": "Success 200", "type": "String", "optional": true, "field": "anonymous.NAME.responseDataKey", "description": "<p>TBD: defined only if context defined</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"forecastEndDate\":{\n    \"predefinedValue\":null,\n    \"label\":\"Data prevista FL\",\n    \"type\":\"DAY\"\n  },\n  \"execProgRev\":{\n    \"predefinedValue\":{\n      \"value\":\"0\",\n      \"type\":\"const\"\n    },\n    \"label\":\"Revisione progetto esecutivo\",\n    \"type\":\"NUMBER\"\n  },\n  \"accountablePhoneNumber\":{\n    \"predefinedValue\":{\n      \"value\":\"[\\\"info\\\"][\\\"currentUserObj\\\"][\\\"mobilePhone\\\"]\",\n      \"type\":\"bracketNotation\"\n    },\n    \"label\":\"Telefono responsabile attività\",\n    \"type\":\"STRING\"\n  },\n  \"dateDocCIL\":{\n    \"predefinedValue\":{\n      \"type\":\"now\"\n    },\n    \"label\":\"Data emissione CIL\",\n    \"type\":\"DAY\"\n  }\n}", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/instance/types/activities/properties\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Instance"}, {"type": "get", "url": "/api/art/instance/types/activities/:TYPE/actions", "title": "Activity type actions", "name": "instanceTypesActivitiesTypeActions", "group": "Instance", "description": "<p>Return activity type actions</p> ", "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object", "optional": false, "field": "ACTION", "description": "<p>Object representing the info for the action named ACTION</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "ACTION.id", "description": "<p>Action id</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "ACTION.name", "description": "<p>Action name</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "ACTION.description", "description": "<p>Action description</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "ACTION.ui", "description": "<p>Action custom url</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "ACTION.isStartingType", "description": "<p><code>true</code> if the the action is a starting action, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "ACTION.isFinalType", "description": "<p><code>true</code> if the the action is a final action, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "ACTION.isSelfAssignType", "description": "<p><code>true</code> if the the action is a self assign action, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "ACTION.isSelfDeassignType", "description": "<p><code>true</code> if the the action is a self deassign action, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "ACTION.isVirtualAssignType", "description": "<p><code>true</code> if the the action is a virtual assign action, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "ACTION.isVirtualDeassignType", "description": "<p><code>true</code> if the the action is a virtual deassign action, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "ACTION.isAssignType", "description": "<p><code>true</code> if the the action is a assign action, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "ACTION.isParkType", "description": "<p><code>true</code> if the the action is a park action, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "ACTION.isUpdateActivityPropertyType", "description": "<p><code>true</code> if the the action permit to update activity property, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "ACTION.isVirtualType", "description": "<p><code>true</code> if the the action is a virtual action, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "ACTION.doAssignNotification", "description": "<p><code>true</code> if the user assignee of the activity must be notified via email, <code>false</code> otherwise.   Present only if <code>isAssignType=true</code>.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"TRASFERIMENTO_MATERIALE_ACK\": {\n    \"isVirtualDeassignType\": false,\n    \"isVirtualAssignType\": false,\n    \"isSelfAssignType\": false,\n    \"name\": \"TRASFERIMENTO_MATERIALE_ACK\",\n    \"isUpdateActivityPropertyType\": false,\n    \"description\": \"TRASFERIMENTO_MATERIALE_ACK\",\n    \"isFinalType\": false,\n    \"isVirtualType\": false,\n    \"isAssignType\": false,\n    \"isParkType\": false,\n    \"ui\": null,\n    \"isSelfDeassignType\": false,\n    \"id\": \"182\",\n    \"isStartingType\": false\n  }\n}", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/instance/types/activities/COLLAUDO_CCS/actions\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Instance"}, {"type": "get", "url": "/api/art/instance/types/activities/:TYPE", "title": "Activity type info", "name": "instanceTypesActivitiesTypeInfo", "group": "Instance", "description": "<p>Return activity type info</p> ", "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object", "optional": false, "field": "STATUS", "description": "<p>Object representing the actions for the status named STATUS</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "STATUS.ACTION", "description": "<p>Object representing the properties for the action named ACTION</p> "}, {"group": "Success 200", "type": "Object[]", "optional": false, "field": "STATUS.ACTION.properties", "description": "<p>Object representing the features of the property</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "STATUS.ACTION.assignable", "description": "<p>DEPRECATED: see the definition of the key <code>isAssignType</code> returned by API <strong>Instance - Activity type actions</strong> <code>GET /api/art/instance/types/activities/:TYPE/actions</code>.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "STATUS.ACTION.assignNotification", "description": "<p>DEPRECATED: see the definition of the key <code>doAssignNotification</code> returned by API <strong>Instance - Activity type actions</strong> <code>GET /api/art/instance/types/activities/:TYPE/actions</code>.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"OPEN\":{\n    \"TAKE_IN_CHARGE\":{\n      \"assignable\": false,\n      \"properties\":[\n\n      ]\n    },\n    \"REVOCATION\":{\n      \"assignable\": true,\n      \"properties\":[\n        {\n          \"name\":\"REASON\",\n          \"hidden\":false,\n          \"mandatory\":true,\n          \"prePopulate\":false,\n          \"readOnly\":false\n        }\n      ]\n    }\n  },\n  \"UPDATING_EFFORT\":{\n    \"UPDATE_TOTAL_EFFORT\":{\n      \"assignable\": false,\n      \"properties\":[\n        {\n          \"name\":\"TOTAL_EFFORT\",\n          \"hidden\":false,\n          \"mandatory\":true,\n          \"prePopulate\":false,\n          \"readOnly\":false\n        }\n      ]\n    }\n  }\n}", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/instance/types/activities/EPEL_IC\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Instance"}, {"type": "get", "url": "/api/art/instance/types/activities/:TYPE/statuses", "title": "Activity type statuses", "name": "instanceTypesActivitiesTypeStatuses", "group": "Instance", "description": "<p>Return activity type statuses</p> ", "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object", "optional": false, "field": "STATUS", "description": "<p>Object representing the info for the status named STATUS</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "STATUS.id", "description": "<p>Status id</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "STATUS.name", "description": "<p>Status name</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "STATUS.description", "description": "<p>Status description</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "STATUS.ui", "description": "<p>Status custom url</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "STATUS.isFinal", "description": "<p><code>true</code> if the the status is a final status, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "STATUS.isStarting", "description": "<p><code>true</code> if the the status is the starting status, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "STATUS.isVirtual", "description": "<p><code>true</code> if the the status is a virtual status, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "STATUS.isAnyStatus", "description": "<p><code>true</code> if the the status is a &quot;anyStatus&quot; status, <code>false</code> otherwise.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"RMA\": {\n    \"name\": \"RMA\",\n    \"description\": \"RMA\",\n    \"isVirtual\": false,\n    \"isFinal\": true,\n    \"isStarting\": false,\n    \"ui\": null,\n    \"isAnyStatus\": false,\n    \"id\": \"146\"\n  },\n  \"ERRORE_IN_CARICO_SIRTI\": {\n    \"name\": \"ERRORE_IN_CARICO_SIRTI\",\n    \"description\": \"ERRORE_IN_CARICO_SIRTI\",\n    \"isVirtual\": false,\n    \"isFinal\": false,\n    \"isStarting\": false,\n    \"ui\": null,\n    \"isAnyStatus\": false,\n    \"id\": \"136\"\n  }\n}", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/instance/types/activities/COLLAUDO_CCS/statuses\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Instance"}, {"type": "get", "url": "/api/art/instance/types/systems/properties", "title": "System properties", "name": "instanceTypesSystemsProperties", "group": "Instance", "description": "<p>Return system properties</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String", "optional": true, "field": "type", "description": "<p>Returns the property informations suitable only for the specific system type.</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous", "description": "<p>The property informations.</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "anonymous.NAME", "description": "<p>The NAME of the property.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.NAME.type", "description": "<p>The type of the property.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.NAME.description", "description": "<p>The description of the property.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.NAME.multiplex", "description": "<p><code>true</code> if the property is multiplex (array), <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "anonymous.NAME.isFile", "description": "<p><code>true</code> if the property is a file property, <code>false</code> otherwise.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n    \"API_TDT_03\": {\n        \"isFile\": false,\n        \"type\": \"STRING\",\n        \"description\": \"API_TDT_03\",\n        \"multiplex\": false\n    },\n    \"DATA_FIELD_AN\": {\n        \"isFile\": false,\n        \"type\": \"TIMESTAMP\",\n        \"description\": \"DATA_FIELD_AN\",\n        \"multiplex\": false\n    },\n    \"STATO_INTERVENTO_MASTER\": {\n        \"isFile\": false,\n        \"type\": \"STRING\",\n        \"description\": \"Stato intervento master\",\n        \"multiplex\": false\n    }\n}", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/instance/types/systems/properties\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Instance"}, {"type": "put", "url": "/api/art/instance/users/:user/groups", "title": "Update User Groups", "name": "instanceUpdateUserGroups", "group": "Instance", "description": "<p>Update user groups</p> ", "examples": [{"title": "Example usage:", "content": "PUT http://localhost/api/art/instance/users/LIVRAG/groups\nContent-Type: application/json\nAccept: application/json\n\n{\n  \"add\" : [ \"GROUP1\", \"GROUP2\" ],\n  \"remove : [ \"GROUP3\" ]\n}", "type": "HTTP"}], "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String[]", "optional": true, "field": "add", "description": "<p>Groups of the user to add.</p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "remove", "description": "<p>Groups of the user to remove.</p> "}, {"group": "Parameter", "type": "Boolean", "optional": true, "field": "serviceUser", "defaultValue": "true", "description": "<p><true> if the user to create must be a serviceUser, <false>otherwise</false></p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "String", "optional": false, "field": "username", "description": "<p>The username of the user logged in.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "admin", "description": "<p><code>true</code> if the user is an admin, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "userAdmin", "description": "<p><code>true</code> if the user is an user admin, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "root", "description": "<p><code>true</code> if the user is an user root, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "firstName", "description": "<p>The fist name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "lastName", "description": "<p>The last name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "fullName", "description": "<p>The full name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "email", "description": "<p>The email address of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "mobilePhone", "description": "<p>The mobile phone number of the user logged in.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "groups", "description": "<p>The groups the user belongs to.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "roles", "description": "<p>The roles the user belongs to.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "serviceUser", "description": "<p><code>true</code> if the user is a service user, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "disabled", "description": "<p><code>true</code> if the user is a disabled user, <code>false</code> otherwise.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"username\": \"<PERSON><PERSON><PERSON>\",\n  \"admin\" : false,\n  \"userAdmin\" : false,\n  \"root\" : false,\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"fullName\": \"<PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"mobilePhone\": null,\n  \"groups\": [\n    \"USER\",\n    \"PM\"\n  ],\n  \"roles\": [\n    \"AREA_MANAGER\"\n  ],\n  \"serviceUser\" : false,\n  \"disabled\"\t: false,\n}", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Instance"}, {"type": "put", "url": "/api/art/instance/users/:user/roles", "title": "Update User Roles", "name": "instanceUpdateUserRoles", "group": "Instance", "description": "<p>Update user roles</p> ", "examples": [{"title": "Example usage:", "content": "PUT http://localhost/api/art/instance/users/LIVRAGH/roles\nContent-Type: application/json\nAccept: application/json\n\n{\n  \"add\" : [ \"ROLE1\", \"ROLE2\" ],\n  \"remove : [ \"ROLE3\" ]\n}", "type": "HTTP"}], "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String[]", "optional": true, "field": "add", "description": "<p>Roles of the user to add.</p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "remove", "description": "<p>Roles of the user to remove.</p> "}, {"group": "Parameter", "type": "Boolean", "optional": true, "field": "serviceUser", "defaultValue": "true", "description": "<p><true> if the user to create must be a serviceUser, <false>otherwise</false></p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "String", "optional": false, "field": "username", "description": "<p>The username of the user logged in.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "admin", "description": "<p><code>true</code> if the user is an admin, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "userAdmin", "description": "<p><code>true</code> if the user is an user admin, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "root", "description": "<p><code>true</code> if the user is an user root, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "firstName", "description": "<p>The fist name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "lastName", "description": "<p>The last name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "fullName", "description": "<p>The full name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "email", "description": "<p>The email address of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "mobilePhone", "description": "<p>The mobile phone number of the user logged in.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "groups", "description": "<p>The groups the user belongs to.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "roles", "description": "<p>The roles the user belongs to.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "serviceUser", "description": "<p><code>true</code> if the user is a service user, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "disabled", "description": "<p><code>true</code> if the user is a disabled user, <code>false</code> otherwise.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"username\": \"<PERSON><PERSON><PERSON>\",\n  \"admin\" : false,\n  \"userAdmin\" : false,\n  \"root\" : false,\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"fullName\": \"<PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"mobilePhone\": null,\n  \"groups\": [\n    \"USER\",\n    \"PM\"\n  ],\n  \"roles\": [\n    \"AREA_MANAGER\"\n  ],\n  \"serviceUser\" : false,\n  \"disabled\"\t: false,\n}", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Instance"}, {"type": "get", "url": "/graphs/recap/:TYPE", "title": "Graphs", "name": "Graphs", "group": "Recap", "description": "<p>Get recap graphs</p> ", "examples": [{"title": "Example usage:", "content": "GET /graphs/recap/WO HTTP/1.1", "type": "HTTP"}], "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "Number", "optional": true, "field": "days", "defaultValue": "5", "description": "<p>The number of days for which to get statistics before today.</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous", "description": "<p>Sets of the graphs.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "anonymous.key", "description": "<p>Label for the graph</p> "}, {"group": "Success 200", "type": "Number[]", "optional": false, "field": "anonymous.values", "description": "<p>Array of two items: the first one is the <code>x</code> (timestamp in unix time), the second one is the <code>y</code> (count) value for the graph</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n  {\n    \"values\": [\n      [\n        1450224000000,\n        0\n      ],\n      [\n        1450310400000,\n        2\n      ],\n      [\n        1450396800000,\n        0\n      ],\n      [\n        1450483200000,\n        0\n      ],\n      [\n        1450569600000,\n        0\n      ],\n      [\n        1450656000000,\n        0\n      ]\n    ],\n    \"key\": \"APERTE\"\n  }\n]", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Recap"}, {"type": "get", "url": "/reports/:OBJECT_TYPE/:TYPE_ID", "title": "Report details", "name": "reportDetails", "group": "Reports", "description": "<p>Get report details</p> ", "examples": [{"title": "Example usage:", "content": "GET /reports/activities/78 HTTP/1.1", "type": "HTTP"}], "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String", "allowedValues": ["xlsx", "json", "csv"], "optional": true, "field": "format", "description": "<p>Report format.</p> "}, {"group": "Parameter", "type": "String", "optional": false, "field": "dateStart", "description": "<p>Filter report after this creation date.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "dateEnd", "description": "<p>Filter report before this creation date.   Default value: now.</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "File", "optional": false, "field": "anonymous", "description": "<p>The binary file if param &#39;format&#39; is defined.</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "count", "description": "<p>The count of the report if params &#39;format&#39; is not defined.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n    \"data\":[\n        {\n            \"DESCRIZIONE_ANOMALIA\":\"asdfasd\",\n            \"DATA_INSORGENZA_ALLARME\":\"2018-08-02T12:13:18.*********+02:00\",\n            \"LAST_CHANGE_USER\":\"ROOT ROOT\",\n            \"ACTIVITY_DESCRIPTION\":\"asdfasd\",\n            \"PROTEZIONE\":\"SI\",\n            \"STATUS\":\"DIAGNOSI_PRELIMINARE\",\n            \"SITO_DESTINAZIONE\":\"MIJET\",\n            \"CODICE_CLIENTE\":\"EASY00\",\n            \"LOCAZIONE_DESTINAZIONE\":\"JET MULTIMEDIA SPA - VIA DELLA POSTA 8/10 - MILANO\",\n            \"PROATTIVA\":null,\n            \"DATA_PREVISTA_ATTIVITA\":null,\n            \"MOT\":\"[\\\"EASY00ELI11040701\\\"]\",\n            \"CODICE_TIPO_GUASTO\":\"IC\",\n            \"LAST_CHANGE_DATE\":\"2018-08-02T12:13:19.*********+02:00\",\n            \"LOCAZIONE_ORIGINE\":\"EASYNET SPA - V.LE FULVIO TESTI 7 - MILANO\",\n            \"CREATION_USER\":\"ROOT ROOT\",\n            \"CURRENT_USER\":null,\n            \"PRIORITA_GUASTO\":\"Alta\",\n            \"ID_CIRCUITO\":\"10002\",\n            \"ACTIVITY_ID\":7709,\n            \"CREATION_DATE\":\"2018-08-02T12:13:18.*********+02:00\",\n            \"DATA_INIZIO_FDM\":null,\n            \"SITO_ORIGINE\":\"MIEAS\",\n            \"SYSTEM_ID\":3440,\n            \"SYSTEM_NAME\":\"7709\",\n            \"DATA_FINE_FDM\":null,\n            \"DENOMINAZIONE_CLIENTE\":\"EASYNET (EASY00)\",\n            \"CIRCUITI\":\"[\\\"MIEAS-MIJET-ETH-001\\\",\\\"MIEAS-MIJET-VC12-001(1/5)\\\",\\\"MIEAS-MIJET-VC12-002(2/5)\\\",\\\"MIEAS-MIJET-VC12-003(3/5)\\\",\\\"MIEAS-MIJET-VC12-004(4/5)\\\",\\\"MIEAS-MIJET-VC12-005(5/5)\\\"]\"\n        }\n    ],\n    \"header\":[\n        {\n            \"name\":\"ACTIVITY_ID\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"number\"\n        },\n        {\n            \"name\":\"ACTIVITY_DESCRIPTION\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"string\"\n        },\n        {\n            \"name\":\"SYSTEM_ID\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"number\"\n        },\n        {\n            \"name\":\"SYSTEM_NAME\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"string\"\n        },\n        {\n            \"name\":\"STATUS\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"string\"\n        },\n        {\n            \"name\":\"CREATION_USER\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"string\"\n        },\n        {\n            \"name\":\"CREATION_DATE\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"date\"\n        },\n        {\n            \"name\":\"LAST_CHANGE_USER\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"string\"\n        },\n        {\n            \"name\":\"LAST_CHANGE_DATE\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"date\"\n        },\n        {\n            \"name\":\"CURRENT_USER\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"string\"\n        },\n        {\n            \"name\":\"DATA_PREVISTA_ATTIVITA\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"date\"\n        },\n        {\n            \"name\":\"CODICE_TIPO_GUASTO\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"string\"\n        },\n        {\n            \"name\":\"DESCRIZIONE_ANOMALIA\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"string\"\n        },\n        {\n            \"name\":\"DATA_INSORGENZA_ALLARME\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"date\"\n        },\n        {\n            \"name\":\"PRIORITA_GUASTO\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"string\"\n        },\n        {\n            \"name\":\"PROATTIVA\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"string\"\n        },\n        {\n            \"name\":\"CODICE_CLIENTE\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"string\"\n        },\n        {\n            \"name\":\"DENOMINAZIONE_CLIENTE\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"string\"\n        },\n        {\n            \"name\":\"ID_CIRCUITO\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"string\"\n        },\n        {\n            \"name\":\"SITO_ORIGINE\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"string\"\n        },\n        {\n            \"name\":\"LOCAZIONE_ORIGINE\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"string\"\n        },\n        {\n            \"name\":\"SITO_DESTINAZIONE\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"string\"\n        },\n        {\n            \"name\":\"LOCAZIONE_DESTINAZIONE\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"string\"\n        },\n        {\n            \"name\":\"CIRCUITI\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"string\"\n        },\n        {\n            \"name\":\"PROTEZIONE\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"string\"\n        },\n        {\n            \"name\":\"MOT\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"string\"\n        },\n        {\n            \"name\":\"DATA_INIZIO_FDM\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"date\"\n        },\n        {\n            \"name\":\"DATA_FINE_FDM\",\n            \"checkboxFilter\":false,\n            \"hidden\":false,\n            \"type\":\"date\"\n        }\n    ]\n}", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Reports"}, {"type": "get", "url": "/reports", "title": "Reports overview", "name": "reportsOverview", "group": "Reports", "description": "<p>Get reports overview</p> ", "examples": [{"title": "Example usage:", "content": "GET /reports HTTP/1.1", "type": "HTTP"}], "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Number", "optional": false, "field": "id", "description": "<p>The id of the report</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "type", "description": "<p>The type of the report: can be activities|custom</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "label", "description": "<p>The label of the report</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "description", "description": "<p>The description of the report</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "groupLabel", "description": "<p>The label of the cluster for the report</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "groupDescription", "description": "<p>The description of the cluster for the report</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n    {\n        \"id\": 93,\n        \"groupLabel\":\"A_TT\",\n        \"groupDescription\":\"Gestione Trouble Ticket\",\n        \"label\":\"TT\",\n        \"type\":\"activities\",\n        \"description\":\"Gestione Trouble Ticket\"\n    },\n    {\n        \"id\": 94,\n        \"groupLabel\":\"A_TT\",\n        \"groupDescription\":\"Gestione Trouble Ticket\",\n        \"label\":\"TT - Report avanzato\",\n        \"type\":\"custom\",\n        \"description\":\"Report TT avanzato\"\n    },\n    {\n        \"id\": 95,\n        \"groupLabel\":\"A_TTB\",\n        \"groupDescription\":\"Trouble Ticket Blocccanti\",\n        \"label\":\"TTB\",\n        \"type\":\"activities\",\n        \"description\":\"Trouble Ticket Blocccanti\"\n    }\n]", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Reports"}, {"type": "post", "url": "/api/art/sessions", "title": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "group": "Session", "description": "<p><PERSON>gin</p> ", "examples": [{"title": "Example usage:", "content": "POST http://localhost/api/art/sessions\nContent-Type: application/json\nAccept: application/json\n\n{\n  \"username\" : \"FOO\",\n  \"password\" : \"BAR\"\n}", "type": "HTTP"}], "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String", "optional": false, "field": "username", "description": "<p>Username of the user.</p> "}, {"group": "Parameter", "type": "String", "optional": false, "field": "password", "description": "<p>Password of the user.</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object", "optional": false, "field": "user", "description": "<p>The user informations.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "user.username", "description": "<p>The username of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "user.firstName", "description": "<p>The fist name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "user.lastName", "description": "<p>The last name of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "user.email", "description": "<p>The email address of the user logged in.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "user.mobilePhone", "description": "<p>The mobile phone number of the user logged in.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "user.groups", "description": "<p>The groups the user belongs to.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "user.admin", "description": "<p><code>true</code> if the user is administrator, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "user.serviceUser", "description": "<p><code>true</code> if the user is a service user, <code>false</code> otherwise.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "token", "description": "<p>The session token. I has to be used to close a session.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"user\" : {\n    \"username\": \"<PERSON><PERSON><PERSON>\",\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"<PERSON><PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"mobilePhone\": null,\n    \"groups\": [\n      \"ADMI<PERSON>\",\n      \"PM\"\n    ],\n    \"admin\" : true,\n    \"serviceUser\" : false,\n    \"disabled\" : false\n  },\n  \"token\" : \"VSaOEAAARtHoKM3oOfFXOSF4nE3rrOzy\"\n}", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Session", "header": {"fields": {"Header": [{"group": "Header", "type": "String", "optional": false, "field": "Content-Type", "description": "<p>It must be set to <code>application/json</code>.</p> "}]}}}, {"type": "delete", "url": "/api/art/sessions/:TOKEN", "title": "Logout", "name": "Logout", "group": "Session", "description": "<p><PERSON><PERSON>ut</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "skip<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultValue": "0", "description": "<p>WARNING: can be used only in development environment.</p> "}]}}, "examples": [{"title": "Example usage:", "content": "DELETE http://localhost/api/art/sessions/VSaOEAAARtHoKM3oOfFXOSF4nE3rrOzy", "type": "HTTP"}], "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 204 No content", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Session"}, {"type": "get", "url": "/api/art/shared-resources/:ID", "title": "Get shared resource", "name": "getSharedResources", "group": "Shared", "description": "<p>Get shared resource</p> ", "examples": [{"title": "Example usage:", "content": "GET /api/art/shared-resources/cJ9jvFO0zu HTTP/1.1", "type": "HTTP"}], "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "File", "optional": false, "field": "anonymous", "description": "<p>The binary file.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\nContent-Disposition: attachment\nContent-Length: 34114\nContent-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Shared"}, {"type": "post", "url": "/api/art/systems", "title": "Create", "name": "createSystem", "group": "System", "description": "<p>Create system</p> ", "examples": [{"title": "Example usage:", "content": "POST http://localhost/api/art/systems\nContent-Type: application/json\nAccept: application/json\n\n{\n  \"type\" : \"PTE_MANAGEMENT\",\n  \"objectType\" : \"DII\",\n  \"description\" : \"PTE_Management IC1\",\n  \"groups\" : [\"USER\",\"ADMIN\"]\n  \"properties\" : {\n    \"SUPERVISOR\" : \"a.l<PERSON><PERSON><PERSON>@sirti.it\",\n    \"IC_NUMBER\" : \"75884\"\n  }\n}", "type": "HTTP"}], "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String", "optional": false, "field": "type", "description": "<p>Type of the system to create.</p> "}, {"group": "Parameter", "type": "String", "optional": false, "field": "objectType", "description": "<p>Object Type of the system to create.</p> "}, {"group": "Parameter", "type": "String", "optional": false, "field": "description", "description": "<p>Description of the system.</p> "}, {"group": "Parameter", "type": "String[]", "optional": false, "field": "groups", "description": "<p>Groups that must have visibility on the just created system.</p> "}, {"group": "Parameter", "type": "Object", "optional": true, "field": "properties", "description": "<p>An object representing the properties, where the keys are the NAMEs of the properties.</p> "}, {"group": "Parameter", "type": "String", "optional": false, "field": "properties.NAME", "description": "<p>The name of the property.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "date", "defaultValue": "Now()", "description": "<p>Operation date - <strong><em> NYI </em></strong></p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Number", "optional": false, "field": "id", "description": "<p>Id of the system created.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"id\" : 31722454\n}", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "System", "header": {"fields": {"Header": [{"group": "Header", "type": "String", "optional": false, "field": "Content-Type", "description": "<p>It must be set to <code>application/json</code>.</p> "}]}}}, {"type": "get", "url": "/systems/:ID", "title": "Get", "name": "getSystem", "group": "System", "description": "<p>Returns an object describing the system.</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "excludeInfo", "defaultValue": "0", "description": "<p>Exclude system info.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "excludeProperties", "defaultValue": "0", "description": "<p>Exclude system properties.</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Number", "optional": false, "field": "id", "description": "<p>The id of the system found.</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "info", "description": "<p>The info of the system found in the same format returned by API <strong>System - Info</strong> <code>GET /api/art/systems/:ID/info</code>. Available only if param <code>excludeInfo=0</code> (default).</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "properties", "description": "<p>The properties of the system found in the same format returned by API <strong>System - Properties</strong> <code>GET /api/art/systems/:ID/properties</code>. Available only if param <code>excludeProperties=0</code> (default).</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"info\": {\n    \"disabled\": false,\n    \"disablingDate\": null,\n    \"endingDate\": null,\n    \"active\": true,\n    \"description\": \"ENEL-FTTH-859\",\n    \"properties\": [\n      \"cadastralCode\",\n      \"centralId\",\n      \"city\",\n      \"cityId\",\n      \"contractId\",\n      \"country\",\n      \"customerId\",\n      \"pfpId\",\n      \"pfpName\",\n      \"pop\",\n      \"projectId\",\n      \"province\",\n      \"ring\"\n        ],\n    \"ended\": false,\n    \"creationDate\": \"2017-07-31T11:31:16.*********+02:00\",\n    \"groups\": [\n\t  \"ADMIN\",\n\t  \"PROJECT_ENEL_FTTH_859\",\n\t  \"PROJECT4RPB_ENEL_FTTH_859\",\n\t  \"ROOT\"\n    ],\n  \"type\": \"PROJECT\"\n  },\n  \"id\": 4519,\n  \"properties\": {\n    \"country\": \"Italy\",\n    \"ring\": \"12W\",\n    \"contractId\": \"FTTH\",\n    \"centralId\": \"G273_04\",\n    \"projectId\": \"859\",\n    \"city\": \"Palermo\",\n    \"customerId\": \"ENEL\",\n    \"cadastralCode\": null,\n    \"pfpName\": \"12w1\",\n    \"pfpId\": \"2074719\",\n    \"cityId\": \"2249\",\n    \"province\": \"Palermo\",\n    \"pop\": \"04 - PA-ROCCA\"\n  }\n}", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/systems/1234\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "System"}, {"type": "get", "url": "/api/art/systems/:ID/children", "title": "Find children", "name": "getSystemChildren", "group": "System", "description": "<p>Returns an array of objects describing the children systems matching the specified criteria.</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String[]", "optional": true, "field": "type", "description": "<p>Filter systems of these types.</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Number", "optional": false, "field": "id", "description": "<p>The id of the system found.</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "info", "description": "<p>The info of the system found in the same format returned by API <strong>System - Info</strong> <code>GET /api/art/systems/:ID/info</code>.</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "properties", "description": "<p>The properties of the system found in the same format returned by API <strong>System - Properties</strong> <code>GET /api/art/systems/:ID/properties</code>.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n  {\n    \"info\": {\n      \"disabled\": false,\n      \"disablingDate\": null,\n      \"endingDate\": null,\n      \"active\": true,\n      \"description\": \"SCAVO-26-mt\",\n      \"properties\": [\n        \"category\",\n        \"contractId\",\n        \"customerId\",\n        \"estimatedDuration\",\n        \"permitsAreaId\",\n        \"projectId\",\n        \"ring\",\n        \"ringId\",\n        \"subCategory\",\n        \"toAssignQuantity\",\n        \"unitOfMeasure\"\n      ],\n      \"ended\": false,\n      \"creationDate\": \"2018-02-06T14:21:16.*********+01:00\",\n      \"groups\": [\n        \"ADMIN\",\n        \"CITY_2249\",\n        \"PROJECT_ENEL_FTTH_993\",\n        \"ROOT\"\n      ],\n      \"type\": \"MACROLAVORO\"\n    },\n    \"id\": 10375,\n    \"properties\": {\n      \"unitOfMeasure\": \"mt\",\n      \"ring\": \"07\",\n      \"subCategory\": \"SCAVO\",\n      \"contractId\": \"FTTH\",\n      \"toAssignQuantity\": \"26\",\n      \"customerId\": \"ENEL\",\n      \"projectId\": \"993\",\n      \"permitsAreaId\": \"2444370\",\n      \"ringId\": \"GA3Q\",\n      \"estimatedDuration\": \"0\",\n      \"category\": \"SCAVO\"\n    }\n  }\n]", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/systems/:ID/children/?type=MACROLAVORO\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "System"}, {"type": "get", "url": "/api/art/systems", "title": "Find", "name": "getSystems", "group": "System", "description": "<p>Returns an array of objects describing the systems matching the specified criteria.</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String[]", "optional": true, "field": "type", "description": "<p>Filter systems of these types.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "type_equal", "description": "<p>Filter systems of given type.</p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "type_in", "description": "<p>Filter systems given a list.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "type_begins", "description": "<p>Filter systems with type name that begins with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "type_ends", "description": "<p>Filter systems with type name that ends with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "type_contains", "description": "<p>Filter systems with type name that contains with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "id_equal", "description": "<p>Filter systems given an id.</p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "id_in", "description": "<p>Filter systems given a list of ids.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "id_begins", "description": "<p>Filter systems with id that begins with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "id_ends", "description": "<p>Filter systems with id that ends with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "id_contains", "description": "<p>Filter systems with id that contains with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "description_equal", "description": "<p>Filter systems with a defined description.</p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "description_in", "description": "<p>Filter systems given a list of descriptions.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "description_begins", "description": "<p>Filter systems with description that begins with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "description_ends", "description": "<p>Filter systems with description that ends with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "description_contains", "description": "<p>Filter systems with description that contains with passed value.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "active", "defaultValue": "1", "description": "<p><code>1</code> active systems, <code>0</code> inactive systems.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "suspended", "defaultValue": "0", "description": "<p><code>1</code> suspended systems, <code>0</code> not suspended systems.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "sp_NAME", "description": "<p>Where NAME is the name of the property to filter. Can be repeated for more then one property name.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "sp_NAME_equal", "description": "<p>Where NAME is the name of the property to filter. Can be repeated for more then one system property name. An exact search of the value will be done.</p> "}, {"group": "Parameter", "type": "String[]", "optional": true, "field": "sp_NAME_in", "description": "<p>Where NAME is the name of the property to filter. Can be repeated for more then one system property name. An exact search of the given list of the values will be done.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "sp_NAME_begins", "description": "<p>Where NAME is the name of the property to filter. Filter activities with system property that begins with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "sp_NAME_ends", "description": "<p>Where NAME is the name of the property to filter. Filter activities system with property that ends with passed value.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "sp_NAME_contains", "description": "<p>Where NAME is the name of the property to filter. Filter activities with system property that contains with passed value.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["1"], "optional": true, "field": "sp_NAME_isNull", "description": "<p>Where NAME is the name of the property to filter. A search of the NAME with value <code>null</code> will be done.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["1"], "optional": true, "field": "sp_NAME_isNotNull", "description": "<p>Where NAME is the name of the property to filter. A search of the NAME with value <code>not null</code> will be done.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "includeActivities", "defaultValue": "0", "description": "<p>Include activities list about system.</p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "caseSensitive", "defaultValue": "1", "description": "<p><code>0</code> execute the search in non-CASE_SENSITIVE mode, <code>1</code> execute the search in CASE_SENSITIVE mode.</p> "}, {"group": "Parameter", "type": "String[]", "allowedValues": ["[-]id", "[-]type", "[-]creationDate", "[-]disablingDate", "[-]endingDate", "[-]description", "[-]sp_NAME"], "optional": true, "field": "sort", "description": "<p>If present, it define sort fields. If the field is prefixed by <code>-</code> the sort order is descending.</p> <p> example:</p> <pre><code>sort=type&amp;sort=-id&amp;sort=-sp_COMPETENCE_CERTER </code></pre>"}, {"group": "Parameter", "type": "Number", "optional": true, "field": "limit", "description": "<p>Maximum number of results</p> "}, {"group": "Parameter", "type": "Number", "optional": true, "field": "skip", "description": "<p>Skip the first <code>skip</code> records from the the results set</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Number", "optional": false, "field": "id", "description": "<p>The id of the system found.</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "info", "description": "<p>The info of the system found in the same format returned by API <strong>System - Info</strong> <code>GET /api/art/systems/:ID/info</code>.</p> "}, {"group": "Success 200", "type": "Object", "optional": false, "field": "properties", "description": "<p>The properties of the system found in the same format returned by API <strong>System - Properties</strong> <code>GET /api/art/systems/:ID/properties</code>.</p> "}, {"group": "Success 200", "type": "Object[]", "optional": false, "field": "activities", "description": "<p>Objects of the activities of the system in the same format returned by API <strong>Activity - Find</strong> <code>GET /api/art/activities</code>. The key is present only if <code>incluseActivities=1</code></p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n   {\n      \"id\" : 2096,\n      \"info\" : {\n        \"description\" : \"PTE_Management IC1\",\n        \"type\" : \"PTE_MANAGEMENT\",\n        \"active\" : true,\n        \"disabled\" : false,\n        \"ended\" : false,\n        \"creationDate\" : \"2015-04-17T10:57:35.*********+02:00\",\n        \"disablingDate\" : null,\n        \"endingDate\" : null,\n        \"properties\" : [\n          \"PROJECT_COMMESSA\",\n          \"CUSTOMER\",\n          \"DESCRIPTION\",\n          \"INITIATIVE_NUMBER\"\n        ],\n        \"groups\" : [\n          \"ADMIN\",\n          \"ROOT\",\n          \"USER\"\n        ]\n      },\n      \"properties\" : {\n         \"PROJECT_COMMESSA\" : \"P406\",\n         \"INITIATIVE_NUMBER\" : \"7945\",\n         \"DESCRIPTION\" : null,\n         \"CUSTOMER\" : \"ACME\"\n      }\n   }\n]", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/systems?sp_ID_RISORSA=U002313254&sp_DN=**********&sp_ID_ULLCO=C00008734&type=ULL_LINEA_NON_ATTIVA&type=ULL_LINEA_ATTIVA_NP\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "System"}, {"type": "get", "url": "/api/art/systems", "title": "Find Extended", "name": "getSystemsExtended", "group": "System", "description": "<p>Find systems extended. It accepts all the parameters of API <strong>System - Find</strong>, plus the parameter <code>extendedOutput</code>.</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "Number", "allowedValues": ["1", "0"], "optional": false, "field": "extendedOutput", "description": "<p>If <code>1</code> instead of returning an array of objects describing the systems, it returns an hash: the key <code>results</code> contains an array of system objects,  the key <code>count</code> contains the total count of the results excluding limit and skip parameters. If <code>0</code> has the same behaviour of API <strong>System - Find</strong></p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "onlyCount", "description": "<p><code>0</code> returns an hash with the keys <code>count</code> e <code>results</code>, <code>1</code> returns an hash with only the key <code>count</code>.</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous", "description": "<p>Object rappresenting the search</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "anonymous.count", "description": "<p>The total count of the systems found.</p> "}, {"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous.results", "description": "<p>Objects of the systems found.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"count\":165,\n  \"results\":[\n    {\n      \"info\":{\n        \"disabled\":false,\n        \"disablingDate\":null,\n        \"endingDate\":null,\n        \"active\":true,\n        \"description\":\"IIN-OPS\",\n        \"properties\":[\n          \"CC\",\n          \"COMPETENCE_CENTER_DESCRIPTION\",\n          \"COMPETENCE_CENTER_MANAGER\",\n          \"FIRST_NAME\",\n          \"LAST_NAME\"\n        ],\n        \"ended\":false,\n        \"creationDate\":\"2015-04-30T15:42:39.*********+02:00\",\n        \"groups\":[\n          \"ADMIN\",\n          \"CCM_IIN-OPS\",\n          \"EPEL_MANAGER\",\n          \"GUEST\",\n          \"ROOT\",\n          \"SUPERVISOR\"\n        ],\n        \"type\":\"COMPETENCE_CENTER\"\n      },\n      \"id\":2372,\n      \"properties\":{\n        \"FIRST_NAME\":\"Edoardo\",\n        \"COMPETENCE_CENTER_MANAGER\":\"<EMAIL>\",\n        \"CC\":\"5041\",\n        \"LAST_NAME\":\"<PERSON><PERSON><PERSON>\",\n        \"COMPETENCE_CENTER_DESCRIPTION\":\"Outside Plant Solutions & IPR\"\n      }\n    }\n  ]\n}", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/systems?extendedOutput=1\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "System"}, {"type": "get", "url": "/api/art/systems", "title": "Find for export", "name": "getSystemsForExport", "group": "System", "description": "<p>Find systems for export. It accepts all the parameters of API <strong>System - Find</strong>, plus the parameter <code>export</code>.</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String", "allowedValues": ["xlsx", "csv"], "optional": false, "field": "export", "description": "<p>Set the format of the file to download</p> <p>NOTE: Param <code>type</code> or <code>id_equal</code> is mandatory</p> "}]}}, "examples": [{"title": "Example usage:", "content": "GET /api/art/systems?export=xlsx&type=NETWORK", "type": "HTTP"}], "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "File", "optional": false, "field": "anonymous", "description": "<p>The binary file.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\nContent-Disposition: attachment; filename=\"export_20190401172524.xlsx\"\nContent-Length: 34114\nContent-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "System"}, {"type": "get", "url": "/api/art/systems", "title": "Find GridOutput", "name": "getSystemsGridOuput", "group": "System", "description": "<p>Find systems for grid output. It accepts all the parameters of API <strong>System - Find</strong>, plus the parameter <code>gridOutput</code>.</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "Number", "allowedValues": ["1", "0"], "optional": false, "field": "gridOutput", "description": "<p>If <code>1</code> instead of returning an array of objects describing the systems, it returns an hash: the key <code>results</code> contains an array of system objects,  the key <code>count</code> contains the total count of the results excluding limit and skip parameters. If <code>0</code> has the same behaviour of API <strong>System - Find</strong></p> <p>NOTE: Param <code>type</code> or <code>id_equal</code> is mandatory</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous", "description": "<p>Object rappresenting the search</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "anonymous.count", "description": "<p>The total count of the systems found.</p> "}, {"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous.results", "description": "<p>Objects of the systems found in grid format.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"count\": 6,\n  \"results\": {\n    \"data\": [\n      {\n        \"[\\\"info\\\"][\\\"type\\\"]\": \"NETWORK\"\n      },\n      {\n        \"[\\\"info\\\"][\\\"type\\\"]\": \"NETWORK\"\n      },\n      {\n        \"[\\\"info\\\"][\\\"type\\\"]\": \"NETWORK\"\n      },\n      {\n        \"[\\\"info\\\"][\\\"type\\\"]\": \"NETWORK\"\n      },\n      {\n        \"[\\\"info\\\"][\\\"type\\\"]\": \"NETWORK\"\n      },\n      {\n        \"[\\\"info\\\"][\\\"type\\\"]\": \"NETWORK\"\n      }\n    ],\n    \"header\": [\n      {\n        \"name\": \"tipo\",\n        \"type\": \"string\",\n        \"hidden\": false,\n        \"checkboxFilter\": false,\n        \"id\": \"[\\\"info\\\"][\\\"type\\\"]\"\n      }\n    ]\n  }\n}", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/systems?gridOutput=1&type=NETWORK\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "System"}, {"type": "get", "url": "/api/art/systems", "title": "Find ids", "name": "getSystemsIds", "group": "System", "description": "<p>Find systems ids. It accepts all the parameters of API <strong>System - Find</strong>, plus the parameter <code>onlyids</code>.</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "Number", "allowedValues": ["1", "0"], "optional": false, "field": "onlyids", "description": "<p>If <code>1</code> instead of returning an array of objects describing the systems, it returns an array of system ids,   if <code>0</code> has the same behaviour of API <strong>System - Find</strong></p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Number[]", "optional": false, "field": "id", "description": "<p>Ids of the systems found.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n  1356,\n  9281\n]", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/systems?onlyids=1,sp_ID_RISORSA=U002313254&sp_DN=**********&sp_ID_ULLCO=C00008734&type=ULL_LINEA_NON_ATTIVA&type=ULL_LINEA_ATTIVA_NP\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "System"}, {"type": "get", "url": "/api/art/systems", "title": "Find ids extended", "name": "getSystemsIdsExtended", "group": "System", "description": "<p>Find systems ids extended. It accepts all the parameters of API <strong>System - Find Ids</strong>, plus the parameter <code>extendedOutput</code>.</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "Number", "allowedValues": ["1", "0"], "optional": false, "field": "extendedOutput", "description": "<p>If <code>1</code> instead of returning an array of ids describing the systems, it returns an hash: the key <code>results</code> contains an array of systems ids,  the key <code>count</code> contains the total count of the results excluding limit and skip parameters. If <code>0</code> has the same behaviour of API <strong>System - Find Ids</strong></p> "}, {"group": "Parameter", "type": "Number", "allowedValues": ["0", "1"], "optional": true, "field": "onlyCount", "description": "<p><code>0</code> returns an hash with the keys <code>count</code> e <code>results</code>, <code>1</code> returns an hash with only the key <code>count</code>.</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous", "description": "<p>Object rappresenting the search</p> "}, {"group": "Success 200", "type": "Number", "optional": false, "field": "anonymous.count", "description": "<p>The total count of the systems found.</p> "}, {"group": "Success 200", "type": "Object[]", "optional": false, "field": "anonymous.results", "description": "<p>Ids of the systems found.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"count\":165,\n  \"results\":[\n    \"2372\"\n  ]\n}", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/systems?onlyids=1&extendedOutput=1\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "System"}, {"type": "put", "url": "/api/art/systems/:ID", "title": "Disable/Enable", "name": "systemDisableEnable", "group": "System", "description": "<p>Disable or Enable system</p> ", "examples": [{"title": "Example usage:", "content": "PUT http://localhost/api/art/systems/2104\nContent-Type: application/json\n\n{\n  \"action\" : \"disable\"\n}", "type": "HTTP"}], "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String", "allowedValues": ["disable", "enable"], "optional": false, "field": "action", "description": "<p><code>disable</code> disable the system, <code>enable</code> enable the system.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "date", "defaultValue": "Now()", "description": "<p>Disable date. Not available with <code>&quot;action&quot; : &quot;enable&quot;</code> - <strong><em> NYI </em></strong></p> "}]}}, "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 204 No content", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "System", "header": {"fields": {"Header": [{"group": "Header", "type": "String", "optional": false, "field": "Content-Type", "description": "<p>It must be set to <code>application/json</code>.</p> "}]}}}, {"type": "delete", "url": "/api/art/systems/:ID", "title": "End", "name": "systemEnd", "group": "System", "description": "<p>End system</p> ", "examples": [{"title": "Example usage:", "content": "DELETE http://localhost/api/art/systems/2104", "type": "HTTP"}], "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String", "optional": true, "field": "date", "defaultValue": "Now()", "description": "<p>End date - <strong><em> NYI </em></strong></p> "}]}}, "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 204 No content", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "System"}, {"type": "delete", "url": "/api/art/systems/:ID/groups", "title": "Remove Groups viewing the system", "name": "systemGroupsDelete", "group": "System", "description": "<p>Remove the groups specified from the list of the groups viewing the system. Note that &quot;ROOT&quot; group cannot be used.</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String[]", "optional": false, "field": "groups", "description": "<p>The group name.</p> "}]}}, "examples": [{"title": "Example usage:", "content": "DELETE http://localhost/api/art/systems/2104/groups\nContent-Type: application/json\n\n{\n  \"groups\": [\n    \"ADMIN\",\n    \"EPEL_MANAGER\"\n  ]\n}", "type": "HTTP"}], "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 204 No content", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "System", "header": {"fields": {"Header": [{"group": "Header", "type": "String", "optional": false, "field": "Content-Type", "description": "<p>It must be set to <code>application/json</code>.</p> "}]}}}, {"type": "get", "url": "/api/art/systems/:ID/groups", "title": "Groups viewing the system", "name": "systemGroupsGet", "group": "System", "description": "<p>Returns the list of the groups that can view the system</p> ", "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "String[]", "optional": false, "field": "group", "description": "<p>The group name.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n  \"ADMIN\",\n  \"ROOT\",\n  \"USER\"\n]", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/systems/2104/groups\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "System"}, {"type": "post", "url": "/api/art/systems/:ID/groups", "title": "Add Groups viewing the system", "name": "systemGroupsPost", "group": "System", "description": "<p>Add groups to the list of the groups viewing the system. Note that &quot;ROOT&quot; group cannot be used.</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String[]", "optional": false, "field": "groups", "description": "<p>The group name.</p> "}]}}, "examples": [{"title": "Example usage:", "content": "POST http://localhost/api/art/systems/2104/groups\nContent-Type: application/json\n\n{\n  \"groups\": [\n    \"EPEL_MANAGER\"\n  ]\n}", "type": "HTTP"}], "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 204 No content", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "System", "header": {"fields": {"Header": [{"group": "Header", "type": "String", "optional": false, "field": "Content-Type", "description": "<p>It must be set to <code>application/json</code>.</p> "}]}}}, {"type": "put", "url": "/api/art/systems/:ID/groups", "title": "Override Groups viewing the system", "name": "systemGroupsPut", "group": "System", "description": "<p>Override the list of the groups viewing the system. Note that &quot;ROOT&quot; group cannot be used.</p> ", "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String[]", "optional": false, "field": "groups", "description": "<p>The group name.</p> "}]}}, "examples": [{"title": "Example usage:", "content": "PUT http://localhost/api/art/systems/2104/groups\nContent-Type: application/json\n\n{\n  \"groups\": [\n    \"ADMIN\",\n    \"EPEL_MANAGER\"\n  ]\n}", "type": "HTTP"}], "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 204 No content", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "System", "header": {"fields": {"Header": [{"group": "Header", "type": "String", "optional": false, "field": "Content-Type", "description": "<p>It must be set to <code>application/json</code>.</p> "}]}}}, {"type": "get", "url": "/api/art/systems/:ID/info", "title": "Info", "name": "systemInfo", "group": "System", "description": "<p>Returns system informations</p> ", "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "String", "optional": false, "field": "description", "description": "<p>The description of the system.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "type", "description": "<p>The system type</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "active", "description": "<p><code>true</code> if the system is active, <code>false</code> otherwhise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "disabled", "description": "<p><code>true</code> if the system is disabled, <code>false</code> otherwhise.</p> "}, {"group": "Success 200", "type": "Boolean", "optional": false, "field": "ended", "description": "<p><code>true</code> if the system is disabled, <code>false</code> otherwhise.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "creationDate", "description": "<p>The date when the system has been created.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "disablingDate", "description": "<p>If the system is disabled is the disable date.</p> "}, {"group": "Success 200", "type": "String", "optional": false, "field": "endingDate", "description": "<p>If the system is ended is the end date.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "properties", "description": "<p>The property types associated to the system.</p> "}, {"group": "Success 200", "type": "String[]", "optional": false, "field": "groups", "description": "<p>The groups that can view the system.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"description\" : \"PTE_Management IC1\",\n  \"type\" : \"PTE_MANAGEMENT\",\n  \"active\" : true,\n  \"disabled\" : false,\n  \"ended\" : false,\n  \"creationDate\" : \"2015-04-17T10:57:35.*********+02:00\",\n  \"disablingDate\" : null,\n  \"endingDate\" : null,\n  \"properties\" : [\n    \"PROJECT_COMMESSA\",\n    \"CUSTOMER\",\n    \"DESCRIPTION\",\n    \"INITIATIVE_NUMBER\"\n  ],\n  \"groups\" : [\n    \"ADMIN\",\n    \"ROOT\",\n    \"USER\"\n  ]\n}", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/systems/2104/info\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "System"}, {"type": "get", "url": "/api/art/systems/:ID/properties", "title": "Properties", "name": "systemProperties", "group": "System", "description": "<p>Returns an object representing the properties of the system, where the keys are the NAMEs of the properties.</p> ", "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "String", "optional": false, "field": "NAME", "description": "<p>The value of the property named NAME.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n{\n  \"SUPERVISOR\" : \"a<PERSON><PERSON><PERSON><PERSON><PERSON>@sirti.it\",\n  \"INITIATIVE_NUMBER\" : \"17895\",\n  \"CUSTOMER\": \"ACME\"\n}", "type": "json"}]}, "examples": [{"title": "Example usage:", "content": "GET http://localhost/api/art/systems/2104/properties\nAccept: application/json", "type": "HTTP"}], "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "System"}, {"type": "put", "url": "/api/art/systems/:ID/properties", "title": "Upsert Properties", "name": "systemUpsertProperties", "group": "System", "description": "<p>Upsert system properties</p> ", "examples": [{"title": "Example usage:", "content": "PUT http://localhost/api/art/systems/2104/properties\nContent-Type: application/json\n\n{\n  \"properties\" : {\n  \t\"SUPERVISOR\" : \"a.<PERSON><PERSON><PERSON><PERSON>@sirti.it\",\n  \t\"IC_NUMBER\" : null\n  }\n}", "type": "HTTP"}], "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "Object", "optional": false, "field": "properties", "description": "<p>An object representing the properties, where the keys are the NAMEs of the properties.    To delete a property set the value to <code>null</code></p> "}, {"group": "Parameter", "type": "String", "optional": false, "field": "properties.NAME", "description": "<p>The name of the property.</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "date", "defaultValue": "Now()", "description": "<p>Operation date - <strong><em> NYI </em></strong></p> "}]}}, "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 204 No content", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "System", "header": {"fields": {"Header": [{"group": "Header", "type": "String", "optional": false, "field": "Content-Type", "description": "<p>It must be set to <code>application/json</code>.</p> "}]}}}, {"type": "post", "url": "/api/art/tmpfiles", "title": "Upload temporary file(s)", "name": "createTmpfile", "group": "Tmpfile", "description": "<p>Upload temporary file(s) that can be used, for example, to attach to an activity and/or to a step</p> ", "header": {"fields": {"Header": [{"group": "Header", "type": "String", "optional": false, "field": "Content-Type", "description": "<p>It must be set to <code>multipart/form-data</code>.</p> "}]}}, "examples": [{"title": "Example usage:", "content": "POST /api/art/tmpfiles HTTP/1.1\nAccept: application/json\nContent-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW\n\n----WebKitFormBoundary7MA4YWxkTrZu0gW\nContent-Disposition: form-data; name=\"attachment\"; filename=\"importazione.docx\"\nContent-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document\n\n\n----WebKitFormBoundary7MA4YWxkTrZu0gW\nContent-Disposition: form-data; name=\"attachment\"; filename=\"esportazione.xlsx\"\nContent-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\n\n\n----WebKitFormBoundary7MA4YWxkTrZu0gW", "type": "HTTP"}], "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "File[]", "optional": false, "field": "attachment", "description": "<p>Files to upload.</p> "}]}}, "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "String[]", "optional": false, "field": "id", "description": "<p>Id of the uploaded file.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\n\n[\n  \"xTalgqtjzg.docx\",\n  \"7wmWlBzH4D.xlsx\"\n]", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Tmpfile"}, {"type": "delete", "url": "/api/art/tmpfiles/:ID", "title": "Delete temporary file", "name": "deleteTmpfile", "group": "Tmpfile", "description": "<p>Delete temporary file</p> ", "examples": [{"title": "Example usage:", "content": "DELETE /api/art/tmpfiles/cJ9jvFO0zu.xls HTTP/1.1", "type": "HTTP"}], "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 204 No content", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Tmpfile"}, {"type": "get", "url": "/api/art/tmpfiles/:ID", "title": "Download temporary file", "name": "downloadTmpfile", "group": "Tmpfile", "description": "<p>Download temporary file</p> ", "examples": [{"title": "Example usage:", "content": "GET /api/art/tmpfiles/cJ9jvFO0zu.xls HTTP/1.1", "type": "HTTP"}], "success": {"fields": {"Success 200": [{"group": "Success 200", "type": "File", "optional": false, "field": "anonymous", "description": "<p>The binary file.</p> "}]}, "examples": [{"title": "Success-Response:", "content": "HTTP/1.1 200 OK\nContent-Disposition: attachment; filename=\"esportazione.xlsx\"\nContent-Length: 34114\nContent-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Tmpfile"}, {"type": "put", "url": "/api/art/tmpfiles/:ID", "title": "Update temporary file", "name": "updateTmpfile", "group": "Tmpfile", "description": "<p>Update temporary file info</p> ", "examples": [{"title": "Example usage:", "content": "PUT /api/art/tmpfiles/cJ9jvFO0zu.xls\nContent-Type: application/json\n\n{\n  \"title\" : \"Titolo\",\n  \"description\": \"Descrizione\",\n  \"revision\": \"1.1\",\n  \"refDate\": \"2019-01-01\"\n  \"docType\": \"Altro\",\n}", "type": "HTTP"}], "parameter": {"fields": {"Parameter": [{"group": "Parameter", "type": "String", "optional": true, "field": "title", "description": "<p>The title of the temporary file</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "description", "description": "<p>The description of the temporary file</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "revision", "description": "<p>The revision reference of the temporary file</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "refDate", "description": "<p>The date reference of the temporary file in the format YYYY-MM-DD</p> "}, {"group": "Parameter", "type": "String", "optional": true, "field": "docType", "description": "<p>The document type of the temporary file</p> "}]}}, "success": {"examples": [{"title": "Success-Response:", "content": "HTTP/1.1 204 No content", "type": "json"}]}, "version": "0.0.0", "filename": "/home/<USER>/COM/share/perl5/API/ART/REST.pm", "groupTitle": "Tmpfile", "header": {"fields": {"Header": [{"group": "Header", "type": "String", "optional": false, "field": "Content-Type", "description": "<p>It must be set to <code>application/json</code>.</p> "}]}}}]