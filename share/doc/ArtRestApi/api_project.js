define({
  "name": "ART RESTFul API",
  "version": "0.0.1",
  "description": "",
  "header": {
    "title": "Introduction",
    "content": "<h1 id=\"introduction\">Introduction</h1>\n<p>Here you can find all the API to interact with ART objects.</p>\n<h2 id=\"dates\">Dates</h2>\n<p>Please note that all dates are ISO formatted strings.</p>\n<h2 id=\"errors\">Errors</h2>\n<p>In case of error a HTTP error status is returned. The body will contain an object defined as follow:</p>\n<ul>\n<li><code>UUID</code>: unique identifier of the error occurred. You must include it if you contact administrators in case of problems.</li>\n<li><code>message</code>: a message describing the error.</li>\n<li><code>internalMessage</code>: available only if you run the webservices in <em>development</em> mode, it contains further information about the error.</li>\n</ul>\n<p>Example:</p>\n<pre><code>HTTP/1.1 400 Bad Request\nContent-Type: application/json\n\n{\n  &quot;UUID&quot;: &quot;39021fefd8ff0e0510735a563abf3701&quot;,\n  &quot;message&quot;: &quot;Uncorrect parameters&quot;,\n  &quot;internalMessage&quot;: &quot;Error in creating system: SYSTEM_TYPE_NAME &#39;BID_MANAGEMENT&#39; undefined!&quot;\n}\n</code></pre><p><strong>Note</strong>: if the status code <code>401 Authorization Required</code> is returned, the body is empty!</p>\n<h2 id=\"prefix\">Prefix</h2>\n<p>By default routes prefix is <code>/api/art</code>. You can change it defining environment variable <code>ART_WS_ROUTES_PREFIX</code>.</p>\n<h2 id=\"virus-scan\">Virus Scan</h2>\n<p>To perform virus scan on uploaded files by route <code>POST /api/art/tmpfiles</code> you must have <a href=\"http://www.clamav.net/\">ClamAV</a> daemon running on the host running the webservices\nand set environment variable <code>CLAMAV_DAEMON_PORT</code> with the value of the TCP/IP port or the unix domain socket where ClamAV is listening.\n<strong>Note</strong>: Uploaded files are temporary stored in <code>$TMPDIR</code>; please check that the user running ClamAV daemon can access this directory.</p>\n"
  },
  "order": [
    "Session",
    "Login",
    "Logout",
    "System",
    "getSystem",
    "getSystems",
    "getSystemsExtended",
    "getSystemsIds",
    "getSystemsIdsExtended",
    "getSystemsGridOuput",
    "getSystemsForExport",
    "createSystem",
    "systemDisableEnable",
    "systemEnd",
    "systemInfo",
    "systemProperties",
    "systemUpsertProperties",
    "systemGroupsGet",
    "systemGroupsPost",
    "systemGroupsPut",
    "systemGroupsDelete",
    "getSystemChildren",
    "Activity",
    "getActivity",
    "getActivities",
    "getActivitiesExtended",
    "getActivitiesIds",
    "getActivitiesIdsExtended",
    "getActivitiesGridOutput",
    "getActivitiesForExport",
    "createActivity",
    "activityInfo",
    "activityProperties",
    "getActivityProperties",
    "activityUpsertProperties",
    "activityDestUsers",
    "activityAssign",
    "activityAssignGetUsers",
    "activityHistory",
    "activityCanDoAction",
    "stepActivity",
    "getActivityChildren",
    "attachmentList",
    "checkMethodsTransitionAttachment",
    "downloadTransitionAttachment",
    "deleteTransitionAttachment",
    "activityLock",
    "activityUnlock",
    "getActivityHierarchy",
    "Tmpfile",
    "createTmpfile",
    "downloadTmpfile",
    "deleteTmpfile",
    "updateTmpfile",
    "Shared",
    "getSharedResources",
    "Dashboards",
    "dashboardsConfig",
    "dashboardsData",
    "dashboardsDataForExport",
    "Reports",
    "reportsOverview",
    "reportDetails",
    "Favorites",
    "createFavorite",
    "getFavorites",
    "getFavorite",
    "updateFavorite",
    "deleteFavorite",
    "Instance",
    "instanceGetAuthenticatedUser",
    "instanceTypesActivities",
    "instanceTypesActivitiesTypeInfo",
    "instanceTypesActivitiesTypeStatuses",
    "instanceTypesActivitiesTypeActions",
    "instanceTypeActivityTypeActivityProperties",
    "instanceModifyActivityProperties",
    "instanceTypesActivitiesProperties",
    "instanceActivityPropertiesGroups",
    "instanceTypesSystemsProperties",
    "instanceGetGroups",
    "instanceGetRoles",
    "instanceGetUsers",
    "instanceCreateUser",
    "instanceUpdateUserGroups",
    "instanceAddUserGroup",
    "instanceDeleteUserGroup",
    "instanceUpdateUserRoles",
    "instanceAddUserRole",
    "instanceDeleteUserRole",
    "Recap",
    "Graphs",
    "instanceTypesActivitiesTypeProperties"
  ],
  "template": {
    "withCompare": false
  },
  "sampleUrl": false,
  "apidoc": "0.2.0",
  "generator": {
    "name": "apidoc",
    "time": "2022-10-18T15:38:46.642Z",
    "url": "http://apidocjs.com",
    "version": "0.12.3"
  }
});