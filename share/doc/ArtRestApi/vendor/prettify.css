/* Pretty printing styles. Used with prettify.js. */
/* Vim sunburst theme by <PERSON> */
pre .str {
  color: #65B042;
}
/* string  - green */
pre .kwd {
  color: #E28964;
}
/* keyword - dark pink */
pre .com {
  color: #AEAEAE;
  font-style: italic;
}
/* comment - gray */
pre .typ {
  color: #89bdff;
}
/* type - light blue */
pre .lit {
  color: #3387CC;
}
/* literal - blue */
pre .pun {
  color: #fff;
}
/* punctuation - white */
pre .pln {
  color: #fff;
}
/* plaintext - white */
pre .tag {
  color: #89bdff;
}
/* html/xml tag    - light blue */
pre .atn {
  color: #bdb76b;
}
/* html/xml attribute name  - khaki */
pre .atv {
  color: #65B042;
}
/* html/xml attribute value - green */
pre .dec {
  color: #3387CC;
}
/* decimal - blue */
/* Specify class=linenums on a pre to get line numbering */
ol.linenums {
  margin-top: 0;
  margin-bottom: 0;
  color: #AEAEAE;
}
/* IE indents via margin-left */
li.L0,
li.L1,
li.L2,
li.L3,
li.L5,
li.L6,
li.L7,
li.L8 {
  list-style-type: none;
}
/* Alternate shading for lines */
@media print {
  pre .str {
    color: #060;
  }
  pre .kwd {
    color: #006;
    font-weight: bold;
  }
  pre .com {
    color: #600;
    font-style: italic;
  }
  pre .typ {
    color: #404;
    font-weight: bold;
  }
  pre .lit {
    color: #044;
  }
  pre .pun {
    color: #440;
  }
  pre .pln {
    color: #000;
  }
  pre .tag {
    color: #006;
    font-weight: bold;
  }
  pre .atn {
    color: #404;
  }
  pre .atv {
    color: #060;
  }
}