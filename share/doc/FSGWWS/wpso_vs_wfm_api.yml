openapi: 3.0.0
info:
  title: "WPSO vs. NFS Integration Layer"
  description: "This document describes the interface (made-up by a RESTful web service) to integrate WPSO and NFS systems in order to synchronize field operations of activities managed, planned and assigned in WPSO."
  version: "0.1.5"
  contact:
    email: "a.<PERSON><PERSON><PERSON><PERSON>@sirti.it"
servers:
  - url: http://dlvgwpol001.ict.sirti.net:10172/sirti/api/private/wpsogwws/wpsogw
    description: Development server
components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-KEY
  parameters:
    fieldServiceName:
      name: "fieldServiceName"
      in: "path"
      description: "Field service name"
      required: true
      schema:
        type: "string"
        enum: [
          NFS
        ]
    fieldServiceId:
      name: "fieldServiceId"
      in: "path"
      description: "Field service Id"
      required: true
      schema:
        type: "string"
    activityId:
      name: "activityId"
      in: "path"
      description: "Activity id (i.e. id_attivita WPSO)"
      required: true
      schema:
        type: "string"
  schemas:
    EngagementEvent:
      type: "object"
      required:
        - "name"
        - "userid"
        - "username"
        - "timestamp"
      properties:
        name:
          type: "string"
          enum: [
            "assignment",
            "start",
            "pause"
          ]
        userid:
          type: "string"
          description: "User id of the user that loaded media file on the remote provider"
          example: "LIVRAGH"
        username:
          type: "string"
          description: "Name of the user that loaded media file on the remote provider in the form"
          format: "lastname, firstname"
          example: "LIVRAGHI, ALVARO"
        timestamp:
          type: "string"
          nullable: false
          format: date-time
        description:
          type: "string"
          description: "Action annotations"
          example: "Descrizione di prova"
        geoLocation:
          type: "string"
          description: "Latitude,Longitude"
          nullable: true
          example: "45.111,2.444"
    UpdateEngagement:
      type: "object"
      required:
        - "action"
        - "properties"
        - "userid"
        - "username"
        - "timestamp"
        - "geoLocation"
      properties:
        action:
          type: "string"
          description: "Action for the engagement"
        properties:
          type: "object"
          description: "Keys/Values"
        description:
          type: "string"
          description: "Action annotations"
          example: "Descrizione di prova"
        geoLocation:
          type: "string"
          description: "Latitude,Longitude"
          nullable: true
          example: "45.111,2.444"
        userid:
          type: "string"
          description: "Username"
          example: "LIVRAGH"
        username:
          type: "string"
          description: "Name of the user that loaded media file on the remote provider in the form"
          format: "lastname, firstname"
          example: "LIVRAGHI, ALVARO"
        timestamp:
          type: "string"
          format: date-time
    EngagementRules:
      type: "array"
      items:
        $ref: "#/components/schemas/EngagementRule"
    EngagementRule:
      type: "object"
      required:
        - "action"
        - "description"
        - "behaviour"
        - "properties"
      properties:
        action:
          type: "string"
          description: "Action for the engagement"
        description:
          type: "string"
          description: "Description of the action for the engagement"
          nullable: true
        behaviour:
          type: "string"
          description: "Action behaviour"
          enum: 
            - STARTED
            - NOT DONE
            - COMPLETED
          nullable: false
        properties:
          type: "array"
          description: "Properties type"
          items:
            $ref: "#/components/schemas/EngagementPropertySchema"
    EngagementPropertySchema:
      type: "object"
      required:
      - "key"
      - "type"
      - "label"
      - "mandatory"
      - "readOnly"
      properties:
        key:
          type: "string"
          description: "Property identifier"
        value:
          type: "string"
          description: "Current property value. Mandatory when <code>\"readOnly\": true</code>"
          nullable: true
        type:
          type: "string"
          description: "Property type"
          enum: [
              'STRING',
              'MEMO',
              'DATE',
              'DATETIME',
              'NUMBER',
              'BOOLEAN',
              'LIST',
              'AUTOSUGGEST'
          ]
        label:
          type: "string"
          description: "Property label"
        hint:
          type: "string"
          description: "Hint for the property"
        mandatory:
          type: "boolean"
          description: "<code>true</code> if the property is mandatory, else <code>false</code>"
        readOnly:
          type: "boolean"
          description: "<code>true</code> if the property is read-only, else <code>false</code>"
        listDetails:
          type: "object"
          description: "List details for type=LIST"
          properties:
            remoteSource:
              type: "string"
              description: "url for remote values. NYI"
            values:
              type: "array"
              items:
                type: "object"
                required:
                  - id
                  - label
                properties:
                  "id":
                    "type": "string"
                    "description": "Unique identifier for gw"
                  "label":
                    "type": "string"
                    "description": "Label to show in the form"
        autosuggestDetails:
          type: "object"
          description: "List details for type=AUTOSUGGEST"
          properties:
            remoteValues:
              type: "string"
              description: "url for remote values. NYI"
            values:
              type: "array"
              items:
                type: "object"
                required:
                  - id
                  - label
                properties:
                  "id":
                    "type": "string"
                    "description": "Unique identifier for gw"
                  "label":
                    "type": "string"
                    "description": "Label to show in the form"
    CreateEngagement:
      type: "object"
      required:
        - "id"
        - "activityOrigin"
        - "activityDump"
        - "fieldActivityOrign"
        - "fieldActivityDump"
      properties:
        context:
          type: "string"
          description: "Contesto di applicazione della richiesta"
        id: 
          type: "string"
          description: "WPSO Unique identifier"
          example:
            '12345'
        activityOrigin:
          type: "string"
          description: "Activity origin"
          enum: [
            'core',
            'ap',
            'works'
          ]
        activityDump:
          type: "object"
          description: "Activity dump"
        fieldActivityOrigin:
          type: "string"
          description: "Field activity origin"
          enum: [
            'core',
            'ap',
            'works'
          ]
        fieldActivityDump:
          type: "object"
          description: "Field activity dump"        
    CreateEngagementResponse:
      type: "object"
      required:
      - "fieldServiceId"
      properties:
        fieldServiceId:
          type: "string"
          description: "Field Service Id"
    ExtraDataEngagement:
      type: "object"
      required:
      - "externalId"
      - "externalSequence"
      properties:
        externalId:
          type: "string"
          description: "CS01 Id"
        externalSequence:
          type: "string"
          description: "CS01 operation sequence"
    ErrorDescription:
      type: "object"
      required:
        - UUID
        - message
      properties:
        UUID:
          type: "string"
          description: "Unique identifier"
        message:
          type: "string"
          description: "Client error description"
        internalMessage:
          type: "string"
          description: "Client internal error description"
    NewMediaNotification:
      type: "object"
      required:
        - "providerId"
        - "sourceId"
        - "parameters"
        - "filename"
        - "docType"
        - "userid"
        - "username"
        - "refDate"
      properties:
        providerId:
          type: "string"
          description: "Id of the media provider that will provide actual media"
          enum: [
              'CARTELLA_DIGITALE'
          ]
        sourceId:
          type: "string"
          description: "Id of the source (ie: id intervento CS01)"
          example: "1234"
        parameters: 
          type: object
          description: "Object of arbitrary key/value pairs to be suggested to media provider for retreiving actual media"
          example: {
            "VistaplusTransactionId": "12345678",
            "FileIndex": "0",
            "SocietaCode": "01"
          }
        filename:
          type: string
          description: "Filename of the new media"
          example: "new_media_file.ext"
        userid:
          type: "string"
          description: "User id of the user that loaded media file on the remote provider"
          example: "LIVRAGH"
        username:
          type: "string"
          description: "Name of the user that loaded media file on the remote provider in the form"
          format: "lastname, firstname"
          example: "LIVRAGHI, ALVARO"
        docType:
          type: "string"
          description: "Document type of the new media"
          enum: [
            "FSDOC" # TBD: ipotesi unico tipo documento; in alternativa si dovrebbe condividere i tipi fra WPSO e Cartella Digitale
          ]
        title:
          type: "string"
          description: "Title of the new media"
          example: "Title of new media"
        description:
          type: "string"
          description: "Description of the new media"
          example: "Description of the new media"
        revision:
          type: "string"
          description: "Revision number of the media (i.e. 'v1.0.0', 'rev. 1', ecc.)"
          example: "rev. 1"
        refDate:
          type: string
          description: "The date-time of media loading on the remote provider"
          format: date-time
security:
  - ApiKeyAuth: []
tags:
  - name: NFS
    description: "Routes to be called by NFS to interact with WPSO"
  - name: WPSO
    description: "Routes to be called by WPSO to interact with NFS"
paths:
  /{fieldServiceName}/engagements:
    description: "Collection of engagement resources"
    parameters:
      - $ref: '#/components/parameters/fieldServiceName'
    post:
      summary: "New engagement"
      tags:
        - "WPSO"
      description: "This route allows WPSO to create a new engagement to be sent to NFS.<br><br>The request will be synchronously routed to NFS by calling <code>CreateActivity()</code> method."
      requestBody:
        description: "Engagement creation"
        required: true
        content: 
          "application/json":
            schema:
              $ref: "#/components/schemas/CreateEngagement"
      responses:
        "201":
          description: "Engaged"
          content:
            "application/json":
              schema:
                $ref: "#/components/schemas/CreateEngagementResponse"
  /{fieldServiceName}/engagements/{fieldServiceId}/actions:
    parameters:
      - $ref: '#/components/parameters/fieldServiceName'
      - $ref: '#/components/parameters/fieldServiceId'
    get:
      summary: "Actions allowed for an engagement"
      tags:
        - NFS
      description: "This route must be used by NFS to retrieve the list of the available actions for updating an engagement.<br><br>The reuqest will be synchronously routed to WSPO to discover the actions currently allowed for the specified engagement"
      responses:
        "200":
          description: "In the payload the list of allowed actions"
          content:
            "application/json":
              schema:
                $ref: "#/components/schemas/EngagementRules"
        "401":
          description: "User unauthorized"
          content:
            "application/json":
              schema:
                $ref: "#/components/schemas/ErrorDescription"
        "404":
          description: "Resource not found"
          content:
            "application/json":
              schema:
                $ref: "#/components/schemas/ErrorDescription"
        "500":
          description: "Internal Server Error"
          content:
            "application/json":
              schema:
                $ref: "#/components/schemas/ErrorDescription"
  /{fieldServiceName}/engagements/{fieldServiceId}/history:
    parameters:
      - $ref: '#/components/parameters/fieldServiceName'
      - $ref: '#/components/parameters/fieldServiceId'
    post:
      summary: "Engagement status transition"
      tags:
        - NFS
      description: "This route must be used by NFS to update an engagement by adding a new transition to its history; to do so, NFS provides to WPSO the selected action and the related data gathered during field operations.<br><br>The request will be synchronously routed to WPSO to perform specified <code>action</code> over the related activity."
      requestBody:
        description: "Object that needs to be passed to the server"
        required: true
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/UpdateEngagement"
      responses:
        "204":
          description: "Engagement updated"
        "4XX":
          description: "Client Errors"
          content:
            "application/json":
              schema:
                $ref: "#/components/schemas/ErrorDescription"
        "5XX":
          description: "Server Errors"
          content:
            "application/json":
              schema:
                $ref: "#/components/schemas/ErrorDescription"
  /{fieldServiceName}/engagements/{fieldServiceId}:
    parameters:
      - $ref: '#/components/parameters/fieldServiceName'
      - $ref: '#/components/parameters/fieldServiceId'
    put:
      summary: "Engagement update"
      tags:
        - WPSO
      description: "This route should be used by WPSO to update an engagement.<br><br>The request will be synchronously routed to NFS by calling one of <code>UpdateActivity()</code>, <code>CloseActivity()</code> or <code>CancelActivity()</code> methods based on the status of the WPSO activity."
      requestBody:
        description: "Object that needs to be passed to the server"
        required: true
        content: 
          "application/json":
            schema:
              $ref: "#/components/schemas/CreateEngagement"
      responses:
        "201":
          description: "Engagement updated"
          content:
            "application/json":
              schema:
                $ref: "#/components/schemas/CreateEngagementResponse"
  /{fieldServiceName}/engagements/{fieldServiceId}/events:
    parameters:
      - $ref: '#/components/parameters/fieldServiceName'
      - $ref: '#/components/parameters/fieldServiceId'
    post:
      summary: "Engagement field events"
      tags:
        - NFS
      description: "This route must be used by NFS to notify WPSO of a field event related to the specified engagement (not an event triggered by an WPSO action).<br><br>The reuqest will be synchronously routed to keep track of the suggested event"
      requestBody:
        description: "Object that needs to be passed to the server"
        required: true
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/EngagementEvent"
      responses:
        "204":
          description: "NFS core event successfully recorded"
        "4XX":
          description: "Client Errors"
          content:
            "application/json":
              schema:
                $ref: "#/components/schemas/ErrorDescription"
        "5XX":
          description: "Server Errors"
          content:
            "application/json":
              schema:
                $ref: "#/components/schemas/ErrorDescription"
  /{fieldServiceName}/activities/{activityId}/media-notifications:
    description: "Collection of media notifications"
    parameters:
      - $ref: '#/components/parameters/fieldServiceName'
      - $ref: '#/components/parameters/activityId'
    post:
      summary: "Activity new medium notification"
      tags:
        - "NFS"
      description: "This route allows NFS to provide the necessary information about a particular medium (document, photo, video, etc.) to enable WPSO to later retrieve actual medium from the suggested remote CMS (e.g. Cartella Digitale, VistaPlus, Alfresco, etc.).<br><br>Retrieval of the medium is performed by WPSO asynchronously."
      requestBody:
        required: true
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/NewMediaNotification"
      responses:
        "204":
          description: "Media notification successfully submitted"
