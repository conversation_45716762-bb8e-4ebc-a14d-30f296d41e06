# ARTLEGACY

## Installazione from scratch

 1. configurare il database seguendo le istruzioni presenti nel file `$COM/share/sql/ART/core/dumpdb/README`
 
 2. creare in `$HOME` i seguenti symlink:
    - `ln -sv $COM/share/www/ARTLEGACY ARTNEW`
    - `ln -sv ARTNEW ART`
 
 3. eseguire
    - `$HOME/ARTNEW/Appl/rilascio/crea_symlink.sh -p $HOME/{PROJECT}`
 
 4. adeguare il file `$HOME/.api.art.conf` con l'opportuno **ARTID**

## Upgrade da versione precedente alla versione 7.0.0

1. effettuare l'upgrade di **ARTNEW** alla versione `7.0.0` creando la patch dal vecchio repository

2. eseguire in `$HOME`:
    - `rm ARTNEW`
    - `ln -sv $COM/share/www/ARTLEGACY ARTNEW`

3. eseguire
    - `$HOME/ARTNEW/Appl/rilascio/crea_symlink.sh -p $HOME/{PROJECT}`
 
4. ricreare i symlink eventualmente presenti nella vecchia istanza **ARTNEW**

5. effettuare l'upgrade alla versione corrente

NB: dopo le opportune verifiche di funzionamento è possibile eliminare la vecchia installazione di **ARTNEW** in `$HOME/ARTNEW_7.0.0`

