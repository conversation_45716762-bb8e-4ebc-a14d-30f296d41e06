#
################ PROCEDURE IN CRONTAB
#
### invio SIGHUP ai demoni per far loro riaprire i file di log con la data corrente
00 00 * * * . $HOME/.bash_profile; daemons_signal_hup.sh -q
#
### invio reportistica
23 00 * * * . $HOME/.bash_profile; exec.sh -- query_monitoraggio WPSO -t c > /dev/null
#
### invio alert OSS
00 1,3,5,7,9,11,13,15,17,19,21,23 * * * . $HOME/.bash_profile; exec.sh -- query_monitoraggio WPSO_OSS -t c > /dev/null
#
### gestisco la ricezione delle nuove ODA
00 8,12,16,20 * * * . $HOME/.bash_profile; workorder_management -t c > /dev/null
#
### gestisco i contatori giornalieri
34 00 * * * . $HOME/.bash_profile; network_counters -t c > /dev/null
#
### resend a CS01
28 01 * * 1 . $HOME/.bash_profile; external_sync_resend -t c > /dev/null
#
### chiude le NETWORK ferme in VERIFICA_DOCUMENTAZIONE da più di 15 giorni
02 02 * * * . $HOME/.bash_profile; chiusura_network_attesa_doc -t c > /dev/null
#
### Verifica i ROE per cui è necessario calcolare le coordinate
02 07,17 * * * . $HOME/.bash_profile; roe_recupera_coordinate -t c > /dev/null
#
### aggiornamento bundle certificati
10 00 * * * . ~/.bash_profile; cat /etc/ssl/certs/*.crt /etc/ssl/certs/*.pem $HOME/var/ssl/ca/elastic_http_ca.*.crt > $HOME/var/ssl/ca/bundle.crt
#
### archiviazione log
38 04 * * * . ~/.bash_profile; YYYY=$(date +\%Y); mkdir -p $LOG_PATH/old/$YYYY/; for f in $(find $LOG_PATH/ -path $LOG_PATH/old -prune -o -type f -mtime +5 ! -name 'daemon.*' -printf '\%P\n' | grep -v '^./old$'); do if [ $(dirname $f) == '.' ]; then gzip $LOG_PATH/$f; mv $LOG_PATH/$f.gz $LOG_PATH/old/$YYYY/ >/dev/null; else gzip $LOG_PATH/$f; mkdir -p $LOG_PATH/old/$YYYY/$(dirname $f)/; mv $LOG_PATH/$f.gz $LOG_PATH/old/$YYYY/$(dirname $f)/ >/dev/null; fi; done; unset YYYY
#
### gestisco acquisizione dati Network SAP
23 00 * * * . $HOME/.bash_profile; network_dati_Sap -t c > /dev/null
#
### gestisco acquisizione dati PTE SAP
27 00 * * * . $HOME/.bash_profile; pte_dati_Sap -t c > /dev/null
#