# README.dbjobs

## Svecchiamento tabelle interscambio CS01

Ogni notte viene lanciato un job con l'utente `CS01_EXCHANGE@xENFTTHCORE` per cancellare i vecchi messaggi (header e body) per CS01.

Vengono eliminati più vecchi di 7 giorni con le seguenti caratteristiche:
* messaggi legati a fasi di attività in corso mantenendo l'ultimo messaggio
* messaggi legati a *non fasi* di attività in corso mantenendo l'ultimo messaggio
* messaggi legati ad attività di `EXTERNAL_SYNC` in stato finale

### Procedura

```sql
CREATE OR REPLACE
PROCEDURE      cs01_exchange.cleaning_enqueue_h as
begin
delete tbl_art_enqueue_h
where id in (
select h.id
from tbl_art_enqueue_h h
where h.evento not in ('APERTURA_LAVORO', 'CHIUSURA_LAVORO_OK', 'CHIUSURA_LAVORO_KO')
  and h.id < (
   select max(h2.id)
   from tbl_art_enqueue_h h2
   where h2.id_source = h.id_source
   and h2.evento not in ('APERTURA_LAVORO', 'CHIUSURA_LAVORO_OK', 'CHIUSURA_LAVORO_KO')
  )
  and h.data_elaborazione < trunc(sysdate)-7
union
select h.id
from tbl_art_enqueue_h h
where h.evento in ('APERTURA_LAVORO', 'CHIUSURA_LAVORO_OK', 'CHIUSURA_LAVORO_KO')
  and h.id < (
   select max(h2.id)
   from tbl_art_enqueue_h h2
   where h2.id_source = h.id_source
   and h2.evento in ('APERTURA_LAVORO', 'CHIUSURA_LAVORO_OK', 'CHIUSURA_LAVORO_KO')
  )
  and h.data_elaborazione < trunc(sysdate)-7
union
select h.id
from tbl_art_enqueue_h h
   join core_art.v_attivita vatt on vatt.id = h.id_source
   join core_art.a_padre_a_figlio apaf on apaf.padre = vatt.id
   join core_art.v_attivita vatt2 on vatt2.id = apaf.figlio
where h.data_elaborazione < trunc(sysdate)-7
  and vatt2.in_Stato_finale = 'Y'
);
commit;
end;
/
```