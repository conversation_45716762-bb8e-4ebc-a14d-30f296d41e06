# Apache Kafka

## Software Requirements

- Confluent Platform
- Java Development kit 1.8
- jq 

## Confluent Platform

> **Confluent Platform** makes it easy build real-time data pipelines and streaming applications. By integrating data from multiple sources and locations into a single, central Event Streaming Platform for your company.
> 
> Confluent Platform lets you focus on how to derive business value from your data rather than worrying about the underlying mechanics such as how data is being transported or massaged between various systems. Specifically, the Confluent Platform simplifies connecting data sources to Apache Kafka, building applications with Kafka, as well as securing, monitoring, and managing your Kafka infrastructure.
> 
> At the core of Confluent Platform is [Apache Kafka](https://kafka.apache.org/), the most popular open source distributed streaming platform.
> 
> The key capabilities of Kafka are:
> 
> - Publish and subscribe to streams of records
> - Store streams of records in a fault tolerant way
> - Process streams of records
> 
> Each release of Confluent Platform includes the latest release of Apache Kafka and additional tools and services that make it easier to build and manage an Event Streaming Platform.
> 
> Confluent Platform delivers both community and commercially licensed features that complement and enhance your Apache Kafka deployment.
> 
> Confluent **Community Features** are:
> 
> - *Apache Kafka*, "a distributed streaming system"
> - *Apache Connect*/*Confluent Connectors*, that "leverage the Kafka Connect API to connect Apache Kafka to other data systems"
> - *Confluent Schema Registry*, ensures "compatibility of data and code as the system grows and evolves"
> - *Confluent KSQL*, "a streaming SQL engine for Apache Kafka"
> - *Confluent REST Proxy*, "makes it fast and easy to produce and consume messages through Kafka"

### Installation

Confluent Platform may be deployed either in on-premises, cointainers or cloud scenarios.

In WPSO architecture the on-premises scenario best fits.

In addition, a single-node deployment satisfies business  requirements in which a from-scratch resume is acceptable.

Here installation instruction that describes installation in a Linux environment using a TAR file:

    $ wget -O http://packages.confluent.io/archive/5.1/confluent-community-5.1.2-2.11.tar.gz

    $ tar -xvzf confluent-community-5.1.2-2.11.tar.gz

### Add Oracle JDBC driver to Kafka Connect JDBC connector

It's necessary also to install the Oracle JDBC driver in:

	$ confluent/share/java/kafka-connect-jdbc/
	$ ln -vs /u01/app/oracle/product/current/jdbc/lib/ojdbc6.jar ojdbc6.jar

### Environment

In order to change default directory where Confluent stores data in non-persistent mode (development mode), you can set the ´CONFLUENT_CURRENT´ environment variable:

    $ mkdir <path-to-confluent>/var

    $ export CONFLUENT_CURRENT=<path-to-confluent>/var


#### Local configuration

This is a configuration sample to add to ´.bash_profile´:

    ...

    # Configurazione ENV per Apache Kafka
    export OPT=$HOME/opt

    # JDK environments
    export JAVA_HOME=$OPT/jdk
    export CLASSPATH=$JAVA_HOME/lib
    PATH=$JAVA_HOME/bin:$PATH

    # Confluent environments
    export CONFLUENT_HOME=$OPT/confluent
    export CONFLUENT_CURRENT=$HOME/var/confluent
    mkdir -p $CONFLUENT_CURRENT
    PATH=$CONFLUENT_HOME/bin:$PATH

    # jq
    PATH="$OPT/bin:$PATH"

    ...

### Start confluent platform

	$ confluent start
