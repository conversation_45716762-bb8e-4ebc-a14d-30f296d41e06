# README

# Requirements #

## RHEL yum packages ##
* firefox
* xorg-x11-auth
* dbus-x11
* ImageMagick-devel
* ImageMagick-perl
* gd-devel

## Perl ##

* perl -> 5.10.1

### CPAN ###

* Date::Calc -> 6.3
* File::Basename -> 2.77
* Carp -> 1.3301
* utf8 -> 1.07
* JSON -> 2.26
* MIME::Types -> 1.31
* File::Copy -> 2.14
* HTTP::Status -> 5.817
* Template -> 2.25
* Locale::TextDomain -> 1.24
* Dancer2 -> 0.160000
* Apache::DBI -> 1.08
* DBI -> 1.634
* Crypt::JWT -> 0.017
* JSON::MaybeXS -> 1.003005
* JSV::Validator -> 0.08
* REST::Client -> 273
* Image::Magick -> 6.89
* GD -> 2.59
* GD::Text -> 0.86 
* GD::Barcode::Code39 -> 1.1
* MIME::Base32 -> 1.303
* XML::XPath -> 1.44
* Encode::Detect -> 1.0.1
* Selenium::Remote::Driver -> 1.44
* Crypt::OTP -> 2.0.0
* MIME::Base64 -> 3.13
* Email::Valid -> 1.196
* Log::Log4perl -> 1.54
* Log::Log4perl::Layout::JSON -> 0.59
* URI::Encode -> 1.1.1

### COMMON ###

* COMMON -> COM_14.11.1
  * SIRTI::DB
  * SIRTI::Reports
  * API::ART::REST

## Other software ##

### selenium-server-standalone -> 3.141.59

Scaricabile da https://selenium-release.storage.googleapis.com/3.141/selenium-server-standalone-3.141.59.jar

### geckodriver -> 0.29.1

Scaricabile da https://github.com/mozilla/geckodriver/releases/download/v0.29.1/geckodriver-v0.29.1-linux64.tar.gz

### wkhtmltopdf

Questo software viene utilizzato per la generazione degli statini in pdf e deve essere preinstallato a livello utente o di sistema.

Il pacchetto per architetture Linux 64-bit può essere scaricato da Google Drive al seguente [link](https://drive.google.com/open?id=0ByEmeC4H5QD4QVJRLTgxOE03elU) e scompattato in `$HOME/opt/`

**NOTA**: dipende dalle seguenti librerie: zlib, fontconfig, freetype, X11 libs (libX11, libXext, libXrender)
