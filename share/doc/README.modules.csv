<PERSON><PERSON><PERSON>;dvmas003 (07/10/2019);tvmas109 (26/09/2018);dlvspso001 (07/10/2019)
Apache::ASP;2.61;;2.62
Apache::DBI;1.08;1.08;1.12
Apache2::Const;2.000005;2.000005;2.000010
Array::Utils;0.5;0.5;0.5
Aspect;1.04;;n/a
Authen::Radius;0.17;0.17;0.26
Benchmark;1.11;1.11;1.22
bytes;1.03;1.03;1.05
Cache::Memcached;1.30;1.30;1.30
Carp;1.3301;1.3301;1.42
CGI;3.49;3.49;4.38
CGI::Carp;3.45;;4.38
CGI::Session;n/a;;n/a
CharsetDetector;n/a;n/a;n/a
Class::Singleton;1.4;1.4;1.5
Config;version not found;version not found;5.026001
Config::Simple;4.58;;4.58
constant;1.17;1.17;1.33
Crypt::JWT;0.010;0.010;0.020
Cwd;3.47;3.3;3.74
Dancer2;0.160000;0.160000;0.206000
Dancer2::Core::Types;0.160000;0.160000;0.206000
Dancer2::FileUtils;0.160000;0.160000;0.206000
Dancer2::Plugin;0.160000;0.160000;0.206000
Data::Dumper;2.128;2.128;2.167
Date::Calc;6.3;6.3;6.4
Date::Calendar;6.3;6.3;6.4
Date::Calendar::Profiles;6.3;6.3;6.4
Date::Language;1.10;1.10;1.10
DateTime;1.18;1.18;1.46
DateTime::Format::ISO8601;0.08;0.08;0.08
DateTime::Format::Strptime;1.56;1.56;1.75
DBD::Oracle;1.24;1.74;1.74
DBI;1.634;1.634;1.64
Digest::MD5;2.39;2.39;2.55
Email::MIME::ContentType;1.017;1.017;1.022
Email::Stuffer;0.012;0.012;0.016
Encode;2.35;2.35;2.96
English;1.04;1.04;1.10
enum;1.016;1.016;1.11
Excel::Writer::XLSX;0.83;0.83;0.96
Expect;1.21;1.21;n/a
Exporter;5.63;5.63;5.72
Fcntl;1.06;1.06;1.13
File::Basename;2.77;2.77;2.85
File::CacheDir;1.30;1.30;1.30
File::Copy;2.14;2.14;2.32
File::Find;1.14;1.14;1.34
File::Path;2.08;2.08;2.15
File::Scan::ClamAV;1.93;1.93;1.95
File::Spec;3.47;3.3;3.74
File::Spec::Functions;3.47;3.3;3.74
File::Temp;0.22;0.22;0.2304
File::Type;n/a;n/a;n/a
FindBin;1.50;1.50;1.51
Getopt::Long;2.42;2.42;2.5
Getopt::Std;1.06;1.06;1.12
GTop;n/a;n/a;n/a
HTML::Entities;3.64;3.64;3.69
HTML::Template;n/a;n/a;2.97
HTTP::Headers;5.827;5.827;6.14
HTTP::Status;5.817;5.817;6.14
integer;1.00;1.00;1.01
IO::Handle;1.28;1.28;1.36
IO::Poll;0.07;0.07;0.10
IO::String;1.08;1.08;1.08
IO::Zlib;1.09;1.09;1.10
IPC::SharedCache;n/a;n/a;n/a
IPC::SysV;2.01;2.01;2.07
JSON;2.90;2.90;2.97001
JSON::MaybeXS;1.003005;1.003005;1.00301
lib;0.62;0.62;0.64
List::Util;1.46;1.41;1.49
Locale::TextDomain;1.24;1.24;1.29
Log::Log4perl;1.30;1.30;1.49
Log::Log4perl::Logger;version not found;version not found;version not found
LWP::UserAgent;5.833;5.833;6.31
Mail::Address;2.07;2.07;2.20
Mail::Cclient;1.12;1.12;n/a
Mail::IMAPClient;3.39;n/a;n/a
Mail::POP3Client;n/a;n/a;n/a
Memoize;1.01_03;1.01_03;1.03_01
MIME::Base64;3.14;3.14;3.15
MIME::Entity;5.502;5.502;5.509
MIME::Lite;3.027;3.027;3.030
MIME::Parser;5.502;5.502;5.509
MIME::QuotedPrint;3.13;3.13;3.13
MIME::Types;1.31;1.31;2.17
MIME::WordDecoder;version not found;version not found;version not found
MongoDB;v0.708.0.0;n/a;v1.8.1
MongoDB::MongoClient;v0.708.0.0;n/a;v1.8.1
MongoDB::OID;v0.708.0.0;n/a;v1.8.1
Moo;1.005;1.005;2.003004
Net::FTP;2.77;;3.10
Net::IMAP::Simple;n/a;n/a;1.2209
Net::LDAP;n/a;;n/a
Net::SNMP;n/a;n/a;
Net::SSLeay;1.65;1.65;1.84
overload;1.07;1.07;1.28
POSIX;1.17;1.17;1.76
Proc::Daemon;n/a;n/a;0.23
Scalar::Util;1.46;1.41;1.49
Shell::Base;0.05;n/a;n/a
SOAP::Lite;0.712;0.712;1.26
Spreadsheet::WriteExcel;2.40;2.40;2.40
Spreadsheet::WriteExcel::Big;2.40;2.40;2.40
Storable;2.51;2.21;3.11
strict;1.04;1.04;1.11
subs;1.00;1.00;1.02
Sys::Syslog;0.33;0.33;0.35
TAP::Harness;3.32;3.32;3.39
Template;2.25;;2.27
Test::More;1.001003;1.001003;1.302122
Text::Autoformat;1.669002;1.669002;1.74
Text::CSV_XS;0.74;0.74;1.34
Text::CSV::Encoded;0.10;0.10;0.25
Text::Iconv;1.7;1.7;1.7
Text::Levenshtein;n/a;;n/a
Text::Table;1.133;n/a;1.133
Text::Wrap;2009.0305;2009.0305;2013.0523
Text::Wrapper;1.02;1.02;1.05
threads;1.82;1.72;2.15
threads::shared;1.29;1.29;1.58
Tie::IxHash;1.22;1.22;1.23
Time::HiRes;1.9721;1.9721;1.9754
Time::Local;1.1901;1.1901;1.25
Ubic::Daemon;1.40;1.40;1.60
UNIVERSAL;1.05;1.05;1.13
URI;1.60;1.60;1.73
URI::Escape;3.31;3.31;3.31
URI::QueryParam;version not found;version not found;1.73
utf8;1.07;1.07;1.19
vars;1.01;1.01;1.03
warnings;1.06;1.06;1.37
XML::Parser;2.36;2.36;2.44
XML::Parser::Lite::Tree;n/a;n/a;0.14
XML::Xerces;260.2;260.2;260.2
XML::Xerces::PerlErrorHandler;n/a;n/a;n/a
YAML;0.72;0.72;1.24
YAML::XS;0.59;n/a;0.69
