<?xml version = '1.0' encoding = 'UTF-8'?>
<Table class="oracle.dbtools.crest.model.design.relational.Table" id="09019C72-C862-CA42-F3C9-9AFC798664CC" directorySegmentName="seg_0" name="RA_TARGET_ACK">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:25 UTC</createdTime>
<commentInRDBMS>Tabella gestione lavorazione ACK TARGET</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<schemaObject>711EC94B-D353-9DFF-5EFF-B6142FF83D6E</schemaObject>
<pkName>RA_TARGET_ACK_PK</pkName>
<fonts itemClass="oracle.dbtools.crest.model.design.FontObjectWr">
<FontObject>
<foType>Titolo</foType>
<fontStyle>1</fontStyle>
<colorRGB>-16776961</colorRGB>
</FontObject>
<FontObject>
<foType>Colonna</foType>
<colorRGB>-16777216</colorRGB>
</FontObject>
<FontObject>
<foType>Tipo di dati</foType>
<colorRGB>-16744448</colorRGB>
</FontObject>
<FontObject>
<foType>Elemento chiave privata</foType>
<colorRGB>-16776961</colorRGB>
</FontObject>
<FontObject>
<foType>Elemento chiave esterna</foType>
<colorRGB>-16777216</colorRGB>
</FontObject>
<FontObject>
<foType>Elemento chiave univoca</foType>
<colorRGB>-16776961</colorRGB>
</FontObject>
<FontObject>
<foType>Non nullo</foType>
<colorRGB>-65536</colorRGB>
</FontObject>
<FontObject>
<foType>Chiave</foType>
<colorRGB>-16777216</colorRGB>
</FontObject>
</fonts>
<columns itemClass="oracle.dbtools.crest.model.design.relational.Column">
<Column name="RA_TARGET_ID" id="118E7737-3713-A998-7E83-CAEB8D0A99C7">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<commentInRDBMS>ID riferimento TARGET</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<dataTypePrecision>11</dataTypePrecision>
<delegate>D79AA8D5-1A24-000E-137D-88EE24E5574F</delegate>
<autoIncrementCycle>false</autoIncrementCycle>
<associations>
<colAssociation fkAssociation="43A8B2C8-3308-8D7E-08DB-384CDA98E688" referredColumn="D79AA8D5-1A24-000E-137D-88EE24E5574F"/>
</associations>
</Column>
<Column name="SESSION_ID" id="1E285511-0702-FA31-886C-DAB7311FC551">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<commentInRDBMS>Session ID fetch ACK TARGET</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<dataTypePrecision>11</dataTypePrecision>
<delegate>7BDF236F-BB6A-80E0-13F5-66E32B9AB418</delegate>
<autoIncrementCycle>false</autoIncrementCycle>
<associations>
<colAssociation fkAssociation="9F92B3FA-9193-B95E-90F6-1D435F6447B9" referredColumn="7BDF236F-BB6A-80E0-13F5-66E32B9AB418"/>
</associations>
</Column>
<Column name="FETCH_ACK_DATE" id="2A4CC1FD-C101-575F-F8FE-34A92C20EA5D">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<commentInRDBMS>Date fetch ACK TARGET</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT007</logicalDatatype>
<dataTypeSize>7</dataTypeSize>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="NOTE" id="9485180A-D754-71D6-39AA-B56B620FBB0B">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<commentInRDBMS>NOTE ACK TARGET</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT024</logicalDatatype>
<dataTypeSize>4000 CHAR</dataTypeSize>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
</columns>
<indexes itemClass="oracle.dbtools.crest.model.design.relational.Index">
<ind_PK_UK id="C3EAC54E-C29F-C7FE-27F2-761577130D0D" name="RA_TARGET_ACK_PK">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<schemaObject>711EC94B-D353-9DFF-5EFF-B6142FF83D6E</schemaObject>
<pk>true</pk>
<indexState>Primary Constraint</indexState>
<indexColumnUsage>
<colUsage columnID="118E7737-3713-A998-7E83-CAEB8D0A99C7"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="5EE6A471-4B27-61F5-4B8A-CC800036F165" name="RA_TARGET_ACK_FK0">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<indexState>Foreign Key</indexState>
<indexColumnUsage>
<colUsage columnID="118E7737-3713-A998-7E83-CAEB8D0A99C7"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="13C62A6F-E704-044E-6BF6-62997CE99302" name="RA_TARGET_ACK_FK1">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<indexState>Foreign Key</indexState>
<indexColumnUsage>
<colUsage columnID="1E285511-0702-FA31-886C-DAB7311FC551"/>
</indexColumnUsage>
</ind_PK_UK>
</indexes>
</Table>