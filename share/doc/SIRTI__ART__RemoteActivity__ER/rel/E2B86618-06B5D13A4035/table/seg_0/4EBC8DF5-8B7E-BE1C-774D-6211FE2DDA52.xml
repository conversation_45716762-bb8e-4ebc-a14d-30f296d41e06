<?xml version = '1.0' encoding = 'UTF-8'?>
<Table class="oracle.dbtools.crest.model.design.relational.Table" id="4EBC8DF5-8B7E-BE1C-774D-6211FE2DDA52" directorySegmentName="seg_0" name="RA_DATA">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:25 UTC</createdTime>
<commentInRDBMS>Tabella delle informazioni correlate agli eventi</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<schemaObject>711EC94B-D353-9DFF-5EFF-B6142FF83D6E</schemaObject>
<pkName>SYS_C0019164</pkName>
<fonts itemClass="oracle.dbtools.crest.model.design.FontObjectWr">
<FontObject>
<foType>Titolo</foType>
<fontStyle>1</fontStyle>
<colorRGB>-16776961</colorRGB>
</FontObject>
<FontObject>
<foType>Colonna</foType>
<colorRGB>-16777216</colorRGB>
</FontObject>
<FontObject>
<foType>Tipo di dati</foType>
<colorRGB>-16744448</colorRGB>
</FontObject>
<FontObject>
<foType>Elemento chiave privata</foType>
<colorRGB>-16776961</colorRGB>
</FontObject>
<FontObject>
<foType>Elemento chiave esterna</foType>
<colorRGB>-16777216</colorRGB>
</FontObject>
<FontObject>
<foType>Elemento chiave univoca</foType>
<colorRGB>-16776961</colorRGB>
</FontObject>
<FontObject>
<foType>Non nullo</foType>
<colorRGB>-65536</colorRGB>
</FontObject>
<FontObject>
<foType>Chiave</foType>
<colorRGB>-16777216</colorRGB>
</FontObject>
</fonts>
<columns itemClass="oracle.dbtools.crest.model.design.relational.Column">
<Column name="ID" id="5578BBEB-4627-962F-CFDC-524D7619F40C">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<commentInRDBMS>IDentificativo univoco dell&apos;informazione correlata all&apos;evento RA_ID</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<dataTypePrecision>11</dataTypePrecision>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="RA_ID" id="87D53F14-9820-3FF5-19B9-FBB0F07EFF00">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<commentInRDBMS>IDentificativo di correlazione con l&apos;evento</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<dataTypePrecision>11</dataTypePrecision>
<delegate>56EDB25E-869E-614A-D395-3E8BB6EB0C7D</delegate>
<autoIncrementCycle>false</autoIncrementCycle>
<associations>
<colAssociation fkAssociation="46335F89-0162-648C-E149-9BB1C2F85A11" referredColumn="56EDB25E-869E-614A-D395-3E8BB6EB0C7D"/>
</associations>
</Column>
<Column name="NAME" id="7059C9E7-9BA4-E2EB-5F70-C2841D1D49A9">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<commentInRDBMS>Nome dell&apos;informazione correlata all&apos;evento</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT024</logicalDatatype>
<dataTypeSize>64 CHAR</dataTypeSize>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="VALUE" id="41576527-1ECA-9F5B-A2B3-AB96B22FEA9E">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<commentInRDBMS>Valore dell&apos;informazione correlata all&apos;evento</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT024</logicalDatatype>
<dataTypeSize>4000 CHAR</dataTypeSize>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="CHUNK_ID" id="5A7DB1EC-22E0-0B8E-3C27-D05C16C12934">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<dataTypePrecision>11</dataTypePrecision>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
</columns>
<indexes itemClass="oracle.dbtools.crest.model.design.relational.Index">
<ind_PK_UK id="E00D7629-E2DD-57CD-3CA1-F16A5C767F2F" name="SYS_C0019164">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<pk>true</pk>
<indexState>Primary Constraint</indexState>
<indexColumnUsage>
<colUsage columnID="5578BBEB-4627-962F-CFDC-524D7619F40C"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="7B26E0B4-6E21-8BE4-2AD0-D00A41B10F59" name="IX_SYS_C0019164">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<schemaObject>711EC94B-D353-9DFF-5EFF-B6142FF83D6E</schemaObject>
<indexState>Unique Plain Index</indexState>
<indexColumnUsage>
<colUsage columnID="5578BBEB-4627-962F-CFDC-524D7619F40C"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="89F9BE3E-36C8-5AD1-D8C2-130637E4F741" name="IDX_RA_DATA_REF">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<schemaObject>711EC94B-D353-9DFF-5EFF-B6142FF83D6E</schemaObject>
<indexColumnUsage>
<colUsage columnID="87D53F14-9820-3FF5-19B9-FBB0F07EFF00"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="8C469A8D-6846-D152-0C1A-9948A8FB0C3D" name="FK_RA_DATA">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<indexState>Foreign Key</indexState>
<indexColumnUsage>
<colUsage columnID="87D53F14-9820-3FF5-19B9-FBB0F07EFF00"/>
</indexColumnUsage>
</ind_PK_UK>
</indexes>
</Table>