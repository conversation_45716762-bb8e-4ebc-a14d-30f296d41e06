<?xml version = '1.0' encoding = 'UTF-8'?>
<Table class="oracle.dbtools.crest.model.design.relational.Table" id="8026BB24-05F7-0B6D-9A7D-EEF779ED52EF" directorySegmentName="seg_0" name="RA_TARGET">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:25 UTC</createdTime>
<commentInRDBMS>Tabella gestione eventi TARGET</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<schemaObject>711EC94B-D353-9DFF-5EFF-B6142FF83D6E</schemaObject>
<pkName>RA_TARGET_PK</pkName>
<fonts itemClass="oracle.dbtools.crest.model.design.FontObjectWr">
<FontObject>
<foType>Titolo</foType>
<fontStyle>1</fontStyle>
<colorRGB>-16776961</colorRGB>
</FontObject>
<FontObject>
<foType>Colonna</foType>
<colorRGB>-16777216</colorRGB>
</FontObject>
<FontObject>
<foType>Tipo di dati</foType>
<colorRGB>-16744448</colorRGB>
</FontObject>
<FontObject>
<foType>Elemento chiave privata</foType>
<colorRGB>-16776961</colorRGB>
</FontObject>
<FontObject>
<foType>Elemento chiave esterna</foType>
<colorRGB>-16777216</colorRGB>
</FontObject>
<FontObject>
<foType>Elemento chiave univoca</foType>
<colorRGB>-16776961</colorRGB>
</FontObject>
<FontObject>
<foType>Non nullo</foType>
<colorRGB>-65536</colorRGB>
</FontObject>
<FontObject>
<foType>Chiave</foType>
<colorRGB>-16777216</colorRGB>
</FontObject>
</fonts>
<columns itemClass="oracle.dbtools.crest.model.design.relational.Column">
<Column name="ID" id="D79AA8D5-1A24-000E-137D-88EE24E5574F">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<commentInRDBMS>IDentificativo univoco del TARGET</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<dataTypePrecision>11</dataTypePrecision>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="RA_ID" id="58FC3692-DE58-AEFF-BF50-DAFEA3295B08">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<commentInRDBMS>IDentificativo dell&apos;evento principale</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<dataTypePrecision>11</dataTypePrecision>
<delegate>56EDB25E-869E-614A-D395-3E8BB6EB0C7D</delegate>
<autoIncrementCycle>false</autoIncrementCycle>
<associations>
<colAssociation fkAssociation="F31F0F1A-94B7-737D-0609-A14EFCF6804F" referredColumn="56EDB25E-869E-614A-D395-3E8BB6EB0C7D"/>
</associations>
</Column>
<Column name="TARGET_SESSION_ID" id="8EEB432B-D345-A331-4381-C46D0BA7B646">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<commentInRDBMS>Identificativo della sessione</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<dataTypePrecision>11</dataTypePrecision>
<delegate>098BDFE7-5F35-54E9-09CD-7B3D2F259E79</delegate>
<autoIncrementCycle>false</autoIncrementCycle>
<associations>
<colAssociation fkAssociation="A0A1F87E-8EAC-38E7-E8C7-9D50909CC52A" referredColumn="098BDFE7-5F35-54E9-09CD-7B3D2F259E79"/>
</associations>
</Column>
<Column name="NEED_ACK" id="99F5A3A3-C852-E2BA-20F3-E955641B1D50">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<commentInRDBMS>Abilita ACK</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<constraintName>RA_TARGET_CK0</constraintName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT025</logicalDatatype>
<dataTypeSize>1 BYTE</dataTypeSize>
<listOfValues>
<valueDef value="&apos;1&apos;" description=""/>
</listOfValues>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
</columns>
<indexes itemClass="oracle.dbtools.crest.model.design.relational.Index">
<ind_PK_UK id="94725D26-FDEE-1273-CB4B-94E80DD9DCF1" name="RA_TARGET_PK">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<schemaObject>711EC94B-D353-9DFF-5EFF-B6142FF83D6E</schemaObject>
<pk>true</pk>
<indexState>Primary Constraint</indexState>
<indexColumnUsage>
<colUsage columnID="D79AA8D5-1A24-000E-137D-88EE24E5574F"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="4D3CB89D-A9A0-9BE3-ACD4-C0F801504F60" name="RA_TARGET_FK0">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<indexState>Foreign Key</indexState>
<indexColumnUsage>
<colUsage columnID="58FC3692-DE58-AEFF-BF50-DAFEA3295B08"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="A68BA04E-E90A-3C3A-54A5-D869D0F2933B" name="RA_TARGET_FK1">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<indexState>Foreign Key</indexState>
<indexColumnUsage>
<colUsage columnID="8EEB432B-D345-A331-4381-C46D0BA7B646"/>
</indexColumnUsage>
</ind_PK_UK>
</indexes>
</Table>