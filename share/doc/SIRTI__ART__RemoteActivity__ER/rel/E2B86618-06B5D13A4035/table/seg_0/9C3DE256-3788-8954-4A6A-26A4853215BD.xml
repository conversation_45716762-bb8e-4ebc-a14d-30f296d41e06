<?xml version = '1.0' encoding = 'UTF-8'?>
<Table class="oracle.dbtools.crest.model.design.relational.Table" id="9C3DE256-3788-8954-4A6A-26A4853215BD" directorySegmentName="seg_0" name="RA_TARGET_FETCH">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:25 UTC</createdTime>
<commentInRDBMS>Tabella gestione fetch eventi TARGET</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<schemaObject>711EC94B-D353-9DFF-5EFF-B6142FF83D6E</schemaObject>
<pkName>RA_TARGET_FETCH_PK</pkName>
<fonts itemClass="oracle.dbtools.crest.model.design.FontObjectWr">
<FontObject>
<foType>Titolo</foType>
<fontStyle>1</fontStyle>
<colorRGB>-16776961</colorRGB>
</FontObject>
<FontObject>
<foType>Colonna</foType>
<colorRGB>-16777216</colorRGB>
</FontObject>
<FontObject>
<foType>Tipo di dati</foType>
<colorRGB>-16744448</colorRGB>
</FontObject>
<FontObject>
<foType>Elemento chiave privata</foType>
<colorRGB>-16776961</colorRGB>
</FontObject>
<FontObject>
<foType>Elemento chiave esterna</foType>
<colorRGB>-16777216</colorRGB>
</FontObject>
<FontObject>
<foType>Elemento chiave univoca</foType>
<colorRGB>-16776961</colorRGB>
</FontObject>
<FontObject>
<foType>Non nullo</foType>
<colorRGB>-65536</colorRGB>
</FontObject>
<FontObject>
<foType>Chiave</foType>
<colorRGB>-16777216</colorRGB>
</FontObject>
</fonts>
<columns itemClass="oracle.dbtools.crest.model.design.relational.Column">
<Column name="TARGET_ID" id="94D96820-BA7C-FFFD-9012-FDB2A6FD4EC3">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<commentInRDBMS>IDentificativo univoco del TARGET</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<dataTypePrecision>11</dataTypePrecision>
<delegate>D79AA8D5-1A24-000E-137D-88EE24E5574F</delegate>
<autoIncrementCycle>false</autoIncrementCycle>
<associations>
<colAssociation fkAssociation="BCF74BF3-04F0-8706-00AE-3AB2375D147B" referredColumn="D79AA8D5-1A24-000E-137D-88EE24E5574F"/>
</associations>
</Column>
<Column name="TARGET_REF" id="A2BF8CAA-0022-627D-A2AD-DB2C27D33570">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<commentInRDBMS>Riferimento TARGET</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT024</logicalDatatype>
<dataTypeSize>128 CHAR</dataTypeSize>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="FETCH_SESSION_ID" id="F4CFF7E8-53C6-110A-5F50-651A93C9EE48">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<commentInRDBMS>ID sessione fetch del TARGET</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<dataTypePrecision>11</dataTypePrecision>
<delegate>098BDFE7-5F35-54E9-09CD-7B3D2F259E79</delegate>
<autoIncrementCycle>false</autoIncrementCycle>
<associations>
<colAssociation fkAssociation="E4438B65-A116-D29E-35D2-146341BE3BA5" referredColumn="098BDFE7-5F35-54E9-09CD-7B3D2F259E79"/>
</associations>
</Column>
<Column name="FETCH_DATE" id="984607E8-B657-0E27-779F-D5F701D858EB">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<commentInRDBMS>Data fetch del TARGET</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT007</logicalDatatype>
<dataTypeSize>7</dataTypeSize>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="STATUS" id="F4FBEAC3-3E3D-B95D-EA2E-F2FB71B15CD7">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<commentInRDBMS>Status fetch del TARGET</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<dataTypePrecision>5</dataTypePrecision>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="REASON" id="E3B3E619-4EB7-817E-54FB-F8D5E257B3AA">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<commentInRDBMS>Reason fetch del TARGET</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT024</logicalDatatype>
<dataTypeSize>4000 CHAR</dataTypeSize>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
</columns>
<indexes itemClass="oracle.dbtools.crest.model.design.relational.Index">
<ind_PK_UK id="7E36AF75-D51C-6D9F-2BF7-938105306FC8" name="RA_TARGET_FETCH_PK">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<schemaObject>711EC94B-D353-9DFF-5EFF-B6142FF83D6E</schemaObject>
<pk>true</pk>
<indexState>Primary Constraint</indexState>
<indexColumnUsage>
<colUsage columnID="94D96820-BA7C-FFFD-9012-FDB2A6FD4EC3"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="0F02A3A9-CB2A-7D2A-1D50-8778EA9D0FF9" name="RA_TARGET_FETCH_FK0">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<indexState>Foreign Key</indexState>
<indexColumnUsage>
<colUsage columnID="94D96820-BA7C-FFFD-9012-FDB2A6FD4EC3"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="4B7A33FD-CB2B-269A-404D-2BDFBE2F94C2" name="RA_TARGET_FETCH_FK2">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<indexState>Foreign Key</indexState>
<indexColumnUsage>
<colUsage columnID="F4CFF7E8-53C6-110A-5F50-651A93C9EE48"/>
</indexColumnUsage>
</ind_PK_UK>
</indexes>
</Table>