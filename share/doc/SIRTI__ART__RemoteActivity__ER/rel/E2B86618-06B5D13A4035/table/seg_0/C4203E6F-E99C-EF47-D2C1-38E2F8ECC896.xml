<?xml version = '1.0' encoding = 'UTF-8'?>
<Table class="oracle.dbtools.crest.model.design.relational.Table" id="C4203E6F-E99C-EF47-D2C1-38E2F8ECC896" directorySegmentName="seg_0" name="RA">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:25 UTC</createdTime>
<commentInRDBMS>Tabella principale per le comunicazioni REMOTE_ACTIVITY (RA)</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<schemaObject>711EC94B-D353-9DFF-5EFF-B6142FF83D6E</schemaObject>
<pkName>SYS_C0019156</pkName>
<fonts itemClass="oracle.dbtools.crest.model.design.FontObjectWr">
<FontObject>
<foType>Titolo</foType>
<fontStyle>1</fontStyle>
<colorRGB>-16776961</colorRGB>
</FontObject>
<FontObject>
<foType>Colonna</foType>
<colorRGB>-16777216</colorRGB>
</FontObject>
<FontObject>
<foType>Tipo di dati</foType>
<colorRGB>-16744448</colorRGB>
</FontObject>
<FontObject>
<foType>Elemento chiave privata</foType>
<colorRGB>-16776961</colorRGB>
</FontObject>
<FontObject>
<foType>Elemento chiave esterna</foType>
<colorRGB>-16777216</colorRGB>
</FontObject>
<FontObject>
<foType>Elemento chiave univoca</foType>
<colorRGB>-16776961</colorRGB>
</FontObject>
<FontObject>
<foType>Non nullo</foType>
<colorRGB>-65536</colorRGB>
</FontObject>
<FontObject>
<foType>Chiave</foType>
<colorRGB>-16777216</colorRGB>
</FontObject>
</fonts>
<columns itemClass="oracle.dbtools.crest.model.design.relational.Column">
<Column name="ID" id="56EDB25E-869E-614A-D395-3E8BB6EB0C7D">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:25 UTC</createdTime>
<commentInRDBMS>IDentificativo univoco evento</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<dataTypePrecision>11</dataTypePrecision>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="SOURCE_REF" id="6544865C-2C53-5DD3-BA16-1E8E39E68DE9">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:25 UTC</createdTime>
<commentInRDBMS>Identificativo di riferimento dell&apos;origine</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT024</logicalDatatype>
<dataTypeSize>128 CHAR</dataTypeSize>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="INSERT_SESSION_ID" id="CD751F12-B220-2109-FE42-087A978B722D">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:25 UTC</createdTime>
<commentInRDBMS>IDentificativo della sessione che ha eseguito l&apos;inserimento dell&apos;evento</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<dataTypePrecision>11</dataTypePrecision>
<delegate>7BDF236F-BB6A-80E0-13F5-66E32B9AB418</delegate>
<autoIncrementCycle>false</autoIncrementCycle>
<associations>
<colAssociation fkAssociation="276F4742-E02F-2AB6-CBFA-2BFE8085CD23" referredColumn="7BDF236F-BB6A-80E0-13F5-66E32B9AB418"/>
</associations>
</Column>
<Column name="INSERT_DATE" id="2D62621C-C80B-20DD-5437-BC3891764867">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:25 UTC</createdTime>
<commentInRDBMS>Data di registrazione (inserimento) dell&apos;evento</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT007</logicalDatatype>
<dataTypeSize>7</dataTypeSize>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="SCHEDULE_DATE" id="C6DCE5E7-2BA6-DACA-B520-033DF332EA92">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:25 UTC</createdTime>
<commentInRDBMS>Data a partire dalla qualle l&apos;evento dovra&apos; essere triggerato sulla destinazione</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT007</logicalDatatype>
<dataTypeSize>7</dataTypeSize>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="EXPIRY_DATE" id="68AD6181-DBDA-5E7F-54C4-2C1F42906890">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:25 UTC</createdTime>
<commentInRDBMS>Data oltre la quale l&apos;evento non dovra&apos; piu&apos; essere triggerato sulla destinazione</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT007</logicalDatatype>
<dataTypeSize>7</dataTypeSize>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="NEED_ACK" id="55541396-B332-8486-743D-7A4CD99D2C9E">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:25 UTC</createdTime>
<commentInRDBMS>Se valorizzato ad 1 indica che il mittente ha richiesto che venga prodotto un acknowledge alla lettura del messaggio</commentInRDBMS>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<constraintName>CHK_RA_ACK</constraintName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT025</logicalDatatype>
<dataTypeSize>1 BYTE</dataTypeSize>
<listOfValues>
<valueDef value="&apos;1&apos;" description=""/>
</listOfValues>
<autoIncrementCycle>false</autoIncrementCycle>
</Column>
<Column name="EVENT_ID" id="241775C6-5FD3-DA5F-15C3-D502B2A17393">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:25 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<nullsAllowed>true</nullsAllowed>
<useDomainConstraints>false</useDomainConstraints>
<use>1</use>
<logicalDatatype>LOGDT019</logicalDatatype>
<dataTypePrecision>11</dataTypePrecision>
<delegate>387FEA10-7B63-D2C7-6961-B9A3B00C1680</delegate>
<autoIncrementCycle>false</autoIncrementCycle>
<associations>
<colAssociation fkAssociation="81535E71-445D-FC35-05CC-D6AFFB89BE46" referredColumn="387FEA10-7B63-D2C7-6961-B9A3B00C1680"/>
</associations>
</Column>
</columns>
<indexes itemClass="oracle.dbtools.crest.model.design.relational.Index">
<ind_PK_UK id="E3395304-6E52-5719-30C6-FA3D38C5E93B" name="SYS_C0019156">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<pk>true</pk>
<indexState>Primary Constraint</indexState>
<indexColumnUsage>
<colUsage columnID="56EDB25E-869E-614A-D395-3E8BB6EB0C7D"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="DAFDB5B1-2F50-D1D2-4310-B637DA115D2B" name="IX_SYS_C0019156">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<schemaObject>711EC94B-D353-9DFF-5EFF-B6142FF83D6E</schemaObject>
<indexState>Unique Plain Index</indexState>
<indexColumnUsage>
<colUsage columnID="56EDB25E-869E-614A-D395-3E8BB6EB0C7D"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="9B587B75-95F0-3754-8232-6AEAE33AFFED" name="IDX_RA_INSERT_SESSION">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<schemaObject>711EC94B-D353-9DFF-5EFF-B6142FF83D6E</schemaObject>
<indexColumnUsage>
<colUsage columnID="CD751F12-B220-2109-FE42-087A978B722D"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="8D95FD85-5486-0346-4456-2A39D8EF7AE5" name="IDX_RA_INS">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<schemaObject>711EC94B-D353-9DFF-5EFF-B6142FF83D6E</schemaObject>
<indexColumnUsage>
<colUsage columnID="2D62621C-C80B-20DD-5437-BC3891764867"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="8D501615-839F-FAD6-6530-A8D7762D96C7" name="IDX_RA_SCHED">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<schemaObject>711EC94B-D353-9DFF-5EFF-B6142FF83D6E</schemaObject>
<indexColumnUsage>
<colUsage columnID="C6DCE5E7-2BA6-DACA-B520-033DF332EA92"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="B82AC143-9EA1-1C0F-0F49-AC0858ED691C" name="IDX_RA_EXP">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<schemaObject>711EC94B-D353-9DFF-5EFF-B6142FF83D6E</schemaObject>
<indexColumnUsage>
<colUsage columnID="68AD6181-DBDA-5E7F-54C4-2C1F42906890"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="999C3426-7AE4-35D0-8144-5B2A8FD151CD" name="IDX_RA_NEED_ACK">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<schemaObject>711EC94B-D353-9DFF-5EFF-B6142FF83D6E</schemaObject>
<indexColumnUsage>
<colUsage columnID="55541396-B332-8486-743D-7A4CD99D2C9E"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="20FAA63C-FBCF-86D2-DA68-5108F961F376" name="FK_RA_INSERT_SESSION_ID">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<indexState>Foreign Key</indexState>
<indexColumnUsage>
<colUsage columnID="CD751F12-B220-2109-FE42-087A978B722D"/>
</indexColumnUsage>
</ind_PK_UK>
<ind_PK_UK id="9B43E49C-EB37-6C8F-5296-E098EEF0128F" name="RA_FK0">
<createdBy>belotti</createdBy>
<createdTime>2012-10-11 13:26:26 UTC</createdTime>
<generatorID>Generato dall&apos;utente</generatorID>
<ownerDesignName>SIRTI__ART__RemoteActivity__ER</ownerDesignName>
<indexState>Foreign Key</indexState>
<indexColumnUsage>
<colUsage columnID="241775C6-5FD3-DA5F-15C3-D502B2A17393"/>
</indexColumnUsage>
</ind_PK_UK>
</indexes>
</Table>