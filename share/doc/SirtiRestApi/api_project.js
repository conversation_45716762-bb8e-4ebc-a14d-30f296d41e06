define({
  "name": "SIRTI RESTFul API",
  "version": "0.0.1",
  "description": "",
  "header": {
    "title": "Introduction",
    "content": "<h1 id=\"introduction\">Introduction</h1>\n<p>Here you can find all the SIRTI API utils.</p>\n<h2 id=\"dates\">Dates</h2>\n<p>Please note that all dates are ISO formatted strings.</p>\n<h2 id=\"errors\">Errors</h2>\n<p>In case of error a HTTP error status is returned. The body will contain an object defined as follow:</p>\n<ul>\n<li><code>UUID</code>: unique identifier of the error occurred. You must include it if you contact administrators in case of problems.</li>\n<li><code>message</code>: a message describing the error.</li>\n<li><code>internalMessage</code>: available only if you run the webservices in <em>development</em> mode, it contains further information about the error.</li>\n</ul>\n<p>Example:</p>\n<pre><code>HTTP/1.1 400 Bad Request\nContent-Type: application/json\n\n{\n  &quot;UUID&quot;: &quot;39021fefd8ff0e0510735a563abf3701&quot;,\n  &quot;message&quot;: &quot;Uncorrect parameters&quot;,\n  &quot;internalMessage&quot;: &quot;Error in creating system: SYSTEM_TYPE_NAME &#39;BID_MANAGEMENT&#39; undefined!&quot;\n}\n</code></pre><p><strong>Note</strong>: if the status code <code>401 Authorization Required</code> is returned, the body is empty!</p>\n<h2 id=\"prefix\">Prefix</h2>\n<p>By default routes prefix is <code>/rest</code>. You can change it defining environment variable <code>WS_ROUTES_PREFIX</code>.</p>\n"
  },
  "order": [],
  "template": {
    "withCompare": false
  },
  "sampleUrl": false,
  "apidoc": "0.2.0",
  "generator": {
    "name": "apidoc",
    "time": "2016-02-03T15:35:12.806Z",
    "url": "http://apidocjs.com",
    "version": "0.12.3"
  }
});