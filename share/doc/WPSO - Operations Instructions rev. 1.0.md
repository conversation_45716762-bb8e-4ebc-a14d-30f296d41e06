```
 _    _   ______    _____    _____ 
| |  | |  | ___ \  /  ___|  |  _  |
| |  | |  | |_/ /  \ `--.   | | | |
| |/\| |  |  __/    `--. \  | | | |
\  /\  /  | |      /\__/ /  \ \_/ /
 \/  \/   \_|      \____/    \___/ 
                             
 _____                     _              
/  ___|                   (_)             
\ `--.   ___  _ __ __   __ _   ___   ___  
 `--. \ / _ \| '__|\ \ / /| | / __| / _ \ 
/\__/ /|  __/| |    \ V / | || (__ |  __/ 
\____/  \___||_|     \_/  |_| \___| \___| 
 _____              _                 _                 _                
|  _  |            | |               | |               | |               
| | | | _ __   ___ | |__    ___  ___ | |_  _ __   __ _ | |_   ___   _ __ 
| | | || '__| / __|| '_ \  / _ \/ __|| __|| '__| / _` || __| / _ \ | '__|
\ \_/ /| |   | (__ | | | ||  __/\__ \| |_ | |   | (_| || |_ | (_) || |   
 \___/ |_|    \___||_| |_| \___||___/ \__||_|    \__,_| \__| \___/ |_|   


  ____                           __    _                 
 / __ \   ___  ___   ____ ___ _ / /_  (_) ___   ___   ___
/ /_/ /  / _ \/ -_) / __// _ `// __/ / / / _ \ / _ \ (_-<
\____/  / .__/\__/ /_/   \_,_/ \__/ /_/  \___//_//_//___/
       /_/                                                                                                                      
   ____             __                    __    _                 
  /  _/  ___   ___ / /_  ____ __ __ ____ / /_  (_) ___   ___   ___
 _/ /   / _ \ (_-</ __/ / __// // // __// __/ / / / _ \ / _ \ (_-<
/___/  /_//_//___/\__/ /_/   \_,_/ \__/ \__/ /_/  \___//_//_//___/

rev. 1.0 - 14-03-2025
```

---

- [1. GRACEFUL SHUTDOWN](#1-graceful-shutdown)
  - [1.1. Webapp](#11-webapp)
    - [1.1.1. hostname: `prvwsrwp101`](#111-hostname-prvwsrwp101)
    - [1.1.2. Hostname: `prvwsrwp001`](#112-hostname-prvwsrwp001)
  - [1.2. Web Services](#12-web-services)
    - [1.2.1. Hostname: `prvwsrwp101`](#121-hostname-prvwsrwp101)
    - [1.2.2. Hostname: `prvwsrwp001`](#122-hostname-prvwsrwp001)
  - [1.3. Microservices](#13-microservices)
    - [1.3.1. Hostname: `prvosswp001`](#131-hostname-prvosswp001)
  - [1.4. Confluent Platform Community Edition (Apache Kafka)](#14-confluent-platform-community-edition-apache-kafka)
    - [1.4.1. Hostname: `prvkkwp002`](#141-hostname-prvkkwp002)
  - [1.5. Kibana](#15-kibana)
    - [1.5.1. Hostname: `prvkbnwp002`](#151-hostname-prvkbnwp002)
  - [1.6. Elasticsearch](#16-elasticsearch)
    - [1.6.1. Hostname: `prvelswp002`](#161-hostname-prvelswp002)
  - [1.7. NOTES](#17-notes)
- [2. STARTUP](#2-startup)
  - [2.1. Elasticsearch](#21-elasticsearch)
    - [2.1.1. Hostname: `prvelswp002`](#211-hostname-prvelswp002)
  - [2.2. Kibana](#22-kibana)
    - [2.2.1. Hostname: `prvkbnwp002`](#221-hostname-prvkbnwp002)
  - [2.3. Confluent Platform Community Edition (Apache Kafka)](#23-confluent-platform-community-edition-apache-kafka)
    - [2.3.1. Hostname: `prvkkwp002`](#231-hostname-prvkkwp002)
  - [2.4. Microservices](#24-microservices)
    - [2.4.1. Hostname: `prvwsrwp001`](#241-hostname-prvwsrwp001)
  - [2.5. Web Services](#25-web-services)
    - [2.5.1. Hostname: `prvwsrwp101`](#251-hostname-prvwsrwp101)
    - [2.5.2. Hostname: `prvosswp001`](#252-hostname-prvosswp001)
  - [2.6. Webapp](#26-webapp)
    - [2.6.1. Hostname: `prvwsrwp001`](#261-hostname-prvwsrwp001)
    - [2.6.2. Hostname: `prvwsrwp101`](#262-hostname-prvwsrwp101)

---

## 1. GRACEFUL SHUTDOWN

### 1.1. Webapp

#### 1.1.1. hostname: `prvwsrwp101`
```shell
su - wpsoui
httpd -f /home/<USER>/httpd.conf -k stop
```

#### 1.1.2. Hostname: `prvwsrwp001`
```shell
su - wpsoui
httpd -f /home/<USER>/httpd.conf -k stop
```

### 1.2. Web Services

#### 1.2.1. Hostname: `prvwsrwp101`
```shell
su - wpredi
apachectl.sh -C ${ETC}/httpd.REDIWS.conf stop
```

```shell
su - wpsoworks
apachectl.sh -C ${ETC}/httpd.WSART.conf stop
```

```shell
su - wpsoap
apachectl.sh -C ${ETC}/httpd.WSART.conf stop
```

```shell
su - wpsocore
apachectl.sh -C ${ETC}/httpd.WSART.conf stop
apachectl.sh -C ${ETC}/httpd.FSGWWS.conf stop
apachectl.sh -C ${ETC}/httpd.NETWORKGWWS.conf stop
```

#### 1.2.2. Hostname: `prvwsrwp001`
```shell
su - wpredi
apachectl.sh -C ${ETC}/httpd.REDIWS.conf stop
```

```shell
su - wpsoworks
apachectl.sh -C ${ETC}/httpd.WSART.conf stop
apachectl.sh -C ${ETC}/httpd.ARTLEGACY.conf stop
```

```shell
su - wpsoap
apachectl.sh -C ${ETC}/httpd.WSART.conf stop
apachectl.sh -C ${ETC}/httpd.ARTLEGACY.conf stop
```

```shell
su - wpsocore
apachectl.sh -C ${ETC}/httpd.WSART.conf stop
apachectl.sh -C ${ETC}/httpd.ARTLEGACY.conf stop
apachectl.sh -C ${ETC}/httpd.FSGWWS.conf stop
apachectl.sh -C ${ETC}/httpd.NETWORKGWWS.conf stop
kill `cat $ROOT/var/lock/memcached.$(hostname).pid` && rm $ROOT/var/lock/memcached.$(hostname).pid
```

### 1.3. Microservices

#### 1.3.1. Hostname: `prvosswp001`
```shell
su - wpsocore
daemons_stop_and_check.sh
```

```shell
su - wpsoap
daemons_stop_and_check.sh
```

```shell
su - wpsoworks
daemons_stop_and_check.sh
```

### 1.4. Confluent Platform Community Edition (Apache Kafka)

#### 1.4.1. Hostname: `prvkkwp002`

```shell
stop-kafka.sh
```

### 1.5. Kibana

#### 1.5.1. Hostname: `prvkbnwp002`

```shell
sudo systemctl stop kibana.service
```

### 1.6. Elasticsearch

#### 1.6.1. Hostname: `prvelswp002`
```shell
sudo systemctl stop elasticsearch.service
```

### 1.7. NOTES

Potrebbero esserci residue connessioni sui DB Oracle provenienti da:

- `prvasmrv001` (utente: `webssdb`) su schemi `REMOTE_ACTIVITY`
- BI (Gruppo Mainardi) su schemi `*_RPT`
- `PADVCSAQ` via utente `CLIENT_ADVCSAQ`
- SQLDeveloper vari

Tali connessioni possono essere tranquillamente killate

## 2. STARTUP

### 2.1. Elasticsearch

#### 2.1.1. Hostname: `prvelswp002`

```shell
sudo systemctl start elasticsearch.service
```

### 2.2. Kibana

#### 2.2.1. Hostname: `prvkbnwp002`

```shell
sudo systemctl start kibana.service
```

### 2.3. Confluent Platform Community Edition (Apache Kafka)

#### 2.3.1. Hostname: `prvkkwp002`

```shell
start-kafka.sh
```

### 2.4. Microservices

#### 2.4.1. Hostname: `prvwsrwp001`

```shell
su - wpsocore
memcached -d -p 11219 -v -P $ROOT/var/lock/memcached.$(hostname).pid >>$ROOT/var/logs/memcached.$(hostname).log 2>&1
apachectl.sh -C ${ETC}/httpd.WSART.conf start
apachectl.sh -C ${ETC}/httpd.ARTLEGACY.conf start
apachectl.sh -C ${ETC}/httpd.FSGWWS.conf start
apachectl.sh -C ${ETC}/httpd.NETWORKGWWS.conf start
```

```shell
su - wpsoworks
apachectl.sh -C ${ETC}/httpd.WSART.conf start
apachectl.sh -C ${ETC}/httpd.ARTLEGACY.conf start
```

```shell
su - wpsoap
apachectl.sh -C ${ETC}/httpd.WSART.conf start
apachectl.sh -C ${ETC}/httpd.ARTLEGACY.conf start
```

```shell
su - wpredi
apachectl.sh -C ${ETC}/httpd.REDIWS.conf start
```

### 2.5. Web Services

#### 2.5.1. Hostname: `prvwsrwp101`

```shell
su - wpsocore
apachectl.sh -C ${ETC}/httpd.WSART.conf start
apachectl.sh -C ${ETC}/httpd.FSGWWS.conf start
apachectl.sh -C ${ETC}/httpd.NETWORKGWWS.conf start
```

```shell
su - wpsoworks
apachectl.sh -C ${ETC}/httpd.WSART.conf start
```

```shell
su - wpsoap
apachectl.sh -C ${ETC}/httpd.WSART.conf start
```

```shell
su - wpredi
apachectl.sh -C ${ETC}/httpd.REDIWS.conf start
```

#### 2.5.2. Hostname: `prvosswp001`

```shell
su - wpsocore
daemons_start_and_check.sh
```

```shell
su - wpsoap
daemons_start_and_check.sh
```

```shell
su - wpsoworks
daemons_start_and_check.sh
```

### 2.6. Webapp

#### 2.6.1. Hostname: `prvwsrwp001`

```shell
su - wpsoui
httpd -f /home/<USER>/httpd.conf -k start
```

#### 2.6.2. Hostname: `prvwsrwp101`

```shell
su - wpsoui
httpd -f /home/<USER>/httpd.conf -k start
```
