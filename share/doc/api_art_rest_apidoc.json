{"name": "ART RESTFul API", "version": "0.0.1", "header": {"title": "Introduction", "filename": "api_art_rest_apidoc_header.md"}, "order": ["Session", "<PERSON><PERSON>", "Logout", "System", "getSystem", "getSystems", "getSystemsExtended", "getSystemsIds", "getSystemsIdsExtended", "getSystemsGridOuput", "getSystemsForExport", "createSystem", "systemDisableEnable", "systemEnd", "systemInfo", "systemProperties", "systemProperty", "systemPropertyMeasurements", "systemUpsertProperties", "systemGroupsGet", "systemGroupsPost", "systemGroupsPut", "systemGroupsDelete", "getSystemChildren", "Activity", "getActivity", "getActivities", "getActivitiesExtended", "getActivitiesIds", "getActivitiesIdsExtended", "getActivitiesGridOutput", "getActivitiesForExport", "createActivity", "activityInfo", "activityProperties", "getActivityProperties", "activityUpsertProperties", "activityDestUsers", "activityAssign", "activityAssignGetUsers", "activityHistory", "activityCanDoAction", "stepActivity", "getActivityChildren", "attachmentList", "checkMethodsTransitionAttachment", "downloadTransitionAttachment", "deleteTransitionAttachment", "activityLock", "activityUnlock", "getActivityHierarchy", "Tmpfile", "createTmpfile", "downloadTmpfile", "deleteTmpfile", "updateTmpfile", "Shared", "getSharedResources", "Dashboards", "dashboardsConfig", "dashboardsData", "dashboardsDataForExport", "Reports", "reportsOverview", "reportDetails", "Favorites", "createFavorite", "getFavorites", "getFavorite", "updateFavorite", "deleteFavorite", "Instance", "instanceGetAuthenticatedUser", "instanceTypesActivities", "instanceTypesActivitiesTypeInfo", "instanceTypesActivitiesTypeStatuses", "instanceTypesActivitiesTypeActions", "instanceTypeActivityTypeActivityProperties", "instanceModifyActivityProperties", "instanceTypesActivitiesProperties", "instanceActivityPropertiesGroups", "instanceTypeSystemsTypeSystemProperties", "instanceModifySystemProperties", "instanceTypesSystemsProperties", "instanceSystemPropertiesGroups", "instanceGetGroups", "instanceGetRoles", "instanceGetUsers", "instanceCreateUser", "instanceUpdateUserGroups", "instanceAddUserGroup", "instanceDeleteUserGroup", "instanceUpdateUserRoles", "instanceAddUserRole", "instanceDeleteUserRole", "Recap", "Graphs", "instanceTypesActivitiesTypeProperties"], "template": {"withCompare": false}}