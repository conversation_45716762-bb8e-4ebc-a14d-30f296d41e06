# Introduction

Here you can find all the API to interact with ART objects.

## Dates

Please note that all dates are ISO formatted strings.

## Errors

In case of error a HTTP error status is returned. The body will contain an object defined as follow:
* <code>UUID</code>: unique identifier of the error occurred. You must include it if you contact administrators in case of problems.
* <code>message</code>: a message describing the error.
* <code>internalMessage</code>: available only if you run the webservices in *development* mode, it contains further information about the error.

Example:

```
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "UUID": "39021fefd8ff0e0510735a563abf3701",
  "message": "Uncorrect parameters",
  "internalMessage": "Error in creating system: SYSTEM_TYPE_NAME 'BID_MANAGEMENT' undefined!"
}
```

**Note**: if the status code <code>401 Authorization Required</code> is returned, the body is empty!

## Prefix

By default routes prefix is <code>/api/art</code>. You can change it defining environment variable <code>ART_WS_ROUTES_PREFIX</code>.

## Virus Scan

To perform virus scan on uploaded files by route <code>POST /api/art/tmpfiles</code> you must have [ClamAV](http://www.clamav.net/) daemon running on the host running the webservices
and set environment variable <code>CLAMAV_DAEMON_PORT</code> with the value of the TCP/IP port or the unix domain socket where ClamAV is listening.
**Note**: Uploaded files are temporary stored in <code>$TMPDIR</code>; please check that the user running ClamAV daemon can access this directory.
