package API::ART::APP::Activity::ACTIVITY_TYPE_NAME::Binding::Action::ACTION_NAME;

################################################################
#
# ACTIVITY_TYPE_NAME
#
# binding: ACTION_NAME
#
################################################################

use strict;
use warnings;

use base qw(API::ART::Activity::Binding::Action::Base);

#---------------------------------------------------------------
# verranno richiamati i metodi (se presenti)
#
# pre->($self, $activity) prima dell'azione
# post->($self, $activity) dopo l'azione
#
# questi metodi devono ritornare:
# true -> ok
# false -> in caso di errore (occorre valorizzare last_error()
#
# e' possibile rimappare anche le azioni virtuali
#----------------------------------------------------------------

1;
