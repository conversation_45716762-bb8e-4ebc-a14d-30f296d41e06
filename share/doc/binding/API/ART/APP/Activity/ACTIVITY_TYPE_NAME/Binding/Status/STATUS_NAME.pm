package API::ART::APP::Activity::ACTIVITY_TYPE_NAME::Binding::Status::STATUS_NAME;

################################################################
#
# ACTIVITY_TYPE_NAME
#
# binding: STATUS_NAME
#
################################################################

use strict;
use warnings;

use base qw(API::ART::Activity::Binding::Status::Base);

#---------------------------------------------------------------
# verranno richiamati i metodi (se presenti)
#
# on_enter->($self, $activity) prima di entrare nello stato
# on_exit->($self, $activity) uscendo dallo stato
#
# questi metodi devono ritornare:
# true -> ok
# false -> in caso di errore (occorre valorizzare last_error()
#
#----------------------------------------------------------------

1;

