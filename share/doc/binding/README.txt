*******************
B I N D I N G
*******************

Per istanziare le attivita' (usando il binding):
API::ART::Activity::Factory()->new(ART=>$ART, ID=>$id_attivita)

definito il $TIPO_ATTIVITA dell'ID associato all'attivita'

si istanzia:

API::ART::Activity::Binding (che estende API::ART::Activity e ne conserva l'interfaccia)

oppure un suo discendente:

API::ART::APP::Activity::$TIPO_ATTIVITA

ad ogni step (anche virtuale), verra' richiamato (se presente)

nell'ordine:

API::ART::APP::Activity::$TIPO_ATTIVITA::Binding::Status::$NOME_STATO_INIZIALE->on_exit($self, $activity)

API::ART::APP::Activity::$TIPO_ATTIVITA::Binding::Action::$NOME_AZIONE->on_exit($self, $activity)

-- step di ART tramite API::ART::Activity

API::ART::APP::Activity::$TIPO_ATTIVITA::Binding::Status::$NOME_AZIONE->on_exit($self, $activity)

API::ART::APP::Activity::$TIPO_ATTIVITA::Binding::Status::$NOME_STATO_FINALE->on_enter($self, $activity)

se uno di questi metodi restituisce false, si assume che venga impostato $art->last_error():
- lo step() verra' interrotto
- verra' effettuato il rollback sul safepoint di tutte le transazioni sul db (api_art) legate allo step

al fine di evitare la possibilita' di effettuare loop infiniti nella storia attivita, all'interno di uno step, non e' possibile entrare piu' di 2 volte nello stesso stato.

FLOWSTAT (CHECK BINDING)

e' possibile usare il TOOL delle COMMON: 

flowstat 

con il parametro -b

per rappresentare i flussi ART evidenziando:
- azioni e stati con Binding attivo e funzionante (grassetto in nero)
- azioni e stati con Binding attivo ma in errore (grassetto in rosso)

su STDOUT viene stampato .dot

su STDERR viene riportato un breve log sulle classi ricercate

per renderizzare un file .dot es. 

dot -T svg nomefile.svg nomefile.dot
