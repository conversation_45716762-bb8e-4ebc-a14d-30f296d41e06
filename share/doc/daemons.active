#
# file generato con "daemons_gui.sh" in data 30/01/2019 16:47:06
# creato backup della versione precedente in /home/<USER>/WPSOCORE/etc/daemons.active.20190130164706
#
api_art_emailservice
notification_service_ramq.conf
ENEL_FTTH_crea_nuovi_progetti
external_sync
external_sync_ack_handler
loader_management
loader_test_data_management
network
network_out_requests
network_private_permits_notify_close
network_private_permits_notify_open
network_public_permits_notify_close
network_public_permits_notify_open
network_response
network_works_report
network_roe_notify_close
network_roe_notify_open
network_works_notify_close
network_works_notify_open
network_booking_ack
new_tt_ack
roe_private_permits_notify_close
roe_private_permits_notify_open
roe_public_permits_notify_close
roe_public_permits_notify_open
roe_recupera_coordinate
roe_works_notify_close
roe_works_notify_open
pte_private_permits_notify_close
pte_private_permits_notify_open
pte_works_notify_close
pte_works_notify_open
fibercop_notifiche
service_management
preemptive_maintenance_works_notify_open
preemptive_maintenance_works_notify_close
corrective_maintenance_works_notify_open
corrective_maintenance_works_notify_close
corrective_maintenance_booking_ack
extraordinary_maintenance_works_notify_open
extraordinary_maintenance_works_notify_close
ods_management
ods_linked_management
users_management
lc02_booking_ack
api_art_activity_source_external_sync
api_art_activity_source_lc01
api_art_activity_source_lc02
api_art_activity_source_lc03
api_art_activity_source_lc04
api_art_activity_source_lc05
api_art_activity_source_loader
api_art_activity_source_loader_dati_collaudo
api_art_activity_source_manutenzione_correttiva
api_art_activity_source_manutenzione_correttiva_giunzione
api_art_activity_source_manutenzione_preventiva
api_art_activity_source_manutenzione_straordinaria
api_art_activity_source_manutenzione_preventiva_01
api_art_activity_source_manutenzione_straordinaria_01
api_art_activity_source_network
api_art_activity_source_rapporto_lavori
api_art_activity_source_rapporto_lavori_parziale
api_art_activity_source_work_order
api_art_activity_source_shrink
api_art_activity_stream_external_sync
api_art_activity_stream_lc01
api_art_activity_stream_lc02
api_art_activity_stream_lc03
api_art_activity_stream_lc04
api_art_activity_stream_lc05
api_art_activity_stream_loader
api_art_activity_stream_loader_dati_collaudo
api_art_activity_stream_manutenzione_correttiva
api_art_activity_stream_manutenzione_correttiva_giunzione
api_art_activity_stream_manutenzione_preventiva
api_art_activity_stream_manutenzione_straordinaria
api_art_activity_stream_manutenzione_preventiva_01
api_art_activity_stream_manutenzione_straordinaria_01
api_art_activity_stream_network
api_art_activity_stream_rapporto_lavori
api_art_activity_stream_rapporto_lavori_parziale
api_art_activity_stream_work_order
api_art_activity_attachments_source
api_art_activity_system_property_counter
network_bot_speedark_response
network_documentation_timeout
rashrink
remote_media_notifications
preemptive_maintenance_01_works_notify_close
preemptive_maintenance_01_works_notify_open
extraordinary_maintenance_01_works_notify_open
extraordinary_maintenance_01_works_notify_close
ap_notify_generic_event