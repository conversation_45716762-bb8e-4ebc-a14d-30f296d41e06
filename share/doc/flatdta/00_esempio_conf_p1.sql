
set line 1000
set autocommit off
set echo on
set SERVEROUT on  size 1000000
set SERVEROUT off

/*
    esempio di configurazione 
*/


whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;


declare    -- disabilita i trigger se presenti
    c flat_dta.generic_cursor;
    n user_triggers.trigger_name%type;
begin
    open c for
        select trigger_name
        from user_triggers 
        where
            status='ENABLED'
            and table_name in ('ATTIVITA','DATI_TECNICI_ATTIVITA')
    ;
    loop
        fetch c into n;
        exit when c%notfound;
        execute immediate 'alter trigger ' || n || ' disable';
    end loop;
    close c;
end;
/


def tipo_attivita='TESTFLATDTA'
def id_ta="(select id_tipo_attivita from  tipi_attivita where nome_tipo_attivita='&tipo_attivita')"
def mytable='TEST_FLATDATI'


delete dati_tecnici_attivita where
id_attivita in (
    select id_attivita from attivita where id_tipo_attivita in &id_ta
);

delete storia_attivita where 
id_attivita in (
    select id_attivita from attivita where id_tipo_attivita in &id_ta
);

delete attivita where id_tipo_attivita in &id_ta;
delete tdta_dtaflat where id_tipo_attivita in &id_ta;
delete ta_dtaflat where  id_tipo_attivita in &id_ta;
delete tipi_attivita where  id_tipo_attivita in &id_ta; 
delete tipi_dati_tecnici_attivita where descrizione like 'TEST_FLATDTA_%';



insert into tipi_attivita(id_tipo_attivita,descrizione,nome_tipo_attivita)
values (seq_tipi_att.nextval,'test flatdta','&tipo_attivita');


select * from v_ta_dtaflat  where nome_tipo_attivita='&tipo_attivita';

insert into  ta_dtaflat(id_tipo_attivita,tabella_associata,abilitato)  values (&id_ta,'&mytable','Y');


select * from v_ta_dtaflat  where nome_tipo_attivita='&tipo_attivita';




insert into tipi_dati_tecnici_attivita(id_tipo_dato_tecnico_attivita,id_tipo_attivita,tipo_ui,descrizione)
values (seq_tipi_dati_tecnici_att.nextval,-1,'TEXT','TEST_FLATDTA_VARCHAR')
;
insert into tipi_dati_tecnici_attivita(id_tipo_dato_tecnico_attivita,id_tipo_attivita,tipo_ui,descrizione)
values (seq_tipi_dati_tecnici_att.nextval,-1,'TEXT','TEST_FLATDTA_CHAR')
;
insert into tipi_dati_tecnici_attivita(id_tipo_dato_tecnico_attivita,id_tipo_attivita,tipo_ui,descrizione)
values (seq_tipi_dati_tecnici_att.nextval,-1,'TEXT','TEST_FLATDTA_DATE')
;
insert into tipi_dati_tecnici_attivita(id_tipo_dato_tecnico_attivita,id_tipo_attivita,tipo_ui,descrizione)
values (seq_tipi_dati_tecnici_att.nextval,-1,'TEXT','TEST_FLATDTA_NUMBER')
;
insert into tipi_dati_tecnici_attivita(id_tipo_dato_tecnico_attivita,id_tipo_attivita,tipo_ui,descrizione)
values (seq_tipi_dati_tecnici_att.nextval,-1,'TEXT','TEST_FLATDTA_FLOAT')
;
insert into tipi_dati_tecnici_attivita(id_tipo_dato_tecnico_attivita,id_tipo_attivita,tipo_ui,descrizione)
values (seq_tipi_dati_tecnici_att.nextval,-1,'TEXT','TEST_FLATDTA_NC')
;


insert into tdta_dtaflat(id_tipo_attivita,col_seq,id_tipo_dato_tecnico_attivita,nome_colonna,data_type,data_length,char_used)
select &id_ta,0,id_tipo_dato_tecnico_attivita,'C_VARCHAR','VARCHAR2',30,'C'
from 
    tipi_dati_tecnici_attivita 
where 
    descrizione='TEST_FLATDTA_VARCHAR'
;


insert into tdta_dtaflat(id_tipo_attivita,col_seq,id_tipo_dato_tecnico_attivita,nome_colonna,data_type,data_length,char_used)
select &id_ta,1,id_tipo_dato_tecnico_attivita,'C_CHAR','CHAR',5,'C'
from 
    tipi_dati_tecnici_attivita 
where 
    descrizione='TEST_FLATDTA_CHAR'
;

insert into tdta_dtaflat(id_tipo_attivita,col_seq,id_tipo_dato_tecnico_attivita,nome_colonna,data_type,date_format)
select &id_ta,2,id_tipo_dato_tecnico_attivita,'C_DATE','DATE','YYYYMMDD'
from 
    tipi_dati_tecnici_attivita 
where 
    descrizione='TEST_FLATDTA_DATE'
;

insert into tdta_dtaflat(id_tipo_attivita,col_seq,id_tipo_dato_tecnico_attivita,nome_colonna,data_type,data_precision,data_scale)
select &id_ta,3,id_tipo_dato_tecnico_attivita,'C_NUMBER','NUMBER',5,2
from 
    tipi_dati_tecnici_attivita 
where 
    descrizione='TEST_FLATDTA_NUMBER'
;


insert into tdta_dtaflat(id_tipo_attivita,col_seq,id_tipo_dato_tecnico_attivita,nome_colonna,data_type)
select &id_ta,4,id_tipo_dato_tecnico_attivita,'C_FLOAT','FLOAT'
from 
    tipi_dati_tecnici_attivita 
where 
    descrizione='TEST_FLATDTA_FLOAT'
;


insert into tdta_dtaflat(id_tipo_attivita,col_seq,id_tipo_dato_tecnico_attivita,disabilitato,nome_colonna)
select &id_ta,5,id_tipo_dato_tecnico_attivita,'Y','C_NC'
from 
    tipi_dati_tecnici_attivita 
where 
    descrizione='TEST_FLATDTA_NC'
 ;


select d  from  v_dmlgen_dtaflat
where id_tipo_attivita=&id_ta
order by id_tipo_attivita,line_seq
;


declare     -- crea la tabella associata
    sq varchar(4000) := '';
    c pls_integer;
    cur flat_dta.generic_cursor;
    d  v_dmlgen_dtaflat.d%type;
begin
    select count(*) into c from user_tables where table_name=upper('&mytable');
    if c > 0 then
        execute immediate 'drop table &mytable cascade constraints purge';
    end if;
    open cur for 
        select d  from  v_dmlgen_dtaflat
        where id_tipo_attivita=&id_ta
        order by id_tipo_attivita,line_seq
    ;
    loop
        fetch cur into d;
        exit when cur%notfound;
        sq := sq || ' ' || d;
    end loop;
    close cur;
    dbms_output.put_line(sq);
    execute immediate sq;
end;
/


/* inserisce una attivita e dati tecnici per provare la sincronizzazione */


insert into sistemi (id_sistema,id_tipo_sistema,descrizione,id_classe_sistema,id_categoria_sistema,Data_Inizio_Osservazione) 
        values(seq_sistemi.nextval,(select min(id_tipo_sistema) from Tipi_Sistema),'test',(select min(id_classe_sistema) from Classi_Sistema),1,sysdate)
    ;

insert into attivita (id_attivita,id_sistema,id_tipo_attivita,descrizione,stato_corrente,data_ult_varstat,id_op_ult_varstat) 
        values(seq_attivita.nextval,seq_sistemi.currval,&id_ta,'test',(select id_stato from stati where Nome='APERTA'),sysdate,(select min(id_operatore) from operatori))
    ;



def id_a="(select max(id_attivita) from attivita where  id_tipo_attivita=&id_ta)"


insert into storia_attivita(id_attivita,data_esecuzione,id_action,descr_azione,id_operatore,Id_Stato_Risultante) 
    values (&id_a,sysdate,(select min(id_action) from Action),'test',(select min(id_operatore) from operatori),(select min(id_stato) from stati))
;


def evdate="(select max(data_esecuzione) from storia_attivita where id_attivita=&id_a)"

insert into  dati_tecnici_attivita (id_tipo_dato_tecnico_attivita,id_attivita,data_esecuzione,descrizione) 
    values ((select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione='TEST_FLATDTA_VARCHAR'),&id_a,&evdate,'test_varchar');


insert into  dati_tecnici_attivita (id_tipo_dato_tecnico_attivita,id_attivita,data_esecuzione,descrizione) 
    values ((select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione='TEST_FLATDTA_CHAR'),&id_a,&evdate,'tchar');


insert into  dati_tecnici_attivita (id_tipo_dato_tecnico_attivita,id_attivita,data_esecuzione,descrizione) 
    values ((select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione='TEST_FLATDTA_DATE'),&id_a,&evdate,'20110901');


insert into  dati_tecnici_attivita (id_tipo_dato_tecnico_attivita,id_attivita,data_esecuzione,descrizione) 
    values ((select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione='TEST_FLATDTA_NUMBER'),&id_a,&evdate,'55.66');

insert into  dati_tecnici_attivita (id_tipo_dato_tecnico_attivita,id_attivita,data_esecuzione,descrizione) 
    values ((select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione='TEST_FLATDTA_FLOAT'),&id_a,&evdate,'55.6666666');

insert into  dati_tecnici_attivita (id_tipo_dato_tecnico_attivita,id_attivita,data_esecuzione,descrizione) 
    values ((select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione='TEST_FLATDTA_NC'),&id_a,&evdate,'questo testo non viene memorizzato nella tabella associata');


select * from &mytable where id_attivita=&id_a;




declare
    id_ta tipi_attivita.id_tipo_attivita%type; 
begin
    select id_tipo_attivita into id_ta from  tipi_attivita where nome_tipo_attivita='&tipo_attivita';
    flat_dta.sync(id_ta);
end;
/


select 
    id_tipo_dato_tecnico_attivita    id_tdta
    ,id_attivita                     id_a
    ,cast(to_char(data_esecuzione,'YYYYMMDD') as varchar(15)) data_esecuzione
    ,errcode
    ,cast(substr(flat_dta.get_exception_message(errcode),1,50) as varchar(50))   errmsg
from 
    table(flat_dta.get_last_sync_errs)
;


select * from table(flat_dta.get_last_sync_summaries); 

select * from &mytable where id_attivita=&id_a;


update dati_tecnici_attivita
set descrizione='data non valida' 
where 
    id_tipo_dato_tecnico_attivita=(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione='TEST_FLATDTA_DATE')
    and id_attivita=&id_a
    and data_esecuzione=&evdate
;


declare
    id_ta tipi_attivita.id_tipo_attivita%type; 
begin
    select id_tipo_attivita into id_ta from  tipi_attivita where nome_tipo_attivita='&tipo_attivita';
    flat_dta.sync(id_ta);
end;
/


select * from table(flat_dta.get_last_sync_summaries); 

select 
    id_tipo_dato_tecnico_attivita    id_tdta
    ,id_attivita                     id_a
    ,cast(to_char(data_esecuzione,'YYYYMMDD') as varchar(15)) data_esecuzione
    ,errcode
    ,cast(substr(flat_dta.get_exception_message(errcode),1,50) as varchar(50))   errmsg
from 
    table(flat_dta.get_last_sync_errs)
;


commit;
quit




