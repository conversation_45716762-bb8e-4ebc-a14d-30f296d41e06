
set line 1000
set autocommit off
set echo on
set SERVEROUT on  size 1000000


/*
    esempio di configurazione 
*/


whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;





declare    -- abilita i trigger 
    c flat_dta.generic_cursor;
    n user_triggers.trigger_name%type;
begin
    open c for
        select trigger_name
        from user_triggers 
        where
            status='DISABLED'
            and table_name in ('ATTIVITA','DATI_TECNICI_ATTIVITA')
    ;
    loop
        fetch c into n;
        exit when c%notfound;
        execute immediate 'alter trigger ' || n || ' enable';
    end loop;
    close c;
end;
/


def tipo_attivita='TESTFLATDTA'
def id_ta="(select id_tipo_attivita from  tipi_attivita where nome_tipo_attivita='&tipo_attivita')"
def mytable='TEST_FLATDATI'
def id_a="(select max(id_attivita) from attivita where  id_tipo_attivita=&id_ta)"
def evdate="(select max(data_esecuzione) from storia_attivita where id_attivita=&id_a)"



select to_char(c_date,'YYYYMMDD') c_date from &mytable where id_attivita=&id_a;


update dati_tecnici_attivita
set descrizione='20110930' 
where 
    id_tipo_dato_tecnico_attivita=(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione='TEST_FLATDTA_DATE')
    and id_attivita=&id_a
    and data_esecuzione=&evdate
;


select to_char(c_date,'YYYYMMDD') c_date from &mytable where id_attivita=&id_a;


update dati_tecnici_attivita
set descrizione='20110930' 
where 
    id_tipo_dato_tecnico_attivita=(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione='TEST_FLATDTA_DATE')
    and id_attivita=&id_a
    and data_esecuzione=&evdate
;


begin    -- agg con data errata
    update dati_tecnici_attivita
    set descrizione='data errata' 
    where 
        id_tipo_dato_tecnico_attivita=(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione='TEST_FLATDTA_DATE')
        and id_attivita=&id_a
        and data_esecuzione=&evdate
    ;
exception
    when others then
        if sqlcode = flat_dta.EXN_BAD_VALUE then
            dbms_output.put_line('dati errati');
        else
            raise;
        end if;
end;
/


commit;
quit
