
set line 1000
set autocommit off
set echo on
set SERVEROUT on  size 1000000


/*
    esempio di configurazione 
*/


whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;





def tipo_attivita='TESTFLATDTA'
def nome_dt='TEST_FLATDTA_NC'

def id_ta="(select id_tipo_attivita from  tipi_attivita where nome_tipo_attivita='&tipo_attivita')"
def id_nc="(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where  descrizione='&nome_dt')"

declare
     assert_error   constant pls_integer := -20000;
     c pls_integer;
     r1  tdta_dtaflat%rowtype;
begin
    update tipi_dati_tecnici_attivita 
        set morto='Y'
    where 
        descrizione='&nome_dt'
    ;
    if sql%rowcount <> 1 then
        raise_application_error(assert_error,' fallito step am1');
    end if;


    
    select count(*) into c from tdta_dtaflat
    where id_tipo_attivita=&id_ta  
    and id_tipo_dato_tecnico_attivita=&id_nc
    ;

    if sql%rowcount <> 1 then
        raise_application_error(assert_error,' fallito step am2');
    end if;

    select * into r1 from tdta_dtaflat
    where id_tipo_attivita=&id_ta  
    and id_tipo_dato_tecnico_attivita=&id_nc
    ;

    delete tdta_dtaflat 
    where id_tipo_attivita=&id_ta  
    and id_tipo_dato_tecnico_attivita=&id_nc
    ;

    if sql%rowcount <> 1 then
        raise_application_error(assert_error,' fallito step am3');
    end if;

    select count(*) into c from v_tdta_dtaflat
    where id_tipo_attivita=&id_ta  
        and  id_tipo_dato_tecnico_attivita=&id_nc
    ;

    if sql%rowcount <> 1 then
        raise_application_error(assert_error,' fallito step am4');
    end if;


    select count(*) into c from v_tdta_dtaflat
    where id_tipo_attivita=&id_ta  
        and  id_tipo_dato_tecnico_attivita=&id_nc
        and abilitata='N'
    ;

    if sql%rowcount <> 1 then
        raise_application_error(assert_error,' fallito step am5');
    end if;


    update tipi_dati_tecnici_attivita 
        set morto=null
    where 
        descrizione='&nome_dt'
    ;
    if sql%rowcount <> 1 then
        raise_application_error(assert_error,' fallito step am6');
    end if;

 
    select count(*) into c from v_tdta_dtaflat
    where id_tipo_attivita=&id_ta  
        and  id_tipo_dato_tecnico_attivita=&id_nc
        and abilitata='Y'
    ;

    if sql%rowcount <> 1 then
        raise_application_error(assert_error,' fallito step am7');
    end if;


    insert into tdta_dtaflat values r1;
    if sql%rowcount <> 1 then
        raise_application_error(assert_error,' fallito step am8');
    end if;


    select count(*) into c from v_tdta_dtaflat
    where id_tipo_attivita=&id_ta  
        and  id_tipo_dato_tecnico_attivita=&id_nc
        and abilitata='N'
    ;

    if sql%rowcount <> 1 then
        raise_application_error(assert_error,' fallito step am9');
    end if;


end;
/
    

commit;
quit
