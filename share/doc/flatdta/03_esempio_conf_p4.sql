
set line 1000
set autocommit off
set echo on
set SERVEROUT on  size 1000000


/*
    esempio di configurazione 
*/


whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;


def tipo_attivita='TESTFLATDTA'
def nome_dt='TEST_FLATDTA_ADDVARCHAR'
def nome_col='C_VARCHAR_ADD'

def id_ta="(select id_tipo_attivita from  tipi_attivita where nome_tipo_attivita='&tipo_attivita')"
def id_nc="(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where  descrizione='&nome_dt')"

def t_ass="(select tabella_associata from v_ta_dtaflat where nome_tipo_attivita='&tipo_attivita')"

declare    -- disabilita i trigger se presenti
    c flat_dta.generic_cursor;
    n user_triggers.trigger_name%type;
begin
    open c for
        select trigger_name
        from user_triggers 
        where
            status='ENABLED'
            and table_name in ('ATTIVITA','DATI_TECNICI_ATTIVITA')
    ;
    loop
        fetch c into n;
        exit when c%notfound;
        execute immediate 'alter trigger ' || n || ' disable';
    end loop;
    close c;
end;
/

declare   -- aggiunge la nuova colonna nei dati di configurazione
     assert_error   constant pls_integer := -20000;
     c pls_integer;
     t_ass user_objects.object_name%type;
begin

    select tabella_associata into t_ass from v_ta_dtaflat where nome_tipo_attivita='&tipo_attivita';

    insert into tipi_dati_tecnici_attivita(id_tipo_dato_tecnico_attivita,id_tipo_attivita,tipo_ui,descrizione)
    select seq_tipi_dati_tecnici_att.nextval,-1,'TEXT','&nome_dt' from dual
    where '&nome_dt' not in (select descrizione from tipi_dati_tecnici_attivita)
    ;

    dbms_output.put_line('inserite 1 ' || sql%rowcount);

    insert into tdta_dtaflat(id_tipo_attivita,col_seq,id_tipo_dato_tecnico_attivita,nome_colonna,data_type,data_length,char_used)
    select &id_ta,10,id_tipo_dato_tecnico_attivita,'&nome_col','VARCHAR2',40,'C'
    from 
        tipi_dati_tecnici_attivita 
    where 
        descrizione='&nome_dt'
        and id_tipo_dato_tecnico_attivita not in (select  id_tipo_dato_tecnico_attivita from tdta_dtaflat where id_tipo_attivita=&id_ta)
    ;
     --   and '&nome_col' not in (select nome_colonna from tdta_dtaflat where id_tipo_attivita=&id_ta)
    

    dbms_output.put_line('inserite 2 ' || sql%rowcount);
end;
/

declare   -- aggiunge la nuova colonna nella tabella flat
     alter_col  varchar(200); 
     t_ass user_objects.object_name%type;
     assert_error   constant pls_integer := -20000;
     c pls_integer;
    
begin
    select d into alter_col from v_dmlgen_dtaflat where id_tipo_attivita=&id_ta and id_tipo_dato_tecnico_attivita=&id_nc;
    select tabella_associata into t_ass from v_ta_dtaflat where nome_tipo_attivita='&tipo_attivita';

    select count(*) into c from user_tab_columns 
    where table_name=t_ass
    and  column_name='&nome_col'
    ;
    if c = 0 then
        declare
            sq varchar(255) := 'alter table ' || t_ass ||  ' add (' || substr(alter_col,3) || ')';
        begin
            flat_dta.put_lines(sq);
            execute immediate sq;
        end;
    end if;    
end;
/


def id_a="(select max(id_attivita) from attivita where  id_tipo_attivita=&id_ta)"
def evdate="(select max(data_esecuzione) from storia_attivita where id_attivita=&id_a)"
def new_value='nuovo_valore_in_nuova_colonna'


declare
    a_conf v_ta_dtaflat%rowtype;
    id_tdta tipi_dati_tecnici_attivita.id_tipo_dato_tecnico_attivita%type;
    id_a    attivita.id_attivita%type;
    evdate  storia_attivita.data_esecuzione%type;
    assert_error   constant pls_integer := -20000;
begin
    select * into a_conf from v_ta_dtaflat where nome_tipo_attivita='&tipo_attivita';
    select id_tipo_dato_tecnico_attivita into id_tdta from tipi_dati_tecnici_attivita where descrizione='&nome_dt';


    select id_attivita,max(data_esecuzione) into id_a,evdate 
    from 
        storia_attivita 
    where 
        id_attivita in (select max(id_attivita) from attivita  where  id_tipo_attivita=a_conf.id_tipo_attivita)
    group by
        id_attivita
    ;


    -- pulisce

    declare
        sq varchar(255) := 'insert into  ' ||  a_conf.tabella_associata  || '(id_attivita) ' ||
            ' select :1 from dual where :2  not in (select  id_attivita from '  ||  a_conf.tabella_associata || ')';  
    begin
        flat_dta.put_lines(sq);
        execute immediate sq using id_a,id_a;
        flat_dta.put_lines('inserite ' || sql%rowcount || ' righe in ' || a_conf.tabella_associata);
    end;

    declare
        sq varchar(255) := 'update ' ||  a_conf.tabella_associata || ' set &nome_col=:1 where id_attivita=:2'; 
    begin
        flat_dta.put_lines(sq);
        execute immediate sq using upper('&new_value'),id_a;
        if sql%rowcount <> 1 then
            raise_application_error(assert_error,' fallito step upd1');
        end if;
    end;

    delete   dati_tecnici_attivita 
    where  (id_tipo_dato_tecnico_attivita,id_attivita,data_esecuzione) in (
                select id_tdta,id_a,evdate from dual
    );
    flat_dta.put_lines('cancellate ' || sql%rowcount || ' righe in dati_tecnici_attivita');

        -- inserisce un valore in dati_tecnici_attivita relativo alla nuova colonna
    insert into  dati_tecnici_attivita (id_tipo_dato_tecnico_attivita,id_attivita,data_esecuzione,descrizione)
    select id_tdta,id_a,evdate,'&new_value' from dual;

     
    declare  -- avvia la sincronizzazione e ne controlla l'esito
        v varchar(4000);
        sq varchar(255) := 'select &nome_col from ' || a_conf.tabella_associata ||  ' where id_attivita=:1';
    begin
        flat_dta.sync(a_conf.id_tipo_attivita);
        flat_dta.put_lines(sq);
        execute immediate sq into v using id_a;
        if v <> '&new_value' then
            raise_application_error(assert_error,' fallito step sync1');
        end if;
    end;

    flat_dta.put_lines(' test passato per attivita (' || id_a || ') in tabella ''' || a_conf.tabella_associata || '''');  
end;
/


commit;

declare    -- abilita i trigger se presenti
    c flat_dta.generic_cursor;
    n user_triggers.trigger_name%type;
begin
    open c for
        select trigger_name
        from user_triggers 
        where
            status='DISABLED'
            and table_name in ('ATTIVITA','DATI_TECNICI_ATTIVITA')
    ;
    loop
        fetch c into n;
        exit when c%notfound;
        execute immediate 'alter trigger ' || n || ' enable';
    end loop;
    close c;
end;
/

rollback;

quit

