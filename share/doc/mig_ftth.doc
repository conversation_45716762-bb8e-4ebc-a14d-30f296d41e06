- mig ftth
		- attesa file Lecce aggiornato
		- manca
			- fare in modo di gestire networkId e ODSId con padding a sinistra
			- gestire casistiche ROE:
				- nativi "a corpo"
					- fare check di consistenza su ODSId
				- nativi "a misura"
					- creare off-line (no invio a CS01) ex-novo external_sink
					- portarlo in READY
					- agganciare idODS e idCS01
					- recuperare ultima operazione
				- migrati da "a misura" ad "a corpo"
					- come i nativi "a misura"
				- migrati da "a corpo" ad "a misura"
					- da capire se ce ne sono
					- eventualmente da gestire come i nativi "a misura"
			- generazione lavori sui PTE (i permessi sono pochi e NON saranno migrati)
				- parcheggiare lavori on-going
			- generazione lavori sull'Armadio
			- adeguare mapping TipoPTE / Potenzialità
