# Introduction

Here you can find all the SIRTI API utils.

## Dates

Please note that all dates are ISO formatted strings.

## Errors

In case of error a HTTP error status is returned. The body will contain an object defined as follow:
* <code>UUID</code>: unique identifier of the error occurred. You must include it if you contact administrators in case of problems.
* <code>message</code>: a message describing the error.
* <code>internalMessage</code>: available only if you run the webservices in *development* mode, it contains further information about the error.

Example:

```
HTTP/1.1 400 Bad Request
Content-Type: application/json

{
  "UUID": "39021fefd8ff0e0510735a563abf3701",
  "message": "Uncorrect parameters",
  "internalMessage": "Error in creating system: SYSTEM_TYPE_NAME 'BID_MANAGEMENT' undefined!"
}
```

**Note**: if the status code <code>401 Authorization Required</code> is returned, the body is empty!

## Prefix

By default routes prefix is <code>/rest</code>. You can change it defining environment variable <code>WS_ROUTES_PREFIX</code>.
