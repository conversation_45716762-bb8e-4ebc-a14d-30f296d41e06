---
tipo_attivita: EXTERNAL_SYNC
tipi_sistema:
  - NETWORK
  - ROE
  - MANUTENZIONE_CORRETTIVA
  - EL01
  - EL02
transizioni:
  - stato_iniziale: START
    action: APERTURA
    stato_finale: APERTA
    gruppi:
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: APERTA
    action: READY
    stato_finale: READY
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalWorkTypeId
        etichetta: Tipo Avviso / Tipo Intervento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: READY
    action: CHIUSURA
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: raTargetId
        etichetta: Target Id R.A. richiedente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: READY
    action: RESEND
    stato_finale: WAITING_FOR_ACK
    gruppi:
      - ADMIN
      - ASSURANCE
      - ROOT
    tdta:
      - posizione: 0
        descrizione: syncId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: currentStatus
        etichetta: ''
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'EQUIVALENTE_DE,EQUIVALENTE_IL,EQUIVALENTE_SL,EQUIVALENTE_FL,EQUIVALENTE_AN'
      - posizione: 2
        descrizione: syncStatus
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: syncReason
        etichetta: ''
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: READY
    action: SEND
    stato_finale: WAITING_FOR_ACK
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: raTargetId
        etichetta: Target Id R.A. richiedente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: syncId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: currentStatus
        etichetta: ''
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'EQUIVALENTE_DE,EQUIVALENTE_IL,EQUIVALENTE_SL,EQUIVALENTE_FL,EQUIVALENTE_AN'
      - posizione: 3
        descrizione: event
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: startingStatus
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: WAITING_FOR_ACK
    action: ACK_KO
    stato_finale: SWITCH_ASSURANCE
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: syncStatus
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: syncReason
        etichetta: ''
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: externalFetchDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
  - stato_iniziale: WAITING_FOR_ACK
    action: ACK_OK
    stato_finale: READY
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: externalFetchDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
      - posizione: 2
        descrizione: syncStatus
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: syncReason
        etichetta: ''
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: SWITCH_ASSURANCE
    action: KO_GENERIC
    stato_finale: ASSURANCE
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: SWITCH_ASSURANCE
    action: KO_YY_CLAS10_NOT_VALID
    stato_finale: ASSURANCE_YY_CLAS10_NOT_VALID
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: SWITCH_ASSURANCE
    action: KO_YY_NW_NOT_FOUND
    stato_finale: ASSURANCE_YY_NW_NOT_FOUND
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: ASSURANCE
    action: IGNORE
    stato_finale: READY
    gruppi:
      - ADMIN
      - ASSURANCE
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: currentStatus
        etichetta: ''
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'EQUIVALENTE_DE,EQUIVALENTE_IL,EQUIVALENTE_SL,EQUIVALENTE_FL,EQUIVALENTE_AN'
      - posizione: 2
        descrizione: syncStatus
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: syncReason
        etichetta: ''
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ASSURANCE
    action: RESEND
    stato_finale: WAITING_FOR_ACK
    gruppi:
      - ADMIN
      - ASSURANCE
      - ROOT
    tdta:
      - posizione: 0
        descrizione: syncId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: currentStatus
        etichetta: ''
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'EQUIVALENTE_DE,EQUIVALENTE_IL,EQUIVALENTE_SL,EQUIVALENTE_FL,EQUIVALENTE_AN'
      - posizione: 2
        descrizione: syncStatus
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: syncReason
        etichetta: ''
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ASSURANCE
    action: SANITY
    stato_finale: WAITING_FOR_ACK
    gruppi:
      - ADMIN
      - ASSURANCE
      - ROOT
    tdta:
      - posizione: 0
        descrizione: syncId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: currentStatus
        etichetta: ''
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'EQUIVALENTE_DE,EQUIVALENTE_IL,EQUIVALENTE_SL,EQUIVALENTE_FL,EQUIVALENTE_AN'
      - posizione: 2
        descrizione: syncStatus
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: syncReason
        etichetta: ''
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ASSURANCE_YY_CLAS10_NOT_VALID
    action: IGNORE
    stato_finale: READY
    gruppi:
      - ADMIN
      - ASSURANCE
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: currentStatus
        etichetta: ''
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'EQUIVALENTE_DE,EQUIVALENTE_IL,EQUIVALENTE_SL,EQUIVALENTE_FL,EQUIVALENTE_AN'
      - posizione: 2
        descrizione: syncStatus
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: syncReason
        etichetta: ''
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ASSURANCE_YY_CLAS10_NOT_VALID
    action: RESEND
    stato_finale: WAITING_FOR_ACK
    gruppi:
      - ADMIN
      - ASSURANCE
      - ROOT
    tdta:
      - posizione: 0
        descrizione: syncId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: currentStatus
        etichetta: ''
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'EQUIVALENTE_DE,EQUIVALENTE_IL,EQUIVALENTE_SL,EQUIVALENTE_FL,EQUIVALENTE_AN'
      - posizione: 2
        descrizione: syncStatus
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: syncReason
        etichetta: ''
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ASSURANCE_YY_CLAS10_NOT_VALID
    action: SANITY
    stato_finale: WAITING_FOR_ACK
    gruppi:
      - ADMIN
      - ASSURANCE
      - ROOT
    tdta:
      - posizione: 0
        descrizione: syncId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: currentStatus
        etichetta: ''
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'EQUIVALENTE_DE,EQUIVALENTE_IL,EQUIVALENTE_SL,EQUIVALENTE_FL,EQUIVALENTE_AN'
      - posizione: 2
        descrizione: syncStatus
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: syncReason
        etichetta: ''
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ASSURANCE_YY_NW_NOT_FOUND
    action: IGNORE
    stato_finale: READY
    gruppi:
      - ADMIN
      - ASSURANCE
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: currentStatus
        etichetta: ''
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'EQUIVALENTE_DE,EQUIVALENTE_IL,EQUIVALENTE_SL,EQUIVALENTE_FL,EQUIVALENTE_AN'
      - posizione: 2
        descrizione: syncStatus
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: syncReason
        etichetta: ''
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ASSURANCE_YY_NW_NOT_FOUND
    action: RESEND
    stato_finale: WAITING_FOR_ACK
    gruppi:
      - ADMIN
      - ASSURANCE
      - ROOT
    tdta:
      - posizione: 0
        descrizione: syncId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: currentStatus
        etichetta: ''
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'EQUIVALENTE_DE,EQUIVALENTE_IL,EQUIVALENTE_SL,EQUIVALENTE_FL,EQUIVALENTE_AN'
      - posizione: 2
        descrizione: syncStatus
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: syncReason
        etichetta: ''
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ASSURANCE_YY_NW_NOT_FOUND
    action: SANITY
    stato_finale: WAITING_FOR_ACK
    gruppi:
      - ADMIN
      - ASSURANCE
      - ROOT
    tdta:
      - posizione: 0
        descrizione: syncId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: currentStatus
        etichetta: ''
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'EQUIVALENTE_DE,EQUIVALENTE_IL,EQUIVALENTE_SL,EQUIVALENTE_FL,EQUIVALENTE_AN'
      - posizione: 2
        descrizione: syncStatus
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: syncReason
        etichetta: ''
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
