---
tipo_attivita: EXTERNAL_SYNC_FS
tipi_sistema:
  - NETWORK
  - MANUTENZIONE_CORRETTIVA
  - EL02
transizioni:
  - stato_iniziale: START
    action: APERTURA
    stato_finale: APERTA
    gruppi:
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workId
        etichetta: Id lavoro o permesso da gestire
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: externalSequence
        etichetta: Progressivo external sequence
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: context
        etichetta: Contesto di esecuzione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: maxRetries
        etichetta: Massimo numero tentativi per singola richiesta FSGW
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: retriesLeft
        etichetta: Numero tentativi rimasti per singola richiesta FSGW
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: activityOrigin
        etichetta: Origine attività padre
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: fieldActivityOrigin
        etichetta: Origine attività campo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: APERTA
    action: REQUEST_FOR_SENDING
    stato_finale: WAITING_FOR_SENDING
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: retriesLeft
        etichetta: Numero tentativi rimasti per singola richiesta FSGW
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: Y
      - posizione: 1
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 2
        descrizione: workId
        etichetta: Id lavoro o permesso da gestire
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: raTargetId
        etichetta: Target Id R.A. richiedente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: WAITING_FOR_SENDING
    action: KO
    stato_finale: SEND_KO
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: response_code
        etichetta: HTTP Code
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: response_content
        etichetta: HTTP Content
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: WAITING_FOR_SENDING
    action: MAX_RETRY_REACHED
    stato_finale: ASSURANCE
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: response_code
        etichetta: HTTP Code
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: response_content
        etichetta: HTTP Content
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: WAITING_FOR_SENDING
    action: OK
    stato_finale: READY
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: response_code
        etichetta: HTTP Code
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: response_content
        etichetta: HTTP Content
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: SEND_KO
    action: RETRY
    stato_finale: WAITING_FOR_SENDING
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workId
        etichetta: Id lavoro o permesso da gestire
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: retriesLeft
        etichetta: Numero tentativi rimasti per singola richiesta FSGW
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: Y
      - posizione: 2
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: raTargetId
        etichetta: Target Id R.A. richiedente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ASSURANCE
    action: CHIUSURA
    stato_finale: ERROR
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: READY
    action: CHIUSURA
    stato_finale: DONE
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: READY
    action: REQUEST_FOR_SENDING
    stato_finale: WAITING_FOR_SENDING
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: retriesLeft
        etichetta: Numero tentativi rimasti per singola richiesta FSGW
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: Y
      - posizione: 1
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 2
        descrizione: workId
        etichetta: Id lavoro o permesso da gestire
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: raTargetId
        etichetta: Target Id R.A. richiedente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
