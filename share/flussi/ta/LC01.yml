---
tipo_attivita: LC01
tipi_sistema:
  - EL01
transizioni:
  - stato_iniziale: START
    action: APERTURA
    stato_finale: APERTA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cabinetType
        etichetta: Tipo Elemento Livello 01 FiberCop
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'CNO,ARLO'
      - posizione: 1
        descrizione: primaryFiber
        etichetta: Fibre di primaria
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 2
        descrizione: OLTPort
        etichetta: Porta OLT
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: __DTC_WIN_AT
        etichetta: 'Walk in [AT] [FASE 1]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: __DTC_DDPP_AT
        etichetta: 'Documentazione di Progetto Planimetria [AT] [FASE 1]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: __DTC_DDPS_AT
        etichetta: 'Documentazione di Progetto Sinottico [AT] [FASE 1]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: __DTC_DDC_AT
        etichetta: 'Documentazione di cantiere Planimetria (As built) [AT] [FASE 2]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: __DTC_DDCS_AT
        etichetta: 'Documentazione di cantiere Sinottico (As built) [AT] [FASE 2]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: __DTC_DDCP_AT
        etichetta: 'Documentazione di cantiere Primaria (As built) [AT] [PRIMARIA]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 9
        descrizione: __DTC_FIR
        etichetta: 'FIR [AT] [FASE 2]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 10
        descrizione: __DTC_CRE
        etichetta: 'CRE [AT] [FASE 2]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 11
        descrizione: __DTC_PLN_SERVICE
        etichetta: 'Ngneer Aggiornamento Cartografico [Service] [FASE 2]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 12
        descrizione: __DTC_SIN_SERVICE
        etichetta: 'Sinottico Aggiornato [Service] [FASE 2]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 13
        descrizione: __DTC_XFB_SERVICE
        etichetta: 'Export fibre [Service] [FASE 1]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 14
        descrizione: __DTC_ARF_SERVICE
        etichetta: 'Area influenza (distr.Rame e PTE) [Service] [FASE 1]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 15
        descrizione: __DTC_RFO_SERVICE
        etichetta: 'Export RFOS [Service] [FASE 1]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 16
        descrizione: __DTC_IFO_SERVICE
        etichetta: 'Export IFOS [Service] [FASE 1]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 17
        descrizione: __DTC_CFO_SERVICE
        etichetta: 'Export CFOS [Service] [FASE 1]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: __DTC_DYN_SERVICE
        etichetta: 'Dynamic (Sinottico) [Service] [FASE 2]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 19
        descrizione: __DTC_XSP_SERVICE
        etichetta: 'Export splitter primari e secondari [Service] [FASE 1]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 20
        descrizione: __DTC_XTL_SERVICE
        etichetta: 'Export tratte logiche [Service] [FASE 2]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 21
        descrizione: __DTC_OPI_SERVICE
        etichetta: 'Report OPI [Service] [FASE 1]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 22
        descrizione: __DTC_CDP_SERVICE
        etichetta: 'Stamp codino di primaria [Service] [PRIMARIA]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 23
        descrizione: __DTC_LAA_SERVICE
        etichetta: 'Stamp livello area di armadio [Service] [FASE 2]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 24
        descrizione: __DTC_CWOP_SERVICE
        etichetta: 'Videata chiusura WO Primaria [Service] [PRIMARIA]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 25
        descrizione: __DTC_CWO_SERVICE
        etichetta: 'Videata chiusura WO Secondaria [Service] [FASE 2]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 26
        descrizione: __DTC_SC_SERVICE
        etichetta: 'Schemi canalizz. (se utilizzata) [Service] [FASE 2]'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 27
        descrizione: __DTC_VAR
        etichetta: DOC. VARIE
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 28
        descrizione: PTECurrentValue
        etichetta: Valore corrente PTE
        tipo_ui: CUREUR
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: NUOVA_DOCUMENTAZIONE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
  - stato_iniziale: APERTA
    action: AVVIO
    stato_finale: IN_CORSO
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: IN_CORSO
    action: AGGIORNAMENTO_DATI
    stato_finale: IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: customerWorkOrderId
        etichetta: Customer Work Order
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: customerWorkOrderStatus
        etichetta: Customer Work Order Stato
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Aperto,In Carico Impresa,Fine Aggiornamento Parziale,Fine Aggiornamento,Chiuso,Lock,Ticket'
      - posizione: 2
        descrizione: customerWorkOrderNote
        etichetta: Customer Work Order Note
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkOrderPrimaryNtw
        etichetta: Customer Work Order NTW di Primaria
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 4
        descrizione: customerWorkOrderPNRRNtw
        etichetta: Customer Work Order NTW PNRR
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 5
        descrizione: primaryFiber
        etichetta: Fibre di primaria
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 6
        descrizione: OLTPort
        etichetta: Porta OLT
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
  - stato_iniziale: IN_CORSO
    action: AGGIORNAMENTO_VALORE_MEDIO_PTE
    stato_finale: IN_CORSO
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: PTECurrentValue
        etichetta: Valore corrente PTE
        tipo_ui: CUREUR
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: cabinetAnalysisStatus
        etichetta: Lavorabilità Armadio
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: 'Da Analizzare,Lavorabile,Non Lavorabile'
  - stato_iniziale: IN_CORSO
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: IN_CORSO
    action: CARICAMENTO_DOCUMENTAZIONE
    stato_finale: IN_CORSO
    gruppi:
      - ADMIN
      - ROOT
      - SERVICE
  - stato_iniziale: IN_CORSO
    action: CHIUSURA
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: IN_CORSO
    action: DATI_PROGETTAZIONE
    stato_finale: IN_CORSO
    gruppi:
      - ADMIN
      - PROGETTISTA
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dtProject001
        etichetta: Valore medio PTE da progetto
        tipo_ui: CUREUR
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: cabinetAnalysisStatus
        etichetta: Lavorabilità Armadio
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: 'Da Analizzare,Lavorabile,Non Lavorabile'
