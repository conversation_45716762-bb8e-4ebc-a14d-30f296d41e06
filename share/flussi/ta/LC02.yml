---
tipo_attivita: LC02
tipi_sistema:
  - EL02
transizioni:
  - stato_iniziale: START
    action: APERTURA
    stato_finale: APERTA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: id
        etichetta: Id
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"id\"]"
          }
      - posizione: 1
        descrizione: elementType
        etichetta: Tipo Elemento Livello 02 FiberCop
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'PTE,ROE'
      - posizione: 2
        descrizione: cabinetId
        etichetta: Id Armadio
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: copperDistributor
        etichetta: Distributore rame
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: copperCabinet
        etichetta: Armadio rame
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: RoId
        etichetta: Identificativo RO
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: AOLId
        etichetta: Identificativo AOL
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: customerAssistant
        etichetta: Ass. Cliente
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 8
        descrizione: externalMacroActivity
        etichetta: Descrizione Macroattivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: externalMacroActivityId
        etichetta: Macroattivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 10
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 11
        descrizione: externalActivityId
        etichetta: Attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 12
        descrizione: externalAccountingType
        etichetta: Tipo contabilità
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 13
        descrizione: note
        etichetta: Note
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 14
        descrizione: PTEPotential
        etichetta: Potenzialita
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'PTE Large (48),PTE Small (24),PTE 12,ROE 16,ROE 32,ROE 48'
      - posizione: 15
        descrizione: PTEPosition
        etichetta: Posizione PTE
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Terminale,Passante'
      - posizione: 16
        descrizione: PTEType
        etichetta: Tipo PTE
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Optical Core,Completo,Da pozzetto'
      - posizione: 17
        descrizione: requestId
        etichetta: Id attivita caricamento
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 18
        descrizione: requestorName
        etichetta: Utente richiedente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 19
        descrizione: seat
        etichetta: Sede
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 20
        descrizione: specialProject
        etichetta: Progetto speciale
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 21
        descrizione: location
        etichetta: Ubicazione
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'INTERNO INGRESSO,INTERNO CANTINE,INTERNO GARAGE,INTERNO CORTILE,MURO ESTERNO,MURO INTERNO,ANDRONE,SOTTOSCALA,CENTRALINO,PALO,SEMINTERRATO,MARCIAPIEDE,COLONNINA O COLONNA,POZZETTO'
      - posizione: 22
        descrizione: CLLI
        etichetta: CLLI
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 23
        descrizione: UI
        etichetta: UI
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 24
        descrizione: workOrderId
        etichetta: Ordine cliente
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 25
        descrizione: WBELevel3
        etichetta: WBE 3 Livello
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 26
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 27
        descrizione: cluster
        etichetta: Lotto
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'CALABRIA,CB2,EMILIA OV,EMILIA ROM,FVG,LOM N.O.,MILANO,PUGLIA,ROMA,SARD SUD,SICILIA E,SICILIA O,TOSCANA OV,VENETO,VICENZA'
      - posizione: 28
        descrizione: clusterJobReport
        etichetta: Cluster
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 29
        descrizione: penetrationIndex
        etichetta: Fascia rendicontazione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 30
        descrizione: address
        etichetta: Indirizzo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 31
        descrizione: city
        etichetta: Città
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 32
        descrizione: province
        etichetta: Provincia
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 33
        descrizione: zipCode
        etichetta: CAP
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 34
        descrizione: region
        etichetta: Regione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 35
        descrizione: country
        etichetta: Stato
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 36
        descrizione: ring
        etichetta: Anello
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 37
        descrizione: FORing
        etichetta: FO anello
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 38
        descrizione: primarySplitter
        etichetta: Splitter primario
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 39
        descrizione: secondarySplitterCount
        etichetta: N. Splitter Second.
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 40
        descrizione: migFibercop
        etichetta: migrato Fibercop
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 41
        descrizione: ROEActivityId
        etichetta: Id Attivita ROE
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 42
        descrizione: flagSkipAT
        etichetta: Segnale per evitare l'attesa AT
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 43
        descrizione: teamAssistant
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 44
        descrizione: planningFirm
        etichetta: Identificativo impresa progettazione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 45
        descrizione: planningFirmDesc
        etichetta: Nome impresa progettazione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 46
        descrizione: PTEScope
        etichetta: Gestione PTE
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 47
        descrizione: workPerformance
        etichetta: Prestazione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 48
        descrizione: subcontractItem
        etichetta: Identificativo Item
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNA_AT
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - NUM
      - PM
      - ROOT
    tdta:
      - posizione: 0
        descrizione: teamAssistant
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: teamAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNA_CENTRO_LAVORO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workingGroupCodeChoice
        etichetta: Scelta Centro Lavoro
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNA_FASCIA_RENDICONTAZIONE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: penetrationIndex
        etichetta: Fascia rendicontazione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: subcontractItem
        etichetta: Identificativo Item
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_DATI
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 1
        descrizione: workingGroupCodeChoice
        etichetta: Scelta Centro Lavoro
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: id
        etichetta: Id
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"id\"]"
          }
      - posizione: 3
        descrizione: cluster
        etichetta: Lotto
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'CALABRIA,CB2,EMILIA OV,EMILIA ROM,FVG,LOM N.O.,MILANO,PUGLIA,ROMA,SARD SUD,SICILIA E,SICILIA O,TOSCANA OV,VENETO,VICENZA'
      - posizione: 4
        descrizione: clusterJobReport
        etichetta: Cluster
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: penetrationIndex
        etichetta: Fascia rendicontazione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 6
        descrizione: subcontractItem
        etichetta: Identificativo Item
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: address
        etichetta: Indirizzo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 8
        descrizione: city
        etichetta: Città
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 9
        descrizione: province
        etichetta: Provincia
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 10
        descrizione: zipCode
        etichetta: CAP
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 11
        descrizione: region
        etichetta: Regione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 12
        descrizione: country
        etichetta: Stato
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 13
        descrizione: copperDistributor
        etichetta: Distributore rame
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 14
        descrizione: copperCabinet
        etichetta: Armadio rame
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 15
        descrizione: RoId
        etichetta: Identificativo RO
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 16
        descrizione: AOLId
        etichetta: Identificativo AOL
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 17
        descrizione: PTEPosition
        etichetta: Posizione PTE
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Terminale,Passante'
      - posizione: 18
        descrizione: PTEType
        etichetta: Tipo PTE
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Optical Core,Completo,Da pozzetto'
      - posizione: 19
        descrizione: requestId
        etichetta: Id attivita caricamento
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 20
        descrizione: requestorName
        etichetta: Utente richiedente
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 21
        descrizione: location
        etichetta: Ubicazione
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'INTERNO INGRESSO,INTERNO CANTINE,INTERNO GARAGE,INTERNO CORTILE,MURO ESTERNO,MURO INTERNO,ANDRONE,SOTTOSCALA,CENTRALINO,PALO,SEMINTERRATO,MARCIAPIEDE,COLONNINA O COLONNA,POZZETTO'
      - posizione: 22
        descrizione: specialProject
        etichetta: Progetto speciale
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 23
        descrizione: CLLI
        etichetta: CLLI
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 24
        descrizione: UI
        etichetta: UI
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 25
        descrizione: seat
        etichetta: Sede
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 26
        descrizione: customerAssistant
        etichetta: Ass. Cliente
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 27
        descrizione: note
        etichetta: Note
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 28
        descrizione: PTEPotential
        etichetta: Potenzialita
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'PTE Large (48),PTE Small (24),PTE 12,ROE 16,ROE 32,ROE 48'
      - posizione: 29
        descrizione: ring
        etichetta: Anello
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 30
        descrizione: FORing
        etichetta: FO anello
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 31
        descrizione: primarySplitter
        etichetta: Splitter primario
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 32
        descrizione: secondarySplitterCount
        etichetta: N. Splitter Second.
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 33
        descrizione: paymentNetwork
        etichetta: Network rendicontazione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 34
        descrizione: workPerformance
        etichetta: Prestazione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 35
        descrizione: workOrderId
        etichetta: Ordine cliente
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 36
        descrizione: WBELevel3
        etichetta: WBE 3 Livello
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 37
        descrizione: externalMacroActivity
        etichetta: Descrizione Macroattivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 38
        descrizione: externalMacroActivityId
        etichetta: Macroattivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 39
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 40
        descrizione: externalActivityId
        etichetta: Attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 41
        descrizione: externalAccountingType
        etichetta: Tipo contabilità
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 42
        descrizione: testDataMeta001
        etichetta: Nw NgNeer / NW WPSO
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
        valori: 'NW NgNeer = NW WPSO,NW NgNeer <> NW WPSO,NW NgNeer si / NW WPSO no,NW NgNeer no / NW WPSO si'
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_LAVORO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: updateType
        etichetta: Tipo Aggiornamento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: workSuspensionReason
        etichetta: Motivo sospensione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: survey
        etichetta: Sopralluogo
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: infrastructure
        etichetta: Infrastruttura
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: laying
        etichetta: Posa
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: junction
        etichetta: Giunzione
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: restoration
        etichetta: Ripristino
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 8
        descrizione: planning
        etichetta: Progettazione
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: updateDatabaseF1
        etichetta: Update Database F1
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 10
        descrizione: updateDatabaseF2
        etichetta: Update Database F2
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 11
        descrizione: testApp
        etichetta: Collaudo App
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 12
        descrizione: testOTDR
        etichetta: Collaudo OTDR
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 13
        descrizione: externalSequence
        etichetta: Progressivo external sequence
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_NETWORK
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: paymentNetwork
        etichetta: Network rendicontazione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workOrderId
        etichetta: Ordine cliente
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: externalMacroActivity
        etichetta: Descrizione Macroattivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: externalMacroActivityId
        etichetta: Macroattivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: externalActivityId
        etichetta: Attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: externalAccountingType
        etichetta: Tipo contabilità
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: testDataMeta001
        etichetta: Nw NgNeer / NW WPSO
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
        valori: 'NW NgNeer = NW WPSO,NW NgNeer <> NW WPSO,NW NgNeer si / NW WPSO no,NW NgNeer no / NW WPSO si'
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_PERMESSO_BUILDING
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: privatePermitsActivityId
        etichetta: Id Attivita Permesso Privato
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: updateType
        etichetta: Tipo Aggiornamento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: privatePermitsSuspensionReason
        etichetta: Motivo sospensione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNA_ODS
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: ODS
        etichetta: OdS
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Ordine Di Servizio
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNA_RIFERIMENTO_CONTRATTO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: subcontractItem
        etichetta: Identificativo Item
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: APERTURA_LAVORO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: maker
        etichetta: Esecutore
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: teamId
        etichetta: Identificativo squadra
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: teamName
        etichetta: Nome squadra
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: subContractCode
        etichetta: Identificativo fornitore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: subContractName
        etichetta: Nome fornitore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: externalSequence
        etichetta: Progressivo external sequence
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: survey
        etichetta: Sopralluogo
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 8
        descrizione: infrastructure
        etichetta: Infrastruttura
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: laying
        etichetta: Posa
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 10
        descrizione: junction
        etichetta: Giunzione
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 11
        descrizione: restoration
        etichetta: Ripristino
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 12
        descrizione: planning
        etichetta: Progettazione
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 13
        descrizione: updateDatabaseF1
        etichetta: Update Database F1
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 14
        descrizione: updateDatabaseF2
        etichetta: Update Database F2
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 15
        descrizione: testApp
        etichetta: Collaudo App
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 16
        descrizione: testOTDR
        etichetta: Collaudo OTDR
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: APERTURA_PERMESSO_BUILDING
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: maker
        etichetta: Esecutore
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: teamId
        etichetta: Identificativo squadra
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: teamName
        etichetta: Nome squadra
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: subContractCode
        etichetta: Identificativo fornitore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: subContractName
        etichetta: Nome fornitore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: privatePermitsActivityId
        etichetta: Id Attivita Permesso Privato
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: externalSequence
        etichetta: Progressivo external sequence
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_LAVORO_KO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: externalSequence
        etichetta: Progressivo external sequence
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: survey
        etichetta: Sopralluogo
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: infrastructure
        etichetta: Infrastruttura
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: laying
        etichetta: Posa
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: junction
        etichetta: Giunzione
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: restoration
        etichetta: Ripristino
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: planning
        etichetta: Progettazione
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 8
        descrizione: updateDatabaseF1
        etichetta: Update Database F1
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: updateDatabaseF2
        etichetta: Update Database F2
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 10
        descrizione: testApp
        etichetta: Collaudo App
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 11
        descrizione: testOTDR
        etichetta: Collaudo OTDR
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_LAVORO_OK
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: externalSequence
        etichetta: Progressivo external sequence
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: survey
        etichetta: Sopralluogo
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: infrastructure
        etichetta: Infrastruttura
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: laying
        etichetta: Posa
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: junction
        etichetta: Giunzione
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: restoration
        etichetta: Ripristino
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: planning
        etichetta: Progettazione
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 8
        descrizione: updateDatabaseF1
        etichetta: Update Database F1
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: updateDatabaseF2
        etichetta: Update Database F2
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 10
        descrizione: testApp
        etichetta: Collaudo App
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 11
        descrizione: testOTDR
        etichetta: Collaudo OTDR
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_PERMESSO_BUILDING_KO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: privatePermitsActivityId
        etichetta: Id Attivita Permesso Privato
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: externalSequence
        etichetta: Progressivo external sequence
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_PERMESSO_BUILDING_OK
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: privatePermitsActivityId
        etichetta: Id Attivita Permesso Privato
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: externalSequence
        etichetta: Progressivo external sequence
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: DATI_COLLAUDO_CLIENTE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: testData001
        etichetta: Bando
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: testData002
        etichetta: Area di Influenza (AI)
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 2
        descrizione: testData003
        etichetta: Data Collaudo
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: testData004
        etichetta: Data Connected
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 4
        descrizione: testData005
        etichetta: Splitter Secondario (SS)
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 5
        descrizione: testData006
        etichetta: Splitter Primario (SP)
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 6
        descrizione: testData007
        etichetta: Fatturazione
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 7
        descrizione: testData008
        etichetta: Data Test
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
      - posizione: 8
        descrizione: testData009
        etichetta: Esito Collaudo
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'KO,KO VUC,OK,OK PARZIALE'
      - posizione: 9
        descrizione: testData010
        etichetta: Presenza OTDR
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'NO,SI'
      - posizione: 10
        descrizione: testData011
        etichetta: Presenza Foto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 11
        descrizione: testData012
        etichetta: Stato RCI
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'DA COLLAUDARE,DOCUMENTAZIONE KO,OK RCI,MANCA ALLEGATO'
      - posizione: 12
        descrizione: testData013
        etichetta: Data richiesta VUC
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
      - posizione: 13
        descrizione: testData014
        etichetta: Id Stato VUC
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'CONNECTED OK,KO VUC TIM,IN ATTESA CONNECTED,IN ATTESA VUC,OK VUC SISTEMA,OK VUC TIM'
      - posizione: 14
        descrizione: testData015
        etichetta: Data VUC
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
      - posizione: 15
        descrizione: testData016
        etichetta: Network NGNeer
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 16
        descrizione: testData017
        etichetta: OA
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'NORD EST,NORD OVEST,CENTRO,SUD'
      - posizione: 17
        descrizione: testDataMeta001
        etichetta: Nw NgNeer / NW WPSO
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
        valori: 'NW NgNeer = NW WPSO,NW NgNeer <> NW WPSO,NW NgNeer si / NW WPSO no,NW NgNeer no / NW WPSO si'
      - posizione: 18
        descrizione: testDataMeta002
        etichetta: PTE in banca dati
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: ___ANY_STATUS___
    action: DATI_COLLAUDO_CLIENTE_RIMOZIONE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: testData001
        etichetta: Bando
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: testData002
        etichetta: Area di Influenza (AI)
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: testData003
        etichetta: Data Collaudo
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: testData004
        etichetta: Data Connected
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: testData005
        etichetta: Splitter Secondario (SS)
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: testData006
        etichetta: Splitter Primario (SP)
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: testData007
        etichetta: Fatturazione
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: testData008
        etichetta: Data Test
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
      - posizione: 8
        descrizione: testData009
        etichetta: Esito Collaudo
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: 'KO,KO VUC,OK,OK PARZIALE'
      - posizione: 9
        descrizione: testData010
        etichetta: Presenza OTDR
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: 'NO,SI'
      - posizione: 10
        descrizione: testData011
        etichetta: Presenza Foto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 11
        descrizione: testData012
        etichetta: Stato RCI
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: 'DA COLLAUDARE,DOCUMENTAZIONE KO,OK RCI,MANCA ALLEGATO'
      - posizione: 12
        descrizione: testData013
        etichetta: Data richiesta VUC
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
      - posizione: 13
        descrizione: testData014
        etichetta: Id Stato VUC
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: 'CONNECTED OK,KO VUC TIM,IN ATTESA CONNECTED,IN ATTESA VUC,OK VUC SISTEMA,OK VUC TIM'
      - posizione: 14
        descrizione: testData015
        etichetta: Data VUC
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
      - posizione: 15
        descrizione: testData016
        etichetta: Network NGNeer
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 16
        descrizione: testData017
        etichetta: OA
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: 'NORD EST,NORD OVEST,CENTRO,SUD'
      - posizione: 17
        descrizione: testDataMeta001
        etichetta: Nw NgNeer / NW WPSO
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: 'NW NgNeer = NW WPSO,NW NgNeer <> NW WPSO,NW NgNeer si / NW WPSO no,NW NgNeer no / NW WPSO si'
      - posizione: 18
        descrizione: testDataMeta002
        etichetta: PTE in banca dati
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: FORZATURA_ODA_WBE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workOrderId
        etichetta: Ordine cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: WBELevel3
        etichetta: WBE 3 Livello
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 2
        descrizione: clusterJobReport
        etichetta: Cluster
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: penetrationIndex
        etichetta: Fascia rendicontazione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: Y
      - posizione: 4
        descrizione: externalMacroActivity
        etichetta: Descrizione Macroattivita
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: externalMacroActivityId
        etichetta: Macroattivita
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: externalActivityId
        etichetta: Attivita
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: externalAccountingType
        etichetta: Tipo contabilità
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: NUOVA_DOCUMENTAZIONE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
  - stato_iniziale: ___ANY_STATUS___
    action: SOCIETARIZZAZIONE_CLIENTE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: paymentNetwork
        etichetta: Network rendicontazione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: paymentNetworkOld
        etichetta: Network rendicontazione precedente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: workOrderId
        etichetta: Ordine cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: workOrderIdOld
        etichetta: Ordine cliente precedente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: WBELevel3
        etichetta: WBE 3 Livello
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: WBELevel3Old
        etichetta: WBE 3 Livello precedente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: testDataMeta001
        etichetta: Nw NgNeer / NW WPSO
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
        valori: 'NW NgNeer = NW WPSO,NW NgNeer <> NW WPSO,NW NgNeer si / NW WPSO no,NW NgNeer no / NW WPSO si'
      - posizione: 7
        descrizione: teamAssistant
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: teamAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: APERTA
    action: INIZIO_ATTIVITA
    stato_finale: ATTESA_AT
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: ATTESA_AT
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: paymentNetwork
        etichetta: Network rendicontazione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: testDataMeta001
        etichetta: Nw NgNeer / NW WPSO
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
        valori: 'NW NgNeer = NW WPSO,NW NgNeer <> NW WPSO,NW NgNeer si / NW WPSO no,NW NgNeer no / NW WPSO si'
      - posizione: 2
        descrizione: testDataMeta002
        etichetta: PTE in banca dati
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: ATTESA_AT
    action: PRESA_IN_CARICO
    stato_finale: ATTESA_INIZIO_LAVORI
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: teamAssistant
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_AT
    action: PRESA_IN_CARICO_CON_NETWORK
    stato_finale: ATTESA_INIZIO_LAVORI
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: teamAssistant
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: paymentNetwork
        etichetta: Network rendicontazione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: workOrderId
        etichetta: Ordine cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: testDataMeta001
        etichetta: Nw NgNeer / NW WPSO
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
        valori: 'NW NgNeer = NW WPSO,NW NgNeer <> NW WPSO,NW NgNeer si / NW WPSO no,NW NgNeer no / NW WPSO si'
  - stato_iniziale: ATTESA_AT
    action: PRESA_IN_CARICO_CON_ODA
    stato_finale: ATTESA_INIZIO_LAVORI
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: teamAssistant
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: workOrderId
        etichetta: Ordine cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ATTESA_INIZIO_LAVORI
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: paymentNetwork
        etichetta: Network rendicontazione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: testDataMeta001
        etichetta: Nw NgNeer / NW WPSO
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
        valori: 'NW NgNeer = NW WPSO,NW NgNeer <> NW WPSO,NW NgNeer si / NW WPSO no,NW NgNeer no / NW WPSO si'
      - posizione: 2
        descrizione: testDataMeta002
        etichetta: PTE in banca dati
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: ATTESA_INIZIO_LAVORI
    action: INIZIO_LAVORI
    stato_finale: IN_CORSO
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: ATTESA_INIZIO_LAVORI
    action: NON_LAVORABILE
    stato_finale: NON_LAVORABILE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: unworkableReason
        etichetta: Causale non lavorabile
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'KO - Eccessiva Onerosità,KO - Utente non FTTH (Banca / Caserma / Scuola etc...),KO - Distributore Rame Non Presente,KO - Stabile disabitato/in demolizione,KO - Permesso Privato,KO - Permesso Ente,KO - In attesa Variante progetto,KO - In attesa autorizzazione utilizzo infrastruttura altro ente (IRU / illuminazione pubblica)'
      - posizione: 1
        descrizione: testDataMeta002
        etichetta: PTE in banca dati
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: ATTESA_INIZIO_LAVORI
    action: SOSPENSIONE
    stato_finale: SOSPESA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: suspendReason
        etichetta: Causale sospensione
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'KO - Eccessiva Onerosità,KO - Utente non FTTH (Banca / Caserma / Scuola etc...),KO - Distributore Rame Non Presente,KO - Stabile disabitato/in demolizione,KO - Permesso Privato,KO - Permesso Ente,KO - In attesa Variante progetto,KO - In attesa autorizzazione utilizzo infrastruttura altro ente (IRU / illuminazione pubblica)'
  - stato_iniziale: IN_CORSO
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: paymentNetwork
        etichetta: Network rendicontazione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: testDataMeta001
        etichetta: Nw NgNeer / NW WPSO
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
        valori: 'NW NgNeer = NW WPSO,NW NgNeer <> NW WPSO,NW NgNeer si / NW WPSO no,NW NgNeer no / NW WPSO si'
      - posizione: 2
        descrizione: testDataMeta002
        etichetta: PTE in banca dati
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: IN_CORSO
    action: FINE_ATTIVITA
    stato_finale: COLLAUDATO
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: IN_CORSO
    action: NON_LAVORABILE
    stato_finale: NON_LAVORABILE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: unworkableReason
        etichetta: Causale non lavorabile
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'KO - Eccessiva Onerosità,KO - Utente non FTTH (Banca / Caserma / Scuola etc...),KO - Distributore Rame Non Presente,KO - Stabile disabitato/in demolizione,KO - Permesso Privato,KO - Permesso Ente,KO - In attesa Variante progetto,KO - In attesa autorizzazione utilizzo infrastruttura altro ente (IRU / illuminazione pubblica)'
      - posizione: 1
        descrizione: testDataMeta002
        etichetta: PTE in banca dati
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: IN_CORSO
    action: SOSPENSIONE
    stato_finale: SOSPESA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: suspendReason
        etichetta: Causale sospensione
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'KO - Eccessiva Onerosità,KO - Utente non FTTH (Banca / Caserma / Scuola etc...),KO - Distributore Rame Non Presente,KO - Stabile disabitato/in demolizione,KO - Permesso Privato,KO - Permesso Ente,KO - In attesa Variante progetto,KO - In attesa autorizzazione utilizzo infrastruttura altro ente (IRU / illuminazione pubblica)'
  - stato_iniziale: NON_LAVORABILE
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: paymentNetwork
        etichetta: Network rendicontazione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: testDataMeta001
        etichetta: Nw NgNeer / NW WPSO
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
        valori: 'NW NgNeer = NW WPSO,NW NgNeer <> NW WPSO,NW NgNeer si / NW WPSO no,NW NgNeer no / NW WPSO si'
      - posizione: 2
        descrizione: testDataMeta002
        etichetta: PTE in banca dati
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: NON_LAVORABILE
    action: CHIUSURA
    stato_finale: NON_LAVORATO
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: NON_LAVORABILE
    action: RIPRESA
    stato_finale: SMISTAMENTO_DA_NON_LAVORABILE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: testDataMeta002
        etichetta: PTE in banca dati
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: SOSPESA
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: paymentNetwork
        etichetta: Network rendicontazione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: testDataMeta001
        etichetta: Nw NgNeer / NW WPSO
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
        valori: 'NW NgNeer = NW WPSO,NW NgNeer <> NW WPSO,NW NgNeer si / NW WPSO no,NW NgNeer no / NW WPSO si'
      - posizione: 2
        descrizione: testDataMeta002
        etichetta: PTE in banca dati
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: SOSPESA
    action: NON_LAVORABILE
    stato_finale: NON_LAVORABILE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: unworkableReason
        etichetta: Causale non lavorabile
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'KO - Eccessiva Onerosità,KO - Utente non FTTH (Banca / Caserma / Scuola etc...),KO - Distributore Rame Non Presente,KO - Stabile disabitato/in demolizione,KO - Permesso Privato,KO - Permesso Ente,KO - In attesa Variante progetto,KO - In attesa autorizzazione utilizzo infrastruttura altro ente (IRU / illuminazione pubblica)'
      - posizione: 1
        descrizione: testDataMeta002
        etichetta: PTE in banca dati
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: SOSPESA
    action: RIPRESA
    stato_finale: SMISTAMENTO_DA_SOSPESA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: testDataMeta002
        etichetta: PTE in banca dati
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: Y
  - stato_iniziale: COLLAUDATO
    action: CHIUSURA
    stato_finale: LAVORATO
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: COLLAUDATO
    action: RITORNO_IN_LAVORAZIONE
    stato_finale: IN_CORSO
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: SMISTAMENTO_DA_NON_LAVORABILE
    action: RIPRESA_DA_ATTESA_INIZIO_LAVORI
    stato_finale: ATTESA_INIZIO_LAVORI
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: SMISTAMENTO_DA_NON_LAVORABILE
    action: RIPRESA_DA_IN_CORSO
    stato_finale: IN_CORSO
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: SMISTAMENTO_DA_SOSPESA
    action: RIPRESA_DA_ATTESA_INIZIO_LAVORI
    stato_finale: ATTESA_INIZIO_LAVORI
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: SMISTAMENTO_DA_SOSPESA
    action: RIPRESA_DA_IN_CORSO
    stato_finale: IN_CORSO
    gruppi:
      - ADMIN
      - ROOT
