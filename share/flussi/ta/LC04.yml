---
tipo_attivita: LC04
tipi_sistema:
  - EL04
transizioni:
  - stato_iniziale: START
    action: APERTURA
    stato_finale: APERTA
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
    tdta:
      - posizione: 0
        descrizione: geoCoder
        etichetta: Formato codifica GEO
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: geoCode
        etichetta: Codifica GEO
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: geoCoordinates
        etichetta: Coordinate
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: address
        etichetta: Indirizzo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: ftthContract
        etichetta: Contratto FTTH
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: parentAssetId
        etichetta: Id Asset Riferimento
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: parentAssetName
        etichetta: Nome Asset Riferimento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: LDO
        etichetta: LDO
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 8
        descrizione: workOrderId
        etichetta: Ordine cliente
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
      - posizione: 10
        descrizione: PFS
        etichetta: PFS
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 11
        descrizione: ftthAssetId
        etichetta: PTE/PTA
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 12
        descrizione: ftthUI
        etichetta: Numero UI
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 13
        descrizione: customerId
        etichetta: Id Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 14
        descrizione: contractId
        etichetta: Id Contratto
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 15
        descrizione: ftthCustomerCertification
        etichetta: Certificato Cliente
        tipo_ui: 'BOOL  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 16
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_DATI
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
    tdta:
      - posizione: 0
        descrizione: address
        etichetta: Indirizzo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: ftthContract
        etichetta: Contratto FTTH
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 2
        descrizione: LDO
        etichetta: LDO
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: workOrderId
        etichetta: Ordine cliente
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 4
        descrizione: PFS
        etichetta: PFS
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 5
        descrizione: ftthAssetId
        etichetta: PTE/PTA
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 6
        descrizione: ftthUI
        etichetta: Numero UI
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 7
        descrizione: ftthCustomerCertification
        etichetta: Certificato Cliente
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_PTE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: ftthAssetId
        etichetta: PTE/PTA
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CERTIFICAZIONE_CLIENTE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
    tdta:
      - posizione: 0
        descrizione: ftthCustomerCertification
        etichetta: Certificato Cliente
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
  - stato_iniziale: ___ANY_STATUS___
    action: NUOVA_DOCUMENTAZIONE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
  - stato_iniziale: APERTA
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancellationDay
        etichetta: Data Annullamento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: cancellationReason
        etichetta: Motivo Annullamento
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Civico inesistente,Civico già servito,Altro'
  - stato_iniziale: APERTA
    action: PRESA_IN_CARICO
    stato_finale: DA_LAVORARE
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
    tdta:
      - posizione: 0
        descrizione: takeInChargeDay
        etichetta: Data presa in carico
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
  - stato_iniziale: DA_LAVORARE
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancellationDay
        etichetta: Data Annullamento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: cancellationReason
        etichetta: Motivo Annullamento
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Civico inesistente,Civico già servito,Altro'
  - stato_iniziale: DA_LAVORARE
    action: CABLAGGIO
    stato_finale: RAGGIUNTO
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cablingDay
        etichetta: Data Cablaggio
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
  - stato_iniziale: DA_LAVORARE
    action: LAVORI_CIVILI
    stato_finale: INFRASTRUTTURATO
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
  - stato_iniziale: DA_LAVORARE
    action: PRESENZA_VINCOLO
    stato_finale: VINCOLATO
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
    tdta:
      - posizione: 0
        descrizione: constraintDay
        etichetta: Data Vincolo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: constraintReason
        etichetta: Causale vincolo
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Attesa privato,Diniego privato,Attesa Ente e utilities,Diniego Ente e utilities,Altro'
  - stato_iniziale: RAGGIUNTO
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancellationDay
        etichetta: Data Annullamento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: cancellationReason
        etichetta: Motivo Annullamento
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Civico inesistente,Civico già servito,Altro'
  - stato_iniziale: RAGGIUNTO
    action: CABLAGGIO_KO
    stato_finale: INFRASTRUTTURATO
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
  - stato_iniziale: RAGGIUNTO
    action: GIUNZIONE
    stato_finale: GIUNTATO
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
    tdta:
      - posizione: 0
        descrizione: junctionDay
        etichetta: Data Giunzione
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
  - stato_iniziale: RAGGIUNTO
    action: PRESENZA_VINCOLO
    stato_finale: VINCOLATO
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
    tdta:
      - posizione: 0
        descrizione: constraintDay
        etichetta: Data Vincolo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: constraintReason
        etichetta: Causale vincolo
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Attesa privato,Diniego privato,Attesa Ente e utilities,Diniego Ente e utilities,Altro'
  - stato_iniziale: INFRASTRUTTURATO
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancellationDay
        etichetta: Data Annullamento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: cancellationReason
        etichetta: Motivo Annullamento
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Civico inesistente,Civico già servito,Altro'
  - stato_iniziale: INFRASTRUTTURATO
    action: CABLAGGIO
    stato_finale: RAGGIUNTO
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cablingDay
        etichetta: Data Cablaggio
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
  - stato_iniziale: INFRASTRUTTURATO
    action: LAVORI_CIVILI_KO
    stato_finale: DA_LAVORARE
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
  - stato_iniziale: INFRASTRUTTURATO
    action: PRESENZA_VINCOLO
    stato_finale: VINCOLATO
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
    tdta:
      - posizione: 0
        descrizione: constraintDay
        etichetta: Data Vincolo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: constraintReason
        etichetta: Causale vincolo
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Attesa privato,Diniego privato,Attesa Ente e utilities,Diniego Ente e utilities,Altro'
  - stato_iniziale: VINCOLATO
    action: RIMOZIONE_VINCOLO
    stato_finale: __SMISTAMENTO_VINCOLO
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
    tdta:
      - posizione: 0
        descrizione: revokeConstraintDay
        etichetta: Data rimozione vincolo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
  - stato_iniziale: GIUNTATO
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancellationDay
        etichetta: Data Annullamento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: cancellationReason
        etichetta: Motivo Annullamento
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Civico inesistente,Civico già servito,Altro'
  - stato_iniziale: GIUNTATO
    action: COLLAUDO
    stato_finale: COLLAUDATO
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
    tdta:
      - posizione: 0
        descrizione: testDay
        etichetta: Data Collaudo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
  - stato_iniziale: GIUNTATO
    action: GIUNZIONE_KO
    stato_finale: RAGGIUNTO
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
  - stato_iniziale: __SMISTAMENTO_VINCOLO
    action: RITORNA_IN_DA_LAVORARE
    stato_finale: DA_LAVORARE
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: __SMISTAMENTO_VINCOLO
    action: RITORNA_IN_INFRASTRUTTURATO
    stato_finale: INFRASTRUTTURATO
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: __SMISTAMENTO_VINCOLO
    action: RITORNA_IN_RAGGIUNTO
    stato_finale: RAGGIUNTO
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: COLLAUDATO
    action: COLLAUDO_KO
    stato_finale: GIUNTATO
    gruppi:
      - ADMIN
      - AT
      - AT02
      - PM02
      - ROOT
  - stato_iniziale: COLLAUDATO
    action: RENDICONTAZIONE
    stato_finale: RENDICONTATO
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: reportingDay
        etichetta: Data Rendicontazione
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
