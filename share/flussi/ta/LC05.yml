---
tipo_attivita: LC05
tipi_sistema:
  - EL05
transizioni:
  - stato_iniziale: START
    action: APERTURA
    stato_finale: APERTA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: geoCoder
        etichetta: Formato codifica GEO
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: geoCode
        etichetta: Codifica GEO
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: geoCoordinates
        etichetta: Coordinate
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: address
        etichetta: Indirizzo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: parentAssetId
        etichetta: Id Asset Riferimento
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: parentAssetName
        etichetta: Nome Asset Riferimento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
      - posizione: 7
        descrizione: ftthAssetId
        etichetta: PTE/PTA
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 8
        descrizione: ftthUI
        etichetta: Numero UI
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: customerId
        etichetta: Id Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 10
        descrizione: contractId
        etichetta: Id Contratto
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 11
        descrizione: ftthCustomerCertification
        etichetta: Certificato Cliente
        tipo_ui: 'BOOL  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 12
        descrizione: ftthAddressAnnouncement
        etichetta: A bando
        tipo_ui: 'BOOL  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 13
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 14
        descrizione: ftthAddressType
        etichetta: Tipologia civico
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'PTE Light Realizzato,PTE Full Ubicato,Infrastruttura esistente'
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_DATI
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: address
        etichetta: Indirizzo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: ftthAssetId
        etichetta: PTE/PTA
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 2
        descrizione: ftthUI
        etichetta: Numero UI
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: ftthCustomerCertification
        etichetta: Certificato Cliente
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 4
        descrizione: ftthAddressAnnouncement
        etichetta: A bando
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_FILE_AREA_INFLUENZA
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: CLLI
        etichetta: CLLI
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: ftthAddressROEType
        etichetta: Tipo ROE
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: ftthAddressDistributor
        etichetta: Distributore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: geoCoordinates
        etichetta: Coordinate
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: ftthAddressTestDate
        etichetta: Data collaudo
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: ftthAddressConnectedDate
        etichetta: Data connected
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: requestId
        etichetta: Id attivita caricamento
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: requestorName
        etichetta: Utente richiedente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_FILE_CIVICI
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: ftthAddressPROCOM2021
        etichetta: PROCOM2021
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: geoCoordinates
        etichetta: Coordinate
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: address
        etichetta: Indirizzo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: ftthUI
        etichetta: Numero UI
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: ftthAddressUIHouse
        etichetta: N_UI_ABITA
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: ftthAddressUIFactory
        etichetta: N_UI_IMPR
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: ftthAddressUIPA
        etichetta: N_UI_PA
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: CLLI
        etichetta: CLLI
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 8
        descrizione: ftthAddressPTELight
        etichetta: PTE Light
        tipo_ui: 'BOOL  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: ftthAddressNMonof
        etichetta: N_MONOF
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 10
        descrizione: ftthAddressPotMultif
        etichetta: POT_MULTIF
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 11
        descrizione: ftthAddressCluster
        etichetta: LOTTO
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 12
        descrizione: ftthAddressCUP
        etichetta: CUP
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 13
        descrizione: ftthAddressProjectNA
        etichetta: PROJECT_NA
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 14
        descrizione: requestId
        etichetta: Id attivita caricamento
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 15
        descrizione: requestorName
        etichetta: Utente richiedente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_INFO_A_BANDO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: ftthAddressAnnouncement
        etichetta: A bando
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_PTE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: ftthAssetId
        etichetta: PTE/PTA
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CERTIFICAZIONE_CLIENTE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: ftthCustomerCertification
        etichetta: Certificato Cliente
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
  - stato_iniziale: ___ANY_STATUS___
    action: DA_DOCUMENTARE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: ftthUpdateDBSubcontractName
        etichetta: Subappalto documentazione
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: "{\n  \"TYPE\": \"URI\",\n  \"URI\": \"../../../resources/so/suppliers\",\n  \"LAYOUT\": \"AUTOCOMPLETE\",\n  \"CONTEXT\": \"Config\",\n  \"ADD_FROM_AUTOCOMPLETE_ONLY\": true\r,\n  \"CONTEXT_KEY\": \"SiNFOWPSOCOREConfigPage\",\n  \"DISPLAY_PROPERTY\": \"companyName\",\n  \"KEY_PROPERTY\": \"companyCode\",\n  \"RESPONSE_DATA_KEY\": \"companyName\",\n  \"QUERY_KEY\": \"companyName\"\n}"
      - posizione: 1
        descrizione: ftthUpdateDBSubcontractCode
        etichetta: Identificativo subappalto documentazione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: DA_INVENTARIARE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: ftthInventorySubcontractName
        etichetta: Subappalto inventario
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: "{\n  \"TYPE\": \"URI\",\n  \"URI\": \"../../../resources/so/suppliers\",\n  \"LAYOUT\": \"AUTOCOMPLETE\",\n  \"CONTEXT\": \"Config\",\n  \"ADD_FROM_AUTOCOMPLETE_ONLY\": true\r,\n  \"CONTEXT_KEY\": \"SiNFOWPSOCOREConfigPage\",\n  \"DISPLAY_PROPERTY\": \"companyName\",\n  \"KEY_PROPERTY\": \"companyCode\",\n  \"RESPONSE_DATA_KEY\": \"companyName\",\n  \"QUERY_KEY\": \"companyName\"\n}"
      - posizione: 1
        descrizione: ftthInventorySubcontractCode
        etichetta: Identificativo subappalto inventario
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: DOCUMENTAZIONE_KO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: ftthUpdateDBStatus
        etichetta: Stato documentazione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: ftthUpdateDBKOReason
        etichetta: Motivazione documentazione KO
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'PROGETTATO ANCHE SU ALTRO ARMADIO,KO BD PTE FULL,ALTRO'
      - posizione: 2
        descrizione: ftthUpdateDBKONote
        etichetta: Nota documentazione KO
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: DOCUMENTAZIONE_OK
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: ftthUpdateDBStatus
        etichetta: Stato documentazione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: ftthUpdateDBKOReason
        etichetta: Motivazione documentazione KO
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: 'PROGETTATO ANCHE SU ALTRO ARMADIO,KO BD PTE FULL,ALTRO'
      - posizione: 2
        descrizione: ftthUpdateDBKONote
        etichetta: Nota documentazione KO
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: INVENTARIO_KO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: ftthInventoryStatus
        etichetta: Stato inventario
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: ftthInventoryKOReason
        etichetta: Motivazione inventario KO
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'NESSUN PTE ASSOCIABILE (MANCA O ECCESSIVAMENTE DISTANTE),PTE IN ATTESA DI RISOLUZIONE TK E/O CERTIFICAZIONE CIVICO,EGON NON PRESENTE NGNEER/DAFNE,STESSO CIVICO EGON DIVERSO,ALTRO'
      - posizione: 2
        descrizione: ftthInventoryKONote
        etichetta: Nota inventario KO
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: INVENTARIO_OK
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: ftthInventoryStatus
        etichetta: Stato inventario
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: ftthInventoryKOReason
        etichetta: Motivazione inventario KO
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: 'NESSUN PTE ASSOCIABILE (MANCA O ECCESSIVAMENTE DISTANTE),PTE IN ATTESA DI RISOLUZIONE TK E/O CERTIFICAZIONE CIVICO,EGON NON PRESENTE NGNEER/DAFNE,STESSO CIVICO EGON DIVERSO,ALTRO'
      - posizione: 2
        descrizione: ftthInventoryKONote
        etichetta: Nota inventario KO
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: ftthAddressType
        etichetta: Tipologia civico
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'PTE Light Realizzato,PTE Full Ubicato,Infrastruttura esistente'
      - posizione: 4
        descrizione: ftthAddressInventoryType
        etichetta: Tipologia inventario
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: Y
        valori: 'Invio a Inventory,Area di influenza'
  - stato_iniziale: ___ANY_STATUS___
    action: NUOVA_DOCUMENTAZIONE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: ___ANY_STATUS___
    action: TIPOLOGIA_CIVICO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: ftthAddressType
        etichetta: Tipologia civico
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'PTE Light Realizzato,PTE Full Ubicato,Infrastruttura esistente'
  - stato_iniziale: APERTA
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancellationDay
        etichetta: Data Annullamento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: cancellationReason
        etichetta: Motivo Annullamento
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Civico inesistente,Civico già servito,Altro'
  - stato_iniziale: APERTA
    action: PRESA_IN_CARICO
    stato_finale: DA_LAVORARE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: takeInChargeDay
        etichetta: Data presa in carico
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
  - stato_iniziale: DA_LAVORARE
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancellationDay
        etichetta: Data Annullamento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: cancellationReason
        etichetta: Motivo Annullamento
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Civico inesistente,Civico già servito,Altro'
  - stato_iniziale: DA_LAVORARE
    action: CABLAGGIO
    stato_finale: RAGGIUNTO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cablingDay
        etichetta: Data Cablaggio
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
  - stato_iniziale: DA_LAVORARE
    action: LAVORI_CIVILI
    stato_finale: INFRASTRUTTURATO
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: DA_LAVORARE
    action: PRESENZA_VINCOLO
    stato_finale: VINCOLATO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: constraintDay
        etichetta: Data Vincolo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: constraintReason
        etichetta: Causale vincolo
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Attesa privato,Diniego privato,Attesa Ente e utilities,Diniego Ente e utilities,Altro'
  - stato_iniziale: RAGGIUNTO
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancellationDay
        etichetta: Data Annullamento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: cancellationReason
        etichetta: Motivo Annullamento
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Civico inesistente,Civico già servito,Altro'
  - stato_iniziale: RAGGIUNTO
    action: CABLAGGIO_KO
    stato_finale: INFRASTRUTTURATO
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: RAGGIUNTO
    action: GIUNZIONE
    stato_finale: GIUNTATO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: junctionDay
        etichetta: Data Giunzione
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
  - stato_iniziale: RAGGIUNTO
    action: PRESENZA_VINCOLO
    stato_finale: VINCOLATO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: constraintDay
        etichetta: Data Vincolo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: constraintReason
        etichetta: Causale vincolo
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Attesa privato,Diniego privato,Attesa Ente e utilities,Diniego Ente e utilities,Altro'
  - stato_iniziale: INFRASTRUTTURATO
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancellationDay
        etichetta: Data Annullamento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: cancellationReason
        etichetta: Motivo Annullamento
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Civico inesistente,Civico già servito,Altro'
  - stato_iniziale: INFRASTRUTTURATO
    action: CABLAGGIO
    stato_finale: RAGGIUNTO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cablingDay
        etichetta: Data Cablaggio
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
  - stato_iniziale: INFRASTRUTTURATO
    action: LAVORI_CIVILI_KO
    stato_finale: DA_LAVORARE
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: INFRASTRUTTURATO
    action: PRESENZA_VINCOLO
    stato_finale: VINCOLATO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: constraintDay
        etichetta: Data Vincolo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: constraintReason
        etichetta: Causale vincolo
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Attesa privato,Diniego privato,Attesa Ente e utilities,Diniego Ente e utilities,Altro'
  - stato_iniziale: VINCOLATO
    action: RIMOZIONE_VINCOLO
    stato_finale: __SMISTAMENTO_VINCOLO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: revokeConstraintDay
        etichetta: Data rimozione vincolo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
  - stato_iniziale: GIUNTATO
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancellationDay
        etichetta: Data Annullamento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: cancellationReason
        etichetta: Motivo Annullamento
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Civico inesistente,Civico già servito,Altro'
  - stato_iniziale: GIUNTATO
    action: COLLAUDO
    stato_finale: COLLAUDATO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: testDay
        etichetta: Data Collaudo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
  - stato_iniziale: GIUNTATO
    action: GIUNZIONE_KO
    stato_finale: RAGGIUNTO
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: __SMISTAMENTO_VINCOLO
    action: RITORNA_IN_DA_LAVORARE
    stato_finale: DA_LAVORARE
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: __SMISTAMENTO_VINCOLO
    action: RITORNA_IN_INFRASTRUTTURATO
    stato_finale: INFRASTRUTTURATO
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: __SMISTAMENTO_VINCOLO
    action: RITORNA_IN_RAGGIUNTO
    stato_finale: RAGGIUNTO
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: COLLAUDATO
    action: COLLAUDO_KO
    stato_finale: GIUNTATO
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: COLLAUDATO
    action: RENDICONTAZIONE
    stato_finale: RENDICONTATO
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: reportingDay
        etichetta: Data Rendicontazione
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
