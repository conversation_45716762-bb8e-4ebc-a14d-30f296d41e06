---
tipo_attivita: MANUTENZIONE_CORRETTIVA
tipi_sistema:
  - MANUTENZIONE_CORRETTIVA
transizioni:
  - stato_iniziale: START
    action: APERTURA
    stato_finale: APERTA
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - TIM_OP
      - TIM_RT
    tdta:
      - posizione: 0
        descrizione: oa
        etichetta: OA
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: FOL
        etichetta: FOL
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: customerCentralA
        etichetta: CLLI Centrale A
        tipo_ui: LOOKUP
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: |-
          {
            "TYPE": "URI",
            "URI": "../../../customers/TIM/contracts/RTN_MNT/so/maintenance/corrective/lookups/customerCentralA",
            "LAYOUT": "AUTOCOMPLETE",
            "CONTEXT": "Config",
            "CONTEXT_KEY": "SiNFOWPSOCOREConfigPage",
            "ADD_FROM_AUTOCOMPLETE_ONLY": true
          }
      - posizione: 3
        descrizione: customerCentralZ
        etichetta: CLLI Centrale Z
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: |-
          {
            "TYPE": "URI",
            "URI": "lookups/customerCentralZ",
            "LAYOUT": "AUTOCOMPLETE",
            "CONTEXT": "App",
            "ADD_FROM_AUTOCOMPLETE_ONLY": true
          }
      - posizione: 4
        descrizione: customerCentralADesc
        etichetta: Descrizione Centrale A
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: customerCentralZDesc
        etichetta: Descrizione Centrale Z
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: mntPlant
        etichetta: Impianto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: |-
          {
            "TYPE": "URI",
            "URI": "lookups/mntPlant",
            "LAYOUT": "AUTOCOMPLETE",
            "CONTEXT": "App",
            "ADD_FROM_AUTOCOMPLETE_ONLY": false
          }
      - posizione: 7
        descrizione: mntPlantCode
        etichetta: Codice Impianto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: |-
          {
            "TYPE": "URI",
            "URI": "lookups/mntPlantCode",
            "LAYOUT": "AUTOCOMPLETE",
            "CONTEXT": "App",
            "ADD_FROM_AUTOCOMPLETE_ONLY": false
          }
      - posizione: 8
        descrizione: mntLink
        etichetta: Collegamento
        tipo_ui: MOOKUP
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: '{"TYPE": "URI", "URI" : "", "LAYOUT": "SELECT", "CONTEXT": "App", "ADD_FROM_AUTOCOMPLETE_ONLY": false}'
      - posizione: 9
        descrizione: failedCablesNumber
        etichetta: Numero cavi guasti
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 10
        descrizione: customerLocation
        etichetta: Localizzazione TIM
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 11
        descrizione: customerContact
        etichetta: Riferimento TIM da contattare
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 12
        descrizione: customerEmail
        etichetta: E-mail riferimento TIM
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 13
        descrizione: customerRTNEmail
        etichetta: E-mail riferimento RTN TIM
        tipo_ui: 'EMAIL '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 14
        descrizione: mntNote
        etichetta: Note
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 15
        descrizione: reporter
        etichetta: Fonte segnalazione del guasto
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'TIM,Altri'
      - posizione: 16
        descrizione: corrRegion
        etichetta: Competenza territoriale Sirti
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Calabria,Campania Nord,Campania Sud,Emilia Romagna,Friuli Venezia Giulia,Lazio /Abruzzo,Liguria,Lombardia,Marche - Umbria,Piemonte,Puglia,Sardegna,Sicilia Est,Sicilia Ovest,Toscana,Veneto / Trentino Alto Adige,Veneto Nord'
      - posizione: 17
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 18
        descrizione: openedBy
        etichetta: Aperto da
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: 'SIRTI,Cliente'
      - posizione: 19
        descrizione: __DTC_OTD
        etichetta: Traccia OTDR
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 20
        descrizione: __DTC_SSG
        etichetta: Scheda di segnalazione guasto
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 21
        descrizione: __DTC_VAR
        etichetta: DOC. VARIE
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 22
        descrizione: __DTC_MR8
        etichetta: Documento MR8
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 23
        descrizione: __DTC_FGU
        etichetta: Foto Guasto
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 24
        descrizione: __DTC_PDFP
        etichetta: PDF Preventiva
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 25
        descrizione: __DTC_FGEO
        etichetta: Foto Georeferenziata
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 26
        descrizione: openDate
        etichetta: Data esecuzione apertura segnalazione guasto
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_LAVORO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: updateType
        etichetta: Tipo Aggiornamento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: workSuspensionReason
        etichetta: Motivo sospensione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: APERTURA_LAVORO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: externalSequence
        etichetta: Progressivo external sequence
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_LAVORO_KO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_LAVORO_OK
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: endWorkDate
        etichetta: Data fine lavori
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: NUOVA_DOCUMENTAZIONE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: APERTA
    action: APERTURA_SIRTI
    stato_finale: ATTESA_INIZIO_LAVORI
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: currentUser
        etichetta: Login assistente tecnico assegnatario
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: currentUserName
        etichetta: Assistente tecnico assegnatario
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: onFieldContactName
        etichetta: Referente in campo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: onFieldContactId
        etichetta: Identificativo referente in campo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: takeOverDate
        etichetta: Data esecuzione presa in carico
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
  - stato_iniziale: APERTA
    action: RISCONTRATO_DISSERVIZIO
    stato_finale: ATTESA_PRESA_IN_CARICO
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: ATTESA_INIZIO_LAVORI
    action: ANNULLAMENTO
    stato_finale: ATTESA_CHIUSURA_LAVORO_ANNULLAMENTO
    gruppi:
      - ADMIN
      - AT
      - REMOTE_AUTOMATION
      - ROOT
    tdta:
      - posizione: 0
        descrizione: technicalCancelDate
        etichetta: Data Annullamento Tecnico
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: technicalCancelReason
        etichetta: Causale Annullamento Tecnico
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Errata apertura,Guasto duplicato'
      - posizione: 2
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: mntDefectGeoRef
        etichetta: Georeferenziazione punto guasto
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
  - stato_iniziale: ATTESA_INIZIO_LAVORI
    action: INIZIO_SOPRALLUOGO
    stato_finale: ANALISI_AMBIENTALE_E_DETTAGLIO_RISCONTRO_GUASTO
    gruppi:
      - ADMIN
      - AT
      - REMOTE_AUTOMATION
      - ROOT
    tdta:
      - posizione: 0
        descrizione: maintCorrStartWorkDate
        etichetta: Data inizio intervento
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: maintCorrStartWorkDay
        etichetta: Giorno inizio intervento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: maintCorrStartWorkHour
        etichetta: Ora inizio intervento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: mntDefectGeoRef
        etichetta: Georeferenziazione punto guasto
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
      - posizione: 7
        descrizione: surveyStartDate
        etichetta: Data esecuzione inizio sopralluogo
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
  - stato_iniziale: ATTESA_PRESA_IN_CARICO
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: technicalCancelDate
        etichetta: Data Annullamento Tecnico
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: technicalCancelReason
        etichetta: Causale Annullamento Tecnico
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Errata apertura,Guasto duplicato'
      - posizione: 2
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: mntDefectGeoRef
        etichetta: Georeferenziazione punto guasto
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
  - stato_iniziale: ATTESA_PRESA_IN_CARICO
    action: ANNULLAMENTO_CLIENTE
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - ROOT
      - TIM_OP
      - TIM_RT
    tdta:
      - posizione: 0
        descrizione: cancelDate
        etichetta: Data Annullamento
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: cancelReason
        etichetta: Motivo Annullamento
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_PRESA_IN_CARICO
    action: ASSEGNAZIONE_DISSERVIZIO
    stato_finale: ATTESA_INIZIO_LAVORI
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: currentUser
        etichetta: Login assistente tecnico assegnatario
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: currentUserName
        etichetta: Assistente tecnico assegnatario
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: onFieldContactName
        etichetta: Referente in campo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: onFieldContactId
        etichetta: Identificativo referente in campo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: assignmentDate
        etichetta: Data esecuzione assegnazione disservizio
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
  - stato_iniziale: ATTESA_PRESA_IN_CARICO
    action: ERRATA_COMPETENZA
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: mntRepairNotes
        etichetta: Descrizione dell'evento di guasto e dell'intervento di riparazione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Questo valore verrà inserito nel documento MR8
      - posizione: 1
        descrizione: companyManager
        etichetta: Firma impresa
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Questo valore verrà inserito nel documento MR8
      - posizione: 2
        descrizione: cancelDate
        etichetta: Data Annullamento
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 3
        descrizione: cancelReason
        etichetta: Motivo Annullamento
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: wrongCompetenceDate
        etichetta: Data esecuzione errata competenza
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 5
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: mntDefectGeoRef
        etichetta: Georeferenziazione punto guasto
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
  - stato_iniziale: ATTESA_PRESA_IN_CARICO
    action: PRESA_IN_CARICO_DISSERVIZIO
    stato_finale: ATTESA_INIZIO_LAVORI
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: currentUser
        etichetta: Login assistente tecnico assegnatario
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: currentUserName
        etichetta: Assistente tecnico assegnatario
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: onFieldContactName
        etichetta: Referente in campo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: onFieldContactId
        etichetta: Identificativo referente in campo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: takeOverDate
        etichetta: Data esecuzione presa in carico
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
  - stato_iniziale: ATTESA_CHIUSURA_LAVORO_ANNULLAMENTO
    action: CHIUSURA
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - REMOTE_AUTOMATION
      - ROOT
  - stato_iniziale: ANALISI_AMBIENTALE_E_DETTAGLIO_RISCONTRO_GUASTO
    action: ANNULLAMENTO
    stato_finale: ATTESA_CHIUSURA_LAVORO_ANNULLAMENTO
    gruppi:
      - ADMIN
      - AT
      - REMOTE_AUTOMATION
      - ROOT
    tdta:
      - posizione: 0
        descrizione: technicalCancelDate
        etichetta: Data Annullamento Tecnico
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: technicalCancelReason
        etichetta: Causale Annullamento Tecnico
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Errata apertura,Guasto duplicato'
      - posizione: 2
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: mntDefectGeoRef
        etichetta: Georeferenziazione punto guasto
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
  - stato_iniziale: ANALISI_AMBIENTALE_E_DETTAGLIO_RISCONTRO_GUASTO
    action: ERRATA_COMPETENZA
    stato_finale: ATTESA_CHIUSURA_LAVORO_ANNULLAMENTO
    gruppi:
      - ADMIN
      - AT
      - REMOTE_AUTOMATION
      - ROOT
    tdta:
      - posizione: 0
        descrizione: mntRepairNotes
        etichetta: Descrizione dell'evento di guasto e dell'intervento di riparazione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Questo valore verrà inserito nel documento MR8
      - posizione: 1
        descrizione: companyManager
        etichetta: Firma impresa
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Questo valore verrà inserito nel documento MR8
      - posizione: 2
        descrizione: cancelDate
        etichetta: Data Annullamento
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 3
        descrizione: cancelReason
        etichetta: Motivo Annullamento
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: wrongCompetenceDate
        etichetta: Data esecuzione errata competenza
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 5
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: mntDefectGeoRef
        etichetta: Georeferenziazione punto guasto
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
  - stato_iniziale: ANALISI_AMBIENTALE_E_DETTAGLIO_RISCONTRO_GUASTO
    action: REGISTRAZIONE_SOPRALLUOGO_CON_GUASTO_NON_RISCONTRATO
    stato_finale: ATTESA_CHIUSURA_LAVORO
    gruppi:
      - ADMIN
      - AT
      - REMOTE_AUTOMATION
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancelDate
        etichetta: Data Annullamento
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: cancelReason
        etichetta: Motivo Annullamento
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: companyManager
        etichetta: Firma impresa
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Questo valore verrà inserito nel documento MR8
      - posizione: 3
        descrizione: mntRelocation
        etichetta: Ri-localizzazione SIRTI
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: mntRepairNotes
        etichetta: Descrizione dell'evento di guasto e dell'intervento di riparazione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Questo valore verrà inserito nel documento MR8
      - posizione: 5
        descrizione: defectNotFoundDate
        etichetta: Data esecuzione registrazione sopralluogo con guasto non riscontrato
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 6
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 9
        descrizione: mntDefectGeoRef
        etichetta: Georeferenziazione punto guasto
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
  - stato_iniziale: ANALISI_AMBIENTALE_E_DETTAGLIO_RISCONTRO_GUASTO
    action: REGISTRAZIONE_SOPRALLUOGO_CON_GUASTO_RISCONTRATO
    stato_finale: ATTESA_RIPARAZIONE
    gruppi:
      - ADMIN
      - AT
      - REMOTE_AUTOMATION
      - ROOT
    tdta:
      - posizione: 0
        descrizione: mntRelocation
        etichetta: Ri-localizzazione SIRTI
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: reason
        etichetta: Causa Danneggiamento
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Danni da Terzi - Ignoti,Danni da Terzi - Noti,Degrado Materiali,Difetto di installazione,Roditori,Incendio,Cedimento sede di posa,Eventi naturali,Doloso'
      - posizione: 2
        descrizione: mntRequestedAssistance
        etichetta: Assistenza richiesta
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: mntDamagingRegistry
        etichetta: Anagrafica danneggiante
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: corrRegionDefectSeat
        etichetta: Regione sede guasto
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: "Abruzzo,Basilicata,Calabria,Campania,Emilia Romagna,Friuli Venezia Giulia,Lazio,Liguria,Lombardia,Marche,Molise,Piemonte,Puglia,Sardegna,Sicilia,Toscana,Trentino Alto Adige,Umbria,Val d'Aosta,Veneto"
      - posizione: 5
        descrizione: mntPurchaserRegistry
        etichetta: Anagrafica committente
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: corrRegionDefectManagement
        etichetta: Gestore sede guasto
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Anas,Regione,Provincia,Comune,Astral,PRIVATO,SALT,AdF,ASPI,ATIVA,SATAP,MILANO - SERRAVALLE,ATMB,ATS,SAV,A21 AUTOVIE PADANE,A22 AUTOSTRADA DEL BRENNERO,A4 AUTOSTRADA BS-PD,AUTOVIE VENETE,VENETO STRADE,CAV,FVG STRADE,EDR,A15 AUTOSTRADA DELLA CISA,ENTE EUR,SAT,STRADA DEI PARCHI,TANGENZIALE DI NAPOLI,CAS'
      - posizione: 7
        descrizione: mntDefectSeat
        etichetta: Sede guasto
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'AUTOSTRADALE,STRADALE'
      - posizione: 8
        descrizione: mntDefectStreet
        etichetta: 'Località guasto - strada'
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: mntDefectKm
        etichetta: 'Località guasto - chilometrica'
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 10
        descrizione: mntRTNCablesQuantity
        etichetta: Quantità di cavi RTN presenti
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '0,1,2,3,4,5,6,7,8,9,10'
      - posizione: 11
        descrizione: mntRTNDamagedCablesQuantity
        etichetta: Quantità di cavi RTN danneggiati
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '0,1,2,3,4,5,6,7,8,9,10'
      - posizione: 12
        descrizione: mntRGRCablesQuantity
        etichetta: Quantità di cavi RGR presenti
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '0,1,2,3,4,5,6,7,8,9,10'
      - posizione: 13
        descrizione: mntRGRDamagedCablesQ
        etichetta: Quantità di cavi RGR danneggiati
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '0,1,2,3,4,5,6,7,8,9,10'
      - posizione: 14
        descrizione: mntASPIOthCablesQuantity
        etichetta: Quantità di cavi ASPI non a canone presenti
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '0,1,2,3,4,5,6,7,8,9,10'
      - posizione: 15
        descrizione: mntASPIOthDamagedCablesQ
        etichetta: Quantità di cavi ASPI non a canone danneggiati
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '0,1,2,3,4,5,6,7,8,9,10'
      - posizione: 16
        descrizione: mntOthersCablesQuantity
        etichetta: Quantità di cavi di altri gestori presenti
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '0,1,2,3,4,5,6,7,8,9,10'
      - posizione: 17
        descrizione: mntDamagedInfrastructure
        etichetta: Infrastruttura danneggiata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 18
        descrizione: damagedInfrastructureType
        etichetta: Tipo di infrastruttura danneggiata
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Tritubo in trincea,Tritubo in zancatura,Palificazione,Canalizzazione,Galleria PP.SS.,No-dig,altro'
      - posizione: 19
        descrizione: diggingNeeded
        etichetta: Necessità scavo
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 20
        descrizione: mntRequestedSignage
        etichetta: Necessità di posa segnaletica
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 21
        descrizione: mntAuthorizationAuthorityDate
        etichetta: Data/ora rilascio autorizzazione Ente
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 22
        descrizione: defectFoundDate
        etichetta: Data esecuzione registrazione sopralluogo con guasto riscontrato
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 23
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 24
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 25
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 26
        descrizione: mntDefectGeoRef
        etichetta: Georeferenziazione punto guasto
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
  - stato_iniziale: ANALISI_AMBIENTALE_E_DETTAGLIO_RISCONTRO_GUASTO
    action: SOSPENSIONE_SOPRALLUOGO
    stato_finale: SOPRALLUOGO_SOSPESO
    gruppi:
      - ADMIN
      - AT
      - REMOTE_AUTOMATION
      - ROOT
    tdta:
      - posizione: 0
        descrizione: surveySuspensionReason
        etichetta: Causale sospensione
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 Intervento da programmare,02 Mancata concessione per cantierizzazione,03 Rimozione cantiere su richiesta Terzi (PP.SS. - Ente - altro),04 Fattore metereologico (nebbia - neve - altro),05 Rischio operativo (Presenza AT/MT - frane - incendio  - altro),06 Infrastruttura non immediatamente accessibile,07 altro'
      - posizione: 1
        descrizione: surveySuspensionNotes
        etichetta: Note sospensione
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: mntDefectGeoRef
        etichetta: Georeferenziazione punto guasto
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
      - posizione: 6
        descrizione: surveySuspensionDate
        etichetta: Data esecuzione sospensione sopralluogo
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
  - stato_iniziale: ATTESA_CHIUSURA_LAVORO
    action: CHIUSURA
    stato_finale: GUASTO_NON_RISCONTRATO
    gruppi:
      - ADMIN
      - REMOTE_AUTOMATION
      - ROOT
  - stato_iniziale: ATTESA_RIPARAZIONE
    action: AGGIORNAMENTO_DATI_ANALISI_AMBIENTALE_E_DETTAGLIO_RISCONTRO_GUASTO
    stato_finale: ATTESA_RIPARAZIONE
    gruppi:
      - ADMIN
      - AT
      - REMOTE_AUTOMATION
      - ROOT
    tdta:
      - posizione: 0
        descrizione: mntRelocation
        etichetta: Ri-localizzazione SIRTI
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: reason
        etichetta: Causa Danneggiamento
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Danni da Terzi - Ignoti,Danni da Terzi - Noti,Degrado Materiali,Difetto di installazione,Roditori,Incendio,Cedimento sede di posa,Eventi naturali,Doloso'
      - posizione: 2
        descrizione: mntRequestedAssistance
        etichetta: Assistenza richiesta
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: mntDamagingRegistry
        etichetta: Anagrafica danneggiante
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 4
        descrizione: corrRegionDefectSeat
        etichetta: Regione sede guasto
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: "Abruzzo,Basilicata,Calabria,Campania,Emilia Romagna,Friuli Venezia Giulia,Lazio,Liguria,Lombardia,Marche,Molise,Piemonte,Puglia,Sardegna,Sicilia,Toscana,Trentino Alto Adige,Umbria,Val d'Aosta,Veneto"
      - posizione: 5
        descrizione: mntPurchaserRegistry
        etichetta: Anagrafica committente
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 6
        descrizione: corrRegionDefectManagement
        etichetta: Gestore sede guasto
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Anas,Regione,Provincia,Comune,Astral,PRIVATO,SALT,AdF,ASPI,ATIVA,SATAP,MILANO - SERRAVALLE,ATMB,ATS,SAV,A21 AUTOVIE PADANE,A22 AUTOSTRADA DEL BRENNERO,A4 AUTOSTRADA BS-PD,AUTOVIE VENETE,VENETO STRADE,CAV,FVG STRADE,EDR,A15 AUTOSTRADA DELLA CISA,ENTE EUR,SAT,STRADA DEI PARCHI,TANGENZIALE DI NAPOLI,CAS'
      - posizione: 7
        descrizione: mntDefectSeat
        etichetta: Sede guasto
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'AUTOSTRADALE,STRADALE'
      - posizione: 8
        descrizione: mntDefectStreet
        etichetta: 'Località guasto - strada'
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 9
        descrizione: mntDefectKm
        etichetta: 'Località guasto - chilometrica'
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 10
        descrizione: mntRGRCablesQuantity
        etichetta: Quantità di cavi RGR presenti
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: '0,1,2,3,4,5,6,7,8,9,10'
      - posizione: 11
        descrizione: mntRGRDamagedCablesQ
        etichetta: Quantità di cavi RGR danneggiati
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: '0,1,2,3,4,5,6,7,8,9,10'
      - posizione: 12
        descrizione: mntASPIOthCablesQuantity
        etichetta: Quantità di cavi ASPI non a canone presenti
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: '0,1,2,3,4,5,6,7,8,9,10'
      - posizione: 13
        descrizione: mntASPIOthDamagedCablesQ
        etichetta: Quantità di cavi ASPI non a canone danneggiati
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: '0,1,2,3,4,5,6,7,8,9,10'
      - posizione: 14
        descrizione: mntOthersCablesQuantity
        etichetta: Quantità di cavi di altri gestori presenti
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: '0,1,2,3,4,5,6,7,8,9,10'
      - posizione: 15
        descrizione: mntDamagedInfrastructure
        etichetta: Infrastruttura danneggiata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 16
        descrizione: damagedInfrastructureType
        etichetta: Tipo di infrastruttura danneggiata
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Tritubo in trincea,Tritubo in zancatura,Palificazione,Canalizzazione,Galleria PP.SS.,No-dig,altro'
      - posizione: 17
        descrizione: diggingNeeded
        etichetta: Necessità scavo
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 18
        descrizione: mntRequestedSignage
        etichetta: Necessità di posa segnaletica
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 19
        descrizione: mntAuthorizationAuthorityDate
        etichetta: Data/ora rilascio autorizzazione Ente
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 20
        descrizione: dataUpdateDate
        etichetta: Data esecuzione aggiornamento dati
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 21
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 22
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 23
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 24
        descrizione: mntDefectGeoRef
        etichetta: Georeferenziazione punto guasto
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
  - stato_iniziale: ATTESA_RIPARAZIONE
    action: ANNULLAMENTO
    stato_finale: ATTESA_CHIUSURA_LAVORO_RIPARAZIONE
    gruppi:
      - ADMIN
      - AT
      - REMOTE_AUTOMATION
      - ROOT
    tdta:
      - posizione: 0
        descrizione: technicalCancelDate
        etichetta: Data Annullamento Tecnico
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: technicalCancelReason
        etichetta: Causale Annullamento Tecnico
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Errata apertura,Guasto duplicato'
      - posizione: 2
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: mntDefectGeoRef
        etichetta: Georeferenziazione punto guasto
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
  - stato_iniziale: ATTESA_RIPARAZIONE
    action: INIZIO_RIPARAZIONE
    stato_finale: RIPARAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - REMOTE_AUTOMATION
      - ROOT
    tdta:
      - posizione: 0
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: mntDefectGeoRef
        etichetta: Georeferenziazione punto guasto
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
      - posizione: 4
        descrizione: repairStartDate
        etichetta: Data esecuzione inizio riparazione
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
  - stato_iniziale: SOPRALLUOGO_SOSPESO
    action: RIPRESA_SOPRALLUOGO
    stato_finale: ANALISI_AMBIENTALE_E_DETTAGLIO_RISCONTRO_GUASTO
    gruppi:
      - ADMIN
      - AT
      - REMOTE_AUTOMATION
      - ROOT
    tdta:
      - posizione: 0
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: mntDefectGeoRef
        etichetta: Georeferenziazione punto guasto
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
      - posizione: 4
        descrizione: surveyRestartDate
        etichetta: Data esecuzione ripresa sopralluogo
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
  - stato_iniziale: ATTESA_CHIUSURA_LAVORO_RIPARAZIONE
    action: CHIUSURA
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: RIPARAZIONE_IN_CORSO
    action: AGGIORNAMENTO_DATI_ANALISI_AMBIENTALE_E_DETTAGLIO_RISCONTRO_GUASTO
    stato_finale: RIPARAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - REMOTE_AUTOMATION
      - ROOT
    tdta:
      - posizione: 0
        descrizione: mntRelocation
        etichetta: Ri-localizzazione SIRTI
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: reason
        etichetta: Causa Danneggiamento
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Danni da Terzi - Ignoti,Danni da Terzi - Noti,Degrado Materiali,Difetto di installazione,Roditori,Incendio,Cedimento sede di posa,Eventi naturali,Doloso'
      - posizione: 2
        descrizione: mntRequestedAssistance
        etichetta: Assistenza richiesta
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: mntDamagingRegistry
        etichetta: Anagrafica danneggiante
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 4
        descrizione: corrRegionDefectSeat
        etichetta: Regione sede guasto
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: "Abruzzo,Basilicata,Calabria,Campania,Emilia Romagna,Friuli Venezia Giulia,Lazio,Liguria,Lombardia,Marche,Molise,Piemonte,Puglia,Sardegna,Sicilia,Toscana,Trentino Alto Adige,Umbria,Val d'Aosta,Veneto"
      - posizione: 5
        descrizione: mntPurchaserRegistry
        etichetta: Anagrafica committente
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 6
        descrizione: corrRegionDefectManagement
        etichetta: Gestore sede guasto
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Anas,Regione,Provincia,Comune,Astral,PRIVATO,SALT,AdF,ASPI,ATIVA,SATAP,MILANO - SERRAVALLE,ATMB,ATS,SAV,A21 AUTOVIE PADANE,A22 AUTOSTRADA DEL BRENNERO,A4 AUTOSTRADA BS-PD,AUTOVIE VENETE,VENETO STRADE,CAV,FVG STRADE,EDR,A15 AUTOSTRADA DELLA CISA,ENTE EUR,SAT,STRADA DEI PARCHI,TANGENZIALE DI NAPOLI,CAS'
      - posizione: 7
        descrizione: mntDefectSeat
        etichetta: Sede guasto
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'AUTOSTRADALE,STRADALE'
      - posizione: 8
        descrizione: mntDefectStreet
        etichetta: 'Località guasto - strada'
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 9
        descrizione: mntDefectKm
        etichetta: 'Località guasto - chilometrica'
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 10
        descrizione: mntRGRCablesQuantity
        etichetta: Quantità di cavi RGR presenti
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: '0,1,2,3,4,5,6,7,8,9,10'
      - posizione: 11
        descrizione: mntRGRDamagedCablesQ
        etichetta: Quantità di cavi RGR danneggiati
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: '0,1,2,3,4,5,6,7,8,9,10'
      - posizione: 12
        descrizione: mntASPIOthCablesQuantity
        etichetta: Quantità di cavi ASPI non a canone presenti
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: '0,1,2,3,4,5,6,7,8,9,10'
      - posizione: 13
        descrizione: mntASPIOthDamagedCablesQ
        etichetta: Quantità di cavi ASPI non a canone danneggiati
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: '0,1,2,3,4,5,6,7,8,9,10'
      - posizione: 14
        descrizione: mntOthersCablesQuantity
        etichetta: Quantità di cavi di altri gestori presenti
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: '0,1,2,3,4,5,6,7,8,9,10'
      - posizione: 15
        descrizione: mntDamagedInfrastructure
        etichetta: Infrastruttura danneggiata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 16
        descrizione: damagedInfrastructureType
        etichetta: Tipo di infrastruttura danneggiata
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Tritubo in trincea,Tritubo in zancatura,Palificazione,Canalizzazione,Galleria PP.SS.,No-dig,altro'
      - posizione: 17
        descrizione: diggingNeeded
        etichetta: Necessità scavo
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 18
        descrizione: mntRequestedSignage
        etichetta: Necessità di posa segnaletica
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 19
        descrizione: mntAuthorizationAuthorityDate
        etichetta: Data/ora rilascio autorizzazione Ente
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 20
        descrizione: dataUpdateDate
        etichetta: Data esecuzione aggiornamento dati
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 21
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 22
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 23
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 24
        descrizione: mntDefectGeoRef
        etichetta: Georeferenziazione punto guasto
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
  - stato_iniziale: RIPARAZIONE_IN_CORSO
    action: ANNULLAMENTO
    stato_finale: ATTESA_CHIUSURA_LAVORO_RIPARAZIONE
    gruppi:
      - ADMIN
      - AT
      - REMOTE_AUTOMATION
      - ROOT
    tdta:
      - posizione: 0
        descrizione: technicalCancelDate
        etichetta: Data Annullamento Tecnico
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: technicalCancelReason
        etichetta: Causale Annullamento Tecnico
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Errata apertura,Guasto duplicato'
      - posizione: 2
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: mntDefectGeoRef
        etichetta: Georeferenziazione punto guasto
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
  - stato_iniziale: RIPARAZIONE_IN_CORSO
    action: REGISTRAZIONE_DATI_RIPARAZIONE
    stato_finale: ATTESA_CHIUSURA_LAVORO_RIPARAZIONE
    gruppi:
      - ADMIN
      - AT
      - REMOTE_AUTOMATION
      - ROOT
    tdta:
      - posizione: 0
        descrizione: mntOperationType
        etichetta: Tipo di Riparazione
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'PROVVISORIA,DEFINITIVA'
      - posizione: 1
        descrizione: mntRGROthersCableLaying
        etichetta: Posa cavo RGR
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: mntRGROthersCableLayingMeters
        etichetta: Metri posa cavo RGR
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: mntASPICableLaying
        etichetta: Posa cavo ASPI non a canone
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: mntASPICableLayingMeters
        etichetta: Metri posa cavo ASPI non a canone
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: mntRTNDisserviceCablesQuantity
        etichetta: Numero fibre disservite su cavi RTN
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: mntRTNCableLayingMeters
        etichetta: Metri posa cavo RTN
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: mntRGROthersDisserviceCablesQ
        etichetta: Numero fibre disservite su cavi RGR
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144'
      - posizione: 8
        descrizione: mntASPIOthDisserviceCablesQ
        etichetta: Numero fibre disservite su cavi ASPI non a canone
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144'
      - posizione: 9
        descrizione: junctionStartDate
        etichetta: Data e ora inizio giunzione
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
      - posizione: 10
        descrizione: junctionEndDate
        etichetta: Data e ora fine giunzione
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
      - posizione: 11
        descrizione: clientFeedbackDate
        etichetta: Data e ora riscontro cliente risalita circuiti
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
      - posizione: 12
        descrizione: mntEndWorkDate
        etichetta: Data ultimazione lavori
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
      - posizione: 13
        descrizione: workDuration
        etichetta: Durata intervento (hh:mi)
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 14
        descrizione: cableList
        etichetta: Elenco cavi
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 15
        descrizione: elapsed
        etichetta: Ore
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 16
        descrizione: mntRepairNotes
        etichetta: Descrizione dell'evento di guasto e dell'intervento di riparazione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Questo valore verrà inserito nel documento MR8
      - posizione: 17
        descrizione: companyManager
        etichetta: Firma impresa
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Questo valore verrà inserito nel documento MR8
      - posizione: 18
        descrizione: repairDataDate
        etichetta: Data esecuzione registrazione dati riparazione
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 19
        descrizione: mntASPIDisserviceCablesQty
        etichetta: Di cui fibre disservite su cavi ASPI a canone
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 20
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 21
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 22
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 23
        descrizione: mntDefectGeoRef
        etichetta: Georeferenziazione punto guasto
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
  - stato_iniziale: RIPARAZIONE_IN_CORSO
    action: SOSPENSIONE_RIPARAZIONE
    stato_finale: RIPARAZIONE_SOSPESA
    gruppi:
      - ADMIN
      - AT
      - REMOTE_AUTOMATION
      - ROOT
    tdta:
      - posizione: 0
        descrizione: repairSuspensionReason
        etichetta: Causale sospensione
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 Intervento da programmare,02 Mancata concessione per cantierizzazione,03 Rimozione cantiere su richiesta Terzi (PP.SS. - Ente - altro),04 Fattore metereologico (nebbia - neve - altro),05 Rischio operativo (Presenza AT/MT - frane - incendio  - altro),06 Infrastruttura non immediatamente accessibile,07 altro'
      - posizione: 1
        descrizione: repairSuspensionNotes
        etichetta: Note sospensione
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: mntDefectGeoRef
        etichetta: Georeferenziazione punto guasto
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
      - posizione: 6
        descrizione: repairSuspensionDate
        etichetta: Data esecuzione sospensione riparazione
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
  - stato_iniziale: RIPARAZIONE_SOSPESA
    action: RIPRESA_RIPARAZIONE
    stato_finale: RIPARAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - REMOTE_AUTOMATION
      - ROOT
    tdta:
      - posizione: 0
        descrizione: fieldServiceId
        etichetta: Id servizio da campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: fieldServiceUsername
        etichetta: Utenza servizio su campo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: fieldServiceTimestamp
        etichetta: Timestamp servizio su campo
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: mntDefectGeoRef
        etichetta: Georeferenziazione punto guasto
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00000,0.000000"
          }
      - posizione: 4
        descrizione: repairRestartDate
        etichetta: Data esecuzione ripresa riparazione
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "INPUT_FORMAT": "datetime"
          }
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
