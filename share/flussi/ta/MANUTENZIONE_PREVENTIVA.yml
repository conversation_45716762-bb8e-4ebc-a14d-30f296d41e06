---
tipo_attivita: MANUTENZIONE_PREVENTIVA
tipi_sistema:
  - MANUTENZIONE_PREVENTIVA
transizioni:
  - stato_iniziale: START
    action: APERTURA
    stato_finale: APERTA
    gruppi:
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 1
        descrizione: oa
        etichetta: OA
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: exRO
        etichetta: Ex RO
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: mntRegion
        etichetta: Regione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: cableList
        etichetta: Elenco cavi
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: mntWorkType
        etichetta: Tipo Lavoro
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 Visita Tracciato,04 Sorveglianza Lavori,05 Misure,06 Riepilogo Trimestre'
      - posizione: 6
        descrizione: trackType
        etichetta: Tipo Tracciato
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'DO Dorsale,DV Derivazione'
      - posizione: 7
        descrizione: workSeat
        etichetta: Sede
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Stradale,Autostradale'
        valore_default: ''
        valore_predefinito: ''
        suggerimento: 'Compilare per Tipo Lavoro: Sorveglianza Lavori'
      - posizione: 8
        descrizione: surveillanceType
        etichetta: Tipo Sorveglianza
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'RI Richiesta,VP Visite Periodiche'
        valore_default: ''
        valore_predefinito: ''
        suggerimento: 'Compilare obbligatoriamente per Tipo Lavoro: Sorveglianza Lavori'
      - posizione: 9
        descrizione: mntAddress
        etichetta: Località/Indirizzo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: 'Compilare obbligatoriamente per Tipo Lavoro: Sorveglianza Lavori'
      - posizione: 10
        descrizione: firmName
        etichetta: Nome Ditta
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: 'Compilare obbligatoriamente per Tipo Lavoro: Sorveglianza Lavori'
      - posizione: 11
        descrizione: onBehalf
        etichetta: Operante per conto di
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: "Autostrade per l'Italia,Strada dei Parchi SpA,Milano Serravalle-Milano Tangenziali,Autostrada Pedemontana Lombarda,ATIVA,SATAP,Autostrada Asti-Cuneo (ASTM),Società Italiana per il Traforo Autostradale del Frejus,Società Autostrade Valdostane,Autostrada dei Fiori,Società Autostrada Ligure Toscana,Società Autostrada Tirrenica,Brebemi S.p.A.,Autovie Venete,Autovia Padana,Autostrada del Brennero S.p.A.,Autostrada Brescia - Verona - Vicenza - Padova S.p.A.,Società Autostrade Meridionali,Consorzio per le Autostrade Siciliane,ANAS,ALTRO"
        valore_default: ''
        valore_predefinito: ''
        suggerimento: 'Compilare per Tipo Lavoro: Sorveglianza Lavori'
      - posizione: 12
        descrizione: onBehalf2
        etichetta: Operante per conto di (ALTRO)
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 13
        descrizione: frequency
        etichetta: Periodicità Attività
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Gennaio,Febbraio,Marzo,Aprile,Maggio,Giugno,Luglio,Agosto,Settembre,Ottobre,Novembre,Dicembre,Non Richiesta,Annuale'
        valore_default: ''
        valore_predefinito: ''
        suggerimento: 'Compilare per Tipo Lavoro: Visita Tracciato, Misure, Riepilogo Trimestre'
      - posizione: 14
        descrizione: year
        etichetta: Anno
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: 'Compilare per Tipo Lavoro: Visita Tracciato, Misure, Riepilogo Trimestre'
      - posizione: 15
        descrizione: quarter
        etichetta: Trimestre
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '1,2,3,4'
        valore_default: ''
        valore_predefinito: ''
        suggerimento: 'Compilare per Tipo Lavoro: Visita Tracciato, Misure, Riepilogo Trimestre'
      - posizione: 16
        descrizione: measureType
        etichetta: Tipologia Di Misura
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 Mis. Isolamento Guaina,02 Ver. Res. Imp. Terra,03 Mis. Isol. Condut.'
        valore_default: ''
        valore_predefinito: ''
        suggerimento: 'Compilare per Tipo Lavoro: Misure'
      - posizione: 17
        descrizione: defect
        etichetta: Anomalia
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 No,02 Si'
        valore_default: ''
        valore_predefinito: ''
        suggerimento: 'Compilare per Tipo Lavoro: Misure'
      - posizione: 18
        descrizione: AOA
        etichetta: AOA
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 19
        descrizione: __DTC_MOD2
        etichetta: Modello 233969
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 20
        descrizione: __DTC_MR1
        etichetta: MR1
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 21
        descrizione: __DTC_VAR
        etichetta: DOC. VARIE
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_LAVORO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: updateType
        etichetta: Tipo Aggiornamento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: workSuspensionReason
        etichetta: Motivo sospensione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: APERTURA_LAVORO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_LAVORO_KO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_LAVORO_OK
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: endWorkDate
        etichetta: Data fine lavori
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: wStartDate1
        etichetta: '1° Lavoro - Data apertura'
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: wEndDate1
        etichetta: '1° Lavoro - Data chiusura'
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: wAnomaliesDangers1
        etichetta: '1° Lavoro - Anomalie / Pericoli rilevati'
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: wThirdPartiesSystemSignalling1
        etichetta: '1° Lavoro - Segnalazione impianto a terzi'
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: wNotes1
        etichetta: '1° Lavoro - Osservazioni'
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: wStartDate2
        etichetta: '2° Lavoro - Data apertura'
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 8
        descrizione: wEndDate2
        etichetta: '2° Lavoro - Data chiusura'
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: wAnomaliesDangers2
        etichetta: '2° Lavoro - Anomalie / Pericoli rilevati'
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 10
        descrizione: wThirdPartiesSystemSignalling2
        etichetta: '2° Lavoro - Segnalazione impianto a terzi'
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 11
        descrizione: wNotes2
        etichetta: '2° Lavoro - Osservazioni'
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 12
        descrizione: wStartDate
        etichetta: 'Lavoro - Data apertura'
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 13
        descrizione: wAnomaliesDangers
        etichetta: Anomalie / Pericoli rilevati
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
            "type": "const",
            "value": "NESSUNA"
          }
  - stato_iniziale: ___ANY_STATUS___
    action: NUOVA_DOCUMENTAZIONE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: APERTA
    action: PRESA_IN_CARICO
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: currentUser
        etichetta: Login assistente tecnico assegnatario
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: currentUserName
        etichetta: Assistente tecnico assegnatario
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancelDate
        etichetta: Data Annullamento
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: defect
        etichetta: Anomalia
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 No,02 Si'
        valore_default: ''
        valore_predefinito: ''
        suggerimento: 'Compilare per Tipo Lavoro: Misure'
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: ASSEGNAZIONE_AD_ALTRO_AT
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: currentUser
        etichetta: Login assistente tecnico assegnatario
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: currentUserName
        etichetta: Assistente tecnico assegnatario
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: FINE_LAVORI
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: elapsed
        etichetta: Ore
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: activitySummary
        etichetta: Riep. Attività
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: defect
        etichetta: Anomalia
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 No,02 Si'
        valore_default: ''
        valore_predefinito: ''
        suggerimento: 'Compilare per Tipo Lavoro: Misure'
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: FINE_LAVORI_VISITA_TRACCIATO
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: elapsed
        etichetta: Ore
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: activitySummary
        etichetta: Riep. Attività
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: defect
        etichetta: Anomalia
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 No,02 Si'
        valore_default: ''
        valore_predefinito: ''
        suggerimento: 'Compilare per Tipo Lavoro: Misure'
      - posizione: 3
        descrizione: cableTypeList
        etichetta: Cavo
        tipo_ui: 'LIST  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'FO,DM,COAX'
      - posizione: 4
        descrizione: companyManager
        etichetta: Firma impresa
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Questo valore verrà inserito nel documento MR8
      - posizione: 5
        descrizione: documentDate
        etichetta: Data documento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: PRESA_IN_CARICO
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: currentUser
        etichetta: Login assistente tecnico assegnatario
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: currentUserName
        etichetta: Assistente tecnico assegnatario
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: SOSPENSIONE
    stato_finale: SOSPESA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: defect
        etichetta: Anomalia
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 No,02 Si'
        valore_default: ''
        valore_predefinito: ''
        suggerimento: 'Compilare per Tipo Lavoro: Misure'
  - stato_iniziale: SOSPESA
    action: RIPRESA
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
