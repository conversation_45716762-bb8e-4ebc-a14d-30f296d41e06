---
tipo_attivita: MANUTENZIONE_STRAORDINARIA
tipi_sistema:
  - MANUTENZIONE_STRAORDINARIA
transizioni:
  - stato_iniziale: START
    action: APERTURA
    stato_finale: APERTA
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - TIM_OP
    tdta:
      - posizione: 0
        descrizione: oa
        etichetta: OA
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: exRO
        etichetta: Ex RO
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: mntRegion
        etichetta: Regione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 4
        descrizione: defectLocation
        etichetta: Loc. Guasto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: defectStreet
        etichetta: Via Guasto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: mntActivityType
        etichetta: Tipo Attività
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Guasto fo non disservito,Guasto DM,Infrastruttura'
      - posizione: 7
        descrizione: mntDamagedInfrastructure
        etichetta: Infrastruttura danneggiata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 8
        descrizione: mntOperationType
        etichetta: Tipo di Riparazione
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'PROVVISORIA,DEFINITIVA'
      - posizione: 9
        descrizione: extrReporter
        etichetta: Segnalato da
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: "TIM,Altri,Attivita' di preventiva"
      - posizione: 10
        descrizione: mntPrevType
        etichetta: Tipo Preventiva
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'ISP MENS APP TIM,REV ANN APP TIM'
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Compilare se e solo se "Tipo attività" è uguale a "Infrastruttura" e "Segnalato da" è uguale a "Attivita' di Preventiva"
      - posizione: 11
        descrizione: reason
        etichetta: Causa Danneggiamento
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Danni da Terzi - Ignoti,Danni da Terzi - Noti,Degrado Materiali,Difetto di installazione,Roditori,Incendio,Cedimento sede di posa,Eventi naturali,Doloso'
      - posizione: 12
        descrizione: mntCentralA
        etichetta: Centrale A
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 13
        descrizione: mntCentralB
        etichetta: Centrale B
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 14
        descrizione: defectDate
        etichetta: Data riscontro anomalia
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 15
        descrizione: AOA
        etichetta: AOA
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 16
        descrizione: cableList
        etichetta: Elenco cavi
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 17
        descrizione: defectNumber
        etichetta: Anomalia N°
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: mntNote
        etichetta: Note
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 19
        descrizione: customerRTNEmail
        etichetta: E-mail riferimento RTN TIM
        tipo_ui: 'EMAIL '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 20
        descrizione: __DTC_OTD
        etichetta: Traccia OTDR
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 21
        descrizione: __DTC_SSG
        etichetta: Scheda di segnalazione guasto
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 22
        descrizione: __DTC_VAR
        etichetta: DOC. VARIE
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 23
        descrizione: __DTC_MR8
        etichetta: Documento MR8
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 24
        descrizione: __DTC_FGU
        etichetta: Foto Guasto
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 25
        descrizione: __DTC_PDFP
        etichetta: PDF Preventiva
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 26
        descrizione: __DTC_FGEO
        etichetta: Foto Georeferenziata
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_LAVORO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: updateType
        etichetta: Tipo Aggiornamento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: workSuspensionReason
        etichetta: Motivo sospensione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: APERTURA_LAVORO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: externalSequence
        etichetta: Progressivo external sequence
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_LAVORO_KO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_LAVORO_OK
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: endWorkDate
        etichetta: Data fine lavori
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: NUOVA_DOCUMENTAZIONE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: APERTA
    action: PRESA_IN_CARICO
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: currentUser
        etichetta: Login assistente tecnico assegnatario
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: currentUserName
        etichetta: Assistente tecnico assegnatario
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancelDate
        etichetta: Data Annullamento
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: ASSEGNAZIONE_AD_ALTRO_AT
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: currentUser
        etichetta: Login assistente tecnico assegnatario
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: currentUserName
        etichetta: Assistente tecnico assegnatario
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: FINE_LAVORI
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: elapsed
        etichetta: Ore
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: systemType
        etichetta: Tipo impianto
        tipo_ui: 'LIST  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'COAX,F.O.,TERMINAZIONI,INFRASTRUTTURA,T.F.O.,TERRE & PALI'
      - posizione: 2
        descrizione: extrDamagedSystemPart
        etichetta: Parte impianto danneggiata
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: "Pozzetti e/o camerette distrutto -> sostituzione completa,Pozzetti e/o camerette distrutto -> sostituzione anelli,Pozzetti e/o camerette danneggiato -> sostituzione anelli,Pozzetti e/o camerette danneggiato -> stuccatura/tamponatura,Coperchi e telai di pozzetti e camerette distrutto -> sostituzione coperchio,Coperchi e telai di pozzetti e camerette distrutto -> sostituzione coperchio + telaio,Coperchi e telai di pozzetti e camerette danneggiato -> sostituzione coperchio,Coperchi e telai di pozzetti e camerette danneggiato -> sostituzione coperchio + telaio,Targhette di identificazione assenti -> messa in opera targhetta,Targhette di identificazione da sostituire -> sostituita targhetta,Targhette di identificazione da riposizionare -> ripristino targhetta,staffe e canalette su muri e viadotti staffe ossidate -> sostituzione staffe,staffe e canalette su muri e viadotti staffe staccate -> ripristino staffe ,staffe e canalette su muri e viadotti cassetta aperta -> ripristino cassetta,staffe e canalette su muri e viadotti cassetta deteriorata -> sostituzione cassetta,staffe e canalette su muri e viadotti staffe e cassette danneggiate intero tratto -> sostituzione completa,Tritubi posati all'interno di canalette o cavidotti fuoriuscita tritubo -> ripristino sede tritubo,Tritubi posati all'interno di canalette o cavidotti tritubo danneggiato (singolo monotubo) -> ripristino parziale tritubo,Tritubi posati all'interno di canalette o cavidotti tritubo danneggiato (completo) -> ripristino totale tritubo,Bauletti e manufatti distrutto -> rifacimento completo,Bauletti e manufatti danneggiato -> ripristino bauletto,Recinzioni / scalette / corrimano / barriere distrutte -> sostituzione completa,Recinzioni / scalette / corrimano / barriere danneggiate -> ripristino esistente,punti di rigenerazione / armadi esterni e relativi impianti di terra e di protezione distrutte -> sostituzione completa,punti di rigenerazione / armadi esterni e relativi impianti di terra e di protezione danneggiate -> ripristino esistente,Le FS/CO e altre parti di impianto DM 7bcp - 10bcp distrutto -> sostituzione completa,Le FS/CO e altre parti di impianto DM 7bcp - 10bcp danneggiato -> ripristino esistente"
      - posizione: 3
        descrizione: companyManager
        etichetta: Firma impresa
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Questo valore verrà inserito nel documento MR8
      - posizione: 4
        descrizione: maintExtrStartWorkDay
        etichetta: Data inizio intervento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: maintExtrStartWorkHour
        etichetta: Ora inizio intervento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: maintExtrEndDefectDay
        etichetta: Data fine disservizio
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: maintExtrEndDefectHour
        etichetta: Ora fine disservizio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: maintExtrEndWorkDay
        etichetta: Data fine lavoro
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 9
        descrizione: maintExtrEndWorkHour
        etichetta: Ora fine lavoro
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 10
        descrizione: maintExtrPermanentlyRepaired
        etichetta: Riparazione eseguita in modo definitivo
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 11
        descrizione: maintExtrActionForDrainage
        etichetta: Azioni da intraprendere per eseguire la bonifica
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: PRESA_IN_CARICO
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: currentUser
        etichetta: Login assistente tecnico assegnatario
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: currentUserName
        etichetta: Assistente tecnico assegnatario
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: SOSPENSIONE
    stato_finale: SOSPESA
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: SOSPESA
    action: RIPRESA
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
