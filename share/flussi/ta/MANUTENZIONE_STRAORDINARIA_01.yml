---
tipo_attivita: MANUTENZIONE_STRAORDINARIA_01
tipi_sistema:
  - MANUTENZIONE_STRAORDINARIA_01
transizioni:
  - stato_iniziale: START
    action: APERTURA
    stato_finale: APERTA
    gruppi:
      - ADMIN
      - AT03
      - ROOT
    tdta:
      - posizione: 0
        descrizione: customerId
        etichetta: Id Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: contractId
        etichetta: Id Contratto
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: maintenanceDescription
        etichetta: Descrizione Manutenzione
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 4
        descrizione: route
        etichetta: Tratta
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "TYPE": "URI",
            "URI": "../../../customers/FASTWEB/so/assets/routes/lookup/route",
            "LAYOUT": "AUTOCOMPLETE",
            "CONTEXT": "Config",
            "ADD_FROM_AUTOCOMPLETE_ONLY": true
          }
      - posizione: 5
        descrizione: routeId
        etichetta: Identificativo tratta
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: routeAssetId
        etichetta: Identificativo asset tratta
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: routeLocationId
        etichetta: Sede
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: routeArea
        etichetta: Area
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 9
        descrizione: routeRegion
        etichetta: Regione tratta
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 10
        descrizione: extraordinaryWorkType
        etichetta: Tipo lavoro
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Sorveglianza Lavori Terzi,Sorveglianza Lavori con Picchettatura,Sfalcio,Bonifica Testate Palermo-Mazara'
      - posizione: 11
        descrizione: extraordinarySurveillanceType
        etichetta: Tipo Sorveglianza
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Richiesta da Sirti,Visite Periodiche,Ispezioni Periodiche,Richiesta dal Cliente'
      - posizione: 12
        descrizione: surveillanceLocation
        etichetta: Località/Indirizzo sorveglianza
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 13
        descrizione: extraordinaryCompanyName
        etichetta: Nome ditta
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 14
        descrizione: workingOnBehalfOf
        etichetta: Operante per conto di
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: "Autostrade per l'Italia,Strada dei Parchi SpA,Società Autostrada Tirrenica,Società Autostrade Meridionali,Consorzio per le Autostrade Siciliane,ANAS"
      - posizione: 15
        descrizione: activityFrequency
        etichetta: Periodicità Attività
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Gennaio,Febbraio,Marzo,Aprile,Maggio,Giugno,Luglio,Agosto,Settembre,Ottobre,Novembre,Dicembre,Non richiesta,Annuale'
      - posizione: 16
        descrizione: extraordinaryYear
        etichetta: Anno
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 17
        descrizione: quaterly
        etichetta: Trimestre
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '1,2,3,4'
      - posizione: 18
        descrizione: __DTC_ODL
        etichetta: ORDINE DI LAVORO
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 19
        descrizione: __DTC_SL
        etichetta: SORVEGLIANZA LAVORI
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 20
        descrizione: __DTC_ALT
        etichetta: ALTRO
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_LAVORO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: updateType
        etichetta: Tipo Aggiornamento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: workSuspensionReason
        etichetta: Motivo sospensione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: APERTURA_LAVORO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: maker
        etichetta: Esecutore
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: teamId
        etichetta: Identificativo squadra
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: teamName
        etichetta: Nome squadra
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: subContractCode
        etichetta: Identificativo fornitore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: subContractName
        etichetta: Nome fornitore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_LAVORO_KO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_LAVORO_OK
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: NUOVA_DOCUMENTAZIONE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT03
      - ROOT
  - stato_iniziale: APERTA
    action: ASSEGNAZIONE_AD_ALTRO_AT
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT03
      - ROOT
  - stato_iniziale: APERTA
    action: PRESA_IN_CARICO
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT03
      - ROOT
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT03
      - ROOT
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: FINE_MANUTENZIONE
    stato_finale: FINE_MANUTENZIONE
    gruppi:
      - ADMIN
      - AT03
      - ROOT
    tdta:
      - posizione: 0
        descrizione: maintenanceStartWorkDate
        etichetta: Data inizio lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: maintenanceEndWorkDate
        etichetta: Data fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 2
        descrizione: hours
        etichetta: Ore
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: maintenanceAnnotation
        etichetta: Note/Segnalazioni varie
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 4
        descrizione: maintenanceAnomaly
        etichetta: Anomalia
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Si,No'
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: SOSPENSIONE_MANUTENZIONE
    stato_finale: SOSPESA
    gruppi:
      - ADMIN
      - AT03
      - ROOT
  - stato_iniziale: SOSPESA
    action: RIPRESA_MANUTENZIONE
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT03
      - ROOT
