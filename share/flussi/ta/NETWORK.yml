---
tipo_attivita: NETWORK
tipi_sistema:
  - NETWORK
transizioni:
  - stato_iniziale: START
    action: APERTURA
    stato_finale: APERTA
    gruppi:
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalWorkTypeId
        etichetta: Tipo Avviso / Tipo Intervento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: externalMacroActivityId
        etichetta: Macroattivita
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: externalMacroActivity
        etichetta: Descrizione Macroattivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: externalActivityId
        etichetta: Attivita
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: externalAccountingType
        etichetta: Tipo contabilità
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: networkId
        etichetta: Id network
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: propositionNumber
        etichetta: N. Preventivo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 8
        descrizione: networkDesc
        etichetta: Descrizione Network
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: networkType
        etichetta: Tipo Network
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 10
        descrizione: networkTypeDesc
        etichetta: Descrizione tipo Network
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 11
        descrizione: centralId
        etichetta: ID centrale
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 12
        descrizione: central
        etichetta: Descrizione centrale
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 13
        descrizione: centralGeoLocation
        etichetta: Locazione centrale
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 14
        descrizione: RO
        etichetta: RO
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 15
        descrizione: AOL
        etichetta: AOL
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 16
        descrizione: estimatedCost
        etichetta: Preventivo costi
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 17
        descrizione: customerWBE
        etichetta: WBE cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 18
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 19
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 20
        descrizione: workOrderId
        etichetta: Ordine cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 21
        descrizione: workOrder
        etichetta: Descrizione ordine d'acquisto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 22
        descrizione: customerTakeInChargeDate
        etichetta: Data presa in carico Network
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 23
        descrizione: customerAssignmentDate
        etichetta: Data assegnazione
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 24
        descrizione: customerAssignmentDateActual
        etichetta: Data assegnazione corrente
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 25
        descrizione: forecastStartDate
        etichetta: Data prevista IL
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 26
        descrizione: forecastEndDate
        etichetta: Data prevista FL
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 27
        descrizione: forecastEndDateActual
        etichetta: Data prevista FL corrente
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 28
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 29
        descrizione: safetyAccountableName
        etichetta: Nome responsabile dei rischi
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
            "type": "const",
            "value": "Sig.ra MILENA"
          }
      - posizione: 30
        descrizione: safetyAccountableSurname
        etichetta: Cognome responsabile dei rischi
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
            "type": "const",
            "value": "CUSINATO"
          }
      - posizione: 31
        descrizione: supplierId
        etichetta: Identificativo fornitore
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
            "type": "const",
            "value": "**********"
          }
      - posizione: 32
        descrizione: supplierDescription
        etichetta: 'Descrizione Fornitore '
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
            "type": "const",
            "value": "SIRTI TELCO INFRASTRUCTURES S.P.A"
          }
      - posizione: 33
        descrizione: ameliaId
        etichetta: ID_AMELIA
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 34
        descrizione: OI
        etichetta: O.I.
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 35
        descrizione: site
        etichetta: Sede
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 36
        descrizione: cartographicAlphanumericUpdate
        etichetta: Aggiornamento cartografico/alfanumerico/dinamyc
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: 'Nessuno,Alfanumerico,Cartografico,Dynamic,Alfanumerico/Cartografico,Alfanumerico/Dynamic,Cartografico/Dynamic,Alfanumerico/Cartografico/Dynamic'
      - posizione: 37
        descrizione: commessa
        etichetta: Commessa
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 38
        descrizione: descrizioneCommessa
        etichetta: Descrizione commessa
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 39
        descrizione: networkPurpose
        etichetta: Scopo Network
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: 'Gestione armadio,Primaria,Rimborso Cosap,Ripristino,Altri'
      - posizione: 40
        descrizione: WBELevel3
        etichetta: WBE 3 Livello
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 41
        descrizione: __DTC_AL3
        etichetta: 'DOC. ALLEGATO 3: CONSEGNA DOC RL'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 42
        descrizione: __DTC_APC
        etichetta: DOC. PROGETTO
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 43
        descrizione: __DTC_CAR
        etichetta: DOC. ASBUILT DI CANTIERE
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 44
        descrizione: __DTC_CIL
        etichetta: DOC. CERTIFICATO INIZIO LAVORI
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 45
        descrizione: __DTC_CKL
        etichetta: DOC. KIT LIST
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 46
        descrizione: __DTC_CPS
        etichetta: DOC. PIANO OPERATIVO SICUREZZA
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 47
        descrizione: __DTC_CPT
        etichetta: DOC. PIANO TERRITORIALE
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 48
        descrizione: __DTC_CUI
        etichetta: DOC. CERTIFICATO ULTIMAZIONE IMPIANTO
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 49
        descrizione: __DTC_DCO
        etichetta: DOC. DOCUMENTAZIONE COLLAUDO
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 50
        descrizione: __DTC_DOF
        etichetta: DOC. DOCUMENTAZIONE FOTOGRAFICA
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 51
        descrizione: __DTC_DRN
        etichetta: DOC. DOCUMENTO RIMODULAZIONE NETWORK
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 52
        descrizione: __DTC_MA4
        etichetta: DOC. MODELLO A4
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 53
        descrizione: __DTC_MAI
        etichetta: DOC. MODELLO AREA DI INFLUENZA
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 54
        descrizione: __DTC_PRE
        etichetta: DOC. PREVENTIVO
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 55
        descrizione: __DTC_VAR
        etichetta: DOC. VARIE
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 56
        descrizione: __DTC_DYN
        etichetta: DOC. DYNAMIC
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 57
        descrizione: __DTC_NGN
        etichetta: DOC. NGNEER
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 58
        descrizione: __DTC_UNI
        etichetta: DOC. UNICA-RA
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 59
        descrizione: __DTC_PRV
        etichetta: DOC. Apertura preventivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 60
        descrizione: __DTC_RFR
        etichetta: DOC. Rimodulazione preventivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 61
        descrizione: __DTC_ANL
        etichetta: DOC. Annullamento preventivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 62
        descrizione: __DTC_CSL
        etichetta: DOC. CIL Sospension lavori
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 63
        descrizione: __DTC_CRL
        etichetta: DOC. CIL Ripresa lavori
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 64
        descrizione: __DTC_CVP
        etichetta: DOC. CIL Variazione persone
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 65
        descrizione: __DTC_CVD
        etichetta: DOC. CIL Variazione durata
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 66
        descrizione: __DTC_CUIp
        etichetta: DOC. CUI Parziale
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 67
        descrizione: __DTC_RCIp
        etichetta: DOC. RCI Parziale
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 68
        descrizione: __DTC_RCI
        etichetta: DOC. RCI Finale
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 69
        descrizione: __DTC_ALF
        etichetta: DOC. ALFANUMERICO
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 70
        descrizione: __DTC_CTG
        etichetta: DOC. CARTOGRAFICO
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 71
        descrizione: __DTC_SDIS
        etichetta: 'SpeedArk - Distinta Materiali'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 72
        descrizione: __DTC_SDOC
        etichetta: 'SpeedArk - Documentazione'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 73
        descrizione: accountingThreshold
        etichetta: Valore Max Produzione
        tipo_ui: CUREUR
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
            "type": "const",
            "value": "0"
          }
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNA_AT
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - NUM
      - PM
      - ROOT
    tdta:
      - posizione: 0
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNA_CENTRO_LAVORO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workingGroupCodeChoice
        etichetta: Scelta Centro Lavoro
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_LAVORO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: updateType
        etichetta: Tipo Aggiornamento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: workSuspensionReason
        etichetta: Motivo sospensione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_PERMESSO_BUILDING
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: privatePermitsActivityId
        etichetta: Id Attivita Permesso Privato
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: updateType
        etichetta: Tipo Aggiornamento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: privatePermitsSuspensionReason
        etichetta: Motivo sospensione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_PERMESSO_LAVORI
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: publicPermitsActivityId
        etichetta: Id Attivita Permesso Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: updateType
        etichetta: Tipo Aggiornamento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: publicPermitsSuspensionReason
        etichetta: Motivo sospensione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_SOGLIA_PRODUZIONE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - PM
      - ROOT
    tdta:
      - posizione: 0
        descrizione: accountingThreshold
        etichetta: Valore Max Produzione
        tipo_ui: CUREUR
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
            "type": "const",
            "value": "0"
          }
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNA_ODS
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: ODS
        etichetta: OdS
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Ordine Di Servizio
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNA_ODS_COLLEGATO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalIdLinked
        etichetta: Id intervento Collegato
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Id intervento Statistico
      - posizione: 1
        descrizione: ODSLinked
        etichetta: ODS Collegato
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Ordine Di Servizio Linked
  - stato_iniziale: ___ANY_STATUS___
    action: ANNULLAMENTO_NON_GESTITO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancellationDate
        etichetta: Data annullamento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: APERTURA_LAVORO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: maker
        etichetta: Esecutore
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: teamId
        etichetta: Identificativo squadra
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: teamName
        etichetta: Nome squadra
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: subContractCode
        etichetta: Identificativo fornitore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: subContractName
        etichetta: Nome fornitore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: externalSequence
        etichetta: Progressivo external sequence
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: APERTURA_PERMESSO_BUILDING
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: maker
        etichetta: Esecutore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: teamId
        etichetta: Identificativo squadra
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: teamName
        etichetta: Nome squadra
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: subContractCode
        etichetta: Identificativo fornitore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: subContractName
        etichetta: Nome fornitore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: privatePermitsActivityId
        etichetta: Id Attivita Permesso Privato
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: APERTURA_PERMESSO_LAVORI
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: maker
        etichetta: Esecutore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: teamId
        etichetta: Identificativo squadra
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: teamName
        etichetta: Nome squadra
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: subContractCode
        etichetta: Identificativo fornitore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: subContractName
        etichetta: Nome fornitore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: publicPermitsActivityId
        etichetta: Id Attivita Permesso Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_LAVORO_KO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_LAVORO_OK
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_PERMESSO_BUILDING_KO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: privatePermitsActivityId
        etichetta: Id Attivita Permesso Privato
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_PERMESSO_BUILDING_OK
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: privatePermitsActivityId
        etichetta: Id Attivita Permesso Privato
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_PERMESSO_LAVORI_KO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: publicPermitsActivityId
        etichetta: Id Attivita Permesso Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_PERMESSO_LAVORI_OK
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: publicPermitsActivityId
        etichetta: Id Attivita Permesso Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: NUOVA_DOCUMENTAZIONE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
  - stato_iniziale: ___ANY_STATUS___
    action: RAPPORTO_LAVORI_ACCETTATO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: worksReportStatus
        etichetta: Stato rapporto lavori
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'INVIATO,ACCETTATO,RESPINTO,CONSUNTIVATO'
  - stato_iniziale: ___ANY_STATUS___
    action: RAPPORTO_LAVORI_INVIO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: worksReportSendDate
        etichetta: Data invio rapporto lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: worksReportStatus
        etichetta: Stato rapporto lavori
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'INVIATO,ACCETTATO,RESPINTO,CONSUNTIVATO'
  - stato_iniziale: ___ANY_STATUS___
    action: RAPPORTO_LAVORI_PARZIALE_ACCETTATO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: partialWorksReportStatus
        etichetta: Stato rapporto lavori parziale
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'INVIATO,ACCETTATO,RESPINTO,CONSUNTIVATO'
  - stato_iniziale: ___ANY_STATUS___
    action: RAPPORTO_LAVORI_PARZIALE_INVIO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: partialWorksReportSendDate
        etichetta: Data invio rapporto lavori parziale
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: partialWorksReportStatus
        etichetta: Stato rapporto lavori parziale
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'INVIATO,ACCETTATO,RESPINTO,CONSUNTIVATO'
  - stato_iniziale: ___ANY_STATUS___
    action: RAPPORTO_LAVORI_PARZIALE_RESPINTO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: partialWorksReportStatus
        etichetta: Stato rapporto lavori parziale
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'INVIATO,ACCETTATO,RESPINTO,CONSUNTIVATO'
  - stato_iniziale: ___ANY_STATUS___
    action: RAPPORTO_LAVORI_PARZIALE_RICEZIONE_CONSUNTIVAZIONE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: partialWorksReportStatus
        etichetta: Stato rapporto lavori parziale
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'INVIATO,ACCETTATO,RESPINTO,CONSUNTIVATO'
  - stato_iniziale: ___ANY_STATUS___
    action: RAPPORTO_LAVORI_RESPINTO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: worksReportStatus
        etichetta: Stato rapporto lavori
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'INVIATO,ACCETTATO,RESPINTO,CONSUNTIVATO'
  - stato_iniziale: ___ANY_STATUS___
    action: RAPPORTO_LAVORI_RICEZIONE_CONSUNTIVAZIONE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: worksReportStatus
        etichetta: Stato rapporto lavori
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'INVIATO,ACCETTATO,RESPINTO,CONSUNTIVATO'
      - posizione: 1
        descrizione: worksReportCLCounter
        etichetta: Giorni per accettazione rapporto lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: RIMODULAZIONE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: forecastEndDateActual
        etichetta: Data prevista FL corrente
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: previousForecastEndDate
        etichetta: Data prevista FL precedente
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: customerAssignmentDateActual
        etichetta: Data assegnazione corrente
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: previousCustomerAssignmentDate
        etichetta: Data assegnazione precedente
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: rescheduleCount
        etichetta: N. rimodulazioni
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: SOCIETARIZZAZIONE_CLIENTE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: networkId
        etichetta: Id network
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: networkIdOld
        etichetta: Id network precedente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: workOrderId
        etichetta: Ordine cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: workOrderIdOld
        etichetta: Ordine cliente precedente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: WBELevel3
        etichetta: WBE 3 Livello
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: WBELevel3Old
        etichetta: WBE 3 Livello precedente
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: customerWBE
        etichetta: WBE cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: customerWBEOld
        etichetta: WBE cliente precedente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 8
        descrizione: buyer
        etichetta: Identificativo committente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: buyerOld
        etichetta: Identificativo committente precedente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 10
        descrizione: buyerDesc
        etichetta: Committente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 11
        descrizione: buyerDescOld
        etichetta: Committente precedente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 12
        descrizione: businessProject
        etichetta: Contratto cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 13
        descrizione: businessProjectOld
        etichetta: Contratto cliente precedente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 14
        descrizione: propositionNumber
        etichetta: N. Preventivo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 15
        descrizione: propositionNumberOld
        etichetta: N. Preventivo precedente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 16
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 17
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: ODS
        etichetta: OdS
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Ordine Di Servizio
  - stato_iniziale: APERTA
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancellationDate
        etichetta: Data annullamento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
  - stato_iniziale: APERTA
    action: ANOMALIA
    stato_finale: ANOMALIA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: APERTA
    action: ASSEGNAZIONE_AD_ALTRO_AT
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: APERTA
    action: ASSEGNAZIONE_AD_ALTRO_AT_FTTH
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: networkDesc
        etichetta: Descrizione Network
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 7
        descrizione: workOrder
        etichetta: Descrizione ordine d'acquisto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 8
        descrizione: networkPurpose
        etichetta: Scopo Network
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Gestione armadio,Primaria,Rimborso Cosap,Ripristino,Altri'
  - stato_iniziale: APERTA
    action: ASSEGNAZIONE_AD_ALTRO_AT_FTTH_PRIMARIA
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
      - posizione: 7
        descrizione: networkDesc
        etichetta: Descrizione Network
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 8
        descrizione: workOrder
        etichetta: Descrizione ordine d'acquisto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
  - stato_iniziale: APERTA
    action: ASSEGNAZIONE_AD_ALTRO_AT_NW
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: takeInChargeNote
        etichetta: Note presa in carico
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: networkRoute
        etichetta: Direttrice
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 8
        descrizione: workSite
        etichetta: Tipo cantiere
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 Autostradale,02 Stradale,03 Altro'
      - posizione: 9
        descrizione: securityPlan
        etichetta: Piano sicurezza
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 Ottenuti,02 Richiesti,03 Parziali,04 Non necessario,05 Da richiedere,06 Da valutare'
  - stato_iniziale: APERTA
    action: ASSEGNAZIONE_AD_ALTRO_AT_SITI_5G
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: networkSiteType
        etichetta: Tipologia Sito Network
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Primaria,Realizzazione sito,Ripristino,Realizzazione sito + Ripristino,Altro'
      - posizione: 7
        descrizione: siteIdList
        etichetta: Elenco siti
        tipo_ui: MOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "TYPE": "URI",
            "URI": "../../../customers/TIM/contracts/CREATION/so/sites/lookups/siteIdList",
            "LAYOUT": "AUTOCOMPLETE",
            "CONTEXT": "Config",
            "ADD_FROM_AUTOCOMPLETE_ONLY": true
          }
  - stato_iniziale: APERTA
    action: AVANZAMENTO
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: APERTA
    action: PRESA_IN_CARICO
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: APERTA
    action: PRESA_IN_CARICO_FTTH
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: networkDesc
        etichetta: Descrizione Network
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 7
        descrizione: workOrder
        etichetta: Descrizione ordine d'acquisto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 8
        descrizione: networkPurpose
        etichetta: Scopo Network
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Gestione armadio,Primaria,Rimborso Cosap,Ripristino,Altri'
  - stato_iniziale: APERTA
    action: PRESA_IN_CARICO_FTTH_PRIMARIA
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
      - posizione: 7
        descrizione: networkDesc
        etichetta: Descrizione Network
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 8
        descrizione: workOrder
        etichetta: Descrizione ordine d'acquisto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
  - stato_iniziale: APERTA
    action: PRESA_IN_CARICO_NW
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: takeInChargeNote
        etichetta: Note presa in carico
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: networkRoute
        etichetta: Direttrice
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 8
        descrizione: workSite
        etichetta: Tipo cantiere
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 Autostradale,02 Stradale,03 Altro'
      - posizione: 9
        descrizione: securityPlan
        etichetta: Piano sicurezza
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 Ottenuti,02 Richiesti,03 Parziali,04 Non necessario,05 Da richiedere,06 Da valutare'
  - stato_iniziale: APERTA
    action: PRESA_IN_CARICO_SITI_5G
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: networkSiteType
        etichetta: Tipologia Sito Network
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Primaria,Realizzazione sito,Ripristino,Realizzazione sito + Ripristino,Altro'
      - posizione: 7
        descrizione: siteIdList
        etichetta: Elenco siti
        tipo_ui: MOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "TYPE": "URI",
            "URI": "../../../customers/TIM/contracts/CREATION/so/sites/lookups/siteIdList",
            "LAYOUT": "AUTOCOMPLETE",
            "CONTEXT": "Config",
            "ADD_FROM_AUTOCOMPLETE_ONLY": true
          }
  - stato_iniziale: APERTA
    action: RICERCA_DOCUMENTAZIONE_PROGETTO
    stato_finale: ATTESA_DOCUMENTAZIONE_PROGETTO
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: ANOMALIA
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancellationDate
        etichetta: Data annullamento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
  - stato_iniziale: ANOMALIA
    action: ASSEGNAZIONE_AD_ALTRO_AT
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ANOMALIA
    action: ASSEGNAZIONE_AD_ALTRO_AT_FTTH
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: networkDesc
        etichetta: Descrizione Network
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 7
        descrizione: workOrder
        etichetta: Descrizione ordine d'acquisto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 8
        descrizione: networkPurpose
        etichetta: Scopo Network
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Gestione armadio,Primaria,Rimborso Cosap,Ripristino,Altri'
  - stato_iniziale: ANOMALIA
    action: ASSEGNAZIONE_AD_ALTRO_AT_FTTH_PRIMARIA
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
      - posizione: 7
        descrizione: networkDesc
        etichetta: Descrizione Network
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 8
        descrizione: workOrder
        etichetta: Descrizione ordine d'acquisto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
  - stato_iniziale: ANOMALIA
    action: ASSEGNAZIONE_AD_ALTRO_AT_NW
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: takeInChargeNote
        etichetta: Note presa in carico
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: networkRoute
        etichetta: Direttrice
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 8
        descrizione: workSite
        etichetta: Tipo cantiere
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 Autostradale,02 Stradale,03 Altro'
      - posizione: 9
        descrizione: securityPlan
        etichetta: Piano sicurezza
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 Ottenuti,02 Richiesti,03 Parziali,04 Non necessario,05 Da richiedere,06 Da valutare'
  - stato_iniziale: ANOMALIA
    action: ASSEGNAZIONE_AD_ALTRO_AT_SITI_5G
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: networkSiteType
        etichetta: Tipologia Sito Network
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Primaria,Realizzazione sito,Ripristino,Realizzazione sito + Ripristino,Altro'
      - posizione: 7
        descrizione: siteIdList
        etichetta: Elenco siti
        tipo_ui: MOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "TYPE": "URI",
            "URI": "../../../customers/TIM/contracts/CREATION/so/sites/lookups/siteIdList",
            "LAYOUT": "AUTOCOMPLETE",
            "CONTEXT": "Config",
            "ADD_FROM_AUTOCOMPLETE_ONLY": true
          }
  - stato_iniziale: ANOMALIA
    action: PRESA_IN_CARICO
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ANOMALIA
    action: PRESA_IN_CARICO_FTTH
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: networkDesc
        etichetta: Descrizione Network
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 7
        descrizione: workOrder
        etichetta: Descrizione ordine d'acquisto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 8
        descrizione: networkPurpose
        etichetta: Scopo Network
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Gestione armadio,Primaria,Rimborso Cosap,Ripristino,Altri'
  - stato_iniziale: ANOMALIA
    action: PRESA_IN_CARICO_FTTH_PRIMARIA
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
      - posizione: 7
        descrizione: networkDesc
        etichetta: Descrizione Network
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 8
        descrizione: workOrder
        etichetta: Descrizione ordine d'acquisto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
  - stato_iniziale: ANOMALIA
    action: PRESA_IN_CARICO_NW
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: takeInChargeNote
        etichetta: Note presa in carico
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: networkRoute
        etichetta: Direttrice
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 8
        descrizione: workSite
        etichetta: Tipo cantiere
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 Autostradale,02 Stradale,03 Altro'
      - posizione: 9
        descrizione: securityPlan
        etichetta: Piano sicurezza
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 Ottenuti,02 Richiesti,03 Parziali,04 Non necessario,05 Da richiedere,06 Da valutare'
  - stato_iniziale: ANOMALIA
    action: PRESA_IN_CARICO_SITI_5G
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: networkSiteType
        etichetta: Tipologia Sito Network
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Primaria,Realizzazione sito,Ripristino,Realizzazione sito + Ripristino,Altro'
      - posizione: 7
        descrizione: siteIdList
        etichetta: Elenco siti
        tipo_ui: MOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "TYPE": "URI",
            "URI": "../../../customers/TIM/contracts/CREATION/so/sites/lookups/siteIdList",
            "LAYOUT": "AUTOCOMPLETE",
            "CONTEXT": "Config",
            "ADD_FROM_AUTOCOMPLETE_ONLY": true
          }
  - stato_iniziale: DA_ESEGUIRE
    action: AGGIORNAMENTO_ARMADIO
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
  - stato_iniziale: DA_ESEGUIRE
    action: AGGIORNAMENTO_SITI_5G
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: networkSiteType
        etichetta: Tipologia Sito Network
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Primaria,Realizzazione sito,Ripristino,Realizzazione sito + Ripristino,Altro'
      - posizione: 1
        descrizione: siteIdList
        etichetta: Elenco siti
        tipo_ui: MOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "URI",
            "URI": "../../../customers/TIM/contracts/CREATION/so/sites/lookups/siteIdList",
            "LAYOUT": "AUTOCOMPLETE",
            "CONTEXT": "Config",
            "ADD_FROM_AUTOCOMPLETE_ONLY": true
          }
  - stato_iniziale: DA_ESEGUIRE
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancellationDate
        etichetta: Data annullamento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
  - stato_iniziale: DA_ESEGUIRE
    action: ASSEGNAZIONE_AUTOMATICA_LAVORO_AT
    stato_finale: ATTESA_APERTURA_LAVORO
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: DA_ESEGUIRE
    action: CHIUSURA_MNT_REG
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: DA_ESEGUIRE
    action: INIZIO_LAVORI
    stato_finale: ATTESA_CONFERMA_INIZIO_LAVORI
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 1
        descrizione: centralOf
        etichetta: Centrale di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"technicalSiteDesc\"]"
          }
      - posizione: 2
        descrizione: ftcName
        etichetta: Funzione territoriale competente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"central\"]"
          }
      - posizione: 3
        descrizione: technicalArea
        etichetta: Settore tecnico
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'ImpiantiCommutazione,ImpiantiTrasmissione,LavoriContoEsercizio,LavoriVari,ReteDistribuzioneCavi,RetiSpeciali,SistemiAlimentazioneCondizionamento,SistemiGestione'
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "ReteDistribuzioneCavi"
          }
      - posizione: 4
        descrizione: execProg
        etichetta: Progetto esecutivo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: execProgRev
        etichetta: Revisione progetto esecutivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 6
        descrizione: zeroCostActivity
        etichetta: Lavoro costo zero
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: zeroCostActivityDate
        etichetta: Data lavoro costo zero
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 8
        descrizione: activityNote
        etichetta: Descrizione lavori
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"networkDesc\"]"
          }
      - posizione: 9
        descrizione: versionDescription
        etichetta: Causale
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "PrimaComunicazione"
          }
      - posizione: 10
        descrizione: technology
        etichetta: Tecnica
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 - DWDM'
      - posizione: 11
        descrizione: startDate
        etichetta: Data inizio lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 12
        descrizione: presumedEndDate
        etichetta: Data presunta fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"forecastEndDateActual\"]"
          }
      - posizione: 13
        descrizione: odaNumber
        etichetta: ODA
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"workOrderId\"]"
          }
      - posizione: 14
        descrizione: subCode
        etichetta: Codice ditta sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 15
        descrizione: subDescription
        etichetta: Descrizione ditta sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 16
        descrizione: subAccountableName
        etichetta: Nome persona di riferimento sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 17
        descrizione: subAccountableSurname
        etichetta: Cognome persona di riferimento sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: subPhone
        etichetta: Telefono sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 19
        descrizione: subFax
        etichetta: Fax sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 20
        descrizione: subDate
        etichetta: Data decorrenza sub
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 21
        descrizione: testRequest
        etichetta: Validità CIL come richiesta collaudo
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 22
        descrizione: dateDocCIL
        etichetta: Data emissione CIL
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 23
        descrizione: progCIL
        etichetta: Nr. progressivo CIL
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 24
        descrizione: accountableSurname
        etichetta: Cognome responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: Y
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"lastName\"]"
          }
      - posizione: 25
        descrizione: accountableName
        etichetta: Nome responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: Y
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"firstName\"]"
          }
      - posizione: 26
        descrizione: accountablePhoneNumber
        etichetta: Telefono responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"mobilePhone\"]"
          }
      - posizione: 27
        descrizione: accountableDate
        etichetta: Data decorrenza responsabile attività
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 28
        descrizione: IOCDate
        etichetta: 'IOC Data inizio opere civili (IMPORTANTE: da valorizzare in presenza di opere civili)'
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 29
        descrizione: IOCAddress
        etichetta: 'IOC Via (IMPORTANTE: da valorizzare in presenza di opere civili)'
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 30
        descrizione: externalDocumentType
        etichetta: Tipo documento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 31
        descrizione: currCILDateDoc
        etichetta: Data emissione CIL Inizio Lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 32
        descrizione: currCILprog
        etichetta: Nr. progressivo CIL Inizio Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 33
        descrizione: currCILRespDate
        etichetta: Data Esito CIL Inizio Lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 34
        descrizione: currCILRespDocumentStatus
        etichetta: Esito CIL Inizio Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 35
        descrizione: currCILRespInfo
        etichetta: Descrizione esito CIL Inizio Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 36
        descrizione: currCILRespProg
        etichetta: Nr. progressivo esito CIL Inizio Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_DOCUMENTAZIONE_PROGETTO
    action: ASSEGNAZIONE_AD_ALTRO_AT
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - NUM
      - REGIONAL_MANAGER
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_DOCUMENTAZIONE_PROGETTO
    action: ASSEGNAZIONE_AD_ALTRO_AT_FTTH
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - NUM
      - REGIONAL_MANAGER
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: networkDesc
        etichetta: Descrizione Network
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 7
        descrizione: workOrder
        etichetta: Descrizione ordine d'acquisto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 8
        descrizione: networkPurpose
        etichetta: Scopo Network
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Gestione armadio,Primaria,Rimborso Cosap,Ripristino,Altri'
  - stato_iniziale: ATTESA_DOCUMENTAZIONE_PROGETTO
    action: ASSEGNAZIONE_AD_ALTRO_AT_FTTH_PRIMARIA
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
      - posizione: 7
        descrizione: networkDesc
        etichetta: Descrizione Network
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 8
        descrizione: workOrder
        etichetta: Descrizione ordine d'acquisto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
  - stato_iniziale: ATTESA_DOCUMENTAZIONE_PROGETTO
    action: ASSEGNAZIONE_AD_ALTRO_AT_SITI_5G
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: networkSiteType
        etichetta: Tipologia Sito Network
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Primaria,Realizzazione sito,Ripristino,Realizzazione sito + Ripristino,Altro'
      - posizione: 7
        descrizione: siteIdList
        etichetta: Elenco siti
        tipo_ui: MOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "TYPE": "URI",
            "URI": "../../../customers/TIM/contracts/CREATION/so/sites/lookups/siteIdList",
            "LAYOUT": "AUTOCOMPLETE",
            "CONTEXT": "Config",
            "ADD_FROM_AUTOCOMPLETE_ONLY": true
          }
  - stato_iniziale: ATTESA_DOCUMENTAZIONE_PROGETTO
    action: DOCUMENTAZIONE_PROGETTO_RECUPERATA
    stato_finale: ATTESA_PRESA_IN_CARICO
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: projectCode
        etichetta: Codice progetto
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ATTESA_DOCUMENTAZIONE_PROGETTO
    action: PRESA_IN_CARICO
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - NUM
      - REGIONAL_MANAGER
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_DOCUMENTAZIONE_PROGETTO
    action: PRESA_IN_CARICO_FTTH
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - NUM
      - REGIONAL_MANAGER
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: networkDesc
        etichetta: Descrizione Network
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 7
        descrizione: workOrder
        etichetta: Descrizione ordine d'acquisto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 8
        descrizione: networkPurpose
        etichetta: Scopo Network
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Gestione armadio,Primaria,Rimborso Cosap,Ripristino,Altri'
  - stato_iniziale: ATTESA_DOCUMENTAZIONE_PROGETTO
    action: PRESA_IN_CARICO_FTTH_PRIMARIA
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
      - posizione: 7
        descrizione: networkDesc
        etichetta: Descrizione Network
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 8
        descrizione: workOrder
        etichetta: Descrizione ordine d'acquisto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
  - stato_iniziale: ATTESA_DOCUMENTAZIONE_PROGETTO
    action: PRESA_IN_CARICO_SITI_5G
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: networkSiteType
        etichetta: Tipologia Sito Network
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Primaria,Realizzazione sito,Ripristino,Realizzazione sito + Ripristino,Altro'
      - posizione: 7
        descrizione: siteIdList
        etichetta: Elenco siti
        tipo_ui: MOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "TYPE": "URI",
            "URI": "../../../customers/TIM/contracts/CREATION/so/sites/lookups/siteIdList",
            "LAYOUT": "AUTOCOMPLETE",
            "CONTEXT": "Config",
            "ADD_FROM_AUTOCOMPLETE_ONLY": true
          }
  - stato_iniziale: ATTESA_DOCUMENTAZIONE_PROGETTO
    action: TIMEOUT_DOCUMENTAZIONE_PROGETTO
    stato_finale: ATTESA_PRESA_IN_CARICO
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: ATTESA_APERTURA_LAVORO
    action: APERTURA_LAVORI_OK
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: ATTESA_APERTURA_LAVORO
    action: ASSEGNAZIONE_AUTOMATICA_LAVORO
    stato_finale: ATTESA_APERTURA_LAVORO
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: subContractEmail1
        etichetta: Email 1 subappalto
        tipo_ui: 'EMAIL '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: subContractEmail2
        etichetta: Email 2 subappalto
        tipo_ui: 'EMAIL '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: teamEmail
        etichetta: Email squadra
        tipo_ui: 'EMAIL '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ATTESA_CONFERMA_INIZIO_LAVORI
    action: INIZIO_LAVORI_ACCETTATO
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: prog
        etichetta: Nr. progressivo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: responseDate
        etichetta: Data esito
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: documentStatus
        etichetta: Esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: info
        etichetta: Descrizione esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: externalDocumentStatus
        etichetta: Codice esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: currCILRespDate
        etichetta: Data Esito CIL Inizio Lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: currCILRespDocumentStatus
        etichetta: Esito CIL Inizio Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: currCILRespInfo
        etichetta: Descrizione esito CIL Inizio Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: currCILRespProg
        etichetta: Nr. progressivo esito CIL Inizio Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_CONFERMA_INIZIO_LAVORI
    action: INIZIO_LAVORI_RESPINTO
    stato_finale: INIZIO_LAVORI_RESPINTO
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: prog
        etichetta: Nr. progressivo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: responseDate
        etichetta: Data esito
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: documentStatus
        etichetta: Esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: info
        etichetta: Descrizione esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: externalDocumentStatus
        etichetta: Codice esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: currCILRespDate
        etichetta: Data Esito CIL Inizio Lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: currCILRespDocumentStatus
        etichetta: Esito CIL Inizio Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: currCILRespInfo
        etichetta: Descrizione esito CIL Inizio Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: currCILRespProg
        etichetta: Nr. progressivo esito CIL Inizio Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_PRESA_IN_CARICO
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancellationDate
        etichetta: Data annullamento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
  - stato_iniziale: ATTESA_PRESA_IN_CARICO
    action: ASSEGNAZIONE_AD_ALTRO_AT
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_PRESA_IN_CARICO
    action: ASSEGNAZIONE_AD_ALTRO_AT_FTTH
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: networkDesc
        etichetta: Descrizione Network
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 7
        descrizione: workOrder
        etichetta: Descrizione ordine d'acquisto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 8
        descrizione: networkPurpose
        etichetta: Scopo Network
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Gestione armadio,Primaria,Rimborso Cosap,Ripristino,Altri'
  - stato_iniziale: ATTESA_PRESA_IN_CARICO
    action: ASSEGNAZIONE_AD_ALTRO_AT_FTTH_PRIMARIA
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
      - posizione: 7
        descrizione: networkDesc
        etichetta: Descrizione Network
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 8
        descrizione: workOrder
        etichetta: Descrizione ordine d'acquisto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
  - stato_iniziale: ATTESA_PRESA_IN_CARICO
    action: ASSEGNAZIONE_AD_ALTRO_AT_SITI_5G
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: networkSiteType
        etichetta: Tipologia Sito Network
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Primaria,Realizzazione sito,Ripristino,Realizzazione sito + Ripristino,Altro'
      - posizione: 7
        descrizione: siteIdList
        etichetta: Elenco siti
        tipo_ui: MOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "TYPE": "URI",
            "URI": "../../../customers/TIM/contracts/CREATION/so/sites/lookups/siteIdList",
            "LAYOUT": "AUTOCOMPLETE",
            "CONTEXT": "Config",
            "ADD_FROM_AUTOCOMPLETE_ONLY": true
          }
  - stato_iniziale: ATTESA_PRESA_IN_CARICO
    action: ASSEGNAZIONE_AUTOMATICA_AT
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 1
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 2
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: Y
      - posizione: 3
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: primaryATEmail
        etichetta: AT primario email
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: primaryATUsername
        etichetta: AT primario username
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: primaryATFullname
        etichetta: AT primario nome
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: secondaryATFullname
        etichetta: AT secondario nome
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 8
        descrizione: secondaryATUsername
        etichetta: AT secondario username
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: secondaryATEmail
        etichetta: AT secondario email
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ATTESA_PRESA_IN_CARICO
    action: PRESA_IN_CARICO
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_PRESA_IN_CARICO
    action: PRESA_IN_CARICO_FTTH
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: networkDesc
        etichetta: Descrizione Network
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 7
        descrizione: workOrder
        etichetta: Descrizione ordine d'acquisto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 8
        descrizione: networkPurpose
        etichetta: Scopo Network
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Gestione armadio,Primaria,Rimborso Cosap,Ripristino,Altri'
  - stato_iniziale: ATTESA_PRESA_IN_CARICO
    action: PRESA_IN_CARICO_FTTH_PRIMARIA
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
      - posizione: 7
        descrizione: networkDesc
        etichetta: Descrizione Network
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 8
        descrizione: workOrder
        etichetta: Descrizione ordine d'acquisto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
  - stato_iniziale: ATTESA_PRESA_IN_CARICO
    action: PRESA_IN_CARICO_SITI_5G
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: externalActivity
        etichetta: Descrizione attivita
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: Y
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 2
        descrizione: customerTechnicalAssistantName
        etichetta: Assistente Tecnico Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 4
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: networkSiteType
        etichetta: Tipologia Sito Network
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Primaria,Realizzazione sito,Ripristino,Realizzazione sito + Ripristino,Altro'
      - posizione: 7
        descrizione: siteIdList
        etichetta: Elenco siti
        tipo_ui: MOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "TYPE": "URI",
            "URI": "../../../customers/TIM/contracts/CREATION/so/sites/lookups/siteIdList",
            "LAYOUT": "AUTOCOMPLETE",
            "CONTEXT": "Config",
            "ADD_FROM_AUTOCOMPLETE_ONLY": true
          }
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: AGGIORNAMENTO_ARMADIO
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: AGGIORNAMENTO_SITI_5G
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: networkSiteType
        etichetta: Tipologia Sito Network
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Primaria,Realizzazione sito,Ripristino,Realizzazione sito + Ripristino,Altro'
      - posizione: 1
        descrizione: siteIdList
        etichetta: Elenco siti
        tipo_ui: MOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "URI",
            "URI": "../../../customers/TIM/contracts/CREATION/so/sites/lookups/siteIdList",
            "LAYOUT": "AUTOCOMPLETE",
            "CONTEXT": "Config",
            "ADD_FROM_AUTOCOMPLETE_ONLY": true
          }
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: AGGIUNTA_DOCUMENTAZIONE
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancellationDate
        etichetta: Data annullamento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: COLLAUDO
    stato_finale: ATTESA_CONFERMA_COLLAUDO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dateDocRCI
        etichetta: Data emissione RCI
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: centralOf
        etichetta: Centrale di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"technicalSiteDesc\"]"
          }
      - posizione: 2
        descrizione: tdta0019
        etichetta: Realizzazione
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Parziale,Saldo,Totale'
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "Totale"
          }
      - posizione: 3
        descrizione: tdta0020
        etichetta: Codice progetto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"description\"]"
          }
      - posizione: 4
        descrizione: execProgRev
        etichetta: Revisione progetto esecutivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 5
        descrizione: activityNote
        etichetta: Descrizione lavori
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"networkDesc\"]"
          }
      - posizione: 6
        descrizione: testDate
        etichetta: Data collaudo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"endWorkDateStep\"]"
          }
      - posizione: 7
        descrizione: testTime
        etichetta: Ora collaudo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "00:00"
          }
      - posizione: 8
        descrizione: tdta0021
        etichetta: Telefono per collaudo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "+393486912457"
          }
      - posizione: 9
        descrizione: tdta0022
        etichetta: Fax per collaudo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 10
        descrizione: endDate
        etichetta: Data fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 11
        descrizione: tdta0023
        etichetta: Impianto / note
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 12
        descrizione: shipmentType
        etichetta: Tipo consegna
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 13
        descrizione: externalDocumentType
        etichetta: Tipo documento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 14
        descrizione: progRCI
        etichetta: Nr. progressivo RCI
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 15
        descrizione: currRCTDateDoc
        etichetta: Data emissione RCI Collaudo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 16
        descrizione: currRCTprog
        etichetta: Nr. progressivo RCI Collaudo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 17
        descrizione: currRCTRespDate
        etichetta: Data Esito RCI Collaudo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: currRCTRespDocumentStatus
        etichetta: Esito RCI Collaudo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 19
        descrizione: currRCTRespInfo
        etichetta: Descrizione esito RCI Collaudo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 20
        descrizione: currRCTRespProg
        etichetta: Nr. progressivo esito RCI Collaudo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: COLLAUDO_PARZIALE
    stato_finale: ATTESA_CONFERMA_COLLAUDO_PARZIALE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dateDocRCI
        etichetta: Data emissione RCI
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: centralOf
        etichetta: Centrale di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"technicalSiteDesc\"]"
          }
      - posizione: 2
        descrizione: tdta0019
        etichetta: Realizzazione
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: 'Parziale,Saldo,Totale'
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "Totale"
          }
      - posizione: 3
        descrizione: tdta0020
        etichetta: Codice progetto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"description\"]"
          }
      - posizione: 4
        descrizione: execProgRev
        etichetta: Revisione progetto esecutivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 5
        descrizione: activityNote
        etichetta: Descrizione lavori
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"networkDesc\"]"
          }
      - posizione: 6
        descrizione: testDate
        etichetta: Data collaudo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"endWorkDateStep\"]"
          }
      - posizione: 7
        descrizione: testTime
        etichetta: Ora collaudo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "00:00"
          }
      - posizione: 8
        descrizione: tdta0021
        etichetta: Telefono per collaudo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "+393486912457"
          }
      - posizione: 9
        descrizione: tdta0022
        etichetta: Fax per collaudo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 10
        descrizione: endDate
        etichetta: Data fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 11
        descrizione: tdta0023
        etichetta: Impianto / note
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 12
        descrizione: shipmentType
        etichetta: Tipo consegna
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 13
        descrizione: externalDocumentType
        etichetta: Tipo documento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 14
        descrizione: progRCI
        etichetta: Nr. progressivo RCI
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 15
        descrizione: currRCPDateDoc
        etichetta: Data emissione RCI Collaudo Parziale
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 16
        descrizione: currRCPprog
        etichetta: Nr. progressivo RCI Collaudo Parziale
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 17
        descrizione: currRCPRespDate
        etichetta: Data Esito RCI Collaudo Parziale
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: currRCPRespDocumentStatus
        etichetta: Esito RCI Collaudo Parziale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 19
        descrizione: currRCPRespInfo
        etichetta: Descrizione esito RCI Collaudo Parziale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 20
        descrizione: currRCPRespProg
        etichetta: Nr. progressivo esito RCI Collaudo Parziale
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: FINE_LAVORI
    stato_finale: ATTESA_CONFERMA_FINE_LAVORI
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: centralOf
        etichetta: Centrale di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"technicalSiteDesc\"]"
          }
      - posizione: 1
        descrizione: cluName
        etichetta: Centro di lavoro unificato di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"central\"]"
          }
      - posizione: 2
        descrizione: dateDocCUI
        etichetta: Data emissione CUI
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 3
        descrizione: endDate
        etichetta: Data fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 4
        descrizione: shipmentType
        etichetta: Tipo consegna
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: execProg
        etichetta: Progetto esecutivo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: execProgRev
        etichetta: Revisione progetto esecutivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 7
        descrizione: technology
        etichetta: Tecnica
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 - DWDM'
      - posizione: 8
        descrizione: particularNotes
        etichetta: Segnalazioni particolari
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: partyAtTest
        etichetta: Personale Telecom
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Presente,Non presente'
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "Non presente"
          }
      - posizione: 10
        descrizione: testDate
        etichetta: Data collaudo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"endWorkDateStep\"]"
          }
      - posizione: 11
        descrizione: advancementPercent
        etichetta: Percentuale avanzamento
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 12
        descrizione: odaNumber
        etichetta: ODA
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"workOrderId\"]"
          }
      - posizione: 13
        descrizione: moiValue
        etichetta: Importo manodopera (Euro)
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00"
          }
      - posizione: 14
        descrizione: fornitureValue
        etichetta: Importo fornitura (Euro)
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00"
          }
      - posizione: 15
        descrizione: productionValue
        etichetta: Importo produzione (Euro)
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 16
        descrizione: activityNote
        etichetta: Descrizione lavori
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"networkDesc\"]"
          }
      - posizione: 17
        descrizione: progCUI
        etichetta: Nr. progressivo CUI
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: tdta0001
        etichetta: Dichiarazione di conformità DLgs 37/2008
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 19
        descrizione: tdta0002
        etichetta: Rapportazione lavori / Riepilogo prestazioni
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 20
        descrizione: tdta0003
        etichetta: Formulario identificazione rifiuti
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 21
        descrizione: tdta0004
        etichetta: Materiale di scorta
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 22
        descrizione: tdta0005
        etichetta: Documentazione tecnica / manuali apparato
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 23
        descrizione: tdta0006
        etichetta: Norma/e di collaudo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 24
        descrizione: tdta0007
        etichetta: Verbale di collaudo "Stand Alone"
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 25
        descrizione: tdta0008
        etichetta: Verbali e/o Modelli di collaudo specifici
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 26
        descrizione: tdta0009
        etichetta: Rapporto prova (per i Sistemi di Alimentazione e CDZ)
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 27
        descrizione: tdta0010
        etichetta: Autocertificazione di collaudo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 28
        descrizione: docs
        etichetta: Documenti Network
        tipo_ui: 'JSON  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 29
        descrizione: tdta0012
        etichetta: 2 Copie di cartografia agg.
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 30
        descrizione: tdta0013
        etichetta: Fatt. relative a forn. locali
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 31
        descrizione: tdta0014
        etichetta: Misure E/O e/o P. o autocertificaz.
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 32
        descrizione: tdta0015
        etichetta: Cp. Stp "Riep. op. in eser"
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 33
        descrizione: tdta0016
        etichetta: Modello rete cablata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 34
        descrizione: tdta0017
        etichetta: Modello area di influenza
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 35
        descrizione: tdta0018
        etichetta: El. riep. pres e mat for.
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 36
        descrizione: sendWithoutPeople
        etichetta: Invia documento senza elenco persone accesso in centrale
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 37
        descrizione: people
        etichetta: Elenco delle persone per l'accesso
        tipo_ui: 'JSON  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 38
        descrizione: cartographicAlphanumericUpdate
        etichetta: Aggiornamento cartografico/alfanumerico/dinamyc
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Nessuno,Alfanumerico,Cartografico,Dynamic,Alfanumerico/Cartografico,Alfanumerico/Dynamic,Cartografico/Dynamic,Alfanumerico/Cartografico/Dynamic'
      - posizione: 39
        descrizione: externalDocumentType
        etichetta: Tipo documento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 40
        descrizione: currCUTDateDoc
        etichetta: Data emissione CUI Fine Lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 41
        descrizione: currCUTprog
        etichetta: Nr. progressivo CUI Fine Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 42
        descrizione: currCUTRespDate
        etichetta: Data Esito CUI Fine Lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 43
        descrizione: currCUTRespDocumentStatus
        etichetta: Esito CUI Fine Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 44
        descrizione: currCUTRespInfo
        etichetta: Descrizione esito CUI Fine Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 45
        descrizione: currCUTRespProg
        etichetta: Nr. progressivo esito CUI Fine Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: FINE_LAVORI_PARZIALE
    stato_finale: ATTESA_CONFERMA_FINE_LAVORI_PARZIALE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: centralOf
        etichetta: Centrale di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"technicalSiteDesc\"]"
          }
      - posizione: 1
        descrizione: cluName
        etichetta: Centro di lavoro unificato di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"central\"]"
          }
      - posizione: 2
        descrizione: dateDocCUI
        etichetta: Data emissione CUI
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 3
        descrizione: endDate
        etichetta: Data fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 4
        descrizione: shipmentType
        etichetta: Tipo consegna
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: execProg
        etichetta: Progetto esecutivo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: execProgRev
        etichetta: Revisione progetto esecutivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 7
        descrizione: technology
        etichetta: Tecnica
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 - DWDM'
      - posizione: 8
        descrizione: particularNotes
        etichetta: Segnalazioni particolari
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: partyAtTest
        etichetta: Personale Telecom
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Presente,Non presente'
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "Non presente"
          }
      - posizione: 10
        descrizione: testDate
        etichetta: Data collaudo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"endWorkDateStep\"]"
          }
      - posizione: 11
        descrizione: advancementPercent
        etichetta: Percentuale avanzamento
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 12
        descrizione: odaNumber
        etichetta: ODA
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"workOrderId\"]"
          }
      - posizione: 13
        descrizione: moiValue
        etichetta: Importo manodopera (Euro)
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00"
          }
      - posizione: 14
        descrizione: fornitureValue
        etichetta: Importo fornitura (Euro)
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00"
          }
      - posizione: 15
        descrizione: productionValue
        etichetta: Importo produzione (Euro)
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 16
        descrizione: activityNote
        etichetta: Descrizione lavori
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"networkDesc\"]"
          }
      - posizione: 17
        descrizione: progCUI
        etichetta: Nr. progressivo CUI
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: tdta0001
        etichetta: Dichiarazione di conformità DLgs 37/2008
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 19
        descrizione: tdta0002
        etichetta: Rapportazione lavori / Riepilogo prestazioni
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 20
        descrizione: tdta0003
        etichetta: Formulario identificazione rifiuti
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 21
        descrizione: tdta0004
        etichetta: Materiale di scorta
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 22
        descrizione: tdta0005
        etichetta: Documentazione tecnica / manuali apparato
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 23
        descrizione: tdta0006
        etichetta: Norma/e di collaudo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 24
        descrizione: tdta0007
        etichetta: Verbale di collaudo "Stand Alone"
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 25
        descrizione: tdta0008
        etichetta: Verbali e/o Modelli di collaudo specifici
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 26
        descrizione: tdta0009
        etichetta: Rapporto prova (per i Sistemi di Alimentazione e CDZ)
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 27
        descrizione: tdta0010
        etichetta: Autocertificazione di collaudo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 28
        descrizione: docs
        etichetta: Documenti Network
        tipo_ui: 'JSON  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 29
        descrizione: tdta0012
        etichetta: 2 Copie di cartografia agg.
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 30
        descrizione: tdta0013
        etichetta: Fatt. relative a forn. locali
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 31
        descrizione: tdta0014
        etichetta: Misure E/O e/o P. o autocertificaz.
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 32
        descrizione: tdta0015
        etichetta: Cp. Stp "Riep. op. in eser"
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 33
        descrizione: tdta0016
        etichetta: Modello rete cablata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 34
        descrizione: tdta0017
        etichetta: Modello area di influenza
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 35
        descrizione: tdta0018
        etichetta: El. riep. pres e mat for.
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 36
        descrizione: people
        etichetta: Elenco delle persone per l'accesso
        tipo_ui: 'JSON  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 37
        descrizione: cartographicAlphanumericUpdate
        etichetta: Aggiornamento cartografico/alfanumerico/dinamyc
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Nessuno,Alfanumerico,Cartografico,Dynamic,Alfanumerico/Cartografico,Alfanumerico/Dynamic,Cartografico/Dynamic,Alfanumerico/Cartografico/Dynamic'
      - posizione: 38
        descrizione: externalDocumentType
        etichetta: Tipo documento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 39
        descrizione: currCUPDateDoc
        etichetta: Data emissione CUI Fine Lavori Parziale
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 40
        descrizione: currCUPprog
        etichetta: Nr. progressivo CUI Fine Lavori Parziale
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 41
        descrizione: currCUPRespDate
        etichetta: Data Esito CUI Fine Lavori Parziale
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 42
        descrizione: currCUPRespDocumentStatus
        etichetta: Esito CUI Fine Lavori Parziale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 43
        descrizione: currCUPRespInfo
        etichetta: Descrizione esito CUI Fine Lavori Parziale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 44
        descrizione: currCUPRespProg
        etichetta: Nr. progressivo esito CUI Fine Lavori Parziale
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: SOSPENSIONE
    stato_finale: ATTESA_CONFERMA_SOSPENSIONE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: centralOf
        etichetta: Centrale di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"technicalSiteDesc\"]"
          }
      - posizione: 1
        descrizione: ftcName
        etichetta: Funzione territoriale competente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"central\"]"
          }
      - posizione: 2
        descrizione: technicalArea
        etichetta: Settore tecnico
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'ImpiantiCommutazione,ImpiantiTrasmissione,LavoriContoEsercizio,LavoriVari,ReteDistribuzioneCavi,RetiSpeciali,SistemiAlimentazioneCondizionamento,SistemiGestione'
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "ReteDistribuzioneCavi"
          }
      - posizione: 3
        descrizione: execProg
        etichetta: Progetto esecutivo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: execProgRev
        etichetta: Revisione progetto esecutivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 5
        descrizione: zeroCostActivity
        etichetta: Lavoro costo zero
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: zeroCostActivityDate
        etichetta: Data lavoro costo zero
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: activityNote
        etichetta: Descrizione lavori
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"networkDesc\"]"
          }
      - posizione: 8
        descrizione: suspensionReason
        etichetta: Motivo sospensione
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'ERRATA CONSISTENZA,MANCANZA MATERIALI,MANC. PREDISP. PRIV,PERMESSI COMUNE,PERMESSI ENTI,PERMESSI PRIVATI,TUBAZ.TI NON PRATICAB.,MANCA ALIMENTAZIONE,MANCA INSTRADAMENTO, MANCA PROGETTO RETE,VARIE)'
      - posizione: 9
        descrizione: versionDescription
        etichetta: Causale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "PrimaComunicazione"
          }
      - posizione: 10
        descrizione: technology
        etichetta: Tecnica
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 - DWDM'
      - posizione: 11
        descrizione: startDate
        etichetta: Data inizio lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 12
        descrizione: presumedEndDate
        etichetta: Data presunta fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"forecastEndDateActual\"]"
          }
      - posizione: 13
        descrizione: odaNumber
        etichetta: ODA
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"workOrderId\"]"
          }
      - posizione: 14
        descrizione: subCode
        etichetta: Codice ditta sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 15
        descrizione: subDescription
        etichetta: Descrizione ditta sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 16
        descrizione: subAccountableName
        etichetta: Nome persona di riferimento sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 17
        descrizione: subAccountableSurname
        etichetta: Cognome persona di riferimento sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: subPhone
        etichetta: Telefono sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 19
        descrizione: subFax
        etichetta: Fax sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 20
        descrizione: subDate
        etichetta: Data decorrenza sub
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 21
        descrizione: testRequest
        etichetta: Validità CIL come richiesta collaudo
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 22
        descrizione: dateDocCIL
        etichetta: Data emissione CIL
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 23
        descrizione: progCIL
        etichetta: Nr. progressivo CIL
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 24
        descrizione: accountableSurname
        etichetta: Cognome responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: Y
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"lastName\"]"
          }
      - posizione: 25
        descrizione: accountableName
        etichetta: Nome responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: Y
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"firstName\"]"
          }
      - posizione: 26
        descrizione: accountablePhoneNumber
        etichetta: Telefono responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"mobilePhone\"]"
          }
      - posizione: 27
        descrizione: accountableDate
        etichetta: Data decorrenza responsabile attività
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 28
        descrizione: externalDocumentType
        etichetta: Tipo documento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 29
        descrizione: currCISDateDoc
        etichetta: Data emissione CIL Sospensione
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 30
        descrizione: currCISprog
        etichetta: Nr. progressivo CIL Sospensione
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 31
        descrizione: currCISRespDate
        etichetta: Data Esito CIL Sospensione
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 32
        descrizione: currCISRespDocumentStatus
        etichetta: Esito CIL Sospensione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 33
        descrizione: currCISRespInfo
        etichetta: Descrizione esito CIL Sospensione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 34
        descrizione: currCISRespProg
        etichetta: Nr. progressivo esito CIL Sospensione
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: VARIAZIONE_DURATA
    stato_finale: ATTESA_CONFERMA_VARIAZIONE_DURATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: centralOf
        etichetta: Centrale di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"technicalSiteDesc\"]"
          }
      - posizione: 1
        descrizione: ftcName
        etichetta: Funzione territoriale competente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"central\"]"
          }
      - posizione: 2
        descrizione: technicalArea
        etichetta: Settore tecnico
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'ImpiantiCommutazione,ImpiantiTrasmissione,LavoriContoEsercizio,LavoriVari,ReteDistribuzioneCavi,RetiSpeciali,SistemiAlimentazioneCondizionamento,SistemiGestione'
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "ReteDistribuzioneCavi"
          }
      - posizione: 3
        descrizione: execProg
        etichetta: Progetto esecutivo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: execProgRev
        etichetta: Revisione progetto esecutivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 5
        descrizione: zeroCostActivity
        etichetta: Lavoro costo zero
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: zeroCostActivityDate
        etichetta: Data lavoro costo zero
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: activityNote
        etichetta: Descrizione lavori
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"networkDesc\"]"
          }
      - posizione: 8
        descrizione: versionDescription
        etichetta: Causale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "PrimaComunicazione"
          }
      - posizione: 9
        descrizione: technology
        etichetta: Tecnica
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 - DWDM'
      - posizione: 10
        descrizione: startDate
        etichetta: Data inizio lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 11
        descrizione: presumedEndDate
        etichetta: Data presunta fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"forecastEndDateActual\"]"
          }
      - posizione: 12
        descrizione: odaNumber
        etichetta: ODA
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"workOrderId\"]"
          }
      - posizione: 13
        descrizione: subCode
        etichetta: Codice ditta sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 14
        descrizione: subDescription
        etichetta: Descrizione ditta sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 15
        descrizione: subAccountableName
        etichetta: Nome persona di riferimento sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 16
        descrizione: subAccountableSurname
        etichetta: Cognome persona di riferimento sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 17
        descrizione: subPhone
        etichetta: Telefono sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: subFax
        etichetta: Fax sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 19
        descrizione: subDate
        etichetta: Data decorrenza sub
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 20
        descrizione: testRequest
        etichetta: Validità CIL come richiesta collaudo
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 21
        descrizione: dateDocCIL
        etichetta: Data emissione CIL
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 22
        descrizione: progCIL
        etichetta: Nr. progressivo CIL
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 23
        descrizione: accountableSurname
        etichetta: Cognome responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: Y
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"lastName\"]"
          }
      - posizione: 24
        descrizione: accountableName
        etichetta: Nome responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: Y
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"firstName\"]"
          }
      - posizione: 25
        descrizione: accountablePhoneNumber
        etichetta: Telefono responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"mobilePhone\"]"
          }
      - posizione: 26
        descrizione: accountableDate
        etichetta: Data decorrenza responsabile attività
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 27
        descrizione: externalDocumentType
        etichetta: Tipo documento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 28
        descrizione: currCIDDateDoc
        etichetta: Data emissione CIL Variazione Durata
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 29
        descrizione: currCIDprog
        etichetta: Nr. progressivo CIL Variazione Durata
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 30
        descrizione: currCIDRespDate
        etichetta: Data Esito CIL Variazione Durata
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 31
        descrizione: currCIDRespDocumentStatus
        etichetta: Esito CIL Variazione Durata
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 32
        descrizione: currCIDRespInfo
        etichetta: Descrizione esito CIL Variazione Durata
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 33
        descrizione: currCIDRespProg
        etichetta: Nr. progressivo esito CIL Variazione Durata
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: VARIAZIONE_PERSONALE
    stato_finale: ATTESA_CONFERMA_VARIAZIONE_PERSONALE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: centralOf
        etichetta: Centrale di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"technicalSiteDesc\"]"
          }
      - posizione: 1
        descrizione: ftcName
        etichetta: Funzione territoriale competente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"central\"]"
          }
      - posizione: 2
        descrizione: technicalArea
        etichetta: Settore tecnico
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'ImpiantiCommutazione,ImpiantiTrasmissione,LavoriContoEsercizio,LavoriVari,ReteDistribuzioneCavi,RetiSpeciali,SistemiAlimentazioneCondizionamento,SistemiGestione'
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "ReteDistribuzioneCavi"
          }
      - posizione: 3
        descrizione: execProg
        etichetta: Progetto esecutivo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: execProgRev
        etichetta: Revisione progetto esecutivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 5
        descrizione: zeroCostActivity
        etichetta: Lavoro costo zero
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: zeroCostActivityDate
        etichetta: Data lavoro costo zero
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: activityNote
        etichetta: Descrizione lavori
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"networkDesc\"]"
          }
      - posizione: 8
        descrizione: versionDescription
        etichetta: Causale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "PrimaComunicazione"
          }
      - posizione: 9
        descrizione: technology
        etichetta: Tecnica
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 - DWDM'
      - posizione: 10
        descrizione: startDate
        etichetta: Data inizio lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 11
        descrizione: presumedEndDate
        etichetta: Data presunta fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"forecastEndDateActual\"]"
          }
      - posizione: 12
        descrizione: odaNumber
        etichetta: ODA
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"workOrderId\"]"
          }
      - posizione: 13
        descrizione: subCode
        etichetta: Codice ditta sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 14
        descrizione: subDescription
        etichetta: Descrizione ditta sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 15
        descrizione: subAccountableName
        etichetta: Nome persona di riferimento sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 16
        descrizione: subAccountableSurname
        etichetta: Cognome persona di riferimento sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 17
        descrizione: subPhone
        etichetta: Telefono sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: subFax
        etichetta: Fax sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 19
        descrizione: subDate
        etichetta: Data decorrenza sub
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 20
        descrizione: testRequest
        etichetta: Validità CIL come richiesta collaudo
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 21
        descrizione: dateDocCIL
        etichetta: Data emissione CIL
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 22
        descrizione: progCIL
        etichetta: Nr. progressivo CIL
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 23
        descrizione: accountableSurname
        etichetta: Cognome responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: Y
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"lastName\"]"
          }
      - posizione: 24
        descrizione: accountableName
        etichetta: Nome responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: Y
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"firstName\"]"
          }
      - posizione: 25
        descrizione: accountablePhoneNumber
        etichetta: Telefono responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"mobilePhone\"]"
          }
      - posizione: 26
        descrizione: accountableDate
        etichetta: Data decorrenza responsabile attività
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 27
        descrizione: IOCDate
        etichetta: 'IOC Data inizio opere civili (IMPORTANTE: da valorizzare in presenza di opere civili)'
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 28
        descrizione: IOCAddress
        etichetta: 'IOC Via (IMPORTANTE: da valorizzare in presenza di opere civili)'
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 29
        descrizione: externalDocumentType
        etichetta: Tipo documento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 30
        descrizione: currCIPDateDoc
        etichetta: Data emissione CIL Variazione Personale
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 31
        descrizione: currCIPprog
        etichetta: Nr. progressivo CIL Variazione Personale
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 32
        descrizione: currCIPRespDate
        etichetta: Data Esito CIL Variazione Personale
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 33
        descrizione: currCIPRespDocumentStatus
        etichetta: Esito CIL Variazione Personale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 34
        descrizione: currCIPRespInfo
        etichetta: Descrizione esito CIL Variazione Personale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 35
        descrizione: currCIPRespProg
        etichetta: Nr. progressivo esito CIL Variazione Personale
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: INIZIO_LAVORI_RESPINTO
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancellationDate
        etichetta: Data annullamento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
  - stato_iniziale: INIZIO_LAVORI_RESPINTO
    action: INIZIO_LAVORI
    stato_finale: ATTESA_CONFERMA_INIZIO_LAVORI
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: customerWorkingArea
        etichetta: Area di lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "customerWorkingAreaList",
            "TARGET": "customerWorkingAreaList"
          }
      - posizione: 1
        descrizione: centralOf
        etichetta: Centrale di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"technicalSiteDesc\"]"
          }
      - posizione: 2
        descrizione: ftcName
        etichetta: Funzione territoriale competente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"central\"]"
          }
      - posizione: 3
        descrizione: technicalArea
        etichetta: Settore tecnico
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'ImpiantiCommutazione,ImpiantiTrasmissione,LavoriContoEsercizio,LavoriVari,ReteDistribuzioneCavi,RetiSpeciali,SistemiAlimentazioneCondizionamento,SistemiGestione'
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "ReteDistribuzioneCavi"
          }
      - posizione: 4
        descrizione: execProg
        etichetta: Progetto esecutivo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: execProgRev
        etichetta: Revisione progetto esecutivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 6
        descrizione: zeroCostActivity
        etichetta: Lavoro costo zero
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: zeroCostActivityDate
        etichetta: Data lavoro costo zero
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 8
        descrizione: activityNote
        etichetta: Descrizione lavori
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"networkDesc\"]"
          }
      - posizione: 9
        descrizione: versionDescription
        etichetta: Causale
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "PrimaComunicazione"
          }
      - posizione: 10
        descrizione: technology
        etichetta: Tecnica
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 - DWDM'
      - posizione: 11
        descrizione: startDate
        etichetta: Data inizio lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 12
        descrizione: presumedEndDate
        etichetta: Data presunta fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"forecastEndDateActual\"]"
          }
      - posizione: 13
        descrizione: odaNumber
        etichetta: ODA
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"workOrderId\"]"
          }
      - posizione: 14
        descrizione: subCode
        etichetta: Codice ditta sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 15
        descrizione: subDescription
        etichetta: Descrizione ditta sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 16
        descrizione: subAccountableName
        etichetta: Nome persona di riferimento sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 17
        descrizione: subAccountableSurname
        etichetta: Cognome persona di riferimento sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: subPhone
        etichetta: Telefono sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 19
        descrizione: subFax
        etichetta: Fax sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 20
        descrizione: subDate
        etichetta: Data decorrenza sub
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 21
        descrizione: testRequest
        etichetta: Validità CIL come richiesta collaudo
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 22
        descrizione: dateDocCIL
        etichetta: Data emissione CIL
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 23
        descrizione: progCIL
        etichetta: Nr. progressivo CIL
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 24
        descrizione: accountableSurname
        etichetta: Cognome responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: Y
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"lastName\"]"
          }
      - posizione: 25
        descrizione: accountableName
        etichetta: Nome responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: Y
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"firstName\"]"
          }
      - posizione: 26
        descrizione: accountablePhoneNumber
        etichetta: Telefono responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"mobilePhone\"]"
          }
      - posizione: 27
        descrizione: accountableDate
        etichetta: Data decorrenza responsabile attività
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 28
        descrizione: IOCDate
        etichetta: 'IOC Data inizio opere civili (IMPORTANTE: da valorizzare in presenza di opere civili)'
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 29
        descrizione: IOCAddress
        etichetta: 'IOC Via (IMPORTANTE: da valorizzare in presenza di opere civili)'
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 30
        descrizione: externalDocumentType
        etichetta: Tipo documento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 31
        descrizione: currCILDateDoc
        etichetta: Data emissione CIL Inizio Lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 32
        descrizione: currCILprog
        etichetta: Nr. progressivo CIL Inizio Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 33
        descrizione: currCILRespDate
        etichetta: Data Esito CIL Inizio Lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 34
        descrizione: currCILRespDocumentStatus
        etichetta: Esito CIL Inizio Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 35
        descrizione: currCILRespInfo
        etichetta: Descrizione esito CIL Inizio Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 36
        descrizione: currCILRespProg
        etichetta: Nr. progressivo esito CIL Inizio Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_CONFERMA_COLLAUDO
    action: COLLAUDO_ACCETTATO
    stato_finale: COLLAUDO_EFFETTUATO
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: prog
        etichetta: Nr. progressivo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: responseDate
        etichetta: Data esito
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: documentStatus
        etichetta: Esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: info
        etichetta: Descrizione esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: externalDocumentStatus
        etichetta: Codice esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: currRCTRespDate
        etichetta: Data Esito RCI Collaudo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: currRCTRespDocumentStatus
        etichetta: Esito RCI Collaudo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: currRCTRespInfo
        etichetta: Descrizione esito RCI Collaudo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: currRCTRespProg
        etichetta: Nr. progressivo esito RCI Collaudo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_CONFERMA_COLLAUDO
    action: COLLAUDO_RESPINTO
    stato_finale: COLLAUDO_RESPINTO
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: prog
        etichetta: Nr. progressivo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: responseDate
        etichetta: Data esito
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: documentStatus
        etichetta: Esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: info
        etichetta: Descrizione esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: externalDocumentStatus
        etichetta: Codice esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: currRCTRespDate
        etichetta: Data Esito RCI Collaudo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: currRCTRespDocumentStatus
        etichetta: Esito RCI Collaudo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: currRCTRespInfo
        etichetta: Descrizione esito RCI Collaudo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: currRCTRespProg
        etichetta: Nr. progressivo esito RCI Collaudo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_CONFERMA_COLLAUDO_PARZIALE
    action: COLLAUDO_PARZIALE_ACCETTATO
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: prog
        etichetta: Nr. progressivo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: responseDate
        etichetta: Data esito
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: documentStatus
        etichetta: Esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: info
        etichetta: Descrizione esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: externalDocumentStatus
        etichetta: Codice esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: currRCPRespDate
        etichetta: Data Esito RCI Collaudo Parziale
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: currRCPRespDocumentStatus
        etichetta: Esito RCI Collaudo Parziale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: currRCPRespInfo
        etichetta: Descrizione esito RCI Collaudo Parziale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: currRCPRespProg
        etichetta: Nr. progressivo esito RCI Collaudo Parziale
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_CONFERMA_COLLAUDO_PARZIALE
    action: COLLAUDO_PARZIALE_RESPINTO
    stato_finale: COLLAUDO_PARZIALE_RESPINTO
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: prog
        etichetta: Nr. progressivo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: responseDate
        etichetta: Data esito
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: documentStatus
        etichetta: Esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: info
        etichetta: Descrizione esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: externalDocumentStatus
        etichetta: Codice esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: currRCPRespDate
        etichetta: Data Esito RCI Collaudo Parziale
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: currRCPRespDocumentStatus
        etichetta: Esito RCI Collaudo Parziale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: currRCPRespInfo
        etichetta: Descrizione esito RCI Collaudo Parziale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: currRCPRespProg
        etichetta: Nr. progressivo esito RCI Collaudo Parziale
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_CONFERMA_FINE_LAVORI
    action: FINE_LAVORI_ACCETTATO
    stato_finale: FINE_LAVORI
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: prog
        etichetta: Nr. progressivo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: responseDate
        etichetta: Data esito
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: documentStatus
        etichetta: Esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: info
        etichetta: Descrizione esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: externalDocumentStatus
        etichetta: Codice esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: endWorkDateStep
        etichetta: Data step fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: currCUTRespDate
        etichetta: Data Esito CUI Fine Lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: currCUTRespDocumentStatus
        etichetta: Esito CUI Fine Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: currCUTRespInfo
        etichetta: Descrizione esito CUI Fine Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 9
        descrizione: currCUTRespProg
        etichetta: Nr. progressivo esito CUI Fine Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_CONFERMA_FINE_LAVORI
    action: FINE_LAVORI_RESPINTO
    stato_finale: FINE_LAVORI_RESPINTA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: prog
        etichetta: Nr. progressivo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: responseDate
        etichetta: Data esito
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: documentStatus
        etichetta: Esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: info
        etichetta: Descrizione esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: externalDocumentStatus
        etichetta: Codice esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: currCUTRespDate
        etichetta: Data Esito CUI Fine Lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: currCUTRespDocumentStatus
        etichetta: Esito CUI Fine Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: currCUTRespInfo
        etichetta: Descrizione esito CUI Fine Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: currCUTRespProg
        etichetta: Nr. progressivo esito CUI Fine Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_CONFERMA_FINE_LAVORI_PARZIALE
    action: FINE_LAVORI_PARZIALE_ACCETTATO
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: prog
        etichetta: Nr. progressivo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: responseDate
        etichetta: Data esito
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: documentStatus
        etichetta: Esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: info
        etichetta: Descrizione esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: endPartialWorkDateStep
        etichetta: Data step fine lavori parziale
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: externalDocumentStatus
        etichetta: Codice esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: currCUPRespDate
        etichetta: Data Esito CUI Fine Lavori Parziale
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: currCUPRespDocumentStatus
        etichetta: Esito CUI Fine Lavori Parziale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: currCUPRespInfo
        etichetta: Descrizione esito CUI Fine Lavori Parziale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 9
        descrizione: currCUPRespProg
        etichetta: Nr. progressivo esito CUI Fine Lavori Parziale
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_CONFERMA_FINE_LAVORI_PARZIALE
    action: FINE_LAVORI_PARZIALE_RESPINTO
    stato_finale: FINE_LAVORI_PARZIALE_RESPINTO
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: prog
        etichetta: Nr. progressivo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: responseDate
        etichetta: Data esito
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: documentStatus
        etichetta: Esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: info
        etichetta: Descrizione esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: externalDocumentStatus
        etichetta: Codice esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: currCUPRespDate
        etichetta: Data Esito CUI Fine Lavori Parziale
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: currCUPRespDocumentStatus
        etichetta: Esito CUI Fine Lavori Parziale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: currCUPRespInfo
        etichetta: Descrizione esito CUI Fine Lavori Parziale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: currCUPRespProg
        etichetta: Nr. progressivo esito CUI Fine Lavori Parziale
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 9
        descrizione: advancementPercent
        etichetta: Percentuale avanzamento
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_CONFERMA_SOSPENSIONE
    action: SOSPENSIONE_ACCETTATA
    stato_finale: SOSPESA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: prog
        etichetta: Nr. progressivo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: responseDate
        etichetta: Data esito
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: documentStatus
        etichetta: Esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: info
        etichetta: Descrizione esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: externalDocumentStatus
        etichetta: Codice esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: currCISRespDate
        etichetta: Data Esito CIL Sospensione
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: currCISRespDocumentStatus
        etichetta: Esito CIL Sospensione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: currCISRespInfo
        etichetta: Descrizione esito CIL Sospensione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: currCISRespProg
        etichetta: Nr. progressivo esito CIL Sospensione
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_CONFERMA_SOSPENSIONE
    action: SOSPENSIONE_RESPINTA
    stato_finale: SOSPENSIONE_RESPINTA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: prog
        etichetta: Nr. progressivo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: responseDate
        etichetta: Data esito
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: documentStatus
        etichetta: Esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: info
        etichetta: Descrizione esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: externalDocumentStatus
        etichetta: Codice esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: currCISRespDate
        etichetta: Data Esito CIL Sospensione
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: currCISRespDocumentStatus
        etichetta: Esito CIL Sospensione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: currCISRespInfo
        etichetta: Descrizione esito CIL Sospensione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: currCISRespProg
        etichetta: Nr. progressivo esito CIL Sospensione
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_CONFERMA_VARIAZIONE_DURATA
    action: VARIAZIONE_DURATA_ACCETTATA
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: prog
        etichetta: Nr. progressivo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: responseDate
        etichetta: Data esito
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: documentStatus
        etichetta: Esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: info
        etichetta: Descrizione esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: externalDocumentStatus
        etichetta: Codice esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: currCIDRespDate
        etichetta: Data Esito CIL Variazione Durata
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: currCIDRespDocumentStatus
        etichetta: Esito CIL Variazione Durata
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: currCIDRespInfo
        etichetta: Descrizione esito CIL Variazione Durata
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: currCIDRespProg
        etichetta: Nr. progressivo esito CIL Variazione Durata
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_CONFERMA_VARIAZIONE_DURATA
    action: VARIAZIONE_DURATA_RESPINTA
    stato_finale: VARIAZIONE_DURATA_RESPINTA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: prog
        etichetta: Nr. progressivo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: responseDate
        etichetta: Data esito
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: documentStatus
        etichetta: Esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: info
        etichetta: Descrizione esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: externalDocumentStatus
        etichetta: Codice esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: currCIDRespDate
        etichetta: Data Esito CIL Variazione Durata
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: currCIDRespDocumentStatus
        etichetta: Esito CIL Variazione Durata
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: currCIDRespInfo
        etichetta: Descrizione esito CIL Variazione Durata
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: currCIDRespProg
        etichetta: Nr. progressivo esito CIL Variazione Durata
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_CONFERMA_VARIAZIONE_PERSONALE
    action: VARIAZIONE_PERSONALE_ACCETTATA
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: prog
        etichetta: Nr. progressivo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: responseDate
        etichetta: Data esito
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: documentStatus
        etichetta: Esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: info
        etichetta: Descrizione esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: externalDocumentStatus
        etichetta: Codice esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: currCIPRespDate
        etichetta: Data Esito CIL Variazione Personale
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: currCIPRespDocumentStatus
        etichetta: Esito CIL Variazione Personale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: currCIPRespInfo
        etichetta: Descrizione esito CIL Variazione Personale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: currCIPRespProg
        etichetta: Nr. progressivo esito CIL Variazione Personale
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_CONFERMA_VARIAZIONE_PERSONALE
    action: VARIAZIONE_PERSONALE_RESPINTA
    stato_finale: VARIAZIONE_PERSONALE_RESPINTA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: prog
        etichetta: Nr. progressivo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: responseDate
        etichetta: Data esito
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: documentStatus
        etichetta: Esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: info
        etichetta: Descrizione esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: externalDocumentStatus
        etichetta: Codice esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: currCIPRespDate
        etichetta: Data Esito CIL Variazione Personale
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: currCIPRespDocumentStatus
        etichetta: Esito CIL Variazione Personale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: currCIPRespInfo
        etichetta: Descrizione esito CIL Variazione Personale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: currCIPRespProg
        etichetta: Nr. progressivo esito CIL Variazione Personale
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: COLLAUDO_EFFETTUATO
    action: AGGIORNAMENTO_ARMADIO
    stato_finale: COLLAUDO_EFFETTUATO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
  - stato_iniziale: COLLAUDO_EFFETTUATO
    action: AGGIORNAMENTO_SITI_5G
    stato_finale: COLLAUDO_EFFETTUATO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: networkSiteType
        etichetta: Tipologia Sito Network
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Primaria,Realizzazione sito,Ripristino,Realizzazione sito + Ripristino,Altro'
      - posizione: 1
        descrizione: siteIdList
        etichetta: Elenco siti
        tipo_ui: MOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "URI",
            "URI": "../../../customers/TIM/contracts/CREATION/so/sites/lookups/siteIdList",
            "LAYOUT": "AUTOCOMPLETE",
            "CONTEXT": "Config",
            "ADD_FROM_AUTOCOMPLETE_ONLY": true
          }
  - stato_iniziale: COLLAUDO_EFFETTUATO
    action: FINE_LAVORI
    stato_finale: ATTESA_CONFERMA_FINE_LAVORI
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: centralOf
        etichetta: Centrale di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"technicalSiteDesc\"]"
          }
      - posizione: 1
        descrizione: cluName
        etichetta: Centro di lavoro unificato di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"central\"]"
          }
      - posizione: 2
        descrizione: dateDocCUI
        etichetta: Data emissione CUI
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 3
        descrizione: endDate
        etichetta: Data fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 4
        descrizione: shipmentType
        etichetta: Tipo consegna
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: execProg
        etichetta: Progetto esecutivo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: execProgRev
        etichetta: Revisione progetto esecutivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 7
        descrizione: technology
        etichetta: Tecnica
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 - DWDM'
      - posizione: 8
        descrizione: particularNotes
        etichetta: Segnalazioni particolari
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: partyAtTest
        etichetta: Personale Telecom
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Presente,Non presente'
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "Non presente"
          }
      - posizione: 10
        descrizione: testDate
        etichetta: Data collaudo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"endWorkDateStep\"]"
          }
      - posizione: 11
        descrizione: advancementPercent
        etichetta: Percentuale avanzamento
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 12
        descrizione: odaNumber
        etichetta: ODA
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"workOrderId\"]"
          }
      - posizione: 13
        descrizione: moiValue
        etichetta: Importo manodopera (Euro)
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00"
          }
      - posizione: 14
        descrizione: fornitureValue
        etichetta: Importo fornitura (Euro)
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00"
          }
      - posizione: 15
        descrizione: productionValue
        etichetta: Importo produzione (Euro)
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 16
        descrizione: activityNote
        etichetta: Descrizione lavori
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"networkDesc\"]"
          }
      - posizione: 17
        descrizione: progCUI
        etichetta: Nr. progressivo CUI
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: tdta0001
        etichetta: Dichiarazione di conformità DLgs 37/2008
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 19
        descrizione: tdta0002
        etichetta: Rapportazione lavori / Riepilogo prestazioni
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 20
        descrizione: tdta0003
        etichetta: Formulario identificazione rifiuti
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 21
        descrizione: tdta0004
        etichetta: Materiale di scorta
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 22
        descrizione: tdta0005
        etichetta: Documentazione tecnica / manuali apparato
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 23
        descrizione: tdta0006
        etichetta: Norma/e di collaudo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 24
        descrizione: tdta0007
        etichetta: Verbale di collaudo "Stand Alone"
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 25
        descrizione: tdta0008
        etichetta: Verbali e/o Modelli di collaudo specifici
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 26
        descrizione: tdta0009
        etichetta: Rapporto prova (per i Sistemi di Alimentazione e CDZ)
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 27
        descrizione: tdta0010
        etichetta: Autocertificazione di collaudo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 28
        descrizione: docs
        etichetta: Documenti Network
        tipo_ui: 'JSON  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 29
        descrizione: tdta0012
        etichetta: 2 Copie di cartografia agg.
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 30
        descrizione: tdta0013
        etichetta: Fatt. relative a forn. locali
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 31
        descrizione: tdta0014
        etichetta: Misure E/O e/o P. o autocertificaz.
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 32
        descrizione: tdta0015
        etichetta: Cp. Stp "Riep. op. in eser"
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 33
        descrizione: tdta0016
        etichetta: Modello rete cablata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 34
        descrizione: tdta0017
        etichetta: Modello area di influenza
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 35
        descrizione: tdta0018
        etichetta: El. riep. pres e mat for.
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 36
        descrizione: sendWithoutPeople
        etichetta: Invia documento senza elenco persone accesso in centrale
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 37
        descrizione: people
        etichetta: Elenco delle persone per l'accesso
        tipo_ui: 'JSON  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 38
        descrizione: cartographicAlphanumericUpdate
        etichetta: Aggiornamento cartografico/alfanumerico/dinamyc
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Nessuno,Alfanumerico,Cartografico,Dynamic,Alfanumerico/Cartografico,Alfanumerico/Dynamic,Cartografico/Dynamic,Alfanumerico/Cartografico/Dynamic'
      - posizione: 39
        descrizione: externalDocumentType
        etichetta: Tipo documento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 40
        descrizione: currCUTDateDoc
        etichetta: Data emissione CUI Fine Lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 41
        descrizione: currCUTprog
        etichetta: Nr. progressivo CUI Fine Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 42
        descrizione: currCUTRespDate
        etichetta: Data Esito CUI Fine Lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 43
        descrizione: currCUTRespDocumentStatus
        etichetta: Esito CUI Fine Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 44
        descrizione: currCUTRespInfo
        etichetta: Descrizione esito CUI Fine Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 45
        descrizione: currCUTRespProg
        etichetta: Nr. progressivo esito CUI Fine Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: COLLAUDO_RESPINTO
    action: ANNULLAMENTO_INVIO
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: COLLAUDO_RESPINTO
    action: COLLAUDO
    stato_finale: ATTESA_CONFERMA_COLLAUDO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dateDocRCI
        etichetta: Data emissione RCI
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: centralOf
        etichetta: Centrale di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"technicalSiteDesc\"]"
          }
      - posizione: 2
        descrizione: tdta0019
        etichetta: Realizzazione
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Parziale,Saldo,Totale'
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "Totale"
          }
      - posizione: 3
        descrizione: tdta0020
        etichetta: Codice progetto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"description\"]"
          }
      - posizione: 4
        descrizione: execProgRev
        etichetta: Revisione progetto esecutivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 5
        descrizione: activityNote
        etichetta: Descrizione lavori
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"networkDesc\"]"
          }
      - posizione: 6
        descrizione: testDate
        etichetta: Data collaudo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"endWorkDateStep\"]"
          }
      - posizione: 7
        descrizione: testTime
        etichetta: Ora collaudo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "00:00"
          }
      - posizione: 8
        descrizione: tdta0021
        etichetta: Telefono per collaudo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "+393486912457"
          }
      - posizione: 9
        descrizione: tdta0022
        etichetta: Fax per collaudo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 10
        descrizione: endDate
        etichetta: Data fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 11
        descrizione: tdta0023
        etichetta: Impianto / note
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 12
        descrizione: shipmentType
        etichetta: Tipo consegna
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 13
        descrizione: externalDocumentType
        etichetta: Tipo documento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 14
        descrizione: progRCI
        etichetta: Nr. progressivo RCI
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 15
        descrizione: currRCTDateDoc
        etichetta: Data emissione RCI Collaudo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 16
        descrizione: currRCTprog
        etichetta: Nr. progressivo RCI Collaudo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 17
        descrizione: currRCTRespDate
        etichetta: Data Esito RCI Collaudo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: currRCTRespDocumentStatus
        etichetta: Esito RCI Collaudo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 19
        descrizione: currRCTRespInfo
        etichetta: Descrizione esito RCI Collaudo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 20
        descrizione: currRCTRespProg
        etichetta: Nr. progressivo esito RCI Collaudo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: COLLAUDO_PARZIALE_RESPINTO
    action: ANNULLAMENTO_INVIO
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: COLLAUDO_PARZIALE_RESPINTO
    action: COLLAUDO_PARZIALE
    stato_finale: ATTESA_CONFERMA_COLLAUDO_PARZIALE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dateDocRCI
        etichetta: Data emissione RCI
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 1
        descrizione: centralOf
        etichetta: Centrale di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"technicalSiteDesc\"]"
          }
      - posizione: 2
        descrizione: tdta0019
        etichetta: Realizzazione
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: 'Parziale,Saldo,Totale'
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "Totale"
          }
      - posizione: 3
        descrizione: tdta0020
        etichetta: Codice progetto
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"description\"]"
          }
      - posizione: 4
        descrizione: execProgRev
        etichetta: Revisione progetto esecutivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 5
        descrizione: activityNote
        etichetta: Descrizione lavori
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"networkDesc\"]"
          }
      - posizione: 6
        descrizione: testDate
        etichetta: Data collaudo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"endWorkDateStep\"]"
          }
      - posizione: 7
        descrizione: testTime
        etichetta: Ora collaudo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "00:00"
          }
      - posizione: 8
        descrizione: tdta0021
        etichetta: Telefono per collaudo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "+393486912457"
          }
      - posizione: 9
        descrizione: tdta0022
        etichetta: Fax per collaudo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 10
        descrizione: endDate
        etichetta: Data fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 11
        descrizione: tdta0023
        etichetta: Impianto / note
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 12
        descrizione: shipmentType
        etichetta: Tipo consegna
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 13
        descrizione: externalDocumentType
        etichetta: Tipo documento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 14
        descrizione: progRCI
        etichetta: Nr. progressivo RCI
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 15
        descrizione: currRCPDateDoc
        etichetta: Data emissione RCI Collaudo Parziale
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 16
        descrizione: currRCPprog
        etichetta: Nr. progressivo RCI Collaudo Parziale
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 17
        descrizione: currRCPRespDate
        etichetta: Data Esito RCI Collaudo Parziale
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: currRCPRespDocumentStatus
        etichetta: Esito RCI Collaudo Parziale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 19
        descrizione: currRCPRespInfo
        etichetta: Descrizione esito RCI Collaudo Parziale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 20
        descrizione: currRCPRespProg
        etichetta: Nr. progressivo esito RCI Collaudo Parziale
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: FINE_LAVORI
    action: ATTESA_RAPPORTO_LAVORI
    stato_finale: RAPPORTO_LAVORI_IN_CORSO
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: FINE_LAVORI_RESPINTA
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancellationDate
        etichetta: Data annullamento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
  - stato_iniziale: FINE_LAVORI_RESPINTA
    action: FINE_LAVORI
    stato_finale: ATTESA_CONFERMA_FINE_LAVORI
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: centralOf
        etichetta: Centrale di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"technicalSiteDesc\"]"
          }
      - posizione: 1
        descrizione: cluName
        etichetta: Centro di lavoro unificato di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"central\"]"
          }
      - posizione: 2
        descrizione: dateDocCUI
        etichetta: Data emissione CUI
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 3
        descrizione: endDate
        etichetta: Data fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 4
        descrizione: shipmentType
        etichetta: Tipo consegna
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: execProg
        etichetta: Progetto esecutivo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: execProgRev
        etichetta: Revisione progetto esecutivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 7
        descrizione: technology
        etichetta: Tecnica
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 - DWDM'
      - posizione: 8
        descrizione: particularNotes
        etichetta: Segnalazioni particolari
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: partyAtTest
        etichetta: Personale Telecom
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Presente,Non presente'
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "Non presente"
          }
      - posizione: 10
        descrizione: testDate
        etichetta: Data collaudo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"endWorkDateStep\"]"
          }
      - posizione: 11
        descrizione: advancementPercent
        etichetta: Percentuale avanzamento
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 12
        descrizione: odaNumber
        etichetta: ODA
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"workOrderId\"]"
          }
      - posizione: 13
        descrizione: moiValue
        etichetta: Importo manodopera (Euro)
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00"
          }
      - posizione: 14
        descrizione: fornitureValue
        etichetta: Importo fornitura (Euro)
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00"
          }
      - posizione: 15
        descrizione: productionValue
        etichetta: Importo produzione (Euro)
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 16
        descrizione: activityNote
        etichetta: Descrizione lavori
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"networkDesc\"]"
          }
      - posizione: 17
        descrizione: progCUI
        etichetta: Nr. progressivo CUI
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: tdta0001
        etichetta: Dichiarazione di conformità DLgs 37/2008
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 19
        descrizione: tdta0002
        etichetta: Rapportazione lavori / Riepilogo prestazioni
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 20
        descrizione: tdta0003
        etichetta: Formulario identificazione rifiuti
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 21
        descrizione: tdta0004
        etichetta: Materiale di scorta
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 22
        descrizione: tdta0005
        etichetta: Documentazione tecnica / manuali apparato
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 23
        descrizione: tdta0006
        etichetta: Norma/e di collaudo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 24
        descrizione: tdta0007
        etichetta: Verbale di collaudo "Stand Alone"
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 25
        descrizione: tdta0008
        etichetta: Verbali e/o Modelli di collaudo specifici
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 26
        descrizione: tdta0009
        etichetta: Rapporto prova (per i Sistemi di Alimentazione e CDZ)
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 27
        descrizione: tdta0010
        etichetta: Autocertificazione di collaudo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 28
        descrizione: docs
        etichetta: Documenti Network
        tipo_ui: 'JSON  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 29
        descrizione: tdta0012
        etichetta: 2 Copie di cartografia agg.
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 30
        descrizione: tdta0013
        etichetta: Fatt. relative a forn. locali
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 31
        descrizione: tdta0014
        etichetta: Misure E/O e/o P. o autocertificaz.
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 32
        descrizione: tdta0015
        etichetta: Cp. Stp "Riep. op. in eser"
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 33
        descrizione: tdta0016
        etichetta: Modello rete cablata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 34
        descrizione: tdta0017
        etichetta: Modello area di influenza
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 35
        descrizione: tdta0018
        etichetta: El. riep. pres e mat for.
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 36
        descrizione: sendWithoutPeople
        etichetta: Invia documento senza elenco persone accesso in centrale
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 37
        descrizione: people
        etichetta: Elenco delle persone per l'accesso
        tipo_ui: 'JSON  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 38
        descrizione: cartographicAlphanumericUpdate
        etichetta: Aggiornamento cartografico/alfanumerico/dinamyc
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Nessuno,Alfanumerico,Cartografico,Dynamic,Alfanumerico/Cartografico,Alfanumerico/Dynamic,Cartografico/Dynamic,Alfanumerico/Cartografico/Dynamic'
      - posizione: 39
        descrizione: externalDocumentType
        etichetta: Tipo documento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 40
        descrizione: currCUTDateDoc
        etichetta: Data emissione CUI Fine Lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 41
        descrizione: currCUTprog
        etichetta: Nr. progressivo CUI Fine Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 42
        descrizione: currCUTRespDate
        etichetta: Data Esito CUI Fine Lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 43
        descrizione: currCUTRespDocumentStatus
        etichetta: Esito CUI Fine Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 44
        descrizione: currCUTRespInfo
        etichetta: Descrizione esito CUI Fine Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 45
        descrizione: currCUTRespProg
        etichetta: Nr. progressivo esito CUI Fine Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: FINE_LAVORI_PARZIALE_RESPINTO
    action: ANNULLAMENTO_INVIO
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: FINE_LAVORI_PARZIALE_RESPINTO
    action: FINE_LAVORI_PARZIALE
    stato_finale: ATTESA_CONFERMA_FINE_LAVORI_PARZIALE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: centralOf
        etichetta: Centrale di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"technicalSiteDesc\"]"
          }
      - posizione: 1
        descrizione: cluName
        etichetta: Centro di lavoro unificato di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"central\"]"
          }
      - posizione: 2
        descrizione: dateDocCUI
        etichetta: Data emissione CUI
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 3
        descrizione: endDate
        etichetta: Data fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 4
        descrizione: shipmentType
        etichetta: Tipo consegna
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: execProg
        etichetta: Progetto esecutivo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: execProgRev
        etichetta: Revisione progetto esecutivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 7
        descrizione: technology
        etichetta: Tecnica
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 - DWDM'
      - posizione: 8
        descrizione: particularNotes
        etichetta: Segnalazioni particolari
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: partyAtTest
        etichetta: Personale Telecom
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Presente,Non presente'
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "Non presente"
          }
      - posizione: 10
        descrizione: testDate
        etichetta: Data collaudo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"endWorkDateStep\"]"
          }
      - posizione: 11
        descrizione: advancementPercent
        etichetta: Percentuale avanzamento
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 12
        descrizione: odaNumber
        etichetta: ODA
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"workOrderId\"]"
          }
      - posizione: 13
        descrizione: moiValue
        etichetta: Importo manodopera (Euro)
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00"
          }
      - posizione: 14
        descrizione: fornitureValue
        etichetta: Importo fornitura (Euro)
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00"
          }
      - posizione: 15
        descrizione: productionValue
        etichetta: Importo produzione (Euro)
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 16
        descrizione: activityNote
        etichetta: Descrizione lavori
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"networkDesc\"]"
          }
      - posizione: 17
        descrizione: progCUI
        etichetta: Nr. progressivo CUI
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: tdta0001
        etichetta: Dichiarazione di conformità DLgs 37/2008
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 19
        descrizione: tdta0002
        etichetta: Rapportazione lavori / Riepilogo prestazioni
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 20
        descrizione: tdta0003
        etichetta: Formulario identificazione rifiuti
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 21
        descrizione: tdta0004
        etichetta: Materiale di scorta
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 22
        descrizione: tdta0005
        etichetta: Documentazione tecnica / manuali apparato
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 23
        descrizione: tdta0006
        etichetta: Norma/e di collaudo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 24
        descrizione: tdta0007
        etichetta: Verbale di collaudo "Stand Alone"
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 25
        descrizione: tdta0008
        etichetta: Verbali e/o Modelli di collaudo specifici
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 26
        descrizione: tdta0009
        etichetta: Rapporto prova (per i Sistemi di Alimentazione e CDZ)
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 27
        descrizione: tdta0010
        etichetta: Autocertificazione di collaudo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 28
        descrizione: docs
        etichetta: Documenti Network
        tipo_ui: 'JSON  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 29
        descrizione: tdta0012
        etichetta: 2 Copie di cartografia agg.
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 30
        descrizione: tdta0013
        etichetta: Fatt. relative a forn. locali
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 31
        descrizione: tdta0014
        etichetta: Misure E/O e/o P. o autocertificaz.
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 32
        descrizione: tdta0015
        etichetta: Cp. Stp "Riep. op. in eser"
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 33
        descrizione: tdta0016
        etichetta: Modello rete cablata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 34
        descrizione: tdta0017
        etichetta: Modello area di influenza
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 35
        descrizione: tdta0018
        etichetta: El. riep. pres e mat for.
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 36
        descrizione: people
        etichetta: Elenco delle persone per l'accesso
        tipo_ui: 'JSON  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 37
        descrizione: cartographicAlphanumericUpdate
        etichetta: Aggiornamento cartografico/alfanumerico/dinamyc
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Nessuno,Alfanumerico,Cartografico,Dynamic,Alfanumerico/Cartografico,Alfanumerico/Dynamic,Cartografico/Dynamic,Alfanumerico/Cartografico/Dynamic'
      - posizione: 38
        descrizione: externalDocumentType
        etichetta: Tipo documento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 39
        descrizione: currCUPDateDoc
        etichetta: Data emissione CUI Fine Lavori Parziale
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 40
        descrizione: currCUPprog
        etichetta: Nr. progressivo CUI Fine Lavori Parziale
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 41
        descrizione: currCUPRespDate
        etichetta: Data Esito CUI Fine Lavori Parziale
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 42
        descrizione: currCUPRespDocumentStatus
        etichetta: Esito CUI Fine Lavori Parziale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 43
        descrizione: currCUPRespInfo
        etichetta: Descrizione esito CUI Fine Lavori Parziale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 44
        descrizione: currCUPRespProg
        etichetta: Nr. progressivo esito CUI Fine Lavori Parziale
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: SOSPESA
    action: AGGIORNAMENTO_ARMADIO
    stato_finale: SOSPESA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
  - stato_iniziale: SOSPESA
    action: AGGIORNAMENTO_SITI_5G
    stato_finale: SOSPESA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: networkSiteType
        etichetta: Tipologia Sito Network
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Primaria,Realizzazione sito,Ripristino,Realizzazione sito + Ripristino,Altro'
      - posizione: 1
        descrizione: siteIdList
        etichetta: Elenco siti
        tipo_ui: MOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "URI",
            "URI": "../../../customers/TIM/contracts/CREATION/so/sites/lookups/siteIdList",
            "LAYOUT": "AUTOCOMPLETE",
            "CONTEXT": "Config",
            "ADD_FROM_AUTOCOMPLETE_ONLY": true
          }
  - stato_iniziale: SOSPESA
    action: AGGIUNTA_DOCUMENTAZIONE
    stato_finale: SOSPESA
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: SOSPESA
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancellationDate
        etichetta: Data annullamento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
  - stato_iniziale: SOSPESA
    action: RIPRESA
    stato_finale: ATTESA_CONFERMA_RIPRESA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: centralOf
        etichetta: Centrale di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"technicalSiteDesc\"]"
          }
      - posizione: 1
        descrizione: ftcName
        etichetta: Funzione territoriale competente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"central\"]"
          }
      - posizione: 2
        descrizione: technicalArea
        etichetta: Settore tecnico
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'ImpiantiCommutazione,ImpiantiTrasmissione,LavoriContoEsercizio,LavoriVari,ReteDistribuzioneCavi,RetiSpeciali,SistemiAlimentazioneCondizionamento,SistemiGestione'
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "ReteDistribuzioneCavi"
          }
      - posizione: 3
        descrizione: execProg
        etichetta: Progetto esecutivo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: execProgRev
        etichetta: Revisione progetto esecutivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 5
        descrizione: zeroCostActivity
        etichetta: Lavoro costo zero
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: zeroCostActivityDate
        etichetta: Data lavoro costo zero
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: activityNote
        etichetta: Descrizione lavori
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"networkDesc\"]"
          }
      - posizione: 8
        descrizione: versionDescription
        etichetta: Causale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "PrimaComunicazione"
          }
      - posizione: 9
        descrizione: technology
        etichetta: Tecnica
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 - DWDM'
      - posizione: 10
        descrizione: startDate
        etichetta: Data inizio lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 11
        descrizione: presumedEndDate
        etichetta: Data presunta fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"forecastEndDateActual\"]"
          }
      - posizione: 12
        descrizione: odaNumber
        etichetta: ODA
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"workOrderId\"]"
          }
      - posizione: 13
        descrizione: subCode
        etichetta: Codice ditta sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 14
        descrizione: subDescription
        etichetta: Descrizione ditta sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 15
        descrizione: subAccountableName
        etichetta: Nome persona di riferimento sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 16
        descrizione: subAccountableSurname
        etichetta: Cognome persona di riferimento sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 17
        descrizione: subPhone
        etichetta: Telefono sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: subFax
        etichetta: Fax sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 19
        descrizione: subDate
        etichetta: Data decorrenza sub
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 20
        descrizione: testRequest
        etichetta: Validità CIL come richiesta collaudo
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 21
        descrizione: dateDocCIL
        etichetta: Data emissione CIL
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 22
        descrizione: progCIL
        etichetta: Nr. progressivo CIL
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 23
        descrizione: accountableSurname
        etichetta: Cognome responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: Y
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"lastName\"]"
          }
      - posizione: 24
        descrizione: accountableName
        etichetta: Nome responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: Y
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"firstName\"]"
          }
      - posizione: 25
        descrizione: accountablePhoneNumber
        etichetta: Telefono responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"mobilePhone\"]"
          }
      - posizione: 26
        descrizione: accountableDate
        etichetta: Data decorrenza responsabile attività
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 27
        descrizione: IOCDate
        etichetta: 'IOC Data inizio opere civili (IMPORTANTE: da valorizzare in presenza di opere civili)'
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 28
        descrizione: IOCAddress
        etichetta: 'IOC Via (IMPORTANTE: da valorizzare in presenza di opere civili)'
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 29
        descrizione: externalDocumentType
        etichetta: Tipo documento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 30
        descrizione: currCIRDateDoc
        etichetta: Data emissione CIL Ripresa
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 31
        descrizione: currCIRprog
        etichetta: Nr. progressivo CIL Ripresa
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 32
        descrizione: currCIRRespDate
        etichetta: Data Esito CIL Ripresa
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 33
        descrizione: currCIRRespDocumentStatus
        etichetta: Esito CIL Ripresa
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 34
        descrizione: currCIRRespInfo
        etichetta: Descrizione esito CIL Ripresa
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 35
        descrizione: currCIRRespProg
        etichetta: Nr. progressivo esito CIL Ripresa
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: SOSPENSIONE_RESPINTA
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancellationDate
        etichetta: Data annullamento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
  - stato_iniziale: SOSPENSIONE_RESPINTA
    action: ANNULLAMENTO_INVIO
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: SOSPENSIONE_RESPINTA
    action: SOSPENSIONE
    stato_finale: ATTESA_CONFERMA_SOSPENSIONE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: centralOf
        etichetta: Centrale di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"technicalSiteDesc\"]"
          }
      - posizione: 1
        descrizione: ftcName
        etichetta: Funzione territoriale competente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"central\"]"
          }
      - posizione: 2
        descrizione: technicalArea
        etichetta: Settore tecnico
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'ImpiantiCommutazione,ImpiantiTrasmissione,LavoriContoEsercizio,LavoriVari,ReteDistribuzioneCavi,RetiSpeciali,SistemiAlimentazioneCondizionamento,SistemiGestione'
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "ReteDistribuzioneCavi"
          }
      - posizione: 3
        descrizione: execProg
        etichetta: Progetto esecutivo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: execProgRev
        etichetta: Revisione progetto esecutivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 5
        descrizione: zeroCostActivity
        etichetta: Lavoro costo zero
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: zeroCostActivityDate
        etichetta: Data lavoro costo zero
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: activityNote
        etichetta: Descrizione lavori
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"networkDesc\"]"
          }
      - posizione: 8
        descrizione: suspensionReason
        etichetta: Motivo sospensione
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'ERRATA CONSISTENZA,MANCANZA MATERIALI,MANC. PREDISP. PRIV,PERMESSI COMUNE,PERMESSI ENTI,PERMESSI PRIVATI,TUBAZ.TI NON PRATICAB.,MANCA ALIMENTAZIONE,MANCA INSTRADAMENTO, MANCA PROGETTO RETE,VARIE)'
      - posizione: 9
        descrizione: versionDescription
        etichetta: Causale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "PrimaComunicazione"
          }
      - posizione: 10
        descrizione: technology
        etichetta: Tecnica
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 - DWDM'
      - posizione: 11
        descrizione: startDate
        etichetta: Data inizio lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 12
        descrizione: presumedEndDate
        etichetta: Data presunta fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"forecastEndDateActual\"]"
          }
      - posizione: 13
        descrizione: odaNumber
        etichetta: ODA
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"workOrderId\"]"
          }
      - posizione: 14
        descrizione: subCode
        etichetta: Codice ditta sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 15
        descrizione: subDescription
        etichetta: Descrizione ditta sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 16
        descrizione: subAccountableName
        etichetta: Nome persona di riferimento sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 17
        descrizione: subAccountableSurname
        etichetta: Cognome persona di riferimento sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: subPhone
        etichetta: Telefono sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 19
        descrizione: subFax
        etichetta: Fax sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 20
        descrizione: subDate
        etichetta: Data decorrenza sub
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 21
        descrizione: testRequest
        etichetta: Validità CIL come richiesta collaudo
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 22
        descrizione: dateDocCIL
        etichetta: Data emissione CIL
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 23
        descrizione: progCIL
        etichetta: Nr. progressivo CIL
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 24
        descrizione: accountableSurname
        etichetta: Cognome responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: Y
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"lastName\"]"
          }
      - posizione: 25
        descrizione: accountableName
        etichetta: Nome responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: Y
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"firstName\"]"
          }
      - posizione: 26
        descrizione: accountablePhoneNumber
        etichetta: Telefono responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"mobilePhone\"]"
          }
      - posizione: 27
        descrizione: accountableDate
        etichetta: Data decorrenza responsabile attività
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 28
        descrizione: externalDocumentType
        etichetta: Tipo documento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 29
        descrizione: currCISDateDoc
        etichetta: Data emissione CIL Sospensione
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 30
        descrizione: currCISprog
        etichetta: Nr. progressivo CIL Sospensione
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 31
        descrizione: currCISRespDate
        etichetta: Data Esito CIL Sospensione
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 32
        descrizione: currCISRespDocumentStatus
        etichetta: Esito CIL Sospensione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 33
        descrizione: currCISRespInfo
        etichetta: Descrizione esito CIL Sospensione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 34
        descrizione: currCISRespProg
        etichetta: Nr. progressivo esito CIL Sospensione
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: VARIAZIONE_DURATA_RESPINTA
    action: ANNULLAMENTO_INVIO
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: VARIAZIONE_DURATA_RESPINTA
    action: VARIAZIONE_DURATA
    stato_finale: ATTESA_CONFERMA_VARIAZIONE_DURATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: centralOf
        etichetta: Centrale di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"technicalSiteDesc\"]"
          }
      - posizione: 1
        descrizione: ftcName
        etichetta: Funzione territoriale competente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"central\"]"
          }
      - posizione: 2
        descrizione: technicalArea
        etichetta: Settore tecnico
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'ImpiantiCommutazione,ImpiantiTrasmissione,LavoriContoEsercizio,LavoriVari,ReteDistribuzioneCavi,RetiSpeciali,SistemiAlimentazioneCondizionamento,SistemiGestione'
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "ReteDistribuzioneCavi"
          }
      - posizione: 3
        descrizione: execProg
        etichetta: Progetto esecutivo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: execProgRev
        etichetta: Revisione progetto esecutivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 5
        descrizione: zeroCostActivity
        etichetta: Lavoro costo zero
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: zeroCostActivityDate
        etichetta: Data lavoro costo zero
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: activityNote
        etichetta: Descrizione lavori
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"networkDesc\"]"
          }
      - posizione: 8
        descrizione: versionDescription
        etichetta: Causale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "PrimaComunicazione"
          }
      - posizione: 9
        descrizione: technology
        etichetta: Tecnica
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 - DWDM'
      - posizione: 10
        descrizione: startDate
        etichetta: Data inizio lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 11
        descrizione: presumedEndDate
        etichetta: Data presunta fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"forecastEndDateActual\"]"
          }
      - posizione: 12
        descrizione: odaNumber
        etichetta: ODA
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"workOrderId\"]"
          }
      - posizione: 13
        descrizione: subCode
        etichetta: Codice ditta sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 14
        descrizione: subDescription
        etichetta: Descrizione ditta sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 15
        descrizione: subAccountableName
        etichetta: Nome persona di riferimento sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 16
        descrizione: subAccountableSurname
        etichetta: Cognome persona di riferimento sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 17
        descrizione: subPhone
        etichetta: Telefono sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: subFax
        etichetta: Fax sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 19
        descrizione: subDate
        etichetta: Data decorrenza sub
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 20
        descrizione: testRequest
        etichetta: Validità CIL come richiesta collaudo
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 21
        descrizione: dateDocCIL
        etichetta: Data emissione CIL
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 22
        descrizione: progCIL
        etichetta: Nr. progressivo CIL
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 23
        descrizione: accountableSurname
        etichetta: Cognome responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: Y
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"lastName\"]"
          }
      - posizione: 24
        descrizione: accountableName
        etichetta: Nome responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: Y
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"firstName\"]"
          }
      - posizione: 25
        descrizione: accountablePhoneNumber
        etichetta: Telefono responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"mobilePhone\"]"
          }
      - posizione: 26
        descrizione: accountableDate
        etichetta: Data decorrenza responsabile attività
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 27
        descrizione: externalDocumentType
        etichetta: Tipo documento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 28
        descrizione: currCIDDateDoc
        etichetta: Data emissione CIL Variazione Durata
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 29
        descrizione: currCIDprog
        etichetta: Nr. progressivo CIL Variazione Durata
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 30
        descrizione: currCIDRespDate
        etichetta: Data Esito CIL Variazione Durata
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 31
        descrizione: currCIDRespDocumentStatus
        etichetta: Esito CIL Variazione Durata
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 32
        descrizione: currCIDRespInfo
        etichetta: Descrizione esito CIL Variazione Durata
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 33
        descrizione: currCIDRespProg
        etichetta: Nr. progressivo esito CIL Variazione Durata
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: VARIAZIONE_PERSONALE_RESPINTA
    action: ANNULLAMENTO_INVIO
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: VARIAZIONE_PERSONALE_RESPINTA
    action: VARIAZIONE_PERSONALE
    stato_finale: ATTESA_CONFERMA_VARIAZIONE_PERSONALE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: centralOf
        etichetta: Centrale di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"technicalSiteDesc\"]"
          }
      - posizione: 1
        descrizione: ftcName
        etichetta: Funzione territoriale competente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"central\"]"
          }
      - posizione: 2
        descrizione: technicalArea
        etichetta: Settore tecnico
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'ImpiantiCommutazione,ImpiantiTrasmissione,LavoriContoEsercizio,LavoriVari,ReteDistribuzioneCavi,RetiSpeciali,SistemiAlimentazioneCondizionamento,SistemiGestione'
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "ReteDistribuzioneCavi"
          }
      - posizione: 3
        descrizione: execProg
        etichetta: Progetto esecutivo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: execProgRev
        etichetta: Revisione progetto esecutivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 5
        descrizione: zeroCostActivity
        etichetta: Lavoro costo zero
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: zeroCostActivityDate
        etichetta: Data lavoro costo zero
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: activityNote
        etichetta: Descrizione lavori
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"networkDesc\"]"
          }
      - posizione: 8
        descrizione: versionDescription
        etichetta: Causale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "PrimaComunicazione"
          }
      - posizione: 9
        descrizione: technology
        etichetta: Tecnica
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 - DWDM'
      - posizione: 10
        descrizione: startDate
        etichetta: Data inizio lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 11
        descrizione: presumedEndDate
        etichetta: Data presunta fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"forecastEndDateActual\"]"
          }
      - posizione: 12
        descrizione: odaNumber
        etichetta: ODA
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"workOrderId\"]"
          }
      - posizione: 13
        descrizione: subCode
        etichetta: Codice ditta sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 14
        descrizione: subDescription
        etichetta: Descrizione ditta sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 15
        descrizione: subAccountableName
        etichetta: Nome persona di riferimento sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 16
        descrizione: subAccountableSurname
        etichetta: Cognome persona di riferimento sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 17
        descrizione: subPhone
        etichetta: Telefono sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: subFax
        etichetta: Fax sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 19
        descrizione: subDate
        etichetta: Data decorrenza sub
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 20
        descrizione: testRequest
        etichetta: Validità CIL come richiesta collaudo
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 21
        descrizione: dateDocCIL
        etichetta: Data emissione CIL
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 22
        descrizione: progCIL
        etichetta: Nr. progressivo CIL
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 23
        descrizione: accountableSurname
        etichetta: Cognome responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: Y
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"lastName\"]"
          }
      - posizione: 24
        descrizione: accountableName
        etichetta: Nome responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: Y
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"firstName\"]"
          }
      - posizione: 25
        descrizione: accountablePhoneNumber
        etichetta: Telefono responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"mobilePhone\"]"
          }
      - posizione: 26
        descrizione: accountableDate
        etichetta: Data decorrenza responsabile attività
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 27
        descrizione: IOCDate
        etichetta: 'IOC Data inizio opere civili (IMPORTANTE: da valorizzare in presenza di opere civili)'
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 28
        descrizione: IOCAddress
        etichetta: 'IOC Via (IMPORTANTE: da valorizzare in presenza di opere civili)'
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 29
        descrizione: externalDocumentType
        etichetta: Tipo documento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 30
        descrizione: currCIPDateDoc
        etichetta: Data emissione CIL Variazione Personale
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 31
        descrizione: currCIPprog
        etichetta: Nr. progressivo CIL Variazione Personale
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 32
        descrizione: currCIPRespDate
        etichetta: Data Esito CIL Variazione Personale
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 33
        descrizione: currCIPRespDocumentStatus
        etichetta: Esito CIL Variazione Personale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 34
        descrizione: currCIPRespInfo
        etichetta: Descrizione esito CIL Variazione Personale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 35
        descrizione: currCIPRespProg
        etichetta: Nr. progressivo esito CIL Variazione Personale
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: RAPPORTO_LAVORI_IN_CORSO
    action: AGGIORNAMENTO_ARMADIO
    stato_finale: RAPPORTO_LAVORI_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
  - stato_iniziale: RAPPORTO_LAVORI_IN_CORSO
    action: AGGIORNAMENTO_SITI_5G
    stato_finale: RAPPORTO_LAVORI_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: networkSiteType
        etichetta: Tipologia Sito Network
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Primaria,Realizzazione sito,Ripristino,Realizzazione sito + Ripristino,Altro'
      - posizione: 1
        descrizione: siteIdList
        etichetta: Elenco siti
        tipo_ui: MOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "URI",
            "URI": "../../../customers/TIM/contracts/CREATION/so/sites/lookups/siteIdList",
            "LAYOUT": "AUTOCOMPLETE",
            "CONTEXT": "Config",
            "ADD_FROM_AUTOCOMPLETE_ONLY": true
          }
  - stato_iniziale: RAPPORTO_LAVORI_IN_CORSO
    action: AGGIUNTA_DOCUMENTAZIONE
    stato_finale: RAPPORTO_LAVORI_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: RAPPORTO_LAVORI_IN_CORSO
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancellationDate
        etichetta: Data annullamento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
  - stato_iniziale: RAPPORTO_LAVORI_IN_CORSO
    action: FINE_LAVORI
    stato_finale: ATTESA_CONFERMA_REINVIO_FINE_LAVORI
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: centralOf
        etichetta: Centrale di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"technicalSiteDesc\"]"
          }
      - posizione: 1
        descrizione: cluName
        etichetta: Centro di lavoro unificato di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"central\"]"
          }
      - posizione: 2
        descrizione: dateDocCUI
        etichetta: Data emissione CUI
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 3
        descrizione: endDate
        etichetta: Data fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 4
        descrizione: shipmentType
        etichetta: Tipo consegna
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: execProg
        etichetta: Progetto esecutivo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: execProgRev
        etichetta: Revisione progetto esecutivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 7
        descrizione: technology
        etichetta: Tecnica
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 - DWDM'
      - posizione: 8
        descrizione: particularNotes
        etichetta: Segnalazioni particolari
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: partyAtTest
        etichetta: Personale Telecom
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Presente,Non presente'
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "Non presente"
          }
      - posizione: 10
        descrizione: testDate
        etichetta: Data collaudo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"endWorkDateStep\"]"
          }
      - posizione: 11
        descrizione: advancementPercent
        etichetta: Percentuale avanzamento
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 12
        descrizione: odaNumber
        etichetta: ODA
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"workOrderId\"]"
          }
      - posizione: 13
        descrizione: moiValue
        etichetta: Importo manodopera (Euro)
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00"
          }
      - posizione: 14
        descrizione: fornitureValue
        etichetta: Importo fornitura (Euro)
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00"
          }
      - posizione: 15
        descrizione: productionValue
        etichetta: Importo produzione (Euro)
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 16
        descrizione: activityNote
        etichetta: Descrizione lavori
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"networkDesc\"]"
          }
      - posizione: 17
        descrizione: progCUI
        etichetta: Nr. progressivo CUI
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: tdta0001
        etichetta: Dichiarazione di conformità DLgs 37/2008
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 19
        descrizione: tdta0002
        etichetta: Rapportazione lavori / Riepilogo prestazioni
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 20
        descrizione: tdta0003
        etichetta: Formulario identificazione rifiuti
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 21
        descrizione: tdta0004
        etichetta: Materiale di scorta
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 22
        descrizione: tdta0005
        etichetta: Documentazione tecnica / manuali apparato
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 23
        descrizione: tdta0006
        etichetta: Norma/e di collaudo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 24
        descrizione: tdta0007
        etichetta: Verbale di collaudo "Stand Alone"
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 25
        descrizione: tdta0008
        etichetta: Verbali e/o Modelli di collaudo specifici
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 26
        descrizione: tdta0009
        etichetta: Rapporto prova (per i Sistemi di Alimentazione e CDZ)
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 27
        descrizione: tdta0010
        etichetta: Autocertificazione di collaudo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 28
        descrizione: docs
        etichetta: Documenti Network
        tipo_ui: 'JSON  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 29
        descrizione: tdta0012
        etichetta: 2 Copie di cartografia agg.
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 30
        descrizione: tdta0013
        etichetta: Fatt. relative a forn. locali
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 31
        descrizione: tdta0014
        etichetta: Misure E/O e/o P. o autocertificaz.
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 32
        descrizione: tdta0015
        etichetta: Cp. Stp "Riep. op. in eser"
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 33
        descrizione: tdta0016
        etichetta: Modello rete cablata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 34
        descrizione: tdta0017
        etichetta: Modello area di influenza
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 35
        descrizione: tdta0018
        etichetta: El. riep. pres e mat for.
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 36
        descrizione: sendWithoutPeople
        etichetta: Invia documento senza elenco persone accesso in centrale
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 37
        descrizione: people
        etichetta: Elenco delle persone per l'accesso
        tipo_ui: 'JSON  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 38
        descrizione: cartographicAlphanumericUpdate
        etichetta: Aggiornamento cartografico/alfanumerico/dinamyc
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Nessuno,Alfanumerico,Cartografico,Dynamic,Alfanumerico/Cartografico,Alfanumerico/Dynamic,Cartografico/Dynamic,Alfanumerico/Cartografico/Dynamic'
      - posizione: 39
        descrizione: externalDocumentType
        etichetta: Tipo documento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 40
        descrizione: currCUTDateDoc
        etichetta: Data emissione CUI Fine Lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 41
        descrizione: currCUTprog
        etichetta: Nr. progressivo CUI Fine Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 42
        descrizione: currCUTRespDate
        etichetta: Data Esito CUI Fine Lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 43
        descrizione: currCUTRespDocumentStatus
        etichetta: Esito CUI Fine Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 44
        descrizione: currCUTRespInfo
        etichetta: Descrizione esito CUI Fine Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 45
        descrizione: currCUTRespProg
        etichetta: Nr. progressivo esito CUI Fine Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: RAPPORTO_LAVORI_IN_CORSO
    action: RAPPORTO_LAVORI_CONCLUSO
    stato_finale: VERIFICA_CHIUSURA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: ATTESA_CONFERMA_RIPRESA
    action: RIPRESA_ACCETTATA
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: prog
        etichetta: Nr. progressivo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: responseDate
        etichetta: Data esito
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: documentStatus
        etichetta: Esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: info
        etichetta: Descrizione esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: externalDocumentStatus
        etichetta: Codice esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: currCIRRespDate
        etichetta: Data Esito CIL Ripresa
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: currCIRRespDocumentStatus
        etichetta: Esito CIL Ripresa
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: currCIRRespInfo
        etichetta: Descrizione esito CIL Ripresa
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: currCIRRespProg
        etichetta: Nr. progressivo esito CIL Ripresa
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_CONFERMA_RIPRESA
    action: RIPRESA_RESPINTA
    stato_finale: RIPRESA_RESPINTA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: prog
        etichetta: Nr. progressivo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: responseDate
        etichetta: Data esito
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: documentStatus
        etichetta: Esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: info
        etichetta: Descrizione esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: externalDocumentStatus
        etichetta: Codice esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: currCIRRespDate
        etichetta: Data Esito CIL Ripresa
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: currCIRRespDocumentStatus
        etichetta: Esito CIL Ripresa
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: currCIRRespInfo
        etichetta: Descrizione esito CIL Ripresa
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: currCIRRespProg
        etichetta: Nr. progressivo esito CIL Ripresa
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_CONFERMA_REINVIO_FINE_LAVORI
    action: FINE_LAVORI_ACCETTATO
    stato_finale: RAPPORTO_LAVORI_IN_CORSO
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: prog
        etichetta: Nr. progressivo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: responseDate
        etichetta: Data esito
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: documentStatus
        etichetta: Esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: info
        etichetta: Descrizione esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: externalDocumentStatus
        etichetta: Codice esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: endWorkDateStep
        etichetta: Data step fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: currCUTRespDate
        etichetta: Data Esito CUI Fine Lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: currCUTRespDocumentStatus
        etichetta: Esito CUI Fine Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: currCUTRespInfo
        etichetta: Descrizione esito CUI Fine Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 9
        descrizione: currCUTRespProg
        etichetta: Nr. progressivo esito CUI Fine Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_CONFERMA_REINVIO_FINE_LAVORI
    action: FINE_LAVORI_RESPINTO
    stato_finale: REINVIO_FINE_LAVORI_RESPINTO
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: prog
        etichetta: Nr. progressivo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: responseDate
        etichetta: Data esito
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: documentStatus
        etichetta: Esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: info
        etichetta: Descrizione esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: externalDocumentStatus
        etichetta: Codice esito
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: currCUTRespDate
        etichetta: Data Esito CUI Fine Lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: currCUTRespDocumentStatus
        etichetta: Esito CUI Fine Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: currCUTRespInfo
        etichetta: Descrizione esito CUI Fine Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: currCUTRespProg
        etichetta: Nr. progressivo esito CUI Fine Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: VERIFICA_CHIUSURA
    action: DOCUMENTAZIONE_NON_RICHIESTA
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: VERIFICA_CHIUSURA
    action: DOCUMENTAZIONE_RICHIESTA
    stato_finale: VERIFICA_DOCUMENTAZIONE
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: RIPRESA_RESPINTA
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancellationDate
        etichetta: Data annullamento
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
  - stato_iniziale: RIPRESA_RESPINTA
    action: ANNULLAMENTO_INVIO
    stato_finale: SOSPESA
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: RIPRESA_RESPINTA
    action: RIPRESA
    stato_finale: ATTESA_CONFERMA_RIPRESA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: centralOf
        etichetta: Centrale di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"technicalSiteDesc\"]"
          }
      - posizione: 1
        descrizione: ftcName
        etichetta: Funzione territoriale competente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"central\"]"
          }
      - posizione: 2
        descrizione: technicalArea
        etichetta: Settore tecnico
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'ImpiantiCommutazione,ImpiantiTrasmissione,LavoriContoEsercizio,LavoriVari,ReteDistribuzioneCavi,RetiSpeciali,SistemiAlimentazioneCondizionamento,SistemiGestione'
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "ReteDistribuzioneCavi"
          }
      - posizione: 3
        descrizione: execProg
        etichetta: Progetto esecutivo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: execProgRev
        etichetta: Revisione progetto esecutivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 5
        descrizione: zeroCostActivity
        etichetta: Lavoro costo zero
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: zeroCostActivityDate
        etichetta: Data lavoro costo zero
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: activityNote
        etichetta: Descrizione lavori
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"networkDesc\"]"
          }
      - posizione: 8
        descrizione: versionDescription
        etichetta: Causale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "PrimaComunicazione"
          }
      - posizione: 9
        descrizione: technology
        etichetta: Tecnica
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 - DWDM'
      - posizione: 10
        descrizione: startDate
        etichetta: Data inizio lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 11
        descrizione: presumedEndDate
        etichetta: Data presunta fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"forecastEndDateActual\"]"
          }
      - posizione: 12
        descrizione: odaNumber
        etichetta: ODA
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"workOrderId\"]"
          }
      - posizione: 13
        descrizione: subCode
        etichetta: Codice ditta sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 14
        descrizione: subDescription
        etichetta: Descrizione ditta sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 15
        descrizione: subAccountableName
        etichetta: Nome persona di riferimento sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 16
        descrizione: subAccountableSurname
        etichetta: Cognome persona di riferimento sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 17
        descrizione: subPhone
        etichetta: Telefono sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: subFax
        etichetta: Fax sub
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 19
        descrizione: subDate
        etichetta: Data decorrenza sub
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 20
        descrizione: testRequest
        etichetta: Validità CIL come richiesta collaudo
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 21
        descrizione: dateDocCIL
        etichetta: Data emissione CIL
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 22
        descrizione: progCIL
        etichetta: Nr. progressivo CIL
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 23
        descrizione: accountableSurname
        etichetta: Cognome responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: Y
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"lastName\"]"
          }
      - posizione: 24
        descrizione: accountableName
        etichetta: Nome responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: Y
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"firstName\"]"
          }
      - posizione: 25
        descrizione: accountablePhoneNumber
        etichetta: Telefono responsabile attività
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"info\"][\"currentUserObj\"][\"mobilePhone\"]"
          }
      - posizione: 26
        descrizione: accountableDate
        etichetta: Data decorrenza responsabile attività
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 27
        descrizione: IOCDate
        etichetta: 'IOC Data inizio opere civili (IMPORTANTE: da valorizzare in presenza di opere civili)'
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 28
        descrizione: IOCAddress
        etichetta: 'IOC Via (IMPORTANTE: da valorizzare in presenza di opere civili)'
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 29
        descrizione: externalDocumentType
        etichetta: Tipo documento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 30
        descrizione: currCIRDateDoc
        etichetta: Data emissione CIL Ripresa
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 31
        descrizione: currCIRprog
        etichetta: Nr. progressivo CIL Ripresa
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 32
        descrizione: currCIRRespDate
        etichetta: Data Esito CIL Ripresa
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 33
        descrizione: currCIRRespDocumentStatus
        etichetta: Esito CIL Ripresa
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 34
        descrizione: currCIRRespInfo
        etichetta: Descrizione esito CIL Ripresa
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 35
        descrizione: currCIRRespProg
        etichetta: Nr. progressivo esito CIL Ripresa
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: REINVIO_FINE_LAVORI_RESPINTO
    action: ANNULLAMENTO_INVIO
    stato_finale: RAPPORTO_LAVORI_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: REINVIO_FINE_LAVORI_RESPINTO
    action: FINE_LAVORI
    stato_finale: ATTESA_CONFERMA_REINVIO_FINE_LAVORI
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: centralOf
        etichetta: Centrale di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"technicalSiteDesc\"]"
          }
      - posizione: 1
        descrizione: cluName
        etichetta: Centro di lavoro unificato di
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"central\"]"
          }
      - posizione: 2
        descrizione: dateDocCUI
        etichetta: Data emissione CUI
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 3
        descrizione: endDate
        etichetta: Data fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 4
        descrizione: shipmentType
        etichetta: Tipo consegna
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: execProg
        etichetta: Progetto esecutivo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: execProgRev
        etichetta: Revisione progetto esecutivo
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 7
        descrizione: technology
        etichetta: Tecnica
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '01 - DWDM'
      - posizione: 8
        descrizione: particularNotes
        etichetta: Segnalazioni particolari
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: partyAtTest
        etichetta: Personale Telecom
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Presente,Non presente'
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "Non presente"
          }
      - posizione: 10
        descrizione: testDate
        etichetta: Data collaudo
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"endWorkDateStep\"]"
          }
      - posizione: 11
        descrizione: advancementPercent
        etichetta: Percentuale avanzamento
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 12
        descrizione: odaNumber
        etichetta: ODA
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: Y
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"workOrderId\"]"
          }
      - posizione: 13
        descrizione: moiValue
        etichetta: Importo manodopera (Euro)
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00"
          }
      - posizione: 14
        descrizione: fornitureValue
        etichetta: Importo fornitura (Euro)
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0.00"
          }
      - posizione: 15
        descrizione: productionValue
        etichetta: Importo produzione (Euro)
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 16
        descrizione: activityNote
        etichetta: Descrizione lavori
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"properties\"][\"networkDesc\"]"
          }
      - posizione: 17
        descrizione: progCUI
        etichetta: Nr. progressivo CUI
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: tdta0001
        etichetta: Dichiarazione di conformità DLgs 37/2008
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 19
        descrizione: tdta0002
        etichetta: Rapportazione lavori / Riepilogo prestazioni
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 20
        descrizione: tdta0003
        etichetta: Formulario identificazione rifiuti
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 21
        descrizione: tdta0004
        etichetta: Materiale di scorta
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 22
        descrizione: tdta0005
        etichetta: Documentazione tecnica / manuali apparato
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 23
        descrizione: tdta0006
        etichetta: Norma/e di collaudo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 24
        descrizione: tdta0007
        etichetta: Verbale di collaudo "Stand Alone"
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 25
        descrizione: tdta0008
        etichetta: Verbali e/o Modelli di collaudo specifici
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 26
        descrizione: tdta0009
        etichetta: Rapporto prova (per i Sistemi di Alimentazione e CDZ)
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 27
        descrizione: tdta0010
        etichetta: Autocertificazione di collaudo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 28
        descrizione: docs
        etichetta: Documenti Network
        tipo_ui: 'JSON  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 29
        descrizione: tdta0012
        etichetta: 2 Copie di cartografia agg.
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 30
        descrizione: tdta0013
        etichetta: Fatt. relative a forn. locali
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 31
        descrizione: tdta0014
        etichetta: Misure E/O e/o P. o autocertificaz.
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 32
        descrizione: tdta0015
        etichetta: Cp. Stp "Riep. op. in eser"
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 33
        descrizione: tdta0016
        etichetta: Modello rete cablata
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 34
        descrizione: tdta0017
        etichetta: Modello area di influenza
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 35
        descrizione: tdta0018
        etichetta: El. riep. pres e mat for.
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 36
        descrizione: sendWithoutPeople
        etichetta: Invia documento senza elenco persone accesso in centrale
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "const",
          "value": "0"
          }
      - posizione: 37
        descrizione: people
        etichetta: Elenco delle persone per l'accesso
        tipo_ui: 'JSON  '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 38
        descrizione: cartographicAlphanumericUpdate
        etichetta: Aggiornamento cartografico/alfanumerico/dinamyc
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Nessuno,Alfanumerico,Cartografico,Dynamic,Alfanumerico/Cartografico,Alfanumerico/Dynamic,Cartografico/Dynamic,Alfanumerico/Cartografico/Dynamic'
      - posizione: 39
        descrizione: externalDocumentType
        etichetta: Tipo documento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 40
        descrizione: currCUTDateDoc
        etichetta: Data emissione CUI Fine Lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 41
        descrizione: currCUTprog
        etichetta: Nr. progressivo CUI Fine Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 42
        descrizione: currCUTRespDate
        etichetta: Data Esito CUI Fine Lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 43
        descrizione: currCUTRespDocumentStatus
        etichetta: Esito CUI Fine Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 44
        descrizione: currCUTRespInfo
        etichetta: Descrizione esito CUI Fine Lavori
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 45
        descrizione: currCUTRespProg
        etichetta: Nr. progressivo esito CUI Fine Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: REINVIO_FINE_LAVORI_RESPINTO
    action: RAPPORTO_LAVORI_CONCLUSO
    stato_finale: VERIFICA_CHIUSURA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: VERIFICA_DOCUMENTAZIONE
    action: AGGIORNAMENTO_ARMADIO
    stato_finale: VERIFICA_DOCUMENTAZIONE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cabinet
        etichetta: Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Inserire l'armadio con tre caratteri numerici oppure tre caratteri numerici seguiti da '/' e dai tre caratteri numerici dell'ID CNO. Il dato è obbligatorio se il campo 'Scopo Network' è valorizzato con 'Gestione armadio'
  - stato_iniziale: VERIFICA_DOCUMENTAZIONE
    action: AGGIORNAMENTO_SITI_5G
    stato_finale: VERIFICA_DOCUMENTAZIONE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: networkSiteType
        etichetta: Tipologia Sito Network
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Primaria,Realizzazione sito,Ripristino,Realizzazione sito + Ripristino,Altro'
      - posizione: 1
        descrizione: siteIdList
        etichetta: Elenco siti
        tipo_ui: MOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "URI",
            "URI": "../../../customers/TIM/contracts/CREATION/so/sites/lookups/siteIdList",
            "LAYOUT": "AUTOCOMPLETE",
            "CONTEXT": "Config",
            "ADD_FROM_AUTOCOMPLETE_ONLY": true
          }
  - stato_iniziale: VERIFICA_DOCUMENTAZIONE
    action: CHIUSURA
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: VERIFICA_DOCUMENTAZIONE
    action: DOCUMENTAZIONE_AGGIORNAMENTO_BANCA_DATI
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - AT
      - ROOT
