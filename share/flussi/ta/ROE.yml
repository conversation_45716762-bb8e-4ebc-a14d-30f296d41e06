---
tipo_attivita: ROE
tipi_sistema:
  - ROE
transizioni:
  - stato_iniziale: START
    action: APERTURA
    stato_finale: APERTA
    gruppi:
      - ROOT
    tdta:
      - posizione: 0
        descrizione: networkId
        etichetta: Id network
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: requestId
        etichetta: Id attivita caricamento
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: requestorName
        etichetta: Utente richiedente
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: ROE
        etichetta: ROE
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: CNO
        etichetta: CNO
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: address
        etichetta: Indirizzo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: city
        etichetta: Città
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: province
        etichetta: Provincia
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 8
        descrizione: zipCode
        etichetta: CAP
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: copperDistributor
        etichetta: Distributore rame
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 10
        descrizione: RO
        etichetta: RO
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 11
        descrizione: AOL
        etichetta: AOL
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 12
        descrizione: centralId
        etichetta: ID centrale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 13
        descrizione: central
        etichetta: Descrizione centrale
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 14
        descrizione: ROEType
        etichetta: Tipo ROE
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Esterno,Interno,Pozzetto,Colonnina,ROE PTE Colonnina,ROE PTE Interno,ROE PTE Muro Esterno,ROE PTE Muro Interno,ROE PTE Sotterraneo'
      - posizione: 15
        descrizione: ROEPosition
        etichetta: Posizione ROE
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Terminale,Passante'
      - posizione: 16
        descrizione: typology
        etichetta: Tipo attività
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 17
        descrizione: specialProject
        etichetta: Progetto speciale
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 18
        descrizione: ring
        etichetta: Anello
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 19
        descrizione: FORing
        etichetta: FO anello
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 20
        descrizione: primarySplitter
        etichetta: Splitter primario
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 21
        descrizione: secondarySplitterCount
        etichetta: N. Splitter Second.
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 22
        descrizione: CLLI
        etichetta: CLLI
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 23
        descrizione: UI
        etichetta: UI
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 24
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 25
        descrizione: seat
        etichetta: Sede
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 26
        descrizione: teamAssistant
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 27
        descrizione: customerAssistant
        etichetta: Ass. Cliente
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 28
        descrizione: note
        etichetta: Note
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 29
        descrizione: geoCoordinates
        etichetta: Coordinate
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 30
        descrizione: geoCoordinatesWarning
        etichetta: Warning coordinate
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 31
        descrizione: potential
        etichetta: Potenzialita
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'ROE 16,ROE 16 (SOCRATE),ROE 32,ROE 32 (SOCRATE),ROE 48 MTCVO,ROE POZZETTO,PTE Small,PTE Large'
      - posizione: 32
        descrizione: WBELevel3
        etichetta: WBE 3 Livello
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 33
        descrizione: missingNetwork
        etichetta: Network mancante
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 34
        descrizione: ROEManagementType
        etichetta: Tipo gestione ROE
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 35
        descrizione: ameliaId
        etichetta: ID_AMELIA
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 36
        descrizione: mail
        etichetta: Mail
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 37
        descrizione: primarySplitterFW
        etichetta: Splitter primario FW
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 38
        descrizione: secondaySplitterFW
        etichetta: Splitter second. FW
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 39
        descrizione: ROEProposal
        etichetta: Proposta ROE
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 40
        descrizione: __DTC_DOF
        etichetta: DOC. DOCUMENTAZIONE FOTOGRAFICA
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 41
        descrizione: __DTC_VAR
        etichetta: DOC. VARIE
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 42
        descrizione: OI
        etichetta: O.I.
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 43
        descrizione: commessa
        etichetta: Commessa
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_DATI
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: primarySplitter
        etichetta: Splitter primario
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: CNO
        etichetta: CNO
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: ROE
        etichetta: ROE
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: address
        etichetta: Indirizzo
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: copperDistributor
        etichetta: Distributore rame
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: UI
        etichetta: UI
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: ROEType
        etichetta: Tipo ROE
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Esterno,Interno,Pozzetto,Colonnina,ROE PTE Colonnina,ROE PTE Interno,ROE PTE Muro Esterno,ROE PTE Muro Interno,ROE PTE Sotterraneo'
      - posizione: 7
        descrizione: ROEPosition
        etichetta: Posizione ROE
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Terminale,Passante'
      - posizione: 8
        descrizione: specialProject
        etichetta: Progetto speciale
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 9
        descrizione: ring
        etichetta: Anello
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 10
        descrizione: FORing
        etichetta: FO anello
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 11
        descrizione: secondarySplitterCount
        etichetta: N. Splitter Second.
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 12
        descrizione: CLLI
        etichetta: CLLI
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 13
        descrizione: potential
        etichetta: Potenzialita
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'ROE 16,ROE 16 (SOCRATE),ROE 32,ROE 32 (SOCRATE),ROE 48 MTCVO,ROE POZZETTO,PTE Small,PTE Large'
      - posizione: 14
        descrizione: geoCoordinates
        etichetta: Coordinate
        tipo_ui: LATLNG
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 15
        descrizione: geoCoordinatesWarning
        etichetta: Warning coordinate
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_LAVORO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: updateType
        etichetta: Tipo Aggiornamento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: workSuspensionReason
        etichetta: Motivo sospensione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_PERMESSO_BUILDING
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: privatePermitsActivityId
        etichetta: Id Attivita Permesso Privato
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: updateType
        etichetta: Tipo Aggiornamento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: privatePermitsSuspensionReason
        etichetta: Motivo sospensione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_PERMESSO_LAVORI
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: publicPermitsActivityId
        etichetta: Id Attivita Permesso Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: updateType
        etichetta: Tipo Aggiornamento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: publicPermitsSuspensionReason
        etichetta: Motivo sospensione
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNA_ODS
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: ODS
        etichetta: OdS
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Ordine Di Servizio
  - stato_iniziale: ___ANY_STATUS___
    action: APERTURA_LAVORO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: externalSequence
        etichetta: Progressivo external sequence
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: APERTURA_PERMESSO_BUILDING
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: privatePermitsActivityId
        etichetta: Id Attivita Permesso Privato
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: APERTURA_PERMESSO_LAVORI
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: publicPermitsActivityId
        etichetta: Id Attivita Permesso Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: ASSOCIA_NETWORK_A_MISURA
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: networkId
        etichetta: Id network
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_LAVORO_KO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: externalSequence
        etichetta: Progressivo external sequence
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_LAVORO_OK
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workActivityId
        etichetta: Id Attivita Lavoro
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: externalSequence
        etichetta: Progressivo external sequence
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_PERMESSO_BUILDING_KO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: privatePermitsActivityId
        etichetta: Id Attivita Permesso Privato
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_PERMESSO_BUILDING_OK
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: privatePermitsActivityId
        etichetta: Id Attivita Permesso Privato
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_PERMESSO_LAVORI_KO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: publicPermitsActivityId
        etichetta: Id Attivita Permesso Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: CHIUSURA_PERMESSO_LAVORI_OK
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: publicPermitsActivityId
        etichetta: Id Attivita Permesso Lavori
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: TRASFORMA_A_GARA
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: ROEManagementType
        etichetta: Tipo gestione ROE
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: TRASFORMA_A_MISURA
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: ROEManagementType
        etichetta: Tipo gestione ROE
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: APERTA
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancelReason
        etichetta: Motivo Annullamento
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: APERTA
    action: PRESA_IN_CARICO
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 1
        descrizione: teamAssistant
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: DA_ESEGUIRE
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancelReason
        etichetta: Motivo Annullamento
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: DA_ESEGUIRE
    action: AVVIO_LAVORI
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: DA_ESEGUIRE
    action: PRESA_IN_CARICO
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
      - posizione: 1
        descrizione: teamAssistant
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: DA_ESEGUIRE
    action: SOSPENSIONE
    stato_finale: SOSPESA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: suspensionReasonROE
        etichetta: Motivo sospensione ROE
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Mancanza permesso privato,Mancanza permesso Ente,Problemi infrastruttura,Altro'
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: cancelReason
        etichetta: Motivo Annullamento
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: GIUNZIONE_EFFETTUATA
    stato_finale: CERTIFICAZIONE_E_COLLAUDO
    gruppi:
      - ADMIN
      - AT
      - PM
      - ROOT
  - stato_iniziale: LAVORAZIONE_IN_CORSO
    action: SOSPENSIONE
    stato_finale: SOSPESA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: suspensionReasonROE
        etichetta: Motivo sospensione ROE
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Mancanza permesso privato,Mancanza permesso Ente,Problemi infrastruttura,Altro'
  - stato_iniziale: SOSPESA
    action: RIPRESA
    stato_finale: RIPRESA
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: CERTIFICAZIONE_E_COLLAUDO
    action: CHIUSURA
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: CERTIFICAZIONE_E_COLLAUDO
    action: RIPRISTINO_NECESSARIO
    stato_finale: ATTESA_RIPRISTINO
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: CERTIFICAZIONE_E_COLLAUDO
    action: RIPRISTINO_NON_NECESSARIO
    stato_finale: ATTESA_ASSOCIAZIONE_NETWORK
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: CERTIFICAZIONE_E_COLLAUDO
    action: SOSPENSIONE
    stato_finale: SOSPESA
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: suspensionReasonROE
        etichetta: Motivo sospensione ROE
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Mancanza permesso privato,Mancanza permesso Ente,Problemi infrastruttura,Altro'
  - stato_iniziale: RIPRESA
    action: RIPRESA_CERTIFICAZIONE_E_COLLAUDO
    stato_finale: CERTIFICAZIONE_E_COLLAUDO
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: RIPRESA
    action: RIPRESA_DA_ESEGUIRE
    stato_finale: DA_ESEGUIRE
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: RIPRESA
    action: RIPRESA_LAVORAZIONE_IN_CORSO
    stato_finale: LAVORAZIONE_IN_CORSO
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: ATTESA_RIPRISTINO
    action: RIPRISTINATO
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: ATTESA_RIPRISTINO
    action: RIPRISTINATO_DA_ASSOCIARE_A_NETWORK
    stato_finale: ATTESA_ASSOCIAZIONE_NETWORK
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: ATTESA_ASSOCIAZIONE_NETWORK
    action: CHIUSURA
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: ATTESA_ASSOCIAZIONE_NETWORK
    action: NETWORK_ASSOCIATA
    stato_finale: ATTESA_PAGAMENTO
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: networkId
        etichetta: Id network
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: missingNetwork
        etichetta: Network mancante
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_PAGAMENTO
    action: PAGAMENTO_AVVENUTO
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: ATTESA_PAGAMENTO
    action: PAGAMENTO_NEGATO
    stato_finale: ATTESA_ASSOCIAZIONE_NETWORK
    gruppi:
      - ADMIN
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: networkId
        etichetta: Id network
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
