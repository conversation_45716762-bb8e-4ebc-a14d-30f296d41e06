---
tipo_attivita: WO001
tipi_sistema:
  - WO001
transizioni:
  - stato_iniziale: START
    action: APERTURA
    stato_finale: APERTA
    gruppi:
      - ADMIN
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: customerId
        etichetta: Id Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: contractId
        etichetta: Id Contratto
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: id
        etichetta: Id
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: |-
          {
          "type": "bracketNotation",
          "value": "[\"system\"][\"properties\"][\"id\"]"
          }
      - posizione: 3
        descrizione: companyAbbreviation
        etichetta: Sigla azienda
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: externalWorkTypeId
        etichetta: Tipo Avviso / Tipo Intervento
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: dt0001
        etichetta: Regione
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: "ABRUZZO,CALABRIA,CAMPANIA,EMILIA ROMAGNA,FRIULI VENEZIA GIULIA,LAZIO,LIGURIA,LOMBARDIA,MARCHE,MATERA,MOLISE,PIEMONTE,POTENZA,PUGLIA,SALERNO,SARDEGNA,SICILIA,TOSCANA,TRENTINO ALTO ADIGE,UMBRIA,VALLE D'AOSTA,VENETO"
      - posizione: 6
        descrizione: dt0002
        etichetta: Area Sirti
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: 'CE,NE,NO,SU,S1'
      - posizione: 7
        descrizione: dt0020
        etichetta: Area Cliente
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
        valori: 'CE,NE,NO,SU'
      - posizione: 8
        descrizione: dt0004
        etichetta: Centro lavoro ordine
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: '496407,496411,496415,496426,496428,496429,496439,496454,496455,496457,496466,496470,496480,496481,496485,496486,496487'
      - posizione: 9
        descrizione: dt0006
        etichetta: Descrizione
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 10
        descrizione: dt0007
        etichetta: Disponibilità progetto W3
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'DISPONIBILE,NON APPLICABILE,NON DISPONIBILE'
      - posizione: 11
        descrizione: dt0008
        etichetta: Tipo progetto W3
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'PEIAF,PERAF,PMRAF'
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Obbligatorio se 'Disponibilità progetto' = 'DISPONIBILE'
      - posizione: 12
        descrizione: dt0009
        etichetta: Codice di progetto W3
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Obbligatorio se 'Disponibilità progetto' = 'DISPONIBILE'
      - posizione: 13
        descrizione: dt0010
        etichetta: Codice di progetto W3 normalizzato
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 14
        descrizione: dt0011
        etichetta: Codice sito
        tipo_ui: LOOKUP
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "TYPE": "URI",
            "URI": "../../../customers/CUSTOMER001/contracts/CONTRACT001/so/wo001s/lookups/bts",
            "LAYOUT": "AUTOCOMPLETE",
            "CONTEXT": "Config",
            "ADD_FROM_AUTOCOMPLETE_ONLY": true,
            "CONTEXT_KEY": "SiNFOWPSOCOREConfigPage",
            "DISPLAY_PROPERTY": "site",
            "KEY_PROPERTY": "siteCode",
            "RESPONSE_DATA_KEY": "siteName",
            "QUERY_KEY": "siteName"
          }
      - posizione: 15
        descrizione: dt0012
        etichetta: Cliente
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 16
        descrizione: dt0013
        etichetta: Codice fattibilità
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: ''
        suggerimento: Obbligatorio se 'Cliente' è valorizzato
      - posizione: 17
        descrizione: dt0014
        etichetta: Data avvio
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: ''
        valore_default: ''
        valore_predefinito: "{\n\"type\": \"now\"\n}"
      - posizione: 18
        descrizione: __DTC_MDI
        etichetta: 'Mail di ingaggio: numero allegati'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 19
        descrizione: __DTC_PWI
        etichetta: 'Progetto Wind: numero allegati'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 20
        descrizione: __DTC_VSO
        etichetta: 'Verbale Sopralluogo: numero allegati'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 21
        descrizione: __DTC_KLP
        etichetta: 'Kit List Preventivo: numero allegati'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 22
        descrizione: __DTC_RAU
        etichetta: 'Richiesta Autorizzazione: numero allegati'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 23
        descrizione: __DTC_AUT
        etichetta: 'Autorizzazione: numero allegati'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 24
        descrizione: __DTC_ASB
        etichetta: 'As-Built: numero allegati'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 25
        descrizione: __DTC_VCO
        etichetta: 'Verbale Collaudo: numero allegati'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 26
        descrizione: __DTC_KLC
        etichetta: 'Kit List Consuntivo: numero allegati'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 27
        descrizione: __DTC_CILAV
        etichetta: 'Comunicazione Inizio Lavori: numero allegati'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 28
        descrizione: __DTC_CUIMP
        etichetta: 'Comunicazione Ultimazione Impianto: numero allegati'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 29
        descrizione: __DTC_ALT
        etichetta: ALTRO
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
  - stato_iniziale: ___ANY_STATUS___
    action: ANNULLAMENTO_CLIENTE
    stato_finale: ANNULLATA_CLIENTE
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
  - stato_iniziale: ___ANY_STATUS___
    action: CREAZIONE_AMBITO_OPERATIVO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dt0003
        etichetta: Macroattività ordine
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'LWMAN,LWRFI,M7MAKE,M8BUY,SLICE'
      - posizione: 1
        descrizione: dt0005
        etichetta: Piano
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'ADJ,ALTRO MAN,APP,BTS,BUY,CLIENTE,DAS,GPON,MAKE,MAN,OLO,PNRR,SLICE-A,SLICE-B,SLICE-C,SLICE_NE,SPOST,UPG'
      - posizione: 2
        descrizione: dt0038
        etichetta: Riferimento ambito operativo
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: KIT_LIST
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dt0042
        etichetta: Data Kit List
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: dt0043
        etichetta: Tipo Kit List
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'PREVENTIVO,CONSUNTIVO'
  - stato_iniziale: ___ANY_STATUS___
    action: MODIFICA_DATI
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dt0044
        etichetta: Fatta Fideiussione
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: dt0045
        etichetta: Data Inizio Lavori DL Wind
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 2
        descrizione: dt0046
        etichetta: Data Fine Lavori DL Wind
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
  - stato_iniziale: ___ANY_STATUS___
    action: NOTIFICA_EMAIL
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: mailingList
        etichetta: "Elenco email utenti riferimento (separato da ',')"
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
  - stato_iniziale: ___ANY_STATUS___
    action: NUOVA_DOCUMENTAZIONE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
  - stato_iniziale: APERTA
    action: ASSEGNAZIONE_AD_ALTRO_AT
    stato_finale: FATTIBILITA_PROGETTO
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: APERTA
    action: PRESA_IN_CARICO
    stato_finale: FATTIBILITA_PROGETTO
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: FATTIBILITA_PROGETTO
    action: ASSEGNAZIONE_AD_ALTRO_AT
    stato_finale: FATTIBILITA_PROGETTO
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: FATTIBILITA_PROGETTO
    action: FATTIBILITA_KO
    stato_finale: FATTIBILITA_PROGETTO
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
  - stato_iniziale: FATTIBILITA_PROGETTO
    action: PERMESSI_NON_NECESSARI
    stato_finale: PERMESSI_NON_NECESSARI
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dt0015
        etichetta: Permessi necessari
        tipo_ui: 'BOOL  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: FATTIBILITA_PROGETTO
    action: RICHIESTA_PERMESSI
    stato_finale: ITER_PERMESSI
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dt0015
        etichetta: Permessi necessari
        tipo_ui: 'BOOL  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: dt0016
        etichetta: Data richiesta permessi
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: FATTIBILITA_PROGETTO
    action: SOSPENSIONE_LAVORI
    stato_finale: SOSPESA
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dt0018
        etichetta: Data sospensione
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: dt0019
        etichetta: Causale sospensione
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'CAUSALE_SOSPENSIONE_1, CAUSALE_SOSPENSIONE_2, CAUSALE_SOSPENSIONE_3'
  - stato_iniziale: PERMESSI_NON_NECESSARI
    action: INIZIO_LAVORI
    stato_finale: LAVORI
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dt0022
        etichetta: Data inizio lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ITER_PERMESSI
    action: PERMESSI_OTTENUTI
    stato_finale: PERMESSI_OTTENUTI
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dt0017
        etichetta: Data ottenimento permessi
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ITER_PERMESSI
    action: RICHIESTA_PERMESSI
    stato_finale: ITER_PERMESSI
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dt0015
        etichetta: Permessi necessari
        tipo_ui: 'BOOL  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: dt0016
        etichetta: Data richiesta permessi
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ITER_PERMESSI
    action: SOSPENSIONE_LAVORI
    stato_finale: SOSPESA
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dt0018
        etichetta: Data sospensione
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: dt0019
        etichetta: Causale sospensione
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'CAUSALE_SOSPENSIONE_1, CAUSALE_SOSPENSIONE_2, CAUSALE_SOSPENSIONE_3'
  - stato_iniziale: SOSPESA
    action: RIPRESA
    stato_finale: SMISTAMENTO_RIPRESA
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dt0023
        etichetta: Data ripresa
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: LAVORI
    action: ASSEGNAZIONE_AD_ALTRO_AT
    stato_finale: LAVORI
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: LAVORI
    action: LAVORI_CONCLUSI
    stato_finale: DOCUMENTAZIONE_E_CONSUNTIVAZIONE
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dt0026
        etichetta: Data fine lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: LAVORI
    action: SOSPENSIONE_LAVORI
    stato_finale: SOSPESA
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dt0018
        etichetta: Data sospensione
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: dt0019
        etichetta: Causale sospensione
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'CAUSALE_SOSPENSIONE_1, CAUSALE_SOSPENSIONE_2, CAUSALE_SOSPENSIONE_3'
  - stato_iniziale: PERMESSI_OTTENUTI
    action: INIZIO_LAVORI
    stato_finale: LAVORI
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dt0022
        etichetta: Data inizio lavori
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: SMISTAMENTO_RIPRESA
    action: TORNA_IN_FATTIBILITA_PROGETTO
    stato_finale: FATTIBILITA_PROGETTO
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: SMISTAMENTO_RIPRESA
    action: TORNA_IN_ITER_PERMESSI
    stato_finale: ITER_PERMESSI
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: SMISTAMENTO_RIPRESA
    action: TORNA_IN_LAVORI
    stato_finale: LAVORI
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: DOCUMENTAZIONE_E_CONSUNTIVAZIONE
    action: AS_BUILT
    stato_finale: DOCUMENTAZIONE_E_CONSUNTIVAZIONE
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dt0039
        etichetta: Data As Built
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
  - stato_iniziale: DOCUMENTAZIONE_E_CONSUNTIVAZIONE
    action: CHIUSURA
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
  - stato_iniziale: DOCUMENTAZIONE_E_CONSUNTIVAZIONE
    action: NE
    stato_finale: DOCUMENTAZIONE_E_CONSUNTIVAZIONE
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dt0040
        etichetta: Data NE
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
  - stato_iniziale: DOCUMENTAZIONE_E_CONSUNTIVAZIONE
    action: PENDING
    stato_finale: PENDING
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
  - stato_iniziale: DOCUMENTAZIONE_E_CONSUNTIVAZIONE
    action: RIPRISTINO
    stato_finale: DOCUMENTAZIONE_E_CONSUNTIVAZIONE
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dt0041
        etichetta: Data ripristino
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
  - stato_iniziale: DOCUMENTAZIONE_E_CONSUNTIVAZIONE
    action: VERBALE
    stato_finale: DOCUMENTAZIONE_E_CONSUNTIVAZIONE
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
    tdta:
      - posizione: 0
        descrizione: dt0025
        etichetta: Data verbale
        tipo_ui: 'DAY   '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
  - stato_iniziale: PENDING
    action: CHIUSURA
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - AT04
      - PM04
      - ROOT
