---
tipo_attivita: WORKING_GROUP_CODE
tipi_sistema:
  - WORKING_GROUP_CODE
transizioni:
  - stato_iniziale: START
    action: APERTURA
    stato_finale: APERTA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: companyAbbreviation
        etichetta: Sigla azienda
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: |-
          {
            "TYPE": "SYSTEM_PROPERTY",
            "LAYOUT": "SELECT",
            "CLASS": "workingGroupCodeList",
            "TARGET": "workingGroupCodeList"
          }
  - stato_iniziale: APERTA
    action: ATTIVAZIONE
    stato_finale: ATTIVO
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: ATTIVO
    action: AGGIORNA_MAILING_LIST
    stato_finale: ATTIVO
    gruppi:
      - ADMIN
      - AT
      - PM
      - PM02
      - PM03
      - ROOT
    tdta:
      - posizione: 0
        descrizione: mailingList
        etichetta: "Elenco email utenti riferimento (separato da ',')"
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
  - stato_iniziale: ATTIVO
    action: CHIUSURA
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - ROOT
