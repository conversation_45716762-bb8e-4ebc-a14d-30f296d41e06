---
nome_tipo_sistema: EL02
descrizione: Elemento Livello 02 FiberCop
manutenibile: Y
prefisso_import_automatico: ~
tdt:
  - nome: address
    descrizione: Indirizzo
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: AOLId
    descrizione: Identificativo AOL
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: cabinet
    descrizione: Armadio
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: cabinetId
    descrizione: Id Armadio
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: cabinetType
    descrizione: Tipo Elemento Livello 01 FiberCop
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: canOpenPermits
    descrizione: canOpenPermits
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: canOpenWorks
    descrizione: canOpenWorks
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: central
    descrizione: Descrizione centrale
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: centralId
    descrizione: ID centrale
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: city
    descrizione: Città
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: CLLI
    descrizione: CLLI
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: cluster
    descrizione: Lotto
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: clusterJobReport
    descrizione: Cluster
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: contractId
    descrizione: Id Contratto
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: copperCabinet
    descrizione: Armadio rame
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: copperDistributor
    descrizione: Distributore rame
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: country
    descrizione: Stato
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: customerAssistant
    descrizione: Ass. Cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: customerId
    descrizione: Id Cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: elementType
    descrizione: Tipo Elemento Livello 02 FiberCop
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: externalAccountingType
    descrizione: Tipo contabilità
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: externalActivity
    descrizione: Descrizione attivita
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: externalActivityId
    descrizione: Attivita
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: externalId
    descrizione: externalId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: externalMacroActivity
    descrizione: Descrizione Macroattivita
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: externalMacroActivityId
    descrizione: Macroattivita
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: externalSequence
    descrizione: Progressivo external sequence
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: externalSyncKoReason
    descrizione: externalSyncKoReason
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: externalSyncStatus
    descrizione: externalSyncStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: externalWorkTypeId
    descrizione: Tipo Avviso / Tipo Intervento
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: FORing
    descrizione: FO anello
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: geoCoordinates
    descrizione: Coordinate
    tipo: LATLNG
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: geoCoordinatesWarning
    descrizione: Warning coordinate
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: id
    descrizione: Id
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: idCNO
    descrizione: Id CNO
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: location
    descrizione: Ubicazione
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: migFibercop
    descrizione: migrato Fibercop
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: networkBasedUpon
    descrizione: networkBasedUpon
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: note
    descrizione: Note
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: ODS
    descrizione: OdS
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: paymentNetwork
    descrizione: Network rendicontazione
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: paymentNetworkOld
    descrizione: Network rendicontazione precedente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: penetrationIndex
    descrizione: Fascia rendicontazione
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: planningFirm
    descrizione: Identificativo impresa progettazione
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: primarySplitter
    descrizione: Splitter primario
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: privatePermitsBooking
    descrizione: privatePermitsBooking
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: privatePermitsBookingStatus
    descrizione: privatePermitsBookingStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: privatePermitsBookingWarning
    descrizione: privatePermitsBookingWarning
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: privatePermitsClosedKO
    descrizione: privatePermitsClosedKO
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: privatePermitsClosedOK
    descrizione: privatePermitsClosedOK
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: privatePermitsLAMaker
    descrizione: privatePermitsLAMaker
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: privatePermitsLASubContractCode
    descrizione: privatePermitsLASubContractCode
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: privatePermitsLASubContractName
    descrizione: privatePermitsLASubContractName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: privatePermitsLATeamId
    descrizione: privatePermitsLATeamId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: privatePermitsLATeamName
    descrizione: privatePermitsLATeamName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: privatePermitsOnGoing
    descrizione: privatePermitsOnGoing
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: privatePermitsStatus
    descrizione: privatePermitsStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: privatePermitsTotal
    descrizione: privatePermitsTotal
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: province
    descrizione: Provincia
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: PTEPosition
    descrizione: Posizione PTE
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: PTEPotential
    descrizione: Potenzialita
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: PTEScope
    descrizione: Gestione PTE
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: PTEType
    descrizione: Tipo PTE
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: region
    descrizione: Regione
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: requestId
    descrizione: Id attivita caricamento
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: requestorName
    descrizione: Utente richiedente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: ring
    descrizione: Anello
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: RoId
    descrizione: Identificativo RO
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: seat
    descrizione: Sede
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: secondarySplitterCount
    descrizione: N. Splitter Second.
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: specialProject
    descrizione: Progetto speciale
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: subcontractItem
    descrizione: Identificativo Item
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: teamAssistant
    descrizione: Ruolo Aziendale AT
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: teamAssistantName
    descrizione: Ass. Sirti
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: testDataMeta001
    descrizione: Nw NgNeer / NW WPSO
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: testDataMeta002
    descrizione: PTE in banca dati
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: testData001
    descrizione: Bando
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: testData002
    descrizione: Area di Influenza (AI)
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: testData003
    descrizione: Data Collaudo
    tipo: DATE
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: testData004
    descrizione: Data Connected
    tipo: DATE
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: testData005
    descrizione: Splitter Secondario (SS)
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: testData006
    descrizione: Splitter Primario (SP)
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: testData007
    descrizione: Fatturazione
    tipo: DATE
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: testData008
    descrizione: Data Test
    tipo: TIMESTAMP
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: testData009
    descrizione: Esito Collaudo
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: testData010
    descrizione: Presenza OTDR
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: testData011
    descrizione: Presenza Foto
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: testData012
    descrizione: Stato RCI
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: testData013
    descrizione: Data richiesta VUC
    tipo: TIMESTAMP
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: testData014
    descrizione: Id Stato VUC
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: testData015
    descrizione: Data VUC
    tipo: TIMESTAMP
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: testData016
    descrizione: Network NGNeer
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: testData017
    descrizione: OA
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: UI
    descrizione: UI
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: WBELevel3
    descrizione: WBE 3 Livello
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: WBELevel3Old
    descrizione: WBE 3 Livello precedente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wInfrastructureBooking
    descrizione: wInfrastructureBooking
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wInfrastructureBookingStatus
    descrizione: wInfrastructureBookingStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wInfrastructureBookingWarning
    descrizione: wInfrastructureBookingWarning
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wInfrastructureKO
    descrizione: wInfrastructureKO
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wInfrastructureLAMaker
    descrizione: wInfrastructureLAMaker
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wInfrastructureLAS
    descrizione: wInfrastructureLAS
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wInfrastructureLASubContractCode
    descrizione: wInfrastructureLASubContractCode
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wInfrastructureLASubContractName
    descrizione: wInfrastructureLASubContractName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wInfrastructureLATeamId
    descrizione: wInfrastructureLATeamId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wInfrastructureLATeamName
    descrizione: wInfrastructureLATeamName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wInfrastructureOK
    descrizione: wInfrastructureOK
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wInfrastructureOnFieldStatus
    descrizione: wInfrastructureOnFieldStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wInfrastructureStatus
    descrizione: wInfrastructureStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wInfrastructureSuspReason
    descrizione: wInfrastructureSuspReason
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wInfrastructureTotal
    descrizione: wInfrastructureTotal
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wJunctionBooking
    descrizione: wJunctionBooking
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wJunctionBookingStatus
    descrizione: wJunctionBookingStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wJunctionBookingWarning
    descrizione: wJunctionBookingWarning
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wJunctionKO
    descrizione: wJunctionKO
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wJunctionLAMaker
    descrizione: wJunctionLAMaker
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wJunctionLAS
    descrizione: wJunctionLAS
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wJunctionLASubContractCode
    descrizione: wJunctionLASubContractCode
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wJunctionLASubContractName
    descrizione: wJunctionLASubContractName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wJunctionLATeamId
    descrizione: wJunctionLATeamId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wJunctionLATeamName
    descrizione: wJunctionLATeamName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wJunctionOK
    descrizione: wJunctionOK
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wJunctionOnFieldStatus
    descrizione: wJunctionOnFieldStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wJunctionStatus
    descrizione: wJunctionStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wJunctionSuspReason
    descrizione: wJunctionSuspReason
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wJunctionTotal
    descrizione: wJunctionTotal
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wLayingBooking
    descrizione: wLayingBooking
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wLayingBookingStatus
    descrizione: wLayingBookingStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wLayingBookingWarning
    descrizione: wLayingBookingWarning
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wLayingKO
    descrizione: wLayingKO
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wLayingLAMaker
    descrizione: wLayingLAMaker
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wLayingLAS
    descrizione: wLayingLAS
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wLayingLASubContractCode
    descrizione: wLayingLASubContractCode
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wLayingLASubContractName
    descrizione: wLayingLASubContractName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wLayingLATeamId
    descrizione: wLayingLATeamId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wLayingLATeamName
    descrizione: wLayingLATeamName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wLayingOK
    descrizione: wLayingOK
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wLayingOnFieldStatus
    descrizione: wLayingOnFieldStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wLayingStatus
    descrizione: wLayingStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wLayingSuspReason
    descrizione: wLayingSuspReason
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wLayingTotal
    descrizione: wLayingTotal
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: workersCode
    descrizione: workersCode
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: Y
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: workingGroupCode
    descrizione: Centro Lavoro
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: workOrderId
    descrizione: Ordine cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: workOrderIdOld
    descrizione: Ordine cliente precedente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: workPerformance
    descrizione: Prestazione
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: worksClosedKO
    descrizione: worksClosedKO
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: worksClosedOK
    descrizione: worksClosedOK
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: worksOnGoing
    descrizione: worksOnGoing
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: worksTotal
    descrizione: worksTotal
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wPlanningBooking
    descrizione: wPlanningBooking
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wPlanningBookingStatus
    descrizione: wPlanningBookingStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wPlanningBookingWarning
    descrizione: wPlanningBookingWarning
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wPlanningKO
    descrizione: wPlanningKO
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wPlanningLAMaker
    descrizione: wPlanningLAMaker
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wPlanningLAS
    descrizione: wPlanningLAS
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wPlanningLASubContractCode
    descrizione: wPlanningLASubContractCode
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wPlanningLASubContractName
    descrizione: wPlanningLASubContractName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wPlanningLATeamId
    descrizione: wPlanningLATeamId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wPlanningLATeamName
    descrizione: wPlanningLATeamName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wPlanningOK
    descrizione: wPlanningOK
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wPlanningOnFieldStatus
    descrizione: wPlanningOnFieldStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wPlanningStatus
    descrizione: wPlanningStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wPlanningSuspReason
    descrizione: wPlanningSuspReason
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wPlanningTotal
    descrizione: wPlanningTotal
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wRestorationBooking
    descrizione: wRestorationBooking
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wRestorationBookingStatus
    descrizione: wRestorationBookingStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wRestorationBookingWarning
    descrizione: wRestorationBookingWarning
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wRestorationKO
    descrizione: wRestorationKO
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wRestorationLAMaker
    descrizione: wRestorationLAMaker
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wRestorationLAS
    descrizione: wRestorationLAS
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wRestorationLASubContractCode
    descrizione: wRestorationLASubContractCode
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wRestorationLASubContractName
    descrizione: wRestorationLASubContractName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wRestorationLATeamId
    descrizione: wRestorationLATeamId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wRestorationLATeamName
    descrizione: wRestorationLATeamName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wRestorationOK
    descrizione: wRestorationOK
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wRestorationOnFieldStatus
    descrizione: wRestorationOnFieldStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wRestorationStatus
    descrizione: wRestorationStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wRestorationSuspReason
    descrizione: wRestorationSuspReason
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wRestorationTotal
    descrizione: wRestorationTotal
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wSurveyBooking
    descrizione: wSurveyBooking
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wSurveyBookingStatus
    descrizione: wSurveyBookingStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wSurveyBookingWarning
    descrizione: wSurveyBookingWarning
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wSurveyKO
    descrizione: wSurveyKO
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wSurveyLAMaker
    descrizione: wSurveyLAMaker
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wSurveyLAS
    descrizione: wSurveyLAS
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wSurveyLASubContractCode
    descrizione: wSurveyLASubContractCode
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wSurveyLASubContractName
    descrizione: wSurveyLASubContractName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wSurveyLATeamId
    descrizione: wSurveyLATeamId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wSurveyLATeamName
    descrizione: wSurveyLATeamName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wSurveyOK
    descrizione: wSurveyOK
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wSurveyOnFieldStatus
    descrizione: wSurveyOnFieldStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wSurveyStatus
    descrizione: wSurveyStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wSurveySuspReason
    descrizione: wSurveySuspReason
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wSurveyTotal
    descrizione: wSurveyTotal
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestAppBooking
    descrizione: wTestAppBooking
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestAppBookingStatus
    descrizione: wTestAppBookingStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestAppBookingWarning
    descrizione: wTestAppBookingWarning
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestAppKO
    descrizione: wTestAppKO
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestAppLAMaker
    descrizione: wTestAppLAMaker
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestAppLAS
    descrizione: wTestAppLAS
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestAppLASubContractCode
    descrizione: wTestAppLASubContractCode
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestAppLASubContractName
    descrizione: wTestAppLASubContractName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestAppLATeamId
    descrizione: wTestAppLATeamId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestAppLATeamName
    descrizione: wTestAppLATeamName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestAppOK
    descrizione: wTestAppOK
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestAppOnFieldStatus
    descrizione: wTestAppOnFieldStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestAppStatus
    descrizione: wTestAppStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestAppSuspReason
    descrizione: wTestAppSuspReason
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestAppTotal
    descrizione: wTestAppTotal
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestOTDRBooking
    descrizione: wTestOTDRBooking
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestOTDRBookingStatus
    descrizione: wTestOTDRBookingStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestOTDRBookingWarning
    descrizione: wTestOTDRBookingWarning
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestOTDRKO
    descrizione: wTestOTDRKO
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestOTDRLAMaker
    descrizione: wTestOTDRLAMaker
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestOTDRLAS
    descrizione: wTestOTDRLAS
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestOTDRLASubContractCode
    descrizione: wTestOTDRLASubContractCode
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestOTDRLASubContractName
    descrizione: wTestOTDRLASubContractName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestOTDRLATeamId
    descrizione: wTestOTDRLATeamId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestOTDRLATeamName
    descrizione: wTestOTDRLATeamName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestOTDROK
    descrizione: wTestOTDROK
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestOTDROnFieldStatus
    descrizione: wTestOTDROnFieldStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestOTDRStatus
    descrizione: wTestOTDRStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestOTDRSuspReason
    descrizione: wTestOTDRSuspReason
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wTestOTDRTotal
    descrizione: wTestOTDRTotal
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF1Booking
    descrizione: wUpdateDatabaseF1Booking
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF1BookingStatus
    descrizione: wUpdateDatabaseF1BookingStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF1BookingWarning
    descrizione: wUpdateDatabaseF1BookingWarning
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF1KO
    descrizione: wUpdateDatabaseF1KO
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF1LAMaker
    descrizione: wUpdateDatabaseF1LAMaker
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF1LAS
    descrizione: wUpdateDatabaseF1LAS
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF1LASubContractCode
    descrizione: wUpdateDatabaseF1LASubContractCode
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF1LASubContractName
    descrizione: wUpdateDatabaseF1LASubContractName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF1LATeamId
    descrizione: wUpdateDatabaseF1LATeamId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF1LATeamName
    descrizione: wUpdateDatabaseF1LATeamName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF1OK
    descrizione: wUpdateDatabaseF1OK
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF1OnFieldStatus
    descrizione: wUpdateDatabaseF1OnFieldStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF1Status
    descrizione: wUpdateDatabaseF1Status
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF1SuspReason
    descrizione: wUpdateDatabaseF1SuspReason
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF1Total
    descrizione: wUpdateDatabaseF1Total
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF2Booking
    descrizione: wUpdateDatabaseF2Booking
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF2BookingStatus
    descrizione: wUpdateDatabaseF2BookingStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF2BookingWarning
    descrizione: wUpdateDatabaseF2BookingWarning
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF2KO
    descrizione: wUpdateDatabaseF2KO
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF2LAMaker
    descrizione: wUpdateDatabaseF2LAMaker
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF2LAS
    descrizione: wUpdateDatabaseF2LAS
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF2LASubContractCode
    descrizione: wUpdateDatabaseF2LASubContractCode
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF2LASubContractName
    descrizione: wUpdateDatabaseF2LASubContractName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF2LATeamId
    descrizione: wUpdateDatabaseF2LATeamId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF2LATeamName
    descrizione: wUpdateDatabaseF2LATeamName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF2OK
    descrizione: wUpdateDatabaseF2OK
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF2OnFieldStatus
    descrizione: wUpdateDatabaseF2OnFieldStatus
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF2Status
    descrizione: wUpdateDatabaseF2Status
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF2SuspReason
    descrizione: wUpdateDatabaseF2SuspReason
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF2Total
    descrizione: wUpdateDatabaseF2Total
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: zipCode
    descrizione: CAP
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: rendValTot
    descrizione: Val. misurato
    tipo: CUREUR
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF1TakeInCharge
    descrizione: wUpdateDatabaseF1TakeInCharge
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: wUpdateDatabaseF2TakeInCharge 
    descrizione: wUpdateDatabaseF2TakeInCharge 
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  