---
nome_tipo_sistema: MANUTENZIONE_PREVENTIVA
descrizione: MANUTENZIONE_PREVENTIVA
manutenibile: Y
prefisso_import_automatico: ~
tdt:
  - nome: cable
    descrizione: cable
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: cableIds
    descrizione: Elenco id del sistema CAVO della SEZIONE
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: Y
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: canOpenWorks
    descrizione: canOpenWorks
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: contractId
    descrizione: Id Contratto
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: customerId
    descrizione: Id Cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: directive
    descrizione: directive
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: directiveId
    descrizione: Id sistema DIRETTRICE
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: externalDirectiveId
    descrizione: externalDirectiveId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: section
    descrizione: section
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: sectionDescription
    descrizione: Descrizione sezione
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: sectionId
    descrizione: Id sistema SEZIONE della DIRETTRICE
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: workingGroupCode
    descrizione: Centro Lavoro
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: worksClosedKO
    descrizione: worksClosedKO
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: worksClosedOK
    descrizione: worksClosedOK
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: worksOnGoing
    descrizione: worksOnGoing
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: worksTotal
    descrizione: worksTotal
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
