---
nome_tipo_sistema: SEZIONE
descrizione: Gestione sezioni delle direttrici
manutenibile: Y
prefisso_import_automatico: ~
tdt:
  - nome: customerId
    descrizione: Id Cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: directiveId
    descrizione: Id sistema DIRETTRICE
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: exRO
    descrizione: Ex RO
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: Y
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: mntRegion
    descrizione: Regione
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: Y
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: oa
    descrizione: OA
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: Y
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: regionalWorkingGroupCode
    descrizione: Centro di lavoro su base regionale
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: Y
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: section
    descrizione: section
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: sectionDescription
    descrizione: Descrizione sezione
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
  - nome: workingGroupCodeList
    descrizione: workingGroupCodeList
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    morto: ~
