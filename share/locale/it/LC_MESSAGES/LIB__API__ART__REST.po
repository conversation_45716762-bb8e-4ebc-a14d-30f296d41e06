# Language file for package API::ART::REST.
# Copyright (C) 2016 SIRTI
# <PERSON><PERSON> <a.<PERSON><PERSON><PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: API::ART::REST\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-02-03 16:38+0100\n"
"PO-Revision-Date: 2016-02-03 16:38+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> <a."
"liv<PERSON><EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.6\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-Basepath: ../../../perl5/API/ART\n"
"X-Poedit-KeywordsList: __;$__;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: REST.pm\n"

#: REST.pm:577 REST.pm:1532 REST.pm:1934 REST.pm:1999 REST.pm:2204 REST.pm:2319
#: REST.pm:2372 REST.pm:2428 REST.pm:3138 REST.pm:3481 REST.pm:4207
#: REST.pm:4273
msgid "Invalid JSON"
msgstr "JSON non valido"

#: REST.pm:698
msgid "Bad token value"
msgstr "Valore errato per il token"

#: REST.pm:698
#, perl-brace-format
msgid "You must use the value {sessionid}"
msgstr "Devi utilizzare il valore {sessionid}"

#: REST.pm:994
msgid "Bad TYPE"
msgstr "TYPE errato"

#: REST.pm:1360 REST.pm:3409 REST.pm:3485 REST.pm:3777 REST.pm:3961
#: REST.pm:3999 REST.pm:4133 REST.pm:4217 REST.pm:4280 REST.pm:4404
#: REST.pm:4520 REST.pm:4561 REST.pm:4686 REST.pm:4750 REST.pm:4837
#: REST.pm:4870
msgid "Activity ID not found"
msgstr "ID attività non trovato"

#: REST.pm:1536 REST.pm:2002 REST.pm:2207 REST.pm:4210 REST.pm:4276
#, perl-brace-format
msgid "Missing {paramname} param"
msgstr "Parametro {paramname} mancante"

#: REST.pm:2005 REST.pm:2210 REST.pm:3505 REST.pm:3782 REST.pm:4213
#, perl-brace-format
msgid "Bad value for {paramname} param"
msgstr "Valore errato per il parametro {paramname}"

#: REST.pm:2010
msgid "System ID not found"
msgstr "ID sistema non trovato"

#: REST.pm:4970
msgid "Missing file"
msgstr "File mancante"
