# Language file for package SIRTI::Cache::Driver::Memcached.
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: SIRTI::Cache::Driver::Memcached\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-03-23 15:57+0100\n"
"PO-Revision-Date: 2016-03-23 15:58+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> <a."
"<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.6\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-Basepath: ../../../perl5/SIRTI/Cache/Driver\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;"
"N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: Memcached.pm\n"

#: Memcached.pm:19
#, perl-brace-format
msgid "Missing {paramname} param"
msgstr "Parametro {paramname} mancante"

#: Memcached.pm:41
#, perl-brace-format
msgid "Unable to set key {key}"
msgstr "Impossibile impostare la chiave {key}"

#: Memcached.pm:56
#, perl-brace-format
msgid "Unable to delete key {key}"
msgstr "Impossibile eliminare la chiave {key}"
