# Language file for package SIRTI::REST.
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.l<PERSON><PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: SIRTI::REST\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-02-03 16:38+0100\n"
"PO-Revision-Date: 2016-02-03 16:39+0100\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> <a."
"<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.6\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-Basepath: ../../../perl5/SIRTI\n"
"X-Poedit-KeywordsList: __;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: REST.pm\n"

#: REST.pm:159
msgid "You must invoke this function with a valid filename"
msgstr "Devi invocare questa funzione con un nome file valido"

#: REST.pm:187
#, perl-brace-format
msgid "Not a valid ISO Date: {errmsg}"
msgstr "ISO Date non valida: {errmsg}"
