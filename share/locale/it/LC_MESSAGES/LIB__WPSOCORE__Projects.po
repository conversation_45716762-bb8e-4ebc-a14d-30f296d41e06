# Language file for package WPSOCORE::Projects
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.l<PERSON><PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::Projects\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-11 15:18+0100\n"
"PO-Revision-Date: 2024-11-11 15:18+0100\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> <a."
"<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.5\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: Projects.pm\n"

#: Projects.pm:26
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: Projects.pm:27
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: Projects.pm:57
msgid "Unable to init WPSOCORE::Collection::System::CONTRACT"
msgstr "Impossibile inizializzare WPSOCORE::Collection::System::CONTRACT"

#: Projects.pm:70
msgid "Unable to init WPSOCORE::Collection::System::CITY"
msgstr "Impossibile inizializzare WPSOCORE::Collection::System::CITY"

#: Projects.pm:82
msgid "Unable to init WPSOCORE::Collection::System::PROJECT"
msgstr "Impossibile inizializzare WPSOCORE::Collection::System::PROJECT"

#: Projects.pm:160
#, perl-brace-format
msgid "Project {projectId} skipped because the filter"
msgstr "Progetto {projectId} saltato a causa del filtro"

#: Projects.pm:216
#, perl-brace-format
msgid "{date} is not a valid rfc7232 date"
msgstr "{date} non è una data rfc7232 valida"

#: Projects.pm:341
msgid "Creating system"
msgstr "Creazione sistema"

#: Projects.pm:351
msgid "System created"
msgstr "Sistema creato"
