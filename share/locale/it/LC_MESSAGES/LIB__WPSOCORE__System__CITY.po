# Language file for package WPSOCORE::System::CITY
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::System::CITY\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-11 15:18+0100\n"
"PO-Revision-Date: 2024-11-11 15:18+0100\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> <a."
"<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.5\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/System\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: CITY.pm\n"

#: CITY.pm:178
#, perl-brace-format
msgid "User {user} can not be set as {name}"
msgstr "L'utente {user} non può essere associato come {name}"

#: CITY.pm:225
#, perl-brace-format
msgid ""
"User {user} can not be unset because is associated as civil tecnhnical "
"assistant for projects {projects}"
msgstr ""
"L'utente {user} non può essere rimosso in quanto è associato come assistente "
"tecnico civile per i progetti {projects}"

#: CITY.pm:229
#, perl-brace-format
msgid ""
"User {user} can not be unset because is associated as building permits "
"manager for projects {projects}"
msgstr ""
"L'utente {user} non può essere rimosso in quanto è associato come "
"responsabile permessi edifici per i progetti {projects}"

#~ msgid "User "
#~ msgstr "Utente"
