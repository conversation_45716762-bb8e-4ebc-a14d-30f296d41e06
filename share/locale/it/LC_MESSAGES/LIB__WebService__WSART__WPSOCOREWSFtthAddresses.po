# Language file for package WebService::WSART::WPSOCOREWSFtthAddresses.
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WebService::WSART::WPSOCOREWSFtthAddresses\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-08 16:03+0200\n"
"PO-Revision-Date: 2025-05-08 16:20+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> <a."
"<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.3.2\n"
"X-Poedit-Basepath: ../../../perl5/WebService/WSART\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: WPSOCOREWSFtthAddresses.pm\n"

#: WPSOCOREWSFtthAddresses.pm:87
msgid "Activity not found"
msgstr "Attività non trovate"

#: WPSOCOREWSFtthAddresses.pm:277 WPSOCOREWSFtthAddresses.pm:493
msgid "Invalid JSON"
msgstr "JSON non valido"

#: WPSOCOREWSFtthAddresses.pm:693
#, perl-brace-format
msgid "Missing {paramname} param"
msgstr "Parametro {paramname} mancante"

#: WPSOCOREWSFtthAddresses.pm:696
#, perl-brace-format
msgid "Param {paramname} can be {values}"
msgstr "Il parametro {paramname} può valere {values}"

#: WPSOCOREWSFtthAddresses.pm:822
msgid ""
"Only admin, project manager and technial assistant user can download this "
"report"
msgstr ""
"Solamente gli utenti admin, project manager e assistenti tecnici possono "
"scaricare questo report"

#: WPSOCOREWSFtthAddresses.pm:838
#, perl-brace-format
msgid "Unsupported value \"{paramvalue}\" for parameter 'Accept'"
msgstr "Per il parametro 'Accept' il valore \"{paramvalue}\" non è supportato"
