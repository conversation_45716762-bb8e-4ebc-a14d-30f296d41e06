# Language file for package WebService::WSART::WPSOCOREWSPtes.
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WebService::WSART::WPSOCOREWSPtes\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-11 15:19+0100\n"
"PO-Revision-Date: 2024-11-11 15:19+0100\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> <a."
"<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.5\n"
"X-Poedit-Basepath: ../../../perl5/WebService/WSART\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: WPSOCOREWSPtes.pm\n"

#: WPSOCOREWSPtes.pm:86
msgid "Activity not found"
msgstr "Attività non trovate"

#: WPSOCOREWSPtes.pm:247 WPSOCOREWSPtes.pm:307 WPSOCOREWSPtes.pm:523
msgid "Invalid JSON"
msgstr "JSON non valido"

#: WPSOCOREWSPtes.pm:548
#, perl-brace-format
msgid "Transition not allowed for {tdt} \"{from}->{to}\""
msgstr "Transizione non permessa per {tdt} \"{from}->{to}\""

#: WPSOCOREWSPtes.pm:745
#, perl-brace-format
msgid "Missing {paramname} param"
msgstr "Parametro {paramname} mancante"

#: WPSOCOREWSPtes.pm:748
#, perl-brace-format
msgid "Param {paramname} can be {values}"
msgstr "Il parametro {paramname} può valere {values}"

#: WPSOCOREWSPtes.pm:936 WPSOCOREWSPtes.pm:1022 WPSOCOREWSPtes.pm:1108
#: WPSOCOREWSPtes.pm:1194
msgid ""
"Only admin, project manager, num or techinal assistant user can download "
"this report"
msgstr ""
"Solo gli utenti amministratore, project manager, num o assistente tecnico "
"possono scaricare questo report"

#: WPSOCOREWSPtes.pm:948 WPSOCOREWSPtes.pm:1034 WPSOCOREWSPtes.pm:1120
msgid "Missing mandatory parameter 'regionId'"
msgstr "Parametro obbligatorio \"regionId\" mancante"

#: WPSOCOREWSPtes.pm:955 WPSOCOREWSPtes.pm:1041 WPSOCOREWSPtes.pm:1127
#: WPSOCOREWSPtes.pm:1206
#, perl-brace-format
msgid "Unsupported value \"{paramvalue}\" for parameter 'Accept'"
msgstr "Valore non supportato \"{paramvalue}\" per il parametro 'Accept'"
