# Language file for package WebService::WSART::WPSOCOREWSRoes.
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WebService::WSART::WPSOCOREWSRoes\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-11 15:19+0100\n"
"PO-Revision-Date: 2024-11-11 15:19+0100\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> <a."
"<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.5\n"
"X-Poedit-Basepath: ../../../perl5/WebService/WSART\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: WPSOCOREWSRoes.pm\n"

#: WPSOCOREWSRoes.pm:90
msgid "Activity not found"
msgstr "Attività non trovate"

#: WPSOCOREWSRoes.pm:173 WPSOCOREWSRoes.pm:399 WPSOCOREWSRoes.pm:615
msgid "Invalid JSON"
msgstr "JSON non valido"

#: WPSOCOREWSRoes.pm:181
#, perl-brace-format
msgid "Missing mandatory param {param}"
msgstr "Parametro obbligatorio {param} mancante"

#: WPSOCOREWSRoes.pm:185
#, perl-brace-format
msgid "Param {param} must be an array"
msgstr "Il parametro {param} deve essere un array"

#: WPSOCOREWSRoes.pm:188
#, perl-brace-format
msgid "Param {param} can not be a zero-length array"
msgstr "Il parametro {param} non può essere un array a lughezza zero"

#: WPSOCOREWSRoes.pm:191
#, perl-brace-format
msgid "Max lenght exceeded for param {param}: max value {value}"
msgstr ""
"Lunghezza massima ecceduta per il parametro {param}: valore massimo {value}"

#: WPSOCOREWSRoes.pm:196
#, perl-brace-format
msgid "Param {param} can contain only numeric values"
msgstr "Il parametro {param} può contenere solo valori numerici"

#: WPSOCOREWSRoes.pm:198
#, perl-brace-format
msgid "Duplicated value {value} in param {param}"
msgstr "Valore duplicato {value} nel parametro {param}"

#: WPSOCOREWSRoes.pm:203
#, perl-brace-format
msgid "Param {param} must be an hash"
msgstr "Il parametro {param} deve essere un hash"

#: WPSOCOREWSRoes.pm:217
#, perl-brace-format
msgid "Found {found} activities: expected {expected}"
msgstr "Trovate {found} attività: attese {expected}"

#: WPSOCOREWSRoes.pm:277 WPSOCOREWSRoes.pm:318
#, perl-brace-format
msgid "Unknown BLOCK_ID {value}"
msgstr "BLOCK_ID {value} sconosciuto"
