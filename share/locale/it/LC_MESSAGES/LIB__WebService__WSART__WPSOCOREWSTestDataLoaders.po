# Language file for package WebService::WSART::WPSOCOREWSTestDataLoaders.
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><PERSON>@sirti.it>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WebService::WSART::WPSOCOREWSTestDataLoaders\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-11 15:20+0100\n"
"PO-Revision-Date: 2024-11-11 15:20+0100\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>, r."
"<EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.5\n"
"X-Poedit-Basepath: ../../../perl5/WebService/WSART\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: WPSOCOREWSTestDataLoaders.pm\n"

#: WPSOCOREWSTestDataLoaders.pm:93
msgid "Activity not found"
msgstr "Attività non trovata"

#: WPSOCOREWSTestDataLoaders.pm:167
#, perl-brace-format
msgid "Missing {paramname} param"
msgstr "Parametro {paramname} mancante "

#: WPSOCOREWSTestDataLoaders.pm:217 WPSOCOREWSTestDataLoaders.pm:258
#, perl-brace-format
msgid "Unknown BLOCK_ID {value}"
msgstr "BLOCK_ID {value} sconosciuto"

#: WPSOCOREWSTestDataLoaders.pm:339 WPSOCOREWSTestDataLoaders.pm:532
msgid "Invalid JSON"
msgstr "JSON invalido"
