# Language file for package WebService::WSART::WPSOCOREWSWO002.
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WebService::WSART::WPSOCOREWSWO002\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 10:21+0100\n"
"PO-Revision-Date: 2025-03-26 10:21+0100\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> <a."
"<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.3.2\n"
"X-Poedit-Basepath: ../../../perl5/WebService/WSART\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;"
"N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: WPSOCOREWSWO002.pm\n"

#: WPSOCOREWSWO002.pm:93
msgid "Activity not found"
msgstr "Attività non trovata"

#: WPSOCOREWSWO002.pm:167
#, perl-brace-format
msgid "Missing {paramname} param"
msgstr "Parametro {paramname} mancante"

#: WPSOCOREWSWO002.pm:216 WPSOCOREWSWO002.pm:257
#, perl-brace-format
msgid "Unknown BLOCK_ID {value}"
msgstr "BLOCK_ID {value} sconosciuto"

#: WPSOCOREWSWO002.pm:338 WPSOCOREWSWO002.pm:554
msgid "Invalid JSON"
msgstr "JSON invalido"

#: WPSOCOREWSWO002.pm:742
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"
