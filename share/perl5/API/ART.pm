package API::ART::User;

use strict;
use warnings;
use Carp;  # qw/verbose croak/;
use Digest::MD5;
use SIRTI::AuthRadius;
use Data::Dumper;
use Tie::IxHash;
use Crypt::JWT qw(decode_jwt);
use SIRTI::ART::RemoteActivity::Source;

use base qw(API::Ancestor);

our @USER_ADMIN_GROUPS = qw(ROOT ADMIN VIRTUAL);
our @USER_USER_ADMIN_GROUPS = qw(ROOT USER_ADMIN);

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = {};
	return undef unless ref($params->{ART}) eq 'API::ART';
	#$self->{ART} = $params->{ART};
	
	my $password_condition = '';
	if (exists $params->{PASSWORD} || !$ENV{ART_ALLOW_LOGIN_WITHOUT_PASSWORD}) {
		return undef unless exists $params->{PASSWORD};
		$password_condition = "and o.password_operatore = ".$params->{ART}->_dbh()->quote(Digest::MD5::md5_hex($params->{PASSWORD}));
	}
	
	if (defined $params->{API_KEY}){
		#verifico che l'API_KEY sia valida con la secret
		my $data = decode_jwt(token=>$params->{API_KEY}, key=>$ENV{ART_API_KEY_JWT_SECRET});
		return undef unless $data;
		# verifico se l'API_KEY esiste ed è valida
		my $retrieve_apiKey = $params->{ART}->_dbh()->fetch_minimalized("
			select op.login_operatore
			from operatori_api_key opa
				join operatori op on op.id_operatore = opa.id_operatore
			where opa.data_revoca is null
				and opa.api_key = ".$params->{ART}->_dbh()->quote($params->{API_KEY}));
		
		return undef
			unless defined $retrieve_apiKey;
		return undef
			if $data->{username} ne $retrieve_apiKey;
		$params->{USER} = $data->{username};
		$self->{API_KEY_CONTENT} = {
			API_KEY => $params->{API_KEY},
			CONTENT => $data
		};
	} else {
		return undef unless exists $params->{USER};
		$self->{AUTH}->{TYPE} = 'LOCAL';
		if (defined $params->{AUTH}){
			return undef unless defined $params->{AUTH}->{TYPE};
			if ($params->{AUTH}->{TYPE} eq 'RADIUS'){
				return undef unless defined $params->{AUTH}->{PARAMS};
				my $auth = SIRTI::AuthRadius->new( RADIUS => $params->{AUTH}->{PARAMS}->{RADIUS} );
				if ($auth->get_auth( USERNAME => $params->{USER}, PASSWORD => $params->{PASSWORD} ) ){
					$password_condition = ''; 
					$self->{AUTH}->{TYPE} = $params->{AUTH}->{TYPE};
				}
			}
		}
	}

	my $first_login;
	$params->{USER} = $params->{ART}->_dbh()->quote($params->{USER});
	(
		  $self->{LOGGED}
		, $self->{ID}
		, $self->{SERVICE_USER}
		, $self->{FIRST_NAME}
		, $self->{LAST_NAME}
		, $self->{EMAIL}
		, $self->{MOBILE_PHONE}
		, $self->{DISABLED}
		, $first_login
	) = $params->{ART}->_dbh()->fetch_minimalized(qq(
		select	 1
				, o.id_operatore
				, decode(o.service_user,'S',1)
				, o.nome_operatore
				, o.cognome_operatore
				, o.email
				, o.mobile_phone
				, decode(o.morto,'S',1)
				, nvl2(o.first_login,1,0)
		from	operatori o
		where	upper(o.login_operatore) = upper($params->{USER})
				and o.morto is null
				$password_condition
	));
	return undef unless ($self->{LOGGED});
	$self->{NAME} = $params->{ART}->get_user_name($self->{ID});
	$self->{GROUPS} = $params->{ART}->get_user_groups($self->{ID});
	
	my  %user_groups =
		map {$params->{ART}->get_group_name($_) => 1} @{$self->{GROUPS}};

	$self->{ROOT} = $user_groups{'ROOT'}||0;

	$self->{ADMIN} = 0;
	IS_ADMIN: for (@USER_ADMIN_GROUPS){
		if (exists $user_groups{$_}){
			$self->{ADMIN} = 1;
			last IS_ADMIN;
		}
	}
	$self->{USER_ADMIN} = 0;
	IS_USER_ADMIN: for (@USER_USER_ADMIN_GROUPS){
		if (exists $user_groups{$_}){
			$self->{USER_ADMIN} = 1;
			last IS_USER_ADMIN;
		}
	}
	my $user = bless ($self, $class);
	
	# se ho trovato l'utente verifico se la gestione del comportamento dei gruppi è presente
	if ($self->logged()){
		my $enable = $params->{ART}->_dbh()->fetch_minimalized("select 1 from user_tables where table_name = 'GRUPPI_COMP'");
		
		if ($enable){
				
			# recupero tutte le abilitazioni
			my $sql = "
				select distinct lc.label from gruppi_comp c
					join GRUPPI_COMP_LOOKUP lc on lc.id_comportamento = c.id_comportamento
					join gruppi g on g.id_gruppo = c.id_gruppo
				". ( $self->{ADMIN} ? '': "where g.id_gruppo in (".join(',', @{$self->{GROUPS} }).")") ."
			";
			
			my $beh = $params->{ART}->_dbh()->fetchall_arrayref($sql);
			
			$self->{GROUPS_BEHAVIOUR} = [map {$_->[0]} @{$beh}];
		} else {
			$self->{GROUPS_BEHAVIOUR} = [];
		}

		# aggiorno la data FIRST_LOGIN al primo accesso in assoluto
		unless ($first_login){
			my $sql = "
				DECLARE
					PRAGMA AUTONOMOUS_TRANSACTION;
					id_operatore_p NUMBER(10);
				BEGIN
					id_operatore_p := :ID_OPERATORE;
					update operatori
					set first_login = sysdate
					where id_operatore = id_operatore_p
						and first_login is null;
					COMMIT;
				END;
			";
			$self->{MANAGE_FIRST_LOGIN_PROCEDURE} = $params->{ART}->_create_prepare(__PACKAGE__.'_FIRST_LOGIN', $sql) || return undef;

			# eseguo il bind dei parametri
			$self->{MANAGE_FIRST_LOGIN_PROCEDURE}->bind_param(':ID_OPERATORE', $self->{ID});
			
			my $p = $self->{MANAGE_FIRST_LOGIN_PROCEDURE}->execute;
			
			if ($DBI::errstr){
				$params->{ART}->last_error($DBI::errstr);
				return undef;
			}
		}
	}
	
	$self->{SESSION_ID} = $params->{ART}->info('SESSION_ID');
	
	return $user;
}

# Ritorna il riferimento all'oggetto API::ART passato come argomento al costruttore.
#sub art { return $_[0]->{ART} }

# Ritorna 1 se e' stata stabilita la connessione ad ART
sub logged {
	my $self = shift;
	# TODO Real-time control
	return $self->{LOGGED};
}

# Ritorna l'ID dell'utente.
sub id { return $_[0]->{ID} }

# Ritorna la login dell'utente.
#sub name {
#	my $self = shift;
#	return $self->art()->get_user_name($self->id());
#}
sub name { return $_[0]->{NAME} }

# Ritorna il nome dell'utente.
sub first_name { return $_[0]->{FIRST_NAME} }

# Ritorna il cognome dell'utente.
sub last_name { return $_[0]->{LAST_NAME} }

# Ritorna l'email dell'utente.
sub email { return $_[0]->{EMAIL} }

# Ritorna il cellulare dell'utente.
sub mobile_phone { return $_[0]->{MOBILE_PHONE} }

# Ritorna l'elenco dei gruppi a cui appartiene l'operatore
#sub groups {
#	my $self = shift;
#	return $self->art()->get_user_groups($self->id());
#}
sub groups { return $_[0]->{GROUPS} }

# Ritorna true se l'utente corrente appartiene al gruppo di ROOT
sub is_root { return $_[0]->{ROOT} }

# Ritorna true se l'utente corrente appartiene ai gruppi di ADMIN
# definiti nella variabile @USER_ADMIN_GROUPS
sub is_admin { return $_[0]->{ADMIN} }

# Ritorna true se l'utente corrente appartiene ai gruppi di ADMIN
# definiti nella variabile @USER_USER_ADMIN_GROUPS
sub is_user_admin { return $_[0]->{USER_ADMIN} }

# Ritorna true se l'utente corrente e' di servizio
sub is_service_user { return $_[0]->{SERVICE_USER} }

# Ritorna true se l'utente corrente e' morto
sub is_disabled { return $_[0]->{DISABLED} }

sub su {
	my $self = shift;
	return API::ART::User::Su->new(USER => $self);
}

# Ritorna il tipo di autenticazione andato a buon fine
sub auth_type { return $_[0]->{AUTH}->{TYPE} }

sub check_grant {
	my $self = shift;
	my $grant_type = shift;
	
	my @found = grep {$_ eq $grant_type} @{$self->{GROUPS_BEHAVIOUR}};
	
	return scalar @found ? 1 : 0;
	
	#{
	#	AUT => 1
	#}
}

# Ritorna il session_id
sub get_session_id { return $_[0]->{SESSION_ID} }

# Ritorna il api_key
sub get_api_key_content {
	my $self = shift;
	
	return undef unless defined $self->{API_KEY_CONTENT};

	return $self->{API_KEY_CONTENT};
}

#sub _su {
	
	#$self->{SU} = API::ART::User::Su->new();
#	my $self = shift;
#	my $op = shift || '';
#	my $sugroup = shift || '';
#	croak "Not a valid operator! '$op'" if $op !~ /^(enable|disable|)$/;
#	croak "Not a valid group_id! '$sugroup'" if $sugroup !~ /^\d+$/;

#	if ($op eq 'enable') {
		#my $get_group = $self->get_group_id($sugroup);
#		$self->('SU_GROUPS',  [ $sugroup ]);
#	} elsif ($op eq 'disable') {
#		$self->('SU_GROUPS', []);
#	}
#	return $self->('SU_GROUPS');
#}

#sub su_groups {
#	return [ @{$_[0]->user()->groups()}, @{$_[0]->su()} ];
#}


###########################################################################################

package API::ART::User::Su;

use strict;
use warnings;
use Carp;  # qw/verbose croak/;

use base qw(API::Ancestor);


sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = {};

	return undef unless ref($params->{USER}) eq 'API::ART::User';
	
	$self->{USER_GROUPS} = \@{$params->{USER}->groups()};
	$self->{GROUPS} = [];
	my $Su = bless ($self, $class);
	
	$self->{SESSION_ID} = $params->{USER}->get_session_id();
	
	return $Su;
}

sub _user_groups { return $_[0]->{USER_GROUPS} }
sub activity { 
	my $self = shift;
	return API::ART::User::Su::Activity->new(SU => $self);
}

#TODO sub enable { return $_[0]->{GROUPS} }
#TODO sub disable { return $_[0]->{GROUPS} }

# Ritorna un array con i gruppi dell'utente piu' eventuali gruppi di su
#	$art->user()->su()->groups();

sub groups {
	return [
		@{$_[0]->_user_groups()}
		,@{$_[0]->{GROUPS}
	}];
}

# Ritorna il session_id
sub get_session_id { return $_[0]->{SESSION_ID} }

###########################################################################################

package API::ART::User::Su::Activity;

use strict;
use warnings;
use Carp;  # qw/verbose croak/;

use base qw(API::Ancestor);


sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = {};

	return undef unless ref($params->{SU}) eq 'API::ART::User::Su';
	
	$self->{SESSION_ID} = $params->{SU}->get_session_id();
	
	if($API::ART::SUDED_SESSIONS->{$self->{SESSION_ID}}) {
		$self->{GROUPS} = [ $API::ART::SUDED_SESSIONS->{$self->{SESSION_ID}} ];
	} else {
		$self->{GROUPS} = [];
	}
	$self->{SU_GROUPS} = $params->{SU}->groups();

	return bless $self, $class;
}

# Ritorna il riferimeto all'oggetto user
sub _user_groups { return $_[0]->{SU_GROUPS} }

# Abilita il gruppo di su specificato tramite group_id <scalar> all'utente connesso.
#	$art->user()->su()->activity()->enable(<scalar>);

sub enable {
	my $self = shift;
	my $sugroup = shift || '';
	
	croak "Not a valid group_id! '$sugroup'" if $sugroup !~ /^\d+$/;
	croak "Cannot SU Root" if $sugroup == $API::ART::ROOT_GROUP_ID;
	my $found = grep { $sugroup == $_ } @{$self->_user_groups()};
	
	$self->{'GROUPS'} = [ $sugroup ] unless $found;
	$API::ART::SUDED_SESSIONS->{$self->get_session_id()} = $sugroup;
}

# Elimina un gruppo di su all'utente connesso.
#	$art->user()->su()->activity()->disable();

sub disable {
	$_[0]->{'GROUPS'} = [];
	delete $API::ART::SUDED_SESSIONS->{$_[0]->get_session_id()};
}

# Verifica per l'utente e' abilitato il su.
#	$art->user()->su()->activity()->check();

sub check {
	return scalar @{$_[0]->{'GROUPS'}};
}

# Ritorna un array con i gruppi dell'utente piu' eventuali gruppi di su
#	$art->user()->su()->activity()->groups();

sub groups {
	return [
		@{$_[0]->_user_groups()}
		,@{$_[0]->{GROUPS}
	}];
}

# Ritorna il session_id
sub get_session_id { return $_[0]->{SESSION_ID} }

###########################################################################################

package API::ART::Types::PropertyFile;

use strict;
use warnings;
use Carp;  # qw/verbose croak/;
use Data::Dumper;
use File::Basename;

use base qw(API::Ancestor);

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = {};
	croak 'Missing mandatory params ART' unless ref($params->{ART}) eq 'API::ART';
	
	$self->{ART} = $params->{ART};
	
	for ('INTERNAL_FILENAME', 'FILENAME'){
		croak "Missing mandatory params $_"
			&& return undef
				unless defined $params->{$_};
	}
	
	open $self->{HANDLE}, '<'.$params->{INTERNAL_FILENAME} or croak "Could not open file '$params->{INTERNAL_FILENAME}'. $!";
	binmode($self->{HANDLE});
	
	$self->{FILENAME} = basename($params->{FILENAME});

	return bless ($self, $class);
}

sub art {
	shift->{ART};
}

sub DESTROY {
	my $self = shift;
	close $self->{HANDLE};
}

###########################################################################################

package API::ART::Session;

use strict;
use warnings;
use Carp;  # qw/verbose croak/;
use Data::Dumper;
use DBI;

use base qw(API::Ancestor);

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = {};
	return undef unless ref($params->{ART}) eq 'API::ART';
	return undef unless (exists $params->{USER} && ref($params->{USER}) eq 'API::ART::User');
	return undef unless exists $params->{SID};
	return undef unless exists $params->{SESSION_DURATION} && $params->{SESSION_DURATION} =~ /^\d+$/;
	
	$self->{ID_OPERATORE} = $params->{USER}->id();
	$self->{SID} = $params->{SID};
	$self->{SESSION_DURATION} = $params->{SESSION_DURATION};
	
	my $sql = "
		DECLARE
			PRAGMA AUTONOMOUS_TRANSACTION;
			sid_p VARCHAR2(4000);
			id_operatore_p NUMBER(10);
			session_duration_p NUMBER;
		BEGIN
			sid_p := :SID;
			id_operatore_p := :ID_OPERATORE;
			session_duration_p := :SESSION_DURATION;
			-- se la session_duration e' null allora si tratta della stop
			if session_duration_p is null then -- eseguo la stop
				update operatori_sessioni
				set data_scadenza_sessione = sysdate
				where sid = sid_p
					and id_operatore = id_operatore_p;
			else -- eseguo la start o l'update
				MERGE INTO operatori_sessioni os
					USING (select sid_p sid, id_operatore_p id_operatore, (sysdate + (session_duration_p/(60*60*24))) data_scadenza_sessione from dual) n
					ON (os.sid = n.sid and os.id_operatore = n.id_operatore)
				WHEN MATCHED THEN
					UPDATE SET os.data_scadenza_sessione = n.data_scadenza_sessione
				WHEN NOT MATCHED THEN
					INSERT (sid, id_operatore, data_scadenza_sessione)
					VALUES (n.sid, n.id_operatore, n.data_scadenza_sessione);
			end if;
			COMMIT;
		END;
	";
	
	$self->{MANAGE_SESSION_PROCEDURE} = $params->{ART}->_create_prepare(__PACKAGE__.'_MANAGE_SESSION', $sql) || return undef;
	
	return bless ($self, $class);
}

sub _id_operatore { shift->{ID_OPERATORE} }

sub _sid { shift->{SID} }

sub _session_duration { shift->{SESSION_DURATION} }

sub _manage_session_procedure_sth { shift->{MANAGE_SESSION_PROCEDURE}->get_sth }

# funzione unsafe: eccezione in caso di errore
sub _manage {
	my $self = shift;
	my $type = shift;
	
	my $sth = $self->_manage_session_procedure_sth();
	
	# eseguo il bind dei parametri
	$sth->bind_param(':SID', $self->_sid());
	$sth->bind_param(':ID_OPERATORE', $self->_id_operatore());
	$sth->bind_param(':SESSION_DURATION', ($type eq 'START' ? $self->_session_duration() : undef));
	
	my $p = $sth->execute;
	
	croak $DBI::errstr if $DBI::errstr;
}

sub start { shift->_manage('START') }

sub end { shift->_manage('END') }

###########################################################################################

=head1 NAME

B<API::ART::SharedResources> - Gestione risorse file condivisi

=head1 SYNOPSIS

	# Uses needed packages
	use API::ART;
	use API::ART::SharedResources;

	# Create API::ART instance...
	my $art = API::ART->new(.......);
	# Create API::ART::SharedResources instance...
	my $s = API::ART::SharedResources->new();
	
	my $create = $s->create(ART => $art, PATH => '/tmp/pippo.pl'); 

	my $file = $s->get(RESOURCE_ID => '425709d2a6b260a3b802dc02bda6d9ab');
	
=head1 DESCRIPTION

Questo package consente di scrivere e accedere ai file condivisi

Per la evitare che si accumulino files di risorse temporanee condivise si consigilia di mettere in crontab
schedulato una volta la giorno il seguente comando

	find $ART_REPOSITORY_SHARED_RESOURCES -type f -mtime +1 -exec rm {} \;

=head1 METHODS

Di seguito i metodi esposti dalla classe API::ART::SharedResources.

=cut

package API::ART::SharedResources;

use strict;
use warnings;
use Carp;  # qw/verbose croak/;
use Data::Dumper;
use File::Basename;

use base qw(SIRTI::Base);

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = {};
	
	croak "Missing env ART_REPOSITORY_SHARED_RESOURCES"
		unless defined $ENV{ART_REPOSITORY_SHARED_RESOURCES};
	
	$self->{REPOS} = $ENV{ART_REPOSITORY_SHARED_RESOURCES};
	
	return bless ($self, $class);
}


sub _repos {
	shift->{REPOS};
}

=head2 I<object>->B<create>( ART => I<API::ART> , [PATH => I<path)| HANDLE => I<filehandle>])

Restituisce il riferimento univoco alla risorsa creata.

Richiede obbligatoriamente il parametro ART e uno dei due parametri PATH|HANDLE:

=over 4

=item B<ART>

Un'istanza della classe API::ART

=item B<PATH>

Il path assoluto del file da caricare

=item B<HANDLE>

Il filehandle file da caricare

=back

=cut

sub create{
	my $self = shift;
	my %params = @_;
	my $errmsg = '';
	
	$self->last_error($errmsg)
		&& return undef
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					ART => { isa => 'API::ART' }
				}
				,OPTIONAL	=> {
					PATH => { isa => 'SCALAR' }
					,HANDLE => { isa => 'GLOB' }
				}
				,IGNORE_EXTRA_PARAMS	=> 0
			);

	$self->last_error("At least one param between HANDLE and PATH must be declared")
		&& return undef
			if (!defined $params{HANDLE} && !defined $params{PATH});
	
	$self->last_error("Only one param between HANDLE and PATH must be declared")
		&& return undef
			if (defined $params{HANDLE} && defined $params{PATH});
	
	if (defined $params{PATH}){
		$self->last_error("File ".$params{PATH}." doesn't exists")
			&& return undef
				if (! -e $params{PATH});
		$self->last_error($params{PATH}." is not a file")
			&& return undef
				if (! -f $params{PATH});
		
		unless (open $params{HANDLE}, '<'.$params{PATH}){
			$self->last_error("Unable to open file ".$params{PATH}.": ".$!);
			return undef;
		} 
		binmode($params{HANDLE});
	}
	
	my $uuid = $params{ART}->_gen_session_id();
	my $name = $self->_repos().'/'.$uuid;
	unless (open(FILE, '>'.$name)){
		$self->last_error("Unable to create file ".$uuid.": ".$!);
		return undef;
	}
	binmode( FILE );

	my $fh = $params{HANDLE};
	while (my $row = <$fh>) {
		print FILE $row;
	}
	close FILE;

	return $uuid;

}

=head2 I<object>->B<get>( RESOURCE_ID => I<RESOURCE_ID>)

Restituisce il filehandle della risorsa condivisa

Richiede obbligatoriamente il parametro RESOURCE_ID

=over 4

=item B<RESOURCE_ID>

Identificativo univoco della risorsa condivisa

=back

=cut

sub get {
	my $self = shift;
	my %params = @_;
	my $errmsg = '';
	
	$self->last_error($errmsg)
		&& return undef
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					RESOURCE_ID => { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS	=> 0
			);
	
	my $file = $self->_repos().'/'.basename($params{RESOURCE_ID});
	
	$self->last_error("File ".$params{RESOURCE_ID}." doesn't exists")
		&& return undef
			if (! -e $file);
	
	my $fh;
	unless ( open($fh, '<'.$file)){
		$self->last_error("Unable to read file ".$params{RESOURCE_ID}.": ".$!);
		return undef;
	}
	binmode( $fh );
		
	return $fh;
}

=head2 I<object>->B<delete>( RESOURCE_ID => I<RESOURCE_ID>)

Permette di rimuovere una risorsa condivisa

Richiede obbligatoriamente il parametro RESOURCE_ID

=over 4

=item B<RESOURCE_ID>

Identificativo univoco della risorsa condivisa

=back

=cut

sub delete {
	my $self = shift;
	my %params = @_;
	my $errmsg = '';
	
	$self->last_error($errmsg)
		&& return undef
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					RESOURCE_ID => { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS	=> 0
			);
	
	my $file = $self->_repos().'/'.$params{RESOURCE_ID};
	
	$self->last_error("File ".$params{RESOURCE_ID}." doesn't exists")
		&& return undef
			if (! -e $file);
	
	unless ( unlink $file ){
		$self->last_error("Unable to delete file ".$params{RESOURCE_ID}.": ".$!);
		return undef;
	}
		
	return 1;
}

###########################################################################################


package API::ART;

use strict;
use warnings;
use Carp;
use DateTime::Format::ISO8601;
use DateTime::Format::Strptime;
use JSON;
use Digest::MD5 qw(md5_hex);
use Data::Dumper;
use MIME::Base64;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{COM}/share/locale" );

use base 'API::Ancestor';

use SIRTI::DB;
use SIRTI::ART::Lookup::ART;
use API::ART::Collection::System;
use API::ART::Collection::Activity;
#use API::ART::User;

our $VERSION = '0.9.1';
our $APP_BASE = 'API::ART::APP';

our $ROOT_GROUP_ID = 1;

our $SUDED_SESSIONS = {};

our @USER_ADMIN_GROUPS = qw(ROOT ADMIN VIRTUAL);
our @USER_USER_ADMIN_GROUPS = qw(ROOT USER_ADMIN);

=head1 NAME

B<API::ART> - Classe fondamentale per l'utilizzo delle API di ART

=head1 SYNOPSIS

	use API::ART;
	my $art = API::ART->new(
								ARTID => 'artnew',
								USER => 'username',
								PASSWORD => 'secretpassword',
								DEBUG => 1,
								DEFAULT_DATE_FORMAT => <FORMAT_DATE>,
							);
	if ( $art->test_activity_type_name('GEST_DATI') ) {
		print "ID_TIPO_ATTIVITA('GEST_DATI'): ", $art->get_activity_type_id('GEST_DATI'), "\n";
	}

=head1 DESCRIPTION

Questa classe consente di accedere all'istanza ART desiderata e di sfruttarne le risorse attraverso le API.

Ogni classe delle API, infatti, richiede il passaggio di un'istanza di API::ART come argomento del costruttore.

=head1 METHODS

Di seguito i metodi esposti dalla classe API::ART.

=cut

#
# Metodi privati
#

# ritorna riferimento a SIRTI::DB
sub _dbh {
	my $self = shift;
	if ( defined $self->{'USER'} ) {
		return undef unless $self->{'USER'}->logged();
	}
#	return &{ $self }("DB");
	return $self->{"DB"};
}

# ritorna riferimento all'oggetto API::ART::Login
sub _login {
	return $_[0]->{"ART_LOGIN"};
}

# Interfaccia a SIRTI::ART::Lookup::ART
sub _lookup {
	my $self = shift;
	my $element = shift;
	my $id = shift;
	local $@;
	$self->clear_last_error();
	
	return $self->{"LOOKUP"} unless defined $element && defined $id;
	#my $method = ( ($id =~ /^\d+$/) ? 'nome' : 'id') . '_' . $element;
	my $el = eval{$self->{"LOOKUP"}->$element($id)};
	$self->last_error("$element not found for $id")
		and return undef
			if $@;
	return $self->{"LOOKUP"}->$element($id);
}

# Ricarica SIRTI::ART::Lookup::ART
sub _refresh_lookup {
	my $self = shift;
	if ( $self->{API_SUPPORT} ) {
		delete $self->{ACTIVITY_PROPERTIES_GROUP};
		# Ricreo oggetto lookup
		$self->{LOOKUP} = eval{SIRTI::ART::Lookup::ART->new($self->{DB})};
		$self->last_error("Error reloading LOOKUP!\n$@")
			&& return undef
				if $@;
	}
	return 1;
}

# Test ui permessi per una determinata action
sub _test_permission {
	my $self = shift;
	my %params = @_;
	return undef unless defined($params{ACTIVITY_TYPE_ID});
	return undef unless defined($params{STATUS_ID});
	return undef unless defined($params{GROUPS_ID}) && ref($params{GROUPS_ID}) eq 'ARRAY';

	$self->last_error("Unknown ACTIVITY_TYPE_ID ".$params{ACTIVITY_TYPE_ID})
		&& return undef
			unless ($self->test_activity_type_id($params{ACTIVITY_TYPE_ID}));
	
	$self->last_error("Unknown STATUS_ID ".$params{STATUS_ID})
		&& return undef
			unless ($self->test_activity_status_id($params{STATUS_ID}));

	for my $g (@{$params{GROUPS_ID}}){
		$self->last_error("Unknown GROUP_ID ".$g)
			&& return undef
				unless ($self->test_group_id($g));
	}

	my $groups_id = [ @{$params{GROUPS_ID}} ];

	# join distinc su tutti gli id
	my $groups =
		join (
			',',
			keys %{ { map { $_ => 1 } @{ $groups_id } } }
		);
	
	# cerco l'elenco di tutte le azioni su stati any_status del flusso
	my $action_for_any_status;
	if (defined($params{ACTION_ID})){
		my $groups_count = scalar @{$self->user()->su()->activity()->groups()};
		my $sql_any_status = "
			select 1
			from permission_action pa
			where pa.id_tipo_attivita = ?
			    and pa.id_action_permessa = ?
			    and pa.id_stato_iniziale = ?
			    --and pa.id_stato_iniziale = pa.id_stato_finale
			    and pa.id_gruppo_abilitato in (".join (',', map {'?'} @{$self->user()->su()->activity()->groups()}).")
			    and rownum<2
		";
		$self->_create_prepare(__PACKAGE__.'_tpany'.$groups_count, $sql_any_status);
		
		my @bind_values_any_status = (
			$params{ACTIVITY_TYPE_ID},
			$params{ACTION_ID},
			$self->get_activity_status_id($self->_get_activity_virtual_status_any_status_name()),
		);
		push @bind_values_any_status, @{$self->user()->su()->activity()->groups()};
		
		$action_for_any_status = $self->_create_prepare(__PACKAGE__.'_tpany'.$groups_count)->fetch_minimalized(@bind_values_any_status);
	}
	
	my $sql = qq^
		SELECT	 pa.id_gruppo_abilitato
				^ . ( defined($params{ACTION_ID}) ? ',pa.id_stato_finale '  : '' ) . qq^
		FROM	permission_action pa
		WHERE	pa.id_tipo_attivita = $params{ACTIVITY_TYPE_ID}
				^ . (defined $action_for_any_status ? '' : "AND pa.id_stato_iniziale = $params{STATUS_ID}"). qq^
				^ . ( defined($params{ACTION_ID}) ? " AND pa.id_action_permessa = '$params{ACTION_ID}' " : '' ) . qq^
				AND pa.id_gruppo_abilitato IN ( $groups )
	^;
	if ( defined $params{TARGET_SYSTEM_ID} && $params{STATUS_ID} == $self->get_activity_status_id('START') ) {
		$sql .= qq^
			UNION
			SELECT	 ps.id_gruppo_abilitato
					^ . ( defined($params{ACTION_ID}) ? ",'" . $self->get_activity_status_id($self->get_activity_status_open_name()) . "'" : '' ) . qq^
			FROM	 attivita_per_tipi_sistema apts
					,sistemi s
					,permission_sistemi ps
			WHERE	s.id_sistema = $params{TARGET_SYSTEM_ID}
					AND apts.id_tipo_sistema = s.id_tipo_sistema
					AND apts.id_tipo_attivita = $params{ACTIVITY_TYPE_ID}
					AND ps.id_sistema = s.id_sistema
					AND ps.id_gruppo_abilitato IN ( $groups )
		^;
	}
	return $self->_dbh()->fetch_minimalized(qq^SELECT * FROM ($sql) WHERE ROWNUM < 2^);
}

# Gestione stati ed azioni notevoli
sub get_activity_status_open_name { $_[0]->{"OPEN_STATUS_NAME"} }
sub get_activity_status_closed_name { $_[0]->{"CLOSED_STATUS_NAME"} }

sub get_activity_action_open_name { $_[0]->{"OPEN_ACTION_NAME"} }
sub get_activity_action_close_name { $_[0]->{"CLOSE_ACTION_NAME"} }
sub get_activity_action_update_properties_name { $_[0]->{"UPDATE_PROPERTIES_ACTION_NAME"} }

# Gestione stati ed azioni virtuali
sub _get_activity_virtual_status_close_name { $_[0]->{"CLOSE_VIRTUAL_STATUS_NAME"} }
sub _get_activity_virtual_status_close_id {
	my $self = shift;
	return $self->get_activity_status_id($self->_get_activity_virtual_status_close_name());
}
sub _get_activity_virtual_status_park_name { $_[0]->{"PARK_VIRTUAL_STATUS_NAME"} }
sub _get_activity_virtual_status_park_id {
	my $self = shift;
	return $self->get_activity_status_id($self->_get_activity_virtual_status_park_name());
}
sub _get_activity_virtual_action_close_name { $_[0]->{"CLOSE_VIRTUAL_ACTION_NAME"} }
sub _get_activity_virtual_action_close_id {
	my $self = shift;
	return $self->get_activity_action_id($self->_get_activity_virtual_action_close_name());
}

sub _get_activity_virtual_status_any_status_name { $_[0]->{"ANY_STATUS_STATUS_NAME"} }
sub _get_activity_virtual_status_any_status_id {
	my $self = shift;
	return $self->get_activity_status_id($self->_get_activity_virtual_status_any_status_name());
}

sub _get_activity_virtual_action_park_name { $_[0]->{"PARK_VIRTUAL_ACTION_NAME"} }
sub _get_activity_virtual_action_park_id {
	my $self = shift;
	return $self->get_activity_action_id($self->_get_activity_virtual_action_park_name());
}
sub _get_activity_virtual_action_restore_name { $_[0]->{"RESTORE_VIRTUAL_ACTION_NAME"} }
sub _get_activity_virtual_action_restore_id {
	my $self = shift;
	return $self->get_activity_action_id($self->_get_activity_virtual_action_restore_name());
}
sub _get_activity_virtual_action_goto_name { $_[0]->{"GOTO_VIRTUAL_ACTION_NAME"} }
sub _get_activity_virtual_action_goto_id {
	my $self = shift;
	return $self->get_activity_action_id($self->_get_activity_virtual_action_goto_name());
}
sub _get_activity_virtual_action_loop_name { $_[0]->{"LOOP_VIRTUAL_ACTION_NAME"} }
sub _get_activity_virtual_action_loop_id {
	my $self = shift;
	return $self->get_activity_action_id($self->_get_activity_virtual_action_loop_name());
}
sub _get_activity_virtual_action_update_name { $_[0]->{"UPDATE_VIRTUAL_ACTION_NAME"} }
sub _get_activity_virtual_action_update_id {
	my $self = shift;
	return $self->get_activity_action_id($self->_get_activity_virtual_action_update_name());
}
sub _get_activity_virtual_action_assign_name { $_[0]->{"ASSIGN_VIRTUAL_ACTION_NAME"} }
sub _get_activity_virtual_action_assign_id {
	my $self = shift;
	return $self->get_activity_action_id($self->_get_activity_virtual_action_assign_name());
}
sub _get_activity_virtual_action_deassign_name { $_[0]->{"DEASSIGN_VIRTUAL_ACTION_NAME"} }
sub _get_activity_virtual_action_deassign_id {
	my $self = shift;
	return $self->get_activity_action_id($self->_get_activity_virtual_action_deassign_name());
}
sub _get_activity_virtual_action_invalidate_name { $_[0]->{"INVALIDATE_VIRTUAL_ACTION_NAME"} }
sub _get_activity_virtual_action_invalidate_id {
	my $self = shift;
	return $self->get_activity_action_id($self->_get_activity_virtual_action_invalidate_name());
}
sub _get_activity_virtual_action_add_documentation_name { $_[0]->{"ADD_DOCUMENTATION_VIRTUAL_ACTION_NAME"} }
sub _get_activity_virtual_action_add_documentation_id {
	my $self = shift;
	return $self->get_activity_action_id($self->_get_activity_virtual_action_add_documentation_name());
}
sub _enum_activity_status_by_type {
	my ($self, %params) = @_;
	croak "Expecting ACTIVITY_TYPE_ID or ACTIVITY_TYPE_NAME parameters!\n\n" unless defined $params{ACTIVITY_TYPE_ID} || defined $params{ACTIVITY_TYPE_NAME};
	croak "Expecting only one between ACTIVITY_TYPE_ID and ACTIVITY_TYPE_NAME parameters!\n" if defined $params{ACTIVITY_TYPE_ID} && defined $params{ACTIVITY_TYPE_NAME};
	my $db = $self->_dbh();

	my $activity_type_id;
	if ( defined $params{ACTIVITY_TYPE_ID} ) {
		croak "Invalid ACTIVITY_TYPE_ID '$params{ACTIVITY_TYPE_ID}'!\n" unless $self->test_activity_type_id($params{ACTIVITY_TYPE_ID});
		$activity_type_id = $params{ACTIVITY_TYPE_ID};
	} else {
		croak "Invalid ACTIVITY_TYPE_NAME '$params{ACTIVITY_TYPE_NAME}'!\n" unless $self->test_activity_type_name($params{ACTIVITY_TYPE_NAME});
		$activity_type_id = $self->get_activity_type_id($params{ACTIVITY_TYPE_NAME});
	}
		
	my $sql = "with stati_possibili as (
			select id_stato_iniziale id_stato from PERMISSION_ACTION where id_tipo_attivita = ?
			union
			select id_stato_finale from PERMISSION_ACTION where id_tipo_attivita = ?
			)
			select nome, s.id_stato from stati_possibili p
			join stati s on s.id_stato = p.id_stato
			order by 1";
	
	$self->_create_prepare(__PACKAGE__.'_ACTIVITY_TYPE_STATUS_LIST', $sql) || return undef;
	
	my $result = $self->_create_prepare(__PACKAGE__.'_ACTIVITY_TYPE_STATUS_LIST')->fetchall_hashref($activity_type_id, $activity_type_id) or croak 'Errore: '.$db->get_errormessage();
	
	my %stati = map {$_->{'NOME'} => $_->{'ID_STATO'}} @$result;
	return \%stati;
}
sub _enum_activity_action_by_type {
	my ($self, %params) = @_;
	croak "Expecting ACTIVITY_TYPE_ID or ACTIVITY_TYPE_NAME parameters!\n\n" unless defined $params{ACTIVITY_TYPE_ID} || defined $params{ACTIVITY_TYPE_NAME};
	croak "Expecting only one between ACTIVITY_TYPE_ID and ACTIVITY_TYPE_NAME parameters!\n" if defined $params{ACTIVITY_TYPE_ID} && defined $params{ACTIVITY_TYPE_NAME};
	my $db = $self->_dbh();
	my $groups_id = $self->user()->groups();
	
	my $activity_type_id;
	my $initial_status_id;
	if ( defined $params{ACTIVITY_TYPE_ID} ) {
		croak "Invalid ACTIVITY_TYPE_ID '$params{ACTIVITY_TYPE_ID}'!\n" unless $self->test_activity_type_id($params{ACTIVITY_TYPE_ID});
		$activity_type_id = $params{ACTIVITY_TYPE_ID};
	} else {
		croak "Invalid ACTIVITY_TYPE_NAME '$params{ACTIVITY_TYPE_NAME}'!\n" unless $self->test_activity_type_name($params{ACTIVITY_TYPE_NAME});
		$activity_type_id = $self->get_activity_type_id($params{ACTIVITY_TYPE_NAME});
	}
	
	if ( defined $params{INITIAL_STATUS_ID} ) {
		croak "Expecting INITIAL_STATUS_ID or INITIAL_STATUS_NAME parameters!\n\n" if defined $params{INITIAL_STATUS_NAME};
		croak "Expecting numeric INITIAL_STATUS_ID!\n\n" unless $params{INITIAL_STATUS_ID} =~ /^\d+$/;
		$initial_status_id = $params{INITIAL_STATUS_ID};
	}
	if ( defined $params{INITIAL_STATUS_NAME} ) {
		croak "Expecting INITIAL_STATUS_ID or INITIAL_STATUS_NAME parameters!\n\n" if defined $params{INITIAL_STATUS_ID};
		$initial_status_id = $self->get_activity_status_id($params{INITIAL_STATUS_NAME});
	}

	my $sql = "with azioni_possibili as (
			select distinct id_action_permessa id_action from PERMISSION_ACTION where id_tipo_attivita = ?
			".($initial_status_id ? " and id_stato_iniziale = ? " : "")."
			".($params{SKIP_PERMISSIONS} ? "" : " and id_gruppo_abilitato in (".join(', ',map { '?' } @$groups_id).")")."
			)
			select nome, s.id_action from azioni_possibili p
			join action s on s.id_action = p.id_action
			union
			-- sezione per gestire gli step sugli stati __ANY_STATUS__
			select distinct ac.nome
				, pa.id_action_permessa
			from permission_action pa
				join action ac on ac.id_action = pa.id_action_permessa
			where 1=1
				and (ac.flag_action_partenza is null or ac.flag_action_partenza not in ('D'))
				and pa.id_tipo_attivita = ?
				".($params{SKIP_PERMISSIONS} ? "" : " and pa.id_gruppo_abilitato in (".join(', ',map { '?' } @$groups_id).")")."
				and pa.id_stato_iniziale = (select id_stato from stati where flag_stato_partenza = 'Y')
				".($initial_status_id ? " and ? in (select st.id_stato from stati st where st.flag_stato_partenza not in ('C','Q')) " : "")."
			order by 1";
	
	my $name = __PACKAGE__.'_ACTIVITY_TYPE_ACTION_LIST';
	$name .= $initial_status_id ? '_WITH_INITIAL_STATUS_ID' : '_WITHOUT_INITIAL_STATUS_ID';
	$name .= '_'. scalar @$groups_id unless $params{SKIP_PERMISSIONS};
	
	$self->_create_prepare($name, $sql) || return undef;
	
	my @bind_params = $activity_type_id;
	push @bind_params, $initial_status_id if $initial_status_id;
	push @bind_params, @$groups_id unless $params{SKIP_PERMISSIONS};
	push @bind_params, $activity_type_id;
	push @bind_params, @$groups_id unless $params{SKIP_PERMISSIONS};
	push @bind_params, $initial_status_id if $initial_status_id;
	
	my $result = $self->_create_prepare($name)->fetchall_hashref(@bind_params) or croak 'Errore: '.$db->get_errormessage();
	
	my %stati = map {$_->{'NOME'} => $_->{'ID_ACTION'}} @$result;
	return \%stati;
}


=head2 I<package>->B<new>( ARTID => I<scalar>, USER_REFERENCE => I<object>, USER => I<scalar>, PASSWORD => I<scalar> [, DEBUG => I<scalar>] )

Il metodo B<new()> permette di creare una nuova istanza e richiede che vengano passati i seguenti argomenti:

=over 4

=item B<ARTID>

l'identificativo dell'istanza ART a cui connettersi

=item I<ACCOUNT>

l'account puo' essere specificato in tre modi:

=over 4

=item B<USER_REFERENCE>

istanza di un oggetto B<API::ART::User> (ritornato dal metodo I<API::ART-E<gt>user()>)

=back

oppure:

=over 4

=item B<USER>

la I<login> dell'utente ART da utilizzare per la connessione

=item B<PASSWORD>

la I<password> dell'utente ART da utilizzare per la connessione

=back

oppure:

=over 4

=item B<API_KEY>

la I<API_KEY> da utilizzare per la connessione

=back

=item B<AUTOSAVE>

(default: 0) se impostato ad 1 utilizza l'opzione AUTOCOMMIT con gli oggetti SIRTI::DB

I<ATTENZIONE> : in ambiente web e' necessario forzare B<AUTOSAVE> a 0 in quanto l'AUTOCOMMIT e' di default

=item B<DEBUG>

(default: 0) permette di abilitare l'emissione di messaggi di debug su STDERR (valori ammessi sono 0 o 1)

=item B<AUTOSAVE>

(default: 0) se impostato ad 1 utilizza l'opzione AUTOCOMMIT con gli oggetti SIRTI::DB

I<ATTENZIONE> : in ambiente web e' necessario forzare B<AUTOSAVE> a 0 in quanto l'AUTOCOMMIT e' di default

=item B<SAVE_ALL_PROPERTIES>

(default: 1) se impostato a 0 non salva tutte le PROPERTIES che rimangono invariate

=item I<SESSION_MANAGEMENT>

Gestione del tracciamento della sessione utente. I parametri SID e SESSION_DURATION devono essere passato contestualmente.
In assenza di questi due paremtri il tracciamento della sessione utente non sarà attivato

=over 4

=item B<SID>

SID della sessione da monitorare

=item B<SESSION_DURATION>

Durata in secondi della sessione utente da monitorare

=back

=item B<ALIAS_SYSTEM_PROPERTY>

(default: 0) se impostato a 1 utilizza gli alias delle system_property, altrimenti usa i nomi "ordinari"

=back

=cut
sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	# Parameters:
	my $params = {@_};
	# ARTID					: identificativo dell'istanza ART a cui connettersi
	# USER_REFERENCE		: istanza di un oggetto API::ART::User (ritornato dal metodo API::ART->user())
	# USER 					: username (login) dell'utente ART
	# PASSWORD				: password dell'utente ART
	# AUTOSAVE				: se = true imposta l'AUTOCOMMIT sull'oggetto DBI
	# DEBUG					: controlla la stampa di messaggi di diagnostica
	# SAVE_ALL_PROPERTIES	: se = false non salva tutte le properties che rimangono invariate
	# ALIAS_SYSTEM_PROPERTY	: se = true utilizza gli alias delle system_property
	# DEFAULT_DATE_FORMAT   : imposta il formato data
	# API_KEY				: api_key alternativa a USER|USER_REFERENCE per instanziare l'oggetto. Deve essere un API-KEY di tipo JWT con secret

	# Leggo l'elenco delle connessioni disponibili dai file di configurazione api.art.conf
	my %ARTID = ();
	foreach my $conf_file	(
								"/etc/api.art.conf" ,
								"$ENV{HOME}/etc/api.art.conf" ,
								"$ENV{HOME}/.api.art.conf" ,
								"./.api.art.conf"
							) {
		if ( open(CONF, "<$conf_file") ) {
			my $line = 0;
			while (<CONF>) {
				chomp;
				++$line;
				next if m/^\s*?\#/;
				next if m/^\s*?$/;
				my ($artid, $cn) = split(/\s+/);
				if ( defined $artid  &&  defined $cn ) {
					$ARTID{$artid} = $cn;
				} else {
					print STDERR "[$conf_file] Malformed configuration directive at line $line!\n";
				}
			}
			close CONF;
		} else {
			#print STDERR "Missing configuration file '$conf_file'!\n";
		}
	}
	unless ( keys %ARTID ) {
		print STDERR "Missing configuration!\n";
		exit 1;
	}
	
	# Sintassi
	my $usage = sub {
		my $errmsg = shift;
		my $msg    = "";
		$msg .= "$class : $errmsg\n\n" if defined $errmsg;
		$msg .= qq{Usage:
	${class}->new(
		ARTID => \$artid,
		[ USER => \$artuser, PASSWORD => \$password ] | [ USER_REFERENCE => \$user_reference ] | [ API_KEY => \$api_key ],
		[ DEBUG => \$debug ]
	)

		};
		die $msg;
	};

	#
	# Controlli sui parametri
	#
	##### ARTID
	$usage->(__('Missing ARTID!')) unless defined $params->{ARTID};
	$params->{ARTID} = uc($params->{ARTID});
	$usage->(__('Bad value for ARTID!')) unless grep /^$params->{ARTID}$/i, keys %ARTID;
	if (defined $params->{API_KEY}) {
		$usage->('API_KEY authentication mode available only if ART_ALLOW_LOGIN_WITHOUT_PASSWORD is enable!')
			unless ($ENV{ART_ALLOW_LOGIN_WITHOUT_PASSWORD});
		$usage->('API_KEY authentication mode available only if ART_API_KEY_JWT_SECRET is present!')
			unless ($ENV{ART_API_KEY_JWT_SECRET});
	} else {
		if ( defined $params->{USER_REFERENCE} ) {
			$usage->('Bad USER_REFERENCE!') unless ref($params->{USER_REFERENCE}) eq 'API::ART::User';
		} else {
			$usage->(__('Missing USER!')) unless defined $params->{USER};
			$params->{USER} = uc($params->{USER});
			unless ($ENV{ART_ALLOW_LOGIN_WITHOUT_PASSWORD}) {
				$usage->('Missing PASSWORD!') unless defined $params->{PASSWORD};
			}
		}
	}
	$usage->('Bad value for DEBUG!') if defined $params->{DEBUG} && !grep /^$params->{DEBUG}$/, qw(0 1);
	$params->{DEBUG} = 0 unless defined $params->{DEBUG};

	$usage->('Bad value for SAVE_ALL_PROPERTIES!')
		if defined $params->{SAVE_ALL_PROPERTIES} && !grep /^$params->{SAVE_ALL_PROPERTIES}$/, qw(0 1);

	$usage->('Bad value for ALIAS_SYSTEM_PROPERTY!')
		if defined $params->{ALIAS_SYSTEM_PROPERTY} && !grep /^$params->{ALIAS_SYSTEM_PROPERTY}$/, qw(0 1);
	
	##### Gestione Session Management
	if (
		(defined $params->{SESSION_DURATION} && !defined $params->{SID})
		||
		(!defined $params->{SESSION_DURATION} && defined $params->{SID})
		){
		$usage->(__('Paras SESSION_DURATION and SID must be both defined!'));
	}
	
	# istanzio oggetto ancestor x emissione debug
	my $self = $class->SUPER::new();

	$self->debug( $params->{DEBUG} );
	#$class->SUPER::debug( $params->{DEBUG} );
	
	# Imposto envirnoments
	$self->{ENVIRONMENT} = defined $ENV{ART_ENVIRONMENT} ? $ENV{ART_ENVIRONMENT} : 'development';
	
	# pattern utilizzato per classe DateTime::Format::Strptime
	$self->{STRPTIME_PATTERN} = '%FT%T.%N';

	# Informazioni sulla connessione ecc.
	$self->{INFO} = {
		VERSION => $VERSION,
		ARTID => uc( $params->{ARTID} ),
		CONNECTSTRING => $ARTID{ uc( $params->{ARTID} ) },
		SCRIPT_NAME => $0,
		SCRIPT_PARAMETERS => join('|', @ARGV),
		HOST => $ENV{HOSTNAME},
		CALLER => join(' -> ', caller),
		APPLICATION_USER => $ENV{USER},
		DEFAULT_DATE_FORMAT => 'YYYYMMDDHH24MISS',
		DEFAULT_TIME_ZONE => 'Europe/Rome',	
		DEFAULT_ISO_DATE_FORMAT_ORACLE => 'YYYY-MM-DD"T"hh24:mi:ss.fftzh:tzm',
		DEFAULT_ISO_DATE_FORMAT => DateTime::Format::Strptime->new(pattern   => $self->{STRPTIME_PATTERN}),
		SESSION_ID => API::ART::_gen_session_id()
	};

	# Connessione al DB

	$self->{_AUTOSAVE} = $params->{AUTOSAVE};
	$self->{_DEBUG} = $params->{DEBUG};
	&connect($self); ## Chiama il metodo connect() in modo statico in quanto $self non è ancora blessato
	$usage->("Error connecting to $params->{ARTID}!")
		unless $self->{DB};
	
	# Oggetto lookup
	$self->{LOOKUP} = SIRTI::ART::Lookup::ART->new($self->{DB});
	
	# SAVE_ALL_PROPERTIES
	$self->{SAVE_ALL_PROPERTIES} = $params->{SAVE_ALL_PROPERTIES}
		if defined $params->{SAVE_ALL_PROPERTIES};

	# ALIAS_SYSTEM_PROPERTY
	$self->{ALIAS_SYSTEM_PROPERTY} = $params->{ALIAS_SYSTEM_PROPERTY}
		if defined $params->{ALIAS_SYSTEM_PROPERTY};
	
	# Stati notevoli
	$self->{OPEN_STATUS_NAME} = $self->{DB}->fetch_minimalized("select s.nome from stati s where s.flag_stato_partenza  = 'A'");
	$self->{CLOSED_STATUS_NAME} = $self->{DB}->fetch_minimalized("select s.nome from stati s where s.flag_stato_partenza  = 'C'");
	
	# Azioni notevoli
	$self->{OPEN_ACTION_NAME} = $self->{DB}->fetch_minimalized("select a.nome from action a where a.flag_action_partenza  = 'A'");
	$self->{CLOSE_ACTION_NAME} = $self->{DB}->fetch_minimalized("select a.nome from action a where a.flag_action_partenza  = 'C'");
	$self->{UPDATE_PROPERTIES_ACTION_NAME} = $self->{DB}->fetch_minimalized("select a.nome from action a where a.flag_action_partenza  = 'T'");
	
	# Stati virtuali
	$self->{CLOSE_VIRTUAL_STATUS_NAME} = $self->{DB}->fetch_minimalized("select s.nome from stati s where s.flag_stato_partenza  = 'C'");
	$self->{PARK_VIRTUAL_STATUS_NAME} = '___PARKED___';
	$self->{ANY_STATUS_STATUS_NAME} = $self->{DB}->fetch_minimalized("select s.nome from stati s where s.flag_stato_partenza  = 'Y'");
	# Azioni virtuali
	$self->{CLOSE_VIRTUAL_ACTION_NAME} = $self->{DB}->fetch_minimalized("select a.nome from action a where a.flag_action_partenza  = 'C'");
	$self->{PARK_VIRTUAL_ACTION_NAME} = '___PARK___';
	$self->{RESTORE_VIRTUAL_ACTION_NAME} = '___RESTORE___';
	$self->{GOTO_VIRTUAL_ACTION_NAME} = '___GOTO___';
	$self->{LOOP_VIRTUAL_ACTION_NAME} = '___LOOP___';
	$self->{UPDATE_VIRTUAL_ACTION_NAME} = '___UPDATE___';
	$self->{ASSIGN_VIRTUAL_ACTION_NAME} = $self->{DB}->fetch_minimalized("select a.nome from action a where a.flag_action_partenza  = 'G'");
	$self->{DEASSIGN_VIRTUAL_ACTION_NAME} = $self->{DB}->fetch_minimalized("select a.nome from action a where a.flag_action_partenza  = 'H'");
	$self->{INVALIDATE_VIRTUAL_ACTION_NAME} = $self->{DB}->fetch_minimalized("select a.nome from action a where a.flag_action_partenza  = 'I'");
	$self->{ADD_DOCUMENTATION_VIRTUAL_ACTION_NAME} = $self->{DB}->fetch_minimalized("select a.nome from action a where a.flag_action_partenza  = 'D'");
	
	# Verifico se il DB supporta l'estensione alle API::ART
	$self->{API_SUPPORT} = $self->{DB}->fetch_minimalized(qq(
		SELECT CASE
		          WHEN COUNT (*) = 2
		             THEN 1
		          ELSE 0
		       END
		FROM   user_catalog uc
		WHERE  uc.table_type = 'TABLE'
		       AND uc.table_name IN ('A_PADRE_A_FIGLIO', 'TIPI_DATI_TECNICI_ATT_ACTION')
		       AND EXISTS (SELECT 1
		                   FROM   stati s
		                   WHERE  s.nome = '$self->{CLOSE_VIRTUAL_STATUS_NAME}'
		                          AND s.flag_stato_partenza = 'C')
		       AND EXISTS (SELECT 1
		                   FROM   stati s
		                   WHERE  s.nome = '$self->{PARK_VIRTUAL_STATUS_NAME}'
		                          AND s.flag_stato_partenza = 'V')
		       AND EXISTS (SELECT 1
		                   FROM   action a
		                   WHERE  a.nome = '$self->{CLOSE_VIRTUAL_ACTION_NAME}'
		                          AND a.flag_action_partenza = 'C')
		       AND EXISTS (SELECT 1
		                   FROM   action a
		                   WHERE  a.nome = '$self->{PARK_VIRTUAL_ACTION_NAME}'
		                          AND a.flag_action_partenza = 'V')
		       AND EXISTS (SELECT 1
		                   FROM   action a
		                   WHERE  a.nome = '$self->{RESTORE_VIRTUAL_ACTION_NAME}'
		                          AND a.flag_action_partenza = 'V')
		       AND EXISTS (SELECT 1
		                   FROM   action a
		                   WHERE  a.nome = '$self->{GOTO_VIRTUAL_ACTION_NAME}'
		                          AND a.flag_action_partenza = 'V')
		       AND EXISTS (SELECT 1
		                   FROM   action a
		                   WHERE  a.nome = '$self->{LOOP_VIRTUAL_ACTION_NAME}'
		                          AND a.flag_action_partenza = 'V')
   	));
	
	$self->{LAST_ERROR} = '';
	
	# Workspace per salvare tutte le nuove "create_prepare" del package API::ART
	$self->{_PREPARE} = {};
	
	# Elenco stati "finali"
	$self->{FINAL_STATUS} = [
		$self->{DB}->fetch_minimalized(qq(
			SELECT s.id_stato
			FROM   stati s
			WHERE  s.flag_stato_partenza IN ('C', 'Q')
	   	))
   	];
	
	
	
	# Solo se esiste pieno support alle API::ART
	if ( $self->{API_SUPPORT} ) {
		
		# Determino se l'istanza supporta il lock sulle attivita
		$self->{INFO}->{ACTIVITY_LOCK_SUPPORT} = $self->{DB}->fetch_minimalized(qq/
			SELECT 1
			FROM all_source 
			WHERE name = 'TRAB' 
			  AND type = 'PACKAGE BODY'
			  AND UPPER(text) LIKE 'FUNCTION ACTIVITY_LOCK%'
			  AND rownum < 2
		/);
		unless ($self->{INFO}->{ACTIVITY_LOCK_SUPPORT}) {
			die "Missing activity-locking support!\n";
		}
		
		
		
	   	
	   	# Verifico esistenza constraint FK_ID_TDT
		unless ( $self->{DB}->fetch_minimalized(qq/SELECT 1 FROM user_constraints WHERE constraint_name = 'FK_ID_TDT'/) ) {
			die "ERROR! Missing 'FK_ID_TDT' constraint (Please, run '<COMMON_PATH>/TOOLS/share/script/sql/API_ART_20101128_0001.sql' script).\n";
		}
		
		# Verifico esistenza tabella DATI_TECNICI_HISTORY
		unless ( $self->{DB}->fetch_minimalized(qq/SELECT 1 FROM user_tables WHERE table_name = 'DATI_TECNICI_HISTORY'/) ) {
			die "ERROR! Missing 'DATI_TECNICI_HISTORY' table (Please, run '<COMMON_PATH>/TOOLS/share/script/sql/API_ART_20101128_0001.sql' script).\n";
		}
	   	
	   	$self->{SAVE_ALL_PROPERTIES} = $params->{SAVE_ALL_PROPERTIES}
			if defined $params->{SAVE_ALL_PROPERTIES};
		
		$self->{ALIAS_SYSTEM_PROPERTY} = $params->{ALIAS_SYSTEM_PROPERTY}
			if defined $params->{ALIAS_SYSTEM_PROPERTY};
		
	} else {
		$self->{INFO}->{ACTIVITY_LOCK_SUPPORT} = undef;
		print STDERR "WARNING: Missing Full API_SUPPORT!\n";
	}
	
#	# Verifico esistenza colonna API_CLASS
#	foreach my $table (qw/TIPI_SISTEMA TIPI_DATI_TECNICI TIPI_ATTIVITA TIPI_DATI_TECNICI_ATTIVITA/) {
#		foreach my $column (qw/API_CLASS API_COLLECTION API_CHILD_CLASS/) {
#			unless ( $self->{DB}->fetch_minimalized(qq/SELECT 1 FROM user_tab_columns WHERE table_name = '$table' AND column_name  = '$column'/) ) {
#				die "ERROR! Missing '${table}.${column}' column (Please, run '$ENV{HOME}/COM/TOOLS/share/script/API_ART_20101128_0001.sql' script).\n";
#			}
#		}
#	}
	
	# verifico se ho il supporto per i gruppi estesi
	my $sql_extended_group = "
		select count(1) From user_tab_cols
		where table_name = 'GRUPPI'
		and column_name in ('PRIVATO','AUTOGENERATO')
	";
	
	my $count_extended_group = $self->{DB}->fetch_minimalized($sql_extended_group);
	
	if ($count_extended_group == 2){
		$self->{INFO}->{GROUP_EXTENDED_SUPPORT} = 1;
	} else {
		$self->{INFO}->{GROUP_EXTENDED_SUPPORT} = undef;
		print STDERR "WARNING: Missing GROUP_EXTENDED_SUPPORT!\n";
	}

	# BLOB repository
	$self->{SYSTEM_BLOB_REPOSIROTY} = $self->{DB}->fetch_minimalized(qq/SELECT valore FROM config_art WHERE chiave = 'ART.REPOSITORY'/);
	print STDERR "ART.REPOSITORY configutation key not found!" and
		exit 1
			unless $self->{SYSTEM_BLOB_REPOSIROTY};
	$self->{SYSTEM_BLOB_REPOSIROTY} =~ s/\/*?$//;
	$self->{SYSTEM_BLOB_REPOSIROTY} .= '/system_blobs/';
	opendir my $d, $self->{SYSTEM_BLOB_REPOSIROTY} or
		(print STDERR "BLOB repository $self->{SYSTEM_BLOB_REPOSIROTY} not available: " . $! and	exit 1);
	closedir $d;
	
	$self = bless($self, $class);

	# Connessione ad ART
	if ( defined $params->{API_KEY} ) {
		my %params_new = (
			ART => $self,
			API_KEY => $params->{API_KEY},
		);
		$self->{USER} = API::ART::User->new(%params_new);
		$usage->('Unknown user account!') unless $self->{USER};
	} else {
		if ( defined $params->{USER_REFERENCE} ) {
			$self->{USER} = $params->{USER_REFERENCE};
		} else {
			$params->{AUTH}->{TYPE} = 'LOCAL' unless (defined $params->{AUTH});
			my %params_new = (
				ART => $self,
				USER => $params->{USER},
				AUTH => $params->{AUTH},
			);
			if (defined $params->{PASSWORD}) {
				$params_new{PASSWORD} = $params->{PASSWORD};
			}
			$self->{USER} = API::ART::User->new(%params_new);
			$usage->('Unknown user account!') unless $self->{USER};
		}
	}
	
	##### Gestione Session Management
	if (defined $params->{SESSION_DURATION} && defined $params->{SID}){
		$self->{SESSION_MANAGEMENT}->{SID} = $params->{SID};
		$self->{SESSION_MANAGEMENT}->{SESSION_DURATION} = $params->{SESSION_DURATION};
		$self->{SESSION_MANAGEMENT}->{OBJ} = API::ART::Session->new(ART => $self, SID => $self->{SESSION_MANAGEMENT}->{SID}, SESSION_DURATION => $self->{SESSION_MANAGEMENT}->{SESSION_DURATION}, USER => $self->{USER});
		$usage->(__('Unable to init session!')) unless  $self->{SESSION_MANAGEMENT}->{OBJ};
		# chiamo la start per inizializzare il gestione di sessione
		eval { $self->{SESSION_MANAGEMENT}->{OBJ}->start() };	
		$usage->(__x('Unable to init session: {error}', error => $@)) if $@;
	}
	
	# Verifico passaggio paramentro DEFAULT_DATE_FORMAT
	if ($params->{DEFAULT_DATE_FORMAT}){
		unless (eval {$self->{DB}->fetch_minimalized("select to_char(sysdate,".$self->{DB}->quote($params->{DEFAULT_DATE_FORMAT}).") from dual") }){
			die "ERROR! DEFAULT_DATE_FORMAT '$params->{DEFAULT_DATE_FORMAT}' not valid!\n";
		}
		$self->{INFO}->{DEFAULT_DATE_FORMAT} = $params->{DEFAULT_DATE_FORMAT};
	}
	
	if ($self->api_support()){
		
		# Verifico disponibilita' PT
		my $flatdta_check = $self->{DB}->fetch_minimalized("
			select count(1) from user_objects
			where 
			 object_name = 'FLAT_DTA' and object_type = 'PACKAGE' and status = 'VALID'
		");
		
		if (defined $flatdta_check && $flatdta_check == 1){
			my $flatdta_results = $self->{DB}->fetchall_hashref("
				select vtdta.id_tipo_attivita ACTIVITY_TYPE_ID
				  , ta.nome_tipo_Attivita ACTIVITY_TYPE_NAME
				  , vtd.tabella_Associata ACTIVITY_TYPE_NAME_REMAP
				  , tdta.id_tipo_dato_tecnico_attivita ACTIVITY_PROPERTY_ID
				  , tdta.descrizione ACTIVITY_PROPERTY_NAME
				  , vtdta.nome_colonna ACTIVITY_PROPERTY_REMAP
				  , vtdta.data_type ACTIVITY_PROPERTY_DATA_TYPE
				  , vtdta.date_format ACTIVITY_PROPERTY_DATE_FORMAT
				from v_tdta_dtaflat vtdta
				  join tipi_attivita ta on ta.id_tipo_attivita = vtdta.id_tipo_attivita
				  join tipi_dati_tecnici_attivita tdta on TDTA.id_tipo_dato_tecnico_Attivita = vtdta.id_tipo_dato_tecnico_attivita
				  join v_ta_dtaflat vtd on vtd.id_tipo_attivita = vtdta.id_tipo_attivita
				where vtdta.abilitata = 'Y'
			");
			
			for my $flatdta (@{$flatdta_results}){
				for ('ACTIVITY_TYPE_ID', 'ACTIVITY_TYPE_NAME'){
					if (exists $self->{FLATDTA_SUPPORT}->{$_}->{$flatdta->{$_}}){
						push @{$self->{FLATDTA_SUPPORT}->{$_}->{$flatdta->{$_}}}, $flatdta->{ACTIVITY_PROPERTY_NAME};
					} else {
						$self->{FLATDTA_SUPPORT}->{$_}->{$flatdta->{$_}} = [$flatdta->{ACTIVITY_PROPERTY_NAME}];
					}
					if (exists $self->{FLATDTA_SUPPORT}->{ACTIVITY_PROPERTY_NAME}->{$flatdta->{ACTIVITY_PROPERTY_NAME}}->{$_}){
						push @{$self->{FLATDTA_SUPPORT}->{ACTIVITY_PROPERTY_NAME}->{$flatdta->{ACTIVITY_PROPERTY_NAME}}->{$_}}, $flatdta->{$_};
					} else {
						$self->{FLATDTA_SUPPORT}->{ACTIVITY_PROPERTY_NAME}->{$flatdta->{ACTIVITY_PROPERTY_NAME}}->{$_} = [$flatdta->{$_}];
					}
				}
				$self->{FLATDTA_SUPPORT}->{REMAP}->{TABLE}->{ACTIVITY_TYPE_NAME}->{$flatdta->{ACTIVITY_TYPE_NAME}} = $flatdta->{ACTIVITY_TYPE_NAME_REMAP};
				$self->{FLATDTA_SUPPORT}->{REMAP}->{TABLE}->{ACTIVITY_TYPE_ID}->{$flatdta->{ACTIVITY_TYPE_ID}} = $flatdta->{ACTIVITY_TYPE_NAME_REMAP};
				$self->{FLATDTA_SUPPORT}->{REMAP}->{ACTIVITY_PROPERTY}->{ACTIVITY_TYPE_NAME}->{$flatdta->{ACTIVITY_TYPE_NAME}}->{$flatdta->{ACTIVITY_PROPERTY_NAME}}->{REMAP_NAME} = $flatdta->{ACTIVITY_PROPERTY_REMAP};
				$self->{FLATDTA_SUPPORT}->{REMAP}->{ACTIVITY_PROPERTY}->{ACTIVITY_TYPE_NAME}->{$flatdta->{ACTIVITY_TYPE_NAME}}->{$flatdta->{ACTIVITY_PROPERTY_NAME}}->{DATA_TYPE} = $flatdta->{ACTIVITY_PROPERTY_DATA_TYPE};
				$self->{FLATDTA_SUPPORT}->{REMAP}->{ACTIVITY_PROPERTY}->{ACTIVITY_TYPE_NAME}->{$flatdta->{ACTIVITY_TYPE_NAME}}->{$flatdta->{ACTIVITY_PROPERTY_NAME}}->{DATE_FORMAT} = $flatdta->{ACTIVITY_PROPERTY_DATE_FORMAT};
				$self->{FLATDTA_SUPPORT}->{REMAP}->{ACTIVITY_PROPERTY}->{ACTIVITY_TYPE_ID}->{$flatdta->{ACTIVITY_TYPE_ID}}->{$flatdta->{ACTIVITY_PROPERTY_NAME}}->{REMAP_NAME} = $flatdta->{ACTIVITY_PROPERTY_REMAP};
				$self->{FLATDTA_SUPPORT}->{REMAP}->{ACTIVITY_PROPERTY}->{ACTIVITY_TYPE_ID}->{$flatdta->{ACTIVITY_TYPE_ID}}->{$flatdta->{ACTIVITY_PROPERTY_NAME}}->{DATA_TYPE} = $flatdta->{ACTIVITY_PROPERTY_DATA_TYPE};
				$self->{FLATDTA_SUPPORT}->{REMAP}->{ACTIVITY_PROPERTY}->{ACTIVITY_TYPE_ID}->{$flatdta->{ACTIVITY_TYPE_ID}}->{$flatdta->{ACTIVITY_PROPERTY_NAME}}->{DATE_FORMAT} = $flatdta->{ACTIVITY_PROPERTY_DATE_FORMAT};
			}
		} elsif ($flatdta_check != 0) {
			die "ERROR! Inconsistency in FLATDTA_SUPPORT!\n";
		}
	}
	
	return $self;
}

sub _get_session_management { shift->{SESSION_MANAGEMENT} }

sub _get_session_management_obj { shift->{SESSION_MANAGEMENT}->{OBJ} }

sub _session_management_support { exists shift->{SESSION_MANAGEMENT} ? 1 : 0 }

sub _create_prepare {
	my ($self, $label, $sql, $params) = @_;
	
	croak "Missing mandatory parameter label" unless defined $label;
	
	return $self->{_PREPARE}->{$label} if defined $self->{_PREPARE}->{$label};

	croak "Missing mandatory parameter sql as label not defined" unless defined $sql;

	return $self->{_PREPARE}->{$label} unless defined $sql;

	local $@;
	
	eval{$self->{_PREPARE}->{$label} = $self->_dbh()->create_prepare($sql, $params)};
	
	$self->last_error("Unable to prepare query: ".$@)
		and return undef
			if ($@);
			
	return $self->{_PREPARE}->{$label};
}

sub _destroy_prepare {
	my ($self, $label) = @_;
	delete $self->{_PREPARE}->{$label} if exists $self->{_PREPARE}->{$label};
}

sub _destroy_prepared_statements{
	my $self = shift;
	print STDERR "Number of prepared statements to destroy = ". scalar (keys %{$self->{'_PREPARE'}})."\n" if $self->debug();
	print STDERR "Prepare statements destroyed: ".(join ',', keys %{$self->{'_PREPARE'}})."\n" if $self->debug();
	$self->{'_PREPARE'} = {};	
}

=head2 I<object>->B<disconnect>()

Prepara l'oggetto C<API::ART> per la serializzazione.

=cut
sub disconnect {
	my $self = shift;
	$self->_destroy_prepared_statements();
	delete $self->{EMAIL_SUPPORT};
	delete $self->{NOTIFICATION_SUPPORT};
	delete $self->{SYSTEM_SUPPORT};
	delete $self->{DB};
	delete $self->{SESSION_MANAGEMENT}->{OBJ} if $self->_session_management_support();
	# ivan e' d'accordo. NB: effettuato in quanto l'oggetto DateTime::Format::Strptime contiene
	# una regexp in un formato non supportato dalla libreria di serializzazione Storable 3.15
	delete $self->{INFO}->{DEFAULT_ISO_DATE_FORMAT};
}

=head2 I<object>->B<connect>()

Ripristina l'oggetto C<API::ART> dopo la sua deserializzazione.

=cut
sub connect {
	my $self = shift;

	$self->{INFO}->{DEFAULT_ISO_DATE_FORMAT} = DateTime::Format::Strptime->new(pattern   => $self->{STRPTIME_PATTERN});

	{
		local $@;
		eval {
			$self->{DB} = SIRTI::DB->new(
				$self->{INFO}->{CONNECTSTRING} ,
				{
					AUTOCOMMIT => $self->{_AUTOSAVE},
					DEBUG => $self->{_DEBUG}
				}
			);
		};
	}
	
	if ($self->_session_management_support()){
		# rimetto l'oggetto che ha distrutto la disconnect
		$self->{SESSION_MANAGEMENT}->{OBJ} = API::ART::Session->new(ART => $self, SID => $self->{SESSION_MANAGEMENT}->{SID}, SESSION_DURATION => $self->{SESSION_MANAGEMENT}->{SESSION_DURATION}, USER => $self->{USER});
		$self->last_error(__x("Unable to init API::ART::Session!"))
			&& return undef
				unless defined $self->{SESSION_MANAGEMENT}->{OBJ};
		# chiamo la start per aggiornare la sessione
		eval { $self->_get_session_management_obj->start() };
		if ($@){
			$self->last_error(__x("Unable to start session: {error}", error => $@));
			return undef;
		}
	}
}

=head2 I<object>->B<lookup_get>()

Restituisce l'oggetto interno di tipo C<SIRTI::ART::Lookup::ART>.

=cut
sub lookup_get { shift->{LOOKUP} }

=head2 I<object>->B<lookup_delete>()

Elimina l'oggetto interno di tipo C<SIRTI::ART::Lookup::ART>.
Utile dopo una deserializzazione dell'oggetto C<API::ART>.

=cut
sub lookup_delete { delete shift->{LOOKUP} }

=head2 I<object>->B<lookup_restore>(I<SIRTI::ART::Lookup::ART>)

Ripristina l'oggetto interno di tipo C<SIRTI::ART::Lookup::ART> con l'oggetto passato come argomento.
Utile dopo una deserializzazione dell'oggetto C<API::ART>.

=cut
sub lookup_restore { $_[0]->{LOOKUP} = $_[1] }

=head2 I<object>->B<logout>()

Effettua il logout dell'utente bloccando la gestione della sessione e cancellando l'oggetto API::ART::User.
La connessione con il DB e la LookupArt rimangono valide

=cut
sub logout {
	my $self = shift;
	
	if ($self->_session_management_support()){
		# chiamo la end per terminare la sessione
		eval { $self->_get_session_management_obj->end() };
		if ($@){
			$self->last_error(__x("Unable to end session: {error}", error => $@));
			return undef;
		}
	}
	
	# Elimino il riferimento all'oggetto API::ART::User
	delete $self->{USER};

	return 1;
}

=head2 I<object>->B<version>()

Ritorna il numero di versione del package (sinonimo di I<package>->VERSION()).

=cut
sub version {
	my $self = shift;
	return $VERSION;
}

=head2 I<object>->B<last_error>()

Ritorna la descrizione dell'ultimo errore che si e' verificato.

=cut
sub last_error {
	# &{ $_[0] }("LAST_ERROR", @_[ 1 .. $#_ ])
	my $self = shift;
	my $msg = shift;
	if (defined $msg) {
		$self->{LAST_ERROR} = $msg;
	} else {
		return $self->{LAST_ERROR};
	}
}

=head2 I<object>->B<clear_last_error>()

Resetta la descrizione dell'ultimo errore che si e' verificato.

=cut
sub clear_last_error { ($_[0]->last_error('') ? 0 : 1) }

=head2 I<object>->B<api_support>()

Ritorna 1 se l'istanza a cu si e' connessi ha pieno supporto alle API::ART.

=cut
sub api_support { $_[0]->{"API_SUPPORT"} }

=head2 I<object>->B<activity_lock_support>()

Ritorna 1 se l'istanza a cu si e' connessi ha supporto al lock delle attività.

=cut
sub activity_lock_support { $_[0]->info()->{"ACTIVITY_LOCK_SUPPORT"} }

=head2 I<object>->B<group_extended_support>()

Ritorna 1 se l'istanza a cu si e' connessi ha supporto ai gruppi privati e autogenerati.

=cut
sub group_extended_support { $_[0]->info()->{"GROUP_EXTENDED_SUPPORT"} }

=head2 I<object>->B<session_id>()

Ritorna la B<session_id> che verrà utilizzata (anche) nel lock delle attività.

=cut
sub session_id() { $_[0]->info()->{SESSION_ID} }


=head2 I<object>->B<flatdta_support>()

Ritorna la struttura delle FlatDta definite se l'istanza a cu si e' connessi ha pieno supporto alle PT.

=cut
sub flatdta_support { $_[0]->{"FLATDTA_SUPPORT"} ? 1 : 0 }

=head2 I<object>->B<get_flatdta_list_by_activity_type>()

Ritorna la struttura delle FlatDta dato un ACTIVITY_TYPE_NAME o un ACTIVITY_TYPE_ID. Se non vengono passati parametri ritorna un HASHREF con le chiavi ACTIVITY_TYPE_NAME e ACTIVITYE_TYPE_ID con tutta la configurazione.

=cut

sub _get_flatdta_support { $_[0]->{"FLATDTA_SUPPORT"} }

sub get_flatdta_list_by_activity_type {
	my $self = shift;
	my %params = @_;
	
	$self->last_error('FLATDTA_SUPPORT not enable!')
		unless $self->flatdta_support();
	
	if (defined $params{ACTIVITY_TYPE_ID}) {
		return $self->_get_flatdta_support->{ACTIVITY_TYPE_ID}->{$params{ACTIVITY_TYPE_ID}};
	} elsif (defined $params{ACTIVITY_TYPE_NAME}){
		return $self->_get_flatdta_support->{ACTIVITY_TYPE_NAME}->{$params{ACTIVITY_TYPE_NAME}};
	} else {
		return {
			ACTIVITY_TYPE_ID => $self->_get_flatdta_support->{ACTIVITY_TYPE_ID}
			,ACTIVITY_TYPE_NAME => $self->_get_flatdta_support->{ACTIVITY_TYPE_NAME}
		}
	}
}

=head2 I<object>->B<get_flatdta_list_remap_property_by_activity_type>()

Ritorna il rimappamento delle property delle FlatDta dato un ACTIVITY_TYPE_NAME o un ACTIVITY_TYPE_ID. Se non vengono passati parametri ritorna un HASHREF con le chiavi ACTIVITY_TYPE_NAME e ACTIVITYE_TYPE_ID con tutta la configurazione.

=cut
sub get_flatdta_list_remap_property_by_activity_type {
	my $self = shift;
	my %params = @_;
	
	$self->last_error('FLATDTA_SUPPORT not enable!')
		unless $self->flatdta_support();
	
	if (defined $params{ACTIVITY_TYPE_ID}) {
		return $self->_get_flatdta_support->{REMAP}->{ACTIVITY_PROPERTY}->{ACTIVITY_TYPE_ID}->{$params{ACTIVITY_TYPE_ID}};
	} elsif (defined $params{ACTIVITY_TYPE_NAME}){
		return $self->_get_flatdta_support->{REMAP}->{ACTIVITY_PROPERTY}->{ACTIVITY_TYPE_NAME}->{$params{ACTIVITY_TYPE_NAME}};
	} else {
		return {
			ACTIVITY_TYPE_ID => $self->_get_flatdta_support->{REMAP}->{ACTIVITY_PROPERTY}->{ACTIVITY_TYPE_ID}
			,ACTIVITY_TYPE_NAME => $self->_get_flatdta_support->{REMAP}->{ACTIVITY_PROPERTY}->{ACTIVITY_TYPE_NAME}
		}
	}
}

=head2 I<object>->B<get_flatdta_list_remap_table_by_activity_type>()

Ritorna il rimappamento delle tabelle delle FlatDta dato un ACTIVITY_TYPE_NAME o un ACTIVITY_TYPE_ID. Se non vengono passati parametri ritorna un HASHREF con le chiavi ACTIVITY_TYPE_NAME e ACTIVITYE_TYPE_ID con tutta la configurazione.

=cut
sub get_flatdta_list_remap_table_by_activity_type {
	my $self = shift;
	my %params = @_;
	
	$self->last_error('FLATDTA_SUPPORT not enable!')
		unless $self->flatdta_support();
	
	if (defined $params{ACTIVITY_TYPE_ID}) {
		return $self->_get_flatdta_support->{REMAP}->{TABLE}->{ACTIVITY_TYPE_ID}->{$params{ACTIVITY_TYPE_ID}};
	} elsif (defined $params{ACTIVITY_TYPE_NAME}){
		return $self->_get_flatdta_support->{REMAP}->{TABLE}->{ACTIVITY_TYPE_NAME}->{$params{ACTIVITY_TYPE_NAME}};
	} else {
		return {
			ACTIVITY_TYPE_ID => $self->_get_flatdta_support->{REMAP}->{TABLE}->{ACTIVITY_TYPE_ID}
			,ACTIVITY_TYPE_NAME => $self->_get_flatdta_support->{REMAP}->{TABLE}->{ACTIVITY_TYPE_NAME}
		}
	}
}


=head2 I<object>->B<email_support>()

Ritorna 1 se l'istanza a cu si e' connessi ha il supporto per l'invio delle mail, altrimenti 0.

=cut
sub email_support { $_[0]->_get_email_sender() ? 1 : 0 }

sub _get_email_sender {
	
	my $self = shift;
	local $@;
	
	return $self->{EMAIL_SUPPORT} if exists $self->{EMAIL_SUPPORT};

	$self->{EMAIL_SUPPORT} = eval{
		SIRTI::ART::RemoteActivity::Source->new(
			DB => $self->_dbh()
			,SESSION_DESCRIPTION => 'API::ART - Email Support'
			,SOURCE_CONTEXT => $self->{INFO}->{ARTID}
			,TARGET_CONTEXT => 'API::ART::Email'
		)
	};
	
	return $self->{EMAIL_SUPPORT};
}

=head2 I<object>->B<send_email>()

Invio una email tramite il supporto API::ART se presente (verificare tramite la chiamata $art->email_support()).

Ritorna true in caso di successo, false altrimenti.

Parametri:

=over 4

=item B<TO>

Obbligatorio. Arrayref con i destinatari della email.

=item I<SUBJECT>

Obbligatorio. Subject della email.

=item B<BODY>

Obbligatorio. Body della email.

=item B<REF>

Obbligatorio. Riferimento interno della email.

=item B<CC>

Opzionale. Arrayref con i destinatari in copia della email.

=item B<BCC>

Opzionale. Arrayref con i destinatari in copia nascosta della email.

=item B<SENDER>

Opzionale. Mittente della email.

=item B<REPLY_TO>

Opzionale. ReplyTo della email.

=item B<ATTACHMENTS_DIRECTORY>

Opzionale. Path temporaneo degli allegati da aggiungere alla email: tutti i file presenti verranno allegati
NB: la cartella verrà eliminata una volta inviata la mail

=back

=cut

sub send_email {
	my $self = shift;
	
	croak 'Email Service not available!' unless $self->email_support();
	
	$self->clear_last_error();
	
	my %params = @_;
	my $errmsg = '';

	$self->last_error($errmsg)
		&& return undef
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					TO => { isa => 'ARRAY' }
					,SUBJECT => { isa => 'SCALAR' }
					,REF =>  { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					CC => { isa => 'ARRAY' }
					,BCC => { isa => 'ARRAY' }
					,BODY => { isa => 'SCALAR' }
					,BODY_HTML => { isa => 'SCALAR' }
					,SENDER => { isa => 'SCALAR' }
					,REPLY_TO => { isa => 'SCALAR' }
					,ATTACHMENTS_DIRECTORY => { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS	=> 0
			);

	if (!defined $params{BODY} && !defined $params{BODY_HTML}){
		$self->last_error('At least one param between BODY and BODY_HTML must be defined');
		return undef;
	}

	my $ra_data = {};
	
	for my $key (keys %params){
		next if $key eq 'REF';
		$ra_data->{$key} = $params{$key} if defined $params{$key};
	}
	
	my $rc = $self->_get_email_sender()->insert(
		EVENT => 'SEND_EMAIL',
		SOURCE_REF => $params{REF},
		DATA => $ra_data
	);
	
	unless ($rc){
		$self->last_error('Unable to send email');
		return undef;
	}
	
	return $rc;
}

=head2 I<object>->B<system_blobs_repository>()

Ritorna il path dove salvare/repererire il contenuto delle System Property di tipo BLOB

=cut

sub system_blobs_repository { $_[0]->{SYSTEM_BLOB_REPOSIROTY}; }


=head2 I<object>->B<notification_activity_support>()

Ritorna 1 se l'istanza a cu si e' connessi ha il supporto per l'invio delle notifiche delle attività, altrimenti 0.

=cut

sub notification_activity_support { $_[0]->get_notification_activity_sender() ? 1 : 0 }

=head2 I<object>->B<get_notification_activity_sender>()

Ritorna l'oggetto per l'invio delle notifiche delle attività
NB: verificare sempre tramite il metodo notification_activity_support la possibilità dell'invio

=cut

sub get_notification_activity_sender {
	
	my $self = shift;
	local $@;
	
	return $self->{NOTIFICATION_SUPPORT} if exists $self->{NOTIFICATION_SUPPORT};

	$self->{NOTIFICATION_SUPPORT} = eval{
		SIRTI::ART::RemoteActivity::Source->new(
			DB => $self->_dbh()
			,SESSION_DESCRIPTION => 'API::ART::Activity - Notification Support'
			,SOURCE_CONTEXT => $self->{INFO}->{ARTID}
			,TARGET_CONTEXT => 'API::ART::Act::N'
		)
	};
	
	return $self->{NOTIFICATION_SUPPORT};
}

=head2 I<object>->B<system_support>()

Ritorna 1 se l'istanza a cu si e' connessi ha il supporto per l'invio di notifiche asincrone per i sistemi

=cut

sub system_support { $_[0]->get_system_sender() ? 1 : 0 }

=head2 I<object>->B<system_support>()

Ritorna l'oggetto per l'invio di notifiche per il sistema
NB: verificare sempre tramite il metodo system_support la possibilità dell'invio

=cut

sub get_system_sender {
	
	my $self = shift;
	local $@;
	
	return $self->{SYSTEM_SUPPORT} if exists $self->{SYSTEM_SUPPORT};

	$self->{SYSTEM_SUPPORT} = eval{
		SIRTI::ART::RemoteActivity::Source->new(
			DB => $self->_dbh()
			,SESSION_DESCRIPTION => 'API::ART::System'
			,SOURCE_CONTEXT => $self->{INFO}->{ARTID}
			,TARGET_CONTEXT => 'API::ART::System'
		)
	};
	
	return $self->{SYSTEM_SUPPORT};
}

=head2 I<object>->B<get_environment_mode>()

Ritorna la modalita' di environment impostata.

=cut
sub get_environment_mode { $_[0]->{"ENVIRONMENT"} }

=head2 I<object>->B<user>()

Ritorna il riferimento ad un oggetto API::ART::User

=cut
sub user { $_[0]->{"USER"} }


=head2 I<object>->B<shared_resources>()

Ritorna il riferimento ad un oggetto API::ART::SharedResources

=cut
sub shared_resources {
	my $self = shift;
	
	return $self->{SHARED_RESOURCES} if exists $self->{SHARED_RESOURCES};
	
	# Connessione al servizio SharedResources
	$self->{SHARED_RESOURCES} = eval {API::ART::SharedResources->new()};
	
	$self->last_error($@)
		&& return undef 
			if ($@);
	
	return $self->{SHARED_RESOURCES}; 
}

=head2 I<object>->B<info>( [ I<scalar> ] )

Ritorna informazioni sull'istanza dell'oggetto API::ART.

Senza argomenti ritorna un I<hashref> contenente tutte le informazioni disponibili:

=over 4

=item B<VERSION>

sinonimo di I<object>->version()

=item B<ARTID>

ARTID passato al costruttore

=item B<CONNECTSTRING>

stringa di connessione al DB utilizzata

=item B<USER>

nome utente ART

=item B<HOST>

host name in cui e' in esecuzione lo script

=item B<APPLICATION_USER>

utente applicativo (shell user)

=item B<SCRIPT_NAME>

nome script in esecuzione

=item B<SCRIPT_PARAMETERS>

argomenti dello script in esecuzione

=item B<CALLER>

(usato in caso di debug)

=item B<DEFAULT_DATE_FORMAT>

formato standard per le date

=item B<DEFAULT_TIME_ZONE>

time-zone di default per la memorizzazione delle Activity Property di tipo ISODAT 

=item B<DEFAULT_ISO_DATE_FORMAT_ORACLE>

maschera di formato Oracle per la conversione da stringa ISODAT a TIMESTAMP_TZ 

=item B<DEFAULT_ISO_DATE_FORMAT>

oggetto di tipo DateTime::Format::Strptime per la conversione da ... a ... (citofonare Longhi) 

=item B<SESSION_ID>

B<session_id> che verrà utilizzata (anche) nel lock delle attività


=back

=cut
sub info {
	my $self = shift;
	my $info = $self->{"INFO"};
	my $key = shift;
	return $info->{$key} if defined $key;
	return $info;
}


=head2 I<object>->B<save>()

Salva (COMMITTa) le operazioni eseguite sul db di ART.

=cut
sub save {
	my $self = shift;
	$self->_dbh()->commit();
}


=head2 I<object>->B<cancel>()

Annulla (ROLLBACKa) le operazioni eseguite sul db di ART.

=cut
sub cancel {
	my $self = shift;
	$self->_dbh()->rollback();
	$self->_refresh_lookup();
}

=head2 I<object>->B<quote>()

Quota stringa.

=cut
sub quote { $_[0]->_dbh()->quote($_[1]) }

sub _get_save_all_properties { $_[0]->{"SAVE_ALL_PROPERTIES"} }

sub _get_alias_system_property { $_[0]->{"ALIAS_SYSTEM_PROPERTY"} }

=head2 Metodi informativi sulla struttura di ART

Per i seguenti metodi, I<ELEMENT> e' uno dei seguenti elementi della struttura di ART:

=item I<object>->B<system_type>

=item I<object>->B<system_category>

=item I<object>->B<system_class>

=item I<object>->B<object_type>

=item I<object>->B<system_property>( [ (ACTIVITY_TYPE_ID|ACTIVITY_TYPE_NAME) => <scalar>, EXTENDED_OUTPUT => <0|1> ] )

=item I<object>->B<activity_type>( [ EXTENDED_OUTPUT => <0|1> ] )

=item I<object>->B<activity_action>( [ (ACTIVITY_TYPE_ID|ACTIVITY_TYPE_NAME) => <scalar> [, (INITIAL_STATUS_ID|INITIAL_STATUS_NAME) => <scalar> ] ] )

=item I<object>->B<activity_status>( [ (ACTIVITY_TYPE_ID|ACTIVITY_TYPE_NAME) => <scalar> ] )

=item I<object>->B<activity_property>( [ (ACTIVITY_TYPE_ID|ACTIVITY_TYPE_NAME) => <scalar>, ACTION => <scalar>, EXTENDED_OUTPUT => <0|1> ] )

=item I<object>->B<group>( [ FILTER => <hashref>, EXTENDED_OUTPUT => <0|1> ] )

=item I<object>->B<role>( [ FILTER => <hashref>, EXTENDED_OUTPUT => <0|1> ] )

=item I<object>->B<user>( [ FILTER => <hashref>, EXTENDED_OUTPUT => <0|1> ] )

mentre B<element_id> e B<element_name> rappresentano rispettivamente l'ID ed il NOME degli
elementi per i quali si vogliono avere informazioni.

=head2 I<object>->B<enum_>I<ELEMENT>()

Ritorna un I<hashref> contenente tutti gli elementi definiti per l'I<ELEMENT> in oggetto.

Il metodo B<enum_activity_type> restituisce un I<hashref> ACTIVITY_TYPE_NAME => ACTIVITY_TYPE_ID. Se invocato con il paramentro opzionale SHOW_ONLY_WITH_VISIBILITY
vengono restituite solo le attività su cui l'operatore ha visibilità. Se invocato con il parametro opzionale EXTENDED_OUTPUT => 1
restituira' un I<arrayref> di I<hashref> con le seguenti chiavi: ACTIVITY_TYPE_ID, ACTIVITY_TYPE_NAME, ACTIVITY_TYPE_DESCRIPTION, ACTIVITY_TYPE_CAN_ASSIGN, ACTIVITY_TYPE_CAN_DEASSIGN, ACTIVITY_TYPE_HAS_DASHBOARD, ACTIVITY_TYPE_HAS_REPORT, ACTIVITY_TYPE_UI_ROUTE, ACTIVITY_TYPE_DOCUMENT_TYPES.

I metodi B<enum_activity_action> e B<enum_activity_status> accettano opzionalmente il parametro I<ACTIVITY_TYPE_ID> => <scalar> oppure I<ACTIVITY_TYPE_NAME> => <scalar>.
Impostando questo parametro addizionale i metodi ritorneranno un <arrayref> contenente solo azioni/status definiti per l'attivita' di tipo specificato con <scalar>.

Il metodo B<enum_activity_action> accetta, in aggiunta al parametro ACTIVITY_TYPE_ID o ACTIVITY_TYPE_NAME,
anche un parametro opzionale INITIAL_STATUS_ID oppure INITIAL_STATUS_NAME, e restituisce solo le azioni configurate per lo stato iniziale di riferimento

NOTA: Il metodo B<enum_activity_action> restituisce solo le azioni che l'utente ha il permesso di effettuare.

Il metodo B<enum_activity_property> accetta, opzionalmente i parametri ACTIVITY_TYPE_ID o ACTIVITY_TYPE_NAME per ottenere le property relativa ad un solo TIPO_ATTIVITA,
il parametro ACTION per filtrare solo sulla determinata ACTION, il paramentro SHOW_ONLY_WITH_VISIBILITY per ricercare solo le activity properties su cui l'operatore ha visibilità
 e il parametro EXTENDED_OUTPUT. Con il paramentro EXTENDED_OUTPUT settato ad 1 si otterra' associato a ciascun nome di property un HASHREF con le seguenti chiavi:

=item B<TYPE> Contiene il TIPO_UI.

=item B<LABEL> Contiene la LABEL (se non definita è uguale al nome del TDTA).

=item B<HINT> Contiene l'HINT (se presente restituisce il suggerimento del TDTA).

=item B<PREDEFINED_VALUE> Contiene i valori di predefiniti in struttura. Di seguito le configurazioni possibile riportate come esempio

{
	"type" => "bracketNotation",
	"value" => "[\"system\"][\"properties\"][\"technicalArea\"]"
}

{
	"type" => "const",
	"value" => "SIRTI SPA"
}

{
	"type" => "now"
}

=item B<VALUES> Se TIPO_UI in ('LOOKUP','MOOKUP'), contiene un ARRAY di HASHREF con le informazioni richieste in fase di configurazione. Le chiavi inizianti con il carattere '$' si riferiscono alle info, le altre alle properties.
Se TIPO_UI = 'TAGS', contiene un ARRAY di HASHREF con le chiavi TAG e COUNT.
Se TIPO_UI in ('LIST'), contiene un ARRAY di SCALAR con i valori possibili per la scelta.

=item B<TARGET> Se TIPO_UI in ('LOOKUP','MOOKUP'), contiene il riferimento per ricercare i valori della property nella chiave VALUES.

=item B<TEMPLATE> Se TIPO_UI in ('LOOKUP','MOOKUP') e LAYOUT diverso da TABLE, contiene il template di riferimento.

=item B<LAYOUT> Se TIPO_UI in ('LOOKUP','MOOKUP'), la chiave LAYOUT contiene il layout con il quale deve essere visualizzato sul client. Può valere SELECT (per visualizzare un campo html
  di tipo select a scelta singola in caso di LOOKUP, o a scelta multipla in caso di MOOKUP), BOX (per visualizzare dei radio button in caso di LOOKUP, o dei checkbox in caso
  di MOOKUP), TABLE (per visualizzare una tabella html in cui scegliere una sola riga in caso di LOOKUP, più righe in caso di MOOKUP) o AUTOCOMPLETE (per visualizzare
  una casella con autocompletamento). Se LAYOUT vale TABLE, la chiave VALUES
  restituita conterra' una chiave 'header' che descrive le colonne, e un chiave 'data' con la matrice dei valori da visualizzare nella tabella.
  Se LAYOUT vale AUTOCOMPLETE la chiave VALUES non sara' presente ma saranno presenti le seguenti chiavi: TYPE = 'URI', URI che contiente l'URI da invocare
  in modo asincrono per avere la lista dei valori possibili, CONTEXT uguale a 'App', 'Api' o 'Config' che indica il contesto dove applicare l'URI (contesto 'Api' => API::ART::REST
  /api/art/, 'App' => contesto specifico dell'applicazione es. /dii, 'Config' => contesto recuperabile dal config.json dell'applicazione tramite la chiave CONTEXT_KEY (questa chiave è definita solo in caso di CONTEXT eq 'Config')), DISPLAY_PROPERTY (solo se CONTEXT eq 'Config') che indica la chiave da visualizzare, KEY_PROPERTY (solo se CONTEXT eq 'Config') che indica la chiave univoca di aggancio, QUERY_KEY (solo se CONTEXT eq 'Config') che indica la chiave da utilizzare nelle rotte di lookup, RESPONSE_DATA_KEY (solo se CONTEXT eq 'Config') che indica la chiave della response da utilizzare
  , MIN_LENGTH (solo se CONTEXT eq 'Config') che indica la lunghezza minima di ricerca, ADD_FROM_AUTOCOMPLETE_ONLY che indica se devono essere accettati valori solo valori ritornati
  dall'URI oppure deve essere permesso l'inserimento di nuovi valori.

=item B<CURRENCY> Contiene la valuta da utilizzare (valorizzate se TYPE=CURRENCY).

=item B<FRACTION> Contiene il numero dei decimali della valuta da utilizzare (valorizzate se TYPE=CURRENCY).

=back

Il metodo B<enum_system_property> accetta, opzionalmente i parametri SYSTEM_TYPE_ID o SYSTEM_TYPE_NAME per ottenere le system property relative ad un solo TIPO_SISTEMA e il paramentro EXTENDED_OUTPUT.
Con il paramentro EXTENDED_OUTPUT settato ad 1 si otterra' associato a ciascun nome di system property un HASHREF con le seguenti chiavi:

=item B<ID> Contiene l'id del tipo dato tecnico sistema.

=item B<DESCRIPTION> Contiene la descrizione del tipo dato tecnico sistema.

=item B<MULTIPLEX> Contiene '1' se il tipo dato tecnico sistema puo' avere piu' valori, '0' altrimenti.

=item B<UM> Contiene l'unità di misura del dato quando applicabile.

=item B<IS_FILE> Contiene '1' se il tipo dato tecnico sistema e' di tipo FILE

=item B<IS_MEASURE> Contiene '1' se il tipo dato tecnico sistema e' di tipo MEASURE

=item B<VALUE_MIN> Contiene il valore minimo consentito se il tipo dato tecnico sistema e' di tipo numerico

=item B<VALUE_MAX> Contiene il valore massimo consentito se il tipo dato tecnico sistema e' di tipo numerico

=item B<IS_COUNTER> Contiene '1' se il tipo dato tecnico sistema e' di tipo COUNTER

=item B<TYPE> Contiene il TIPO.

=item B<CURRENCY> Contiene la valuta da utilizzare (valorizzate se TYPE=CURRENCY).

=item B<FRACTION> Contiene il numero dei decimali della valuta o del numero da utilizzare (valorizzate se TYPE=CURRENCY|INTEGER|LONG|FLOAT).

=back

Il metodo B<enum_user> accetta, opzionalmente i parametri FILTER per ottenere l'elenco degli utenti filtrati attraverso i criteri espressi nelle chiavi del parametro (NAME, FIRST_NAME, LAST_NAME, EMAIL, MOBILE_PHONE, SERVICE_USER, DISABLED, GROUPS, ROLES),
e il paramentro EXTENDED_OUTPUT. Con il paramentro EXTENDED_OUTPUT settato ad 1 si otterra' un array di HASHREF con le chiavi previste dal paramentro FILTER; se viene settato anche il parametro INCLUDE_MD5_PASSWORD verra' restituita anche la chiave MD5_PASSWORD.
NB: le ricerche sono case insensitive.

Il metodo B<enum_group> accetta, opzionalmente i parametri FILTER per ottenere l'elenco dei gruppi filtrati attraverso i criteri espressi nelle chiavi del paramentro (NAMES, DESCRIPTIONS),
e il paramentro EXTENDED_OUTPUT. Con il paramentro EXTENDED_OUTPUT settato ad 1 si otterra' un array di HASHREF con le chiavi previste dal paramentro FILTER. NB: le ricerche sono case insensitive.

Il metodo B<enum_role> accetta, opzionalmente i parametri FILTER per ottenere l'elenco dei ruoli filtrati attraverso i criteri espressi nelle chiavi del paramentro (NAMES, DESCRIPTIONS, ROLES, ROLE_ALIASES),
e il paramentro EXTENDED_OUTPUT. Con il paramentro EXTENDED_OUTPUT settato ad 1 si otterra' un array di HASHREF con le chiavi previste dal paramentro FILTER. NB: le ricerche sono case insensitive.

=head2 I<object>->B<test_>I<ELEMENT>B<_name>( I<element_name> )

Verifica se il nome dell'elemento specificato esiste

=head2 I<object>->B<test_>I<ELEMENT>B<_id>( I<element_id> )

Verifica se l'ID dell'elemento specificato esiste

=head2 I<object>->B<get_>I<ELEMENT>B<_name>( I<element_id> )

Ritorna il nome dell'elemento specificato.

=head2 I<object>->B<get_>I<ELEMENT>B<_id>( I<element_name> )

Ritorna l'ID dell'elemento specificato

=head2 I<object>->B<get_activity_action_description>( I<element_name> )

Ritorna la descrizione dell'azione specificata

=head2 I<object>->B<get_activity_action_info>()

Ritorna un hash con le info delle azioni. Accetta opzionalmente il parametro I<ACTIVITY_TYPE_ID> => <scalar> oppure I<ACTIVITY_TYPE_NAME> => <scalar> per filtrare sul tipo attività

=over 4

=item La struttura risultante e' la seguente:

	<hashref> => {
			<name> => {
					'ID' => <scalar>,
					'STARTING_FLAG' => <scalar>,
					'NAME' => <scalar>,
					'UI' => <scalar>,
					'DESCRIPTION' => <scalar>,
					'IS_STARTING_TYPE' => <boolean>, #FLAG_ACTION_PARTENZA 'A'
					'IS_FINAL_TYPE' => <boolean>, #FLAG_ACTION_PARTENZA 'C'
					'IS_SELF_ASSIGN_TYPE' => <boolean>, #FLAG_ACTION_PARTENZA 'E'
					'IS_SELF_DEASSIGN_TYPE' => <boolean>, #FLAG_ACTION_PARTENZA 'F'
					'IS_VIRTUAL_ASSIGN_TYPE' => <boolean>, #FLAG_ACTION_PARTENZA 'G'
					'IS_VIRTUAL_DEASSIGN_TYPE' => <boolean>, #FLAG_ACTION_PARTENZA 'H'
					'IS_ASSIGN_TYPE' => <boolean>, #FLAG_ACTION_PARTENZA 'P'
					'IS_PARK_TYPE' => <boolean>, #FLAG_ACTION_PARTENZA 'S'
					'IS_UPDATE_ACTIVITY_PROPERTY_TYPE', => <boolean> #FLAG_ACTION_PARTENZA 'T'
					'IS_VIRTUAL_TYPE' => <boolean>, #FLAG_ACTION_PARTENZA 'V'
					'DO_ASSIGN_NOTIFICATION' => <boolean> # true if email_support && IS_ASSIGN_TYPE
			}, ...
	}

=back

=head2 I<object>->B<get_activity_status_info>()

Ritorna un hash con le info degli stati. Accetta opzionalmente il parametro I<ACTIVITY_TYPE_ID> => <scalar> oppure I<ACTIVITY_TYPE_NAME> => <scalar> per filtrare sul tipo attività

=over 4

=item La struttura risultante e' la seguente:

	<hashref> => {
			<name> => {
					'ID' => <scalar>,
					'STARTING_FLAG' => <scalar>,
					'NAME' => <scalar>,
					'UI' => <scalar>,
					'DESCRIPTION' => <scalar>,
					'IS_ANY_STATUS' => <boolean> #FLAG_STATO_PARTENZA 'Y'
					'IS_VIRTUAL' => <boolean> #FLAG_STATO_PARTENZA 'V'
					'IS_FINAL' => <boolean> #FLAG_STATO_PARTENZA 'C|Q'
					'IS_STARTING' => <boolean> #FLAG_STATO_PARTENZA 'X'
			}, ...
	}

=back

=cut
sub enum_system_type {
	my $self = shift;
	my %params = @_;
	
	# recupero la visiblità dell'operatore connesso, eseguendo la query solo se necessario
	my $gruppi_abilitati = $params{SHOW_ONLY_WITH_VISIBILITY} ? $self->_dbh()->fetchall_hashref("
		select id_tipo_sistema, nome_tipo_sistema
		from tipi_sistema_gruppi tag
		where tag.id_gruppo in (".join (',', @{$self->user()->su()->activity()->groups()}).")
	") : [];
	
	my $l = $self->_lookup()->{TS_NAME};
	return $l unless $params{SHOW_ONLY_WITH_VISIBILITY};
	my %r = %{$l};
	for my $k (keys %{$l}){
		delete $r{$k} unless grep {$_->{NOME_TIPO_SISTEMA} eq $k} @{$gruppi_abilitati};
	}
	return \%r;
}
sub get_system_type_id { $_[0]->_lookup('id_tipo_sistema', @_[ 1 .. $#_ ]) }
sub get_system_type_name { $_[0]->_lookup('nome_tipo_sistema', @_[ 1 .. $#_ ]) }
sub test_system_type_id { $_[0]->_lookup('is_id_tipo_sistema', @_[ 1 .. $#_ ]) }
sub test_system_type_name { $_[0]->_lookup('is_nome_tipo_sistema', @_[ 1 .. $#_ ]) }

sub enum_system_category { $_[0]->_lookup()->{CS_NAME} }
sub get_system_category_id { $_[0]->_lookup('id_categoria_sistema', @_[ 1 .. $#_ ]) }
sub get_system_category_name { $_[0]->_lookup('nome_categoria_sistema', @_[ 1 .. $#_ ]) }
sub test_system_category_id { $_[0]->_lookup('is_id_categoria_sistema', @_[ 1 .. $#_ ]) }
sub test_system_category_name { $_[0]->_lookup('is_nome_categoria_sistema', @_[ 1 .. $#_ ]) }

sub enum_system_class { $_[0]->_lookup()->{KS_NAME} }
sub get_system_class_id { $_[0]->_lookup('id_classe_sistema', @_[ 1 .. $#_ ]) }
sub get_system_class_name { $_[0]->_lookup('nome_classe_sistema', @_[ 1 .. $#_ ]) }
sub test_system_class_id { $_[0]->_lookup('is_id_classe_sistema', @_[ 1 .. $#_ ]) }
sub test_system_class_name { $_[0]->_lookup('is_nome_classe_sistema', @_[ 1 .. $#_ ]) }

sub enum_object_type { $_[0]->_lookup()->{TO_NAME} }
sub get_object_type_id { $_[0]->_lookup('id_tipo_oggetto', @_[ 1 .. $#_ ]) }
sub get_object_type_name { $_[0]->_lookup('nome_tipo_oggetto', @_[ 1 .. $#_ ]) }
sub test_object_type_id { $_[0]->_lookup('is_id_tipo_oggetto', @_[ 1 .. $#_ ]) }
sub test_object_type_name { $_[0]->_lookup('is_nome_tipo_oggetto', @_[ 1 .. $#_ ]) }

sub get_system_property_id { $_[0]->_lookup('id_tdts', @_[ 1 .. $#_ ]) }
sub get_system_property_name { $_[0]->_lookup('nome_tdts', @_[ 1 .. $#_ ]) }
sub test_system_property_id { $_[0]->_lookup('is_id_tdts', @_[ 1 .. $#_ ]) }
sub test_system_property_name { $_[0]->_lookup('is_nome_tdts', @_[ 1 .. $#_ ]) }

## FIXME: commentare

sub enum_activity_type {
	my $self = shift;
	my %params = @_;
	
	# recupero la visiblità dell'operatore connesso, eseguendo la query solo se necessario
	my $gruppi_abilitati = $params{SHOW_ONLY_WITH_VISIBILITY} ? $self->_dbh()->fetchall_hashref("
		select id_tipo_attivita, nome_tipo_attivita
		from tipi_attivita_gruppi tag
		where tag.id_gruppo in (".join (',', @{$self->user()->su()->activity()->groups()}).")
		union
		select ta.id_tipo_attivita, ta.nome_tipo_attivita
		from permission_action pa
		join tipi_attivita ta on ta.id_tipo_Attivita = pa.id_tipo_attivita
		join action ac on ac.id_action = pa.id_action_permessa and ac.flag_action_partenza = 'A'
		where pa.id_gruppo_abilitato in (".join (',', @{$self->user()->su()->activity()->groups()}).")
	") : [];
	
	if (!$params{EXTENDED_OUTPUT}){
		my $l = $self->_lookup()->{TA_NAME};
		return $l unless $params{SHOW_ONLY_WITH_VISIBILITY};
		my %r = %{$l};
		for my $k (keys %{$l}){
			delete $r{$k} unless grep {$_->{NOME_TIPO_ATTIVITA} eq $k} @{$gruppi_abilitati};
		}
		return \%r;
	} else {
		my $output = [];
	
		for my $id (keys %{$self->_lookup()->{TA_ID}}) {
			next if $params{SHOW_ONLY_WITH_VISIBILITY} && ! grep {$_->{ID_TIPO_ATTIVITA} eq $id} @{$gruppi_abilitati};
			my $action = $self->enum_activity_action(ACTIVITY_TYPE_ID => $id);
			my @keys = keys %{$action};
			my $out = {
				 ACTIVITY_TYPE_ID				=> $id
				,ACTIVITY_TYPE_NAME				=> $self->_lookup()->{TA_ID}->{$id}
				,ACTIVITY_TYPE_DESCRIPTION		=> $self->_lookup()->{TA_DESCRIPTION}->{$id}
				,ACTIVITY_TYPE_CAN_ASSIGN		=> (grep {$_ eq $self->_get_activity_virtual_action_assign_name()} @keys) ? 1 : 0
				,ACTIVITY_TYPE_CAN_DEASSIGN		=> (grep {$_ eq $self->_get_activity_virtual_action_deassign_name()} @keys) ? 1 : 0
				,ACTIVITY_TYPE_HAS_DASHBOARD	=> $self->_lookup()->{TA_HAS_DASHBOARD}->{$id}
				,ACTIVITY_TYPE_HAS_REPORT		=> $self->_lookup()->{TA_HAS_REPORT}->{$id}
				,ACTIVITY_TYPE_UI_ROUTE			=> $self->_lookup()->{TA_UI_ROUTE}->{$id}
				,ACTIVITY_TYPE_DOCUMENT_TYPES	=> $self->_lookup()->{TA_DOCUMENT_TYPES}->{$id}
				,ACTIVITY_TYPE_AP_GROUPS_PERM	=> 0
			};
			my $ta_ap_groups_perm = exists $self->_lookup()->{TA_AP_GROUPS_PERM}->{$id} ? $self->_lookup()->{TA_AP_GROUPS_PERM}->{$id} : [];

			for my $g (@{$self->user()->groups}){
				if (grep {$g eq $_} @{$ta_ap_groups_perm}){
					$out->{ACTIVITY_TYPE_AP_GROUPS_PERM} = 1;
					last;
				}
			}

			push @{$output}, $out
		}
		
		@{$output} = sort {$a->{ACTIVITY_TYPE_NAME} cmp $b->{ACTIVITY_TYPE_NAME}} @{$output};
		
		return $output;
	}
	

	
}

=head2 I<object>->B<get_activity_next_id>( )

Ritorna il prossimo ID_ATTIVITA staccato dalla sequence

=cut

sub get_activity_next_id { $_[0]->_dbh()->fetch_minimalized("select seq_attivita.nextval from dual")}
sub get_activity_type_id { $_[0]->_lookup('id_tipo_attivita', @_[ 1 .. $#_ ]) }
sub get_activity_type_name { $_[0]->_lookup('nome_tipo_attivita', @_[ 1 .. $#_ ]) }
sub get_activity_type_description { $_[0]->_lookup('descrizione_tipo_attivita', @_[ 1 .. $#_ ]) }
sub test_activity_type_id { $_[0]->_lookup('is_id_tipo_attivita', @_[ 1 .. $#_ ]) }
sub test_activity_type_name { $_[0]->_lookup('is_nome_tipo_attivita', @_[ 1 .. $#_ ]) }

sub enum_activity_action {
	my $self = shift;
	if (@_) {
		return $self->_enum_activity_action_by_type(@_);
	} else {
		return $self->_lookup()->{ACTION_NAME};
	}
}
sub get_activity_action_id { $_[0]->_lookup('id_azione', @_[ 1 .. $#_ ]) }
sub get_activity_action_name { $_[0]->_lookup('nome_azione', @_[ 1 .. $#_ ]) }
sub get_activity_action_description { $_[0]->_lookup('descrizione_azione', @_[ 1 .. $#_ ]) }
sub get_activity_action_info {
	my $self = shift;
	my %params = @_;
	
	my $ret = {};
	
	my $filtro = $self->enum_activity_action(%params);
	
	for my $s (values %{$filtro}){
		$ret->{$self->_lookup()->{ACTION}->{$s}->{NAME}} = $self->_lookup()->{ACTION}->{$s};
		#aggiungo ID in quanto non presente nell'hash
		$ret->{$self->_lookup()->{ACTION}->{$s}->{NAME}}->{ID} = $s;
		$ret->{$self->_lookup()->{ACTION}->{$s}->{NAME}}->{IS_STARTING_TYPE} = defined $self->_lookup()->{ACTION}->{$s}->{STARTING_FLAG} && $self->_lookup()->{ACTION}->{$s}->{STARTING_FLAG} =~/^(A)$/ ? $JSON::true : $JSON::false;
		$ret->{$self->_lookup()->{ACTION}->{$s}->{NAME}}->{IS_FINAL_TYPE} = defined $self->_lookup()->{ACTION}->{$s}->{STARTING_FLAG} && $self->_lookup()->{ACTION}->{$s}->{STARTING_FLAG} =~/^(C)$/ ? $JSON::true : $JSON::false;
		$ret->{$self->_lookup()->{ACTION}->{$s}->{NAME}}->{IS_SELF_ASSIGN_TYPE} = defined $self->_lookup()->{ACTION}->{$s}->{STARTING_FLAG} && $self->_lookup()->{ACTION}->{$s}->{STARTING_FLAG} =~/^(E)$/ ? $JSON::true : $JSON::false;
		$ret->{$self->_lookup()->{ACTION}->{$s}->{NAME}}->{IS_SELF_DEASSIGN_TYPE} = defined $self->_lookup()->{ACTION}->{$s}->{STARTING_FLAG} && $self->_lookup()->{ACTION}->{$s}->{STARTING_FLAG} =~/^(F)$/ ? $JSON::true : $JSON::false;
		$ret->{$self->_lookup()->{ACTION}->{$s}->{NAME}}->{IS_VIRTUAL_ASSIGN_TYPE} = defined $self->_lookup()->{ACTION}->{$s}->{STARTING_FLAG} && $self->_lookup()->{ACTION}->{$s}->{STARTING_FLAG} =~/^(G)$/ ? $JSON::true : $JSON::false;
		$ret->{$self->_lookup()->{ACTION}->{$s}->{NAME}}->{IS_VIRTUAL_DEASSIGN_TYPE} = defined $self->_lookup()->{ACTION}->{$s}->{STARTING_FLAG} && $self->_lookup()->{ACTION}->{$s}->{STARTING_FLAG} =~/^(H)$/ ? $JSON::true : $JSON::false;
		$ret->{$self->_lookup()->{ACTION}->{$s}->{NAME}}->{IS_ASSIGN_TYPE} = defined $self->_lookup()->{ACTION}->{$s}->{STARTING_FLAG} && $self->_lookup()->{ACTION}->{$s}->{STARTING_FLAG} =~/^(P)$/ ? $JSON::true : $JSON::false;
		$ret->{$self->_lookup()->{ACTION}->{$s}->{NAME}}->{IS_PARK_TYPE} = defined $self->_lookup()->{ACTION}->{$s}->{STARTING_FLAG} && $self->_lookup()->{ACTION}->{$s}->{STARTING_FLAG} =~/^(S)$/ ? $JSON::true : $JSON::false;
		$ret->{$self->_lookup()->{ACTION}->{$s}->{NAME}}->{IS_UPDATE_ACTIVITY_PROPERTY_TYPE} = defined $self->_lookup()->{ACTION}->{$s}->{STARTING_FLAG} && $self->_lookup()->{ACTION}->{$s}->{STARTING_FLAG} =~/^(T)$/ ? $JSON::true : $JSON::false;
		$ret->{$self->_lookup()->{ACTION}->{$s}->{NAME}}->{IS_VIRTUAL_TYPE} = defined $self->_lookup()->{ACTION}->{$s}->{STARTING_FLAG} && $self->_lookup()->{ACTION}->{$s}->{STARTING_FLAG} =~/^(V)$/ ? $JSON::true : $JSON::false;
		$ret->{$self->_lookup()->{ACTION}->{$s}->{NAME}}->{DO_ASSIGN_NOTIFICATION} = $ret->{$self->_lookup()->{ACTION}->{$s}->{NAME}}->{IS_ASSIGN_TYPE} && $self->email_support() ? $JSON::true : $JSON::false;
	} 
	
	return $ret;
	
}
sub test_activity_action_id { $_[0]->_lookup('is_id_azione', @_[ 1 .. $#_ ]) }
sub test_activity_action_name { $_[0]->_lookup('is_nome_azione', @_[ 1 .. $#_ ]) }

sub enum_activity_status {
	my $self = shift;
	if (@_) {
		return $self->_enum_activity_status_by_type(@_);
	} else {
		return $self->_lookup()->{STATI_NAME};
	}
}
sub get_activity_status_id { $_[0]->_lookup('id_stato', @_[ 1 .. $#_ ]) }
sub get_activity_status_name { $_[0]->_lookup('nome_stato', @_[ 1 .. $#_ ]) }
sub get_activity_status_description { 
	my $self = shift;
	my $status = shift;
	my $id_stato = $self->get_activity_status_id($status);
	$self->last_error("Status $status not found")
		and return undef
			unless $id_stato;
	
	return $self->_lookup()->{STATI}->{$id_stato}->{DESCRIZIONE};
}
sub get_activity_status_info {
	my $self = shift;
	my %params = @_;
	
	my $ret = {};
	
	my $filtro = $self->enum_activity_status(%params);
	
	for my $s (values %{$filtro}){
		$ret->{$self->_lookup()->{STATI}->{$s}->{NAME}} = $self->_lookup()->{STATI}->{$s};
		#aggiungo ID in quanto non presente nell'hash
		$ret->{$self->_lookup()->{STATI}->{$s}->{NAME}}->{ID} = $s;
		$ret->{$self->_lookup()->{STATI}->{$s}->{NAME}}->{IS_FINAL} = $self->_lookup()->{STATI}->{$s}->{STARTING_FLAG} =~/^(C|Q)$/ ? $JSON::true : $JSON::false;
		$ret->{$self->_lookup()->{STATI}->{$s}->{NAME}}->{IS_STARTING} = $self->_lookup()->{STATI}->{$s}->{STARTING_FLAG} =~/^(X)$/ ? $JSON::true : $JSON::false;
		$ret->{$self->_lookup()->{STATI}->{$s}->{NAME}}->{IS_VIRTUAL} = $self->_lookup()->{STATI}->{$s}->{STARTING_FLAG} =~/^(V)$/ ? $JSON::true : $JSON::false;
		$ret->{$self->_lookup()->{STATI}->{$s}->{NAME}}->{IS_ANY_STATUS} = $self->_lookup()->{STATI}->{$s}->{STARTING_FLAG} =~/^(Y)$/ ? $JSON::true : $JSON::false;
	} 
	
	return $ret;
	
}
sub test_activity_status_id { $_[0]->_lookup('is_id_stato', @_[ 1 .. $#_ ]) }
sub test_activity_status_name { $_[0]->_lookup('is_nome_stato', @_[ 1 .. $#_ ]) }

sub get_allowed_groups_by_action {
	my $self = shift;
	my %params = @_;
	
	my $activity_type_id;
	
	if (defined $params{ACTIVITY_TYPE_ID}) {
		$activity_type_id = $params{ACTIVITY_TYPE_ID};
	} elsif (defined $params{ACTIVITY_TYPE_NAME}){
		$activity_type_id = $self->get_activity_type_id($params{ACTIVITY_TYPE_NAME});
	} else {
		$self->last_error("Missing ACTIVITY_TYPE_NAME|ACTIVITY_TYPE_ID!");
		return undef;
	}
	
	my $action_id;
	
	if (defined $params{ACTION_ID}) {
		$action_id = $params{ACTIVITY_TYPE_ID};
	} elsif (defined $params{ACTION_NAME}){
		$action_id = $self->get_activity_action_id($params{ACTION_NAME});
	} else {
		$self->last_error("Missing ACTION_NAME|ACTION_ID!");
		return undef;
	};
	
	my $initial_status_id;
	
	if (defined $params{STATUS_ID}) {
		$initial_status_id = $params{STATUS_ID};
	} elsif (defined $params{STATUS_NAME}){
		$initial_status_id = $self->get_activity_status_id($params{STATUS_NAME});
	} else {
		$self->last_error("Missing ACTION_NAME|ACTION_ID!");
		return undef;
	};

	my $sql = "select id_gruppo_abilitato From permission_action
				where id_tipo_attivita = ?
				and id_stato_iniziale = ?
				and id_Action_permessa = ?";
	
	$self->_create_prepare(__PACKAGE__.'_ALLOWED_GROUPS_BY_ACTION', $sql) || return undef;
	
	my $db = $self->_dbh();
	
	my $result = $self->_create_prepare(__PACKAGE__.'_ALLOWED_GROUPS_BY_ACTION')->fetchall_arrayref($activity_type_id, $initial_status_id,$action_id) or croak 'Errore: '.$db->get_errormessage();	
	
	my @ret = map {$_->[0]} @{$result};
	
	return \@ret;
	
}
=head2 I<object>->B<get_activity_final_status>()

Ritorna un I<arrayref> contenente gli ID degli stati "finali" definiti.

=cut
sub get_activity_final_status { $_[0]->{"FINAL_STATUS"} }

=head2 I<object>->B<is_activity_final_status>( ID => I<STATUS_ID> | NAME => I<STATUS_NAME> )

Ritorna 1 se lo stato fa' riferimento ad uno degli stati "finali" definiti.

=cut
sub is_activity_final_status {
	my $self = shift;
	my %params = @_;
	croak "Expecting ID or NAME parameters!\n" unless defined $params{ID} || defined $params{NAME};
	croak "Expecting only one between ID and NAME parameters!\n" if defined $params{ID} && defined $params{NAME};
	my $status_id;
	if ( defined $params{ID} ) {
		croak "Invalid STATUS_ID '$params{ID}'!\n" unless $self->test_activity_status_id($params{ID});
		$status_id = $params{ID};
	} else {
		croak "Invalid STATUS_NAME '$params{NAME}'!\n" unless $self->test_activity_status_name($params{NAME});
		$status_id = $self->get_activity_status_id($params{NAME});
	}
	return ( grep(/^$status_id$/, @{ $self->{"FINAL_STATUS"} }) ? 1 : 0);
}

sub _get_action_type_flag{
	my $self = shift;
	my $action_name = shift;
	return $self->_dbh()->fetch_minimalized(
		sprintf(
			q(
				-- get_action_type
				select upper(a.flag_action_partenza) action_type_flag from action a
				where a.nome = %s
				and a.morto is null
				and rownum<2
			),
			$self->_dbh()->quote($action_name)
		)
	);
}


=head2 I<object>->B<test_action_type_open>(  I<ACTION_NAME>  )

Ritorna true se l'azione e' di apertura.

=cut
sub test_action_type_open {
	my $self = shift;
	my $action_name = shift;
	my $action_type = $self->_get_action_type_flag($action_name);
	return defined $action_type && $action_type eq 'A';
}

=head2 I<object>->B<test_action_type_close>(  I<ACTION_NAME>  )

Ritorna true se l'azione e' di chiusura.

=cut
sub test_action_type_close {
	my $self = shift;
	my $action_name = shift;
	my $action_type = $self->_get_action_type_flag($action_name);
	return defined $action_type && ( $action_type eq 'C' || $action_type eq 'Q' )
}


=head2 I<object>->B<test_action_type_virtual>(  I<ACTION_NAME>  )

Ritorna true se l'azione e' virtuale (anche virtuale di update).

=cut
sub test_action_type_virtual {
	my $self = shift;
	my $action_name = shift;
	my $action_type = $self->_get_action_type_flag($action_name);
	return defined $action_type && ( $action_type eq 'V' || $action_type eq 'U' );
}

=head2 I<object>->B<test_action_type_virtual_update>(  I<ACTION_NAME>  )

Ritorna true se l'azione e' virtuale di update.

=cut
sub test_action_type_virtual_update {
	my $self = shift;
	my $action_name = shift;
	my $action_type = $self->_get_action_type_flag($action_name);
	return defined $action_type && $action_type eq 'U';
}

=head2 I<object>->B<test_action_type_standard>(  I<ACTION_NAME>  )

Ritorna true se l'azione e' di tipo standard.

=cut
sub test_action_type_standard {
	my $self = shift;
	my $action_name = shift;
	my $action_type = $self->_get_action_type_flag($action_name);
	return defined $action_type && $action_type eq '';
}

sub enum_system_property { 
	my $self = shift;
	my %params = @_;
	return $self->_lookup()->{TDTS_NAME}
		unless keys %params;
	
	my @system_types_id = ();
	if (defined $params{SYSTEM_TYPE_ID}) {
		$self->last_error("Unknown SYSTEM_TYPE_ID ".$params{SYSTEM_TYPE_ID})
			&& return undef
				unless ($self->test_system_type_id($params{SYSTEM_TYPE_ID}));
		push @system_types_id, $params{SYSTEM_TYPE_ID};
	} elsif (defined $params{SYSTEM_TYPE_NAME}){
		$self->last_error("Unknown SYSTEM_TYPE_NAME ".$params{SYSTEM_TYPE_NAME})
			&& return undef
				unless ($self->test_system_type_name($params{SYSTEM_TYPE_NAME}));
		push @system_types_id, $self->get_system_type_id($params{SYSTEM_TYPE_NAME});
	} else {
		push @system_types_id, values %{$self->enum_system_type};
	}
	
	my $system_property = {};
	
	for my $system_type_id (@system_types_id){
		
		my $sql = "
			select distinct td.id_tipo_dato_tecnico ID_TIPO_DATO_TECNICO
				, td.nome NAME
				, td.DESCRIZIONE DESCRIPTION
				, case when TD.MULTIPLEX_VALUE = 'Y' then 1 else 0 end MULTIPLEX
				, case when TD.FLAGFILE = 'Y' then 1 else 0 end IS_FILE
				, case when td.tipo = 'CUREUR' then 'CURRENCY' else td.tipo end TYPE
				, td.um
				, case when TD.MISURA = 'Y' then 1 else 0 end IS_MEASURE
				, td.valore_min VALUE_MIN
				, td.valore_max VALUE_MAX
				, case when TD.CONTATORE = 'Y' then 1 else 0 end IS_COUNTER
				,case when td.tipo = 'CUREUR' then 'EUR' else null end CURRENCY
				,case when td.tipo = 'CUREUR' then 2 else td.precisione end FRACTIONS
			from TIPI_SISTEMA_TIPI_DATI_TECNICI T
			  join TIPI_DATI_TECNICI tD on (T.ID_TIPO_DATO_TECNICO = tD.ID_TIPO_DATO_TECNICO)
			  ". ($params{SHOW_ONLY_WITH_VISIBILITY} ? "join tipi_sistema_Gruppi tsg on tsg.id_tipo_Sistema = t.id_tipo_Sistema and tsg.id_Gruppo in (".join (',', @{$self->user()->su()->activity()->groups()}).")" : '')."
			where t.ID_TIPO_SISTEMA = ?
			  and TD.morto is null
			order by TD.NOME
		";
		
		$self->_create_prepare(__PACKAGE__.'enum_system_property', $sql);
		
		my $property_types = $self->_create_prepare(__PACKAGE__.'enum_system_property')->fetchall_hashref($system_type_id);
		
		my $visibility_property = $self->get_system_property_visibility(SYSTEM_TYPE_NAME => $self->get_system_type_name($system_type_id));

		for my $row (@$property_types) {
			# se attivo il meccanismo di visibilità dei dta deve avere la visibilità
			next if ($visibility_property->{ACTIVE} && !grep {$_->{NAME} eq $row->{NAME}} @{$visibility_property->{PROPERTIES}});
			if ($params{EXTENDED_OUTPUT}){
				my $tipo = $self->_lookup()->tipo_tdts_nome($row->{NAME});
				$system_property->{$row->{NAME}} = {
					 ID					=> $row->{ID_TIPO_DATO_TECNICO}
					,DESCRIPTION		=> $row->{DESCRIPTION}
					,MULTIPLEX			=> $row->{MULTIPLEX}
					,UM					=> $row->{UM}
					,IS_FILE			=> $row->{IS_FILE}
					,IS_MEASURE			=> $row->{IS_MEASURE}
					,VALUE_MIN		=> $row->{VALUE_MIN}
					,VALUE_MAX		=> $row->{VALUE_MAX}
					,IS_COUNTER			=> $row->{IS_COUNTER}
					,TYPE				=> $tipo
					,CURRENCY			=> $row->{CURRENCY}
					,FRACTIONS			=> $row->{FRACTIONS}
				}
			} else {
				$system_property->{$row->{NAME}} = $row->{ID_TIPO_DATO_TECNICO};
			}
		}
	}
	
	return 	$system_property;
	
}


sub enum_activity_property { 
	my $self = shift;
	my %params = @_;
	return $self->_lookup()->{TDTA_NAME}
		unless keys %params;
	
	$params{SHOW_ONLY_WITH_VISIBILITY} = 0 unless defined $params{SHOW_ONLY_WITH_VISIBILITY};
	
	my @activity_types_id = ();
	if (defined $params{ACTIVITY_TYPE_ID}) {
		$self->last_error("Unknown ACTIVITY_TYPE_ID ".$params{ACTIVITY_TYPE_ID})
			&& return undef
				unless ($self->test_activity_type_id($params{ACTIVITY_TYPE_ID}));
		push @activity_types_id, $params{ACTIVITY_TYPE_ID};
	} elsif (defined $params{ACTIVITY_TYPE_NAME}){
		$self->last_error("Unknown ACTIVITY_TYPE_NAME ".$params{ACTIVITY_TYPE_NAME})
			&& return undef
				unless ($self->test_activity_type_name($params{ACTIVITY_TYPE_NAME}));
		push @activity_types_id, $self->get_activity_type_id($params{ACTIVITY_TYPE_NAME});
	} else {
		push @activity_types_id, values %{$self->enum_activity_type(SHOW_ONLY_WITH_VISIBILITY => $params{SHOW_ONLY_WITH_VISIBILITY})};
	}
	
	if (defined $params{ACTION}){
		$self->last_error("Param ACTION must be a STRING!")
			&& return undef
				if ref ($params{ACTION});
		$self->last_error("Unknown ACTION ".$params{ACTION})
			&& return undef
				unless ($self->test_activity_action_name($params{ACTION}));
	}
	
	my $activity_actions_property =  {};
	
	# le new di queste due classi vengono effettuate solo qui per evitare che rimangano riferimenti che non permettono la distruzione
	# dell'oggetto: non spostarle nel metodo API::ART::new
	# NB: volutamente non viene fatto l'eval in quanto considerate safe
	my $collection = {
		ACTIVITY => API::ART::Collection::Activity->new(ART => $self)
		, SYSTEM => API::ART::Collection::System->new(ART => $self)
	};
	
	for my $activity_type (@activity_types_id){
		my $visibility_property = $self->get_activity_property_visibility(ACTIVITY_TYPE_NAME => $self->get_activity_type_name($activity_type));
		my $activity_actions = $self->_enum_activity_action_by_type(ACTIVITY_TYPE_ID => $activity_type, SKIP_PERMISSIONS => 1);
		
		for my $action (keys %{$activity_actions}){
			
			next if defined $params{ACTION} && $params{ACTION} ne $action;
			
			for my $prop (@{$self->get_action_properties( $activity_type , $action )}){
				next if exists $activity_actions_property->{$prop->{NOME_TDTA}};
				# se attivo il meccanismo di visibilità dei dta deve avere la visibilità
				next if ($visibility_property->{ACTIVE} && !grep {$_->{NAME} eq $prop->{NOME_TDTA}} @{$visibility_property->{PROPERTIES}});
				
				if ($params{EXTENDED_OUTPUT}){
			 		my $tipo_ui = $self->_lookup()->tipo_ui_tdta_nome($prop->{NOME_TDTA});
			 		$activity_actions_property->{$prop->{NOME_TDTA}} = {
		 				TYPE => $tipo_ui,
		 				LABEL => $self->_lookup()->etichetta_tdta_nome($prop->{NOME_TDTA}),
		 				HINT => $self->_lookup()->suggerimento_tdta_nome($prop->{NOME_TDTA}),
		 				PREDEFINED_VALUE => undef
		 			};
		 			
		 			my $preDefined = $self->_lookup()->predefinedValue_tdta_nome($prop->{NOME_TDTA});
		 			if (defined $preDefined){
		 				$activity_actions_property->{$prop->{NOME_TDTA}}->{PREDEFINED_VALUE} = from_json($preDefined);
		 			}
		 			
					if ($tipo_ui =~/^(LIST)$/){
						$activity_actions_property->{$prop->{NOME_TDTA}}->{'VALUES'} = [split (',', $self->_lookup()->valori_tdta_nome($prop->{NOME_TDTA}))];
					} elsif ($tipo_ui =~/^(LOOKUP|MOOKUP)$/){
			 			
			 			my $valori = from_json($self->_lookup()->valori_tdta_nome($prop->{NOME_TDTA}));
			 			
			 			$activity_actions_property->{$prop->{NOME_TDTA}}->{'LAYOUT'} = $valori->{LAYOUT};
			 			
			 			$activity_actions_property->{$prop->{NOME_TDTA}}->{'ORDER_BY'} = $valori->{ORDER_BY}||[];
			 			
			 			$activity_actions_property->{$prop->{NOME_TDTA}}->{'TARGET'} = $valori->{TARGET} if defined $valori->{TARGET};
			 			
			 			$activity_actions_property->{$prop->{NOME_TDTA}}->{'TEMPLATE'} = $valori->{TEMPLATE} if defined $valori->{TEMPLATE};
			 			
			 			$activity_actions_property->{$prop->{NOME_TDTA}}->{'VALUES'} = [] if defined $valori->{SOURCE};
			 			
			 			$activity_actions_property->{$prop->{NOME_TDTA}}->{'MATCH'} = $valori->{MATCH} if defined $valori->{MATCH};
			 			
			 			if ($valori->{TYPE} eq 'GROUP'){
							for my $g (@{$valori->{CLASS}}){
								my $group_id = $self->get_group_id($g);
								for my $userId (@{$self->get_group_users($group_id)}){
									my $user_info = {};
									for my $source (@{$valori->{SOURCE}}){
										if ($source eq 'GROUP'){
											$user_info->{$source} = $g;
										} elsif ($source =~/^\$/ ){
											$user_info->{$source} = $self->get_user_info($userId)->{substr($source,1)};
										}
									}
									push @{$activity_actions_property->{$prop->{NOME_TDTA}}->{'VALUES'}}, $user_info;
								}
							}
							$activity_actions_property->{$prop->{NOME_TDTA}}->{'GROUP_BY'} = $valori->{GROUP_BY} if defined $valori->{GROUP_BY};
							
							my @sort = ();
				 			for my $order (@{$activity_actions_property->{$prop->{NOME_TDTA}}->{'ORDER_BY'}}){
				 				
				 				$self->last_error("Bad Configuration for TDTA ".$prop->{NOME_TDTA}.": ORDER_BY must by an ARRAY of HASH")
									&& return undef
				 						if ref $order ne 'HASH';
				 				
				 				$self->last_error("Bad Configuration for TDTA ".$prop->{NOME_TDTA})
									&& return undef
				 						if scalar(keys %{$order})> 1;
				 				
				 				my @order_keys = keys %{$order};
				 				my @order_values = values %{$order};
				 				
				 				if ($order_values[0] == 1){
				 					push @sort, ' (defined $a->{\''.$order_keys[0].'\'} ? $a->{\''.$order_keys[0].'\'} : \'\') cmp ( defined $b->{\''.$order_keys[0].'\'} ? $b->{\''.$order_keys[0].'\'} : \'\' )';
				 				} elsif ($order_values[0] == -1){
				 					push @sort, ' (defined $b->{\''.$order_keys[0].'\'} ? $b->{\''.$order_keys[0].'\'} : \'\') cmp ( defined $a->{\''.$order_keys[0].'\'} ? $a->{\''.$order_keys[0].'\'} : \'\' )';
				 				}
				 			}
				 			
				 			if (scalar @sort){
				 				my $sort_index = join ' or ', @sort;
								@{$activity_actions_property->{$prop->{NOME_TDTA}}->{'VALUES'}} = sort { eval $sort_index } @{$activity_actions_property->{$prop->{NOME_TDTA}}->{'VALUES'}};
				 			}
							
			 			} elsif ($valori->{TYPE} =~/^(ACTIVITY_PROPERTY|SYSTEM_PROPERTY)$/ ){
			 				
			 				$activity_actions_property->{$prop->{NOME_TDTA}}->{'SCOPE'} = $valori->{TYPE};
			 				
			 			} elsif ($valori->{TYPE} eq 'CODE' ) {
			 				
			 				# FIXME: verificare se il singleton puo' dare problemi con i WS Rest
			 				
			 				my $package = $valori->{PACKAGE};
			 				my $function = $valori->{FUNCTION};
			 				eval "require $package;";
			 				$self->last_error("Unable to require $package: $@")
			 					&& return undef
			 						if $@;

			 				my $reports = eval{ $package->instance(API => $self); };
			 				$self->last_error("Unable to get instance of $package: $@")
			 					&& return undef
			 						if $@;

							my $json_output = $reports->$function();
							$self->last_error($reports->last_error())
								&& return undef
									unless defined $json_output;
							
							my $output = from_json($json_output);
							
							if($valori->{LAYOUT} eq 'TABLE') {

								$valori->{HIDDEN_COLUMNS} = [] unless defined $valori->{HIDDEN_COLUMNS};
								for(my $i=0; $i<@{$output->{header}}; $i++) {
									if(grep { $output->{header}->[$i]->{name} eq $_ } @{$valori->{HIDDEN_COLUMNS}}) {
										$output->{header}->[$i]->{hidden} = $JSON::true; 
									} else {
										$output->{header}->[$i]->{hidden} = $JSON::false;
									}
								}
								$activity_actions_property->{$prop->{NOME_TDTA}}->{'VALUES'} = $output;

							} else {

								tie my %cols, 'Tie::IxHash', ();
								for my $s (@{$valori->{SOURCE}}) {
									for (my $i=0; $i<@{$output->{header}}; $i++ ) {
										if($s eq $output->{header}->[$i]->{name}) {
											$cols{$s} = $i;
											last;
										}
									}
								}
								
								for my $d (@{$output->{data}}) {
									my %data = ();
									for my $k (keys %cols) {
										$data{$k} = $d->[$cols{$k}] 
									}
									push @{$activity_actions_property->{$prop->{NOME_TDTA}}->{'VALUES'}}, \%data;
								}
							}
			 				
			 			} elsif ($valori->{TYPE} eq 'URI') {
			 				delete $activity_actions_property->{$prop->{NOME_TDTA}}->{'ORDER_BY'};
			 				for ('URI',  'LAYOUT',  'CONTEXT', 'ADD_FROM_AUTOCOMPLETE_ONLY') {
				 				$self->last_error("Param ".$_." must be defined in the configuration!")
									&& return undef
										unless defined $valori->{$_};
								if ($_ eq 'CONTEXT'){
									$self->last_error("Bad value for param ".$_." in the configuration!")
										&& return undef
											 if $valori->{$_} !~/^(App|Api|Config)$/;
									$activity_actions_property->{$prop->{NOME_TDTA}}->{$_."_KEY"} = $valori->{$_."_KEY"} if defined $valori->{$_."_KEY"};
									$activity_actions_property->{$prop->{NOME_TDTA}}->{"DISPLAY_PROPERTY"} = $valori->{"DISPLAY_PROPERTY"} if defined $valori->{"DISPLAY_PROPERTY"};
									$activity_actions_property->{$prop->{NOME_TDTA}}->{"KEY_PROPERTY"} = $valori->{"KEY_PROPERTY"} if defined $valori->{"KEY_PROPERTY"};
									$activity_actions_property->{$prop->{NOME_TDTA}}->{"QUERY_KEY"} = $valori->{"QUERY_KEY"} if defined $valori->{"QUERY_KEY"};
									$activity_actions_property->{$prop->{NOME_TDTA}}->{"RESPONSE_DATA_KEY"} = $valori->{"RESPONSE_DATA_KEY"} if defined $valori->{"RESPONSE_DATA_KEY"};
									$activity_actions_property->{$prop->{NOME_TDTA}}->{"MIN_LENGTH"} = $valori->{"MIN_LENGTH"} if defined $valori->{"MIN_LENGTH"};
								}
								$activity_actions_property->{$prop->{NOME_TDTA}}->{$_} = $valori->{$_};
			 				}
			 			} else {
			 				
			 				my $p_find = {
			 					$valori->{TYPE}."_TYPE_NAME" => [$valori->{CLASS}]
			 					,'SORT' => []
			 				};
			 				
			 				for my $order (@{$activity_actions_property->{$prop->{NOME_TDTA}}->{'ORDER_BY'}}){

				 				$self->last_error("Bad Configuration for TDTA ".$prop->{NOME_TDTA}.": ORDER_BY must by an ARRAY of HASH")
									&& return undef
				 						if ref $order ne 'HASH';
				 				
				 				$self->last_error("Bad Configuration for TDTA ".$prop->{NOME_TDTA})
									&& return undef
				 						if scalar(keys %{$order})> 1;
				 				
				 				my @order_keys = keys %{$order};
				 				my @order_values = values %{$order};
				 				
				 				if ($order_keys[0] =~/^\$/ ){
					 				$order_keys[0] =~s/^\$//;
				 					push @{$p_find->{SORT}}, { $order_keys[0] => $order_values[0]}
				 				} else {
					 				push @{$p_find->{SORT}}, {
					 					PROPERTY => {
					 						$order_keys[0] => $order_values[0]
					 					}
					 				}
				 				}
				 			}
			 				
			 				my $objs = $collection->{$valori->{TYPE}}->find_object(%{$p_find});
			 				
			 				return undef
					 			unless defined $objs;
			 				
			 				for my $obj (@{$objs}){
			 					next unless defined $obj;
			 					my $val_source = {};
			 					my @list_properties;
			 					for my $source (@{$valori->{SOURCE}}){
			 						if ($source =~/^\$/ ){
			 							$val_source->{$source} = $obj->info(substr($source,1));
			 						} else {
			 							#$val_source->{$source} = $obj->property($source);
			 							push @list_properties, $source;
			 						}
			 					}
			 					
			 					if (scalar @list_properties){
			 						my $prop_find = $obj->property(@list_properties);
			 						if (ref $prop_find eq 'HASH'){
			 							for my $list_prop_key (keys %{$prop_find}){
			 								$val_source->{$list_prop_key} = $prop_find->{$list_prop_key};
				 						}
			 						} else {
			 							$val_source->{$list_properties[0]} = $prop_find;
			 						}
			 					}
			 					
			 					push @{$activity_actions_property->{$prop->{NOME_TDTA}}->{'VALUES'}}, $val_source;
			 				}
			 				
			 			}

			 		} elsif ($tipo_ui =~/^(TAGS)$/){

			 			$activity_actions_property->{$prop->{NOME_TDTA}}->{'VALUES'} = [];
						
			 			my $res_tag_cloud = $self->_dbh()->fetchall_hashref ("
							select tag, sum(num) num from (
								select dtat.tag TAG
					 				, count (1) NUM
					 			From dati_Tecnici_attivita_tags dtat
									join attivita att on att.id_attivita = dtat.id_attivita
									join tipi_attivita ta on att.id_tipo_Attivita = ta.ID_TIPO_ATTIVITA
								where dtat.disabled is null
									and dtat.id_tipo_Dato_Tecnico_attivita = ".$prop->{ID_TDTA}."
									and ta.id_tipo_Attivita = ".$activity_type."
								group by dtat.tag
								union all
								select tag
									, 0 NUM
								from tdta_tags
								where id_tipo_Dato_Tecnico_attivita = ".$prop->{ID_TDTA}."
									and id_tipo_Attivita = ".$activity_type."
								)
							group by tag
			 			");
			 			
			 			for my $tag (@{$res_tag_cloud}){
			 				push @{$activity_actions_property->{$prop->{NOME_TDTA}}->{'VALUES'}}, {
			 					TAG => $tag->{TAG}
			 					, COUNT => $tag->{NUM}
			 				};
			 			}
			 		} elsif ($tipo_ui =~/^(URL)$/){

						my @attributes = $self->_dbh()->fetch_minimalized ("		
				 			select baseurl
				 				, params
				 			From tdta_ui_url tuu
							where tuu.id_tipo_Dato_Tecnico_attivita = ".$prop->{ID_TDTA}."
			 			");

						$activity_actions_property->{$prop->{NOME_TDTA}}->{'ATTRIBUTES'} = {
							BASEURL		=> $attributes[0]
							, PARAMS	=> $attributes[1]
							
						};
						
			 		} elsif ($tipo_ui =~/^(RADIO)$/){
						
						my @valori = split (',', $self->_lookup()->valori_tdta_nome($prop->{NOME_TDTA} ));
						
						$activity_actions_property->{$prop->{NOME_TDTA}}->{'VALUES'} = \@valori;
						
			 		} elsif ($tipo_ui =~/^(POPUP)$/){
						
						my @valori = split (',', $self->_lookup()->valori_tdta_nome($prop->{NOME_TDTA} ));
						
						$activity_actions_property->{$prop->{NOME_TDTA}}->{'VALUES'} = \@valori;
						
			 		} elsif ($tipo_ui =~/^(ISODAT)$/){
						
						if (defined $self->_lookup()->valori_tdta_nome($prop->{NOME_TDTA})) {
							my $valori = from_json($self->_lookup()->valori_tdta_nome($prop->{NOME_TDTA}));
				 			
				 			$activity_actions_property->{$prop->{NOME_TDTA}}->{'INPUT_FORMAT'} = $valori->{INPUT_FORMAT};
						} else {
							$activity_actions_property->{$prop->{NOME_TDTA}}->{'INPUT_FORMAT'} = 'date';
						}
						
			 		} elsif ($tipo_ui =~/^(CURRENCY)$/){
						$activity_actions_property->{$prop->{NOME_TDTA}}->{CURRENCY} = $self->_lookup()->valuta_tdta_nome($prop->{NOME_TDTA}),
						$activity_actions_property->{$prop->{NOME_TDTA}}->{FRACTIONS} = $self->_lookup()->decimali_tdta_nome($prop->{NOME_TDTA}),
					}
			 	} else {
				 	$activity_actions_property->{$prop->{NOME_TDTA}} = $prop->{ID_TDTA};
			 	}
			 }
		}
	}
	return 	$activity_actions_property;
	 
}
sub get_activity_property_id { $_[0]->_lookup('id_tdta', @_[ 1 .. $#_ ]) }
sub get_activity_property_name { $_[0]->_lookup('nome_tdta', @_[ 1 .. $#_ ]) }
sub test_activity_property_id { $_[0]->_lookup('is_id_tdta', @_[ 1 .. $#_ ]) }
sub test_activity_property_name { $_[0]->_lookup('is_nome_tdta', @_[ 1 .. $#_ ]) }

sub enum_group { 
	my $self = shift;
	my %params = @_;
	return $self->_lookup()->{GRUPPI_NAME}
		unless keys %params;
	
	my $errmsg;
		
	croak $errmsg
		unless $self->check_named_params(
			 ERRMSG		=> \$errmsg
			,PARAMS		=> \%params
			,MANDATORY	=> {}
			,OPTIONAL	=> {
				 FILTER				=> { isa => 'HASH' }
				,EXTENDED_OUTPUT	=> { isa => 'SCALAR', list => [0,1] }
			}
			,IGNORE_EXTRA_PARAMS	=> 0
		);
	
	if ($params{FILTER}){
		croak $errmsg
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> $params{FILTER}
				,MANDATORY	=> {}
				,OPTIONAL	=> {
					 NAMES			=> { isa => 'ARRAY' }
					,DESCRIPTIONS	=> { isa => 'ARRAY' }
				}
				,IGNORE_EXTRA_PARAMS	=> 0
			);
		
	}
	
	my $where = '';
	
	for my $param (keys %{$params{FILTER}}){
		
		if ($param =~ /^(NAMES)$/){
			$where.= " and g.nome in (".join (',', (map {$self->_dbh()->quote($_)} @{$params{FILTER}->{$param}})).")";
		} elsif ($param =~ /^(DESCRIPTIONS)$/){
			$where.= " and g.descrizione in (".join (',', (map {$self->_dbh()->quote($_->[0])} @{$params{FILTER}->{$param}})).")";
		}
	}
	
	my $sql = "
		select g.id_gruppo ID
		  , upper(g.nome) NAME
		  , g.descrizione DESCRIPTION
		From gruppi g
		where 1=1
			".$where
	;

	my $res = $self->_dbh()->fetchall_hashref ("$sql");
	
	my $output = $params{EXTENDED_OUTPUT} ? [] : {};
	
	for my $res (@{$res}){
		if ($params{EXTENDED_OUTPUT}) {
			push @{$output},  $res;
		} else {
			$output->{$res->{NAME}} = $res->{ID};
		}
	}
	
	return $output;
	
}
sub get_group_id { $_[0]->_lookup('id_gruppo', @_[ 1 .. $#_ ]) }
sub get_group_name { $_[0]->_lookup('nome_gruppo', @_[ 1 .. $#_ ]) }
sub test_group_id { $_[0]->_lookup('is_id_gruppo', @_[ 1 .. $#_ ]) }
sub test_group_name { $_[0]->_lookup('is_nome_gruppo', @_[ 1 .. $#_ ]) }

=head2 I<object>->B<is_group_private>( I<GROUP_NAME> )

Ritorna 1 se il gruppo è privato (se la funzionalità è supportata)

=cut

sub is_group_private {
	my $self = shift;
	my $group = shift;
	
	$self->last_error("Private group not supported!!")
		&& return undef
			unless $self->group_extended_support();
	
	if ($self->test_group_name){
		my $sql = "
			select nvl2(
				(select privato
				from gruppi
				where nome = ".$self->_dbh()->quote($group).")
			,1,0) from dual
		";
		return $self->_dbh()->fetch_minimalized ($sql);
	} else {
		$self->last_error("Group not found!");
		return undef;
	}
}

=head2 I<object>->B<is_group_autogenerated>( I<GROUP_NAME> )

Ritorna 1 se il gruppo è autogenerato (se la funzionalità è supportata)

=cut

sub is_group_autogenerated {
	my $self = shift;
	my $group = shift;
	
	$self->last_error("Autogenerated group not supported!!")
		&& return undef
			unless $self->group_extended_support();
	
	if ($self->test_group_name){
		my $sql = "
			select nvl2(
				(select autogenerato
				from gruppi
				where nome = ".$self->_dbh()->quote($group).")
			,1,0) from dual
		";
		return $self->_dbh()->fetch_minimalized ($sql);
	} else {
		$self->last_error("Group not found!");
		return undef;
	}
}

sub enum_role { 
	my $self = shift;
	my %params = @_;
	return $self->_lookup()->{RUOLI_NAME}
		unless keys %params;
	
	my $errmsg;
		
	croak $errmsg
		unless $self->check_named_params(
			 ERRMSG		=> \$errmsg
			,PARAMS		=> \%params
			,MANDATORY	=> {}
			,OPTIONAL	=> {
				 FILTER				=> { isa => 'HASH' }
				,EXTENDED_OUTPUT	=> { isa => 'SCALAR', list => [0,1] }
			}
			,IGNORE_EXTRA_PARAMS	=> 0
		);
	
	if ($params{FILTER}){
		croak $errmsg
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> $params{FILTER}
				,MANDATORY	=> {}
				,OPTIONAL	=> {
					 NAMES			=> { isa => 'ARRAY' }
					,DESCRIPTIONS	=> { isa => 'ARRAY' }
					,GROUPS			=> { isa => 'ARRAY' }
					,ROLE_ALIASES		=> { isa => 'ARRAY' }
				}
				,IGNORE_EXTRA_PARAMS	=> 0
			);
		
	}
	
	my $where = '';
	
	for my $param (keys %{$params{FILTER}}){
		
		if ($param =~ /^(NAMES)$/){
			$where.= " and r.nome in (".join (',', (map {$self->_dbh()->quote($_)} @{$params{FILTER}->{$param}})).")";
		} elsif ($param =~ /^(DESCRIPTIONS)$/){
			$where.= " and r.descrizione in (".join (',', (map {$self->_dbh()->quote($_->[0])} @{$params{FILTER}->{$param}})).")";
		} elsif ($param =~ /^(GROUPS)$/){
			$where.="and rg.id_gruppo in (".join (',', (map {$self->get_group_id($_)} @{$params{FILTER}->{$param}})).")";
		} elsif ($param =~ /^(ROLE_ALIASES)$/){
			$where.= " and ra.alias in (".join (',', (map {$self->_dbh()->quote($_->[0])} @{$params{FILTER}->{$param}})).")";
		}
	}
	
	my $sql = "
		select r.id_ruolo ID
		  , r.nome NAME
		  , r.descrizione DESCRIPTION
		  , to_char(r.DATA_CREAZIONE,".$self->_dbh()->quote($self->get_default_date_format()).") CREATION_DATE
		  , rg.id_gruppo GROUPS
		  , ra.alias ROLE_ALIASES
		From ruoli r
		  left join ruoli_gruppi rg on rg.id_ruolo = r.id_ruolo and rg.data_disabilitazione is null
		  left join ruoli_alias ra on ra.id_ruolo = r.id_ruolo and ra.data_disabilitazione is null
		where 1=1
			and r.data_disabilitazione is null
			".$where
	;

	my $res = $self->_dbh()->fetchall_hashref ("$sql");
	
	my $output = $params{EXTENDED_OUTPUT} ? [] : {};
	
	for my $res (@{$res}){
		if ($params{EXTENDED_OUTPUT}) {
			my @obj = grep {$_->{ID} eq $res->{ID}} @{$output};
			
			if (scalar @obj){
				for my $obj_ruolo ('GROUPS', 'ROLE_ALIASES'){
					if ($res->{$obj_ruolo}){
						push @{$obj[0]->{$obj_ruolo}}, $res->{$obj_ruolo} unless grep {$_ eq $res->{$obj_ruolo}} @{$obj[0]->{$obj_ruolo}};
					}
				}
			} else {
				for my $obj_ruolo ('GROUPS', 'ROLE_ALIASES'){
					if ($res->{$obj_ruolo}){
						$res->{$obj_ruolo} = [$res->{$obj_ruolo}] ;
					} else {
						$res->{$obj_ruolo} = [] ;
					}
				}
				push @{$output},  $res;
			}
		} else {
			$output->{$res->{NAME}} = $res->{ID};
		}
	}
	
	return $output;
	
}
sub get_role_id { $_[0]->_lookup('id_ruolo', @_[ 1 .. $#_ ]) }
sub get_role_name { $_[0]->_lookup('nome_ruolo', @_[ 1 .. $#_ ]) }
sub test_role_id { $_[0]->_lookup('is_id_ruolo', @_[ 1 .. $#_ ]) }
sub test_role_name { $_[0]->_lookup('is_nome_ruolo', @_[ 1 .. $#_ ]) }

sub enum_user { 
	my $self = shift;
	my %params = @_;
	return $self->_lookup()->{OPERATORI_NAME}
		unless keys %params;	
	
	my $translate = {
		NAME				=> 'LOGIN_OPERATORE'
		,FIRST_NAME			=> 'NOME_OPERATORE'
		,LAST_NAME			=> 'COGNOME_OPERATORE'
		,EMAIL				=> 'EMAIL'
		,MOBILE_PHONE		=> 'MOBILE_PHONE'
		,SERVICE_USER		=> 'SERVICE_USER'
		,DISABLED			=> 'MORTO'
		,MD5_PASSWORD		=> 'MD5_PASSWORD'
		,GROUPS				=> ''
	};
	
	my $where = '';
	my $join = '';
	
	for my $param (keys %{$params{FILTER}}){
		next unless exists $translate->{$param};
		if ($param =~ /^(SERVICE_USER|DISABLED)$/){
			$where.= " and op.". $translate->{$param} . " is ".($params{FILTER}->{$param} ? 'not': '' )." null";
		}elsif ($param =~ /^(GROUPS)$/){
			$self->last_error("Param GROUPS must be an ARRAY")
				&& return undef
					if ref($params{FILTER}->{$param}) ne 'ARRAY';
			# verifico consistenza gruppi passati
			my $group_ids = [];
			for my $g (@{$params{FILTER}->{$param}}){
				my $group_id = $self->get_group_id($g);
				$self->last_error("GROUPS $g not found!")
					&& return undef
						unless defined $group_id;
				push @{$group_ids}, $group_id;
			}
			for (my $i=0; $i< scalar @{$group_ids}; $i++){
				$join .= " join operatori_gruppi og".$i." on og".$i.".id_operatore = op.id_operatore ";
				$where.=" and og".$i.".id_gruppo = ".$group_ids->[$i]." "; #verifico solo la operatori gruppi in quanto il gruppo principale nella tabella operatori è presente anche sulla tabella operatori_gruppi
			}
		} else {
			$where.= " and lower(op.". $translate->{$param} . ") = lower(".$self->_dbh()->quote($params{FILTER}->{$param}).")";
		}
	}
	
	my $sql = "
		select distinct op.id_operatore ID
			, op.nome_operatore FIRST_NAME
			, op.login_operatore NAME
			, op.email EMAIL
			, decode(op.service_user,'S',1) SERVICE_USER
			, op.cognome_operatore LAST_NAME
			, op.mobile_phone MOBILE_PHONE
			, op.morto DISABLED
			, op.password_operatore MD5_PASSWORD
		from operatori op
			$join
		where 1=1
		".$where
	;

	my $res = $self->_dbh()->fetchall_hashref ("$sql");
	
	my $output = $params{EXTENDED_OUTPUT} ? [] : {};
	
	for my $res (@{$res}){
		if ($params{EXTENDED_OUTPUT}) {
			delete $res->{MD5_PASSWORD}
				unless $params{INCLUDE_MD5_PASSWORD};
			#aggiungo la chiave GROUPS e la popolo solo se non è disabilitato
			$res->{GROUPS} = $res->{DISABLED} ? [] : [map {$self->get_group_name($_)} @{$self->get_user_groups($self->get_user_id($res->{NAME}))}];
			$res->{ROLES} = $res->{DISABLED} ? [] : [map {$self->get_role_name($_)} @{$self->get_user_roles($self->get_user_id($res->{NAME}))}];
			push @{$output},  $res;
		} else {
			$output->{$res->{NAME}} = $res->{ID};
		}
	}
	
	return $output;
	
}
sub get_user_id { $_[0]->_lookup('id_operatore', @_[ 1 .. $#_ ]) }
sub get_user_name { $_[0]->_lookup('nome_operatore', @_[ 1 .. $#_ ]) }
sub test_user_id { $_[0]->_lookup('is_id_operatore', @_[ 1 .. $#_ ]) }
sub test_user_name { $_[0]->_lookup('is_nome_operatore', @_[ 1 .. $#_ ]) }

=head2 I<object>->B<test_permission>( I<ACTIVITY_TYPE_ID> => <scalar>, I<STATUS_ID> => <scalar>, I<GROUPS_ID> => <arrayref>, I<ACTION_ID> => <scalar> )

Restituisce l'id dello stato di destinazione oppure <undef> se i gruppi definiti non possono eseguire l'azione.

=cut

sub test_permission {
	my $self = shift;
	my %params = @_;
	my $errmsg;
	
	croak $errmsg
		unless $self->check_named_params(
			 ERRMSG		=> \$errmsg
			,PARAMS		=> \%params
			,MANDATORY	=> {
				 ACTIVITY_TYPE_ID	=> { isa => 'SCALAR' }
				,STATUS_ID			=> { isa => 'SCALAR' }
				,GROUPS_ID			=> { isa => 'ARRAY' }
				,ACTION_ID			=> { isa => 'SCALAR' }
			}
			,IGNORE_EXTRA_PARAMS	=> 0
		);
	
	my @result = $self->_test_permission(%params);
	return undef if scalar(@result) == 0;
	return $result[1];
}

=head2 I<object>->B<is_system_property_id_multiplex>( SYSTEM_PROPERTY_ID ), I<object>->B<is_system_property_name_multiplex>( SYSTEM_PROPERTY_NAME )

Ritorna 1 se la SYSTEM_PROPERTY accetta valori multipli, 0 altrimenti 

=cut
sub is_system_property_id_multiplex { $_[0]->_lookup('multiplex_tdts_id', @_[ 1 .. $#_ ]) }
sub is_system_property_name_multiplex { $_[0]->_lookup('multiplex_tdts_nome', @_[ 1 .. $#_ ]) }

=head2 I<object>->B<is_action_id_assignable>( ACTION_ID ), I<object>->B<is_action_name_assignable>( ACTION_NAME )

Ritorna 1 se la ACTION deve assegnare l'attivita' ad un'utente, 0 altrimenti 

=cut
sub is_action_id_assignable { $_[0]->_lookup('assegnabile_azione_id', @_[ 1 .. $#_ ]) }
sub is_action_name_assignable { $_[0]->_lookup('assegnabile_azione_nome', @_[ 1 .. $#_ ]) }

=head2 I<object>->B<get_user_groups>( I<user_id> )

Ritorna un I<arrayref> contenente gli ID dei gruppi di cui fa parte lo user specificato

=cut

sub get_user_groups {
	my $self = shift;
	my $user_id = shift;
	return undef unless defined($user_id);
	return [
		$self->_dbh()->fetch_minimalized(qq(
			SELECT id_gruppo
			FROM   (SELECT  /* 0 tipo
			                ,*/o.id_operatore
			                ,o.login_operatore
			                ,g.id_gruppo
			                ,g.nome nome_gruppo
			        FROM     operatori o
			                ,gruppi g
			        WHERE    g.id_gruppo = o.id_gruppo
			        UNION
			        SELECT  /* 1
			                ,*/o.id_operatore
			                ,o.login_operatore
			                ,g.id_gruppo
			                ,g.nome
			        FROM     operatori o
			                ,operatori_gruppi og
			                ,gruppi g
			        WHERE    o.id_operatore = og.id_operatore
			                 AND g.id_gruppo = og.id_gruppo
			        /*ORDER BY 1*/)
			WHERE  id_operatore = $user_id
		))
	];
}

=head2 I<object>->B<get_user_roles>( I<user_id> )

Ritorna un I<arrayref> contenente gli ID dei ruoli di cui fa parte lo user specificato

=cut

sub get_user_roles {
	my $self = shift;
	my $user_id = shift;
	return undef unless defined($user_id);

# nel caso in cui l'istanza non supporti i ruoli ritorno un arrayref vuoto
	# FIXME trovare un flag più idoneo
    return []
		unless $self->{API_SUPPORT} && $self->{INFO}->{GROUP_EXTENDED_SUPPORT};

	my $roles = $self->_dbh()->fetchall_arrayref(qq(
			select id_ruolo
			from v_operatori_ruoli
			where id_operatore =  $user_id
		));

	my $r = [ map { $_->[0] } @{$roles}] ;

	return $r;
}

=head2 I<object>->B<get_group_users>( I<group_id> )

Ritorna un I<arrayref> contenente gli ID degli users facenti parte del gruppo specificato

=cut

sub get_group_users {
	my $self = shift;
	my $group_id = shift;
	return undef unless defined($group_id);
	my @res = 
		$self->_dbh()->fetch_minimalized(qq(
			SELECT  o.id_operatore
			FROM     operatori o
			WHERE    o.id_gruppo = $group_id
			UNION
			SELECT  og.id_operatore
			FROM    operatori_gruppi og
			WHERE    og.id_gruppo = $group_id
		))
	;
	
	return (defined $res[0]) ? \@res : [];
	
}

=head2 I<object>->B<get_role_users>( I<role_id> )

Ritorna un I<arrayref> contenente gli ID degli users facenti parte del ruolo specificato

=cut

sub get_role_users {
	my $self = shift;
	my $role_id = shift;
	return undef unless defined($role_id);
	my @res = 
		$self->_dbh()->fetch_minimalized(qq(
			select id_operatore
			from v_operatori_ruoli
			where id_ruolo =  $role_id
		))
	;
	
	return (defined $res[0]) ? \@res : [];
	
}

=head2 I<object>->B<get_user_info>( I<user_id> )

Ritorna un I<hashref> contenente le info dello user specificato:

=over 4

=item NAME

=item FIRST_NAME

=item LAST_NAME

=item EMAIL

=item MOBILE_PHONE

=item SERVICE_USER

=item DISABLED

=item GROUPS

=item ADMIN

=item USER_ADMIN

=item ROOT

=cut

sub get_user_info {
	my $self = shift;
	my $user_id = shift;
	$self->last_error("Missing mandatory param user_id")
		&& return undef
			unless defined($user_id);
	
	my $info = {};
	
	(
		  $info->{NAME}
		, $info->{FIRST_NAME}
		, $info->{LAST_NAME}
		, $info->{EMAIL}
		, $info->{MOBILE_PHONE}
		, $info->{SERVICE_USER}
		, $info->{DISABLED}
	) =	$self->_dbh()->fetch_minimalized(qq(
			SELECT  
				  o.login_operatore
				, o.nome_operatore
				, o.cognome_operatore
				, o.email
				, o.mobile_phone
				, decode(upper(o.service_user),'Y',1,'S',1) service_user
				, decode(upper(o.morto),'Y',1,'S',1) morto
			FROM  operatori o
			WHERE o.id_operatore = $user_id
	));
	
	$info->{GROUPS} = $self->get_user_groups($user_id);
	$info->{ROLES} = $self->get_user_roles($user_id);
	
	my  %user_groups =
		map {$self->get_group_name($_) => 1} @{$info->{GROUPS}};
	
	$info->{ROOT} = $user_groups{'ROOT'}||0;

	$info->{ADMIN} = 0;
	IS_ADMIN: for (@USER_ADMIN_GROUPS){
		if (exists $user_groups{$_}){
			$info->{ADMIN} = 1;
			last IS_ADMIN;
		}
	}

	$info->{USER_ADMIN} = 0;
	IS_USER_ADMIN: for (@USER_USER_ADMIN_GROUPS){
		if (exists $user_groups{$_}){
			$info->{USER_ADMIN} = 1;
			last IS_USER_ADMIN;
		}
	}
	
	return $info;
}

=head2 I<object>->B<get_user_structure>( I<user_id> )

Restituisce un hash che rappresenta l'utente come una entry dell'array nell'omologo formato B<JSON> restituito dall'B<API::ART::REST>
C<GET /api/art/instance/users> (vedi L<http://dvmas003.ict.sirti.net:10199/doc/ArtRestApi/index.html#api-Instance-instanceGetUsers>).

=cut
sub get_user_structure {
	my $self = shift;
	my $user_id = shift;
	
	my $user = $self->get_user_info($user_id);
	return undef unless defined $user;
	
	return {
	 	username => $user->{'NAME'}
		,root => $user->{'ROOT'} ? $JSON::true : $JSON::false
		,admin => $user->{'ADMIN'} ? $JSON::true : $JSON::false
		,userAdmin => $user->{'USER_ADMIN'} ? $JSON::true : $JSON::false
		,firstName => $user->{'FIRST_NAME'}
		,lastName => $user->{'LAST_NAME'}
		,fullName => $user->{'LAST_NAME'}." ".$user->{'FIRST_NAME'}
		,email => $user->{'EMAIL'}
		,mobilePhone => $user->{'MOBILE_PHONE'}
		,groups => [map { $self->get_group_name($_) } @{$user->{'GROUPS'}}]
		,roles => [map { $self->get_role_name($_) } @{$user->{'ROLES'}}]
		,serviceUser => $user->{'SERVICE_USER'} ? $JSON::true : $JSON::false
		,disabled => $user->{'DISABLED'} ? $JSON::true : $JSON::false
	};
	
}

=head2 I<object>->B<get_group_info>( I<group_id> )

Ritorna un I<hashref> contenente le info del gruppo specificato:

=over 4

=item NAME

=item DESCRIPTION

=item ADMIN

=item ROOT

=item DISABLED

=item PRIVATE

=item AUTOGENERATED

=cut

sub get_group_info {
	my $self = shift;
	my $group_id = shift;
	return undef unless defined($group_id);
	
	my $info = {};
	
	(
		  $info->{DESCRIPTION}
		, $info->{ADMIN}
		, $info->{ROOT}
		, $info->{DISABLED}
		, $info->{NAME}
		, $info->{PRIVATE}
		, $info->{AUTOGENERATED}
	) =	$self->_dbh()->fetch_minimalized(qq(
			select g.descrizione
			  , decode(upper(g.amministratore),'Y',1,'S','1') ADMIN
			  , decode(upper(g.root),'Y',1,'S','1') ROOT
			  , decode(upper(g.morto),'Y',1,'S','1') MORTO
			  , g.nome
			  , g.privato
			  , g.autogenerato
			from gruppi g
			where g.id_gruppo = $group_id
	));
	
	return $info;
}

=head2 I<object>->B<get_groups_from_roles>( I<roles> )

Dato in input un I<arrayref> di nomi ruolo, ritorna un I<arrayref> contenente i nomi gruppo che identificano.

=cut

sub get_groups_from_roles {
	my $self = shift;
	my $roles = shift;
	
	my $search_params = {
		EXTENDED_OUTPUT => 1
	};
	if ($roles){
		$self->last_error('No roles specified') and return undef unless @{$roles};
		$search_params->{FILTER}->{NAMES} = $roles;
	}
	
	my $ruoli = $self->enum_role(%{$search_params});
	
	return undef unless $ruoli;
	
	my @groups = ();
	
	for my $ruolo (@{$ruoli}){
		for my $g (@{$ruolo->{GROUPS}}){
			my $group_name = $self->get_group_name($g);
			push @groups, $group_name unless grep {$_ eq $group_name} @groups;
		}
	}
	
	return \@groups;
}

=head2 I<object>->B<get_roles_from_role_aliases>( I<role_aliases> )

Dato in input un I<arrayref> di alias di ruoli, ritorna un I<arrayref> contenente i nomi ruolo che identificano.

=cut

sub get_roles_from_role_aliases {
	my $self = shift;
	my $role_aliases = shift;
	
	my $result_role_from_aliases = $self->_dbh()->fetchall_arrayref("
				select distinct r.nome from ruoli_alias ra
				  join ruoli r on r.id_ruolo = ra.id_ruolo
				where ra.alias in (
					".join (',', (map {$self->_dbh()->quote($_)} @{$role_aliases}))."
				)
				and ra.data_disabilitazione is null
			");
	
	return [map {$_->[0]} @{$result_role_from_aliases}];
}


=head2 I<object>->B<get_action_properties>( I<ACTIVITY_TYPE_NAME> , I<ACTION_NAME> )

Ritorna un I<arrayref> di I<hashref> con tutti i tipi dati tecnici associati agli stati appartenenti al flusso B<ACTIVITY_TYPE_NAME> ed interessati dall'azione specificata da B<ACTION_NAME>. I<Entrambi i parametri possono essere passati come nome oppure id>.

=over 4

=item La struttura risultante e' la seguente:

	<arrayref> => [
			<hashref> => {
					'NOME_TDTA' => <scalar>,
					'ID_TDTA' => <scalar>,
					'POSIZIONE' => <scalar>,
					'OBBLIGATORIO' => <Y|N>,
					'SOLO_LETTURA' => <Y|N>,
					'NASCOSTO' => <Y|N>,
					'PREPOPOLA' => <Y|N>
			}, ...
	]

=back

=cut

sub get_action_properties {
	my $self = shift;
	my ($activity_type, $action) = @_;
	$activity_type = $self->get_activity_type_id($activity_type) unless $activity_type =~ /^\d+$/;
	$action = $self->get_activity_action_id($action) unless $action =~ /^\d+$/;
	return $self->{"LOOKUP"}->tdta_for_action($self->_dbh(), ID_TA => $activity_type, ID_AZIONE => $action);
}

=head2 I<object>->B<get_activity_type_dot_flowchart>( I<activity_type_name>, [ I<OPTIMIZED_FOR> => <scalar>, [ SIMPLE => <1|0> ] ] )

Ritorna una stringa contenente le istruzioni I<dot> per generare il diagramma di flusso del tipo attivita' I<activity_type_name>.
Se viene passato il parametro I<OPTIMIZED_FOR> il codice I<dot> verra' ottimizzato per il formato di output scelto (es. pdf, svg, gif, ecc.).
Se viene passato il parametro I<SIMPLE> il codice I<dot> non mandera' a capo e non togliera' gli underscore (_) nei nomi degli stati e delle
azioni e non inserira' i permessi e i dati tecnici legati alle azioni; in questo modo sara' piu' semplice l'editing manuale.

=cut

sub get_activity_type_dot_flowchart {
	
	my $self = shift;
	my ($activity_type, %params) = @_;
	
	$self->clear_error();
	
	$self->last_error("Activity type not found!")
		&& return undef
			unless defined $activity_type && $self->test_activity_type_name($activity_type);

	my $capitalize_first = sub {
		my $text = shift;
		return join' ', map({ ucfirst() } split /\s/, lc $text);
	};
	
	use Text::Wrap;
	$Text::Wrap::columns = 15;
	$Text::Wrap::huge = 'overflow';
	$Text::Wrap::separator = '\n';

	my $title = $activity_type;
	$title =~ s/_/ /g unless $params{SIMPLE};
	
	my $dot = <<_EOF_;
digraph "$activity_type" {
_EOF_

	if (defined $params{OPTIMIZED_FOR} && $params{OPTIMIZED_FOR} eq 'pdf') {
		$dot .= <<_EOF_;

	size="8.5,11.0";
	margin=2.2;
	rank=sink;
	rankdir="TB";
	ranksep=2;
	minlen=1;
	ratio=auto;
	splines="true";
	overlap="scale";
	center=1;
	nodesep=1.1;
	quantum=1.1;

_EOF_

	}
	
	$dot .= <<_EOF_;
	
	graph[ratio=auto, center=1, fontname="Calibri,Helvetica", fontsize="14", style="bold", label="$title\\n ", labelloc="t"];
	node[shape=ellipse, fontname="Calibri,Helvetica", fontsize="10", fillcolor="grey95", style=filled];
	edge[fontname="Calibri,Helvetica", fontsize="9", color="grey20"];

_EOF_

	my $rows = $self->_dbh()->fetchall_arrayref("
		select distinct stato_iniziale,azione,stato_finale, case when pa.stato_iniziale = 'START' then -1 else nvl2 ((select 1 from stati st where st.nome = pa.stato_finale and st.flag_Stato_partenza in ('C','Q')), 1,0) end ordine
		from v_permission_action pa
		where tipo_attivita = ".$self->_dbh()->quote($activity_type)."
		order by 4,1
	");
	
	for my $row (@$rows) {
		my ($stato_iniziale, $azione, $stato_finale) = @$row;
		my $props = $self->get_action_properties($activity_type, $azione);
		my $azione_wrapped = $azione;
		$azione_wrapped =~ s/_/ /g unless $params{SIMPLE};
		$azione_wrapped = wrap('', '', $azione_wrapped) unless $params{SIMPLE};
		my $gruppi = $self->_dbh()->fetchall_arrayref("
			select lower(gruppo_utente) gruppo
			from v_permission_action
			where tipo_attivita = ".$self->_dbh()->quote($activity_type)."
			and stato_iniziale = ".$self->_dbh()->quote($stato_iniziale)."
			and azione = ".$self->_dbh()->quote($azione)."
			and stato_finale = ".$self->_dbh()->quote($stato_finale)."
			and upper(gruppo_utente) <> 'ROOT'
			order by 1
		");
		my $azione_style = "dashed";
		for (@$gruppi) {
			$azione_wrapped .= '\n'.$_->[0] unless $params{SIMPLE};
			my $group_info = $self->get_group_info($self->get_group_id($_->[0]));
			$azione_style = "solid" if(!$group_info->{ADMIN});
		}
		my %tmp = (
			 label => $azione_wrapped
			 ,style => $azione_style
		);
		unless($params{SIMPLE}) {
			if(scalar @$props) {
				$tmp{labeltooltip} = 'Properties: ' . join ', ', map { my $text = $_->{NOME_TDTA}; $text =~ s/_/ /g; $capitalize_first->($text) } @$props;
			} else {
				$tmp{labeltooltip} = 'No properties';
			}
		}
		$dot .= sprintf "\t\"%s\" -> \"%s\" [%s]\n", $stato_iniziale, $stato_finale, join(',', map { "$_=\"$tmp{$_}\"" } keys %tmp);
	}
	$dot .= "\n";

	my $stati = $self->_dbh()->fetchall_arrayref("
		select vp.stato_iniziale, 1 NS
		from v_permission_action vp
		where vp.tipo_attivita = ".$self->_dbh()->quote($activity_type)."
		union
		select vp.stato_finale, nvl((select 1 from v_permission_action vp2 where vp2.tipo_Attivita = vp.tipo_attivita and vp2.stato_iniziale = vp.stato_finale and rownum<2),0) NS
		from v_permission_action vp
		where vp.tipo_attivita = ".$self->_dbh()->quote($activity_type)."
	");

	for (@$stati) {
		my ($stato, $ns) = @$_;
		my $stato_wrapped = $stato;
		my %tmp = ();
		unless ($params{SIMPLE}){
			$stato_wrapped =~ s/_/ /g;
			$stato_wrapped = wrap('', '', $stato_wrapped);
			$tmp{label} = $stato_wrapped;
		}
		if($stato eq 'START') {
			$tmp{fillcolor} = "grey25";
			$tmp{shape} = "diamond";
			$tmp{fontcolor} = "white";
		} elsif ($self->is_activity_final_status( NAME => $stato )) {
			$tmp{fillcolor} = "grey50";
			$tmp{shape} = "ellipse";
		} elsif (!$ns) {
			$tmp{fillcolor} = "yellow";
			$tmp{shape} = "ellipse";
		}

		$dot .= sprintf "\t\"%s\" [%s]\n", $stato, join(',', map { "$_=\"$tmp{$_}\"" } keys %tmp);
	}

	$dot .= <<_EOF_;
}
_EOF_

	return $dot;

}

=head2 I<object>->B<get_activity_properties_group>( I<ACTIVITY_TYPE_NAME> => <arrayref>, I<ACTIVITY_TYPE_ID> => <arrayref> [, I<SHOW_ONLY_WITH_VISIBILITY> => <0|1>] )

Ritorna un I<arrayref> di I<hashref> con le caratteristiche delle proprieta' del tipo_attivita

Se è impostato a 1 la chiave SHOW_ONLY_WITH_VISIBILITY vengono restiutite le caratteristiche delle proprieta' solo dei tipi attivita su cui l'operatore ha visibilita'

=over 4

=item La struttura risultante e' la seguente:

	<arrayref> => [
			{
				GROUP => <scalar>,
				ID => <scalar>,
				PROPERTIES => [
					{
						'READ_ONLY' => <Y|N>,
						'NAME' => <scalar>,
						'EXPIRED' => <Y|N>,
						'NULLABLE' => <Y|N>
					}
				]
			}, ...
	]

=back

=cut

sub get_activity_properties_group {
	my $self = shift;
	local $@;
	$self->clear_last_error();
	
	my %params = @_;
	my $errmsg = '';

	$self->last_error($errmsg)
		&& return undef
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {}
				,OPTIONAL	=> {
					ACTIVITY_TYPE_NAME => { isa => 'ARRAY' }
					,ACTIVITY_TYPE_ID => { isa => 'ARRAY' }
				}
				,IGNORE_EXTRA_PARAMS	=> 1
			);
	
	$self->last_error("You can not specify both param ACTIVITY_TYPE_NAME e ACTIVITY_TYPE_ID!")
		&& return undef
			if defined $params{ACTIVITY_TYPE_NAME} && defined $params{ACTIVITY_TYPE_ID};
	
	$params{SHOW_ONLY_WITH_VISIBILITY} = 0 unless defined $params{SHOW_ONLY_WITH_VISIBILITY};
	
	my $eat = $self->enum_activity_type(SHOW_ONLY_WITH_VISIBILITY => $params{SHOW_ONLY_WITH_VISIBILITY});
	
	my $acts_type; 
	if (defined $params{ACTIVITY_TYPE_NAME}){
		for my $acts (@{$params{ACTIVITY_TYPE_NAME}}){
			$self->last_error("Bad ACTIVITY_TYPE_NAME $acts")
				&& return undef
					unless grep {$_ eq $acts} keys %{$eat};
		}
		$acts_type = $params{ACTIVITY_TYPE_NAME};
	} elsif (defined $params{ACTIVITY_TYPE_ID}){
		for my $acts (@{$params{ACTIVITY_TYPE_ID}}){
			$self->last_error("Bad ACTIVITY_TYPE_ID $acts")
				&& return undef
					unless grep {$_ eq $acts} values %{$eat};
		}
		$acts_type = [map {$self->get_activity_type_name($_)} @{$params{ACTIVITY_TYPE_ID}}];
	} else {
		$acts_type = [keys %{$eat}];
	}
	
	if (scalar @{$acts_type}== 1 && exists $self->{ACTIVITY_PROPERTIES_GROUP}->{$acts_type->[0]}){
		return $self->{ACTIVITY_PROPERTIES_GROUP}->{$acts_type->[0]} ;
	} elsif (scalar @{$acts_type} > 1){
		my $missing=0;
		my $return_hash = {};
		for my $activity_type_name (@{$acts_type}){
			if (!exists $self->{ACTIVITY_PROPERTIES_GROUP}->{$activity_type_name}){
				$missing = 1;
			} else {
				$return_hash->{$activity_type_name} = $self->{ACTIVITY_PROPERTIES_GROUP}->{$activity_type_name};
			}
		}
		return $return_hash unless $missing;
	}
	
	my $act_props_conf;
	
	for my $activity_type_name (@{$acts_type}){
		
		
		$act_props_conf->{$activity_type_name} = $self->{"LOOKUP"}->activity_properties_group($activity_type_name);
		
		for (my $i=0; $i < scalar (@{$act_props_conf->{$activity_type_name}});$i++){
			for (my $p=0; $p < scalar (@{$act_props_conf->{$activity_type_name}->[$i]->{PROPERTIES}}); $p++){
				next if defined $act_props_conf->{$activity_type_name}->[$i]->{PROPERTIES}->[$p]->{READ_ONLY} && $act_props_conf->{$activity_type_name}->[$i]->{PROPERTIES}->[$p]->{READ_ONLY} eq 'Y';
				$act_props_conf->{$activity_type_name}->[$i]->{PROPERTIES}->[$p]->{READ_ONLY} = 'Y'
					if defined $act_props_conf->{$activity_type_name}->[$i]->{PROPERTIES}->[$p]->{EXPIRED} && $act_props_conf->{$activity_type_name}->[$i]->{PROPERTIES}->[$p]->{EXPIRED} eq 'Y';
			}
		}
		
		$self->{ACTIVITY_PROPERTIES_GROUP}->{$activity_type_name} = $act_props_conf->{$activity_type_name};
		
	}
	return scalar @{$acts_type}== 1 ? $self->{ACTIVITY_PROPERTIES_GROUP}->{$acts_type->[0]}: $self->{ACTIVITY_PROPERTIES_GROUP};
}

=head2 I<object>->B<set_activity_properties_group>( I<ACTIVITY_TYPE_NAME> => <scalar>, I<ACTIVITY_TYPE_ID> => <scalar>, B<AP> => <arrayref> [, I<SHOW_ONLY_WITH_VISIBILITY> => <0|1>]  )

Imposta le activity property per il tipo attivita' impostato.

Se è impostato a 1 il paramentro SHOW_ONLY_WITH_VISIBILITY permette di impostare la configurazione solo dei tipi attivita su cui l'operatore ha visibilita'

Il parametro B<AP> deve rispettare il seguente formato (nota che la chiave B<GROUP> e' opzionale):

	[
		{
			GROUP => <scalar>,
			PROPERTIES => [
				{
					'NAME' => <scalar>,
					'READ_ONLY' => <Y|N>,
					'NULLABLE' => <Y|N>
				}
			]
		}, ...
	]

=cut

sub set_activity_properties_group {
	my $self = shift;
	local $@;
	$self->clear_last_error();
	
	my %params = @_;
	my $errmsg = '';
	
	$self->last_error("Only admins can set activity property group!")
		&& return undef
			unless $self->user()->is_admin();
	
	$self->last_error($errmsg)
		&& return undef
			unless $self->check_named_params(
				 ERRMSG	 => \$errmsg
				,PARAMS	 => \%params
				,MANDATORY  => {
					 AP => { isa => 'ARRAY' }
				}
				,OPTIONAL   => {
					 ACTIVITY_TYPE_NAME => { isa => 'SCALAR' }
					,ACTIVITY_TYPE_ID => { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS	=> 0
			);
	
	for my $g (@{$params{AP}}) {
		$self->last_error($errmsg)
			&& return undef
				unless $self->check_named_params(
					 ERRMSG	 => \$errmsg
					,PARAMS	 => $g
					,MANDATORY  => {
						 PROPERTIES => { isa => 'ARRAY' }
					}
					,OPTIONAL   => {
						 GROUP => { isa => 'SCALAR', pattern => qr{^.+$} }
					}
					,IGNORE_EXTRA_PARAMS	=> 1
				);
		for my $p (@{$g->{PROPERTIES}}) {
			$self->last_error($errmsg)
				&& return undef
					unless $self->check_named_params(
						 ERRMSG	 => \$errmsg
						,PARAMS	 => $p
						,MANDATORY  => {
							 NAME => { isa => 'SCALAR', pattern => qr{^.+$} }
							,READ_ONLY => { isa => 'SCALAR', list => ['Y','N'] }
							,NULLABLE => { isa => 'SCALAR', list => ['Y','N'] }
						}
						,OPTIONAL   => {}
						,IGNORE_EXTRA_PARAMS	=> 1
					);
		}
	}
	
	$self->last_error("You can not specify both param ACTIVITY_TYPE_NAME or ACTIVITY_TYPE_ID!")
		&& return undef
			if defined $params{ACTIVITY_TYPE_NAME} && defined $params{ACTIVITY_TYPE_ID};
	
	$self->last_error("You must specify on param in ACTIVITY_TYPE_NAME or ACTIVITY_TYPE_ID!")
		&& return undef
			if defined $params{ACTIVITY_TYPE_NAME} && defined $params{ACTIVITY_TYPE_ID};
	
	my $eat = $self->enum_activity_type(SHOW_ONLY_WITH_VISIBILITY => $params{SHOW_ONLY_WITH_VISIBILITY});
	
	my $act_type_id; 
	if (defined $params{ACTIVITY_TYPE_NAME}){
		$self->last_error("Bad ACTIVITY_TYPE_NAME $params{ACTIVITY_TYPE_NAME}")
			&& return undef
				unless grep {$_ eq $params{ACTIVITY_TYPE_NAME}} keys %{$eat};
		$act_type_id = $eat->{$params{ACTIVITY_TYPE_NAME}};
	} elsif (defined $params{ACTIVITY_TYPE_ID}){
		$self->last_error("Bad ACTIVITY_TYPE_ID $params{ACTIVITY_TYPE_ID}")
			&& return undef
				unless grep {$_ eq $params{ACTIVITY_TYPE_ID}} values %{$eat};
		$act_type_id = $params{ACTIVITY_TYPE_ID};
	}
	
	# estraggo i gruppi
	my @groups = ();
	for my $g (@{$params{AP}}) {
		next unless exists $g->{GROUP};
		push @groups, $g->{GROUP};
	}
	
	my $sql;
	my $i;
	
	$self->_dbh()->do("savepoint API__ART__set_act_prop_group");
	
	# elimino le ap relative al tipo attivita
	$sql = "delete from ap where id_tipo_attivita = ".$act_type_id;
	eval { $self->_dbh()->do($sql) };
	if(SIRTI::DB::iserror()) {
		$self->last_error("Unable to delete from ap: ".SIRTI::DB::get_errormessage());
		$self->_dbh()->do("rollback to savepoint API__ART__set_act_prop_group");
		return undef;
	}
	
	# elimino i gruppi abilitati per il tipo attivita
	$sql = "delete from at_ap_groups where id_tipo_attivita = ".$act_type_id;
	eval { $self->_dbh()->do($sql) };
	if(SIRTI::DB::iserror()) {
		$self->last_error("Unable to delete from at_ap_groups: ".SIRTI::DB::get_errormessage());
		$self->_dbh()->do("rollback to savepoint API__ART__set_act_prop_group");
		return undef;
	}
	
	# inserisco eventuali nuovi gruppi
	if(scalar @groups) {
		$sql = "insert into ap_groups (id, descrizione, disabilitato) select seq_ap_groups.nextval, descrizione, null from (";
		$sql .= join(" union ", map { "select ".$self->_dbh()->quote($_)." descrizione from dual" } @groups);
		$sql .= " minus select descrizione from ap_groups where disabilitato is null";
		$sql .= ")"; 
		eval { $self->_dbh()->do($sql) };
		if(SIRTI::DB::iserror()) {
			$self->last_error("Unable to insert into ap_groups: ".SIRTI::DB::get_errormessage());
			$self->_dbh()->do("rollback to savepoint API__ART__set_act_prop_group");
			return undef;
		}
	}
	
	# inserisco i gruppi abilitati per il tipo attività
	if(scalar @groups) {
		$i = 0;
		$sql = "insert into at_ap_groups (id_raggruppamento, id_tipo_attivita, ordine, disabilitato) ";
		$sql .= join(" union ", map { "select (select id from ap_groups where descrizione = ".$self->_dbh()->quote($_)." and disabilitato is null), ".$act_type_id.", ".($i++).", null from dual" } @groups);
		eval { $self->_dbh()->do($sql) };
		if(SIRTI::DB::iserror()) {
			$self->last_error("Unable to insert into at_ap_groups: ".SIRTI::DB::get_errormessage());
			$self->_dbh()->do("rollback to savepoint API__ART__set_act_prop_group");
			return undef;
		}
	}
	
	# inserisco le activity property
	my $ordine_tdta = 0;
	for my $g (@{$params{AP}}) {
		my $ordine_raggruppamento = 0;
		for my $p (@{$g->{PROPERTIES}}) {
			$self->last_error("Bad TDTA name $p->{NAME}")
				&& return undef
					unless $self->{LOOKUP}->is_nome_tdta($p->{NAME});
			$sql = "insert into ap (ID,ID_TIPO_ATTIVITA,ID_TIPO_DATO_TECNICO_ATTIVITA,ORDINE,ID_RAGGRUPPAMENTO,ORDINE_RAGGRUPPAMENTO,SOLO_LETTURA,DISABILITATO,ANNULLABILE) values (";
			$sql .= "seq_ap.nextval,";
			$sql .= $act_type_id.",";
			$sql .= $self->{LOOKUP}->id_tdta($p->{NAME}).",";
			$sql .= ($ordine_tdta++).",";
			$sql .= exists $g->{GROUP} ? "(select id from ap_groups where descrizione = ".$self->_dbh()->quote($g->{GROUP})." and disabilitato is null)," : "null,";
			$sql .= exists $g->{GROUP} ? ($ordine_raggruppamento++)."," : "null,";
			$sql .= $p->{READ_ONLY} eq "Y" ? "'Y'," : "null,";
			$sql .= "null,";
			$sql .= $p->{NULLABLE} eq "Y" ? "'Y'" : "null";
			$sql .= ")";
			eval { $self->_dbh()->do($sql) };
			if(SIRTI::DB::iserror()) {
				$self->last_error("Unable to insert into ap ".$p->{NAME}.": ".SIRTI::DB::get_errormessage());
				$self->_dbh()->do("rollback to savepoint API__ART__set_act_prop_group");
				return undef;
			}
		}
	}
	
	unless($self->_refresh_lookup()) {
		$self->_dbh()->do("rollback to savepoint API__ART__set_act_prop_group");
		return undef;
	}
	
	return 1;
}

=head2 I<object>->B<get_activity_property_groups>( )

Ritorna un I<arrayref> con l'elenco dei gruppi di activity property configurati

=cut

sub get_activity_property_groups {
	my $self = shift;
	
	return $self->{"LOOKUP"}->activity_property_groups();
}

=head2 I<object>->B<get_system_property_groups>( )

Ritorna un I<arrayref> con l'elenco dei gruppi di system property configurati

=cut

sub get_system_property_groups {
	my $self = shift;
	
	return $self->{"LOOKUP"}->system_property_groups();
}

=head2 I<object>->B<get_system_properties_group>( I<SYSTEM_TYPE_NAME> => <arrayref>, I<SYSTEM_TYPE_ID> => <arrayref> [, I<SHOW_ONLY_WITH_VISIBILITY> => <0|1>] )

Ritorna un I<arrayref> di I<hashref> con le caratteristiche delle proprieta' del tipo_Sistema

Se è impostato a 1 la chiave SHOW_ONLY_WITH_VISIBILITY vengono restiutite le caratteristiche delle proprieta' solo dei tipi sistema su cui l'operatore ha visibilita'

=over 4

=item La struttura risultante e' la seguente:

	<arrayref> => [
			{
				GROUP => <scalar>,
				ID => <scalar>,
				PROPERTIES => [
					{
						'READ_ONLY' => <Y|N>,
						'NAME' => <scalar>,
						'EXPIRED' => <Y|N>,
						'NULLABLE' => <Y|N>
					}
				]
			}, ...
	]

=back

=cut

sub get_system_properties_group {
	my $self = shift;
	local $@;
	$self->clear_last_error();
	
	my %params = @_;
	my $errmsg = '';

	$self->last_error($errmsg)
		&& return undef
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {}
				,OPTIONAL	=> {
					SYSTEM_TYPE_NAME => { isa => 'ARRAY' }
					,SYSTEM_TYPE_ID => { isa => 'ARRAY' }
				}
				,IGNORE_EXTRA_PARAMS	=> 1
			);
	
	$self->last_error("You can not specify both param SYSTEM_TYPE_NAME e SYSTEM_TYPE_ID!")
		&& return undef
			if defined $params{SYSTEM_TYPE_NAME} && defined $params{SYSTEM_TYPE_ID};
	
	$params{SHOW_ONLY_WITH_VISIBILITY} = 0 unless defined $params{SHOW_ONLY_WITH_VISIBILITY};
	
	my $eat = $self->enum_system_type(SHOW_ONLY_WITH_VISIBILITY => $params{SHOW_ONLY_WITH_VISIBILITY});
	
	my $sys_type; 
	if (defined $params{SYSTEM_TYPE_NAME}){
		for my $syss (@{$params{SYSTEM_TYPE_NAME}}){
			$self->last_error("Bad SYSTEM_TYPE_NAME $syss")
				&& return undef
					unless grep {$_ eq $syss} keys %{$eat};
		}
		$sys_type = $params{SYSTEM_TYPE_NAME};
	} elsif (defined $params{SYSTEM_TYPE_ID}){
		for my $syss (@{$params{SYSTEM_TYPE_ID}}){
			$self->last_error("Bad SYSTEM_TYPE_ID $syss")
				&& return undef
					unless grep {$_ eq $syss} values %{$eat};
		}
		$sys_type = [map {$self->get_system_type_name($_)} @{$params{SYSTEM_TYPE_ID}}];
	} else {
		$sys_type = [keys %{$eat}];
	}
	
	if (scalar @{$sys_type}== 1 && exists $self->{SYSTEM_PROPERTIES_GROUP}->{$sys_type->[0]}){
		return $self->{SYSTEM_PROPERTIES_GROUP}->{$sys_type->[0]} ;
	} elsif (scalar @{$sys_type} > 1){
		my $missing=0;
		my $return_hash = {};
		for my $system_type_name (@{$sys_type}){
			if (!exists $self->{SYSTEM_PROPERTIES_GROUP}->{$system_type_name}){
				$missing = 1;
			} else {
				$return_hash->{$system_type_name} = $self->{SYSTEM_PROPERTIES_GROUP}->{$system_type_name};
			}
		}
		return $return_hash unless $missing;
	}
	
	my $sys_props_conf;
	
	for my $system_type_name (@{$sys_type}){
		
		$sys_props_conf->{$system_type_name} = $self->{"LOOKUP"}->system_properties_group($system_type_name);
		
		# se non c'è la configurazione li mettiamo tutti senza gruppo
		unless (scalar @{$sys_props_conf->{$system_type_name}}){
			my $prepare = $self->_create_prepare(__PACKAGE__.'spp', "
				select ts.nome_Tipo_sistema SYSTEM_TYPE_NAME
				--, null GROUP_NAME
				--, null GROUP_ID
				, TDT.nome PROPERTY_NAME
				, 'Y' READ_ONLY
				, 'N' EXPIRED -- per ora non ha senso la scadenza
				, 'Y' NULLABLE
				, t.ALIAS ALIAS
				from tipi_sistema_tipi_dati_tecnici t
					join tipi_Sistema ts on ts.id_tipo_Sistema = t.id_tipo_sistema
					join tipi_Dati_Tecnici tdt on tdt.id_tipo_Dato_Tecnico = t.id_tipo_Dato_Tecnico
				where t.id_tipo_sistema = ?
				order by ts.nome_Tipo_sistema,tdt.nome asc
			");

			my $sr = $prepare->fetchall_hashref($self->get_system_type_id($system_type_name));

			my $tmp = {
				PROPERTIES => []
			};
			for my $row (@$sr){
				my $props = {
					NAME => $row->{PROPERTY_NAME}
					, ALIAS => $row->{ALIAS}
					, EXPIRED => $row->{EXPIRED}
					, READ_ONLY => $row->{READ_ONLY}
					, NULLABLE => $row->{NULLABLE}
				};
				
				push @{$tmp->{PROPERTIES}}, $props;
			}

			$sys_props_conf->{$system_type_name} = [$tmp];
		}
		
		for (my $i=0; $i < scalar (@{$sys_props_conf->{$system_type_name}});$i++){
			for (my $p=0; $p < scalar (@{$sys_props_conf->{$system_type_name}->[$i]->{PROPERTIES}}); $p++){
				next if defined $sys_props_conf->{$system_type_name}->[$i]->{PROPERTIES}->[$p]->{READ_ONLY} && $sys_props_conf->{$system_type_name}->[$i]->{PROPERTIES}->[$p]->{READ_ONLY} eq 'Y';
				$sys_props_conf->{$system_type_name}->[$i]->{PROPERTIES}->[$p]->{READ_ONLY} = 'Y'
					if defined $sys_props_conf->{$system_type_name}->[$i]->{PROPERTIES}->[$p]->{EXPIRED} && $sys_props_conf->{$system_type_name}->[$i]->{PROPERTIES}->[$p]->{EXPIRED} eq 'Y';
			}
		}
		
		$self->{SYSTEM_PROPERTIES_GROUP}->{$system_type_name} = $sys_props_conf->{$system_type_name};
		
	}
	return scalar @{$sys_type}== 1 ? $self->{SYSTEM_PROPERTIES_GROUP}->{$sys_type->[0]}: $self->{SYSTEM_PROPERTIES_GROUP};
}

=head2 I<object>->B<set_system_properties_group>( I<SYSTEM_TYPE_NAME> => <scalar>, I<SYSTEM_TYPE_ID> => <scalar>, B<SP> => <arrayref> [, I<SHOW_ONLY_WITH_VISIBILITY> => <0|1>]  )

Imposta le system property per il tipo sistema impostato.

Se è impostato a 1 il paramentro SHOW_ONLY_WITH_VISIBILITY permette di impostare la configurazione solo dei tipi sistema su cui l'operatore ha visibilita'

Il parametro B<SP> deve rispettare il seguente formato (nota che la chiave B<GROUP> e' opzionale):

	[
		{
			GROUP => <scalar>,
			PROPERTIES => [
				{
					'NAME' => <scalar>,
					'READ_ONLY' => <Y|N>,
					'NULLABLE' => <Y|N>
				}
			]
		}, ...
	]

=cut

sub set_system_properties_group {
	my $self = shift;
	local $@;
	$self->clear_last_error();
	
	my %params = @_;
	my $errmsg = '';
	
	$self->last_error("Only admins can set system property group!")
		&& return undef
			unless $self->user()->is_admin();
	
	$self->last_error($errmsg)
		&& return undef
			unless $self->check_named_params(
				 ERRMSG	 => \$errmsg
				,PARAMS	 => \%params
				,MANDATORY  => {
					 SP => { isa => 'ARRAY' }
				}
				,OPTIONAL   => {
					 SYSTEM_TYPE_NAME => { isa => 'SCALAR' }
					,SYSTEM_TYPE_ID => { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS	=> 0
			);
	
	for my $g (@{$params{SP}}) {
		$self->last_error($errmsg)
			&& return undef
				unless $self->check_named_params(
					 ERRMSG	 => \$errmsg
					,PARAMS	 => $g
					,MANDATORY  => {
						 PROPERTIES => { isa => 'ARRAY' }
					}
					,OPTIONAL   => {
						 GROUP => { isa => 'SCALAR', pattern => qr{^.+$} }
					}
					,IGNORE_EXTRA_PARAMS	=> 1
				);
		for my $p (@{$g->{PROPERTIES}}) {
			$self->last_error($errmsg)
				&& return undef
					unless $self->check_named_params(
						 ERRMSG	 => \$errmsg
						,PARAMS	 => $p
						,MANDATORY  => {
							 NAME => { isa => 'SCALAR', pattern => qr{^.+$} }
							,READ_ONLY => { isa => 'SCALAR', list => ['Y','N'] }
							,NULLABLE => { isa => 'SCALAR', list => ['Y','N'] }
						}
						,OPTIONAL   => {}
						,IGNORE_EXTRA_PARAMS	=> 1
					);
		}
	}
	
	$self->last_error("You can not specify both param SYSTEM_TYPE_NAME or SYSTEM_TYPE_ID!")
		&& return undef
			if defined $params{SYSTEM_TYPE_NAME} && defined $params{SYSTEM_TYPE_ID};
	
	$self->last_error("You must specify on param in SYSTEM_TYPE_NAME or SYSTEM_TYPE_ID!")
		&& return undef
			if defined $params{SYSTEM_TYPE_NAME} && defined $params{SYSTEM_TYPE_ID};
	
	my $est = $self->enum_system_type(SHOW_ONLY_WITH_VISIBILITY => $params{SHOW_ONLY_WITH_VISIBILITY});
	
	my $sys_type_id; 
	if (defined $params{SYSTEM_TYPE_NAME}){
		$self->last_error("Bad SYSTEM_TYPE_NAME $params{SYSTEM_TYPE_NAME}")
			&& return undef
				unless grep {$_ eq $params{SYSTEM_TYPE_NAME}} keys %{$est};
		$sys_type_id = $est->{$params{SYSTEM_TYPE_NAME}};
	} elsif (defined $params{SYSTEM_TYPE_ID}){
		$self->last_error("Bad SYSTEM_TYPE_ID $params{SYSTEM_TYPE_ID}")
			&& return undef
				unless grep {$_ eq $params{SYSTEM_TYPE_ID}} values %{$est};
		$sys_type_id = $params{SYSTEM_TYPE_ID};
	}
	
	# estraggo i gruppi
	my @groups = ();
	for my $g (@{$params{SP}}) {
		next unless exists $g->{GROUP};
		push @groups, $g->{GROUP};
	}
	
	my $sql;
	my $i;
	
	$self->_dbh()->do("savepoint API__ART__set_sys_prop_group");
	
	# elimino le ap relative al tipo sistema
	$sql = "delete from sp where id_tipo_sistema = ".$sys_type_id;
	eval { $self->_dbh()->do($sql) };
	if(SIRTI::DB::iserror()) {
		$self->last_error("Unable to delete from sp: ".SIRTI::DB::get_errormessage());
		$self->_dbh()->do("rollback to savepoint API__ART__set_sys_prop_group");
		return undef;
	}
	
	# elimino i gruppi abilitati per il tipo sistema
	$sql = "delete from st_sp_groups where id_tipo_sistema = ".$sys_type_id;
	eval { $self->_dbh()->do($sql) };
	if(SIRTI::DB::iserror()) {
		$self->last_error("Unable to delete from st_sp_groups: ".SIRTI::DB::get_errormessage());
		$self->_dbh()->do("rollback to savepoint API__ART__set_sys_prop_group");
		return undef;
	}
	
	# inserisco eventuali nuovi gruppi
	if(scalar @groups) {
		$sql = "insert into sp_groups (id, descrizione, disabilitato) select seq_sp_groups.nextval, descrizione, null from (";
		$sql .= join(" union ", map { "select ".$self->_dbh()->quote($_)." descrizione from dual" } @groups);
		$sql .= " minus select descrizione from sp_groups where disabilitato is null";
		$sql .= ")"; 
		eval { $self->_dbh()->do($sql) };
		if(SIRTI::DB::iserror()) {
			$self->last_error("Unable to insert into sp_groups: ".SIRTI::DB::get_errormessage());
			$self->_dbh()->do("rollback to savepoint API__ART__set_sys_prop_group");
			return undef;
		}
	}
	
	# inserisco i gruppi abilitati per il tipo sistema
	if(scalar @groups) {
		$i = 0;
		$sql = "insert into st_sp_groups (id_raggruppamento, id_tipo_sistema, ordine, disabilitato) ";
		$sql .= join(" union ", map { "select (select id from sp_groups where descrizione = ".$self->_dbh()->quote($_)." and disabilitato is null), ".$sys_type_id.", ".($i++).", null from dual" } @groups);
		eval { $self->_dbh()->do($sql) };
		if(SIRTI::DB::iserror()) {
			$self->last_error("Unable to insert into st_as_groups: ".SIRTI::DB::get_errormessage());
			$self->_dbh()->do("rollback to savepoint API__ART__set_sys_prop_group");
			return undef;
		}
	}
	
	# inserisco le system property
	my $ordine_tdt = 0;
	for my $g (@{$params{SP}}) {
		my $ordine_raggruppamento = 0;
		for my $p (@{$g->{PROPERTIES}}) {
			$self->last_error("Bad TDT name $p->{NAME}")
				&& return undef
					unless $self->{LOOKUP}->is_nome_tdts($p->{NAME});
			$sql = "insert into sp (ID,ID_TIPO_SISTEMA,ID_TIPO_DATO_TECNICO,ORDINE,ID_RAGGRUPPAMENTO,ORDINE_RAGGRUPPAMENTO,SOLO_LETTURA,DISABILITATO,ANNULLABILE) values (";
			$sql .= "seq_sp.nextval,";
			$sql .= $sys_type_id.",";
			$sql .= $self->{LOOKUP}->id_tdts($p->{NAME}).",";
			$sql .= ($ordine_tdt++).",";
			$sql .= exists $g->{GROUP} ? "(select id from sp_groups where descrizione = ".$self->_dbh()->quote($g->{GROUP})." and disabilitato is null)," : "null,";
			$sql .= exists $g->{GROUP} ? ($ordine_raggruppamento++)."," : "null,";
			$sql .= $p->{READ_ONLY} eq "Y" ? "'Y'," : "null,";
			$sql .= "null,";
			$sql .= $p->{NULLABLE} eq "Y" ? "'Y'" : "null";
			$sql .= ")";
			eval { $self->_dbh()->do($sql) };
			if(SIRTI::DB::iserror()) {
				$self->last_error("Unable to insert into sp ".$p->{NAME}.": ".SIRTI::DB::get_errormessage());
				$self->_dbh()->do("rollback to savepoint API__ART__set_sys_prop_group");
				return undef;
			}
		}
	}
	
	unless($self->_refresh_lookup()) {
		$self->_dbh()->do("rollback to savepoint API__ART__set_sys_prop_group");
		return undef;
	}
	
	return 1;
}

=head2 I<object>->B<test_action_properties>( I<ACTIVITY_TYPE> => <scalar> , I<ACTION> => <scalar>, I<PROPERTIES> => <hashref>, [ I<MISSING_REQUIRED_PARAMS> => <arrayref>, I<EXTRA_PARAMS> <arrayref>, I<IGNORE_EXTRA_PARAMS> => <boolean> ] )

Ritorna un I<boolean> se le B<PROPERTIES> inerenti al flusso B<ACTIVITY_TYPE> (I<come nome oppure id>) ed interessati dall'azione specificata da B<ACTION> (I<come nome oppure id>) soddisfano i requisiti dei TDTA, e non ci siano parametri extra se non specificato il flag B<IGNORE_EXTRA_PARAMS>. Se vengono specificati I<MISSING_REQUIRED_PARAMS>, I<EXTRA_PARAMS> li riempie rispettivamente con le liste dei nomi dei TDTA obbligatori mancanti e di quelli di troppo.

=cut

sub test_action_properties {
	my $self = shift;
	my %params = @_;
	
	croak "Expecting ACTIVITY_TYPE, ACTION and PROPERTIES mandatory parameters!\n" unless defined $params{ACTIVITY_TYPE} && defined $params{ACTION} && defined $params{PROPERTIES};
	my $properties = $self->get_action_properties($params{ACTIVITY_TYPE}, $params{ACTION});
	my $test;
	
	my @all = map { $_->{NOME_TDTA}; } @$properties;
	my @obbl = map { $_->{NOME_TDTA}; } grep { $_->{OBBLIGATORIO} eq 'Y' } @$properties;
	my @pass = map { $_ } keys %{$params{PROPERTIES}};
	my @rq_par = grep { !exists {map { $_ => 1 } @pass}->{$_} } @obbl;
	my @ex_par = grep { !exists {map { $_ => 1 } @all}->{$_} } @pass;
	
	push @{$params{MISSING_REQUIRED_PARAMS}}, @rq_par if ref $params{MISSING_REQUIRED_PARAMS} eq 'ARRAY';
	push @{$params{EXTRA_PARAMS}}, @ex_par if ref $params{EXTRA_PARAMS} eq 'ARRAY';

	$test = (scalar @rq_par == 0);
	$test &&= (scalar @ex_par == 0) if !$params{IGNORE_EXTRA_PARAMS};
	
	return $test;
}

=head2 I<object>->B<get_activity_type_behaviour>( I<ACTIVITY_TYPE_NAME> => <scalar> OR I<ACTIVITY_TYPE_ID> => <scalar>, [ I<NAME> => <scalar> ] )

Restituisce un I<hashref> di <boolean> inerenti a tutti i comportamenti del tipo attivita' configurati. Se manca la tabella TIPI_ATTIVITA_COMP restituisce un I<hashref> vuoto. Se viene passato B<NAME> restituisce un I<boolean> del valore impostato nella colonna <name>.

=cut

sub get_activity_type_behaviour {
	my $self = shift;
	my %params = @_;
	my $activity_type_id;
	my $result= {};
	
	croak "Expecting ACTIVITY_TYPE_NAME or ACTIVITY_TYPE_ID parameters!\n" unless defined $params{ACTIVITY_TYPE_NAME} || defined $params{ACTIVITY_TYPE_ID};
	croak "ACTIVITY_TYPE_ID accepts only digits!\n" unless $params{ACTIVITY_TYPE_ID} =~ /^\d+$/;
	croak "Expecting only one between ACTIVITY_TYPE_NAME and ACTIVITY_TYPE_ID parameters!\n" if defined $params{ACTIVITY_TYPE_NAME} && defined $params{ACTIVITY_TYPE_ID};

	$activity_type_id = $params{ACTIVITY_TYPE_ID} if $params{ACTIVITY_TYPE_ID};
	$activity_type_id = $self->get_activity_type_id($params{ACTIVITY_TYPE_NAME}) unless $params{ACTIVITY_TYPE_ID};

	my $sql_check = $self->_dbh()->query("select 'k' from user_tables where table_name='TIPI_ATTIVITA_COMP'");

	if($sql_check>0){
		my @columns = $self->_dbh()->query("select column_name from user_tab_columns where TABLE_NAME='TIPI_ATTIVITA_COMP' and column_name not in ('ID', 'ID_TIPO_ATTIVITA', 'ABILITATO') order by column_id");
		if (scalar @columns > 0) {
			my @column = map { $_->[0] } @columns;
			my $sql = $self->_dbh()->fetchall_hashref("select " . join (', ', @column) . ", ABILITATO from tipi_attivita_comp where id_tipo_attivita=$activity_type_id");
			if (scalar @$sql == 1) {
				my $key= undef;
				my $value= undef;
				my $options = shift @$sql;
				my $abilitato = $options->{'ABILITATO'};
				delete $options->{'ABILITATO'};
				$result->{$key} = $abilitato && $value while (($key => $value) = each(%$options));
			} elsif (scalar @$sql > 1) {
				croak "Unexpected number of results found, may be a misconfiguration!";
			} else {
				my $key = undef;
				$result->{$_} = undef for @column;
			}
		}
	}
	return $result if !$params{NAME};
	return $result->{$params{NAME}} if $params{NAME};

}

=head2 I<object>->B<get_action_status>( I<ACTIVITY_TYPE_NAME> , I<ACTION_NAME> )

Ritorna un I<ARRAYREF> coi nomi degli stati appartenenti al flusso I<ACTIVITY_TYPE_NAME> ed
interessati dall'azione specificata da B<ACTION_NAME>.

=cut
sub get_action_status {
	my $self = shift;
	my ($activity_type, $action) = @_;
	$activity_type = $self->get_activity_type_id($activity_type) unless $activity_type =~ /^\d+$/;
	$action = $self->get_activity_action_id($action) unless $action =~ /^\d+$/;
	my $sql = "
		SELECT  DISTINCT s.nome
		FROM    permission_action pa
               ,stati s
		WHERE   pa.id_tipo_attivita = $activity_type
                AND pa.id_action_permessa = '$action'
                AND s.id_stato = pa.id_stato_iniziale
	";
	return [ $self->_dbh()->fetch_minimalized($sql) ];
}

=head2 I<object>->B<create_group>( I<NAME> => <scalar>, I<DESCRIPTION> => <scalar> [, I<IS_ADMIN> => <1|0>, I<IS_PRIVATE> => <1|0>, I<IS_AUTOGENERATED> => <1|0>] )

Crea un nuovo gruppo. I<IS_ADMIN> default 0. I<IS_PRIVATE> default 0. I<IS_AUTOGENERATED> default 0. Ritorna l'ID del gruppo in caso di successo.

=cut
sub create_group {
	my $self = shift;
	
	local $@;
	$self->clear_last_error();
	
	my %params = @_;
	my $errmsg = '';

    $self->last_error("Only admins can create_group!")
		&& return undef
			unless $self->user()->is_admin();

	$self->last_error($errmsg)
		&& return undef
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					 NAME	=> { isa => 'SCALAR' }
					,DESCRIPTION			=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					IS_ADMIN => { isa => 'SCALAR', list => [0,1] }
					,IS_PRIVATE => { isa => 'SCALAR', list => [0,1] }
					,IS_AUTOGENERATED => { isa => 'SCALAR', list => [0,1] }
				}
				,IGNORE_EXTRA_PARAMS	=> 0
			);
	
	$params{IS_ADMIN} = 0
		unless exists $params{IS_ADMIN};
	
	$self->last_error("Group ".$params{NAME}." already exists!")
		&& return undef
			if $self->test_group_name($params{NAME});

	if ( $params{NAME} !~ /^[a-zA-Z0-9_]+$/ and $params{NAME} !~ /^[a-z0-9!#$%&'*+\/=?^_`{|}~.-]+@[a-z0-9]([a-z0-9-]*[a-z0-9])?(\.[a-z0-9]([a-z0-9-]*[a-z0-9])?)*$/i ) {
		$self->last_error("Param NAME not valid: you can use letters, numbers, minus and underscore character!");
		return undef;
	}
	
	if(!$self->group_extended_support()){
		if (exists $params{IS_PRIVATE}){
			$self->last_error("Missing Group Private Support: you can not pass IS_PRIVATE param");
			return undef;
		}
		if (exists $params{IS_AUTOGENERATED}){
			$self->last_error("Missing Group Autogenerated Support: you can not pass IS_AUTOGENERATED param");
			return undef;
		}
	}
	
	my $id_group = $self->_dbh()->fetch_minimalized("select seq_gruppi.nextval from dual");

	my $sql = "
		insert into  GRUPPI (
			ID_GRUPPO
			, DESCRIZIONE
			, NOME
			, AMMINISTRATORE
			".($self->group_extended_support() ? ", PRIVATO" : "")."
			".($self->group_extended_support() ? ", AUTOGENERATO" : "")."
		) values (
			 $id_group
			,".$self->_dbh()->quote($params{DESCRIPTION})."
			,".$self->_dbh()->quote($params{NAME})."
			,".($params{IS_ADMIN} ? $self->_dbh()->quote('Y') : "NULL")."
			".($self->group_extended_support() ? ",".($params{IS_PRIVATE} ? $self->_dbh()->quote('Y') : "NULL") : "")."
			".($self->group_extended_support() ? ",".($params{IS_AUTOGENERATED} ? $self->_dbh()->quote('Y') : "NULL") : "")."
		)
	";
	eval {
		$self->_dbh()->do($sql);
	};
	$self->last_error("Error creating group!\n$@")
		&& return undef
			if $@;
	
	return undef unless $self->_refresh_lookup();

	return $id_group;
	
}

=head2 I<object>->B<create_user>( I<NAME> => <scalar>, I<FIRST_NAME> => <scalar>, I<LAST_NAME> => <scalar> [, I<PASSWORD> => <scalar>, I<MD5_PASSWORD> => <scalar>] [, I<GROUPS> => <hashref>, I<ROLES> => <arrayref>, I<ROLE_ALIASES> => <arrayref>] [, I<EMAIL> => <scalar>, I<MOBILE_PHONE> => <scalar>, I<SERVICE_USER> => <1|0>] )

Crea un nuovo utente. Ritorna l'ID dell'utente in caso di successo.

=item B<NAME> Login dell'utente

=item B<PASSWORD> Password dell'utente

=item B<MD5_PASSWORD> Password dell'utente in formato md5

=item I parametri B<PASSWORD> e B<MD5_PASSWORD> sono mutamente esclusivi ed e' sempre obbligatorio specificarne uno.

=item B<FIRST_NAME> Nome dell'utente

=item B<LAST_NAME> Cognome dell'utente

=item B<EMAIL> Email dell'utente

=item B<MOBILE_PHONE> Mobile Phone dell'utente

=item B<SERVICE_USER> Default a 0. Se 1 definisce l'operatore come utente di servizio

=item B<GROUPS> Hash che contiene le seguenti chiavi

=over

=item B<PRIMARY> Gruppo primario (Stringa)

=item B<SECONDARY> Gruppi secondari (Array)

=back

=item B<ROLES> Array che contiene l'elenco dei ruoli

=item B<ROLE_ALIASES> Array che contiene l'elenco degli alias al ruolo

=item I parametri B<ROLES>, B<ROLE_ALIASES> e B<GROUPS> sono mutamente esclusivi ed e' sempre obbligatorio specificarne uno.

=cut
sub create_user {
	my $self = shift;
	
	local $@;
	$self->clear_last_error();
	
	my %params = @_;
	my $errmsg = '';
	
	$self->last_error("Only user admins can create_user!")
		&& return undef
			unless $self->user()->is_user_admin();
	
	$self->last_error($errmsg)
		&& return undef
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					 NAME		=> { isa => 'SCALAR' }
					,FIRST_NAME	=> { isa => 'SCALAR' }
					,LAST_NAME	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					 PASSWORD		=> { isa => 'SCALAR' }
					,MD5_PASSWORD	=> { isa => 'SCALAR' }
					,GROUPS			=> { isa => 'HASH' }
					,ROLES			=> { isa => 'ARRAY' }
					,ROLE_ALIASES	=> { isa => 'ARRAY' }
					,SERVICE_USER	=> { isa => 'SCALAR', list => [1,0] }
					,EMAIL			=> { isa => 'SCALAR' }
					,MOBILE_PHONE	=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS	=> 0
			);
	
	
	my $count_pwd_param = 0;
	for my $c_p ('PASSWORD', 'MD5_PASSWORD'){
		$count_pwd_param++ if defined $params{$c_p};
	}
	
	$self->last_error("You must set one and only one between PASSWORD and MD5_PASSWORD params!")
		&& return undef
			if $count_pwd_param != 1;
	
	my $count_param = 0;
	for my $c_p ('GROUPS', 'ROLES', 'ROLE_ALIASES'){
		$count_param++ if defined $params{$c_p};
	}
	
	$self->last_error("You must set one and only one between GROUPS, ROLES and ROLE_ALIASES params!")
		&& return undef
			if $count_param != 1;
	
	if (defined $params{GROUPS}){
		$self->last_error($errmsg)
			&& return undef
				unless $self->check_named_params(
					 ERRMSG		=> \$errmsg
					,PARAMS		=> $params{GROUPS}
					,MANDATORY	=> {
						PRIMARY	=> { isa => 'SCALAR' }
					}
					,OPTIONAL	=> {
						SECONDARY	=> { isa => 'ARRAY' }
					}
					,IGNORE_EXTRA_PARAMS	=> 0
				);
	}
	
	$params{SERVICE_USER} = 0
		unless exists $params{SERVICE_USER};

	# TODO: Sostituire la seconda regular expression con la verifica di Email::Valid
	if ( $params{NAME} !~ /^[a-zA-Z0-9_]+$/ and $params{NAME} !~ /^[a-z0-9!#$%&'*+\/=?^_`{|}~.-]+@[a-z0-9]([a-z0-9-]*[a-z0-9])?(\.[a-z0-9]([a-z0-9-]*[a-z0-9])?)*$/i ) {
		$self->last_error("Param NAME not valid: you can use letters, numbers, the underscore character or a valid email address!")
			&& return undef
	}
	
	$self->last_error("User ".$params{NAME}." already exists!")
		&& return undef
			if $self->test_user_name($params{NAME});
	
	my $gruppo_principale;
	my $gruppi_secondari;
	if (defined $params{GROUPS}){
		for my $group ($params{GROUPS}->{PRIMARY}, @{$params{GROUPS}->{SECONDARY}}) {
			$self->last_error("Group ".$group." does not exist!")
				&& return undef
					unless $self->test_group_name($group);
		}
		$gruppo_principale = $params{GROUPS}->{PRIMARY};
		$gruppi_secondari = $params{GROUPS}->{SECONDARY};
	} elsif (defined $params{ROLES} || defined $params{ROLE_ALIASES}){
		my $ruoli;
		if (defined $params{ROLES}){
			$ruoli = $params{ROLES};
		} else {
			my $result_role_from_aliases = $self->get_roles_from_role_aliases($params{ROLE_ALIASES});
			return undef
				unless defined $result_role_from_aliases;
			
			$self->last_error("No Aliases found!")
				&& return undef
					unless scalar @{$result_role_from_aliases};
			
			for (@{$result_role_from_aliases}){
				push @{$ruoli}, $_;
			}
		}
		$gruppi_secondari = [];
		for my $role (@{$ruoli}) {
			$self->last_error("Role ".$role." does not exist!")
				&& return undef
					unless $self->test_role_name($role);
			my $role_principale = $self->enum_role(EXTENDED_OUTPUT => 1, FILTER => {NAMES => [$role]})->[0];
			$gruppo_principale = $self->get_group_name($role_principale->{GROUPS}->[0]) unless defined $gruppo_principale;
			for my $gruppo_ruolo (@{$role_principale->{GROUPS}}){
				push @{$gruppi_secondari}, $self->get_group_name($gruppo_ruolo) unless grep {$_ eq $self->get_group_name($gruppo_ruolo)} @{$gruppi_secondari};
			}
		}

	}

	my $id_user = $self->_dbh()->fetch_minimalized("select seq_operatori.nextval from dual");
	
	my $password = defined $params{PASSWORD} ? Digest::MD5::md5_hex($params{PASSWORD}) : $params{MD5_PASSWORD};
	
	my $sql = "
		insert into OPERATORI (
                          id_operatore,
                          nome_operatore,
                          password_operatore,
                          login_operatore,
                          EMAIL,
                          SERVICE_USER,
                          COGNOME_OPERATORE,
                          MOBILE_PHONE,
                          NUMERO_LOGIN_CONSENTITI,
                          id_gruppo,
                          ID_OPERATORE_CREAZIONE,
						  ID_OPERATORE_VARIAZIONE
                    ) values (
                           $id_user
                           ,".$self->_dbh()->quote($params{FIRST_NAME})." --nome_operatore
                           ,".$self->_dbh()->quote($password)." --password_operatore
                           ,".$self->_dbh()->quote(uc($params{NAME}))." --login_operatore
                           ,".$self->_dbh()->quote($params{EMAIL})." --email_operatore
                           ,".(defined $params{SERVICE_USER} && $params{SERVICE_USER} == 1 ? $self->_dbh()->quote('S') : 'NULL')." --service_user
                           ,".$self->_dbh()->quote($params{LAST_NAME})." --cognome_operatore
                           ,".$self->_dbh()->quote($params{MOBILE_PHONE})." --mobile_phone
                           , 100 --numero_login_consentiti
                           , ".$self->get_group_id($gruppo_principale)." --id_gruppo
                           , ".$self->user()->id()." --ID_OPERATORE_CREAZIONE
						   , ".$self->user()->id()." --ID_OPERATORE_VARIAZIONE
                   )
	";
	
	eval {
		$self->_dbh()->do($sql);
	};
	$self->last_error("Error creating user!\n$@")
		&& return undef
			if $@;
	
	my $sql_groups = "
		insert into operatori_gruppi (id_operatore,id_gruppo,id_operatore_creazione)
		select ".$id_user.",".$self->get_group_id($gruppo_principale).",".$self->user()->id()." from dual
	";
	
	for my $group (@{$gruppi_secondari}) {
		$sql_groups .= "
			union select ".$id_user.",".$self->get_group_id($group).",".$self->user()->id()." from dual
		";
	}
	
	eval {
		$self->_dbh()->do($sql_groups);
	};
	$self->last_error("Error assigning user to groups!\n$@")
		&& return undef
			if $@;
	
	return undef unless $self->_refresh_lookup();
		
	return $id_user;
}

=head2 I<object>->B<update_user>( I<NAME> => <scalar>[, I<PASSWORD> => <scalar>, I<MD5_PASSWORD> => <scalar>, I<FIRST_NAME> => <scalar>, I<LAST_NAME> => <scalar>, I<GROUPS> => <hashref>, I<ROLES> => <arrayref>, I<ROLE_ALIASES> => <arrayref>, I<EMAIL> => <scalar>, I<MOBILE_PHONE> => <scalar>, I<SERVICE_USER> => <1|0>, I<DISABLED> => <1|0>] )

Aggiorna le info dell'utente dato il campo NAME. Ritorna 1 in caso di successo

B<NOTA>: funziona solo con gli utenti attivi

=item B<NAME> Login dell'utente

=item B<PASSWORD> Password dell'utente

=item B<MD5_PASSWORD> Password dell'utente in formato md5

=item I parametri B<PASSWORD> e B<MD5_PASSWORD> sono mutamente esclusivi.

=item B<FIRST_NAME> Nome dell'utente

=item B<LAST_NAME> Cognome dell'utente

=item B<EMAIL> Email dell'utente

=item B<MOBILE_PHONE> Mobile Phone dell'utente

=item B<SERVICE_USER> Default a 0. Se 1 imposta l'operatore come utente di servizio

=item B<DISABLED> Se 1 imposta l'operatore come disabilitato

=item B<GROUPS> Hash che contiene le seguenti chiavi

=over

=item B<PRIMARY> Gruppo primario (Stringa)

=item B<SECONDARY> Gruppi secondari (Array)

=back

=item B<ROLES> Array che contiene l'elenco dei ruoli

=item B<ROLE_ALIASES> Array che contiene l'elenco degli alias ai ruoli

=item I parametri B<ROLES>, B<ROLE_ALIASES> e B<GROUPS> sono mutamente esclusivi.

=cut

sub update_user {
	my $self = shift;
	
	local $@;
	$self->clear_last_error();
	
	my %params = @_;
	my $errmsg = '';
	
	$self->last_error("Only user admins can update_user!")
		&& return undef
			unless $self->user()->is_user_admin();
	
	$self->last_error($errmsg)
		&& return undef
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					 NAME		=> { isa => 'SCALAR' }
					
				}
				,OPTIONAL	=> {
					 PASSWORD		=> { isa => 'SCALAR' }
					,MD5_PASSWORD	=> { isa => 'SCALAR' }
					,FIRST_NAME		=> { isa => 'SCALAR' }
					,LAST_NAME		=> { isa => 'SCALAR' }
					,GROUPS			=> { isa => 'HASH' }
					,ROLES			=> { isa => 'ARRAY' }
					,ROLE_ALIASES	=> { isa => 'ARRAY' }
					,SERVICE_USER	=> { isa => 'SCALAR', list => [1,0] }
					,EMAIL			=> { isa => 'SCALAR' }
					,MOBILE_PHONE	=> { isa => 'SCALAR' }
					,DISABLED		=> { isa => 'SCALAR', list => [1] }
				}
				,IGNORE_EXTRA_PARAMS	=> 0
			);
	
	my $count_pwd_param = 0;
	for my $c_p ('PASSWORD', 'MD5_PASSWORD'){
		$count_pwd_param++ if defined $params{$c_p};
	}
	
	$self->last_error("You can set only one between PASSWORD and MD5_PASSWORD params!")
		&& return undef
			if $count_pwd_param > 1;
	
	$self->last_error("User ".$params{NAME}." doesn't exists!")
		&& return undef
			unless $self->test_user_name($params{NAME});
	
	my $gruppo_principale;
	my $gruppi_secondari = [];
	
	if (exists $params{GROUPS} || exists $params{ROLES} || exists $params{ROLE_ALIASES}){
		
		my $count_param = 0;
		for my $c_p ('GROUPS', 'ROLES', 'ROLE_ALIASES'){
			$count_param++ if defined $params{$c_p};
		}
		
		$self->last_error("You must set only one between GROUPS, ROLES and ROLE_ALIASES param!")
			&& return undef
				if $count_param != 1;
		
		my @check_group = ();
		if (defined $params{GROUPS}){
			$self->last_error($errmsg)
				&& return undef
					unless $self->check_named_params(
						 ERRMSG		=> \$errmsg
						,PARAMS		=> $params{GROUPS}
						,MANDATORY	=> {}
						,OPTIONAL	=> {
							PRIMARY	=> { isa => 'SCALAR' }
							,SECONDARY	=> { isa => 'ARRAY' }
						}
						,IGNORE_EXTRA_PARAMS	=> 0
					);
			push @check_group, $params{GROUPS}->{PRIMARY} if exists $params{GROUPS}->{PRIMARY};
			push @check_group, @{$params{GROUPS}->{SECONDARY}} if exists $params{GROUPS}->{SECONDARY};
		} elsif (defined $params{ROLES}){
			@check_group = @{$params{ROLES}};
		} else {
			my $result_role_from_aliases = $self->_dbh()->fetchall_arrayref("
				select distinct r.nome from ruoli_alias ra
				  join ruoli r on r.id_ruolo = ra.id_ruolo
				where ra.alias in (
					".join (',', (map {$self->_dbh()->quote($_)} @{$params{ROLE_ALIASES}}))."
				)
				and ra.data_disabilitazione is null
			");
			
			$self->last_error("No Aliases found!")
				&& return undef
					unless scalar @{$result_role_from_aliases};
			
			for (@{$result_role_from_aliases}){
				push @check_group, $_->[0];
			}
		}
			
		for my $group (@check_group) {
			if (defined $params{GROUPS}){
				$self->last_error("Group ".$group." does not exist!")
					&& return undef
						unless $self->test_group_name($group);
			} else {
				$self->last_error("Roles ".$group." does not exist!")
					&& return undef
						unless $self->test_role_name($group);
			}
		}
		
		if (defined $params{GROUPS}){
			$gruppo_principale = $params{GROUPS}->{PRIMARY};
			$gruppi_secondari = $params{GROUPS}->{SECONDARY} if defined $params{GROUPS}->{SECONDARY};
			#in ogni caso metto anche il gruppo PRIMARIO se non c'è in quanto per ART è sempre anche SECONDARIO e mi serve per cancellare la operatori_gruppi
			push @{$gruppi_secondari}, $gruppo_principale unless grep {$_ eq $gruppo_principale} @{$gruppi_secondari};
		} else {
			for my $role (@check_group) {
				$self->last_error("Role ".$role." does not exist!")
					&& return undef
						unless $self->test_role_name($role);
				my $role_principale = $self->enum_role(EXTENDED_OUTPUT => 1, FILTER => {NAMES => [$role]})->[0];
				$gruppo_principale = $self->get_group_name($role_principale->{GROUPS}->[0]) unless defined $gruppo_principale;
				for my $gruppo_ruolo (@{$role_principale->{GROUPS}}){
					push @{$gruppi_secondari}, $self->get_group_name($gruppo_ruolo) unless grep {$_ eq $self->get_group_name($gruppo_ruolo)} @{$gruppi_secondari};
				}
			}
		}

	}
	
	my $id_user = $self->_dbh()->fetch_minimalized("select id_operatore from operatori where login_operatore = ".$self->_dbh()->quote($params{NAME}));
	
	my $password =
		defined $params{PASSWORD}		? ',password_operatore = '.$self->_dbh()->quote(Digest::MD5::md5_hex($params{PASSWORD}))	:
		defined $params{MD5_PASSWORD}	? ',password_operatore = '.$self->_dbh()->quote($params{MD5_PASSWORD})						:
		''
	;
	
	my $sql;
	if (defined $params{DISABLED}){
		# FIXME: gestione da rivedere in modo più approfondto
		# # per policy GDPR vengono valorizzate a '######' alcune colonne con dati personali
		# my $offuscamento = '######';
		# $sql = "
		# 	update operatori
		# 	set login_operatore = ".$self->_dbh()->quote($params{NAME})."
		# 		, password_operatore = ".$self->_dbh()->quote($offuscamento)."
		# 		, nome_operatore = ".$self->_dbh()->quote($offuscamento)."
		# 		, cognome_operatore = ".$self->_dbh()->quote($offuscamento)."
		# 		".(defined $gruppo_principale ? ',id_gruppo = '.$self->get_group_id($gruppo_principale) : '')."
		# 		".(
		# 			defined $params{SERVICE_USER} 
		# 				? ($params{SERVICE_USER} == 1 
		# 					? ',service_user = \'S\''
		# 					: ',service_user = null'
		# 				)
		# 				: '')."
		# 		, email = ".$self->_dbh()->quote($offuscamento)."
		# 		, mobile_phone = ".$self->_dbh()->quote($offuscamento)."
		# 		, fax = ".$self->_dbh()->quote($offuscamento)." -- NB: per ora solo impostata poi vedremo come utilizzarla
		# 		, morto = 'S'
		# 	where id_operatore = ".$id_user
		# ;
		$sql = "
			update operatori
			set login_operatore = ".$self->_dbh()->quote($params{NAME})."
				".$password."
				".(defined $params{FIRST_NAME} ? ',nome_operatore = '.$self->_dbh()->quote($params{FIRST_NAME}) : '')."
				".(defined $params{LAST_NAME} ? ',cognome_operatore = '.$self->_dbh()->quote($params{LAST_NAME}) : '')."
				".(defined $gruppo_principale ? ',id_gruppo = '.$self->get_group_id($gruppo_principale) : '')."
				".(
					defined $params{SERVICE_USER} 
						? ($params{SERVICE_USER} == 1 
							? ',service_user = \'S\''
							: ',service_user = null'
						)
						: '')."
				".(defined $params{EMAIL} ? ',email = '.$self->_dbh()->quote($params{EMAIL}) : '')."
				".(defined $params{MOBILE_PHONE} ? ',mobile_phone = '.$self->_dbh()->quote($params{MOBILE_PHONE}) : '')."
				, ID_VARIAZIONE = seq_operatori_id_variazione.nextval
				, ID_OPERATORE_VARIAZIONE = ".$self->user()->id()."
				, DATA_VARIAZIONE = systimestamp
				, ID_OPERATORE_DISMISSIONE = ".$self->user()->id()."
				, DATA_DISMISSIONE = systimestamp
				, morto = 'S'
			where id_operatore = ".$id_user
		;
	} else {
		$sql = "
			update operatori
			set login_operatore = ".$self->_dbh()->quote($params{NAME})."
				".$password."
				".(defined $params{FIRST_NAME} ? ',nome_operatore = '.$self->_dbh()->quote($params{FIRST_NAME}) : '')."
				".(defined $params{LAST_NAME} ? ',cognome_operatore = '.$self->_dbh()->quote($params{LAST_NAME}) : '')."
				".(defined $gruppo_principale ? ',id_gruppo = '.$self->get_group_id($gruppo_principale) : '')."
				".(
					defined $params{SERVICE_USER} 
						? ($params{SERVICE_USER} == 1 
							? ',service_user = \'S\''
							: ',service_user = null'
						)
						: '')."
				".(defined $params{EMAIL} ? ',email = '.$self->_dbh()->quote($params{EMAIL}) : '')."
				".(defined $params{MOBILE_PHONE} ? ',mobile_phone = '.$self->_dbh()->quote($params{MOBILE_PHONE}) : '')."
				, ID_VARIAZIONE = seq_operatori_id_variazione.nextval
				, ID_OPERATORE_VARIAZIONE = ".$self->user()->id()."
				, DATA_VARIAZIONE = systimestamp
				, ID_OPERATORE_DISMISSIONE = null
				, DATA_DISMISSIONE = null
				, morto = null
			where id_operatore = ".$id_user
		;
	}
	
	eval {
		$self->_dbh()->do($sql);
	};
	$self->last_error("Error creating user!\n$@")
		&& return undef
			if $@;
	
	if (scalar @{$gruppi_secondari}){
		eval {
			$self->_dbh()->do("delete operatori_gruppi where id_operatore = ".$id_user);
		};
		$self->last_error("Error assigning user to groups!\n$@")
			&& return undef
				if $@;
		
		my $sql_groups = "
			insert into operatori_gruppi (id_operatore,id_gruppo,id_operatore_creazione)
			select ".$id_user.",(select id_gruppo from operatori where id_operatore = ".$id_user."),".$self->user()->id()." from dual
		";
		
		for my $group (@{$gruppi_secondari}) {
			$sql_groups .= "
				union select ".$id_user.",".$self->get_group_id($group).",".$self->user()->id()." from dual
			";
		}
		
		eval {
			$self->_dbh()->do($sql_groups);
		};
		$self->last_error("Error assigning user to groups!\n$@")
			&& return undef
				if $@;
	}
	return undef unless $self->_refresh_lookup();
		
	return 1;
}

=head2 I<object>->B<add_user_groups>( I<NAME> => <scalar>, I<GROUPS> => <array> )

Aggiunge i gruppi all'utente

B<NOTA>: funziona solo con gli utenti attivi

=item B<NAME> Login dell'utente

=item B<GROUPS> Nomi dei gruppi da aggiungere

=cut

sub add_user_groups {
	my $self = shift;
	
	local $@;
	$self->clear_last_error();
	
	my %params = @_;
	my $errmsg = '';
	
	$self->last_error("Only user admins can add_user_groups!")
		&& return undef
			unless $self->user()->is_user_admin();
	
	$self->last_error($errmsg)
		&& return undef
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					 NAME		=> { isa => 'SCALAR' },
					 GROUPS		=> { isa => 'ARRAY' },
					
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS	=> 0
			);
	
	my $sub_unique = sub (@){
		return keys %{ {map { $_ => undef } @_}}; 
	};

	my @unique_groups = $sub_unique->(@{$params{GROUPS}});

	if (scalar @unique_groups != scalar @{$params{GROUPS}}){
		$self->last_error("Duplicated group present in GROUPS param");
		return undef;
	}

	unless ($self->test_user_name($params{NAME})){
		$self->last_error("Unknown user ".$params{NAME});
		return undef;
	}

	my $id_user = $self->get_user_id($params{NAME});

	my $user_info = $self->get_user_info($id_user);
	return undef unless defined $user_info;

	$self->_dbh()->do("savepoint API__ART__add_user_groups");

	for my $group ( @{$params{GROUPS}} ) {
		my $id_group = $self->get_group_id($group);

		# verifico se l'utente ha già il gruppo
		$self->last_error("Group already present for user")
			&& $self->_dbh()->do("rollback to savepoint API__ART__add_user_groups")
			&& return undef
				if(grep {$id_group eq $_}@{$user_info->{GROUPS}});

		my $sql_groups = "
			insert into operatori_gruppi (id_operatore,id_gruppo,id_operatore_creazione)
			select ".$id_user.", ".$id_group.",".$self->user()->id()." from dual
		";
			
		eval {
			$self->_dbh()->do($sql_groups);
		};
		$self->last_error("Error assigning group to user!\n$@")
			&& $self->_dbh()->do("rollback to savepoint API__ART__add_user_groups")
			&& return undef
				if $@;
	}

	$self->_dbh()->do("rollback to savepoint API__ART__add_user_groups")
		&& return undef unless $self->_refresh_lookup();
		
	return 1;
}

=head2 I<object>->B<remove_user_groups>( I<NAME> => <scalar>, I<GROUPS> => <array> )

Rimuove i gruppi dall'utente

B<NOTA>: funziona solo con gli utenti attivi
B<NOTA>: non rimuove l'ultimo gruppo dell'utente

=item B<NAME> Login dell'utente

=item B<GROUPS> Nomi dei gruppi da rimuovere

=cut

sub remove_user_groups {
	my $self = shift;
	
	local $@;
	$self->clear_last_error();
	
	my %params = @_;
	my $errmsg = '';
	
	$self->last_error("Only user admins can remove_user_groups!")
		&& return undef
			unless $self->user()->is_user_admin();
	
	$self->last_error($errmsg)
		&& return undef
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					 NAME		=> { isa => 'SCALAR' },
					 GROUPS		=> { isa => 'ARRAY' },
					
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS	=> 0
			);
	
	my $sub_unique = sub (@){
		return keys %{ {map { $_ => undef } @_}}; 
	};

	my @unique_groups = $sub_unique->(@{$params{GROUPS}});

	if (scalar @unique_groups != scalar @{$params{GROUPS}}){
		$self->last_error("Duplicated group present in GROUPS param");
		return undef;
	}

	unless ($self->test_user_name($params{NAME})){
		$self->last_error("Unknown user ".$params{NAME});
		return undef;
	}

	my $id_user = $self->get_user_id($params{NAME});

	my $user_info = $self->get_user_info($id_user);
	return undef unless defined $user_info;

	$self->_dbh()->do("savepoint API__ART__remove_user_groups");

	for my $group ( @{$params{GROUPS}} ) {

		my $id_group = $self->get_group_id($group);

		# verifico se l'utente ha il gruppo
		my $user_info = $self->get_user_info($id_user);
		$self->_dbh()->do("rollback to savepoint API__ART__remove_user_groups")
			&& return undef unless defined $user_info;

		$self->last_error("Group not present for user")
			&& $self->_dbh()->do("rollback to savepoint API__ART__remove_user_groups")
			&& return undef
				unless (grep {$id_group eq $_}@{$user_info->{GROUPS}});

		if ( @{$user_info->{GROUPS}} < 2 ) {
			$self->last_error('Cannot remove last group from user');
			$self->_dbh()->do("rollback to savepoint API__ART__remove_user_groups");
			return undef;
		}

		my @keep_groups = grep { $id_group != $_ } @{$user_info->{GROUPS}};

		my $sql_groups = sprintf q{delete from operatori_gruppi where id_operatore = %d and id_gruppo = %d},
			$id_user,
			$id_group,
		;

		eval {
			$self->_dbh()->do($sql_groups);
		};
		$self->last_error("Error deleting group from user!\n$@")
			&& $self->_dbh()->do("rollback to savepoint API__ART__remove_user_groups")
			&& return undef
				if $@;

		my $sql = sprintf q{update operatori set id_gruppo = %d where id_operatore = %d}, $keep_groups[0], $id_user;
		eval {
			$self->_dbh()->do($sql);
		};
		$self->last_error("Error deleting group from user!\n$@")
			&& $self->_dbh()->do("rollback to savepoint API__ART__remove_user_groups")
			&& return undef
				if $@;
	}

	$self->_dbh()->do("rollback to savepoint API__ART__remove_user_groups")
		&& return undef unless $self->_refresh_lookup();
		
	return 1;
}

=head2 I<object>->B<add_user_roles>( I<NAME> => <scalar>, I<ROLES> => <array> )

Aggiunge i ruoli all'utente

B<NOTA>: funziona solo con gli utenti attivi

=item B<NAME> Login dell'utente

=item B<ROLES> Nome dei ruoli da aggiungere

=cut

sub add_user_roles {
	my $self = shift;
	
	local $@;
	$self->clear_last_error();
	
	my %params = @_;
	my $errmsg = '';
	
	$self->last_error("Only user admins can add_user_roles!")
		&& return undef
			unless $self->user()->is_user_admin();
	
	$self->last_error($errmsg)
		&& return undef
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					 NAME		=> { isa => 'SCALAR' },
					 ROLES		=> { isa => 'ARRAY' },
					
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS	=> 0
			);
	
	my $id_user = $self->get_user_id($params{NAME});

	$self->_dbh()->do("savepoint API__ART__add_user_roles");

	for my $role ( @{$params{ROLES}} ) {
	
		my $id_role = $self->get_role_id($role);

		my $role_groups = $self->get_groups_from_roles( [ $role ] );
		
		# verifico se l'utente ha già il gruppo
		my $user_info = $self->get_user_info($id_user);
		$self->_dbh()->do("rollback to savepoint API__ART__add_user_roles")
			&& return undef unless defined $user_info;

		$self->last_error("Role already present for user")
			&& $self->_dbh()->do("rollback to savepoint API__ART__add_user_roles")
			&& return undef
				if(grep {$id_role eq $_}@{$user_info->{ROLES}});

		my $user_groups = $user_info->{GROUPS};


		my $sql_groups = sprintf qq{
			insert into operatori_gruppi (id_operatore,id_gruppo,id_operatore_creazione)
			select * from (
				(
					%s
				)
				MINUS
				(
					%s
				)
			)
		},
			join( qq{\n        UNION }, map { sprintf q{select %d, %d, %d from dual}, $id_user, $self->get_group_id($_), $self->user()->id() } @{$role_groups} ),
			join( qq{\n        UNION }, map { sprintf q{select %d, %d, %d from dual}, $id_user, $_, $self->user()->id() } @{$user_groups} )
		;
			
		eval {
			$self->_dbh()->do($sql_groups);
		};
		$self->last_error("Error assigning role to user!\n$@")
			&& $self->_dbh()->do("rollback to savepoint API__ART__add_user_roles")
			&& return undef
				if $@;

	}

	$self->_dbh()->do("rollback to savepoint API__ART__add_user_roles")
		&& return undef unless $self->_refresh_lookup();
		
	return 1;
}

=head2 I<object>->B<remove_user_roles>( I<NAME> => <scalar>, I<ROLES> => <array> )

Rimuove i ruoli dall'utente

B<NOTA>: funziona solo con gli utenti attivi
B<NOTA>: non rimuove l'ultimo ruolo dell'utente

=item B<NAME> Login dell'utente

=item B<ROLES> Nomi dei ruoli da rimuovere

=cut


sub remove_user_roles {
	my $self = shift;
	
	local $@;
	$self->clear_last_error();
	
	my %params = @_;
	my $errmsg = '';
	
	$self->last_error("Only user admins can add_user_roles!")
		&& return undef
			unless $self->user()->is_user_admin();
	
	$self->last_error($errmsg)
		&& return undef
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					 NAME		=> { isa => 'SCALAR' },
					 ROLES		=> { isa => 'ARRAY' },
					
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS	=> 0
			);
	
	my $id_user = $self->get_user_id($params{NAME});

	$self->_dbh()->do("savepoint API__ART__remove_user_roles");

	for my $role ( @{$params{ROLES}} ) {

		my $id_role = $self->get_role_id($role);

		my $role_groups = $self->get_groups_from_roles( [ $role ] );
		
		# verifico se l'utente ha già il gruppo
		my $user_info = $self->get_user_info($id_user);
		$self->_dbh()->do("rollback to savepoint API__ART__remove_user_roles")
			&& return undef unless defined $user_info;

		$self->last_error("Role not present for user")
			&& $self->_dbh()->do("rollback to savepoint API__ART__remove_user_roles")
			&& return undef
				unless (grep {$id_role eq $_}@{$user_info->{ROLES}});

		my $user_groups = $user_info->{GROUPS};
		my $user_roles  = $user_info->{ROLES};
	
		if ( @{$user_roles} < 2 ) {
			$self->last_error('Cannot remove last role from user');
			$self->_dbh()->do("rollback to savepoint API__ART__remove_user_roles");
			return undef;
		}


		my $keep_groups = $self->get_groups_from_roles( [ map { $self->get_role_name($_) } grep {$_ != $id_role} @{$user_roles} ] );
		$self->_dbh()->do("rollback to savepoint API__ART__remove_user_roles")
			&& return undef unless defined $keep_groups;

		my $sql_groups = sprintf qq{
			delete from operatori_gruppi
			where (id_operatore, id_gruppo) in (
				select * from (
					(
						%s
					)
					MINUS
					(
						%s
					)
				)
			)
		},
			join( qq{\n        UNION }, map { sprintf q{select %d, %d from dual}, $id_user, $self->get_group_id($_) } @{$role_groups} ),
			join( qq{\n        UNION }, map { sprintf q{select %d, %d from dual}, $id_user, $self->get_group_id($_) } @{$keep_groups} )
		;
			
		eval {
			$self->_dbh()->do($sql_groups);
		};
		$self->last_error("Error removing role from user!\n$@")
			&& $self->_dbh()->do("rollback to savepoint API__ART__remove_user_roles")
			&& return undef
				if $@;

		my $sql = sprintf q{update operatori set id_gruppo = %d where id_operatore = %d}, $self->get_group_id($keep_groups->[0]), $id_user;
		eval {
			$self->_dbh()->do($sql);
		};
		$self->last_error("Error deleting group from user!\n$@")
			&& $self->_dbh()->do("rollback to savepoint API__ART__remove_user_roles")
			&& return undef
				if $@;

	}

	$self->_dbh()->do("rollback to savepoint API__ART__remove_user_roles")
		&& return undef unless $self->_refresh_lookup();
		
	return 1;
}


=head2 I<object>->B<restore_user>( I<NAME> => <scalar> )

Ripristina un utente disabilitato. Ritorna 1 in caso di successo

=item B<NAME> Login dell'utente

=cut

sub restore_user {
	my $self = shift;

	local $@;
	$self->clear_last_error();

	my %params = @_;
	my $errmsg = '';

	$self->last_error("Only user admins can restore_user!")
		&& return undef
			unless $self->user()->is_user_admin();

	$self->last_error($errmsg)
		&& return undef
			unless $self->check_named_params(
				 ERRMSG	 => \$errmsg
				,PARAMS	 => \%params
				,MANDATORY      => {
					 NAME	   => { isa => 'SCALAR' }
				}
				,OPTIONAL       => {}
				,IGNORE_EXTRA_PARAMS    => 0
			);
	my $user = $self->enum_user(FILTER => {NAME => $params{NAME}, DISABLED => 1});
	
	return undef unless defined $user;

	$self->last_error("User not present or not disabled")
		&& return undef
			unless exists $user->{$params{NAME}};
	
	# facciamo l'update direttamente perche' il metodo update_user gestisce solo le utenze attive 
	# in quanto la lookup art restituisce solo queste utenze	
	eval {
		$self->_dbh()->do("
			update operatori
			set morto = null
				, ID_VARIAZIONE = seq_operatori_id_variazione.nextval
				, ID_OPERATORE_VARIAZIONE = ".$self->user()->id()."
				, DATA_VARIAZIONE = systimestamp
				, ID_OPERATORE_DISMISSIONE = null
				, DATA_DISMISSIONE = null
			where login_operatore = ".$self->_dbh()->quote($params{NAME})
		);
	};
	$self->last_error("Error restoring user!\n$@")
		&& return undef
			if $@;
	return undef unless $self->_refresh_lookup();
	return 1;
	
}

=head2 I<object>->B<format_date>( I<DATE> , I<DATE_FORMAT> )

Ritorna uno I<SCALAR> che rappresenta  I<DATE> formattato secondo il formato di default API::ART->info('DEFAULT_DATE_FORMAT');
I<DATE> dovra' essere conforme al formato descritto in I<DATE_FORMAT>.

=cut
sub format_date {
	my ($self, $date, $format) = @_;
	return $self->_dbh()->fetch_minimalized(qq/
		select	 to_char(to_date('$date', '$format')
				,'${\$self->info('DEFAULT_DATE_FORMAT')}')
		from	dual
	/);
}

=head2 I<object>->B<get_default_date_format>()

Ritorna il <DEFAULT_DATE_FORMAT>.

=cut

sub get_default_date_format {
	my $self = shift;
	return $self->{"INFO"}->{DEFAULT_DATE_FORMAT};
}

=head2 I<object>->B<get_default_iso_date_format_oracle>()

Ritorna il <DEFAULT_ISO_DATE_FORMAT_ORACLE>.

=cut

sub get_default_iso_date_format_oracle {
	my $self = shift;
	return $self->{"INFO"}->{DEFAULT_ISO_DATE_FORMAT_ORACLE};
}

=head2 I<object>->B<get_rfc7232_date_format_oracle>()

Ritorna la maschera Oracle per formattare una data in formato I<RFC7232_DATE_FORMAT>. Questo formato è utilizzato negli header HTTP (giorno e mese in inglese abbreviato case-sensitive e sempre localizzata GMT)

=cut

sub get_rfc7232_date_format_oracle {
	return 'Dy, DD Mon YYYY hh24:mi:ss "GMT"';
} 

=head2 I<object>->B<set_default_date_format>( I<DATE_FORMAT> )

Imposta il parametro DEFAULT_DATE_FORMAT.
Ritorna 1 se l'esito e' ok o undef impostando last_error() se ko.

=cut

sub set_default_date_format {
	my $self = shift;
	my $format = shift;
	$self->last_error("DEFAULT_DATE_FORMAT missing!") && return 0 unless $format;
	unless ( eval {$self->{'DB'}->fetch_minimalized("select to_char(sysdate,".$self->{'DB'}->quote($format).") from dual") }) {
		$self->last_error("ERROR! DEFAULT_DATE_FORMAT '$format' not valid!");
		return 0;
	}
	$self->{"INFO"}->{DEFAULT_DATE_FORMAT} = $format;
	return 1;
}

=head2 I<object>->B<test_date>( I<DATE> , I<DATE_FORMAT> )

Consente di testare la validita' di una data I<DATE> secondo il formato I<DATE_FORMAT>.
Se I<DATE_FORMAT> non viene specificato, utilizza il formato di default API::ART->info('DEFAULT_DATE_FORMAT').
Ritorna 1 se la data e' valida o undef impostando last_error() se la data non e' valida.

=cut
sub test_date {
	my ($self, $date, $format) = @_;
	$format = $self->get_default_date_format unless $format;
	my $result = $self->_dbh()->test_date($date, $format);
	if ( $result ) {
		$self->last_error($result);
		return 0;
	}
	return 1;
}

=head2 I<object>->B<get_sysdate>( I<DATE_FORMAT> )

Ritorna la sysdate nel formato I<DATE_FORMAT>.
Se I<DATE_FORMAT> non viene specificato, ritorna la sysdate nel formato di default API::ART->info('DEFAULT_DATE_FORMAT').

=cut
sub get_sysdate {
	shift->_dbh()->get_sysdate(@_)
}

=head2 I<object>->B<format_iso_date>( I<DATE> )

Formatta una data ISODATE nel formato gestibile da Oracle (vedi metodo I<get_default_iso_date_format_oracle>)

=cut

sub format_iso_date {
	my ($self, $date) = @_;
	
	my $dt = eval{DateTime::Format::ISO8601->parse_datetime( $date )};
	$self->last_error("Not a valid ISO Date: ".$date)
		&& return undef
			if ($@);
	
	my $offset = $dt->set_time_zone($self->{'INFO'}->{DEFAULT_TIME_ZONE})->offset(); # 7200
	my ($hour, $min) = (int($offset/(60*60)), int($offset%(60*60)));

	return $dt->set_time_zone($self->{'INFO'}->{DEFAULT_TIME_ZONE})->set_formatter($self->{'INFO'}->{DEFAULT_ISO_DATE_FORMAT}).sprintf("%+03d:%02d", $hour, $min);
}

=head2 I<object>->B<get_date_from_iso_date>( I<DATE>, I<DATE_FORMAT> )

Converte una data ISODATE nel formato DEFAULT_DATE_FORMAT oppure, se indicato, nel formato I<DATE_FORMAT>.

=cut

sub get_date_from_iso_date {
	my ($self, $date, $date_format) = @_;
	
	my $iso_date = $self->format_iso_date($date);
	
	return undef unless $iso_date;
	
	my $query = "select to_char(to_timestamp_tz(".$self->_dbh()->quote($iso_date).",".$self->_dbh()->quote($self->get_default_iso_date_format_oracle()).") at time zone ".$self->_dbh()->quote($self->{'INFO'}->{DEFAULT_TIME_ZONE}).",".$self->_dbh()->quote((defined $date_format ? $date_format : $self->get_default_date_format())).") from dual";
	
	return $self->_dbh()->fetch_minimalized($query);
	
}

=head2 I<object>->B<get_iso_date_from_date>( I<DATE>, I<ISO_DATE_FORMAT> )

Converte una data DATE nel formato DEFAULT_ISO_DATE_FORMAT_ORACLE oppure, se indicato, nel formato I<ISO_DATE_FORMAT>.

=cut

sub get_iso_date_from_date {
	my ($self, $date, $iso_date_format) = @_;
	
	$iso_date_format = $self->get_default_iso_date_format_oracle()
		unless defined $iso_date_format;

	my $query = "
		select
			TO_CHAR(FROM_TZ(to_timestamp(".$self->_dbh()->quote($date).", ".$self->_dbh()->quote($self->get_default_date_format())."), ".$self->_dbh()->quote($self->{'INFO'}->{DEFAULT_TIME_ZONE})."), ".$self->_dbh()->quote($iso_date_format).")
		from dual
	";

	return $self->_dbh()->fetch_minimalized($query);
}

=head2 I<object>->B<cmp_iso_date>( I<FIRST_DATE>, I<SECOND_DATE> )

Confronta due date di tipo ISODATE gestibile da Oracle (vedi metodo I<get_default_iso_date_format_oracle>) ritornando 1 se la I<FIRST_DATE> > I<SECOND_DATE>, 0 se la I<FIRST_DATE> = I<SECOND_DATE>, -1 se la I<FIRST_DATE> < I<SECOND_DATE>

=cut

sub cmp_iso_date {
	my $self = shift;
	my $date1 = shift;
	my $date2 = shift;
	
	$date1 = $self->format_iso_date($date1);
	return undef unless $date1;
	
	$date2 = $self->format_iso_date($date2);
	return undef unless $date2;
	
	my $res = $self->_dbh()->fetch_minimalized("select CMP_ISODATE_STRING(".$self->_dbh()->quote($date1).",".$self->_dbh()->quote($date2).",".$self->_dbh()->quote($self->get_default_iso_date_format_oracle()).") from dual");
	
	unless (defined $res){
		$self->last_error('Invalid ISODate');
		return undef;
	};
	
	return $res;
}

=head2 I<object>->B<get_rfc7232_date_from_date>( I<DATE> )

Converte una data DATE nel formato I<RFC7232_DATE_FORMAT>.

Esempio: Fri, 03 Nov 2017 14:33:00 GMT

=cut

sub get_rfc7232_date_from_date {
	my ($self, $date) = @_;
	
	# recupero l'NLS_DATE_LANGUAGE per poterlo ripristinare dopo la query
	my $nls_date_language = $self->_dbh()->fetch_minimalized("select value from nls_session_parameters where parameter='NLS_DATE_LANGUAGE'");
	
	# eseguiamo l'alter session per vare giorno e mese in americano come da RFC
	$self->_dbh()->do("ALTER SESSION SET NLS_DATE_LANGUAGE= 'AMERICAN'");
	
	my $query = "
		select
			TO_CHAR(FROM_TZ(to_timestamp(".$self->_dbh()->quote($date).", ".$self->_dbh()->quote($self->get_default_date_format())."), ".$self->_dbh()->quote($self->{'INFO'}->{DEFAULT_TIME_ZONE}).") AT TIME ZONE '+00:00', ".$self->_dbh()->quote($self->get_rfc7232_date_format_oracle()).")
		from dual
	";
	
	my $rfc7232_date = $self->_dbh()->fetch_minimalized($query);
	
	# ripristino l'NLS_DATE_LANGUAGE
	$self->_dbh()->do("ALTER SESSION SET NLS_DATE_LANGUAGE= ".$self->_dbh()->quote($nls_date_language));
	
	return $rfc7232_date;
}

=head2 I<object>->B<get_date_from_rfc7232_date>( I<RFC7232_DATE_FORMAT> )

Converte una data RFC7232_DATE_FORMAT nel formato DATE.

=cut

sub get_date_from_rfc7232_date {
	my ($self, $rfc7232_date) = @_;
	
	# recupero l'NLS_DATE_LANGUAGE per poterlo ripristinare dopo la query
	my $nls_date_language = $self->_dbh()->fetch_minimalized("select value from nls_session_parameters where parameter='NLS_DATE_LANGUAGE'");
	
	# eseguiamo l'alter session per vare giorno e mese in americano come da RFC
	$self->_dbh()->do("ALTER SESSION SET NLS_DATE_LANGUAGE= 'AMERICAN'");
	
	my $query = "
		SELECT 
			to_char(FROM_TZ(
				CAST(
					TO_DATE(".$self->_dbh()->quote($rfc7232_date).",".$self->_dbh()->quote($self->get_rfc7232_date_format_oracle()).") AS TIMESTAMP
				), '+00:00'
			) AT TIME ZONE ".$self->_dbh()->quote($self->{'INFO'}->{DEFAULT_TIME_ZONE})." 
			,".$self->_dbh()->quote($self->get_default_date_format()).")
		FROM DUAL
	";
	
	my $date = $self->_dbh()->fetch_minimalized($query);
	
	# ripristino l'NLS_DATE_LANGUAGE
	$self->_dbh()->do("ALTER SESSION SET NLS_DATE_LANGUAGE= ".$self->_dbh()->quote($nls_date_language));
	
	return $date;
}

=head2 I<object>->B<get_instance_by_class_name>( I<CLASS_NAME>, [@PARAMS] )

Prova ad istanziare la classe $CLASS_NAME->new(@PARAMS) se esiste.

Nel caso in cui la classe non dovesse esistere non solleva nessuna eccezione.

Ritorna un hashref con i seguenti parametri:

=item B<STATUS> OK se la classe esiste oppure se non esiste, KO se la classe presenta degli errori di compilazione

=item B<MESSAGE> valorizzato con il motivo dell'errore (STATUS = KO)

=item B<HANDLER> oggetto della classe $CLASS_NAME istanziato (se la classe esiste)

=cut

sub get_instance_by_class_name{
	my $self = shift;
	my $class_name = shift;
	return $self->_get_or_test_instance_by_class_name($class_name, 'GET', @_);
}

=head2 I<object>->B<test_instance_by_class_name>( I<CLASS_NAME>, [@PARAMS] )

Verifica la presenza della classe $CLASS_NAME se esiste.

Nel caso in cui la classe non dovesse esistere non solleva nessuna eccezione.

Ritorna un hashref con i seguenti parametri:

=item B<STATUS> OK se la classe esiste oppure se non esiste, KO se la classe presenta degli errori di compilazione

=item B<MESSAGE> valorizzato con il motivo dell'errore (STATUS = KO)

=item B<HANDLER> 1 se la classe e' stata trovata

=cut

sub test_instance_by_class_name{
	my $self = shift;
	my $class_name = shift;
	return $self->_get_or_test_instance_by_class_name($class_name, 'FIND', @_);
}

# metodo privato
sub _get_or_test_instance_by_class_name{
	my $self = shift;
	
	my $class_name = shift;
	my $get_object = shift;
	my @params = @_;
	
	my $out;
	
	unless (defined $class_name){
		$self->last_error("missing class_name");
		return;
	}
	
	my $error = do {
		local $@;
		eval "require $class_name;";
		$@;
	};
	if ($error) {
		if ($error =~ m/^Can't locate /) {
			# classe non trovata
			$out = {
				STATUS 	=> 'OK',
			};
		} else {
			# errore nella classe
			$out = {
				STATUS 	=> 'KO',
				MESSAGE	=> "binding actions: $error",
			}
		}
	} else {
		$out = {
			STATUS 	=> 'OK',
			HANDLER => $get_object eq 'GET' ? $class_name->new(@params) : 1
		};
	}
	
	return $out;
}

=head2 I<object>->B<get_activity_binding_class_name>( I<ACTIVITY_TYPE_NAME>, [I<BINDING_TYPE>, I<BINDING_NAME>]  )

Costruisce il nome della classe di binding per le Activity.

=item B<ACTIVITY_TYPE_NAME> Nome tipo attivita'

=item B<BINDING_TYPE> Tipo di binding (es. Status, Action)

=item B<BINDING_NAME> Nome del binding (es. nome di uno stato, nome di un'azione))

=cut
sub get_activity_binding_class_name{
	my ($self, $activity_type_name, $binding_type, $binding_name) = @_;
	
	$self->get_object_binding_class_name(
		'Activity', $activity_type_name, $binding_type, $binding_name
	);
}

=head2 I<object>->B<get_object_binding_class_name>(  I<OBJECT_NAME>, I<ACTIVITY_TYPE_NAME>, [I<BINDING_TYPE>, I<BINDING_NAME>]  )

Costruisce il nome della classe di binding per le Activity.

=item B<ACTIVITY_TYPE_NAME> Nome oggetto su cui applicare il Binding (es. Activity, System)

=item B<ACTIVITY_TYPE_NAME> Nome tipo oggetto

=item B<BINDING_TYPE> Tipo di binding

=item B<BINDING_NAME> Nome del binding

=cut

sub get_object_binding_class_name{
	my ($self, $object_name, $activity_type_name, $binding_type, $binding_name) = @_;
	
	#---------------------------------------------------------------------------------------
	# il nome della classe da rimappare sara':
	#
	# $APP_BASE::$object_name::$activity_type_name
	# (con $binding_type e $binding_name nulli)
	# es. $APP_BAE::Activity::NOTIFICHE_ULL
	#
	# $APP_BASE::$object_name::$activity_type_name::Binding::$binding_type::$binding_name
	# es. $APP_BAE::Activity::NOTIFICHE_ULL::Binding::Status::INVIATO
	#
	#---------------------------------------------------------------------------------------
	
	# il nome dell'oggetto su cui applicare il binding e' obbligatorio
	unless (defined $object_name){
		$self->last_error("binding_name: missing OBJECT_NAME");
		return;
	}
	
	# il nome del tipo attivita' e' obbligatorio
	unless (defined $activity_type_name){
		$self->last_error("binding_name: missing ACTIVITY_TYPE_NAME");
		return;
	}
	
	# BINDING_TYPE e BINDING_NAME devono essere consistenti
	# entrambi nulli oppure entrambi valorizzati
	if (defined $binding_type xor defined $binding_name){
		$self->last_error(
			sprintf"inconsistent: BINDING_TYPE: '%s' and BINDING_NAME: '%s'",
			$binding_type || '', $binding_name || ''
		);
		return;
	}
	
	# classe base di rimappamento dell'oggetto
	my @class_name = (
		$APP_BASE,
		$object_name,
		$activity_type_name
	);
	
	# solo se definita una specializzazione
	if (defined $binding_type){
		push @class_name, ('Binding', $binding_type, $binding_name);
	}
	
	# restituisce il nome della classe per il rimappamento
	return join ('::', @class_name);
}

sub in_binding_create_counter()			{ $_[0]->{__IN_BINDING_CREATE_COUNTER__} }
sub in_binding_create_counter_init()	{ $_[0]->{__IN_BINDING_CREATE_COUNTER__} = {} }
sub in_binding_create_counter_reset()	{ $_[0]->{__IN_BINDING_CREATE_COUNTER__} = undef }

sub in_binding_status_counter()			{ $_[0]->{__IN_BINDING_STATUS_COUNTER__} }
sub in_binding_status_counter_init()	{ $_[0]->{__IN_BINDING_STATUS_COUNTER__} = {} }
sub in_binding_status_counter_reset()	{ $_[0]->{__IN_BINDING_STATUS_COUNTER__} = undef }
sub in_binding_status_counter_delete_activity_id($_)	{
	my $self = shift;
	my $activityId = shift;
	if (defined $self->{__IN_BINDING_STATUS_COUNTER__}){
		for my $status (keys %{$self->{__IN_BINDING_STATUS_COUNTER__}}){
			delete $self->{__IN_BINDING_STATUS_COUNTER__}->{$status}->{$activityId} if exists $self->{__IN_BINDING_STATUS_COUNTER__}->{$status}->{$activityId};
		}
	}

	return 1;
}


# Restituisce le entità per la serializzazione
=head2 I<object>->B<get_entity_serialization>()

Restituisce le entità per la serializzazione.

Ritorna un hashref con la chiave primaria rappresente la tipologia dell'oggetto (es: ATTIVITA, SISTEMA), la chiave secondaria
rapprenta il nome del tipo_oggetto che è un hashref con le seguenti chiavi

=item B<HEADER> E' il nome della chiave da visualizzare

=item B<NAME> Javascript notation dove recuperare il valore del campo

=item B<TYPE> Tipologia del dato

=item B<POSITION> Posizione di visualizzazione

=item B<I<FORMAT>_EXCLUDE> 1 se la colonna deve essere esclusa

=item B<I<FORMAT>_HIDDEN> 1 se la colonna deve essere nascosta (Applicabile solo per i formati JSON e XLSX)

=item B<JSON_DESCRIPTION> Descrizione estesa del campo (Applicabile solo per il formato JSON)


=item Esempio:
	
	{
		'ACTIVITIES' => {
			'NETWORK' => [
				{
					'POSITION' => 1,
					'NAME' => 'info_creationDate_1',
					'JSON_DESCRIPTION' => 'Data creazione network',
					'JSON_HIDDEN' => 0,
					'JSON_EXCLUDE' => 0,
					'TYPE' => 'timestamp_with_time_zone',
					'HEADER' => 'DATA_CREAZIONE',
					'PATH' => '["info"]["creationDate"]'
				},
				{
					'POSITION' => 2,
					'NAME' => 'properties_networkId_2',
					'JSON_DESCRIPTION' => 'Identificativo network',
					'JSON_HIDDEN' => 0,
					'JSON_EXCLUDE' => 0,
					'TYPE' => 'string',
					'HEADER' => 'networkId',
					'PATH' => '["properties"]["networkId"]'
				}
			]
		}
	}
	
=back

=cut
sub get_entity_serialization {
	my $self = shift;
	my %params = @_;
	my $errmsg = '';
	
	$self->last_error($errmsg)
		&& return undef
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					FORMAT => { isa => 'SCALAR', list => ['JSON', 'CSV', 'XLSX'] }
				}
				,OPTIONAL	=> {
					PATH => { isa => 'SCALAR' }
					,HANDLE => { isa => 'GLOB' }
				}
				,IGNORE_EXTRA_PARAMS	=> 0
			);
	
	
	# essendo un oggetto che non cambia viene creato al primo utilizzo e restituito sempre uguale successivamente
	my $serializzazione;
	if (defined $self->{ENTITY_SERIALIZATION}){
		$serializzazione = $self->{ENTITY_SERIALIZATION};
	} else {
	
		my $sql = qq{
			select
				decode (TIPO_OGGETTO,'ATTIVITA','ACTIVITIES','SISTEMA','SYSTEMS', null) TIPO_OGGETTO
				, NOME_OGGETTO
				, ETICHETTA
				, PERCORSO
				, DESCRIZIONE
				, NASCOSTO
				, ESCLUSO
				, FORMATO
				, POSIZIONE
				, case
					when tipo_oggetto = 'ATTIVITA' and percorso like '["properties"]["%' then replace(replace(percorso,'["properties"]["',''),'"]')
					else null
					end DTA_PROPERTIES
				, case
					when tipo_oggetto = 'ATTIVITA' and percorso like '["system"]["properties"]["%' then replace(replace(percorso,'["system"]["properties"]["',''),'"]')
					when tipo_oggetto = 'SISTEMA' and percorso like '["properties"]["%' then replace(replace(percorso,'["properties"]["',''),'"]')
					else null
					end DT_PROPERTIES
			from SERIALIZZAZIONE_ENTITA
			where disabilitato is null
			order by tipo_oggetto, nome_oggetto, posizione
		};
		$self->_create_prepare(__PACKAGE__.'_entser', $sql);
			
		$serializzazione = $self->{ENTITY_SERIALIZATION} = $self->_create_prepare(__PACKAGE__.'_entser')->fetchall_hashref();
	}

	my $tmp_act_vis = {};
	my $tmp_sys_vis = {};
	
	my $ret_entity_serialization = {};
	
	for my $s (@{$serializzazione}){
		
		$self->last_error("Bad configuration: ".Dumper($s))
			&& return undef
				unless defined $s->{TIPO_OGGETTO};
		
		$ret_entity_serialization->{$s->{TIPO_OGGETTO}}->{$s->{NOME_OGGETTO}} = [] if ! exists $ret_entity_serialization->{$s->{TIPO_OGGETTO}}->{$s->{NOME_OGGETTO}};
		
		my $visibility_property;
		if ($s->{TIPO_OGGETTO} eq 'ACTIVITIES'){
			if (! exists $tmp_act_vis->{$s->{NOME_OGGETTO}}){
				$visibility_property = $self->get_activity_property_visibility(ACTIVITY_TYPE_NAME => $s->{NOME_OGGETTO});
				return undef unless defined $visibility_property;
			} else {
				$visibility_property = $tmp_act_vis->{$s->{NOME_OGGETTO}};
			}
			# se attivo il meccanismo di visibilità dei dta deve avere la visibilità
			next if ($visibility_property->{ACTIVE} && defined $s->{DTA_PROPERTIES} && !grep {$_->{NAME} eq $s->{DTA_PROPERTIES}} @{$visibility_property->{PROPERTIES}});
		} else { # if ($s->{TIPO_OGGETTO} eq 'SYSTEMS'){
			if (! exists $tmp_sys_vis->{$s->{NOME_OGGETTO}}){
				$visibility_property = $self->get_system_property_visibility(SYSTEM_TYPE_NAME => $s->{NOME_OGGETTO});
				return undef unless defined $visibility_property;
			} else {
				$visibility_property = $tmp_sys_vis->{$s->{NOME_OGGETTO}};
			}
			# se attivo il meccanismo di visibilità dei dta deve avere la visibilità
			next if ($visibility_property->{ACTIVE} && defined $s->{DT_PROPERTIES} && !grep {$_->{NAME} eq $s->{DT_PROPERTIES}} @{$visibility_property->{PROPERTIES}});
		}

		# split della chiave NASCOSTO per sapere per quali formato il campo è nascosto
		my @hidden = defined $s->{NASCOSTO} ? split (',', $s->{NASCOSTO}) : ();
		
		# split della chiave ESCLUSO per sapere per quali formato il campo è escluso
		my @excluded = defined $s->{ESCLUSO} ? split (',', $s->{ESCLUSO}) : ();
		
		my $name = $s->{PERCORSO};
		$name =~s/(\[)//g;
		$name =~s/(\])/_/g;
		$name =~s/(")//g;
		$name =~s/(')/_/g;
		$name.= $s->{POSIZIONE};
		
		my $tmp = {
			HEADER => $s->{ETICHETTA},
			PATH => $s->{PERCORSO},
			TYPE => $s->{FORMATO},
			NAME => $name,
			POSITION => $s->{POSIZIONE}*1,
			$params{FORMAT}."_EXCLUDE" => (grep {$_ eq $params{FORMAT}} @excluded) ? 1 : 0
		};
		
		if ($params{FORMAT} =~/^(JSON|XLSX)$/){
			$tmp->{$params{FORMAT}."_HIDDEN"} = (grep {$_ eq $params{FORMAT}} @hidden) ? 1 : 0;
			if ($params{FORMAT} eq 'JSON'){
				$tmp->{JSON_DESCRIPTION} = $s->{DESCRIZIONE};
			}
		}
		
		push @{$ret_entity_serialization->{$s->{TIPO_OGGETTO}}->{$s->{NOME_OGGETTO}}}, $tmp;
	}

	return $ret_entity_serialization;
}

# Genera un UUID randomico pseudo-casuale
sub _gen_session_id {  # static method 
	my ($fd, $c, $max) = (undef, undef, 256);
	open($fd,'/dev/urandom') || carp "/dev/urandom: $!";
	my $n = read $fd, $c, $max;
	close $fd;
	carp "/dev/urandom: unable to read $max bytes" if $n < $max;
	return md5_hex($c);
}

sub _remap_keys {
	my $self = shift;
	
	my ($key, %params) = @_;
	
	return $key if $params{USE_DOLLAR} && $key !~/^\$/;
	
	my $prefix= $params{USE_DOLLAR} ? '$' : '';
	
	$key =~s/^\$//;
	
	my $remapped_info = {
		'SYSTEM_NAME'					=> 'description'
		, 'ACTIVE'						=> 'active'
		, 'DISABLED'					=> 'disabled'
		, 'ENDED'						=> 'ended'
		, 'CREATION_DATE'				=> 'creationDate'
		, 'DISABLING_DATE'				=> 'disablingDate'
		, 'ENDING_DATE'					=> 'endingDate'
		, 'SYSTEM_NAME'					=> 'description'
		, 'LAST_VAR_DATE'				=> 'lastVarDate'
		, 'PARENT_ID'					=> 'parentId'
		, 'SYSTEM_ID'					=> 'systemId'
		, 'DESCRIPTION'					=> 'description'
		, 'DATE_NEXT_STEP'				=> 'dateNextStep'
		, 'CURRENT_USER_ID'				=> 'currentUserId'
		, 'SYSTEM_TYPE_ID'				=> 'systemTypeId'
		, 'OWNER_USER_ID'				=> 'ownerId'
		, 'STATUS_ID'					=> 'statusId'
		, 'ACTIVITY_TYPE_ID'			=> 'activityTypeId'
		, 'ACTIVITY_TYPE_NAME'			=> 'activityTypeName'
		, 'SYSTEM_TYPE_NAME'			=> 'systemTypeName'
		, 'ACTIVITY_ID'					=> 'activityId'
		, 'CURRENT_USER_NAME'			=> 'currentUsername'
		, 'OWNER_USER_NAME'				=> 'ownerName'
		, 'OWNER'						=> 'owner'
		, 'OWNER_NAME'					=> 'owner'
		, 'OWNER_FIRST_NAME'			=> 'ownerFirstName'
		, 'OWNER_LAST_NAME'				=> 'ownerLastName'
		, 'STATUS_NAME'					=> 'statusName'
		, 'EMAIL'						=> 'email'
		, 'NAME'						=> 'username'
		, 'FIRST_NAME'					=> 'firstName'
		, 'LAST_NAME'					=> 'lastName'
		, 'MOBILE_PHONE'				=> 'mobilePhone'
		, 'SERVICE_USER'				=> 'serviceUser'
		, 'GROUPS'						=> 'groups'
		, 'CHILDREN'					=> 'children'
		, 'TRANSITION_DATE'				=> 'transitionDate'
		, 'TRANSITION_ID'				=> 'transitionId'
		, 'FROM_STATUS_NAME'			=> 'fromStatus'
		, 'USER_NAME'					=> 'username'
		, 'ACTION_NAME'					=> 'action'
		, 'TO_STATUS_NAME'				=> 'toStatus'
		, 'PROPERTIES'					=> 'properties'
		, 'ATTACHMENTS'					=> 'attachments'
		, 'SEQUENCE'					=> 'sequence'
		, 'FILENAME'					=> 'fileName'
		, 'DOWNLOAD_COUNT'				=> 'downloadCount'
		, 'SIZE'						=> 'size'
		, 'CURRENT_USER_CHANGE_REASON'	=> 'currentUserChangeReason'
		, 'TRANSITION_PROPERTIES'		=> 'transitionProperties'
	};

	return $prefix.$remapped_info->{$key};

}

sub serialize {
	my $self = shift;
	my $obj = shift;
	
	my $obj_hash;
	if (ref ($obj) eq 'API::ART::Activity::History::Transition'){
		my $transition = $obj->serialize();
		while ( my ($key, $value) = each %{$transition} ) {
			next if $key eq 'TYPE';
	
			if ($key =~/^(PROPERTIES)$/){
				$obj_hash->{$self->_remap_keys($key)} = [];
				for my $single_prop (@{$value}){
					push @{$obj_hash->{$self->_remap_keys($key)}},  {
						 'value'	=> $single_prop->{PROPERTY_VALUE}
						,'order'	=> defined $single_prop->{PROPERTY_ORDER} ? $single_prop->{PROPERTY_ORDER}*1 : undef
						#,'id'		=> $single_prop->{PROPERTY_ID}*1
						,'validity'	=> $single_prop->{PROPERTY_VALIDITY} ? $JSON::true : $JSON::false
						,'name'		=> $single_prop->{PROPERTY_NAME}
					};
				}
			} elsif ($key eq 'TRANSITION_DATE') {

				$value =~ s/^(.{4})(.{2})(.{2})(.{2})(.{2})(.{2})$/$1-$2-$3T$4:$5:$6/;
				
				my $iso_date = $self->format_iso_date($value);
				$obj_hash->{$self->_remap_keys($key)} = $iso_date;
				$obj_hash->{id} = $transition->{$key}*1;
			} elsif ($key eq 'ATTACHMENTS'){
				$obj_hash->{$self->_remap_keys($key)} = [];
				for my $attach (@{$value}){
					my $transtionDate = $attach->{TRANSITION_DATE};

					$transtionDate =~ s/^(.{4})(.{2})(.{2})(.{2})(.{2})(.{2})$/$1-$2-$3T$4:$5:$6/;

					push @{$obj_hash->{$self->_remap_keys($key)}}, {
						transitionId => $attach->{TRANSITION_ID}*1,
						transitionDate => $self->format_iso_date($transtionDate),
						sequence => $attach->{SEQUENCE},
						fileName => $attach->{NOME_FILE_CLIENT},
						title => $attach->{TITLE},
						description => $attach->{DESCRIPTION},
						revision => $attach->{REVISION},
						refDate => $attach->{REF_DATE},
						docType => $attach->{DOC_TYPE},
						size => defined $attach->{SIZE} ? $attach->{SIZE}*1 : undef,
						owner => $attach->{OWNER_NAME},
						ownerFirstName => $attach->{OWNER_FIRST_NAME},
						ownerLastName => $attach->{OWNER_LAST_NAME},
						downloadCount => $attach->{DOWNLOAD_COUNT},
					}
				}
					
			} elsif ($key eq 'CURRENT_USER_NAME'){
				if (defined $transition->{CURRENT_USER_CHANGE_REASON}) {
					if (defined $value){
						$obj_hash->{$self->_remap_keys($key)} = $value;
						$obj_hash->{currentUsernameObj} = $self->get_user_structure($self->get_user_id($value));		
					} else {
						$obj_hash->{currentUsernameObj} = $obj_hash->{$self->_remap_keys($key)} = undef;
					}
				}
			} elsif ($key eq 'USER_NAME'){
				$obj_hash->{$self->_remap_keys($key)} = $value;
				$obj_hash->{usernameObj} = $self->get_user_structure($self->get_user_id($value));				
			} else {
				$obj_hash->{$self->_remap_keys($key)} = $value;
				
			}
		}
	}
	return $obj_hash;
}

# Restituisce l'elenco delle property visibili dell'attività relativamente al profilo utente
=head2 I<object>->B<get_activity_property_visibility>(ACTIVITY_TYPE_NANE => I<scalar>)

Restituisce l'elenco delle property visibili dell'attività relativamente al profilo utente.

Ritorna un hashref con le seguenti chiavi

=item B<ACTIVE> Se valorizzato a I<1> indica che è attivo il meccanismo di visibilità delle property

=item B<PROPERTIES> Arrary ref contenente hash con le chiavi I<ID> e I<NAME> per ogni property su cui si ha visibilità. E' valorizzato ad I<undef> in caso di ACTIVE = I<0>

=item Esempio:
	
	{
		'ACTIVE' => 1,
		'PROPERTIES' => [
			{
				'ID' => '929',
				'NAME' => 'oa'
			}
		]
	}
	
=back

=cut

sub get_activity_property_visibility(){
	my $self = shift;

	my %params = @_;
	my $errmsg = '';
	
	$self->last_error($errmsg)
		&& return undef
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					ACTIVITY_TYPE_NAME => { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS	=> 0
			);
	
	unless($self->test_activity_type_name($params{ACTIVITY_TYPE_NAME})){
		$self->last_error("Unknon ACTIVITY_TYPE_NAME ".$params{ACTIVITY_TYPE_NAME});
		return undef;
	}

	# verifico se la visiblità delle property è attiva
	unless (defined $self->{ALL_PROPERTIES}->{ACTIVITIES}->{$params{ACTIVITY_TYPE_NAME}}->{EXISTS}){
		my $sql_visibility = "
			select 1
			from PERMISSION_TA_TDTA_GRUPPI p
			where p.data_Rimozione is null
				and p.id_tipo_attivita = ?
			and rownum<2
		";
		
		my $prepare_visibility = $self->_create_prepare(__PACKAGE__.'__via_property', $sql_visibility);

		$self->{ALL_PROPERTIES}->{ACTIVITIES}->{$params{ACTIVITY_TYPE_NAME}}->{EXISTS} = $prepare_visibility->fetch_minimalized($self->get_activity_type_id($params{ACTIVITY_TYPE_NAME}))||0;
	}
	my $visibility_property;
	if ($self->{ALL_PROPERTIES}->{ACTIVITIES}->{$params{ACTIVITY_TYPE_NAME}}->{EXISTS}){
		my $user_groups = $self->user()->su()->activity()->groups();
		# se è admin o ha il sudo group vedrà sempre tutte le property quindi evito query inutili
		if ($self->user()->is_admin() || grep {$ENV{ART_BINDING_SUDO_GROUP} eq $self->get_group_name($_)}@{$user_groups} ){
			$self->{ALL_PROPERTIES}->{ACTIVITIES}->{$params{ACTIVITY_TYPE_NAME}}->{ACTIVE} = 0;
		} else {
			$self->{ALL_PROPERTIES}->{ACTIVITIES}->{$params{ACTIVITY_TYPE_NAME}}->{ACTIVE} = 1;
			# in base all'attuale profilo (potrebbe essere diverso di volta in volta utilizzando il sudo) capisco quali sono le properties su cui l'utente ha visibilità
			my @sorted_user_groups = sort { $a <=> $b } @{$user_groups};
			my $join_user_groups = join (',', @sorted_user_groups);

			if (defined $self->{ALL_PROPERTIES}->{ACTIVITIES}->{$params{ACTIVITY_TYPE_NAME}}->{PROPERTIES}->{$join_user_groups}){
				$visibility_property = $self->{ALL_PROPERTIES}->{ACTIVITIES}->{$params{ACTIVITY_TYPE_NAME}}->{PROPERTIES}->{$join_user_groups};
			} else {
				my $sql_visibility_property = "
					select distinct p.id_tipo_dato_Tecnico_attivita ID, tdta.descrizione NAME
					from PERMISSION_TA_TDTA_GRUPPI p
						join tipi_dati_Tecnici_Attivita tdta on tdta.id_tipo_Dato_Tecnico_Attivita = p.id_tipo_Dato_tecnico_Attivita
					where p.data_Rimozione is null
						and p.id_tipo_attivita = ?
						and p.id_Gruppo in (".join (',', map {'?'} @{$user_groups}).")
				";
				
				my $prepare_visibility_property = $self->_create_prepare(__PACKAGE__.'__via1_property'.(scalar @{$user_groups}), $sql_visibility_property);

				$self->{ALL_PROPERTIES}->{ACTIVITIES}->{$params{ACTIVITY_TYPE_NAME}}->{PROPERTIES}->{$join_user_groups} = $visibility_property = $prepare_visibility_property->fetchall_hashref($self->get_activity_type_id($params{ACTIVITY_TYPE_NAME}), @{$user_groups});
			}
		}
		return {
			ACTIVE => $self->{ALL_PROPERTIES}->{ACTIVITIES}->{$params{ACTIVITY_TYPE_NAME}}->{ACTIVE},
			PROPERTIES => $visibility_property
		};
	} else {
		return {
			ACTIVE => 0,
			PROPERTIES => $visibility_property
		};
	}
}

# Restituisce l'elenco delle property visibili del sistema relativamente al profilo utente
=head2 I<object>->B<get_system_property_visibility>(SYSTEM_TYPE_NANE => I<scalar>)

Restituisce l'elenco delle property visibili del sistema relativamente al profilo utente.

Ritorna un hashref con le seguenti chiavi

=item B<ACTIVE> Se valorizzato a I<1> indica che è attivo il meccanismo di visibilità delle property

=item B<PROPERTIES> Arrary ref contenente hash con le chiavi I<ID> e I<NAME> per ogni property su cui si ha visibilità. E' valorizzato ad I<undef> in caso di ACTIVE = I<0>

=item Esempio:
	
	{
		'ACTIVE' => 1,
		'PROPERTIES' => [
			{
				'ID' => '929',
				'NAME' => 'oa'
			}
		]
	}
	
=back

=cut

sub get_system_property_visibility(){
	my $self = shift;

	my %params = @_;
	my $errmsg = '';
	
	$self->last_error($errmsg)
		&& return undef
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					SYSTEM_TYPE_NAME => { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS	=> 0
			);
	
	unless($self->test_system_type_name($params{SYSTEM_TYPE_NAME})){
		$self->last_error("Unknon SYSTEM_TYPE_NAME ".$params{SYSTEM_TYPE_NAME});
		return undef;
	}

	# verifico se la visiblità delle property è attiva
	unless (defined $self->{ALL_PROPERTIES}->{SYSTEMS}->{$params{SYSTEM_TYPE_NAME}}->{EXISTS}){
		my $sql_visibility = "
			select 1
			from PERMISSION_TS_TDT_GRUPPI p
			where p.data_Rimozione is null
				and p.id_tipo_sistema = ?
			and rownum<2
		";
		
		my $prepare_visibility = $self->_create_prepare(__PACKAGE__.'__vis_property', $sql_visibility);

		$self->{ALL_PROPERTIES}->{SYSTEMS}->{$params{SYSTEM_TYPE_NAME}}->{EXISTS} = $prepare_visibility->fetch_minimalized($self->get_system_type_id($params{SYSTEM_TYPE_NAME}))||0;
	}
	my $visibility_property;
	if ($self->{ALL_PROPERTIES}->{SYSTEMS}->{$params{SYSTEM_TYPE_NAME}}->{EXISTS}){
		my $user_groups = $self->user()->su()->activity()->groups();
		# se è admin o ha il sudo group vedrà sempre tutte le property quindi evito query inutili
		if ($self->user()->is_admin() || grep {$ENV{ART_BINDING_SUDO_GROUP} eq $self->get_group_name($_)}@{$user_groups} ){
			$self->{ALL_PROPERTIES}->{SYSTEMS}->{$params{SYSTEM_TYPE_NAME}}->{ACTIVE} = 0;
		} else {
			$self->{ALL_PROPERTIES}->{SYSTEMS}->{$params{SYSTEM_TYPE_NAME}}->{ACTIVE} = 1;
			# in base all'attuale profilo (potrebbe essere diverso di volta in volta utilizzando il sudo) capisco quali sono le properties su cui l'utente ha visibilità
			my @sorted_user_groups = sort { $a <=> $b } @{$user_groups};
			my $join_user_groups = join (',', @sorted_user_groups);

			if (defined $self->{ALL_PROPERTIES}->{SYSTEMS}->{$params{SYSTEM_TYPE_NAME}}->{PROPERTIES}->{$join_user_groups}){
				$visibility_property = $self->{ALL_PROPERTIES}->{SYSTEMS}->{$params{SYSTEM_TYPE_NAME}}->{PROPERTIES}->{$join_user_groups};
			} else {
				my $sql_visibility_property = "
					select distinct p.id_tipo_dato_Tecnico ID, tdta.nome NAME
					from PERMISSION_TS_TDT_GRUPPI p
						join tipi_dati_Tecnici tdta on tdta.id_tipo_Dato_Tecnico = p.id_tipo_Dato_tecnico
					where p.data_Rimozione is null
						and p.id_tipo_sistema = ?
						and p.id_Gruppo in (".join (',', map {'?'} @{$user_groups}).")
				";
				
				my $prepare_visibility_property = $self->_create_prepare(__PACKAGE__.'__vis1_property'.(scalar @{$user_groups}), $sql_visibility_property);

				$self->{ALL_PROPERTIES}->{SYSTEMS}->{$params{SYSTEM_TYPE_NAME}}->{PROPERTIES}->{$join_user_groups} = $visibility_property = $prepare_visibility_property->fetchall_hashref($self->get_system_type_id($params{SYSTEM_TYPE_NAME}), @{$user_groups});
			}
		}
		return {
			ACTIVE => $self->{ALL_PROPERTIES}->{SYSTEMS}->{$params{SYSTEM_TYPE_NAME}}->{ACTIVE},
			PROPERTIES => $visibility_property
		};
	} else {
		return {
			ACTIVE => 0,
			PROPERTIES => $visibility_property
		};
	}
}

# Effettua check per varie tipologie di dato
=head2 I<object>->B<check_value>(TYPE => I<scalar>, KEY => I<scalar>, VALUE => I<scalar>)

Restituisce true in caso di successo

=item B<TYPE> Tipologia di dato

=item B<KEY> Nome della chiave da verificare

=item B<VALUE> Valore della chiave da verificare

=item Esempio:
	
	{
		'ACTIVE' => 1,
		'PROPERTIES' => [
			{
				'ID' => '929',
				'NAME' => 'oa'
			}
		]
	}
	
=back

=cut

sub check_value{
	my $self = shift;
	my $errmsg;
	my %params = @_;

	my $val;
	if (ref ($params{VALUE}) && ref ($params{VALUE}) eq 'API::ART::System::DT::Measure'){
		$val = $params{VALUE}->get_value();
	} else {
		$val = $params{VALUE};
	}

	if (defined $val){

		if (defined $params{VALUE_MIN}){
			$self->last_error("Invalid value ".$val." for param ".$params{KEY}.": cannot be lower than ".$params{VALUE_MIN})
				&& return undef
					if $val*1 < $params{VALUE_MIN};
		}
		if (defined $params{VALUE_MAX}){
			$self->last_error("Invalid value ".$val." for param ".$params{KEY}.": cannot be greater than ".$params{VALUE_MAX})
				&& return undef
					if $val*1 > $params{VALUE_MAX};
		}
		if (defined $params{FRACTIONS} && $params{TYPE} ne 'CURRENCY'){
			my $fractions = $params{FRACTIONS};
			my $reg = qr/^(-)?\d+(\.\d{1,${fractions}})?$/;
			$self->last_error("Invalid fractions ".$params{TYPE}." ".$params{KEY}. " = ".$val.": expected ".$fractions." digits")
				&& return undef
					if $val !~ /$reg/;
		}

		# valido INTEGER
		$self->last_error("Invalid INTEGER ".$params{KEY}. " = ".$val)
			&& return undef
				if $params{TYPE} eq 'INTEGER' && $val !~ /^-?\d+$/;

		# valido LONG
		$self->last_error("Invalid LONG ".$params{KEY}. " = ".$val)
			&& return undef
				if $params{TYPE} eq 'LONG' && $val !~ /^-?\d+$/;

		# valido FLOAT
		$self->last_error("Invalid FLOAT ".$params{KEY}. " = ".$val)
			&& return undef
				if $params{TYPE} eq 'FLOAT' && $val !~ /^-?\d+(\.\d+)*$/;

		# valido TIMESTAMP
		if ($params{TYPE} =~/^(TIMESTAMP|ISODAT)$/){
			my $prepare_iso_date = $self->_create_prepare(__PACKAGE__."_check_value", "select to_timestamp_tz(?,'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm') at time zone 'Europe/Rome' from dual");
			eval {$prepare_iso_date->fetch_minimalized($val)};
			if (SIRTI::DB::get_errormessage()){
				#return SIRTI::DB::get_errormessage();
				$self->last_error("Invalid ".$params{TYPE}." ".$params{KEY}. " = ".$val);
				return undef;
			}
		}

		# valido DATE
		if ($params{TYPE} =~/^(DATE|DAY)$/){
			$self->last_error("Invalid ".$params{TYPE}." ".$params{KEY}. " = ".$val)
				&& return undef
					if $val !~ /^\d{4}-\d{2}-\d{2}$/;
			$self->last_error("Invalid ".$params{TYPE}." ".$params{KEY}. " = ".$val)
				&& return undef
					if $self->_dbh()->test_date($val,'YYYY-MM-DD') ne '';
		}

		# valido BOOL
		$self->last_error("Invalid BOOL ".$params{KEY}. " = ".$val)
			&& return undef
				if $params{TYPE} eq 'BOOL' && defined $val && $val !~ /^(0|1)$/;

		# valido LATLNG
		$self->last_error("Invalid coordinates ".$params{KEY}. " = ".$val)
			&& return undef
				if $params{TYPE} eq 'LATLNG' && $val !~ /^([+-])?([0-8]?[0-9](\.\d+)?|90(\.0+)?),\s*([+-])?(180(\.0+)?|((1[0-7][0-9]|[0-9][0-9]|[0-9])(\.\d+)?))$/;
		
		# valido CURRENCY
		if ($params{TYPE} eq 'CURRENCY'){
			my $fractions = $params{FRACTIONS}||0;
			my $reg = qr/^\d+(\.\d{${fractions}})?$/;
			$self->last_error("Invalid CURRENCY ".$params{KEY}. " = ".$val)
				&& return undef
					if $val !~ /$reg/;
		}

		# valido ORA
		if ($params{TYPE} =~/^(ORA)$/){
			$self->last_error("Invalid ".$params{TYPE}." ".$params{KEY}. " = ".$val)
				&& return undef
					if $val !~ /^\d{2}:\d{2}$/;
			$self->last_error("Invalid ".$params{TYPE}." ".$params{KEY}. " = ".$val)
				&& return undef
					if $self->_dbh()->test_date($val,'HH24:MI:SS') ne '';
		}

		# valido POPUP
		if ($params{TYPE} eq 'POPUP'){
			my @elenco_valori = split(',', $params{VALUES});
			unless (grep {$val eq $_} @elenco_valori){
				$self->last_error("Value '".$val."' not available for ".$params{KEY})
					&& return undef;
			}
		}

		# valido BLOB
		if ($params{TYPE} eq 'BLOB') {
			# rimuovo dalla stringa Base64 eventuali ritorni a capo/spazi per poterla validare con una regex
			$val =~ s/[\r\n\s]//g; 
			# tolgo solo il padding alla fine della stirnga (serve per testare la lnghezza effettiva del Base4)
			$val =~ s/=*$//;
			# non può esistere un Base64 più corto di 2 caratteri; il Base64 di una stringa vuota è esso stesso una stringa vuota!
			$self->last_error("Expected valid Base64 encoded value for BLOB property ".$params{KEY}." [$val]")
				&& return undef
					if length($val) < 2;
			# se viene ritornata una stringa vuota (o eventulamente null) non è un Base64 valido
			$self->last_error("Malformed Base64 encoded value for BLOB property ".$params{KEY}." [$val]")
				&& return undef
					unless decode_base64($val);
		}
		
	}

	# valido JSON
	if ($params{TYPE} =~/^(JSON|TAGS|MOOOKUP)$/) {
		my $array = eval{ from_json($val)};
	
		if ($@) {
			$self->last_error("Unable to parse JSON ".$params{KEY}. " = ".$val);
			return undef;
		};
	}

	


	return 1;
}

1;
