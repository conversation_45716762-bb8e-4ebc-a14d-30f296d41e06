package API::ART::Activity;

use strict;
use warnings;
use Carp;
use File::Basename;
use JSON;
use Digest::MD5 qw(md5_hex);
use Array::Utils qw(:all);

use SIRTI::ART::Events;
use API::ART::System;
use API::ART::Activity::Factory;
use API::ART::Activity::History;
use API::ART::Activity::Aging;

use base qw(API::Ancestor);

use constant {
	A_INFO_FRESH	=> 0
	,A_INFO_CACHED	=> 1
};


our $VERSION = '0.01';


# Classe deputata alla costruzione di nuove API::ART::Activity
our $DEFAULT_COLLECTION_CLASS = 'API::ART::Collection::Activity';

# Classe di default dei "figli" di API::ART::Activity
our $DEFAULT_CHILDREN_CLASS = 'API::ART::Activity';

# Classe di default dei "padri" di API::ART::Activity
our $DEFAULT_PARENT_CLASS = 'API::ART::Activity';

# Classe di default del sistema di API::ART::System;
our $DEFAULT_SYSTEM_CLASS = 'API::ART::System';

=head1 NAME

B<API::ART::Activity> - Gestione attivita'

=head1 SYNOPSIS

	# Uses needed packages
	use API::ART;
	use API::ART::Activity;

	# Create API::ART instance...
	my $art = API::ART->new(.......);
	# Create API::ART::Activity instance...
	my $a = API::ART::Activity->new(ART => $art, ID => 349522);

	print "Current status: ", $art->get_status_name($a->info('STATUS_ID')), "\n";
	print "This is the NUMTEL property value: ", $a->properties('NUMTEL'), "\n";
	print "Stepping activity... ";
	my $rc = $a->step(
		DEST_USER => 'pluto',
		ACTION => 'VARIAZIONE',
		DESCRIPTION => 'Variazione da script',
		DATE => '2008-04-30 13:30:59',
		DATE_FORMAT => 'yyyy-mm-dd hh24:mi:ss',
		PROPERTIES => {
			DATALAVORO => '2008-04-24 10:25:31',
			DATAEXCHANGE => 'F',
		},
		ATTACHMENTS => [
			'/tmp/log_old.txt',
			'/tmp/log_new.txt',
		],
	);

=head1 DESCRIPTION

Questo package consente di accedere alle attivita' di ART attraverso un'interfaccia OO.

Per l'utilizzo dell'Aging consultare la documentazione della classe API::ART::Activity::Aging

=head1 METHODS

Di seguito i metodi esposti dalla classe API::ART::Activity.

=cut

#
# Metodi privati
#

sub _step {
	my $self = shift;
	my %params = @_;
	local $@;
	
	$self->art()->clear_last_error();
	
	##### Aggiorna le info() x velocizzare le query successive
	my $info = $self->info();
	
	# USER
	$self->art()->last_error("USER param available only for admin-users")
		&& return undef
			if ( defined $params{USER} && !$self->art()->user()->is_admin() );

	# ACTION
	$self->art()->last_error("Missing ACTION!")
		&& return 0
			unless defined $params{ACTION};
	$self->art()->last_error("Unknown ACTION '$params{ACTION}'!")
		&& return 0
			unless $self->art()->test_activity_action_name($params{ACTION});
	unless ( $params{VIRTUAL} ) {
		$self->art()->last_error("ACTION '$params{ACTION}' not allowed for current STATUS '${ \$self->art()->get_activity_status_name($self->{INFO}->{STATUS_ID}) }'!")
			&& return 0
				unless $self->can_do_action( NAME => $params{ACTION} );
	}
	
	##### Determinazione gruppo abilitato all'esecuzione dell'azione
	my ($group_id, $dest_status_id);
	if ( $params{VIRTUAL} ) {
		$self->art()->last_error("Missing DEST_STATUS!")
			&& return 0
				unless defined $params{DEST_STATUS};
		$self->art()->last_error("Unknown DEST_STATUS '$params{DEST_STATUS}'!")
			&& return 0
				unless $self->art()->test_activity_status_name($params{DEST_STATUS});
		($group_id, $dest_status_id) = (
			$self->art()->get_group_id('ROOT'),
			$self->art()->get_activity_status_id($params{DEST_STATUS})
		);
	} else {
		($group_id, $dest_status_id) = $self->art()->_test_permission(
			ACTIVITY_TYPE_ID => $self->{INFO}->{ACTIVITY_TYPE_ID},
			STATUS_ID => $self->{INFO}->{STATUS_ID},
			ACTION_ID => $self->art()->get_activity_action_id($params{ACTION}),
			GROUPS_ID => $self->art()->user()->su()->activity()->groups()
		);
		$self->art()->last_error("User '".$self->art()->user()->name()."' not allowed for action '$params{ACTION}'!" )
			&& return 0
				unless defined $group_id;
	}
	
	my $is_activity_final_status = $self->art()->is_activity_final_status( ID => $dest_status_id );
	
	# Determina se lo stato di destinazione è uno stato finale...
	# (check disabilitato con parametro IGNORE_FINAL_STATUS)
	unless ($params{IGNORE_FINAL_STATUS}){
		unless (
			(
				defined $self->art()->_get_activity_virtual_action_invalidate_name()
				&&
				$params{ACTION} eq $self->art()->_get_activity_virtual_action_invalidate_name()
			)
			||
			(
				defined $self->art()->_get_activity_virtual_action_add_documentation_name()
				&&
				$params{ACTION} eq $self->art()->_get_activity_virtual_action_add_documentation_name()
			)
		){
			if ( $is_activity_final_status ) {
				# ...e se è possibile "chiudere" l'acttività
				$self->art()->last_error("Can not CLOSE activity ".$self->id()."!")
					&& (return undef)
						unless $self->can_close();
			}
		}
	}
	
	# DESCRIPTION
	$params{DESCRIPTION} = "Automatic Transition" if (! defined $params{DESCRIPTION} || $params{DESCRIPTION} eq '');
	
	# DATE_FORMAT
	$params{DATE_FORMAT} = $self->art()->info('DEFAULT_DATE_FORMAT') unless defined $params{DATE_FORMAT};
	# DATE
	my $use_sysdate = !defined($params{DATE}); # flag che indica allo step che la data non e' stata specificata dall'utente
	#$params{DATE} = $self->art()->_dbh()->get_sysdate($params{DATE_FORMAT}) unless defined $params{DATE};
	if ($params{DATE}){
		
		my $sql = "
			select '1' from DUAL
				where to_Date(?,?)> to_Date(?,?)
		";
	
		$self->art()->_create_prepare(__PACKAGE__.'__step_check_date', $sql);
		
		my @bind_values = (
			$params{DATE},
			$params{DATE_FORMAT},
			$self->{INFO}->{LAST_VAR_DATE},
			$self->art()->info('DEFAULT_DATE_FORMAT')
		);
		
		unless ($self->art()->_create_prepare(__PACKAGE__.'__step_check_date')->fetch_minimalized(@bind_values)){
			$self->art()->last_error("DATE not valid. Must be greater than LAST_VAR_DATE!")
				&& return 0;
		}
	}
	$params{DATE} = $self->{INFO}->{DATE_NEXT_STEP} unless defined $params{DATE};
	my $test_date = $self->art()->_dbh()->test_date($params{DATE}, $params{DATE_FORMAT});
	$self->art()->last_error("Invalid DATE '$params{DATE}': '$test_date'!") && return 0 if $test_date;
	
	my $sql_date = "
		select	 to_char(to_date(?, ?)
				,?)
		from	dual
	";

	$self->art()->_create_prepare(__PACKAGE__.'__step_convert_date', $sql_date);
	
	my @bind_values_sql_date = (
		$params{DATE},
		$params{DATE_FORMAT},
		$self->art()->info('DEFAULT_DATE_FORMAT')
	);
	
	$params{DATE} = $self->art()->_create_prepare(__PACKAGE__.'__step_convert_date')->fetch_minimalized(@bind_values_sql_date);
	
	# DEST_USER
	if (defined $params{DEST_USER}){
		$self->art()->last_error("Unknown DEST_USER '$params{DEST_USER}'!")
			&& return 0
				if !$self->art()->test_user_name($params{DEST_USER}=uc($params{DEST_USER}));

		# verifico che il DEST_USER abbia visibilità sull'attività altrimenti non ha senso assegnargliela
		my $dest_user_groups = $self->art()->get_user_groups($self->art()->get_user_id($params{DEST_USER}));

		my $system_groups = $self->system()->info('GROUPS');
		my $du_has_visibility = 0;
		for my $dg (@{$dest_user_groups}){
			$du_has_visibility = 1
				if grep {$dg eq $self->art()->get_group_id($_)} @{$system_groups};
			last if $du_has_visibility;
		}
		
		$self->art()->last_error("DEST_USER '$params{DEST_USER}' not allowed to manage activity!")
			&& return 0
				unless $du_has_visibility;

		##### Determino se l'operatore destinatario appartiene ad gruppo abilitato alla gestione dello stato destinazioone
		unless ( $is_activity_final_status ) {
			### A meno che sia l'azione di PARK il DEST_USER deve poter movimentare l'attivita' dallo stato di atterraggio
			if ($params{ACTION} ne $self->art()->_get_activity_virtual_action_park_name()){
				$self->art()->last_error("DEST_USER '$params{DEST_USER}' not allowed to manage destination status '${\$self->art()->get_activity_status_name($dest_status_id)}'!")
					&& return 0
						unless	$self->art()->_test_permission(
								ACTIVITY_TYPE_ID => $self->{INFO}->{ACTIVITY_TYPE_ID},
								STATUS_ID => $dest_status_id,
								GROUPS_ID => $dest_user_groups,
							);
			}
		}
	}

	# PROPERTIES
	$params{PROPERTIES} = {} unless defined $params{PROPERTIES};
	$self->art()->last_error("PROPERTIES must be an hash-ref or undefined!")
		&& return 0
			unless ref($params{PROPERTIES}) eq 'HASH';
	
	# ATTACHMENTS
	$params{ATTACHMENTS} = [] unless defined $params{ATTACHMENTS};
	$self->art()->last_error("ATTACHMENTS must be an array-ref or undefined!")
		&& return 0
			unless ref($params{ATTACHMENTS}) eq 'ARRAY';
	
	if ( $params{VIRTUAL} ) {
		# Converto il NOME delle proprieta' (hash-keys) nel corrispettivo ID in
		# quanto la SIRTI::ART::Events->step_activity() non provvedera' a cio' (dato
		# che trattasi di azione VIRTUALE non mappata in TIPI_DATI_TECNICI_ATT_ACTION)
		# Verifico che le PROPERTY siano valide x l'ACTIVITY_TYPE corrente
		my @prop_names = @{ $self->{INFO}->{PROPERTIES} };
		foreach my $prop ( keys %{ $params{PROPERTIES} } ) {
			$self->art()->last_error("PROPERTY '$prop' not allowed!")
				&& (return 0)
					unless grep(/^$prop$/, @prop_names );
			$params{PROPERTIES}->{"${ \$self->art()->get_activity_property_id($prop) }"} = $params{PROPERTIES}->{$prop};
			delete $params{PROPERTIES}->{$prop};
		}
	}

	##### Creazione gestore attivita
	my $handler = $self->_get_handler($group_id, $params{SAVE_ALL_PROPERTIES});
	return undef unless	defined $handler;

	# LOCK
	# verifico se l'attività è già loccata da me stesso
	my $myPreviousLock = 0;	# Se valorizzato a 1, al termine dello step non rimuovo il lock
	if ( $self->is_locked_by_myself() ) {
		$myPreviousLock = 1; # fa in modo che al termine dello step non venga rimosso il lock
	} else {

		# Verifico che l'attività non sia lockata da qualche altro operatore/sessione		
		$self->art()->last_error("Activity locked!")
			&& return 0
				if $self->is_locked();

		# Provo a lockare l'attività		
		$self->art()->last_error("Unable to lock activity!")
			&& return 0
				unless $self->lock();
	}
	
	my $previous_current_user = $info->{CURRENT_USER_NAME};
	
	# imposto il savepoint
	$self->art()->_dbh()->do("SAVEPOINT AAAstep_".$self->id());
	
	# Esecuzione step (con hack x step attivita multipli nello stesso secondo)
	my $rc = '';
	my $step = 0;
	STEP: while () {
		eval {
			$rc = $handler->step_activity(
				virtual_step => $params{VIRTUAL},
				id_attivita => $self->id(),
				id_operatore => defined $params{USER} ? $self->art()->get_user_id($params{USER}) : $self->art()->user()->id,
				id_operatore_corrente => (defined $params{DEST_USER} ? $self->art()->get_user_id($params{DEST_USER}) : undef),
				id_azione => $self->art()->get_activity_action_id($params{ACTION}),
				id_stato => (defined $params{DEST_STATUS} ? $self->art()->get_activity_status_id($params{DEST_STATUS}) : undef),
				descr_attivita => $params{DESCRIPTION},
				data_esecuzione => $params{DATE},
				record => $params{PROPERTIES},
				allegati => $params{ATTACHMENTS},
				art => $self->art()
			);
		};
		my $err = $@;
		# Se...
		if ( $err ne "" ) {
			#print STDERR $err, "\n";
#			if (	$use_sysdate  		# ...non e' stata indicata una specifica DATA
#					&& $step++ == 0  	# ...e' il primo ciclo
#					#&& $err =~ m/unique constraint \(ART\.STORIA_ATTIVITA_PK\) violated/ # ...e' stat violata il constraint
#					&& $err =~ m/unique constraint \(.*STORIA_ATTIVITA_PK\) violated/ # ...e' stat violata il constraint
#				) {
#				$self->art()->warning("*** Waiting 1 second because of unique constraint violation! ***");
#				sleep 1;			# ...attendo un secondo...
#				$params{DATE} = $self->art()->_dbh()->get_sysdate($params{DATE_FORMAT});	# ...ricalcolo la sysdate
#				next STEP;			# ...e riprovo a rieseguire lo step
#			} else {
#				$rc = $err;
#			}
			$rc = $err;
		} else {
			
			$self->{HISTORY}->refresh(1) if $self->{HISTORY};
		}
		last STEP;
	}
	
	# Se si è verificato un errore durante lo step esco con un ko
	if ($rc) {
		# rollback savepoint
		$self->art()->_dbh()->do("rollback to savepoint AAAstep_".$self->id());
		# Se ho messo il lock lo rimuovo
		$self->unlock()
			unless $myPreviousLock;
		$self->art()->last_error($rc) ;
		return 0;
	}

	# sovrascrivo per leggere valori dopo step
	$info = $self->info();
	
	# gestisco l'aging se è presente la configurazione per il tipo_attivita
	if (defined $info->{"AGING"}->{"CALENDAR_ID"}){
		my $ret = API::ART::Activity::Aging::do($self);
		unless (defined $ret){
			$self->art()->_dbh()->do("rollback to savepoint AAAstep_".$self->id());
			$self->art()->last_error("Unable to execute aging: " .$self->art()->last_error());
			return 0;
		}
	}
	
	# gestisco i comportamenti
	my %behaviours_params;
	$behaviours_params{EVENT} = $params{BEHAVIOUR_EVENT} if defined $params{BEHAVIOUR_EVENT};
	
	unless (defined $self->_manage_behaviours(%behaviours_params)){
		# rollback savepoint
		$self->art()->_dbh()->do("rollback to savepoint AAAstep_".$self->id());
		# Se ho messo il lock lo rimuovo
		$self->unlock()
			unless $myPreviousLock;
		return 0;
	};
	
	# Se ho messo il lock lo rimuovo
	$self->unlock()
		unless $myPreviousLock;
	
	if ((defined $params{DEST_USER} || defined $previous_current_user) && $self->art()->email_support() && $params{ASSIGN_NOTIFICATION}) {
		
		my $user_assigned = $params{DEST_USER} || $previous_current_user;
		
		my $user_info = $self->art()->get_user_info($self->art()->get_user_id($user_assigned));
		
		$self->art()->last_error("You asked to send assign notification but user email is missing!")
			&& return 0
				unless defined $user_info->{EMAIL};
		
		my $sql_date_email = "
			select to_char(to_date(?, ?),'YYYYMMDDHH24MISS') from dual
		";
	
		$self->art()->_create_prepare(__PACKAGE__.'__step_mail_ass', $sql_date_email);
		
		my @bind_values_sql_date_email = (
			$info->{'LAST_VAR_DATE'},
			$self->art()->get_default_date_format()
		);
		
		my $date = $self->art()->_create_prepare(__PACKAGE__.'__step_mail_ass')->fetch_minimalized(@bind_values_sql_date_email);
		
		my $p_send_email = {
			TO => ['"'.$user_info->{LAST_NAME}." ".$user_info->{FIRST_NAME}.'" <'.$user_info->{EMAIL}.'>']
			,SUBJECT => '['.($ENV{ART_APPLICATION_NAME}||'ART'). ']['.$self->cached_info('ACTIVITY_TYPE_NAME').'] '.(defined $params{DEST_USER} ? 'The activity '.$self->id().' is assigned to you' : 'The activity '.$self->id().' is deassigned')
			,REF =>  $self->id().' - '.(defined $params{DEST_USER} ? 'ASSIGN_NOTIFICATION' : 'DEASSIGN_NOTIFICATION').' - '.$date
		};
		
		my $sender = '"'.$self->art()->user()->last_name." ".$self->art()->user()->first_name.'"';
		
		my $body_suffix = '';
		if ($self->art()->user()->email()){
			$sender.=' <'.$self->art()->user()->email().'>';
			$p_send_email->{SENDER} = $p_send_email->{REPLY_TO} = $sender;
		} else{
			$body_suffix = "\nThis is an automatically generated email, please do not reply";
		}
		
		my $creation_user = $self->art()->get_user_info($self->cached_info('CREATION_USER_ID'));
		### FIXME: gestire con locale
		my $isodate_format = 'YYYY-MM-DD"T"hh24:mi:sstzh:tzm';
		
		my $sql_date_convert_isodate = "
			select to_char(to_timestamp_tz(?,?),?) From dual
		";
	
		$self->art()->_create_prepare(__PACKAGE__.'__step_mail_ciso', $sql_date_convert_isodate);
		
		my @bind_values_sql_date_convert_isodate = (
			$self->cached_info('CREATION_DATE'),
			$self->art()->get_default_date_format(),
			$isodate_format
		);
		
		my $creation_date = $self->art()->_create_prepare(__PACKAGE__.'__step_mail_ciso')->fetch_minimalized(@bind_values_sql_date_convert_isodate);
		
		@bind_values_sql_date_convert_isodate = (
			$info->{'LAST_VAR_DATE'},
			$self->art()->get_default_date_format(),
			$isodate_format
		);
		
		my $last_var_date = $self->art()->_create_prepare(__PACKAGE__.'__step_mail_ciso')->fetch_minimalized(@bind_values_sql_date_convert_isodate);
		
		$p_send_email->{BODY} = "
Activity Id: ".$self->id()."
Activity Type Name: ".$self->cached_info('ACTIVITY_TYPE_NAME')."
Activity Type Description: ".$self->art()->get_activity_type_description($self->cached_info('ACTIVITY_TYPE_ID'))."
Activity Description: ".$self->cached_info('DESCRIPTION')."
Creation Date: ".$creation_date."
Creation User: ".$creation_user->{LAST_NAME}.", ".$creation_user->{FIRST_NAME}."
Status: ".$self->status_name()."
Last Variation Date: ".$last_var_date."
Transition Annotation: ".$params{DESCRIPTION}."
".(defined $params{DEST_USER} ? 'Assigned' : 'Deassigned')." by: ".$sender;

		if ($self->activity_property()){
			$p_send_email->{BODY} .="\n\n--- Activity Property ---\n";
			my $propsacts=$self->activity_property();
			my @keys = keys %{$propsacts};
			@keys = sort @keys;
			my $sql_dta_convert_isodate = "
				select to_char(to_timestamp_tz(?,?),?) From dual
			";
		
			$self->art()->_create_prepare(__PACKAGE__.'__step_mail_dta_ciso', $sql_dta_convert_isodate);
			for my $key (@keys){
				my $tipo_ui = $self->art()->_lookup()->tipo_ui_tdta_nome($key);
				if ($tipo_ui =~/^(TAGS|MOOKUP)$/){
					my $array = eval{ from_json($propsacts->{$key})};
					next if scalar (@{$array}) == 0;
				} else {
					next unless defined $propsacts->{$key};
					if ($tipo_ui =~/^(ISODAT)$/){
						my @bind_values_sql_dta_convert_isodate = (
							$propsacts->{$key},
							$self->art()->get_default_iso_date_format_oracle(),
							$isodate_format
						);
						
						$propsacts->{$key} = $self->art()->_create_prepare(__PACKAGE__.'__step_mail_dta_ciso')->fetch_minimalized(@bind_values_sql_dta_convert_isodate);
					} elsif ($tipo_ui =~/^(CURRENCY)$/){
						$propsacts->{$key} .= ' '.$self->art()->_lookup()->valuta_tdta_nome($key);
					}
				}
				$p_send_email->{BODY}.="\n".$key.": ".$propsacts->{$key};
			}
			$p_send_email->{BODY}.="\n";
		}
		
		$p_send_email->{BODY}.=(defined $ENV{RESTART_HOMEPAGE} ? "\nUrl: ".$ENV{RESTART_HOMEPAGE}.'?action=view&activityId='.$self->id() : '')."\n".$body_suffix;
		
		unless ($self->art()->send_email(%$p_send_email)){
			# rollback savepoint
			$self->art()->_dbh()->do("rollback to savepoint AAAstep_".$self->id());
			return 0;
		};
	}
	
	# invio la notifica per ogni gruppo di contatto se l'istanza supporta questa funzionalità
	if ($self->art()->notification_activity_support()){
		my $recipients = $self->_get_groups_contacts_values();
		unless (defined $recipients){
			# rollback savepoint
			$self->art()->_dbh()->do("rollback to savepoint AAAstep_".$self->id());
			return 0;
		}

		unless ($self->send_notification(CONFIGURED_RECIPIENTS => $recipients)){
			# rollback savepoint
			$self->art()->_dbh()->do("rollback to savepoint AAAstep_".$self->id());
			return 0;
		};
	}
	
	return 1;
}

sub _get_groups_contacts_values {
	my $self = shift;
	my %params = @_;

	my $recipients= [];

	if ($self->art()->notification_activity_support()){
		my $system_groups = $self->system()->info('GROUPS');
		my $num_groups = scalar @{$system_groups};
		
		my $query_contatti =  "
			select g.nome group_name, gc.label
			from TIPI_ATTIVITA_STATI_CONTATTI tasc
				join gruppi_contatti gc on gc.id = tasc.id_gruppo_contatto
				join gruppi g on g.id_gruppo = gc.id_gruppo
			where tasc.id_tipo_attivita = ?
				and tasc.id_stato = ?
				and gc.abilitato = 'Y'
				and gc.id_gruppo in (".join(',', map {'?'} @{$system_groups}).")
		";
		
		$self->art()->_create_prepare(__PACKAGE__.'_ggcvs_'.$num_groups, $query_contatti);
		
		my $info = $self->info();

		my @bind_values = (
			$info->{'ACTIVITY_TYPE_ID'},
			$info->{'STATUS_ID'}
		);
		
		push (@bind_values, map {$self->art()->get_group_id($_)} @{$system_groups});
		
		my $contacts = $self->art()->_create_prepare(__PACKAGE__.'_ggcvs_'.$num_groups)->fetchall_hashref(@bind_values);
		
		
		for my $contact (@{$contacts}){
			push @$recipients, {
				GROUP_NAME => $contact->{GROUP_NAME}
				,LABEL => $contact->{LABEL}
			}
		}
	}

	return $recipients;

}

sub _get_groups_contacts { shift->{GROUPS_CONTACTS} };

sub _get_behaviours { shift->{BEHAVIOURS} };

sub _manage_behaviours {
	my $self = shift;
	my %params = @_;

	# imposto il savepoint
	$self->art()->_dbh()->do("SAVEPOINT AAAman_beh_".$self->id());
	
	# gestico il comportamento sullo step
	if (!defined $params{EVENT} || $params{EVENT} eq 'on_step'){
		$self->art()->_dbh()->do("rollback to SAVEPOINT AAAman_beh_".$self->id())
			&& return undef
				unless defined $self->_execute_behaviours(BEHAVIOUR_EVENT => ['on_step'], ACT => $self);
	}
	
	# se non è definita o sto scendendo gestisco i figli
	if (!defined $params{EVENT} || $params{EVENT} eq 'on_father_step'){
		# recupero solo i figli che hanno questo tipo di evento
		my $children = $self->get_children(BEHAVIOUR_EVENT => ['on_father_step']);
		return undef unless defined $children;
		
		for my $ch (@{$children}){
			$self->art()->_dbh()->do("rollback to SAVEPOINT AAAman_beh_".$self->id())
				&& return undef
					unless defined $ch->_execute_behaviours(BEHAVIOUR_EVENT => ['on_father_step'], ACT => $ch);
		}
	}

	# se non è definita o sto salendo gestisco il padre
	if (!defined $params{EVENT} || $params{EVENT} eq 'on_child_step'){
		# recupero il padre se e' presente ed eseguo il suo comportamento
		if ($self->parent_id()){
			$self->art()->_dbh()->do("rollback to SAVEPOINT AAAman_beh_".$self->id())
				&& return undef
					unless defined $self->parent()->_execute_behaviours(BEHAVIOUR_EVENT => ['on_child_step'], ACT => $self);
		}
	}
	
	return 1;
}

sub _execute_behaviours{
	my $self = shift;
	my %params = @_;
	
	# imposto default
	$params{BEHAVIOUR_EVENT} = ['on_father_step', 'on_child_step', 'on_step'] unless defined $params{BEHAVIOUR_EVENT};
	
	my $behaviours = $self->_get_behaviours();
	
	# imposto il savepoint
	$self->art()->_dbh()->do("SAVEPOINT AAAex_be_".$self->id());
	
	# giro sugli eventi richiesti
	for my $b (@{$params{BEHAVIOUR_EVENT}}){
		# giro sui comportamenti che ha l'attivita'
		for my $h (@{$behaviours->{$b}}){
			# Imposto il nome della classe che gestice il comportamento
			my $class = defined $h->{CLASS} ? $h->{CLASS} : 'API::ART::Activity::Behaviours::'.$h->{LABEL};
			
			CLASS: {
				local $@;
				eval "require $class;";
				if ($@){
					$self->art()->_dbh()->do("rollback to SAVEPOINT AAAex_be_".$self->id());
					$self->art()->last_error("Unable to require class ($class): " .$@);
					return undef;
				}
			}
			
			if (my $method = $class->can('do')){
				my $ret = $method->($params{ACT}, $b);
				unless (defined $ret){
					$self->art()->_dbh()->do("rollback to SAVEPOINT AAAex_be_".$self->id());
					$self->art()->last_error("Unable to execute behaviour $b: " .$self->art()->last_error());
					return undef;
				}
			}
		}
	}
	return 1;
}

sub _get_handler {
	my $self = shift;
	my $group_id = shift;
	my $save_all_dta = shift;
	local $@;
	$self->art()->clear_last_error();
	$self->art()->last_error("Missing GROUP_ID in HANDLER creation!\n")
		&& (return undef)
			unless defined($group_id);

	unless (defined $save_all_dta){
		$save_all_dta =
			defined $self->{SAVE_ALL_PROPERTIES} 				? $self->{SAVE_ALL_PROPERTIES} 				:
			defined $self->art()->_get_save_all_properties()	? $self->art()->_get_save_all_properties() 	:
			1;
	}

	my $handler = undef;
	eval {
		$handler = SIRTI::ART::Events->new(
			DB => $self->art()->_dbh(),
			ID_TIPO_SISTEMA => $self->{INFO}->{SYSTEM_TYPE_ID},
			ID_TIPO_ATTIVITA => $self->{INFO}->{ACTIVITY_TYPE_ID},
			ID_GRUPPO_ABILITATO => $group_id,
			LOOKUP_ART => $self->art()->_lookup(),
			SAVE_ALL_DTA => $save_all_dta, # Forza la memorizzazione di tutti i DTA anche nel caso il nuovo valore non cambi
		);
	};
	($self->art()->last_error("Error creating HANDLER!\n" . $@))
		&& (return undef)
			if $@;
	return $handler;
}

sub _which_info {
	my ($self, $key, $cache) = @_;
	my $info = $self->_info($cache);
	return $info->{$key} if defined $key;
	return $info;
}
sub _info {
	my ($self, $cache) = @_;
	return $self->{INFO} if $cache eq A_INFO_CACHED;
	#
	# TODO => ottimizzazione richiesta attraverso lock in modo da non eseguire
	# sempre la query ma solo nel caso in cui l'attivita' non sia lockata dall'utente
	#
	my $date_format = $self->art()->info('DEFAULT_DATE_FORMAT');
	
	if (defined $self->{INFO}){
		my $sql = qq(
			SELECT a.descrizione "DESCRIPTION"
				,a.stato_corrente status_id
				,TO_CHAR (a.data_creazione, ?) creation_date
				,st.nome status_name
				,TO_CHAR (a.data_ult_varstat, ?) last_var_date
				,TO_CHAR (
					case
						when a.data_ult_varstat>=sysdate then a.data_ult_varstat+1/86400
						else sysdate
					end,
				?) date_next_step
				,a.id_op_ult_varstat owner_user_id
				,op2.login_operatore owner_user_name
				,a.id_operatore_corrente current_user_id
				,(select op3.login_operatore from operatori op3 where op3.id_operatore = a.id_operatore_corrente) current_user_name
				,a.aggiorna_aging aging_update
				,a.aging aging_seconds
				,case when a.aging is not null then '+' || trunc(a.aging/86400) || ' ' ||to_char(trunc(sysdate)+(a.aging/86400), 'hh24:mi') end aging_interval
				,TO_CHAR (a.data_ult_aggiornamento_aging, ?) aging_last_var_date
				,'+' || trunc(sysdate - a.DATA_ULT_VARSTAT) || ' ' ||to_char(trunc(sysdate)+(sysdate - a.DATA_ULT_VARSTAT), 'hh24:mi') Interval
				,apaf.padre parent_activity_id
				,ID_TRANSIZIONE last_Transition_Id
				,ID_AGGIORNAMENTO last_Update_Id
				,TO_CHAR (cast(TS_AGGIORNAMENTO as date), ?) last_Update_Timestamp
				,VERSIONE version
				,decode(st.flag_stato_partenza, 'C', 1, 'Q', 1, 0) IN_FINAL_STATUS
				,decode(st.flag_stato_partenza, 'V', 1, 0) IN_PARKED_STATUS
			FROM   attivita a
				,operatori op2
				,stati st
				,a_padre_a_figlio apaf
			WHERE  a.id_attivita = ?
				and op2.id_operatore = a.id_op_ult_varstat
				and a.stato_Corrente = st.id_Stato
				AND apaf.figlio (+) = a.id_attivita
				and rownum<2
		);
		$self->{ART}->_create_prepare(__PACKAGE__.'_infop', $sql);
		
		my $res = $self->{ART}->_create_prepare(__PACKAGE__.'_infop')->fetchall_hashref(
			$date_format
			, $date_format
			, $date_format
			, $date_format
			, $date_format
			, $self->{ID}
		);
		$self->{ART}->last_error(__PACKAGE__." : ACTIVITY_ID ".$self->{ID}." not found!")
			&& return undef
				unless scalar @{$res};
		for my $k (keys %{$res->[0]}){
			$self->{INFO}->{$k} = $res->[0]->{$k};
		}
		# rielabora le info dell'aging per poterle gestire in un'unica chiave
		for ('AGING_UPDATE', 'AGING_SECONDS', 'AGING_INTERVAL', 'AGING_LAST_VAR_DATE'){
			$self->{INFO}->{AGING}->{$_} = $self->{INFO}->{$_};
			delete $self->{INFO}->{$_};
		}
	} else {
		my $sql = qq(
			SELECT s.id_tipo_sistema system_type_id
				,a.id_attivita "ACTIVITY_ID"
				,TO_CHAR (a.data_creazione, ?) creation_date
				,a.id_op_creazione creation_user_id
				,op.login_operatore creation_user_name
				,a.id_sistema system_id
				,a.id_tipo_attivita activity_type_id
				,ta.nome_tipo_Attivita ACTIVITY_TYPE_NAME
				,ta.id_calendar CALENDAR_ID
				,ta.ui_route UI_ROUTE
				,ac.LABEL CALENDAR_LABEL
				,ac.DESCRIPTION CALENDAR_DESCRIPTION
				,a.descrizione "DESCRIPTION"
				,a.stato_corrente status_id
				,st.nome status_name
				,TO_CHAR (a.data_ult_varstat, ?) last_var_date
				,TO_CHAR (
					case
						when a.data_ult_varstat>=sysdate then a.data_ult_varstat+1/86400
						else sysdate
					end,
				?) date_next_step
				,a.id_op_ult_varstat owner_user_id
				,op2.login_operatore owner_user_name
				,a.id_operatore_corrente current_user_id
				,(select op3.login_operatore from operatori op3 where op3.id_operatore = a.id_operatore_corrente) current_user_name
				,a.aggiorna_aging aging_update
				,a.aging aging_seconds
				,case when a.aging is not null then '+' || trunc(a.aging/86400) || ' ' ||to_char(trunc(sysdate)+(a.aging/86400), 'hh24:mi') end aging_interval
				,TO_CHAR (a.data_ult_aggiornamento_aging, ?) aging_last_var_date
				,'+' || trunc(sysdate - a.DATA_ULT_VARSTAT) || ' ' ||to_char(trunc(sysdate)+(sysdate - a.DATA_ULT_VARSTAT), 'hh24:mi') Interval
				,apaf.padre parent_activity_id
				,ID_TRANSIZIONE last_Transition_Id
				,ID_AGGIORNAMENTO last_Update_Id
				,TO_CHAR (cast(TS_AGGIORNAMENTO as date), ?) last_Update_Timestamp
				,VERSIONE version
				,decode(st.flag_stato_partenza, 'C', 1, 'Q', 1, 0) IN_FINAL_STATUS
				,decode(st.flag_stato_partenza, 'V', 1, 0) IN_PARKED_STATUS
			FROM   attivita a
				,tipi_attivita ta
				,sistemi s
				,operatori op
				,operatori op2
				,stati st
				,a_padre_a_figlio apaf
				,aging_calendars ac
			WHERE  a.id_attivita = ?
				and ta.id_tipo_attivita = a.id_tipo_attivita
				AND s.id_sistema = a.id_sistema
				and op.id_operatore = a.id_op_creazione
				and op2.id_operatore = a.id_op_ult_varstat
				and a.stato_Corrente = st.id_Stato
				AND apaf.figlio (+) = a.id_attivita
				AND ac.id_calendar (+) = ta.id_calendar
				and rownum<2
		);
		$self->{ART}->_create_prepare(__PACKAGE__.'_infof', $sql);
		
		my $res = $self->{ART}->_create_prepare(__PACKAGE__.'_infof')->fetchall_hashref(
			$date_format
			, $date_format
			, $date_format
			, $date_format
			, $date_format
			, $self->{ID}
		);
		$self->{ART}->last_error(__PACKAGE__." : ACTIVITY_ID ".$self->{ID}." not found!")
			&& return undef
				unless scalar @{$res};

		$self->{INFO} = $res->[0];

		$self->{INFO}->{SYSTEM_TYPE_NAME} = $self->{ART}->get_system_type_name($self->{INFO}->{SYSTEM_TYPE_ID});

		my $sql_tdta = "
			select distinct tdta.descrizione
			from tipi_dati_tecnici_Att_action tdtaa
			join tipi_dati_Tecnici_attivita tdta on tdta.id_tipo_dato_Tecnico_Attivita = tdtaa.id_tipo_dato_Tecnico_attivita
			where tdtaa.id_tipo_attivita = ?
				and tdta.morto is null
		";
		
		$self->{ART}->_create_prepare(__PACKAGE__.'_info_tdta', $sql_tdta);
		
		$self->{INFO}->{PROPERTIES} = [ $self->{ART}->_create_prepare(__PACKAGE__.'_info_tdta')->fetch_minimalized($self->{INFO}->{ACTIVITY_TYPE_ID}) ];

		$self->{INFO}->{AGING} = {};

		my $sql_tas = "
			select TO_CHAR (tas.INSERT_DATE, ?) INSERT_DATE
				, tas.TARGET_LABEL
				, tas.TARGET_LEVEL
				, tas.TARGET_TO
				, tas.TARGET_FROM
			from tipi_attivita_sla tas
			where tas.id_tipo_attivita = ?
				and tas.disable_date is null
			order by tas.target_from, tas.target_to
		";
		
		$self->{ART}->_create_prepare(__PACKAGE__.'_info_tas', $sql_tas);
		
		$self->{INFO}->{AGING}->{SLA} = $self->{ART}->_create_prepare(__PACKAGE__.'_info_tas')->fetchall_hashref($date_format, $self->{INFO}->{ACTIVITY_TYPE_ID});
		# rielabora le info dell'aging per poterle gestire in un'unica chiave
		for ('AGING_UPDATE', 'AGING_SECONDS', 'AGING_INTERVAL', 'AGING_LAST_VAR_DATE', 'CALENDAR_ID', 'CALENDAR_LABEL', 'CALENDAR_DESCRIPTION'){
			$self->{INFO}->{AGING}->{$_} = $self->{INFO}->{$_};
			delete $self->{INFO}->{$_};
		}
	}
	$self->{INFO}->{IS_LOCKED} = $self->is_locked();
	$self->{INFO}->{IS_LOCKED_BY_MYSELF} = $self->is_locked_by_myself();
	
	$self->{INFO}->{CHILDREN} = $self->_get_children();
	
	return $self->{INFO};
}

sub _property {
	my $self = shift;
	my @keys = @_;

	my $properties_all;
	my $visibility_property = $self->{ART}->get_activity_property_visibility(ACTIVITY_TYPE_NAME => $self->{INFO}->{ACTIVITY_TYPE_NAME});
	return undef unless defined $visibility_property;

	my $sql = qq(
		SELECT DISTINCT tdta.id_tipo_dato_tecnico_attivita "ID"
				,tdta.descrizione "NAME"
		FROM	tipi_dati_tecnici_attivita tdta
				,tipi_dati_tecnici_att_action tdtaa
				,dati_tecnici_attivita att
		WHERE	att.id_attivita = ?
				and att.id_tipo_dato_Tecnico_attivita = tdta.id_tipo_dato_Tecnico_attivita
				and tdtaa.id_tipo_attivita = ?
				AND tdtaa.id_tipo_dato_tecnico_attivita = tdta.id_tipo_dato_tecnico_attivita
				AND tdta.morto IS NULL
	);
	
	$self->{ART}->_create_prepare(__PACKAGE__.'__property', $sql);

	my $tmp_properties_all = $self->{ART}->_create_prepare(__PACKAGE__.'__property')->fetchall_hashref($self->id(), $self->{INFO}->{ACTIVITY_TYPE_ID});

	my $visibility_property_list;

	if ($visibility_property->{ACTIVE}){
		$visibility_property_list = $visibility_property->{PROPERTIES};
		for my $k (@{$tmp_properties_all}){
			push @{$properties_all}, $k if grep {$k->{NAME} eq $_->{NAME}} @{$visibility_property_list};
		}
	} else {
		$properties_all = $tmp_properties_all;
	}
	
	my $properties = [];
	
	if (scalar @keys){
		for my $k (@{$properties_all}){
			push @{$properties}, $k if grep {$k->{NAME} eq $_} @keys;
		}
	} else {
		$properties = $properties_all;
	}
	
	$self->{PROPERTIES} = undef;

	if ( defined $properties ) {
		##### Creazione gestore attivita
		
		if ($self->art()->flatdta_support()  &&  $self->art()->get_flatdta_list_by_activity_type(ACTIVITY_TYPE_NAME => $self->{INFO}->{ACTIVITY_TYPE_NAME})){
			my $orig_NLS_NUMERIC_CHARACTERS = $self->art()->_dbh()->get_session_parameters('NLS_NUMERIC_CHARACTERS');
			$self->art()->_dbh()->set_session_parameters('NLS_NUMERIC_CHARACTERS' => '.,');

			my $sql_extract = 'pt.id_attivita ';
			
			@keys = map {$_->{NAME}} @{$properties};
			
			my $dta_count=0;
			my $lookup_get = $self->art()->lookup_get();
			for my $key_pt (@keys){
				if (grep {/^$key_pt$/} @{$self->art()->get_flatdta_list_by_activity_type(ACTIVITY_TYPE_NAME => $self->{INFO}->{ACTIVITY_TYPE_NAME})}){
					if ($self->art()->get_flatdta_list_remap_property_by_activity_type(ACTIVITY_TYPE_NAME => $self->{INFO}->{ACTIVITY_TYPE_NAME})->{$key_pt}->{DATA_TYPE} eq 'DATE'){
						$sql_extract .= ',to_char(pt."'.$self->art()->get_flatdta_list_remap_property_by_activity_type(ACTIVITY_TYPE_NAME => $self->{INFO}->{ACTIVITY_TYPE_NAME})->{$key_pt}->{REMAP_NAME}.'",'.$self->art()->_dbh()->quote($self->art()->get_flatdta_list_remap_property_by_activity_type(ACTIVITY_TYPE_NAME => $self->{INFO}->{ACTIVITY_TYPE_NAME})->{$key_pt}->{DATE_FORMAT}).') "'.$key_pt.'"';
					} elsif ($self->art()->get_flatdta_list_remap_property_by_activity_type(ACTIVITY_TYPE_NAME => $self->{INFO}->{ACTIVITY_TYPE_NAME})->{$key_pt}->{DATA_TYPE} eq 'TIMESTAMP WITH TIME ZONE'){
						$sql_extract .= ',to_char(pt."'.$self->art()->get_flatdta_list_remap_property_by_activity_type(ACTIVITY_TYPE_NAME => $self->{INFO}->{ACTIVITY_TYPE_NAME})->{$key_pt}->{REMAP_NAME}.'",'.$self->art()->_dbh()->quote($self->art()->get_flatdta_list_remap_property_by_activity_type(ACTIVITY_TYPE_NAME => $self->{INFO}->{ACTIVITY_TYPE_NAME})->{$key_pt}->{DATE_FORMAT}).') "'.$key_pt.'"';
					} elsif ($self->art()->get_flatdta_list_remap_property_by_activity_type(ACTIVITY_TYPE_NAME => $self->{INFO}->{ACTIVITY_TYPE_NAME})->{$key_pt}->{DATA_TYPE} eq 'NUMBER' && $lookup_get->tipo_ui_tdta_nome($key_pt) eq 'CURRENCY'){
						$sql_extract .= ', case when to_char(pt."'.$self->art()->get_flatdta_list_remap_property_by_activity_type(ACTIVITY_TYPE_NAME => $self->{INFO}->{ACTIVITY_TYPE_NAME})->{$key_pt}->{REMAP_NAME}.'") like \'%.%\' then to_char(pt."'.$self->art()->get_flatdta_list_remap_property_by_activity_type(ACTIVITY_TYPE_NAME => $self->{INFO}->{ACTIVITY_TYPE_NAME})->{$key_pt}->{REMAP_NAME}.'") when pt."'.$self->art()->get_flatdta_list_remap_property_by_activity_type(ACTIVITY_TYPE_NAME => $self->{INFO}->{ACTIVITY_TYPE_NAME})->{$key_pt}->{REMAP_NAME}.'" is not null then to_char(pt."'.$self->art()->get_flatdta_list_remap_property_by_activity_type(ACTIVITY_TYPE_NAME => $self->{INFO}->{ACTIVITY_TYPE_NAME})->{$key_pt}->{REMAP_NAME}.'")||\'.00\' else to_char(pt."'.$self->art()->get_flatdta_list_remap_property_by_activity_type(ACTIVITY_TYPE_NAME => $self->{INFO}->{ACTIVITY_TYPE_NAME})->{$key_pt}->{REMAP_NAME}.'") end "'.$key_pt.'"';
					} else {
						$sql_extract .= ',pt."'.$self->art()->get_flatdta_list_remap_property_by_activity_type(ACTIVITY_TYPE_NAME => $self->{INFO}->{ACTIVITY_TYPE_NAME})->{$key_pt}->{REMAP_NAME}.'" "'.$key_pt.'"';
					}
				} else {
					$sql_extract .= "
						,
			           (SELECT dta".$dta_count.".descrizione
			            FROM   dati_tecnici_attivita dta".$dta_count."
			            WHERE  dta".$dta_count.".id_tipo_dato_tecnico_attivita = ".$self->art()->get_activity_property_id($key_pt)." -- ".$key_pt."
			                   AND dta".$dta_count.".id_attivita = pt.id_attivita
			                   AND (dta".$dta_count.".data_esecuzione =
			                           (SELECT MAX (dta.data_esecuzione)
			                            FROM   dati_tecnici_attivita dta
			                            WHERE  dta.id_tipo_dato_tecnico_attivita = dta".$dta_count.".id_tipo_dato_tecnico_attivita
			                                   AND dta.id_attivita = pt.id_attivita
			                                   AND dta.morto is null)
			                        OR dta".$dta_count.".data_esecuzione IS NULL
			                       )) \"".$key_pt."\"
					";
					$dta_count++;
				}
			}
			my $sql = "select ".$sql_extract." from ".$self->art()->get_flatdta_list_remap_table_by_activity_type(ACTIVITY_TYPE_NAME => $self->{INFO}->{ACTIVITY_TYPE_NAME})." pt where pt.id_attivita = ".$self->id();
			#print STDERR "\n".$sql."\n";
			$self->{PROPERTIES} = $self->{ART}->_dbh()->fetchall_hashref($sql)->[0];
			$self->art()->_dbh()->set_session_parameters('NLS_NUMERIC_CHARACTERS' => $orig_NLS_NUMERIC_CHARACTERS->{NLS_NUMERIC_CHARACTERS});
		} else {
			my $handler = $self->_get_handler($self->art()->get_user_id('ROOT'));
			return undef unless defined $handler;
			$self->{PROPERTIES} = $handler->activity_selection(
				TYPE => 'HASHREF',
				ATTIVITA => [ $self->id() ],
				TDTA => $properties,
			)->[0];
		}
		# Elimino chiavi che non sono properties
		if(scalar @{$self->{INFO}->{PROPERTIES}} == 1 && !defined $self->{INFO}->{PROPERTIES}->[0]) {
			# se non ci sono property definite per l'attivita', activity_selection ritorna un array con un solo elemento settato undef...
			$self->{PROPERTIES} = {};
		} else {
			foreach my $key (keys %{$self->{PROPERTIES}}) {
				unless (grep /^$key$/, @{$self->{INFO}->{PROPERTIES}}){
					delete $self->{PROPERTIES}{$key} ;
				} else {
					my $tipo_ui = $self->art()->_lookup()->tipo_ui_tdta_nome($key);
					$self->{PROPERTIES}{$key} = '[]'
						if $tipo_ui =~/^(TAGS|MOOKUP)$/
							&& ! defined $self->{PROPERTIES}{$key};
				}

			}
		}
	}

	return $self->{PROPERTIES};
}

sub _get_children {
	my $self = shift;
	my %params = @_;
	my $id = $self->id();
	
	my @where = ();
	my $from = '';
	
	if ( defined $params{STATUS}  &&  scalar(@{$params{STATUS}}) > 0 ) {
		push @where, 'a.stato_corrente IN (' . join(",", map { "'".$self->art()->get_activity_status_id($_)."'" } @{$params{STATUS}}) . ')';
	}
	
	if ( defined $params{ACTIVITY_TYPE_NAME}  &&  scalar(@{$params{ACTIVITY_TYPE_NAME}}) > 0 ) {
		for my $act (@{$params{ACTIVITY_TYPE_NAME}}){
			$self->art()->last_error("Bad ACTIVITY_TYPE_NAME ".$act)
				&& return undef
					unless $self->art()->test_activity_type_name($act);
		}
		
		$from.=', tipi_attivita ta ';
		push @where, 'ta.id_tipo_attivita = a.id_tipo_attivita';
		push @where, 'ta.nome_tipo_attivita IN (' . join(",", map { $self->art()->_dbh()->quote($_) } @{$params{ACTIVITY_TYPE_NAME}}) . ')';
	}
	
	if ( defined $params{BEHAVIOUR_EVENT}  &&  scalar(@{$params{BEHAVIOUR_EVENT}}) > 0 ) {
		# FIXME: capire se gestire i comportamenti
#		for my $act (@{$params{BEHAVIOUR}}){
#			$self->art()->last_error("Bad ACTIVITY_TYPE_NAME ".$act)
#				&& return undef
#					unless $self->art()->test_activity_type_name($act);
#		}
		
		$from.=', v_tipi_attivita_behaviour vtab ';
		push @where, 'vtab.id_tipo_attivita = a.id_tipo_attivita';
		push @where, 'vtab.event_label IN (' . join(",", map { $self->art()->_dbh()->quote($_) } @{$params{BEHAVIOUR_EVENT}}) . ')';
	}
	
	my $where = '';
	$where = 'and ' . join("\n\t\tAND ", @where) if scalar(@where);
	
	my $sql = "
		select		distinct apaf.figlio
		from		a_padre_a_figlio apaf
					, attivita a
					,permission_sistemi ps
					$from
		where		apaf.padre = $id
			and apaf.figlio = a.id_attivita
			and ps.id_sistema = a.id_sistema
			and ps.id_gruppo_abilitato in (".join (',', @{$self->art()->user()->su()->activity()->groups()}).")
			$where
		order by	1
	";
	
	my $children = $self->art()->_dbh()->fetchall_arrayref($sql);
	
	return [ ] unless defined $children;
	return [ map { $_->[0] } @$children ];
}

=head2 I<API::ART::Activity>->B<new>( ART => I<API::ART> , ID => I<activity_id> [ , SAVE_ALL_PROPERTIES => I<save> ] )

Il metodo B<new()> e' il costruttore di classe e richiede due argomenti obbligatori:

=over 4

=item B<ART>

Un'istanza della classe API::ART

=item B<ID>

L'ID dell'attivita' da utilizzare

=item B<SAVE_ALL_PROPERTIES>

(default: 1) se impostato a 0 B<non salva> tutte le PROPERTIES che rimangono invariate

=back

=cut
sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	#
	# ART
	# ID
	#
	# Sintassi
	my $usage = sub {
		my $errmsg = shift;
		my $msg    = "";
		$msg .= "$class : $errmsg\n\n" if defined $errmsg;
		$msg .= "Usage:\n";
		$msg .= "\t$class";
		$msg .= '->new( ART => API::ART->new(), ID => $id )';
		$msg .= "\n\n";
		croak $msg;
	};

	# Controlli sui parametri
	$usage->('Missing ART!') unless ref($params->{ART}) eq 'API::ART';
	$usage->('Missing ID!') unless defined $params->{ID};
	
	my $self  = $class->SUPER::new();
	
	$self->{ART} = $params->{ART};
	$self->{ID} = $params->{ID};
	$self->{SAVE_ALL_PROPERTIES} = $params->{SAVE_ALL_PROPERTIES};
	$self->{ART}->clear_last_error();

	return undef unless $self->info();
	
	my $classSystem = eval '$' . ref($self) . '::DEFAULT_SYSTEM_CLASS';
	
	# se si tratta di un HASH la chiave deve essere valorizzata con il NOME_TIPO_SISTEMA dell'attivita,
	#    e il valore è il nome della classe che rimappa il sistema (es: {CANTIERE => 'WPSOWORKS::System::CANTIERE'})
	if(ref($classSystem) eq 'HASH') {
		$classSystem = defined $classSystem->{$self->{INFO}->{SYSTEM_TYPE_NAME}} ? $classSystem->{$self->{INFO}->{SYSTEM_TYPE_NAME}} : undef;
	}
	$classSystem = $DEFAULT_SYSTEM_CLASS unless defined $classSystem;
	
	CLASS: {
		local $@;
		eval "require $classSystem;";
		if ($@){
			$self->art()->last_error("Unable to require DEFAULT_SYSTEM_CLASS ($classSystem): " .$@);
			return undef;
		}
	}
	
	$self->{SYSTEM} = $classSystem->new( ART => $self->{ART}, ID => $self->{INFO}->{SYSTEM_ID});
	return undef unless defined $self->{SYSTEM}; # last_error() viene impostato da API::ART::System ;-)
	
	# FIXME: capire come gestire con attuali behaviour
	my $sql = "
		select event_label
			, behaviour_label
			, behaviour_class
		From v_tipi_attivita_behaviour
		where id_tipo_attivita = ?
			and disable_date is null
		order by execution_order
	";

	$self->{ART}->_create_prepare(__PACKAGE__.'_new_behav', $sql);
	
	my @bind_values = (
		$self->{INFO}->{ACTIVITY_TYPE_ID}
	);
	
	my $behs = $self->{ART}->_create_prepare(__PACKAGE__.'_new_behav')->fetchall_hashref(@bind_values);
	
	# normalizzo i risultati per avere un hash leggibile facilmente
	$self->{BEHAVIOURS} = {};
	for my $b (@{$behs}){
		if (exists $self->{BEHAVIOURS}->{$b->{EVENT_LABEL}}){
			push @{$self->{BEHAVIOURS}->{$b->{EVENT_LABEL}}},{ LABEL => $b->{BEHAVIOUR_LABEL}, CLASS => $b->{BEHAVIOUR_CLASS}};
		} else {
			$self->{BEHAVIOURS}->{$b->{EVENT_LABEL}} = [{ LABEL => $b->{BEHAVIOUR_LABEL}, CLASS => $b->{BEHAVIOUR_CLASS}}];
		}
	}
	
	my $sql_gruppi_contatti = "
		select distinct gc.id,g.nome, gc.label
		from gruppi_contatti gc
			join tipi_attivita_stati_contatti tas on tas.id_gruppo_contatto = gc.id
			join gruppi g on g.id_gruppo = gc.id_gruppo
		where tas.id_tipo_attivita = ?
	";
	
	$self->{ART}->_create_prepare(__PACKAGE__.'_new_concts', $sql_gruppi_contatti);
	
	my @bind_values_groups = (
		$self->{INFO}->{ACTIVITY_TYPE_ID}
	);
	
	my $contatti = $self->{ART}->_create_prepare(__PACKAGE__.'_new_concts')->fetchall_hashref(@bind_values_groups);
	
	# normalizzo i risultati per avere un hash leggibile facilmente
	$self->{GROUPS_CONTACTS} = {};
	for my $b (@{$contatti}){
		$self->{GROUPS_CONTACTS}->{$b->{NOME}}->{$b->{LABEL}} = $b->{ID};
	}

	return bless( $self, $class );
}


=head2 I<object>->B<art>()

Ritorna il riferimento all'oggetto API::ART passato come argomento al costruttore.

=cut
sub art { return $_[0]->{ART} }


=head2 I<object>->B<id>()

Ritorna l'ID dell'attivita'.

=cut
sub id { return $_[0]->{ID} }


#=head2 I<object>->B<id>()
#
#Ritorna l'ID dell'attivita'.
#
#=cut
#sub default_child_class { return $_[0]->{DEFAULT_CHILD_CLASS} }


=head2 I<object>->B<info>( [I<scalar>] ), I<object>->B<cached_info>( [I<scalar>] )

B<info>() ritorna informazioni sull'istanza dell'oggetto API::ART::Activity rileggendole dal DB.

B<cached_info>() ritorna le ultime informazioni sull'istanza dell'oggetto API::ART::Activity senza rileggerle dal DB.

Senza argomenti ritornano un I<hashref> contenente tutte le informazioni disponibili:

=over 4

=item B<ACTIVITY_ID>

ID dell'attivita' corrente (sinonimo del metodo B<id()>)

=item B<ACTIVITY_TYPE_ID>

ID della tipologia dell'attivita'

=item B<ACTIVITY_TYPE_NAME>

Tipologia dell'attivita'

=item B<CALENDAR_ID>

Id del calendario eventualmente associato per il calcolo dell'aging

=item B<CALENDAR_LABEL>

Label del calendario eventualmente associato per il calcolo dell'aging

=item B<SYSTEM_ID>

ID del sistema a cui e' associata l'attivita'

=item B<SYSTEM_TYPE_ID>

ID della tipologia del sistema a cui e' associata l'attivita'

=item B<SYSTEM_TYPE_NAME>

Tipologia del sistema a cui e' associata l'attivita'

=item B<CREATION_DATE>

Data di creazione dell'attivita' espressa nel formato definito da API::ART::Info('DEFAULT_DATE_FORMAT')

=item B<CREATION_USER_ID>

ID dell'utente che ha creato l'attivita'

=item B<CREATION_USER_NAME>

Login dell'utente che ha creato l'attivita'

=item B<DESCRIPTION>

Descrizione dell'attivita'

=item B<STATUS_ID>

ID dello stato corrente

=item B<STATUS_NAME>

Nome dello stato corrente

=item B<LAST_VAR_DATE>

Data dell'ultima variazione apportata all'attivita' espressa nel formato definito da API::ART::Info('DEFAULT_DATE_FORMAT')

=item B<OWNER_USER_ID>

ID dell'utente che ha apportato l'ultima variazione all'attivita'

=item B<OWNER_USER_NAME>

Login dell'utente che ha apportato l'ultima variazione all'attivita'

=item B<CURRENT_USER_ID>

ID dell'utente a cui e' stata assegnata la gestione deall'attivita'

=item B<CURRENT_USER_NAME>

Login dell'utente a cui e' stata assegnata la gestione deall'attivita'

=item B<PROPERTIES>

Ritorna un I<arrayref> con l'elenco dei nomi delle proprieta' definite per l'attivita'

=item B<CHILDREN>

Ritorna un I<arrayref> con l'elenco degli ID delle attivita' figlie

=item B<IS_LOCKED>

Ritorna 1 se l'attività risulta essere lockata

=item B<IS_LOCKED_BY_MYSELF>

Ritorna 1 se l'attività risulta essere lockata dall'operatore/sessione corrente

=item B<AGING>
Hash che contiene le informazioni relative all'aging

=over 4

=item B<AGING_UPDATE>
Ritorna 1 se l'attività può essere aggiornata a livello di aging

=item B<AGING_SECONDS>
Ritorna il valore dell'aging in secondi

=item B<AGING_INTERVAL>
Ritorna il valore dell'aging in intervallo

=item B<AGING_LAST_VAR_DATE>
Ritorna la data di ultimo aggiornamento dell'aging

=item B<CALENDAR_ID>
Ritorna l'identificativo del calendario di riferimento su cui viene calcolao l'aging

=item B<CALENDAR_LABEL>
Ritorna l'etichetta del calendario di riferimento su cui viene calcolao l'aging

=item B<CALENDAR_DESCRIPTION>
Ritorna la descrizione estesa del calendario di riferimento su cui viene calcolao l'aging

=item B<SLA>
Array con gli SLA configurati per l'attività

=over 4

=item B<TARGET_LABEL>
Ritorna l'etichetta dello sla impostato

=item B<TARGET_LEVEL>
Ritorna il livello dello sla impostato. Può valere INFO, WARNING, DANGER

=item B<TARGET_TO>
Indice di partenza dello sla

=item B<TARGET_FROM>
Indice di arrivo dello sla

=item B<INSERT_DATE>
Data di inserimento dello sla

=back

=item B<IN_FINAL_STATUS>

Ritorna 1 se l'attività è in uno stato finale

=item B<IN_PARKED_STATUS>

Ritorna 1 se l'attività è nello stato di "parcheggio"

=back

=back

=cut
sub info {
	my ($self, $key) = @_;
	return $self->_which_info((defined $key ? $key : undef), A_INFO_FRESH);
}
sub cached_info {
	my ($self, $key) = @_;
	return $self->_which_info((defined $key ? $key : undef), A_INFO_CACHED);
}


=head2 I<object>->B<status_id>(), I<object>->B<status_name>()

Ritornano rispettivamente l'ID ed il nome dello stato corrente dell'attivita'.

=cut
sub status_id { $_[0]->info('STATUS_ID') }
sub status_name { $_[0]->art()->get_activity_status_name($_[0]->status_id()) }


=head2 I<object>->B<property>( [ I<property1> [ , I<property2> [ , I<propertyN> ] ] ] )

Se viene passato il nome di una sola proprieta' ritorna uno I<scalar>.

Se vengono passati piu' nomi di proprieta' oppure senza argomenti, ritorna un I<hashref>.

=cut
sub property {
	my $self = shift;
	my @keys = @_;
	my $property = $self->_property(@keys);
	if ( scalar(@keys) == 1 ) {
		return $property->{$keys[0]};
	} else {
		return $property;
	}
}

=head2 I<object>->B<activity_property>( [ I<property1> [ , I<property2> [ , I<propertyN> ] ] ] )

Ritorna le ACTIVITY PROPERTIES definite nel tipo attivita'

Se viene passato il nome di una sola proprieta' ritorna uno I<scalar>.

Se vengono passati piu' nomi di proprieta' oppure senza argomenti, ritorna un I<hashref>.

=cut
sub activity_property {
	my $self = shift;
	my @keys = @_;
	my $act_props_conf = $self->art()->get_activity_properties_group(ACTIVITY_TYPE_NAME => [$self->cached_info('ACTIVITY_TYPE_NAME')]);
	my $act_props = [];
	for my $p (@{$act_props_conf}){
		for my $p_hash (@{$p->{PROPERTIES}}){
			push @{$act_props} , $p_hash->{NAME} if ! grep { $_ eq $p_hash->{NAME} } @{$act_props} ;
		}
	}
	
	my $err=undef;
	
	my @keys_filter = ();
	if ( scalar(@keys) == 0 ) {
		@keys_filter = @{$act_props};
	} else {
		for my $p (@keys){
			unless (grep {$_ eq $p} @{$act_props}){
				$err.="TDTA ".$p." non presente nell'elenco delle ACTIVITY PROPERTY!\n";
			} else {
				push @keys_filter, $p;
			}
		}
	}
	
	croak ($err) if defined $err;

	my $ret;	
	if(scalar @keys == 1) {
		# siccome nella richiesta hai esplicitamente richiesto una sola activity property ti ritorno uno scalar
		$ret = $self->property(@keys_filter);
	} else {
		my $props = $self->property(@keys_filter);
		if(scalar @keys_filter == 1) {
			$ret = { $keys_filter[0] => $props };
		} else {
			$ret = $props;
		}
	}
	return $ret;
}

=head2 I<object>->B<activity_property_group>(  )

Ritorna le ACTIVITY PROPERTIES con il loro raggruppamento

=over 4

=item La struttura risultante e' la seguente:

	<arrayref> => [
			{
				GROUP => <scalar>,
				ID => <scalar>,
				PROPERTIES => [
					{
						'NAME' => <scalar>,
						'VALUE' => <scalar>,
						'READ_ONLY' => <Y|N>,
						'EXPIRED' => <Y|N>,
						'NOT_YET_USED' => <Y|N>
					}
				]
			}, ...
	]

=back

=cut
sub activity_property_group {
	my $self = shift;
	my $activity_property = $self->activity_property();
	
	my $history = $self->history();
	return undef
    	unless $history;
	
	my @ret = ();
	
	while( my $transition = $history->next() ) {
		my $keys = $self->art()->enum_activity_property(ACTIVITY_TYPE_NAME => $self->cached_info('ACTIVITY_TYPE_NAME'), ACTION => $transition->action_name());
		
		return undef unless $keys;
		
		for my $key (keys %{$keys}){
			push @ret, $key unless grep {$_ eq $key} @ret;
		}
	}
	
	my $act_props_conf = $self->art()->get_activity_properties_group(ACTIVITY_TYPE_NAME => [$self->cached_info('ACTIVITY_TYPE_NAME')]);
	
	my $act_props = [];
	for my $p (@{$act_props_conf}){
		my $tmp;
		
		for my $k (keys %{$p}){
			if ($k ne 'PROPERTIES'){
				$tmp->{$k} = $p->{$k};
			} else {
				my $tmp_props = [];
				for my $p_hash (@{$p->{$k}}){
					next if (!exists $activity_property->{$p_hash->{NAME}});
					$p_hash->{VALUE} = $activity_property->{$p_hash->{NAME}};
					$p_hash->{NOT_YET_USED} = (grep {$_ eq $p_hash->{NAME}} @ret) ? 'N' : 'Y';
					push @{$tmp_props}, $p_hash;
				}
				$tmp->{$k} = $tmp_props;
			}
		}
		push @{$act_props}, $tmp;
	}
	
	return $act_props;
	
}

=head2 I<object>->B<get_dest_users>( ACTION_NAME => <scalar> [,STATUS_NAME => <scalar>] )

Ritorna l'elenco degli user a cui e' assegnabile l'attivita' data un'azione. Se non e' passato lo stato considera lo stato corrente.

=over 4

=item La struttura risultante e' la seguente:

	<arrayref> => [
          {
            'MOBILE_PHONE' => undef,
            'FIRST_NAME' => 'IVAN',
            'NAME' => 'LONGHI',
            'DISABLED' => undef,
            'SERVICE_USER' => undef,
            'LAST_NAME' => 'LONGHI',
            'GROUPS' => [
                          '60'
                        ],
            'EMAIL' => '<EMAIL>'
          }
        ]

=back

=cut

sub get_dest_users {
	my ( $self, %params ) = @_;
	
	croak 'Missing ACTION_NAME' unless defined $params{ACTION_NAME};
	
	my $action_name = $params{ACTION_NAME};
	
	croak 'Invalid ACTION_NAME!' unless $self->art()->test_activity_action_name($action_name);
	
	croak 'ACTION_NAME '.$action_name.' not assignable!' unless $self->art()->is_action_name_assignable($action_name);
	
	my $status_name = $params{STATUS_NAME} ? $params{STATUS_NAME} : $self->status_name;
	
	croak 'Invalid STATUS_NAME!' unless $self->art()->test_activity_status_name($status_name);
	
	my @ret;
	
	if ($action_name eq $self->art()->_get_activity_virtual_action_assign_name()){
		$status_name = $self->art()->_get_activity_virtual_status_any_status_name();
	}
	
	my $dest_status_id = $self->art()->test_permission(
		ACTIVITY_TYPE_ID => $self->{INFO}->{ACTIVITY_TYPE_ID},
		STATUS_ID => $self->art()->get_activity_status_id($status_name),
		ACTION_ID => $self->art()->get_activity_action_id($action_name),
		GROUPS_ID => [$self->art()->get_group_id('ROOT')]
	);
	
	if (defined $dest_status_id){
		my $action_permesse =  $self->art()->enum_activity_action(ACTIVITY_TYPE_ID => $self->{INFO}->{ACTIVITY_TYPE_ID}, INITIAL_STATUS_ID => $dest_status_id, SKIP_PERMISSIONS => 1);
		
		my @gruppi_destinatari;
		
		for my $action_permessa (keys %{$action_permesse}){
			my $gruppi_azione = $self->art()->get_allowed_groups_by_action(ACTIVITY_TYPE_ID => $self->{INFO}->{ACTIVITY_TYPE_ID}, ACTION_NAME => $action_permessa, STATUS_ID => $dest_status_id);
			return undef unless $gruppi_azione;
			foreach my $gruppo_azione (@{$gruppi_azione}){
				next if $self->art()->get_group_info($gruppo_azione)->{ROOT} || $self->art()->get_group_info($gruppo_azione)->{ADMIN};
				push @gruppi_destinatari, $gruppo_azione unless grep {$_ eq $gruppo_azione} @gruppi_destinatari;
			}
		}
		
		my @list_users;
		
		my $system_groups = $self->system()->info('GROUPS');
		
		for my $gruppo_destinatario (@gruppi_destinatari){
			USER: for my $user (@{$self->art()->get_group_users($gruppo_destinatario)}){
				# se l'utente è ROOT non viene considerato
				next if $self->art()->get_user_name($user) eq 'ROOT';
				# se l'utente è gia' presente non devo riaggiungerlo
				next if grep {$_ eq $user} @list_users;
				
				# solo gli utenti che hanno visibilita' sul sistema possono essere aggiunti
				for my $g (@{$system_groups}){
					if (grep {$_ eq $self->art()->get_group_id($g)} @{$self->art()->get_user_groups($user)}){
						push @list_users, $user;
						next USER;
					}
				}
			}
		}
	
		@ret = grep {!$_->{DISABLED}} map {$self->art()->get_user_info($_)} @list_users;
		
		@ret = sort {
			$a->{LAST_NAME} cmp $b->{LAST_NAME} or
			$a->{FIRST_NAME} cmp $b->{FIRST_NAME} or
			$a->{EMAIL} cmp $b->{EMAIL}
		} @ret;
	}
	return \@ret;
}

=head2 I<object>->B<step>( ACTION=>I<scalar> [, DESCRIPTION=>I<scalar>, DATE=>I<scalar>, DATE_FORMAT=>I<scalar>, DEST_USER=>I<scalar>, PROPERTIES=>I<hashref>, ATTACHMENTS=>I<arrayref> ] )

Esegue lo step dell'attivita' eseguendo l'azione specificata dall'argomento B<ACTION>.

Di seguito il significato degli argomenti del metodo B<step()>:

=over 4

=item B<ACTION>

Nome dell'azione da eseguire

=item B<DESCRIPTION>

Descrizione da associare allo step; se omesso verra' utilizzato: "Azione eseguita con 'NOME_SCRIPT'"

=item B<DATE>

Data di esecuzione dell'azione; se omesso verra' utilizzato I<sysdate>.

B<ATTENZIONE!> - Se si omette B<DATE>, lo step verra' eseuito nuovamente nel caso in cui fallisca per la
violazione del I<constraint> sulla I<primary key> della tabella I<STATI_ESTRAZIONE> incrementando la
I<sysdate> di un secondo.

=item B<DATE_FORMAT>

Formato da utilizzare per l'interpretazione di B<DATE>; se omesso verra' utilizzato API::ART::Info('DEFAULT_DATE_FORMAT')

=item B<DEST_USER> I<scalar>

Nome dell'utente a cui deve essere assegnata l'attivita'

=item B<ASSIGN_NOTIFICATION> I<0|1>

In presenza del parametro DEST_USER indica se deve essere notificata l'assegnazione all'utente assegnatario (se presente il supporto per le email: $art->email_support()).

=item B<PROPERTIES> I<hashref>

Un I<hashref> delle proprieta' da associare all'azione B<ACTION>

=item B<ATTACHMENTS> I<arrayref>

Un I<arryref> con i path (assoluti o relativi) dei file da allegare all'azione B<ACTION>

=item B<SAVE_ALL_PROPERTIES>

(default: 1) se impostato a 0 non salva tutte le PROPERTIES che rimangono invariate

=item B<IGNORE_FINAL_STATUS>

(default: 0) se impostato a 1 non vengono applicati i controlli per verificare se l'attivita' puo' essere chiusa

=item B<BEHAVIOUR_EVENT>

Se valorizzato indica che lo step deriva da un comportamento impostato e conseguentemente effettuerà solo quel comportamento e non tutti quelli definiti

=item B<USER> I<scalar>

Nome dell'utente che deve effettuare lo step. Parametro disponibile solo se API::ART: è istanziata da un operatore ADMIN

=back

=cut
sub step {
	my ( $self, %params ) = @_;
	
	$self->art()->last_error("Missing param ACTION")
		&& return undef
			unless defined $params{ACTION};
	
	if (defined $params{PROPERTIES}) {
		my $lookup_properties = $self->art()->_lookup()->tdta_for_action($self->art()->_dbh(), NOME_TA => $self->{INFO}->{ACTIVITY_TYPE_NAME}, NOME_AZIONE => $params{ACTION});	
		
		$self->art()->last_error("PROPERTIES must be an hash-ref or undefined!")
			&& return 0
				unless ref($params{PROPERTIES}) eq 'HASH';
		
		foreach my $prop ( keys %{ $params{PROPERTIES} } ) {
			unless ($params{VIRTUAL}){
				my @caratt_step = grep {$_->{NOME_TDTA} eq $prop } @{$lookup_properties};
				
				delete $params{PROPERTIES}->{$prop} if (defined $caratt_step[0] && $caratt_step[0]->{SOLO_LETTURA} eq 'Y');
			}			
		}
	}
	
	$self->art()->clear_last_error();
	return $self->_step(%params);
}

=head2 I<object>->B<step_filtered>()

Richiama il metodo step effettuando il filtraggio delle PROPERTIES
in funzione delle properties richieste nello step.

Tutti gli altri parametri vengono passati allo step senza alcuna
elaborazione.

=cut

sub step_filtered {
	my ( $self, %params ) = @_;
	my $action_properties_info = $self->{ART}->get_action_properties(
		$self->type_name,
		$params{ACTION}
	);
	my $properties = $params{PROPERTIES};
	$params{PROPERTIES} = {
		map { $_ => $properties->{$_} }
		grep { defined $properties->{$_} }
		map { $_->{NOME_TDTA} }
		@{$action_properties_info}
	};
	return $self->step( %params );
}

=head2 I<object>->B<update_properties>( PROPERTIES=>I<hashref>, [, DESCRIPTION=>I<scalar>, DATE=>I<scalar>, DATE_FORMAT=>I<scalar>, DEST_USER=>I<scalar>, IGNORE_FINAL_STATUS=>I<0|1>, IGNORE_ALL_PROPERTIES_CHECKS=>I<0|1> ] )

Esegue lo step dell'attivita' eseguendo l'azione specializzata per l'aggiornamento delle properties.

Di seguito il significato degli argomenti del metodo B<update_properties()>:

=over 4

=item B<DESCRIPTION>

Descrizione da associare allo step; se omesso verra' utilizzato: "Update properties"

=item B<DATE>

Data di esecuzione dell'azione; se omesso verra' utilizzato I<sysdate>.

=item B<DATE_FORMAT>

Formato da utilizzare per l'interpretazione di B<DATE>; se omesso verra' utilizzato API::ART::Info('DEFAULT_DATE_FORMAT')

=item B<DEST_USER> I<scalar>

Nome dell'utente a cui deve essere assegnata l'attivita'. Se omesso viene mantenuto il DEST_USER corrente dell'attivita'

=item B<PROPERTIES> I<hashref>

Un I<hashref> delle proprieta' da aggiornare

=item B<IGNORE_FINAL_STATUS>

(default: 0) se impostato a 1 non vengono applicati i controlli per verificare se l'attivita' puo' essere chiusa

=item B<IGNORE_ALL_PROPERTIES_CHECKS>

(default: 0) se impostato a 1 non vengono applicati tutti i controlli sulle proprietà da aggiornare

=back

=cut

sub update_properties {
	my $self = shift;
	my %params = @_;
	$self->art()->clear_last_error();
	
	$self->art()->last_error("Activity already closed: unable to update properties")
		&& return undef
			if $self->is_closed();

	# verifico se l'utente ha i permessi per l'aggiornamento delle properties
	my $enum_activity_type = $self->art()->enum_activity_type(
		SHOW_ONLY_WITH_VISIBILITY => 1,
		EXTENDED_OUTPUT => 1
	);
	return undef unless defined $enum_activity_type;
	my @actsInfo = grep {$_->{ACTIVITY_TYPE_NAME} eq $self->cached_info('ACTIVITY_TYPE_NAME')} @{$enum_activity_type};
	$self->art()->last_error("User can not update activity properties")
		&& return undef
			unless $actsInfo[0]->{ACTIVITY_TYPE_AP_GROUPS_PERM};

	$self->art()->last_error("Bad PROPERTIES param!")
		&& return undef
			if  ! exists $params{PROPERTIES}
				|| ref ($params{PROPERTIES}) ne 'HASH'
				|| scalar(keys %{$params{PROPERTIES}}) == 0
				;
	
	my $act_props_conf = $self->activity_property_group();
	my $act_props = [];
	my $act_props_read_only = [];
	my $act_props_nyu = [];
	for my $p (@{$act_props_conf}){
		for my $p_hash (@{$p->{PROPERTIES}}){
			push @{$act_props}, $p_hash->{NAME};
			push @{$act_props_read_only} , $p_hash->{NAME} if $p_hash->{READ_ONLY} eq 'Y';
			push @{$act_props_nyu} , $p_hash->{NAME} if $p_hash->{NOT_YET_USED} eq 'Y';
		}
	}
	
	$params{IGNORE_FINAL_STATUS} = 0 if !exists $params{IGNORE_FINAL_STATUS} || !defined $params{IGNORE_FINAL_STATUS};
	$params{IGNORE_ALL_PROPERTIES_CHECKS} = 0 if !exists $params{IGNORE_ALL_PROPERTIES_CHECKS} || !defined $params{IGNORE_ALL_PROPERTIES_CHECKS};
	
	unless ($params{IGNORE_ALL_PROPERTIES_CHECKS}){
		for my $p (keys %{$params{PROPERTIES}}){
			$self->art()->last_error("Property ".$p." isn't an activity_property!")
				&& return undef
					unless grep {$_ eq $p} @{$act_props}
				;
			$self->art()->last_error("Property ".$p." is READ_ONLY!")
				&& return undef
					if grep {$_ eq $p} @{$act_props_read_only}
				;
			$self->art()->last_error("Property ".$p." cannot be update becase is NOT_YET_USED!")
				&& return undef
					if grep {$_ eq $p} @{$act_props_nyu}
				;
		}
	}
	
	my $step_param = {
		VIRTUAL => 1,
		ACTION => $self->art()->get_activity_action_update_properties_name(),
		PROPERTIES => $params{PROPERTIES},
		DEST_STATUS => $self->get_current_status_name()
	};
	
	$step_param->{DESCRIPTION} = $params{DESCRIPTION}||'Update properties';
	$step_param->{DATE} = $params{DATE} if defined $params{DATE};
	$step_param->{DATE_FORMAT} = $params{DATE_FORMAT} if defined $params{DATE_FORMAT};
	my $current_user = $self->info('CURRENT_USER_NAME');
	$step_param->{DEST_USER} = $current_user if defined $current_user;
	$step_param->{IGNORE_FINAL_STATUS} = $params{IGNORE_FINAL_STATUS} if $params{IGNORE_FINAL_STATUS};

	return $self->step(%{$step_param});
}


=head2 I<object>->B<rollback>()

Consente di riportare l'attivita' alle condizioni antecedenti l'ultimo B<step()> eseguito.

TODO L'unico gruppo di utenti abilitato ad eseguire questa azione e' B<ROOT>

=cut
# TODO rollback()
sub rollback {
	my $self = shift;
	
}


=head2 I<object>->B<is_locked>()

Restituisce 1 se l'attivita' risulta essere lockata

=cut
sub is_locked { 
	my $self = shift;
	my $r = undef;
	
	my $sql = qq/
		SELECT trab.activity_is_locked(
			? --ID_ATTIVITA
		)
		FROM DUAL
	/;
	$self->{ART}->_create_prepare(__PACKAGE__.'_is_locked', $sql);
	
	$r = $self->{ART}->_create_prepare(__PACKAGE__.'_is_locked')->fetch_minimalized($self->id());

	return $r;
}

=head2 I<object>->B<is_locked_by_myself>()

Restituisce 1 se l'attivita' risulta essere lockata dall'utente/sessione corrente

=cut
sub is_locked_by_myself { 
	my $self = shift;
	my $r = undef;
	my $sql = qq/
		SELECT	trab.activity_is_locked_by_myself( 
					?, --ID_ATTIVITA
					?, --ID_OPERATORE
					?  --ID_SESSIONE
				)
		FROM DUAL
	/;
	
	$self->{ART}->_create_prepare(__PACKAGE__.'_is_locked_by_myself', $sql);
	
	$r = $self->{ART}->_create_prepare(__PACKAGE__.'_is_locked_by_myself')->fetch_minimalized(
		$self->id(), 
		$self->art()->user()->id(),
		$self->art()->session_id()
	);
	return $r;
}

=head2 I<object>->B<lock>()

Consente di lockare l'attivita' in modo che non sia possibile apportare modifiche da parte di altri utenti.

Restituisce 1 se l'attività è stata lockata correttamente oppure se la stessa attività risultava già essere lockata
dallo stesso utente/sessione.

=cut
sub lock { 
	my $self = shift;
	my $r = undef;
	
	my $sql = "
		SELECT	trab.activity_lock( 
					?, 
					?,
					?
				)
		FROM DUAL
	";

	$self->art()->_create_prepare(__PACKAGE__.'_lock', $sql);
	
	my @bind_values = (
		$self->id(),
		$self->art()->user()->id(),
		$self->art()->session_id()
	);
	
	$r = $self->art()->_create_prepare(__PACKAGE__.'_lock')->fetch_minimalized(@bind_values);	
	
	$self->art->last_error('Activity locked by another user/session')
		&& return undef 
			unless $r;
	
	return $r;
}

=head2 I<object>->B<unlock>()

Consente di un-lockare l'attivita' in modo che sia possibile apportare modifiche da parte di altri utenti.

Restituisce 1 se l'attività è stata un-lockata.

=cut
sub unlock { 
	my $self = shift; 
	my $r = undef;
	
	my $sql = "
		SELECT	trab.activity_unlock( 
				?, 
				?,
				?
			)
		FROM DUAL
	";

	$self->art()->_create_prepare(__PACKAGE__.'_unlock', $sql);
	
	my @bind_values = (
		$self->id(),
		$self->art()->user()->id(),
		$self->art()->session_id()
	);
	
	$r = $self->art()->_create_prepare(__PACKAGE__.'_unlock')->fetch_minimalized(@bind_values);
	
	$self->art->last_error('Activity not locked or locked by another user/session')
		&& return undef 
			unless $r;
	
	return $r;
}


=head2 I<object>->B<snapshot>()

TODO Ritorna lo 'snapshot' dell'attivita' in un determinato istante.

=cut
# TODO snapshot()
sub snapshot { my $self = shift; 0 }


=head2 I<object>->B<attachment_list>()

Ritorna l'elenco degli allegati dell'attivita'.

  [
    {
      'SEQUENCE' => 0,
      'SIZE' => '964',
      'NOME_FILE_CLIENT' => 'tmp1.txt',
      'OWNER_FIRST_NAME' => 'ROOT',
      'TRANSITION_DATE' => '20190128104754',
      'REF_DATE' => undef,
      'REVISION' => undef,
      'TRANSITION_ID' => '20190128104754',
      'TITLE' => undef,
      'DOC_TYPE' => undef,
      'DOWNLOAD_COUNT' => undef,
      'OWNER_ID' => '1',
      'DESCRIPTION' => undef,
      'OWNER_NAME' => 'ROOT',
      'FILENAME' => 'tmp1.txt',
      'OWNER_LAST_NAME' => 'ROOT'
    }
  ]

=cut

sub attachment_list {

	my $self = shift;
	local $@;
	
	# utilizzando la history devo prima recuperare i suoi filtri e la posizione per reimpostarli alla fine
	# per lasciare la situazione as-is
	my $currentPosition;
	my $currentFilters;
	my $history = $self->{HISTORY};
	if (defined $history){
		$currentPosition = $history->position();
		$currentFilters = $history->get_filter();
	}
	
	my $transitionIterator = eval { $self->history(FILTERS => { HAS_ATTACHMENT => 1 }) };
	if ($@){
		# provo a ripristinare la history se non ce la faccio è fatale
		if (defined $history){
			$history = $self->history(FILTERS => $currentFilters);
			$history->goto($currentPosition)
				if ($history->count());
		}
		$self->art()->last_error($@);
		return undef;
	}

	unless (defined $transitionIterator){
		my $error = $self->art()->last_error();
		# provo a ripristinare la history se non ce la faccio è fatale
		if (defined $history){
			$history = $self->history(FILTERS => $currentFilters);
			$history->goto($currentPosition)
				if ($history->count());
		}
		$self->art()->last_error($error);
		return undef;
	}

	my @attachments = ();
	
	while( my $transition = $transitionIterator->next() ) {
		
		my $seq = 0;
		my $attachments_list = $transition->attachments()->list();
		
		unless ($attachments_list){
			my $error = $self->art()->last_error();
			# provo a ripristinare la history se non ce la faccio è fatale
			if (defined $history){
				$history = $self->history(FILTERS => $currentFilters);
				$history->goto($currentPosition)
					if ($history->count());
			}
			$self->art()->last_error($error);
			return undef;
		}
		
		push @attachments, @{$attachments_list};
	}

	if ($self->art()->last_error()){
		my $error = $self->art()->last_error();
		# provo a ripristinare la history se non ce la faccio è fatale
		if (defined $history){
			$history = $self->history(FILTERS => $currentFilters);
			$history->goto($currentPosition)
				if ($history->count());
		}
		$self->art()->last_error($error);
		return undef;
	}
	
	if (defined $history){
		$history = $self->history(FILTERS => $currentFilters);
		$history->goto($currentPosition)
			if ($history->count() && $currentPosition);
	}
	
	return \@attachments;
	
}

=head2 I<object>->B<get_attachment>( TRANSITION_ID => I<transition_id> , SEQUENCE => I<sequence> [ , TMPDIR => I<tmpdir> ] )

Restituisce il <filehandle> aperto del file allegato alla transition identificata dal I<transition_id> e dall'ordinale I<sequence>.
E<Egrave> necessario chiudere il <filehandle> dopo averlo utilizzato.
Opzionalmente puo' essere passato il parametro <tmpdir>.

=cut

sub get_attachment {

	my $self = shift;
	local $@;

	my $errmsg;
	my %params = @_;
	
	croak $errmsg
		unless	$self->check_named_params(
			 ERRMSG 				=> \$errmsg
			,IGNORE_EXTRA_PARAMS 	=> 0
			,PARAMS 				=> \%params
			,MANDATORY => {
				 TRANSITION_ID => { isa => 'SCALAR' }
				,SEQUENCE => { isa => 'SCALAR' }
			}
			,OPTIONAL => {
				TMPDIR	 => { isa => 'SCALAR' }
			}
		);
	
	my $transition = eval{ new API::ART::Activity::History::Transition(ACTIVITY => $self, ID => $params{TRANSITION_ID}); };
	$self->art()->last_error($@)
		&& return undef
			if $@;
	
	my @attparams = ($params{SEQUENCE});
	push @attparams, $params{TMPDIR}
		if defined $params{TMPDIR};
		
	my $fd_attachment = $transition->attachments->read( @attparams );
	return undef
		unless defined $fd_attachment;
		
	return $fd_attachment;

}

=head2 I<object>->B<delete_attachment>( TRANSITION_ID => I<transition_id> , SEQUENCE => I<sequence> )

Restituisce 1 in caso positivo, undef in caso negativo

=cut

sub delete_attachment {

	my $self = shift;

	my $errmsg;
	my %params = @_;
	
	croak $errmsg
		unless	$self->check_named_params(
			 ERRMSG 				=> \$errmsg
			,IGNORE_EXTRA_PARAMS 	=> 0
			,PARAMS 				=> \%params
			,MANDATORY => {
				 TRANSITION_ID => { isa => 'SCALAR' }
				,SEQUENCE => { isa => 'SCALAR' }
			}
		);
	
	my $transition = eval{ new API::ART::Activity::History::Transition(ACTIVITY => $self, ID => $params{TRANSITION_ID}); };
	$self->art()->last_error($@)
		&& return undef
			if $@;
	
	return undef
		unless $transition->attachments->delete( $params{SEQUENCE} );

	return $self->refresh();

}


=head2 I<object>->B<history>( [ B<FILTERS> => I<hashref>, B<MERGE_CHILDREN> => I<boolean> ] )

Ritorna l'oggetto I<history> di tipo API::ART::Activity::History a cui afferisce l'attivita'.

Opzionalmente accetta l'argomento I<FILTERS> => <hashref> per ottenere le transizioni filtrate e
l'argomento I<MERGE_CHILDREN> => <boolean> per ottenere anche i figli creati

=cut

sub history {
	my ($self, %params) = @_;
	my $errmsg;

	croak $errmsg
		unless	$self->check_named_params(
			 ERRMSG 				=> \$errmsg
			,IGNORE_EXTRA_PARAMS 	=> 0
			,PARAMS 				=> \%params
			,MANDATORY				=> { }
			,OPTIONAL				=> {
				FILTERS	=> { isa => 'HASH' },
				MERGE_CHILDREN	=> { isa => 'SCALAR', list => [0,1] }
			}
		);
	
	$params{MERGE_CHILDREN} = 0 unless defined $params{MERGE_CHILDREN};
	$params{FILTERS} = {} unless defined $params{FILTERS};
	
	if ( $self->{HISTORY} ) {
		$self->{HISTORY}->set_filter(%{$params{FILTERS}}) || return undef;
		$self->{HISTORY}->set_merge_children($params{MERGE_CHILDREN}) || return undef;
	} else {
		$self->{HISTORY} = eval {API::ART::Activity::History->new(ACTIVITY => $self, FILTERS => $params{FILTERS}, MERGE_CHILDREN => $params{MERGE_CHILDREN})};
		$self->art->last_error($@)
			&& return undef
				if $@;
		return undef unless $self->{HISTORY};
 	}

	return $self->{HISTORY};
}


=head2 I<object>->B<is_active>()

Ritorna 1 se l'attivita' non e' CHIUSA o in PARCHEGGIO.

=cut
sub is_active {
	my $self = shift;
	return !$self->is_closed() && !$self->is_parked();
}


=head2 I<object>->B<is_parked>()

Ritorna 1 se l'attivita' e' in PARCHEGGIO.

=cut
sub is_parked {
	my $self = shift;
	return $self->cached_info('IN_PARKED_STATUS');
	# parcheggio?
	#return ($self->art()->_get_activity_virtual_status_park_id() eq $self->info('STATUS_ID'));
}


=head2 I<object>->B<is_closed>()

Ritorna 1 se l'attivita' e' nello stato CHIUSA.

=cut
sub is_closed {
	my $self = shift;
	return $self->cached_info('IN_FINAL_STATUS');
	#return $self->art()->is_activity_final_status( ID => $self->info('STATUS_ID') );
}


=head2 I<object>->B<can_park>()

Ritorna 1 se l'attivita' puo' essere PARCHEGGIATA.

=cut
sub can_park {
	my $self = shift;
	return !$self->is_parked();
}


=head2 I<object>->B<park>( [ I<DESCRIPTION> => I<scalar>] )

Mette l'attivita' nello stato di PARCHEGGIO.

=cut
sub park {
	my $self = shift;
	my %params = @_;
	$self->art()->clear_last_error();
	#DESCRIPTION
	$params{DESCRIPTION} = 'Richiesta sospensione' unless defined $params{DESCRIPTION};
	$self->art()->last_error("Can not PARK the activity!")
		&& (return undef)
			unless $self->can_park();
	$self->art()->last_error("PARK action allowed only for ROOT users!")
		&& (return undef)
			unless $self->art()->user()->is_admin();
	return $self->_step(
		VIRTUAL		=> 1,
		ACTION		=> $self->art()->_get_activity_virtual_action_park_name(),
		DEST_STATUS	=> $self->art()->_get_activity_virtual_status_park_name(),
		DESCRIPTION	=> $params{DESCRIPTION},
		DEST_USER	=> $self->art()->user()->name(),
	);
}


=head2 I<object>->B<can_restore>()

Ritorna 1 se l'attivita' puo' essere RIPRISTINATA dallo stato di PARCHEGGIO.

=cut
sub can_restore {
	my $self = shift;
	return $self->is_parked();
}


=head2 I<object>->B<restore>( [ I<DESCRIPTION> => I<scalar>] )

Ripristina lo stato antecedente la messa in PARCHEGGIO dell'attivita': richiede che l'attivita' sia nello stato di PARCHEGGIO.

=cut
sub restore {
	my $self = shift;
	my %params = @_;
	$self->art()->clear_last_error();
	#DESCRIPTION
	$params{DESCRIPTION} = 'Ripristino' unless defined $params{DESCRIPTION};
	$self->art()->last_error("Can not RESTORE the activity!")
		&& (return undef)
			unless $self->can_restore();
	$self->art()->last_error("RESTORE action allowed only for ROOT users!")
		&& (return undef)
			unless $self->art()->user()->is_admin();
	
	my $sql = "
		SELECT *
		FROM   (SELECT   sa.id_stato_risultante
						,sa.id_operatore_corrente
				FROM     storia_attivita sa
				WHERE    sa.id_attivita = ?
						AND sa.data_esecuzione < (SELECT MAX (sa1.data_esecuzione)
												FROM   storia_attivita sa1
												WHERE  sa1.id_attivita = sa.id_attivita)
				ORDER BY sa.data_esecuzione DESC)
		WHERE  ROWNUM = 1
	";

	$self->art()->_create_prepare(__PACKAGE__.'_restore', $sql);
	
	my @bind_values = (
		$self->id()
	);
	my ($dest_status_id, $dest_user_id) = $self->art()->_create_prepare(__PACKAGE__.'_restore')->fetch_minimalized(@bind_values);
	return $self->_step(
		VIRTUAL		=> 1,
		ACTION		=> $self->art()->_get_activity_virtual_action_restore_name(),
		DEST_STATUS	=> $self->art()->get_activity_status_name($dest_status_id),
		DESCRIPTION	=> $params{DESCRIPTION},
		DEST_USER	=> (defined $dest_user_id ? $self->art()->get_user_name($dest_user_id) : undef),
	);
}


=head2 I<object>->B<can_goto>()

Ritorna 1 se l'attivita' puo' essere portata in uno stato a piacimento: richiede che l'attivita' sia PARCHEGGIATA.

=cut
sub can_goto {
	my $self = shift;
	return $self->is_parked();
}

=head2 I<object>->B<goto>( I<DEST_STATUS> => $status_name [, I<DEST_USER> => $user_name, I<DESCRIPTION> => I<scalar>] )

Consente di mettere l'attivita' in uno stato a piacere: richiede che l'attivita' sia nello stato di PARCHEGGIO.

Se I<DEST_USER> viene omesso verra' impostato l'utente di API::ART.

=cut
sub goto {
	my $self = shift;
	my %params = @_;
	$self->art()->clear_last_error();
	$self->art()->last_error("Can not use GOTO action!")
		&& (return undef)
			unless $self->can_goto();
	$self->art()->last_error("GOTO action allowed only for ROOT users!")
		&& (return undef)
			unless $self->art()->user()->is_admin();
	# ACTION
	$params{ACTION} = $self->art()->_get_activity_virtual_action_goto_name();
	# DESCRIPTION
	$params{DESCRIPTION} = 'Ripristino forzato' unless defined $params{DESCRIPTION};
	# DEST_USER
	$params{DEST_USER} = $self->art()->user()->name() unless defined $params{DEST_USER};
	$self->art()->last_error("Unknown DEST_USER '$params{DEST_USER}'!")
		&& (return undef)
			unless $self->art()->test_user_name($params{DEST_USER});
	# DEST_STATUS
	$self->art()->last_error("Missing DEST_STATUS!")
		&& (return undef)
			unless defined($params{DEST_STATUS});
	$self->art()->last_error("Unknown DEST_STATUS '$params{DEST_STATUS}'!")
		&& (return undef)
			unless $self->art()->test_activity_status_name($params{DEST_STATUS});
	$self->art()->last_error("Can not accept DEST_STATUS '$params{DEST_STATUS}' because is a VIRTUAL STATUS!")
		&& (return undef)
			if $self->art()->_get_activity_virtual_status_park_name() eq $params{DEST_STATUS};
	# Verifica che il DEST_STATUS sia corretto per l'ACTIVITY_TYPE
	my $sql = "
		SELECT 1
			FROM   permission_action pa
			WHERE  pa.id_tipo_attivita = ?
				and pa.ID_STATO_FINALE = ?
				and rownum = 1
	";

	$self->art()->_create_prepare(__PACKAGE__.'_goto', $sql);
	
	my @bind_values = (
		$self->cached_info('ACTIVITY_TYPE_ID'),
		$self->art()->get_activity_status_id($params{DEST_STATUS})
	);
	
	$self->art()->last_error("Invalid DEST_STATUS '$params{DEST_STATUS}' for ACTIVITY_TYPE '${\$self->art()->get_activity_type_name($self->cached_info('ACTIVITY_TYPE_ID'))}'!")
		&& (return undef)
			unless $self->art()->_create_prepare(__PACKAGE__.'_goto')->fetch_minimalized(@bind_values);
	
	return $self->_step(
		VIRTUAL		=> 1,
		ACTION		=> $params{ACTION},
		DEST_STATUS	=> $params{DEST_STATUS},
		DESCRIPTION	=> $params{DESCRIPTION},
		DEST_USER	=> $params{DEST_USER},
	);
}


=head2 I<object>->B<can_loop>()

Ritorna 1 se l'attivita' puo' eseguire un LOOP sullo stato corrente: richiede che l'attivita' NON sia PARCHEGGIATA.

=cut
sub can_loop {
	my $self = shift;
	return $self->is_active();
}


=head2 I<object>->B<loop>( I<PROPERTIES> => I<hashref> [, I<DESCRIPTION> => I<scalar>] )

Consente di modificare i valori delle proprieta' dell'attivita' non modificando lo stato corrente: richiede che l'attivita' non sia nello stato di PARCHEGGIO.

=cut
sub loop {
	my $self = shift;
	my %params = @_;
	$self->art()->clear_last_error();
	$self->art()->last_error("Can not LOOP the activity!")
		&& (return undef)
			unless $self->can_loop();
	$self->art()->last_error("LOOP action allowed only for ROOT users!")
		&& (return undef)
			unless $self->art()->user()->is_admin();
	# DESCRIPTION
	$params{DESCRIPTION} = 'Loop per aggiornamento dati' unless defined $params{DESCRIPTION};
	# PROPERTIES
	$params{PROPERTIES} = {} unless defined $params{PROPERTIES};
	return $self->_step(
		VIRTUAL		=> 1,
		ACTION		=> $self->art()->_get_activity_virtual_action_loop_name(),
		DEST_STATUS	=> $self->art()->get_activity_status_name($self->info('STATUS_ID')),
		DESCRIPTION	=> $params{DESCRIPTION},
		PROPERTIES	=> $params{PROPERTIES},
		SAVE_ALL_PROPERTIES => $params{SAVE_ALL_PROPERTIES},
	);
}

=head2 I<object>->B<can_update>()

Ritorna 1 se l'attivita' puo' eseguire un UPDATE sullo stato corrente con aggiornamento
delle proprieta': richiede che l'attivita' NON sia PARCHEGGIATA.

=cut

sub can_update {
	my $self = shift;
	return $self->is_active();
}

=head2 I<object>->B<update>( DEST_STATUS=>I<scalar>, [ ACTION=>I<scalar>, DESCRIPTION=>I<scalar>, DEST_USER=>I<scalar>, PROPERTIES=>I<hashref>] )

Consente di effettuare uno step virtuale (di tipo virtual update) con la relativa
modifica delle properties.

=over 4

=item B<DEST_STATUS>

Nome dello stato destinazione.

=item B<ACTION>

Nome dell'azione da eseguire (deve essere di tipo virtual update).
Se non viene impostata viene utilizzata l'azione __UPDATE__.

=item B<DESCRIPTION>

Descrizione da associare allo step; se omesso verra' utilizzato: "Azione eseguita con 'NOME_SCRIPT'"

=item B<DEST_USER> I<scalar>

Nome dell'utente a cui deve essere assegnata l'attivita'

=item B<PROPERTIES> I<hashref>

Un I<hashref> delle proprieta' da associare all'azione B<ACTION>

=item B<SAVE_ALL_PROPERTIES>

(default: 1) se impostato a 0 non salva tutte le PROPERTIES che rimangono invariate

=item B<IGNORE_FINAL_STATUS>

(default: 0) se impostato permette di evitare i controlli su azioni virtuali di tipo update;
tramite tale forzatura (da usare consapevolmente) sara' possibile effettuare step virtuali
di tipo update a partire da stati finali.

=back

=cut

#TODO: tutte le azioni virtuali potrebbero ricadere sotto questo metodo
# loop, park, goto, ...
sub update {
	my $self = shift;
	my %params = @_;
	$self->art()->clear_last_error();
	
	unless ($params{IGNORE_FINAL_STATUS}){
		$self->art()->last_error("Can not use UPDATE actions!")
			&& (return undef)
				unless $self->can_update();
	}
			
	$self->art()->last_error("UPDATE actions allowed only for ROOT users!")
		&& (return undef)
			unless $self->art()->user()->is_admin();
	# ACTION
	$params{ACTION} = $self->art()->_get_activity_virtual_action_update_name()
		unless defined $params{ACTION};
	
	$self->art()->last_error("ACTION $params{ACTION} is not a virtual update status!")
		&& (return undef)
			unless $self->art()->test_action_type_virtual_update($params{ACTION});
	
	# DESCRIPTION
	#$params{DESCRIPTION} = 'UPDATE' unless defined $params{DESCRIPTION};
	# DEST_USER
	
	$params{DEST_USER} = $self->art()->user()->name() unless defined $params{DEST_USER};
	$self->art()->last_error("Unknown DEST_USER '$params{DEST_USER}'!")
		&& (return undef)
			unless $self->art()->test_user_name($params{DEST_USER});
	# DEST_STATUS
	$self->art()->last_error("Missing DEST_STATUS!")
		&& (return undef)
			unless defined($params{DEST_STATUS});
	$self->art()->last_error("Unknown DEST_STATUS '$params{DEST_STATUS}'!")
		&& (return undef)
			unless $self->art()->test_activity_status_name($params{DEST_STATUS});
	$self->art()->last_error("Can not accept DEST_STATUS '$params{DEST_STATUS}' because is a VIRTUAL STATUS!")
		&& (return undef)
			if $self->art()->_get_activity_virtual_status_park_name() eq $params{DEST_STATUS};
	# Verifica che il DEST_STATUS sia corretto per l'ACTIVITY_TYPE
	my $sql = "
		SELECT 1
			FROM   permission_action pa
			WHERE  pa.id_tipo_attivita = ?
				and pa.ID_STATO_FINALE = ?
				and rownum = 1
	";

	$self->art()->_create_prepare(__PACKAGE__.'_update', $sql);
	
	my @bind_values = (
		$self->cached_info('ACTIVITY_TYPE_ID'),
		$self->art()->get_activity_status_id($params{DEST_STATUS})
	);
	$self->art()->last_error("Invalid DEST_STATUS '$params{DEST_STATUS}' for ACTIVITY_TYPE '${\$self->art()->get_activity_type_name($self->cached_info('ACTIVITY_TYPE_ID'))}'!")
		&& (return undef)
			unless $self->art()->_create_prepare(__PACKAGE__.'_update')->fetch_minimalized(@bind_values);
	
	$params{PROPERTIES} = {} unless defined $params{PROPERTIES};
	
	return $self->_step(
		VIRTUAL						=> 1,
		ACTION						=> $params{ACTION},
		DEST_STATUS					=> $params{DEST_STATUS},
		DESCRIPTION					=> $params{DESCRIPTION},
		DEST_USER					=> $params{DEST_USER},
		PROPERTIES					=> $params{PROPERTIES},
		SAVE_ALL_PROPERTIES 		=> $params{SAVE_ALL_PROPERTIES},
		IGNORE_FINAL_STATUS 		=> $params{IGNORE_FINAL_STATUS},
		DISABLE_BINDING_INSIDE_STEP => $params{DISABLE_BINDING_INSIDE_STEP},
	);
}


=head2 I<object>->B<can_close>()

Ritorna 1 se l'attivita' puo' essere CHIUSA.

=cut
sub can_close {
	my $self = shift;
	
	return !$self->is_closed() && ($self->_is_standalone() || $self->children_are_closed());
}

sub _is_standalone {
	my $self = shift;
	
	return $self->art()->get_activity_type_behaviour(ACTIVITY_TYPE_ID=> $self->{INFO}->{ACTIVITY_TYPE_ID}, NAME => 'CHIUSURA_FIGLI');
}


=head2 I<object>->B<close>( [ I<DESCRIPTION> => I<scalar> ] )

Consente di chiudere l'attivita': richiede che l'attivita' sia nello stato di PARCHEGGIO.

=cut
sub close {
	my $self = shift;
	my %params = @_;
	$self->art()->clear_last_error();
	# DESCRIPTION
	$params{DESCRIPTION} = 'Chiusura forzata' unless defined $params{DESCRIPTION};
	$self->art()->last_error("Can not CLOSE activity!")
		&& (return undef)
			unless $self->can_close() && $self->is_parked();
	$self->art()->last_error("CLOSE action allowed only for ROOT users!")
		&& (return undef)
			unless $self->art()->user()->is_admin();
	return $self->goto(
		DESCRIPTION => $params{DESCRIPTION},
		ACTION => $self->art()->_get_activity_virtual_action_close_name(),
		DEST_STATUS => $self->art()->_get_activity_virtual_status_close_name(),
	);
}

=head2 I<object>->B<assign>( DEST_USER=>I<scalar>, [, DESCRIPTION=>I<scalar>, ASSIGN_NOTIFICATION=>I<0|1> ] )

Esegue l'assegnazione dell'attivita' eseguendo l'azione specializzata per l'assegnazione dell'attivita'

Di seguito il significato degli argomenti del metodo B<assign()>:

=over 4

=item B<DEST_USER> I<scalar>

Nome dell'utente a cui deve essere assegnata l'attivita'. Se omesso viene mantenuto il DEST_USER corrente dell'attivita'

=item B<DESCRIPTION>

Descrizione da associare allo step; se omesso verra' utilizzato: "Assign"

=item B<ASSIGN_NOTIFICATION>

(default: 0) se impostato a 1 viene inviata una mail per comunicazione l'assegnazione

=back

=cut

sub assign {
	my $self = shift;
	my %params = @_;
	$self->art()->clear_last_error();
	$params{DESCRIPTION} = 'Assign' unless defined $params{DESCRIPTION};
	$self->art()->last_error("Can not ASSIGN the activity!")
		&& (return undef)
			if $self->is_closed();
	$self->art()->last_error("Param DEST_USER mandatory!")
		&& (return undef)
			unless defined $params{DEST_USER};
	
	### Verifico se l'utente puo' fare l'azione di assegnazione
	my $action = $self->art()->enum_activity_action(EXTENDED_OUTPUT => 1, ACTIVITY_TYPE_ID => $self->cached_info()->{ACTIVITY_TYPE_ID});
	my @keys = keys %{$action};
	$self->art()->last_error("User can not ASSIGN!")
		&& (return undef)
			unless (grep {$_ eq $self->art()->_get_activity_virtual_action_assign_name()} @keys);

	return $self->_step(
		VIRTUAL		=> 1,
		ACTION		=> $self->art()->_get_activity_virtual_action_assign_name(),
		DEST_STATUS	=> $self->get_current_status_name(),
		DESCRIPTION	=> $params{DESCRIPTION},
		DEST_USER	=> $params{DEST_USER},
		ASSIGN_NOTIFICATION	=> defined $params{ASSIGN_NOTIFICATION} ? $params{ASSIGN_NOTIFICATION} : 0
	);
}

=head2 I<object>->B<deassign>( [, DESCRIPTION=>I<scalar>, ASSIGN_NOTIFICATION=>I<0|1> ] )

Esegue la deassegnazione dell'attivita' eseguendo l'azione specializzata per la deassegnazione dell'attivita'

Di seguito il significato degli argomenti del metodo B<deassign()>:

=over 4

=item B<DESCRIPTION>

Descrizione da associare allo step; se omesso verra' utilizzato: "Deassign"

=item B<ASSIGN_NOTIFICATION>

(default: 0) se impostato a 1 viene inviata una mail per comunicazione la deassegnazione

=back

=cut

sub deassign {
	my $self = shift;
	my %params = @_;
	$self->art()->clear_last_error();
	$params{DESCRIPTION} = 'Deassign' unless defined $params{DESCRIPTION};
	$self->art()->last_error("Can not DEASSIGN the activity!")
		&& (return undef)
			if $self->is_closed();
	$self->art()->last_error("Can not DEASSIGN activity not assigned!")
		&& (return undef)
			unless defined $self->info()->{CURRENT_USER_NAME};
	
	### Verifico se l'utente puo' fare l'azione di assegnazione
	my $action = $self->art()->enum_activity_action(EXTENDED_OUTPUT => 1, ACTIVITY_TYPE_ID => $self->cached_info()->{ACTIVITY_TYPE_ID});
	my @keys = keys %{$action};
	$self->art()->last_error("User can not DEASSIGN!")
		&& (return undef)
			unless (grep {$_ eq $self->art()->_get_activity_virtual_action_deassign_name()} @keys);
	
	return $self->_step(
		VIRTUAL		=> 1,
		ACTION		=> $self->art()->_get_activity_virtual_action_deassign_name(),
		DEST_STATUS	=> $self->get_current_status_name(),
		DESCRIPTION	=> $params{DESCRIPTION},
		DEST_USER	=> undef,
		ASSIGN_NOTIFICATION	=> defined $params{ASSIGN_NOTIFICATION} ? $params{ASSIGN_NOTIFICATION} : 0
	);
}

=head2 I<object>->B<get_children>( [STATUS => I<arrayref>, ACTIVITY_TYPE_NAME => I<arrayref>, BEHAVIOUR_EVENT => I<arrayref>] )

Ritorna un I<arrayref> di oggetti di tipo API::ART::Activity rappresentante le
attivita' "figlie" dell'attivita' corrente

L'argomento B<STATUS> e' un I<arrayref> di STATI.
L'argomento B<ACTIVITY_TYPE_NAME> e' un I<arrayref> di TIPI_ATTIVITA.
L'argomento B<BEHAVIOUR_EVENT> e' un I<arrayref> di COMPORTAMENTI.

=cut
sub get_children {
	my $self = shift;
	my %params = @_;
	local $@;
	
	if (defined $params{'STATUS'} && ref ($params{'STATUS'}) ne 'ARRAY'){
		$self->art()->last_error('Il parametro STATUS deve essere un ARRAY');
		return undef;
	}
	
	if (defined $params{'ACTIVITY_TYPE_NAME'} && ref ($params{'ACTIVITY_TYPE_NAME'}) ne 'ARRAY'){
		$self->art()->last_error('Il parametro ACTIVITY_TYPE_NAME deve essere un ARRAY');
		return undef;
	}

	if (defined $params{'BEHAVIOUR_EVENT'} && ref ($params{'BEHAVIOUR_EVENT'}) ne 'ARRAY'){
		$self->art()->last_error('Il parametro BEHAVIOUR deve essere un ARRAY');
		return undef;
	}
	
	# Trovo l'elenco dei "figli"
	my $children = $self->_get_children(%params);
	return undef unless defined $children;
	
	# se non ho alcun figlio ritorno direttamente array vuoto
	return []
		unless scalar @{$children};
	
	# Determino a quale classe fare riferimento per il nome della classe Collection da usare per la
	# creazione dell'istanza del 'figlio'
	my $class = eval '$' . ref($self) . '::DEFAULT_CHILDREN_CLASS';
	$self->art()->last_error(ref($self) . '::DEFAULT_CHILDREN_CLASS not defined')
		&& return undef
			unless defined $class;
	
	CLASS: {
		local $@;
		eval "require $class;";
		if ($@){
			$self->art()->last_error("Unable to require DEFAULT_ACTIVITY_CLASS ($class): " .$@);
			return undef;
		}
	}

	# Ritorno un arrayref con le istanze dei "figli" creati attraverso la corretta classe
	my $ret = [
		map {
			$class->new( ART => $self->art() , ID => $_ )
		} @$children
	];
	
	# svuotiamo last_error() in quanto il map precedente lo lascia settato se non riesce ad istanziare una o piu' attivita' (es: mancanza di permessi sull'attivita' figlia')
	$self->art()->clear_last_error();
	
	return $ret;
}


=head2 I<object>->B<children_count>( [STATUS => I<arrayref>, ACTIVITY_TYPE_NAME => I<arrayref>] )

Ritorna il numero di attivita' figlie

L'argomento B<STATUS> e' un I<arrayref> di STATI.
L'argomento B<ACTIVITY_TYPE_NAME> e' un I<arrayref> di TIPI_ATTIVITA.

=cut
sub children_count {
	my $self = shift;
	my $children = $self->_get_children(@_);
	return 0 unless defined $children;
	return scalar( @$children );
}


=head2 I<object>->B<add_child>( I<arguments> )

Crea una nuova attivita' figlio per l'attivita' corrente.

B<NOTA>: per gli argomenti da passare al metodo fare riferimento al metodo B<create()> della classe
B<API::ART::Collection::Activity>.

=cut
sub add_child {
	my $self = shift;
	my %params = @_;
	local $@;

	$self->art()->clear_last_error();

	# Verifico che l'attivita' corrente non sia gia' chiusa
	$self->art()->last_error('Parent activity already closed!')
		&& return undef
			if $self->is_closed();

	# Determino a quale classe fare riferimento per il nome della classe Collection da usare per la
	# creazione dell'istanza del 'figlio'
	my $current_class = eval '$' . ref($self) . '::DEFAULT_CHILDREN_CLASS';
	$self->art()->last_error(ref($self) . '::DEFAULT_CHILDREN_CLASS not defined')
		&& return undef
			unless defined $current_class;
	eval "require $current_class;";
	if ($@){
		$self->art()->last_error("Unable to load DEFAULT_CHILDREN_CLASS ($current_class) as specified by ".ref($self).": " .$@);
		return undef;
	}
	
	my $collection_class = eval "\$${current_class}::DEFAULT_COLLECTION_CLASS";
	$self->art()->last_error($current_class . '::DEFAULT_COLLECTION_CLASS not defined')
		&& return undef
			unless defined $collection_class;
	eval "require $collection_class;";
	if ($@){
		$self->art()->last_error("Unable to load DEFAULT_COLLECTION_CLASS ($collection_class) as specified by ".$current_class.": " .$@);
		return undef;
	}
	
	my $child = eval {
		$collection_class->new(
			ART => $self->art()
		)->create( %params );
	};
	if ($@){
		$self->art()->last_error("Unable to create child: " .$@);
		return undef;
	}
	return undef unless $child;
	return undef unless $self->adopt_children(
		CHILDREN => [ $child ],
	);
	return $child;
}


=head2 I<object>->B<type_id>(), I<object>->B<type_name>()

Ritornano rispettivamente l'ID ed il nome del tipo attivita'.

=cut
sub type_id {
	# Uso la property invece del metodo ->info() in quanto tale informazione non potra' mai cambiare
	$_[0]->cached_info('ACTIVITY_TYPE_ID');
}
sub type_name {
	$_[0]->art()->get_activity_type_name( $_[0]->type_id() );
}


=head2 I<object>->B<parent_id>()

Ritorna l'ID dell'attivita' "padre" o I<undef> se l'attivita' corrente non dipende da altre attivita'.

=cut
sub parent_id {
	$_[0]->info('PARENT_ACTIVITY_ID');
}

=head2 I<object>->B<parent>()

Ritorna un'istanza dell'attivita' padre.

=cut
sub parent {
	my $self = shift;
	my $pid = $self->parent_id();
	return undef unless defined $pid;
	
	# Determino a quale classe fare riferimento per il nome della classe Collection da usare per la
	# creazione dell'istanza del 'padre'
	my $class = eval '$' . ref($self) . '::DEFAULT_PARENT_CLASS';
	CLASS: {
		local $@;
		eval "require $class;";
		if ($@){
			$self->art()->last_error("Unable to require DEFAULT_PARENT_CLASS ($class): " .$@);
			return undef;
		}
	}
	
	return $class->new(
		ART => $self->art() ,
		ID => $self->parent_id()
	);
}

=head2 I<object>->B<test_action_properties>( I<ACTION> => <scalar>, I<PROPERTIES> => <hashref>, [ I<MISSING_REQUIRED_PARAMS> => <arrayref>, I<EXTRA_PARAMS> <arrayref>, I<IGNORE_EXTRA_PARAMS> => <boolean> ] )

Ritorna un I<boolean> se le B<PROPERTIES> inerenti all'attivita' ed interessati dall'azione specificata da B<ACTION> (I<come nome oppure id>) soddisfano i requisiti dei TDTA, e non ci siano parametri extra se non specificato il flag B<IGNORE_EXTRA_PARAMS>. Se vengono specificati I<MISSING_REQUIRED_PARAMS>, I<EXTRA_PARAMS> li riempie rispettivamente con le liste dei nomi dei TDTA obbligatori mancanti e di quelli di troppo.

=cut

sub test_action_properties { $_[0]->art()->test_action_properties(ACTIVITY_TYPE => $_[0]->type_id(), @_[ 1 .. $#_ ]) }


=head2 I<object>->B<parent_type_id>(), I<object>->B<parent_type_name>()

Ritornano rispettivamente l'ID ed il nome del tipo attivita' dell'attivita' padre.

=cut
sub parent_type_id {
	my $self = shift;
	my $p = $self->parent();
	return undef unless defined $p;
	return $p->type_id();
}
sub parent_type_name {
	my $self = shift;
	my $p = $self->parent();
	return undef unless defined $p;
	return $p->type_name();
}


=head2 I<object>->B<parent_property>( [ I<property1> [ , I<property2> [ , I<propertyN> ] ] ] )

Metodo "shortcut" che restituisce le proprieta' del padre dell'attivita' corrente:

	...
	my $pp = $a->parent_property(); # equivale a $a->parent()->property()
	...

Se viene passato il nome di una sola proprieta' ritorna uno I<scalar>.

Se vengono passati piu' nomi di proprieta' oppure senza argomenti, ritorna un I<hashref>.

Nel caso in cui l'attivita' non abbia un padre restituisce B<undef>.

=cut
sub parent_property {
	my $self = shift;
	my $p = $self->parent();
	return undef
		unless $p;
	return $p->property(@_);
}


=head2 I<object>->B<adopt_children>( CHILDREN => I<arrayref> )

Rende le attivita' specificate dall'argomento B<CHILDREN> "figlie" dell'attivita' corrente.

L'argomento B<CHILDREN> e' un I<arrayref> di oggetti B<API::ART::Activity>.

=cut
sub adopt_children {
	my $self = shift;
	my $count = 0;
	my %params = @_;
	local $@;
	
	$self->art()->clear_last_error();
	# CHILDREN
	$self->art()->last_error("Missing CHILDREN!")
		&& return $count
			unless defined $params{CHILDREN};
	$self->art()->last_error("CHILDREN must be an array-ref!")
		&& return $count
			unless ref($params{CHILDREN}) eq 'ARRAY';
	$self->art()->last_error("No CHILD to adopt!")
		&& return $count
			unless scalar(@{$params{CHILDREN}});
	my $id = join ',', map {
#		$self->art()->last_error("CHILDREN must be an array-ref of '$params{CLASS}' object!")
#			&& return $count
#				unless ref($_) eq $params{CLASS};
		'?'
	} @{$params{CHILDREN}};
	
	my $sql = "
		insert	into a_padre_a_figlio (padre, figlio)
			select	?, id_attivita
			from	attivita
			where	id_attivita in ($id)
	";

	$self->art()->_create_prepare(__PACKAGE__.'_adop_ch_'.(scalar @{$params{CHILDREN}}), $sql);
	
	my @bind_values = (
		$self->id()
	);
	
	push (@bind_values, map {$_->id()} @{$params{CHILDREN}});
	
	eval {
		$self->art()->_create_prepare(__PACKAGE__.'_adop_ch_'.(scalar @{$params{CHILDREN}}))->do(@bind_values);
	};
	
	$self->art()->last_error("Error adopting activity!\n$@")
		&& return $count
			if $@;
	return scalar(@{$params{CHILDREN}});
}

=head2 I<object>->B<disinherit_children>( CHILDREN => I<arrayref> )

Disadotta le attività specificate dall'argomento B<CHILDREN> dell'attività corrente.

L'argomento B<CHILDREN> e' un I<arrayref> di oggetti B<API::ART::Activity>.

=cut
sub disinherit_children {
	my $self = shift;
	my %params = @_;
	local $@;
	
	$self->art()->clear_last_error();
	# CHILDREN
	$self->art()->last_error("Missing CHILDREN!")
		&& return undef
			unless defined $params{CHILDREN};
	$self->art()->last_error("CHILDREN must be an array-ref!")
		&& return undef
			unless ref($params{CHILDREN}) eq 'ARRAY';
	$self->art()->last_error("No CHILD to disinherit!")
		&& return undef
			unless scalar(@{$params{CHILDREN}});
	
	# verifico che non mi abbiano passato figli duplicati
	my @children_to_disinherit;
	for my $ch (@{$params{CHILDREN}}){
		if (grep {$_ eq $ch->id} @children_to_disinherit){
			$self->art()->last_error("duplicate CHILD found");
			return undef;
		} else {
			push @children_to_disinherit, $ch->id;
		}
	}
	
	# verifico che ci siano tutti i figli
	my $sql = "
		select count(1)
		from a_padre_a_figlio apaf
		where apaf.padre = ?
		and apaf.figlio in (".join (',', map {'?'} @children_to_disinherit).")
	";

	$self->art()->_create_prepare(__PACKAGE__.'_dis_ch_'.(scalar @children_to_disinherit), $sql);
	
	my @bind_values = (
		$self->id()
	);
	
	push (@bind_values, @children_to_disinherit);
	
	my $id = join ',', map {'?'} @children_to_disinherit;
	my $count_children = $self->art()->_create_prepare(__PACKAGE__.'_dis_ch_'.(scalar @children_to_disinherit))->fetch_minimalized(@bind_values);
	
	$self->art()->last_error("Not all children in the array are children of the activity!")
		&& return undef
			if $count_children != scalar(@{$params{CHILDREN}});
	
	$sql = "
		delete a_padre_a_figlio
			where padre = ?
			and figlio in ($id)
	";

	$self->art()->_create_prepare(__PACKAGE__.'_disch_del_'.(scalar @children_to_disinherit), $sql);
	
	@bind_values = (
		$self->id()
	);
	
	push (@bind_values, @children_to_disinherit);
	
	eval {
		$self->art()->_create_prepare(__PACKAGE__.'_disch_del_'.(scalar @children_to_disinherit))->do(@bind_values);
	};
	$self->art()->last_error("Error disinherit activity!\n$@")
		&& return undef
			if $@;
	return scalar(@{$params{CHILDREN}});
}

=head2 I<object>->B<children_are_closed>()

Ritorna 1 se tutte le attivita' "figlie" sono chiuse

=cut
sub children_are_closed {
	my $self = shift;
	return 1 unless $self->children_count();
	foreach my $child ( @{ $self->get_children() } ) {
		return 0 unless $child->is_closed();
	}
	return 1;
}


=head2 I<object>->B<can_do_action>( I<ID> => $id | I<NAME> => $name )

Ritorna 1 se l'azione specificata puo' essere eseguita in base allo stato corrente dell'attivita'.

=cut
sub can_do_action {
	my $self = shift;
	my %params = @_;
	$self->art()->clear_last_error();
	$self->art()->last_error("Expecting ID or NAME parameters!")
		&& return undef
			unless defined $params{ID} || defined $params{NAME};
	$self->art()->last_error("Expecting only one between ID and NAME parameters!")
		&& return undef
			if defined($params{ID}) && defined($params{NAME});
	my $action_id;
	if ( defined $params{ID} ) {
		$self->art()->last_error("Invalid ACTION_ID '$params{ID}'!")
			&& return undef
				unless $self->test_activity_action_id($params{ID});
		$action_id = $params{ID};
	} else {
		$self->art()->last_error("Invalid ACTION_NAME '$params{NAME}'!")
			&& return undef
				unless $self->art()->test_activity_action_name($params{NAME});
		$action_id = $self->art()->get_activity_action_id($params{NAME});
	}
	
	my $groups_count = scalar @{$self->art()->user()->su()->activity()->groups()};
	# se non sono in uno stato finale verifico se si tratta di un'azione prevista dallo stato__ANY_STATUS__
	# verificando anche i permessi
	if (
		$self->is_active()
		||
		(
			defined $self->art()->_get_activity_virtual_action_invalidate_name()
			&&
			$action_id eq $self->art()->_get_activity_virtual_action_invalidate_id()
		)
		||
		(
			defined $self->art()->_get_activity_virtual_action_add_documentation_name()
			&&
			$action_id eq $self->art()->_get_activity_virtual_action_add_documentation_id()
		)){
		my $sql_any_status = "
			select 1
			from permission_action pa
			where pa.id_tipo_attivita = ?
			    and pa.id_stato_iniziale in (?,?)
				and pa.id_action_permessa = ?
			    and pa.id_gruppo_abilitato in (".join (',', map {'?'} @{$self->art()->user()->su()->activity()->groups()}).")
			    and rownum<2
		";
		$self->art()->_create_prepare(__PACKAGE__.'_candoactany'.$groups_count, $sql_any_status);
		
		my @bind_values_any_status = (
			$self->{INFO}->{ACTIVITY_TYPE_ID},
			$self->art()->get_activity_status_id($self->art()->_get_activity_virtual_status_any_status_name()),
			$self->{INFO}->{STATUS_ID},
			$action_id,
		);
		push @bind_values_any_status, @{$self->art()->user()->su()->activity()->groups()};
		
		my $action_for_any_status = $self->art()->_create_prepare(__PACKAGE__.'_candoactany'.$groups_count)->fetch_minimalized(@bind_values_any_status);
		return $action_for_any_status;
	}
	
	#$self->info();
	my $sql = "
		SELECT 1
		FROM   permission_action pa
		WHERE  pa.id_tipo_attivita = ?
			and pa.ID_STATO_INIZIALE = ?
			and pa.ID_ACTION_PERMESSA = ?
			and pa.id_gruppo_abilitato in (".join (',', map {'?'} @{$self->art()->user()->su()->activity()->groups()}).")
			and rownum<2
	";

	$self->art()->_create_prepare(__PACKAGE__.'_can_do_act'.$groups_count, $sql);
	
	my @bind_values = (
		$self->{INFO}->{ACTIVITY_TYPE_ID},
		$self->{INFO}->{STATUS_ID},
		$action_id
	);
	push @bind_values, @{$self->art()->user()->su()->activity()->groups()};
	
	my $exists = $self->art()->_create_prepare(__PACKAGE__.'_can_do_act'.$groups_count)->fetch_minimalized(@bind_values);
	
	$self->art()->last_error("Invalid ACTION '".$self->art()->get_activity_action_name($action_id)."' for status ".$self->art()->get_activity_status_name($self->{INFO}->{STATUS_ID})."!")
		&& return undef
			unless $exists;
	
	return $exists;
}

=head2 I<object>->B<system_id>() , I<object>->B<system_name>(), I<object>->B<system>()

B<I metodi I<get_system_id>() , I<get_system_name>() e I<system>()> sono obsoleti!>

Ritornano rispettivamente l'ID (I<SYSTEM_ID>), il nome (I<SYSTEM_NAME>) e l'oggetto API::ART:System
a cui e' associata l'attivita' corrente.

=cut
sub system_id { $_[0]->cached_info('SYSTEM_ID') }
sub get_system_id {
	$_[0]->warning("'get_system_id()' method is obsolete: please use 'system_id()' instead!");
	$_[0]->system_id();
}

sub system_name { $_[0]->system()->name() }
sub get_system_name {
	$_[0]->warning("'get_system_name()' method is obsolete: please use 'system_name()' instead!");
	$_[0]->system_name();
}

sub system {
	shift->{SYSTEM};
}
sub get_system {
	$_[0]->warning("'get_system()' method is obsolete: please use 'system()' instead!");
	$_[0]->system();
}


=head2 I<object>->B<system_type_id>(), I<object>->B<system_type_name>()

Ritornano rispettivamente l'ID ed il nome del tipo tipo sistema.

=cut
sub system_type_id {
	my $self = shift;
	my $s = $self->system();
	return undef unless defined $s;
	return $s->type_id();
}
sub system_type_name {
	my $self = shift;
	my $s = $self->system();
	return undef unless defined $s;
	return $s->type_name();
}


=head2 I<object>->B<system_property>()

Ritorna le I<property> del sistema a cui afferisce l'attivita'.

=cut
sub system_property {
	my $self = shift;
	return $self->system()->property(@_);
}

=head2 I<object>->B<traverse_family_tree>( I<CALLBACK> => I<callback> )

Percorre tutte le attivita' dell'albero genealogico invocando la funzione I<callback> passata per ogni attività trovata.

La funzione I<callback> verra' invocata passando come parametro l'attivita' che sta percorrendo: se restituira' I<0> la ricorsione continuera', se restituira' un valore diverso I<0> o I<undef> (settando I<last_error>) terminara' la ricorsione.

La funzione B<traverse_family_tree> ritornera' il valore restituito dall'ultima callback invocata. 

Esempio:

  my $rc = $activity->traverse_family_tree(
    CALLBACK => sub {
      $activity = shift;
      print STDERR "CB ID: ".$activity->id()."\n";
      return 0;
    }
  );
  unless (defined $rc) {
    print STDERR "PROBLEMI: ".$api->last_error()."\n";
  } else {
    print STDERR "RETURN VALUE: $rc\n";
  }

=cut
sub traverse_family_tree {
	my $self = shift;
	
	my %params = @_;
	
	$self->art()->clear_last_error();
	
	# Controllo parametri
	$self->art()->last_error("Missing CALLBACK parameter!") 
		&& (return undef)
			unless defined $params{CALLBACK};
	$self->art()->last_error("Parameter CALLBACK must be a CODEREF!") 
		&& (return undef)
			unless ref $params{CALLBACK} eq 'CODE';
	
	# inizio la ricorsione
	my $ancestor = $self;

	while (my $tmp_ancestor = $ancestor->parent()) {
		# l'attivita' ha un padre
		$ancestor = $tmp_ancestor;
	}
	# quando $ancestor->parent() ha restituito undef sono all'attivita' top del family tree
	# tale attivita' e' nella variabile $ancestor

	$self->art()->clear_last_error();
	
	# inizio la ricorsione
	return $self->_descend_family_tree($ancestor, $params{CALLBACK}, 1);

}

sub _descend_family_tree {
	my $self = shift;
	
	my ($act, $callback, $is_ancestor) = @_;
	
	# la get_children restituisce 'undef' se l'utente non ha visibilita' sull'attivita' figlia
	return 0 unless defined $act;
	
	# cerco i figli dell'attivita'
	my $children = $act->get_children();
	return undef
		unless defined $children;
	
	for my $child (@$children) {
		# scendo ricorsivamente nei figli
		my $rc = $self->_descend_family_tree($child, $callback, 0);
		# fermo la ricorsione se la callback ha restituito un valore diverso da 0 o undef
		return $rc
			if ((!defined $rc) || ($rc != 0));
	}
	
	# ritorno il valore restituito dalla callback
	return $callback->($act, $is_ancestor);
}

=head2 I<object>->B<get_hierarchy>( )

Restituisce tutte le attivita' su cui si ha visibilita' dell'albero genealogico nel formato di un HASH ricorsivo di HASH (chiamati HIERARCHY)
con le seguenti chiavi:

=over 4

=item B<ACTIVITY> => Oggetto API::ART::Activity

=item B<CHILDREN> => ARRAY di HASH di tipo HIERARCHY che rappresentano i figli

=item B<PARENT> => Riferimento all'oggetto di tipo HIERARCHY che rappresenta l'attivita' padre, I<undef> se l'attivita' e' il capostipite della
gerarchia

=back

=cut
sub get_hierarchy {
	my $self = shift;
	
	$self->art()->clear_last_error();
	
	my $ancestor_id = undef;
	my $acts = {};

	return undef
		unless defined $self->traverse_family_tree(CALLBACK => sub {
			my $act = shift;
			my $is_ancestor = shift;
			
			$ancestor_id = $act->id()
				if $is_ancestor;
			
			$acts->{$act->id()} = {
				 ACTIVITY => $act
				,PARENT_ID => $act->parent_id()
				,CHILDREN => []
			};
			return 0;
		});
	
	for my $id (sort keys %$acts) {	
		unless ($id == $ancestor_id) {
			push @{$acts->{$acts->{$id}->{PARENT_ID}}->{CHILDREN}}, $acts->{$id};
		}
	}
	
	for my $id (keys %$acts) {
		# inserisco un riferimento all'oggetto padre
		$acts->{$id}->{PARENT} = defined $acts->{$id}->{PARENT_ID} ? $acts->{$acts->{$id}->{PARENT_ID}}->{ACTIVITY} : undef;
		# elimino l'id dell'attivita' padre che mi serviva nella parte precedente dell'algoritmo ma non e' necessario restituirla al chiamante
		delete $acts->{$id}->{PARENT_ID};
	}
	
	return $acts->{$ancestor_id};
		
}

=pod

=head2 I<object>->B<serialize>( [ OUTPUT => <scalar> ] )

Restituisce l'oggetto perl modificato con i parametri in seguito descritti:

=over 4

=item B<OUTPUT> => <scalar>

accetta 'id' o 'name' (default), ed indica se rappresentare i valori delle info
con il loro id o il loro name, e ritorna un hashref.

=back

=cut

sub serialize { #TODO parametro facoltativo formato (perl hash, json, xml, ...TBD) o una callback
	my ($self, %params) = @_;
	my $errmsg;
	my $art = $self->art();
	$art->clear_last_error();
	
	croak $errmsg
		unless	$self->check_named_params(
			 ERRMSG 				=> \$errmsg
			,IGNORE_EXTRA_PARAMS 	=> 0
			,PARAMS 				=> \%params
			,MANDATORY => {	}
			,OPTIONAL => {
				OUTPUT	=> { isa => 'SCALAR', list => [ 'id', 'name' ] }
			}
		);
	
	# copio l'hash perche' devo cancellare delle chiavi
	my %info = %{$self->info()};
	
	my $sx = $params{OUTPUT} || 'name';
	# se OUTPUT = name devo rimuovere chiavi che terminano con id e viceversa
	$sx = $sx eq 'name' ? 'id' : 'name';
	my $usx = '_'.uc $sx;
	
	for my $key (keys %info){
		next if $key =~/^(ACTIVITY_ID|SYSTEM_ID|PARENT_ACTIVITY_ID)$/;
		delete $info{$key} if $key =~/$usx$/;
	}
	
	return \%info;
}

# --------------------------------------------------------------------
# Da qui in poi definiamo tutti gli alias alle funzioni già esistenti.
# la riga *function if 0 serve per risolvere un baco del perl che solleva un futile warning;
# --------------------------------------------------------------------

=head2 I<object>->B<get_current_status_id>() , I<object>->B<get_current_status_name>()

Questi metodi sono sinonimi di status_id e di status_name e ritornano rispettivamente l'ID (I<STATUS_ID>) ed il nome dello stato corrente dell'attivita'.

=cut

# Creo un alias alle funzioni status_id/name in quanto queste funzioni erano state duplicate.
*get_current_status_id = \&status_id;			# piu' efficace di sub get_current_status_id { shift->status_id(@_); }
*get_current_status_id if 0;					# risolve un baco del perl che solleva un futile warning;
*get_current_status_name = \&status_name;
*get_current_status_name if 0;

=head2 I<object>->B<dump>( %params )

Restituisce un hash che rappresenta l'attività come l'omologo formato B<JSON> restituito dall'B<API::ART::REST>
C<GET /activities/:ID> (vedi L<http://dvmas003.ict.sirti.net:10199/doc/ArtRestApi/index.html#api-Activity-getActivity>).

Le chiavi di primo livello sono le seguenti:

 {
   "id" => ...
   ,"info" => ...
   ,"properties" => ...
   ,"system" => ...
 }

=over

=item B<I<%params>>

=item * B<C<EXCLUDE_INFO>> <C<0|1>>

Esclude la chiave C<info>, default 0

=item * B<C<EXCLUDE_HISTORY>> <C<0|1>>

Esclude la chiave C<history>, default 1

=item * B<C<EXCLUDE_PROPERTIES>> <C<0|1>>

Esclude la chiave C<properties>, default 0

=item * B<C<SYSTEM>> <C<HASHREF>>

Parametri che controllano la chiave C<system> che rappresenta il sistema

=over

=item * B<C<EXCLUDE_ALL>> <C<0|1>>

Esclude in toto la chiave C<system>, default 0

=item * B<C<EXCLUDE_INFO>> <C<0|1>>

Esclude la chiave C<system-E<gt>info>, default 0

=item * B<C<EXCLUDE_PROPERTIES>> <C<0|1>>

Esclude la chiave C<system-E<gt>properties>del sistema, default 0

=item * B<C<SPLIT_PROPERTIES>> <C<0|1>>

Se impostato a 1 nella chiave C<system-E<gt>properties> del sistema non ci saranno le properties di tipo MULTIPLEX (array),
che verranno invece inserite in una chiave dedicata chiamata C<system-E<gt>propertiesArray>, default 0

=item * B<C<EXCLUDE_AVAILABLE_ACTIONS>> <C<0|1>>

Esclude l'elenco delle azioni disponibili per lo stato e il profilo corrente, default 1

=back

=back

=cut
sub dump() {
	my $self = shift;
	my $errmsg;
	my %params = @_;
	
	$self->art()->last_error($errmsg)
		unless	$self->check_named_params(
			 ERRMSG 				=> \$errmsg
			,IGNORE_EXTRA_PARAMS 	=> 0
			,PARAMS 				=> \%params
			,MANDATORY => {}
			,OPTIONAL => {
				 EXCLUDE_INFO => { isa => 'SCALAR', list => [0,1] }
				,EXCLUDE_HISTORY => { isa => 'SCALAR', list => [0,1] }
				,EXCLUDE_PROPERTIES => { isa => 'SCALAR', list => [0,1] }
				,EXCLUDE_AVAILABLE_ACTIONS => { isa => 'SCALAR', list => [0,1] }
				,SYSTEM => { isa => 'HASH' }
			}
		);
	
	# filtri con default 0
	for ('EXCLUDE_INFO', 'EXCLUDE_PROPERTIES'){
		$params{$_} = 0 unless defined $params{$_};
	}
	
	# filtri con default 1
	for ('EXCLUDE_HISTORY', 'EXCLUDE_AVAILABLE_ACTIONS'){
		$params{$_} = 1 unless defined $params{$_};
	}
	
	if (!defined $params{SYSTEM}) {
		$params{SYSTEM} = {};
		$params{EXCLUDE_SYSTEM} = 0;
	} else {
		$params{EXCLUDE_SYSTEM} = $params{SYSTEM}->{EXCLUDE_ALL} ? 1 : 0;
		delete $params{SYSTEM}->{EXCLUDE_ALL};
	}
	
	my $str = {
		 "id" => $self->id() * 1
	};
	
	unless ($params{EXCLUDE_INFO}) {
		my $info = $self->info();
		
		$str->{"info"} = {
			 "lastVarDate"		=> $self->art()->get_iso_date_from_date($info->{"LAST_VAR_DATE"})
			,"creationDate"		=> $self->art()->get_iso_date_from_date($info->{"CREATION_DATE"})
			,"parentId"			=> defined $info->{"PARENT_ACTIVITY_ID"} ? $info->{"PARENT_ACTIVITY_ID"} * 1 : undef
			,"systemId"			=> $info->{"SYSTEM_ID"} * 1
			,"properties"		=> $info->{"PROPERTIES"}
			,"description"		=> $info->{"DESCRIPTION"}
			,"dateNextStep"		=> $self->art()->get_iso_date_from_date($info->{"DATE_NEXT_STEP"})
			,"children"			=> $info->{"CHILDREN"}
			,"isLocked"			=> $info->{"IS_LOCKED"} ? $JSON::true : $JSON::false
			,"isLockedByMyself"	=> $info->{"IS_LOCKED_BY_MYSELF"} ? $JSON::true : $JSON::false
			,"ownerObj"			=> $self->art()->get_user_structure($self->art()->get_user_id($info->{OWNER_USER_NAME}))
			,"creationUserObj"	=> $self->art()->get_user_structure($self->art()->get_user_id($info->{CREATION_USER_NAME}))
			,"currentUserObj"	=> $info->{CURRENT_USER_NAME} ? $self->art()->get_user_structure($self->art()->get_user_id($info->{CURRENT_USER_NAME})) : undef
			,"status"			=> $info->{"STATUS_NAME"}
			,"currentUser"		=> $info->{"CURRENT_USER_NAME"}
			,"type"				=> $info->{"ACTIVITY_TYPE_NAME"}
			,"systemType"		=> $info->{"SYSTEM_TYPE_NAME"}
			,"owner"			=> $info->{"OWNER_USER_NAME"}
			,"creationUser"		=> $info->{"CREATION_USER_NAME"}
			,"closureDate"		=> $self->art()->is_activity_final_status(NAME => $info->{"STATUS_NAME"}) ? $self->art()->get_iso_date_from_date($info->{"LAST_VAR_DATE"}) : undef
			,"isClosed"			=> $self->art()->is_activity_final_status(NAME => $info->{"STATUS_NAME"}) ? $JSON::true : $JSON::false
			,"attachments"		=> []
			,"interval"			=> $info->{"INTERVAL"}
			,"lastTransitionId"		=> $info->{"LAST_TRANSITION_ID"}*1
			,"lastUpdateId"			=> $info->{"LAST_UPDATE_ID"}*1
			,"lastUpdateTimestamp"	=> $self->art()->get_iso_date_from_date($info->{"LAST_UPDATE_TIMESTAMP"})
			,"version"				=> $info->{"VERSION"}*1
			,"active"				=> $self->is_active() ? $JSON::true : $JSON::false
		};
		
		if (defined $info->{AGING}->{CALENDAR_ID}){
			$str->{"info"}->{agingObj} = {
				"calendarId"			=> $info->{AGING}->{CALENDAR_ID}*1
				,"calendarLabel"		=> $info->{AGING}->{CALENDAR_LABEL}
				,"calendarDescription"	=> $info->{AGING}->{CALENDAR_DESCRIPTION}
				,"update"				=> $info->{AGING}->{AGING_UPDATE} ? $JSON::true : $JSON::false
				,"seconds"				=> defined $info->{AGING}->{AGING_SECONDS} ? $info->{AGING}->{AGING_SECONDS}*1 : undef
				,"interval"				=> $info->{AGING}->{AGING_INTERVAL}
				,"lastVarDate"			=> $self->art()->get_iso_date_from_date($info->{AGING}->{AGING_LAST_VAR_DATE})
				,"sla"					=> []
			};
			# rimappo le eventuali soglie di sla dell'aging
			for my $s (@{$info->{AGING}->{SLA}}){
				push @{$str->{"info"}->{agingObj}->{sla}}, {
					"targetLabel" => $s->{TARGET_LABEL},
					"targetTo" => $s->{TARGET_TO} ? $s->{TARGET_TO}*1 : undef,
					"targetLevel" => $s->{TARGET_LEVEL},
					"insertDate" => $self->art()->get_iso_date_from_date($s->{INSERT_DATE}),
					"targetFrom" => $s->{TARGET_FROM}*1,
				};
				if (
					$str->{"info"}->{agingObj}->{seconds} >= $s->{TARGET_FROM}
					&&
					(
						! defined $s->{TARGET_TO}
						||
						$str->{"info"}->{agingObj}->{seconds} <= $s->{TARGET_TO}
					)
				){
					$str->{"info"}->{agingObj}->{targetLevel} = $s->{TARGET_LEVEL};
					$str->{"info"}->{agingObj}->{targetLabel} = $s->{TARGET_LABEL};
				}
			}
		}
		
		# NB: nel caso in cui non ci siano properties associate alle azioni del flusso, la API::ART popolano la
		# chiave PROPERTIES con un array di un elemento undefined ( [ undef ] ).
		# in questo caso faccio ritornare un array vuoto ( [] )
		$str->{"info"}->{properties} = []
			if(!defined $str->{"info"}->{properties}->[0]);
			
		my $attachments = $self->attachment_list();
		return undef unless defined $attachments;
		
		for my $att (@$attachments) {
			push @{$str->{"info"}->{"attachments"}}, {
				 transitionId => $att->{TRANSITION_ID}*1
				,transitionDate => $self->art()->get_iso_date_from_date($att->{TRANSITION_DATE})
				,sequence => $att->{SEQUENCE}*1
				,owner => $att->{OWNER_NAME}
				,ownerFirstName => $att->{OWNER_FIRST_NAME}
				,ownerLastName => $att->{OWNER_LAST_NAME}
				,fileName => $att->{FILENAME}
				,title => $att->{TITLE}
				,description => $att->{DESCRIPTION}
				,revision => $att->{REVISION}
				,refDate => $att->{REF_DATE}
				,docType => $att->{DOC_TYPE}
				,downloadCount => defined $att->{DOWNLOAD_COUNT} ? $att->{DOWNLOAD_COUNT}*1 : undef
				,size => defined $att->{SIZE} ? ($att->{SIZE}*1) : undef
			};
		}
	}
	
	$str->{"properties"} = $self->property() unless $params{EXCLUDE_PROPERTIES};
	
	unless ($params{EXCLUDE_SYSTEM}){
		$str->{"system"} = $self->system()->dump(%{$params{SYSTEM}});
	
		return undef unless defined $str->{"system"};
	}
	
	# gestisco history
	unless ($params{EXCLUDE_HISTORY}) {
		
		$str->{"history"} = [];
		
		my $history = $self->history(); # NB: MERGE_CHILDREN non viene volutamente gestito per ora
	
		while( my $transition = $history->next() ) {
			my $step_hash = $self->art()->serialize($transition);
			$step_hash->{objType} = 'TRANSITION';
			push (@{$str->{"history"}}, $step_hash);
		}
	}

	# gestisco available_actions
	unless ($params{EXCLUDE_AVAILABLE_ACTIONS}) {
		
		$str->{"availableActions"} = [];

		my $availableActions = $self->art()->enum_activity_action(ACTIVITY_TYPE_NAME => $self->cached_info->{"ACTIVITY_TYPE_NAME"}, INITIAL_STATUS_ID => $self->art()->get_activity_status_id($self->get_current_status_name()));
		return undef unless defined $availableActions;

		for my $actionAv (keys %{$availableActions}){
			if ($self->can_do_action(NAME => $actionAv)){
				push @{$str->{"availableActions"}}, $actionAv;
			}
		}
		
	}
	
	return $str;	
}

=head2 I<object>->B<refresh>()

Permette di aggiornare l'attività in modo che venga fatto incrementare il valore delle colonne ID_AGGIORNAMENTO, TS_AGGIORNAMENTO e VERSIONE della tabella ATTIVITA

=cut

sub refresh {
	my $self = shift;
	# serve per far scattare il trigger di aggiornamento
	my $sql = "
		update attivita
		set descrizione = descrizione
		where id_attivita = ?
	";

	$self->art()->_create_prepare(__PACKAGE__.'_refresh', $sql);
	
	my @bind_values = (
		$self->id()
	);
	
	eval {
		$self->art()->_create_prepare(__PACKAGE__.'_refresh')->do(@bind_values);
	};
	$self->art()->last_error("Error refreshing activity!\n$@")
		&& return undef
			if $@;
	return 1;
}

=head2 I<object>->B<update_aging>( %params )

Aggiorna l'aging del'attività

=over

=item B<I<%params>>

=item * B<C<AGING>> <C<NUMBER>>

Valore dell'aging da impostare

=back

=cut

sub update_aging{
	my $self = shift;
	my $errmsg;
	my %params = @_;
	
	$self->art()->last_error($errmsg)
		unless	$self->check_named_params(
			 ERRMSG 				=> \$errmsg
			,IGNORE_EXTRA_PARAMS 	=> 0
			,PARAMS 				=> \%params
			,MANDATORY => {
				AGING => { isa => 'SCALAR' }
			}
		);
	
	# calcolo
	my $sql = "
		update attivita
		set aging = ?
		, data_ult_aggiornamento_aging = sysdate
		where id_attivita = ?
	";

	$self->art()->_create_prepare(__PACKAGE__.'_up_ag', $sql);
	
	my @bind_values = (
		$params{AGING},
		$self->id()
	);
	
	my $s = $self->art()->_create_prepare(__PACKAGE__.'_up_ag')->do(@bind_values);
	
	# calcolo
	$self->art()->last_error("Unable to disable aging")
		&& return undef
			unless defined $s;
	
	return 1; 
}

=head2 I<object>->B<disable_aging>()

Disabilita l'aging del'attività

=cut

sub disable_aging{
	my $self = shift;
	
	my $sql = "
		update attivita
		set aggiorna_aging = null
		where id_attivita = ?
	";

	$self->art()->_create_prepare(__PACKAGE__.'_dis_ag', $sql);
	
	my @bind_values = (
		$self->id()
	);
	
	my $s = $self->art()->_create_prepare(__PACKAGE__.'_dis_ag')->do(@bind_values);
	
	# calcolo
	$self->art()->last_error("Unable to disable aging")
		&& return undef
			unless defined $s;
	
	return 1; 
}

=head2 I<object>->B<enable_aging>()

Abilita l'aging del'attività

=cut

sub enable_aging{
	my $self = shift;
	
	my $sql = "
		update attivita
		set aggiorna_aging = '1'
		, data_ult_aggiornamento_aging = sysdate
		, aging = nvl2(aging, aging, 0)
		where id_attivita = ?
	";

	$self->art()->_create_prepare(__PACKAGE__.'_en_ag', $sql);
	
	my @bind_values = (
		$self->id()
	);
	
	my $s = $self->art()->_create_prepare(__PACKAGE__.'_en_ag')->do(@bind_values);
	
	# calcolo
	$self->art()->last_error("Unable to enable aging")
		&& return undef
			unless defined $s;
	
	return 1; 
}

=head2 I<object>->B<send_notification>()

Invio di una notifica tramite email tramite il supporto API::ART se presente (verificare tramite la chiamata $art->notification_activity_support()).
Il formato della mail è standard. E' possibile customizzare solo i recipient e aggiungere in testa e in coda alla mail una sezione testuale

Ritorna true in caso di successo, false altrimenti.

Per utilizzare questa funzionalità è necessario:

=over 4

Configurare le label di contatti dei gruppi di ART 

=over 8

Tabelle di riferimento:

GRUPPI_CONTATTI: tabella di definizione delle label dei gruppi di contatto (da popolare da parte dello sviuppo: vedi commenti sulla tabella per il dettaglio)

TIPI_ATTIVITA_STATI_CONTATTI: tabella per la definizione degli stati dei tipi_attivita per cui è necessario inviate la notifica (da popolare da parte dello sviuppo: vedi commenti sulla tabella per il dettaglio)

=back

Mettere in esecuzione come demone il ramq basato sul file di configurazione $COM/API_ART/Activity/NotificationService/ramq.conf che leggendo da una coda Remote Activity prepara la mail da inviare tramite il servizio email_service

Mettere in esecuzione come demone il ramq basato sul file di configurazione $COM/API_ART/EmailService/ramq.conf che si preoccupa di inviare le mail

=back

=back

Parametri:

=over 4

=item B<CONFIGURED_RECIPIENTS>

Mutuamente esclusivo rispetto al parametro CUSTOM_RECIPIENTS. Arrayref di HASH con le chiavi GROUP_NAME e LABEL delle label dei gruppi di contatto (GRUPPI_CONTATTI e TIPI_ATTIVITA_STATI_CONTATTI)

=item I<CUSTOM_RECIPIENTS>

Mutuamente esclusivo rispetto al parametro CONFIGURED_RECIPIENTS. Arrayref di email a cui inviare in TO la notifica

=item I<CUSTOM_RECIPIENTS_CC>

Mutuamente esclusivo rispetto al parametro CONFIGURED_RECIPIENTS. Arrayref di email a cui inviare in CC la notifica

=item B<CUSTOM_MESSAGE_BEFORE>

Opzionale. Testo che verrà aggiunto all'inizio del body della mail

=item B<CUSTOM_MESSAGE_AFTER>

Opzionale. Testo che verrà aggiunto alla fine del body della mail

=item B<ATTACHMENTS>

Opzionale. Arrayref di hash di riferimenti agli allegati da inserire.

Le chiavi di ogni hash possibili sono:

=over 4

=item B<TYPE>

Opzionale. Riferiemento al tipo documento

=item B<TRANSITION_ID>

Opzionale. Riferiemento alla transizione da cui recuperare gli allegati

=item B<SEQUENCE_ID>

Opzionale. Riferiemento all'ordine dell'allegato. In questo cao è obbligatorio l'utilizzo anche del campo TRANSITION_ID

=back

=back

=cut

sub send_notification{
	my $self = shift;
	my $errmsg;
	my %params = @_;
	
	$self->art()->clear_last_error();
	
	$self->art()->last_error('Notification Service not available!')
		&& return undef
			unless $self->art()->notification_activity_support();
	
	$self->art()->last_error($errmsg)
		&& return undef
			unless	$self->check_named_params(
				 ERRMSG 				=> \$errmsg
				,IGNORE_EXTRA_PARAMS 	=> 0
				,PARAMS 				=> \%params
				,MANDATORY => {}
				,OPTIONAL => {
					CONFIGURED_RECIPIENTS	=> {isa => 'ARRAY'},
					CUSTOM_RECIPIENTS		=> {isa => 'ARRAY'},
					CUSTOM_RECIPIENTS_CC	=> {isa => 'ARRAY'},
					CUSTOM_MESSAGE_BEFORE	=> {isa => 'SCALAR'},
					CUSTOM_MESSAGE_AFTER	=> {isa => 'SCALAR'},
					ATTACHMENTS				=> {isa => 'ARRAY'},
				}
			);

	#ATTACHMENTS => [
	#	{
	#		TYPE => 'speedarkDoc',
	#		TRANSITION_ID => '20210321171019',
	#		#SEQUENCE_ID => 0
	#	}
	#
	#]
	
	$self->art()->last_error('At least one param between CONFIGURED_RECIPIENTS and CUSTOM_RECIPIENTS must be defined!')
		&& return undef
			if (!defined $params{CONFIGURED_RECIPIENTS} && ! defined $params{CUSTOM_RECIPIENTS});
	
	$self->art()->last_error('Only one param between CONFIGURED_RECIPIENTS and CUSTOM_RECIPIENTS must be defined!')
		&& return undef
			if (defined $params{CONFIGURED_RECIPIENTS} && defined $params{CUSTOM_RECIPIENTS});
	
	for my $attach (@{$params{ATTACHMENTS}}){
		$self->art()->last_error('For param ATTACHMENTS, param SEQUENCE_ID is mandatory for param TRANSITION_ID')
			&& return undef
				if (defined $attach->{SEQUENCE_ID} && ! defined $attach->{TRANSITION_ID});
		
		$self->art()->last_error($errmsg)
		&& return undef
			unless	$self->check_named_params(
				 ERRMSG 				=> \$errmsg
				,IGNORE_EXTRA_PARAMS 	=> 0
				,PARAMS 				=> \%$attach
				,MANDATORY => {}
				,OPTIONAL => {
					TYPE			=> {isa => 'SCALAR'},
					TRANSITION_ID	=> {isa => 'SCALAR'},
					SEQUENCE_ID		=> {isa => 'SCALAR'},
				}
			);
	}

	my $data = {
		ACTIVITY_ID => $self->id(),
		LAST_VAR_DATE => $self->info('LAST_VAR_DATE'),
		DATE_FORMAT => $self->art()->get_default_date_format()
	};
	
	for ('CUSTOM_MESSAGE_BEFORE','CUSTOM_MESSAGE_AFTER'){
		$data->{$_} = $params{$_} if defined $params{$_}
	}
	
	# viene messo come json in quanto è un array di oggetti e deve essere inserito nelle RA
	$data->{ATTACHMENTS} = to_json($params{ATTACHMENTS});

	my $map_custom_recipients = {
		CUSTOM_RECIPIENTS => 'RECIPIENTS',
		CUSTOM_RECIPIENTS_CC => 'RECIPIENTS_CC'
	};

	if (defined $params{CONFIGURED_RECIPIENTS}){
		my $groups_contacts = $self->_get_groups_contacts();
		
		# la chiave CONFIGURED_RECIPIENTS è un array di hash con le chiavi GROUP_NAME e LABEL: lo verifico e invio la mail
		for my $g (@{$params{CONFIGURED_RECIPIENTS}}){
			$self->art()->last_error($errmsg)
				unless	$self->check_named_params(
					 ERRMSG 				=> \$errmsg
					,IGNORE_EXTRA_PARAMS 	=> 0
					,PARAMS 				=> $g
					,MANDATORY => {
						 GROUP_NAME	=> { isa => 'STRING' },
						 LABEL		=> { isa => 'STRING' }
					}
					,OPTIONAL => {
					}
				);
			
			my $id = $groups_contacts->{$g->{GROUP_NAME}}->{$g->{LABEL}};
			
			$self->art()->last_error("Unable to find id for GROUP_NAME ".$g->{GROUP_NAME}." and label ".$g->{LABEL})
				&& return undef
					unless defined $id;
			
			$data->{CONTACT_GROUP_ID} = $id;
			
			my $rc = $self->art()->get_notification_activity_sender()->insert(
				EVENT => 'SEND_NOTIFICATION',
				SOURCE_REF => $self->id().'-'.$id,
				DATA => $data
			);
			
			$self->art()->last_error('Unable to send notification')
				&& return undef
					unless ($rc);
		}
	} elsif (defined $params{CUSTOM_RECIPIENTS} || defined $params{CUSTOM_RECIPIENTS_CC}){
		# verifico che tutte le chiavi siano stringhe
		for ('CUSTOM_RECIPIENTS', 'CUSTOM_RECIPIENTS_CC'){
			if (defined $params{$_}){
				for my $e (@{$params{$_}}){
					$self->art()->last_error('All elements of '.$_.' must be a STRING')
						&& return undef
							if ref ($e);
				}
				$data->{$map_custom_recipients->{$_}} = $params{$_};
			}
		}
		
		
		
		my $rc = $self->art()->get_notification_activity_sender()->insert(
			EVENT => 'SEND_NOTIFICATION',
			SOURCE_REF => $self->id().'-CUSTOM_RECIPIENTS',
			DATA => $data
		);
		
		$self->art()->last_error('Unable to send notification')
			&& return undef
				unless ($rc);
	}
	
	return 1;
}

=head2 I<object>->B<get_ui_route>()

Restituisce il valore della ui_route. Se non definita per il tipo_attivita restituisce ''

=cut

sub get_ui_route(){
	my $self = shift;
	
	return $self->{UI_ROUTE} if exists $self->{UI_ROUTE};
	
	unless (defined $self->cached_info('UI_ROUTE')){
		$self->{UI_ROUTE} = '' ;
		return $self->{UI_ROUTE};
	}
	
	my $dump = $self->dump();
	
	my @comp = split ('/', $self->cached_info('UI_ROUTE'));
	
	for my $c (@comp){
		my $path = $c;
		
		next if $path eq '';
		next if $path !~/^\[/;
		
		$path =~ s/^\[//;
		$path =~ s/\]$//; 
		
		my @parts = split(/\]\[/, $path);
		
		my $transformed = '';
		
		for my $p (@parts){
			if ($p =~ /^\d+$/){
				$transformed.='->['.$p.']';
			} else {
				if ($p =~ /^'.*'$/ || $p =~ /^".*"$/){
					$transformed.='->{'.$p.'}';
				} else {
					$self->art()->last_error("Bad path ".$c);
					return undef; 
				}
			}
		}
		
		$c = eval '$dump'.$transformed;
	}
	
	$self->{UI_ROUTE} = join ('/', @comp);
	
	return $self->{UI_ROUTE};
}

=head2 I<object>->B<set_description>( I<scalar>, [FORCE => I<scalar>] )

Provvede all'aggiornamento della descrizione dell'attività se possibile farlo

Ritorna true in caso di successo, false altrimenti.

=over 4

=item B<FORCE>

Opzionale. Permette l'aggiornamento della descrizione anche se l'attività è in stato finale

=back

=cut

sub set_description(){
	my $self = shift;
	my $description = shift;
	my %params = @_;
	$self->art()->clear_last_error();
	my $errmsg;

	croak $errmsg
		unless	$self->check_named_params(
			 ERRMSG 				=> \$errmsg
			,IGNORE_EXTRA_PARAMS 	=> 0
			,PARAMS 				=> \%params
			,MANDATORY => {	}
			,OPTIONAL => {
				FORCE	=> { isa => 'SCALAR', list => [ 1 ] }
			}
		);

	$self->art()->last_error("Missing description")
		&& return undef
			unless defined $description;
	
	unless ($params{FORCE}){
		$self->art()->last_error("Unable to set description for activity not active")
			&& return undef
				unless ($self->is_active);
	}

	my $sql = qq(
		update attivita
		set descrizione = ?
		WHERE id_attivita = ?
	);
	
	$self->{ART}->_create_prepare(__PACKAGE__.'__sdesc', $sql);

	# imposto il savepoint
	$self->art()->_dbh()->do("SAVEPOINT AAAset_desc_".$self->id());
	
	my $update_desc = $self->{ART}->_create_prepare(__PACKAGE__.'__sdesc')->do($description,$self->id());

	unless (defined $update_desc){
		$self->art()->last_error("Unable to set description");
		$self->art()->_dbh()->do("rollback to SAVEPOINT AAAset_desc_".$self->id());
		return undef;
	}

	# sovrascrivo per leggere valori dopo aggiornamento
	$self->info();

	return 1;
}

=head2 I<object>->B<can_invalidate>()

Ritorna 1 se l'attivita' puo' essere INVALIDATA.

=cut
sub can_invalidate {
	my $self = shift;
	# deve esistere l'azione e deve essere definita all'interno del flusso
	my $vian = $self->art()->_get_activity_virtual_action_invalidate_name();
	return 0
		unless $vian;

	my $action_status = $self->art()->get_action_status($self->cached_info('ACTIVITY_TYPE_NAME'), $vian);
	return 0 unless defined $action_status->[0];

	return 1;
}

=head2 I<object>->B<invalidate>( [ I<DESCRIPTION> => I<scalar>, I<PROPERTIES> => I<hash>] )

Esegue sull'attività l'azione invalidazione (transizione definita come any_status).

=cut
sub invalidate {
	my $self = shift;
	my %params = @_;
	$self->art()->clear_last_error();
	#DESCRIPTION
	$params{DESCRIPTION} = 'Richiesta invalizaione' unless defined $params{DESCRIPTION};
	$self->art()->last_error("Can not INVALIDATE the activity!")
		&& (return undef)
			unless $self->can_invalidate();
	return $self->_step(
		ACTION		=> $self->art()->_get_activity_virtual_action_invalidate_name(),
		DESCRIPTION	=> $params{DESCRIPTION},
		PROPERTIES => $params{PROPERTIES}||{}
	);
}

=head2 I<object>->B<can_add_documentation>()

Ritorna 1 se all'attivita' puo' essere aggiunta della documentazione.

=cut
sub can_add_documentation {
	my $self = shift;
	# deve esistere l'azione e deve essere definita all'interno del flusso
	my $vian = $self->art()->_get_activity_virtual_action_add_documentation_name();
	$self->art()->last_error("Add documentation action not defined")
		&& return undef
			unless defined $vian;

	my $action_status = $self->art()->get_action_status($self->cached_info('ACTIVITY_TYPE_NAME'), $vian);
	$self->art()->last_error("Add documentation action not present for activity type")
		&& return undef
			unless defined $action_status->[0];

	$self->art()->last_error("User not enable to add_documentation")
		&& return undef
			unless($self->can_do_action(NAME => $vian));

	return 1;
}

=head2 I<object>->B<add_documentation>( [ I<DESCRIPTION> => I<scalar>, I<ATTACHMENTS> => I<array>] )

Esegue sull'attività l'aggiunta della documentazione (transizione definita come any_status).

=cut
sub add_documentation {
	my $self = shift;
	my %params = @_;
	$self->art()->clear_last_error();
	my $errmsg;

	croak $errmsg
		unless	$self->check_named_params(
			 ERRMSG 				=> \$errmsg
			,IGNORE_EXTRA_PARAMS 	=> 0
			,PARAMS 				=> \%params
			,MANDATORY => {	
				ATTACHMENTS	=> { isa => 'ARRAY' }
			}
			,OPTIONAL => {
				DESCRIPTION	=> { isa => 'SCALAR' }
			}
		);

	$self->art->last_error("You must pass at least one attachment")
		&& return undef
			unless scalar @{$params{ATTACHMENTS}};

	#DESCRIPTION
	$params{DESCRIPTION} = 'Aggiunta documentazione' unless defined $params{DESCRIPTION};
	$self->art()->last_error("Can not ADD DOCUMENTATION the activity!")
		&& (return undef)
			unless $self->can_add_documentation();
	return $self->_step(
		ACTION		=> $self->art()->_get_activity_virtual_action_add_documentation_name(),
		DESCRIPTION	=> $params{DESCRIPTION},
		ATTACHMENTS => $params{ATTACHMENTS}
	);
}

# sub DESTROY {
# 	my $self = shift;

# 	print STDERR __PACKAGE__."::DESTROY (".$self->id().")\n";

# 	return 1;

# }

1;

