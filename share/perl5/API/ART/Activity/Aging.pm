package API::ART::Activity::Aging;
# public 
use strict;
use warnings;
use Carp;
use Data::Dumper;

=head1 NAME

B<API::ART::Activity::Aging> - Gestione aging attività

=head1 SYNOPSIS

	# Uses needed packages
	use API::ART;
	use API::ART::Activity;

	# Create API::ART instance...
	my $art = API::ART->new(.......);
	# Create API::ART::Activity instance...
	my $a = API::ART::Activity->new(ART => $art, ID => 349522);

	my $ret = API::ART::Activity::Aging::do($a);
	return undef unless (defined $ret);

=head1 DESCRIPTION

Questo package statico consente di gestire l'aging di un'attività in funzione del calendario lavorativo e degli stati di sospensione

Per utilizzare questa funzionalità è necessario:

=over 4

Eseguire lo script aging_populate_calendars per popolare le tabelle degli orari lavorativi. 

=over 8

Tabelle di riferimento:

AGING_CALENDARS: tabella di definizione dei vari calendari (da popolare da parte dello sviuppo: vedi commenti sulla tabella per il dettaglio)

AGING_WORKING_HOURS: tabella per la definizione delle fasce orarie (da popolare da parte dello sviuppo: vedi commenti sulla tabella per il dettaglio)

CALENDARS: tabella calendario (popolata dallo script aging_populate_calendars)

=back

Mettere in esecuzione come demone lo script aging_handler che si occuperà di tenere aggiornata l'aging delle attività

=back

=head1 METHODS

Di seguito i metodi esposti dalla classe API::ART::Activity::Aging.

=cut

=head2 I<object>->B<do>(<API::ART::Activity>)

Calcola l'aging dell'attività passando in input l'oggetto attività

=cut

sub do { # static method 
	my $activity = shift;
	
	my $info = $activity->info();
	
	# verifico se l'attività ha già l'aging attivo
	my $aging_enabled = $info->{'AGING'}->{'AGING_UPDATE'};
	
	if ($aging_enabled){
		# calcolo l'aging, impostando la data di ultimo aggiornamento dell'aging
		
		my $sysdate = $activity->art()->_dbh()->get_sysdate();
		
		my $max_date = (defined $info->{'AGING'}->{'AGING_SECONDS'} && defined $info->{'AGING'}->{'AGING_LAST_VAR_DATE'}) ? $info->{'AGING'}->{'AGING_LAST_VAR_DATE'} : $sysdate;
		
		my $sql_delta = "
			select nvl(sum(round(
				case
					when DATA_ULT_AGGIORNAMENTO between start_date and end_date and DATA_STEP between start_date and end_date then data_step-data_ult_aggiornamento
					when DATA_ULT_AGGIORNAMENTO between start_date and end_date and DATA_STEP not between start_date and end_date then end_date-data_ult_aggiornamento
					when DATA_ULT_AGGIORNAMENTO not between start_date and end_date and DATA_STEP between start_date and end_date then data_step-start_date
					when DATA_ULT_AGGIORNAMENTO not between start_date and end_date and DATA_STEP not between start_date and end_date then end_date-start_date
					else 0
				end*86400
			)),0)delta
			from (
			select cc.*
				, to_date(".$activity->art()->_dbh()->quote($max_date).",".$activity->art()->_dbh()->quote($activity->art()->get_default_date_format()).") DATA_ULT_AGGIORNAMENTO
				, to_date(".$activity->art()->_dbh()->quote($sysdate).",".$activity->art()->_dbh()->quote($activity->art()->get_default_date_format()).") DATA_STEP
				from calendars cc
				where cc.id_calendar = ".$info->{'AGING'}->{'CALENDAR_ID'}."
					and cc.disabled is null
			)es
			where DATA_ULT_AGGIORNAMENTO between start_date and end_date -- DATA_ULT_AGGIORNAMENTO
				or DATA_STEP between start_date and end_date-- DATA_STEP
				or (
					start_date > DATA_ULT_AGGIORNAMENTO /*DATA_ULT_AGGIORNAMENTO*/
					and end_date < DATA_STEP /*DATA_STEP*/
				)
		";
		
		my $delta = $activity->art()->_dbh()->fetch_minimalized($sql_delta);
		
		return undef
			unless defined $activity->update_aging(
				AGING => ($info->{'AGING'}->{AGING_SECONDS}||0)+$delta
			);
	}
	
	# recupero le info degli stati
	my $info_stati = $activity->art()->get_activity_status_info(ACTIVITY_TYPE_NAME => $info->{ACTIVITY_TYPE_NAME});
	return undef unless defined $info_stati;
	
	# verifico se è uno stato di sospensione o è lo stato finale
	if ($info_stati->{$activity->get_current_status_name()}->{STARTING_FLAG} eq 'S' || $activity->is_closed()){
		# disabilita l'aging
		return undef
			unless defined $activity->disable_aging();
	} else {
		# abilita l'aging: se è già abilitata non ci sono problemi
		return undef
			unless defined $activity->enable_aging();
	}
	
	return 1;
}

1;

