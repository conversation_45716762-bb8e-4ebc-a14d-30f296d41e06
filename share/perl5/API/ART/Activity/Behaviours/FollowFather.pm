package API::ART::Activity::Behaviours::FollowFather;
# public 
use strict;
use warnings;
use Carp;
use Data::Dumper;

=head1 NAME

B<API::ART::Activity::Behaviours::FollowFather> - Comportamento per seguire movimentazione dell'attività padre

=head1 SYNOPSIS

	# Uses needed packages
	use API::ART;
	use API::ART::Activity;

	# Create API::ART instance...
	my $art = API::ART->new(.......);
	# Create API::ART::Activity instance...
	my $a = API::ART::Activity->new(ART => $art, ID => 349522);

	my $ret = API::ART::Activity::Behaviours::FollowFather::do($a,'FollowFather');
	return undef unless (defined $ret);

=head1 DESCRIPTION

Questo package statico consente di tenere allineate le attività figlie agli step effettuate dall'attività padre

=head1 METHODS

Di seguito i metodi esposti dalla classe API::ART::Activity::Behaviours::FollowFather.

=cut

=head2 I<object>->B<do>(<API::ART::Activity>, <BEHAVIOR_EVENT>)

Effettua le movimentazioni sull'attività figlia in funzione dello step del padre

=cut

sub do { # static method 
	my $activity = shift;
	my $behaviour_event = shift;
	
	my $parent = $activity->parent();

	# verifico se nell'attività è presente lo stato del padre
	my $statuses = $activity->art()->enum_activity_status(ACTIVITY_TYPE_NAME => $activity->info('ACTIVITY_TYPE_NAME'));
	
	# se non esiste lo stato non devo fare nulla
	return 1 if !exists $statuses->{$activity->get_current_status_name()};
	
	my $history = $parent->history();
	
	# recupero l'ultima azione del padre
	my $last = $history->last();
	
	# recupero dallo stato attuale dell'attività se esistono delle azioni che portano nello stato del padre
	my $sql = "
			select distinct azione
			from v_permission_Action vpa
			where vpa.tipo_Attivita = ".$activity->art()->_dbh()->quote($activity->info('ACTIVITY_TYPE_NAME'))."
			and vpa.stato_iniziale = ".$activity->art()->_dbh()->quote($activity->get_current_status_name())."
			and vpa.stato_finale = ".$activity->art()->_dbh()->quote($last->to_status_name())."
			and vpa.gruppo_utente in (" . join(',', map { $activity->art()->_dbh()->quote($activity->art()->get_group_name($_)) } @{$activity->art()->user()->su()->activity()->groups()}) . ")
	";
	
	my $actions = $activity->art()->_dbh()->fetchall_arrayref($sql);
	
	my $action;
	
	# se non sta aggiornado le properties
	if ($last->action_name() ne $activity->art()->get_activity_action_update_properties_name()){
		# se non esiste non faccio nulla
		if (scalar @{$actions} == 0){
			return 1;
		} elsif (scalar @{$actions} == 1){
		# se è una sola esegua l'unica azione	
			$action = $actions->[0]->[0];
		} elsif (scalar @{$actions} > 1){
		# se esiste piu' di una vedo se c'è quella con lo stesso nome dell'ultima azione del padre 
			if (grep {$_->[0] eq $last->action_name} @{$actions}){
				$action = $last->action_name;
			} else {
				$activity->art()->last_error("Bad configuration: unable to step activity of type ".ref($activity));
			}
		}
	}
	
	if (defined $action){
		my $props = {};
		if (defined $last->properties()){
			for my $p (@{$last->properties()}){
				$props->{$p->{PROPERTY_NAME}} = $p->{PROPERTY_VALUE};
			}
		}
		my $ret = $activity->step_filtered(
			ACTION => $action
			,DESCRIPTION => $last->description
			,DATE => $last->transition_date()
			,BEHAVIOUR_EVENT => $behaviour_event
			,PROPERTIES => $props
		);
		return undef
			unless $ret;
	} elsif($last->action_name() eq $activity->art()->get_activity_action_update_properties_name()){
		# recupero le property del tipo_attivita figlia
		my $act_props = $activity->art()->enum_activity_property(
			ACTIVITY_TYPE_NAME => $activity->info('ACTIVITY_TYPE_NAME'),
			SHOW_ONLY_WITH_VISIBILITY => 1
		);
		
		# eseguo l'aggiornamento delle property solo per quelli possibili
		if (defined $last->properties()){
			my $props = {};
			for my $p (@{$last->properties()}){
				$props->{$p->{PROPERTY_NAME}} = $p->{PROPERTY_VALUE} if exists $act_props->{$p->{PROPERTY_NAME}};
			}
			if (keys %{$props}){
				return undef
					unless $activity->update_properties(
						IGNORE_ALL_PROPERTIES_CHECKS => 1, # serve nel caso in cui l'attività figlia non si abilitata all'aggiornamento
						DATE => $last->transition_date(),
						PROPERTIES => $props
					);
			}
		}
	}
	return 1;
}

1;

