package API::ART::Activity::Behaviours::ObserveChildren;
# public 
use strict;
use warnings;
use Carp;
use Data::Dumper;

=head1 NAME

B<API::ART::Activity::Behaviours::ObserveChildren> - Comportamento per osservare gli step delle attività figlie e recepirle sul padre

=head1 SYNOPSIS

	# Uses needed packages
	use API::ART;
	use API::ART::Activity;

	# Create API::ART instance...
	my $art = API::ART->new(.......);
	# Create API::ART::Activity instance...
	my $a = API::ART::Activity->new(ART => $art, ID => 349522);

	my $ret = API::ART::Activity::Behaviours::ObserveChildren::do($a,'ObserveChildren');
	return undef unless (defined $ret);

=head1 DESCRIPTION

Questo package statico consente di osservare gli step delle attività figlie e recepirle sul padre

=head1 METHODS

Di seguito i metodi esposti dalla classe API::ART::Activity::Behaviours::ObserveChildren.

=cut

=head2 I<object>->B<do>(<API::ART::Activity>, <BEHAVIOR_EVENT>)

Effettua le movimentazioni sull'attività padre in funzione dello step delle attività figlie

=cut

sub do { # static method 
	my $activity = shift;
	my $behaviour_event = shift;
	
	# recupero l'ultimo step del figlio
	my $step_parent = $activity->history()->last();
	
	# verifico se e' possibile fare l'azione sull'attività padre
	my $parent = $activity->parent();
	
	# se non posso non faccio nulla
	return 1 unless $parent->can_do_action(NAME => $step_parent->action_name());
	
	my $props = {};
	if (defined $step_parent->properties()){
		for my $p (@{$step_parent->properties()}){
			$props->{$p->{PROPERTY_NAME}} = $p->{PROPERTY_VALUE};
		}
	}
	
	my $ret = $parent->step_filtered(
		ACTION => $step_parent->action_name()
		,DESCRIPTION => $step_parent->description()
		,BEHAVIOUR_EVENT => $behaviour_event
		,PROPERTIES => $props
	);
	return undef
		unless $ret;

	return 1;
}

1;

