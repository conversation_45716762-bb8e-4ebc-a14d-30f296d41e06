package API::ART::Activity::Binding;

use strict;
use warnings;

use base qw(API::ART::Activity);

use API::ART::Activity::Binding::Action;
use API::ART::Activity::Binding::Status;
use API::ART::Activity::Factory;

# Classe deputata alla costruzione di nuove API::ART::Activity
our $DEFAULT_COLLECTION_CLASS = 'API::ART::Collection::Activity';

# NOTA: era meglio che fosse API::ART::Activity::Factory
our $DEFAULT_CHILDREN_CLASS = "API::ART::Activity::Binding";

# NOTA: ci vorrebbe la riga sotto ma rischiamo la regressione
# our $DEFAULT_PARENT_CLASS = "API::ART::Activity::Factory";

sub _step{
	my $self = shift;
	my $params = {@_};

	return
		$params->{DISABLE_BINDING_INSIDE_STEP} ?
			$self->SUPER::_step( @_ ) :
			$self->_step_binded( @_ );
}

sub _step_binded{
	
	my $self = shift;
	my $params = {@_};
	
	my $step_ok;
	
	use Data::Dumper;

	my $savepoint_name =
		sprintf("API_ART_BND_STEP_%d", $self->id());
	
	# evita loop infiniti tramite bindings
	my $first_in_binding;
	unless (defined $self->art->in_binding_status_counter() ){
		
		if (defined $ENV{ART_BINDING_SUDO_GROUP}) {
			
			if (($params->{VIRTUAL}) || $self->art()->_test_permission(
					ACTIVITY_TYPE_ID => $self->type_id,
					STATUS_ID => $self->get_current_status_id,
					ACTION_ID => $self->art()->get_activity_action_id($params->{ACTION}),
					GROUPS_ID => $self->art()->user()->su()->activity()->groups()
				)
			) {
				$self->art()->user()->su()->activity()->enable( $self->art()->get_group_id($ENV{ART_BINDING_SUDO_GROUP}) );
			} else {
				$self->art()->last_error("PRE-TEST Condition Failed: user '".$self->art()->user()->name()."' not allowed for action ".$params->{ACTION}. " ( activity_id: ".$self->id. ", activity_type: " . $self->art()->get_activity_type_name( $self->type_id() ) . ", current_status: ".$self->get_current_status_name(). ")");
				return;
			}
		}
		
		$first_in_binding = 1;
		$self->art->in_binding_status_counter_init();
		
		# la transazione sul DB inizia con il primo step
		$self->art()->_dbh()->do( "savepoint $savepoint_name" );
	}
	
	my $current_status 	= $self->get_current_status_name();
	my $current_id		= $self->id();
	
	if ( $self->art->in_binding_status_counter()->{$current_status}{$current_id}++ > 2 ){
		$self->art()->last_error("loop steps detected on binding (status: $current_status, id: $current_id)");
		$self->art->in_binding_status_counter_reset();
		$self->art()->user()->su()->activity()->disable() if (defined $ENV{ART_BINDING_SUDO_GROUP});
		return;
	};
	
	BIND_STEP: {
	
		my $binding_status = $self->_get_binding_status($params) || return;
		my $binding_action = $self->_get_binding_action($params) || return;
		
		# can_do_action
		unless ( $params->{VIRTUAL} ) {
			$self->can_do_action(NAME => $params->{ACTION})
				|| last BIND_STEP;
		}
		
		# on exit status (NB: per ora i params vengono passati solo sull'on_exit,
		#                 per la on_enter è necessario capire se passare gli originali o quelli modificati)
		$binding_status->on_exit_status($current_status, $params)
			|| last BIND_STEP;
		
		# pre action
		$binding_action->before_action($params)
			|| last BIND_STEP;
		
		$self->SUPER::_step(%{$params})
			|| last BIND_STEP;
		
		# post action
		$binding_action->after_action($params)
			|| last BIND_STEP;

		# lo step modifica le chiavi delle properties da nome a id
		if ($params->{PROPERTIES}){
			for (keys (%{$params->{PROPERTIES}})){
				if ($self->art()->test_activity_property_id($_)){
					$params->{PROPERTIES}->{$self->art()->get_activity_property_name($_)} = $params->{PROPERTIES}->{$_};
					delete $params->{PROPERTIES}->{$_};
				}
			}
		}
		
		# on exit status
		$binding_status->on_enter_status($self->get_current_status_name(), $params)
			|| last BIND_STEP;
	
		# step effettuato correttamente
		$step_ok = 1;
		
	} # end: BIND_STEP

	# da abilitare per il debug
	#printf STDERR "---%s\n", $self->art->last_error unless $step_ok;
	
	# il primo step di un'eventuale catena di binding
	# si occupa di rimuovere l'hash con il contatore
	# degli stati attraversati ed effettuare il rollback
	# del savepoint di tutta la transazione
	
	unless ($step_ok){
		$self->art->in_binding_status_counter_delete_activity_id($self->id());
	}

	if ($first_in_binding){
		$self->art->in_binding_status_counter_reset();
		$self->art()->user()->su()->activity()->disable() if (defined $ENV{ART_BINDING_SUDO_GROUP} && ! defined $self->art->in_binding_create_counter());
		
		# in caso di errore rollback di tutta la transazione
		$self->art()->_dbh()->do( "rollback to savepoint $savepoint_name" )
			unless $step_ok;
	}
	
	return $step_ok;
}

# istanzia oggetto relativo al tipo di binding selezionato
sub get_binding_handler{
	my ($self, $binding_type, $binding_name) = @_;
	my $class_name =
		$self->art()->get_activity_binding_class_name(
			$self->type_name,
			$binding_type,
			$binding_name
		);

	return $self->art()->get_instance_by_class_name($class_name, $self);
}

sub parent{
	my $self = shift;
	my $pid = $self->parent_id();
	return undef unless defined $pid;
	return API::ART::Activity::Factory->new(
		ART => $self->art() ,
		ID => $self->parent_id()
	);
}

sub add_child{
	my $self = shift;
	#my $pid = $self->parent_id();
	#return undef unless defined $pid;
	return $self->SUPER::add_child(@_);
}

sub can_do_action{
	my $self = shift;
	my %params = @_;
	
	return undef unless $self->SUPER::can_do_action(%params);
	
	my $action_name = defined $params{NAME} ? $params{NAME} : $self->art()->get_action_name($params{ID});
	
	my $params_action = {
		ACTION=> $action_name
	};
	
	my $binding_action = $self->_get_binding_action($params_action);
	
	return $binding_action->can_do();
}

#----------------------
# metodi privati
#----------------------

sub _get_binding_status{
	my $self = shift;
	return API::ART::Activity::Binding::Status->new($self);
}

sub _get_binding_action{
	my ($self, $params) = @_;
	return API::ART::Activity::Binding::Action->new($self, $params->{ACTION});
}

1;
