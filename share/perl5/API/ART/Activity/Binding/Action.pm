package API::ART::Activity::Binding::Action;

use strict;
use warnings;

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	
	my ($activity, $action) = @_;
	
	my $self = {};
		
	if (defined $action){
		
		my $res = $activity->get_binding_handler('Action', $action);
		
		my ($status, $handler, $message) = ($res->{STATUS}, $res->{HANDLER}, $res->{MESSAGE}); 
	
		if ($status eq 'KO'){
			$activity->art->last_error($message);
			return;
		}
		
		if ($status eq 'OK' && defined $handler){
			$self = {
				ACTION_HANDLER  => $handler,
				ACTIVITY		=> $activity,
				ART				=> $activity->art(),
			};
		}
	}
	
	return bless( $self, $class );
}

sub last_error{
	shift->{ACTIVITY}->art()->last_error(@_);
}

sub before_action {
	my $self = shift;
	my $params = shift;

	return 1 unless exists $self->{ACTION_HANDLER};
	
	my $ret = eval{$self->{ACTION_HANDLER}->pre($self->{ACTIVITY},$params)};
	if ($@){
		$self->last_error($@);
		return;
	}
	return $ret;
}

sub after_action {
	my $self = shift;
	my $params = shift;
	
	return 1 unless exists $self->{ACTION_HANDLER};
	
	my $ret = eval{$self->{ACTION_HANDLER}->post($self->{ACTIVITY},$params)};
	if ($@){
		$self->last_error($@);
		return;
	}
	return $ret;
}

sub can_do {
	my $self = shift;
	
	return 1 unless exists $self->{ACTION_HANDLER};

	my $ret = eval{$self->{ACTION_HANDLER}->can_do($self->{ACTIVITY})};
	if ($@){
		$self->last_error($@);
		return;
	}
	return $ret;
}

sub is_binded{
	my $self = shift;
	return exists $self->{ACTION_HANDLER};
}

1;
