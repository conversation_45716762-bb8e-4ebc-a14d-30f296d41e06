package API::ART::Activity::Binding::Action::Base;

use strict;
use warnings;

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $activity = shift;
	
	my $self  = {
		ACTIVITY => $activity,
		ART => $activity->art(),
	};
	
	return bless( $self, $class );
}

sub art      { shift->{ART}; }
sub activity { shift->{ACTIVITY}; }

sub pre{ 1 }

sub post{ 1 }

sub can_do{ 1 }

sub last_error{
	shift->{ACTIVITY}->art()->last_error(@_);
}

1;
