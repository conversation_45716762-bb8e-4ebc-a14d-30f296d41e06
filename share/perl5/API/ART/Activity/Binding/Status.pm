package API::ART::Activity::Binding::Status;

use strict;
use warnings;

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	
	my ($activity) = @_;
	
	my $self = {
		ACTIVITY => $activity,
		ART => $activity->art(),
	};
	
	return bless( $self, $class );
}

sub on_enter_status {
	my $self = shift;		
	return $self->_exec_status('on_enter', @_);
}

sub on_exit_status {
	my $self = shift;		
	return $self->_exec_status('on_exit', @_);
}

sub last_error{
	shift->{ACTIVITY}->art()->last_error(@_);
}

sub _exec_status {
	my ($self, $method_name, $current_status, $params) = @_;
	
	my $activity = $self->{ACTIVITY};
	
	# calcola lo stato SOLO se necessario
	$current_status = defined $current_status ?  $current_status 
					: $activity->get_current_status_name()
	;
	
	my $res = $activity->get_binding_handler('Status', $current_status);
	my ($status, $handler, $message) = ($res->{STATUS}, $res->{HANDLER}, $res->{MESSAGE}); 
	
	if ($status eq 'KO'){
		$self->last_error($message);
		return;
	}
	
	if ($status eq 'OK' && defined $handler){
		### se l'azione e' quella della documentazione il binding sugli stati e' sempre disabilitato
		return 1 if (defined $self->{ART}->_get_activity_virtual_action_add_documentation_name() && defined $params->{ACTION} && $params->{ACTION} eq $self->{ART}->_get_activity_virtual_action_add_documentation_name());
		my $ret = eval{$handler->$method_name($activity, $params)};
		if ($@){
			$self->last_error($@);
			return;
		}
		return $ret;
	} else {
		return 1;
	}
}

1;
