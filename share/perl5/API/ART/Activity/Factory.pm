package API::ART::Activity::Factory;

use strict;
use warnings;
use Carp;
use File::Basename;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{COM}/share/locale" );

our $DEFAULT_ACTIVITY_CLASS = 'API::ART::Activity::Binding';
our $DEFAULT_COLLECTION_CLASS = "API::ART::Collection::Activity";
our $DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

=head1 NAME

B<API::ART::Activity::Factory> - Factory Activity

=head1 SYNOPSIS

	# Uses needed packages
	use API::ART;
	use API::ART::Activity::Factory;

	# Create API::ART instance...
	my $art = API::ART->new(.......);
	# Create API::ART::Activity instance...
	my $a = API::ART::Activity::Factory->new(ART => $art, ID => 349522);
	
	# exit on error
	die $art->last_error() unless defined $a;
	
	# Get current status name
	print "Current status: ", $a->get_current_status_name(), "\n"; 

=head1 DESCRIPTION

Factory per istanziare Activity in funzione dell'ACTIVITY_TYPE_NAME con
funzionalità di binding attiva.

=head1 METHODS

Di seguito i metodi esposti dalla classe API::ART::Activity::Factory.

=cut

=head2 I<package>->B<new>( ART => I<API::ART> , ID => I<activity_id>, @params )

Il metodo B<new()> istanzia una classe in funzione dell'ACTIVITY_TYPE_NAME.

Vengono richiesti i seguenti parametri:

=over 4

=item B<ART>

Un'istanza della classe API::ART 
(parametro obbligatorio la cui mancanza innalza un'eccezione)

=item B<ID>

L'ID dell'attivita' da istanziare 
(parametro obbligatorio la cui mancanza innalza un'eccezione)

=item B<@params>

tutti gli altri parametri vengono passati al costruttore della classe Activity.

=back

In funzione dell'ID e del suo ACTIVITY_TYPE_NAME viene restituita 
un'istanza Activity con binding attivo 
(discendente da 'API::ART::Activity::Binding').

Se non esiste un rimappamento dell'ACTIVITY_TYPE_NAME, viene restituita
una classe 'API::ART::Activity::Binding'.

(impostabile tramite $DEFAULT_ACTIVITY_CLASS).

Tale metodo puo' innalzare eccezioni se richiamato senza i suoi parametri obbligatori.

In tutti gli altri casi di errore viene restituito undef e valorizzato last_error()
delle API::ART.

=cut

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
		
	my ($art, $id) = ($params->{ART}, $params->{ID});
	
	# check params
	unless (defined $art && ref $art eq 'API::ART'){
		croak "missing ART";
	}
	
	unless (defined $id){
		croak "missing ID";
	}
	
	unless ($id =~ m/^\d+$/){
		croak sprintf "ID is not a number: <%s> X<%s>", $id, unpack( 'H*', $id );
	}
	
	# reset API::ART errors
	$art->clear_last_error();
	
	# get ACTIVITY_TYPE_NAME
	my $activity_type_name = $art->_dbh()->fetch_minimalized(qq(
		-- API::ART::Activity::Factory->new()
		select	nome_tipo_attivita
		from 	v_attivita
		where	id = $id
	));
	
	# ACTIVITY not found by ID
	unless (defined $activity_type_name){
		$art->last_error(__x("{class} : ACTIVITY_ID {id} not found!", class => $class, id => $id));
		return;
	}
	
	# create ACTIVITY CLASS NAME (with binding) 
	my $binded_class_name = 
		$art->get_activity_binding_class_name($activity_type_name);
	
	# reporting internal error
	return undef unless defined $binded_class_name;
	
	# get an instance of binded ACTIVITY (if exists) 
	my $get_instance_result = 
		$art->get_instance_by_class_name($binded_class_name, %{$params});
	
	# an error occurs (compilation error on bindend activity class)
	if ($get_instance_result->{STATUS} eq 'KO'){
		$art->last_error($get_instance_result->{MESSAGE});
		return;	
	}
	
	CLASS: {
		local $@;
		eval "require $DEFAULT_ACTIVITY_CLASS;";
		if ($@){
			$art->last_error(__x("Unable to load DEFAULT_ACTIVITY_CLASS ({default_activity_class})", default_activity_class => $DEFAULT_ACTIVITY_CLASS).": " .$@);
			return undef;			
		}
	}
	
	# this factory returns:
	# if exists                       : istance of binded CLASS_NAME
	# if non exists a binded activity : istance of $DEFAULT_ACTIVITY_CLASS
	return exists $get_instance_result->{HANDLER} ?  
			$get_instance_result->{HANDLER} :	 
			$DEFAULT_ACTIVITY_CLASS->new(%{$params});
}

1;