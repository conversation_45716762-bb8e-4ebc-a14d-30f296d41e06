package API::ART::Activity::Finder::Simple;

##################################################
#
# Classe per estrazione
# API::ART::Activity::Finder::Simple
#
##################################################

use strict;
use warnings;
use Carp qw(verbose croak);

use base qw(API::Ancestor);

# can throw exception
sub new {

    my $this  = shift;
    my $class = ref($this) || $this;
    
    my $self  = $class->SUPER::new()
    	|| croak ("non riesco ad istanziare API::Ancestor\n");
    
    my %param = @_;
    
    $self->{ART} = $param{ART} 
    	|| croak("richiesto parametro ART\n");
	
	
	$self->{DB} = $self->{ART}->_dbh();
	
    return bless( $self, $class );
}

#------------------------------------------------------------------
# estrae azione analizzando tutte le properties in modalita' deep 
# (considera tutta la storia)
#------------------------------------------------------------------
sub action_deep_property{
	my $self = shift;
	my %p = @_;
	local $@;
	
	#----------------------------------------------------------------
	# check parametri
	#----------------------------------------------------------------
	my $errmsg;
	unless (
		$self->check_named_params(
			PARAMS => \%p
			, MANDATORY => {
				PROPERTY_NAME				=> {isa => 'SCALAR'},
				PROPERTY_VALUE				=> {isa => 'SCALAR'},
			}
			, OPTIONAL => {
				ACTIVITY_TYPE_NAME 			=> {isa => 'ARRAY'},
				DATE_FORMAT					=> {isa => 'SCALAR'},
			}
			, ERRMSG =>\$errmsg
			, IGNORE_EXTRA_PARAMS => 0
		)
	) {
		# errore nei parametri
		$self->last_error($errmsg);
		return undef;	
	}
	
	my $filter_property_name = $self->{DB}->quote($p{PROPERTY_NAME});
	my $filter_property_value = $self->{DB}->quote($p{PROPERTY_VALUE});
	
	my $filter_tipo_attivita = '';
	if ( defined $p{ACTIVITY_TYPE_NAME} && scalar @{$p{ACTIVITY_TYPE_NAME}}
	){	
		$filter_tipo_attivita = sprintf "AND att.nome_tipo_attivita in (%s)",
		join ',', map {$self->{DB}->quote($_)} @{$p{ACTIVITY_TYPE_NAME}}
	}
	
	my $default_date_format =
		$self->{DB}->quote(
			defined $p{DATE_FORMAT} ?
				$p{DATE_FORMAT} :
				$self->{ART}->get_default_date_format()
		);
	
	my $sql = qq(
-- action_deep_property
SELECT att.id, 
       att.nome_tipo_attivita ACTIVITY_TYPE_NAME, 
       sa.azione ACTION_NAME, 
       sa.stato_risultante STATUS_NAME, 
       to_char(sa.data_esecuzione, $default_date_format) STEP_DATE,
       sa.descrizione   ACTION_DESCRIPTION, 
       sa.login_operatore OWNER_USER_ID,
       sa.login_operatore_corrente CURRENT_USER_ID, 
       tdta.descrizione property_name, 
       dta.descrizione  property_value 
FROM   tipi_dati_tecnici_attivita tdta, 
       dati_tecnici_attivita dta, 
       v_storia_attivita sa, 
       v_attivita att 
WHERE  tdta.id_tipo_dato_tecnico_attivita = dta.id_tipo_dato_tecnico_attivita 
       AND dta.data_esecuzione = sa.data_esecuzione 
       AND sa.id_attivita = dta.id_attivita 
       AND att.id = sa.id_attivita 
       AND dta.morto IS NULL 
       AND tdta.descrizione = $filter_property_name 
       AND dta.descrizione = $filter_property_value
       $filter_tipo_attivita   
	);
	
	my $out = eval { $self->{DB}->fetchall_hashref($sql) };
	if ($@){
		$self->{ART}->last_error('action_deep_property: ' . $@);
		return undef;
	}
	
	return $out;
}

#---------------------------------------------------------------------
# UNIT TEST
#---------------------------------------------------------------------
if ( __FILE__ eq $0 ) {

#	use API::ART;
#	use Data::Dumper;
#	
#	my $api_art = API::ART->new(
#		ARTID => $ENV{ARTID}
#		,USER => $ENV{TLCDLV_SCRIPT_USER}
#		,PASSWORD => $ENV{TLCDLV_SCRIPT_PASSWORD}
#		,AUTOSAVE => 0
#		,DEBUG => 0 #$ENV{ART_DB_DEBUG}
#	);
#	
#	my $p = __PACKAGE__->new(ART=>$api_art);
#	
#	my $out = $p->action_deep_property(
#		ACTIVITY_TYPE_NAME=>['NOTIFICA_ULL','NOTIFICA_WLR'],
#		PROPERTY_NAME=>'FILE',	
#		PROPERTY_VALUE=>'1392178,15',
#		DATE_FORMAT=>'dd-mm-yyyy hh24:mi'
#	);
#	
#	unless (defined $out){
#		croak $api_art->last_error();
#	}
#	
#	print Dumper($out);

}

1;
