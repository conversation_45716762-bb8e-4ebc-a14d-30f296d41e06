################################################################################
#
# API::ART::Activity::History
#
################################################################################

=head1 NAME

B<API::ART::Activity::History> - Classe per la ricerca e navigazione delle transizioni di un'attivitE<agrave>.

=head1 SYNOPSIS

	my $history = API::ART::Activity::History->new(ACTIVITY => <API::ART::Activity>);
	
	while( $transition = $history->next ) {
		print "Data Transizione:".
			$transition->transition_date.	# Oggetto API::ART::Activity::History::Transition
			"\nAllegati:".
			$transition->attachments->list	# Oggetto API::ART::Activity::History::Transition::Attachment
	}
	
=head1 DESCRIPTION

Questo package consente di accedere alla storia di una attivitE<agrave> di ART attraverso un'interfaccia I<Object Oriented>.

La storia dell'attivitE<agrave> viene restituita dall'iteratore con i metodi B<next>, B<previous>, B<first>, B<last> e B<goto>
come oggetto C<API::ART::Activity::History::Transition> (e come oggetti inehrits C<API::ART::Activity> in presenza della condizione
merge_children restituendo le info dell'attivitE<agrave>) che racchiude la transizione di stato (stato di partenza, stato di arrivo,
azione, descrizione, utente e proprietE<agrave>). E<Egrave> possibile effettuare una ricerca nella storia dell'attivitE<agrave> in modo da
impostare un filtro che restituisca solo le transizioni d'interesse.

Una transizione E<egrave> cosE<igrave> composta:

	{
		'ACTION_ID' => '143',
		'CURRENT_USER_ID' => undef,
		'FROM_STATUS_ID' => '34',
		'TRANSITION_DATE' => '20141212000015',
		'ATTACHMENTS' => [
			{
			'NOME_FILE_CLIENT' => 'ajax-loader.gif',
			'NOME_FILE_SERVIZIO' => '7792-201412120000-0017.tar.bz2',
			'SIZE' => '23131'
			}
		],
		'DESCRIPTION' => 'Azione automatica',
		'USER_ID' => '1',
		'PROPERTIES' => [
			{
				'PROPERTY_VALUE' => '201502101400',
				'PROPERTY_ORDER' => '0',
				'PROPERTY_ID' => '181',
				'PROPERTY_VALIDITY' => '1',
				'PROPERTY_NAME' => 'kart_message'
			}
		                ],
		'TO_STATUS_ID' => '91'
	}


=cut

package API::ART::Activity::History;

use strict;
use warnings;
use Carp;
use Digest::MD5;
use API::ART::Activity::Factory;
use API::ART::Activity::History::Transition;

use base qw(API::Ancestor);

################################################################################
#  C O S T A N T I
################################################################################

use constant {
	TYPE_TRANSITION => 0,
	TYPE_CHILD => 1,
};


################################################################################
#  P R O P R I E T A '   P U B B L I C H E
################################################################################

our $VERSION = '0.01';



################################################################################
#  M E T O D I   P R I V A T I
################################################################################

sub _art {
	shift->('ART');
}

sub _activity_id {
	shift->('ACTIVITY_ID');
}

sub _activity_type_id {
	shift->('ACTIVITY_TYPE_ID');
}

sub _activity_info_properties {
	shift->('ACTIVITY_INFO_PROPERTIES');
}

sub _activity_class_name {
	shift->('ACTIVITY_CLASS_NAME');
}

sub _activity_handler {
	shift->('ACTIVITY_HANDLER');
}

sub _check_filters {
	my $self = shift;
	my $errmsg;
	my %params = @_;
	my $art = $self->_art();
	$art->clear_last_error();

	croak $errmsg
		unless	$self->check_named_params(
			 ERRMSG 				=> \$errmsg
			,IGNORE_EXTRA_PARAMS 	=> 0
			,PARAMS 				=> \%params
			,MANDATORY => {	}
			,OPTIONAL => {
				INITIAL_STATUS 	=> { isa => 'ARRAY' }
				,ACTION			=> { isa => 'ARRAY' }
				,TARGET_STATUS	=> { isa => 'ARRAY' }
				,START_DATE		=> { isa => 'SCALAR', custom_check => sub { return $art->last_error() unless $art->test_date($_[0]); return } }
				,END_DATE		=> { isa => 'SCALAR', custom_check => sub { return $art->last_error() unless $art->test_date($_[0]); return } }
				,PROPERTY		=> { isa => 'HASH' }
				,HAS_ATTACHMENT	=> { isa => 'SCALAR', list => [0, 1] }
				,ATTACHMENT		=> { isa => 'ARRAY' }
				,STRICT			=> { isa => 'SCALAR', list => [0, 1] }
				,MERGE_CHILDREN	=> { isa => 'SCALAR', list => [0, 1] }
			}
		);
	
	# Il parametro STRICT abilita la Validazione dei filtri inseriti sugli stati, azioni e property attualmente configurati nel tipo attivita'
	my %artparams;
	%artparams = (ACTIVITY_TYPE_ID => $self->_activity_type_id()) if $params{STRICT};
	my @stati_possibili = keys %{$art->enum_activity_status(%artparams)};
	my @azioni_possibili = keys %{$art->enum_activity_action(%artparams)};
	my @property_possibili = $params{STRICT} ? @{$self->_activity_info_properties()} : keys %{$art->enum_activity_property()};
	
	if (defined $params{INITIAL_STATUS}) {
		for my $status (@{$params{INITIAL_STATUS}}) {
			$art->last_error("Invalid status name $status!!")
				&& (return undef)
					unless grep {$_ eq $status} @stati_possibili;
		}
	}
	if (defined $params{TARGET_STATUS}) {
		for my $status (@{$params{TARGET_STATUS}}) {
			$art->last_error("Invalid status name $status!!")
				&& (return undef)
					unless grep {$_ eq $status} @stati_possibili;
		}
	}
	if (defined $params{ACTION}) {
		for my $action (@{$params{ACTION}}) {
			$art->last_error("Invalid action name $action!!")
				&& (return undef)
					unless grep {$_ eq $action} @azioni_possibili;
		}
	}
	if (defined $params{PROPERTY}) {
		for my $prop (keys %{$params{PROPERTY}}) {
			$art->last_error("Invalid property name $prop!!")
				&& (return undef)
					unless grep {uc $_ eq uc $prop} @property_possibili;
		}
	}
	return 1;
}


sub _prepare_query { # FIXME prevedere dei metodi per sostituire le query SIRTI::ART::Lookup::ART->new per restituire anche gli oggetti "morti"
	my $self = shift;
	my $db = $self->_art()->_dbh();
	my $operator = 'and';
	my $def_date_format = $self->_art()->get_default_date_format;
	
	my @where;
	my @property_where;
	my @inner_where;
	my $join_tdta;
	
	# 1- nell'ordine dei parametri da passare alla prepare
	my @bind = ($def_date_format, $self->_activity_id());

	# 2- nell'ordine dei parametri da passare alla prepare
	if (defined $self->('FILTERS')->{PROPERTY}) {
		my @or;
		for (keys %{$self->('FILTERS')->{PROPERTY}}) {
			push @or, "(x.property_id = (select id_tipo_dato_tecnico_attivita from tipi_DATI_TECNICI_ATTIVITA where descrizione = ?)
			and x.property_value = ?)";
			push @bind, $_, $self->('FILTERS')->{PROPERTY}->{$_};
		}
		my $condition = "(".join ("\n\tor ", @or).")";
		push @property_where, $condition;
		push @inner_where, "x.transition_date in ( select x.transition_date from x where " . join(" and ", @property_where) . ")";
		$join_tdta = 1;
	}
	
	if (defined $self->('FILTERS')->{HAS_ATTACHMENT}) {
		push @inner_where, ($self->('FILTERS')->{HAS_ATTACHMENT} ? '' : "not ") . "exists ( select 1 from allegati_azione a where a.id_attivita = x.activity_id and a.id_action = x.transition_date)";
	}

	my $filename = '';
	if (ref($self->('FILTERS')->{ATTACHMENT}) eq 'ARRAY') {
		$filename = " and a.nome_file_client in (". join(', ', map { '?' } @{$self->('FILTERS')->{ATTACHMENT}}) . ") ";
		push @bind, @{$self->('FILTERS')->{ATTACHMENT}};
		push @inner_where, "exists ( select 1 from allegati_azione a where a.id_attivita = x.activity_id and a.id_action = x.transition_date $filename)";
	}


	# 3- nell'ordine dei parametri da passare alla prepare

#	Se si vuole implementare un filtro che possa prendere gli estremi di un intervallo di stati del flusso, una possibile implementazione e' la seguente:
#	if (defined $self->('FILTERS')->{FROM_STATUS}) {
#		push @where, "transition_date >= ( select min(transition_date) from y where y.from_status_id = ( select id_stato from stati where nome = '?' ) )";
#		push @bind, $self->('FILTERS')->{FROM_STATUS};
#	}
#	if (defined $self->('FILTERS')->{TO_STATUS}) {
#		push @where, "transition_date >= ( select min(transition_date) from y where y.from_status_id = ( select id_stato from stati where nome = '?' ) )";
#		push @bind, $self->('FILTERS')->{TO_STATUS};
#	}

	push @bind, $self->_activity_id();

	if (defined $self->('FILTERS')->{INITIAL_STATUS}) {
		my $qmarks = join (', ', map { '?' } @{$self->('FILTERS')->{INITIAL_STATUS}});
		push @where, "y.from_status_id in (select id_stato from stati where nome in ($qmarks))";
		push @bind, @{$self->('FILTERS')->{INITIAL_STATUS}};
	}
	if (defined $self->('FILTERS')->{TARGET_STATUS}) {
		my $qmarks = join (', ', map { '?' } @{$self->('FILTERS')->{TARGET_STATUS}});
		push @where, "y.to_status_id in (select id_stato from stati where nome in ($qmarks))";
		push @bind, @{$self->('FILTERS')->{TARGET_STATUS}};
	}
	if (defined $self->('FILTERS')->{ACTION}) {
		my $qmarks = join (', ', map { '?' } @{$self->('FILTERS')->{ACTION}});
		push @where, "y.action_id in (select id_action from action where nome in ($qmarks))";
		push @bind, @{$self->('FILTERS')->{ACTION}};
	}
	if (defined $self->('FILTERS')->{START_DATE}) {
		push @where, "y.transition_date >= to_date(?, ?)";
		push @bind, $self->('FILTERS')->{START_DATE};
		push @bind, $def_date_format;
	}
	if (defined $self->('FILTERS')->{END_DATE}) {
		push @where, "y.transition_date <= to_date(?, ?)";
		push @bind, $self->('FILTERS')->{END_DATE};
		push @bind, $def_date_format;
	}
	
	unless ($self->get_merge_children()){
		push @where, "y.type = ".TYPE_TRANSITION;
	}
	
	my $sql = "
	
select
	y.id
	,to_char(y.transition_date, ?) transition_date
	,y.type
from (
	with x as (
		select a.id_attivita activity_id
			,sa.data_esecuzione transition_date
			,sa.descr_azione description
			,(
			  select max(sa1.data_esecuzione)
			  from  storia_attivita sa1
			  where sa1.id_attivita = sa.id_attivita
			    and sa1.data_esecuzione < sa.data_esecuzione
			 ) prev_transition_date
			,sa.id_action action_id
			,sa.id_stato_risultante to_status_id
			,sa.id_operatore user_id
			,sa.id_operatore_corrente current_user_id".
	($join_tdta ? "
			,tdta.id_tipo_dato_tecnico_attivita property_id
			,tdta.descrizione property_name
			,dta.descrizione property_value
			,tdtaa.posizione property_order
			,case when tdta.morto is not null then 0 else 1 end property_validity
	" : "")."
		from  attivita a
		inner join storia_attivita sa on sa.id_attivita = a.id_attivita".
	($join_tdta ? "
		left join dati_tecnici_attivita dta on dta.id_attivita = sa.id_attivita and dta.data_esecuzione = sa.data_esecuzione
		left join tipi_dati_tecnici_attivita tdta on tdta.id_tipo_dato_tecnico_attivita = dta.id_tipo_dato_tecnico_attivita
		left join tipi_dati_tecnici_att_action tdtaa
			on tdtaa.id_tipo_dato_tecnico_attivita = dta.id_tipo_dato_tecnico_attivita
				and tdtaa.id_tipo_attivita = a.id_tipo_attivita
				and tdtaa.id_action = sa.id_action
	" : "")."
		where a.id_attivita = ?
	)
	select ".TYPE_TRANSITION." type
		, to_char(x.transition_date, 'yyyymmddhh24miss') id
		, x.transition_date
		,x.description
		,nvl(sa2.id_stato_risultante, '0') from_status_id
		,x.action_id
		,x.to_status_id
		,x.user_id
		,x.current_user_id".
	($join_tdta ? "
		,x.property_id
		,x.property_name
		,x.property_value
		,x.property_order
		,x.property_validity
		--,(select count('x') from storia_attivita where id_attivita = x.activity_id) transition_count
		--,(select count('x') from dati_tecnici_attivita where id_attivita = x.activity_id) property_count
	" : "")."
	from  x
	left join storia_attivita sa2
		on sa2.id_attivita = x.activity_id
			and sa2.data_esecuzione = x.prev_transition_date
		". (scalar(@inner_where)>0 ? "where ". join ("\n\t\t$operator ", @inner_where) : "") . "
	union all
	select ".TYPE_CHILD." type
		, to_char(apaf.figlio) id
	  	,sa.data_esecuzione transition_date
	  	,null description
		,null from_status_id
		,null action_id
		,null to_status_id
		,null user_id
		,null current_user_id".
	($join_tdta ? "
		,null property_id
		,null property_name
		,null property_value
		,null property_order
		,null property_validity
		--,(select count('x') from storia_attivita where id_attivita = x.activity_id) transition_count
		--,(select count('x') from dati_tecnici_attivita where id_attivita = x.activity_id) property_count
	" : "")."
	From attivita a
	  join a_padre_a_figlio apaf on apaf.padre = a.ID_ATTIVITA
	  join attivita a2 on a2.id_attivita = apaf.figlio
	  join storia_attivita sa on sa.id_attivita = apaf.figlio and sa.ID_ACTION = (select id_action from action where flag_action_partenza = 'A')
	where a.id_attivita = ?
	  and exists (
		select 1
		from permission_sistemi pschild
		where pschild.id_sistema = a2.id_sistema
		  and pschild.id_gruppo_abilitato in (".join (',', @{$self->_art()->user()->groups()}).")
	)
) y

".(scalar(@where)>0 ? "where ". join ("\n\t$operator ", @where) : "") ."
order by y.transition_date asc, y.type asc, y.id asc".
	($join_tdta ? ", y.property_order asc" : "")."

";
#TODO aggiungere il tdta.tipo_ui ?
#,case when tdtaa.id_tipo_attivita is not null then 0 else 1 end property_inactive
	$self->('PREPARE_PARAMS', \@bind);
	return $self->_art()->_create_prepare(__PACKAGE__.'_PREPARE_'.Digest::MD5::md5_hex($sql), $sql);
}

sub _fetch_records {
	my ($self, @initial_info) = @_;
	my $results = [];
	my $records;
	my $prepare = $self->_prepare_query();
	return undef unless defined $prepare;
	unless ($records = $prepare->fetchall_hashref(@{$self->('PREPARE_PARAMS')})) {
		$self->_art()->last_error($self->_art()->_dbh()->get_errormessage);
		return undef;
	}
	
	my ($prev_id, $result);
	
	for my $record (@$records) {
		
		$record->{TYPE} = $record->{TYPE} eq TYPE_TRANSITION ? 'TRANSITION' : 'ACTIVITY';
		
		if ($prev_id && $prev_id ne $record->{ID}) {
			push @$results, $result;
			$result = {};
		}
		
		if ( !$prev_id || $prev_id ne $record->{ID} ) {
			%$result = map { $_ => $record->{$_} } grep { ! /^property/i } keys %$record;
		}
		
		my %props = map { $_ => $record->{$_} } grep { /^property/i } keys %$record;
		push (@{$result->{PROPERTIES}}, \%props) if defined $props{PROPERTY_ID};
		
		$prev_id = $record->{ID};
	}
	if (scalar(keys %$result) > 0 ) {
		push @$results, $result;
		$result = {};
	}
	return $results;
}


sub _set_iterator {
	my $self = shift;
	$self->_iterator;
	#$self->('__ITERATOR__', $self->_iterator);
}
sub _get_iterator {
	my $self = shift;
	$self->_art()->clear_last_error();
	return $self->_set_iterator;
	#$self->_set_iterator unless $self->('__ITERATOR__');
	#return $self->('__ITERATOR__');
}

#
# Creato _iterator secondo il pattern all'url
# http://www.perl.com/pub/2005/06/16/iterators.html
# Perl.com -- Understanding and Using Iterators
#

sub _iterator {
	my $self = shift;
	#my $index = -1;
	#my $done;

	my $results = $self->('__RESULT__');
	
	return sub {
		my $operation = shift;
		my $idx = shift;
		return $self->('__INDEX__')+1 if $operation eq 'index';
		
		if (!$operation || $operation eq 'next') {
			$self->('__INDEX__', $self->('__INDEX__')+1);
			$self->('__DONE__', $self->('__INDEX__') > $#$results);
			$self->('__INDEX__', $self->('__INDEX__')-1) if $self->('__DONE__');
		}
		
		if ($operation eq 'reset') {
			$self->('__INDEX__', -1);
			$self->('__DONE__', 0);
			return 1;
		}
		
		if ($operation eq 'previous') {
			$self->('__INDEX__', $self->('__INDEX__')-1);
			$self->('__DONE__', $self->('__INDEX__') < 0);
			$self->('__INDEX__', $self->('__INDEX__')+1) if $self->('__DONE__');
		}

		if ($operation eq 'first') {
			$self->('__INDEX__', 0);
			$self->('__DONE__',0);
		}
		if ($operation eq 'last') {
			$self->('__INDEX__', $#$results);
			$self->('__DONE__',0);
		}
		if ($operation eq 'goto' && defined $idx) {
			if ($idx < 0) {
				$self->('__INDEX__', $#$results + $idx + 1);
				$self->('__DONE__', $self->('__INDEX__') < 0);
			} else {
				$self->('__INDEX__', $idx - 1);
				$self->('__DONE__',$self->('__INDEX__') > $#$results);
			}
		}
		$self->('__DONE__',1) if scalar(@$results)== 0;

		return undef if $self->('__DONE__') || $self->('__INDEX__') == -1;
		
		if ($results->[$self->('__INDEX__')]->{TYPE} eq 'TRANSITION'){
			return $self->_get_transition($self->('__INDEX__'));
		} else {
			return $self->_get_children($self->('__INDEX__'));
		}
	};
}

sub _init {
	my ($self,%params) = @_;

	return undef unless $self->_set_filter(%{$params{FILTERS}});
	return undef unless $self->_set_merge_children($params{MERGE_CHILDREN});

	$self->_search || return undef;
}

sub _get_transition {
	my $self = shift;
	my $index = shift;
	my $transition = $self->('_TRANSITIONS');
	$transition->{$self->('__RESULT__')->[$index]->{ID}} = API::ART::Activity::History::Transition->new(
		ART	=> $self->_art()
		, ACTIVITY_ID	=> $self->_activity_id()
		, ACTIVITY_INFO_PROPERTIES	=> $self->_activity_info_properties()
		, ACTIVITY_HANDLER	=> $self->_activity_handler()
		, ID => $self->('__RESULT__')->[$index]->{ID}
	) unless $transition->{$self->('__RESULT__')->[$index]->{ID}};
	return $transition->{$self->('__RESULT__')->[$index]->{ID}};
}

sub _get_children {
	my $self = shift;
	my $index = shift;
	my $transition = $self->('_TRANSITIONS');
	
	# Determino a quale classe fare riferimento per il nome della classe da usare per la
	# creazione dell'istanza del 'figlio'
	my $class = eval '$' . $self->_activity_class_name() . '::DEFAULT_CHILDREN_CLASS';
	CLASS: {
		local $@;
		eval "require $class;";
		if ($@){
			$self->_art()->last_error("Unable to require DEFAULT_ACTIVITY_CLASS ($class): " .$@);
			return undef;
		}
	}

	$transition->{$self->('__RESULT__')->[$index]->{ID}} = $class->new(
		ART => $self->_art()
		, ID => $self->('__RESULT__')->[$index]->{ID}
	) unless $transition->{$self->('__RESULT__')->[$index]->{ID}};
	return $transition->{$self->('__RESULT__')->[$index]->{ID}};
}

sub _search {
	my $self = shift;
	my $result = $self->_fetch_records() || return undef;
	$self->('__RESULT__', $result);
	$self->_set_iterator() || return undef;
	
	return 1;
}


################################################################################
#  M E T O D I   P U B B L I C I
################################################################################

=pod

=head1 METHODS

=head2 I<package>->B<new>( ACTIVITY => I<API::ART::Activity> [, FILTERS => <hashref>, MERGE_CHILDREN => <boolean> ] )

Il metodo B<new()> E<egrave> il costruttore di classe e accetta i seguenti parametri:

=over 4

=over 4

=item B<ACTIVITY> obbligatorio

Un'istanza della classe API::ART::Activity (o API::ART::Activity::Binding)

=item B<FILTERS> opzionale => <hashref>

Imposta direttamente i filtri descritti in I<package>->B<set_filter>

=item B<MERGE_CHILDREN> opzionale => <boolean>

Imposta direttamente l'opzione descritta in I<package>->B<set_merge_children>

=back

=back

=cut

sub new() {
	my $this = shift;
	my $class = ref($this) || $this;
	my %params = @_;
	my $errmsg;
	my %self;
	
	croak $errmsg
		unless	$class->check_named_params(
			 ERRMSG 				=> \$errmsg
			,IGNORE_EXTRA_PARAMS 	=> 0
			,PARAMS 				=> \%params
			,MANDATORY => {
				ACTIVITY => { isa => undef, inherits => [ 'API::ART::Activity' ] }
			}
			,OPTIONAL => {
				FILTERS		=> { isa => 'HASH' },
				MERGE_CHILDREN	=> { isa => 'SCALAR', list => [0,1] }
			}
		);
	
	$self{ART} = $params{ACTIVITY}->art();
	
	$self{ACTIVITY_ID} = $params{ACTIVITY}->id();
	$self{ACTIVITY_TYPE_ID} = $params{ACTIVITY}->type_id();
	$self{ACTIVITY_INFO_PROPERTIES} = $params{ACTIVITY}->info('PROPERTIES');
	$self{ACTIVITY_CLASS_NAME} = ref($params{ACTIVITY});
	$self{ACTIVITY_HANDLER} = $params{ACTIVITY}->_get_handler(1);
	
	$self{FILTERS} = {};
	$self{MERGE_CHILDREN} = 0;
	$self{_TRANSITIONS} = {}; # cache interna delle transizioni
	
	$self{__INDEX__} = -1;
	$self{__DONE__} = undef;

	my $closure = sub {
		my $field = shift;
		if (@_) { $self{$field} = shift }
		return $self{$field};
	};
	$closure = bless( $closure, $class );
	
	$closure->_init( FILTERS => $params{FILTERS}||{}, MERGE_CHILDREN => $params{MERGE_CHILDREN}||0 ) || return undef;
	
	return $closure;
}

=pod

=head2 I<package>->B<set_filter>( <hash> )

Imposta i filtri, oppure senza argomenti svuota tutti i filtri ed aggiorna la ricerca. Di seguito la descrizione dei filtri accettati:

=over 4

=over 4

=item B<INITIAL_STATUS> => <arrayref>

Imposta il filtro sullo stato di partenza accettando una lista di B<STATUS_NAME>

=item B<ACTION> => <arrayref>

Imposta il filtro sull'azione accettando una lista di B<ACTION_NAME>

=item B<TARGET_STATUS> => <arrayref>

Imposta il filtro sullo stato di destinazione accettando una lista di B<STATUS_NAME>

=item B<STRICT> => <boolean>

Consente di impostare la ricerca solo sugli stati, azioni e proprietE<agrave> attualmente configurate per il tipo di attivitE<agrave> in esame

=item B<START_DATE> => <scalar>

Imposta il filtro in un range che va dalla data indicata nel formato I<YYYYMMDDHH24MISS>

=item B<END_DATE> => <scalar>

Imposta il filtro in un range che va fino alla data indicata nel formato I<YYYYMMDDHH24MISS>

=item B<PROPERTY> => <hashref>

Imposta il filtro sulle proprieta

=item B<HAS_ATTACHMENT> => <boolean>

Permette di cercare solo le transizioni a cui E<egrave> associato almeno un allegato

=item B<ATTACHMENT> => <arrayref>

Permette di cercare solo le transizioni che abbiano associato gli allegati definiti nella lista dei nomi file richiesti

=item B<MERGE_CHILDREN> => <boolean>

Permette di aggiungere nella history le apertura delle attivita' figlie

=back

=back

=cut

sub _set_filter {
	my $self = shift;
	my %params = @_;

	$self->restart();

	if (%params) {
		return undef unless $self->_check_filters(%params);
	}
	$self->('FILTERS', \%params);
	return 1;
}

sub set_filter {
	my $self = shift;
	my %params = @_;
	
	return undef unless $self->_set_filter(%params);
	$self->_search() || return undef;
	return 1;
}

=pod

=head2 I<package>->B<get_filter>( )

Restituisce l'I<hashref> di tutti i filtri impostati

=cut

sub get_filter {
	my $self = shift;

	return $self->('FILTERS');
}

=pod

=head2 I<package>->B<set_merge_children>( <boolean> )

Imposta la visualizzazione dei figli

=cut

sub _set_merge_children {
	my $self = shift;
	my $merge_children = shift;
	
	$self->_art()->last_error("merge_children can be only 0|1")	
		&& return undef
			unless ($merge_children =~/^(0|1)$/);
	
	$self->('MERGE_CHILDREN', $merge_children);
	return 1;
}

sub set_merge_children {
	my $self = shift;
	my $merge_children = shift;
	
	return undef unless $self->_set_merge_children($merge_children);

	$self->_search() || return undef;
	return 1;
}

=pod

=head2 I<package>->B<get_merge_children>( )

Restituisce 1 in presenza di visualizzazione dei figli impostata, 0 altrimenti.

=cut

sub get_merge_children {
	my $self = shift;

	return $self->('MERGE_CHILDREN');
}

=pod

=head2 I<package>->B<activity>( )

Restituisce l'istanza <API::ART::Activity> a cui afferisce il I<package>

=cut

sub activity {
	my $self = shift;

	return API::ART::Activity::Factory->new(ART => $self->_art(), ID => $self->_activity_id());
}

=pod

=head2 I<package>->B<refresh>( [ <boolean> ] )

Aggiorna il set delle transizioni con il filtro impostato, ed opzionalmente accetta un parametro booleano per mantenere la posizione attuale dell'iteratore

=cut

sub refresh {
	my $self = shift;
	my $keep_position = shift;
	my $rc;
	my $position = $self->position||1;

	$rc = $self->_search();
	$self->goto($position) if ($rc && $keep_position);
	return $rc;
}

=pod

=head2 I<package>->B<serialize>( [ OUTPUT => <scalar> | RAW => <boolean> ] )

Restituisce l'<arrayref> di <hashref> delle transizioni (e dei figli quando applicabile) del filtro corrente ed accetta i parametri di seguito descritti:

=over 4

=over 4

=item B<OUTPUT> => <scalar>

accetta 'id' o 'name' (default), ed indica se rappresentare i valori delle chiavi (action, from_status, to_status, user e current_user) per 
le transizione e delle info per le attivitE<agrave> figlie con il loro id o il loro name, e restituisce un arrayref degli hashref delle transizioni/attivitE<agrave>

=item B<RAW> => <boolean>

accetta un booleano 0 (default) o 1 per ritornare la struttura hashref interna dell'oggetto API::ART::Activity::History indicata solo per uso debug.

=back

=back

=cut

sub serialize {
	my ($self, %params) = @_;
	my $errmsg;
	my $art = $self->_art();
	$art->clear_last_error();

	croak $errmsg
		unless	$self->check_named_params(
			 ERRMSG 				=> \$errmsg
			,IGNORE_EXTRA_PARAMS 	=> 0
			,PARAMS 				=> \%params
			,MANDATORY => {	}
			,OPTIONAL => {
				OUTPUT	=> { isa => 'SCALAR', list => [ 'id', 'name' ] }
				,RAW	=> { isa => 'SCALAR', list => [ 0, 1 ] }
			}
		);
		
	return [ @{$self->('__RESULT__')} ] if $params{RAW};

	my $new_structure = [];
	
	my $position = $self->position;
	$self->restart() if $position > 0;

	while (my $row = $self->next) {
		my $row_serialized = $row->serialize(%params);
		$row_serialized->{TYPE} = ref($row) eq 'API::ART::Activity::History::Transition' ? 'TRANSITION' : 'ACTIVITY';
		push @$new_structure, $row_serialized;
	}
	$self->goto($position) if $position > 0;
	
	return $new_structure;
	
}

=pod

=head2 I<package>->B<count>( )

Restituisce il numero di transizioni/figli del filtro corrente;

=cut

sub count {
	my $self = shift;
	return scalar (@{$self->('__RESULT__')});
}

=pod

=head2 I<package>->B<next>( )

Itera le transizioni restituendo il prossimo elemento rispetto alla posizione attuale come oggetto C<API::ART::Activity::History::Transition> o come oggetto inehrits C<API::ART::Activity>;

=cut

sub next { $_[0]->_get_iterator->('next') }

=pod

=head2 I<package>->B<restart>( )

Resetta l'iteratore riportandolo allo stato iniziale;

=cut

sub restart { $_[0]->_get_iterator->('reset') }

=pod

=head2 I<package>->B<previous>( )

Restituisce l'elemento precedente rispetto alla posizione attuale dell'iteratore come oggetto C<API::ART::Activity::History::Transition> o come oggetto inehrits C<API::ART::Activity>;

=cut

sub previous { $_[0]->_get_iterator->('previous') }

=pod

=head2 I<package>->B<first>( )

Restituisce la prima transizione del filtro come oggetto C<API::ART::Activity::History::Transition> o come oggetto inehrits C<API::ART::Activity>;

=cut

sub first { $_[0]->_get_iterator->('first') }

=pod

=head2 I<package>->B<last>( )

Restituisce l'ultima transizione del filtro come oggetto C<API::ART::Activity::History::Transition> o come oggetto inehrits C<API::ART::Activity>;

=cut

sub last { $_[0]->_get_iterator->('last') }

=pod

=head2 I<package>->B<current>( )

Restituisce la transizione alla posizione attuale dell'iteratore come oggetto C<API::ART::Activity::History::Transition> o come oggetto inehrits C<API::ART::Activity>;

=cut

sub current { $_[0]->_get_iterator->('dummy') }

=pod

=head2 I<package>->B<position>( )

Restituisce la posizione attuale dell'iteratore come <scalar> compatibile alla posizione richiesta dal metodo I<package>->B<goto>;

NOTA: in caso di history vuota restituisce il valore '0' che, coerentemente, non può essere utilizzato dal metodo I<package>->B<goto>

=cut

sub position { $_[0]->_get_iterator->('index') }

=pod

=head2 I<package>->B<goto>( <scalar> )

Permette di navigare nelle transizioni alla posizione indicata da <scalar> come numero positivo
che puE<ograve> andare da 1 a I<package>->B<count>, oppure come numero negativo che puE<ograve> andare
da -1 (ultimo risultato) fino al primo (- I<package>->B<count>) restituendo l'oggetto C<API::ART::Activity::History::Transition> o l'oggetto inehrits C<API::ART::Activity>,
mentre se il numero E<egrave> fuori dall'intervallo dei risultati restituisce undef;

=cut


sub goto {
	my $self = shift;
	my $idx = shift;
	croak "Wrong goto() parameter '$idx'! it must be a positive or negative integer and cannot be 0 !!" if $idx !~ /^-?[1-9]\d*$/;
	return $self->_get_iterator->('goto', $idx);
}

# sub DESTROY{
# 	my $self = shift;
# 	print STDERR __PACKAGE__."::DESTROY\n";
# }


1;


__END__

=pod

=head1 NOTES

E<Egrave> buona norma salvare l'istanza dell'oggetto C<API::ART::Activity::History> in una variabile.
Ogni volta che si chiama B<set_filter> o B<set_merge_children>, il B<costruttore> o C<< API::ART::Activity->history() >>,
le transizioni vengono aggiornate e l'iteratore riparte dal primo elemento.

=head1 BUGS

Eventuali bug riscontrati dovranno essere segnalati su B<ART - Global Services>: L<https://services.sirti.net/artit/> aprendo un apposito DEV-TICKET.

=head1 HISTORY

=over

=item Ver. 0.01

Prima release del modulo

=back

=head1 AUTHOR

Alvaro Livraghi <<EMAIL>>

=cut
