################################################################################
#
# API::ART::Activity::History::Transition
#
################################################################################

=head1 NAME

B<API::ART::Activity::History::Transition> - Classe per la ricerca e navigazione delle transizioni di un'attivitE<agrave>.

=head1 SYNOPSIS

	my $Transition = API::ART::Activity::History::Transition->new(ACTIVITY => <API::ART::Activity>);
	
	print Dumper $Transition->serialize;
	
=head1 DESCRIPTION

Questo package permette di accedere alle informazioni sulla transizione di stato di una attivitE<agrave>.

=cut

package API::ART::Activity::History::Transition;

use strict;
use warnings;
use Carp;
use API::ART::Activity::Factory;
use API::ART::Activity::History::Transition::Attachment;

use base qw(API::Ancestor);


################################################################################
#  P R O P R I E T A '   P U B B L I C H E
################################################################################

our $VERSION = '0.01';


################################################################################
#  M E T O D I   P R I V A T I
################################################################################

sub _prepare_query {
	my $self = shift;
	my $db = $self->art()->_dbh();
	
	my $sql = "
select to_char(y.transition_date, 'yyyymmddhh24miss') id -- univoco
	,to_char(y.transition_date, ?) transition_date
	,y.description
	,y.from_status_id
	,y.action_id
	,y.to_status_id
	,y.user_id
	,case when y.CURRENT_USER_CHANGE_REASON is not null then y.current_user_id else null end current_user_id
	,y.property_id
	,y.property_name
	,y.property_value
	,y.property_order
	,y.property_validity
	,y.CURRENT_USER_CHANGE_REASON
from (
	with x as (
		select a.id_attivita activity_id
			,sa.data_esecuzione transition_date
			,sa.descrizione description
			,(
			  select max(sa1.data_esecuzione)
			  from  storia_attivita sa1
			  where sa1.id_attivita = sa.id_attivita
			    and sa1.data_esecuzione < sa.data_esecuzione
			 ) prev_transition_date
			,ac.id_action action_id
			,st.id_stato to_status_id
			,op1.id_operatore user_id
			,op2.id_operatore current_user_id
			,tdta.id_tipo_dato_tecnico_attivita property_id
			,tdta.descrizione property_name
			,case
				when tdta.tipo_ui in ('MOOKUP','TAGS') and dta.descrizione is null then '[]'
				else dta.descrizione
			 end property_value
			,tdtaa.posizione property_order
			,case when tdta.morto is not null then 0 else 1 end property_validity
      ,case
        when sa.tipo_pic = 'AZIONE_CON_PIC' then 'PIC'
        when sa.tipo_depic = 'AZIONE_CON_DEPIC' then 'DEPIC'
        when sa.tipo_depic = 'NO_PERMESSI' then 'DEPIC_NO_PERMESSI'
        when sa.tipo_assegnazione = 'ESPLICITA' and sa.tipo_deassegnazione is null then 'ASSEGNAZIONE'
        when sa.tipo_assegnazione = 'ESPLICITA' and sa.tipo_deassegnazione = 'ESPLICITA' then 'RIASSEGNAZIONE'
        when sa.tipo_assegnazione = 'VIRTUALE' and sa.tipo_deassegnazione is null then 'ASSEGNAZIONE_VIRTUALE'
        when sa.tipo_assegnazione is null and sa.tipo_deassegnazione = 'VIRTUALE' then 'DEASSEGNAZIONE_VIRTUALE'
        when sa.tipo_assegnazione = 'VIRTUALE' and sa.tipo_deassegnazione = 'VIRTUALE' then 'RIASSEGNAZIONE_VIRTUALE'
        else null
      end CURRENT_USER_CHANGE_REASON
		from  attivita a
		inner join v_storia_attivita sa on sa.id_attivita = a.id_attivita
    inner join action ac on ac.nome = sa.azione
    inner join stati st on st.nome = sa.stato_risultante
    inner join operatori op1 on op1.login_operatore = sa.login_operatore
    left join operatori op2 on op2.login_operatore = sa.login_operatore_corrente
		left join dati_tecnici_attivita dta on dta.id_attivita = sa.id_attivita and dta.data_esecuzione = sa.data_esecuzione
		left join tipi_dati_tecnici_attivita tdta on tdta.id_tipo_dato_tecnico_attivita = dta.id_tipo_dato_tecnico_attivita
		left join tipi_dati_tecnici_att_action tdtaa
			on tdtaa.id_tipo_dato_tecnico_attivita = dta.id_tipo_dato_tecnico_attivita
				and tdtaa.id_tipo_attivita = a.id_tipo_attivita
				and tdtaa.id_action = ac.id_action
		where a.id_attivita = ?
	)
	select x.transition_date
		,x.description
		--,nvl((select id_stato from stati where nome = sa2.stato_risultante), '0') from_status_id
    ,nvl(sa2.id_stato_risultante, '0') from_status_id
		,x.action_id
		,x.to_status_id
		,x.user_id
		,x.current_user_id
		,x.property_id
		,x.property_name
		,x.property_value
		,x.property_order
		,x.property_validity
		--,(select count('x') from storia_attivita where id_attivita = x.activity_id) transition_count
		--,(select count('x') from dati_tecnici_attivita where id_attivita = x.activity_id) property_count
		, x.CURRENT_USER_CHANGE_REASON
	from  x
	left join storia_attivita sa2
		on sa2.id_attivita = x.activity_id
			and sa2.data_esecuzione = x.prev_transition_date
	where x.transition_date = to_date(?, 'yyyymmddhh24miss')
) y
order by y.property_order asc
	";
	
	return $self->art()->_create_prepare(__PACKAGE__.'_PREPARE', $sql);
}

sub _fetch_records {
	my $self = shift;
	my $art = $self->art();
	my $db = $art->_dbh();
	my $error;
	$self->_prepare_query || return undef;

	my $records;
	unless ($records = $art->_create_prepare(__PACKAGE__.'_PREPARE')->fetchall_hashref($art->get_default_date_format, $self->activity_id(), $self->('ID'))) {
		$art->last_error($db->get_errormessage);
		return undef;
	}

	my $visibility_property = $art->get_activity_property_visibility(ACTIVITY_TYPE_NAME => $art->get_activity_type_name($self->activity_info_activity_type_id()));
	return undef unless defined $visibility_property;

	my ($primo_id, $result);
	
	for my $record (@$records) {
		if ( !$primo_id ) {
			%$result = map { $_ => $record->{$_} } grep { ! /^property/i } keys %$record;
		}
		
		my %props = map { $_ => $record->{$_} } grep { /^property/i } keys %$record;
		if (!$visibility_property->{ACTIVE} || (defined $props{PROPERTY_ID} && $visibility_property->{ACTIVE} && grep {$_->{ID} eq $props{PROPERTY_ID}} @{$visibility_property->{PROPERTIES}})){
			push (@{$result->{PROPERTIES}}, \%props) if defined $props{PROPERTY_ID};
		}
		
		if ($self->('ATTACHMENTS')) {
			$result->{ATTACHMENTS} = $self->('ATTACHMENTS')->list;
			return undef unless defined $result->{ATTACHMENTS};
		}
		$primo_id = 1;
	}
	
	# aggiungo chiave TRANSITION_PROPERTIES
	if (exists $result->{PROPERTIES}){
		$result->{TRANSITION_PROPERTIES} = {};
		for my $p (@{$result->{PROPERTIES}}){
			$result->{TRANSITION_PROPERTIES}->{$p->{PROPERTY_NAME}} = $p->{PROPERTY_VALUE};
		}
	}
	
	$self->('_WORKSPACE', $result);
	return 1;
}

################################################################################
#  M E T O D I   P U B B L I C I
################################################################################

=pod

=head1 METHODS

=head2 I<package>->B<new>( %params )

Il metodo B<new()> E<egrave> il costruttore di classe e accetta i seguenti parametri:

=over 4

=over 4

=item B<ACTIVITY> opzionale (deprecato)

Un'istanza della classe API::ART::Activity

=back

I seguenti parametri sono obbligatori in assenza del parametro B<ACTIVITY>

=over 4

=item B<ART>

Istanza della classe B<API::ART>

=item B<ACTIVITY_ID>

Id dell'attività a cui afferisce il I<package>

=item B<ACTIVITY_INFO_PROPERTIES>

Elenco delle property definite per l'attività a cui afferisce il I<package> (C<$activity-E<gt>info('PROPERTIES')>)

=item B<ACTIVITY_HANDLER>

Istanza della classe B<SIRTI::ART::Events>

=back

=back

=cut

sub new {
	my $this = shift;
	my $class = ref($this) || $this;
	my %self;
	my $errmsg;
	my %params = @_;
	
	croak $errmsg
		unless	$class->check_named_params(
			ERRMSG              => \$errmsg,
			IGNORE_EXTRA_PARAMS => 1,
			PARAMS              => \%params,
			MANDATORY           => {
				ID => { isa => 'SCALAR' },
			}
		);
	
	if (!exists $params{ACTIVITY}){
		croak $errmsg
			unless	$class->check_named_params(
				ERRMSG              => \$errmsg,
				IGNORE_EXTRA_PARAMS => 1,
				PARAMS              => \%params,
				MANDATORY           => {
					ART                      => { isa => undef, inherits => [ 'API::ART' ] },
					ACTIVITY_ID              => { isa => 'SCALAR', pattern => qr{^\d+$} },
					ACTIVITY_INFO_PROPERTIES => { isa => 'ARRAY' },
					ACTIVITY_HANDLER         => { isa => undef, inherits => [ 'SIRTI::ART::Events' ] },
				}
			);
		$self{ART} = $params{ART};
		$self{ACTIVITY_ID} = $params{ACTIVITY_ID};
		$self{ACTIVITY_INFO_PROPERTIES} = $params{ACTIVITY_INFO_PROPERTIES};
		$self{ACTIVITY_HANDLER} = $params{ACTIVITY_HANDLER};
	} else {
		croak $errmsg
			unless	$class->check_named_params(
				ERRMSG              => \$errmsg,
				IGNORE_EXTRA_PARAMS => 1,
				PARAMS              => \%params,
				MANDATORY           => {
					ACTIVITY                 => { isa => undef, inherits => [ 'API::ART::Activity' ] },
				}
			);
		$self{ART} = $params{ACTIVITY}->art();
		$self{ACTIVITY_ID} = $params{ACTIVITY}->id();
		$self{ACTIVITY_INFO_PROPERTIES} = $params{ACTIVITY}->info('PROPERTIES');
		$self{ACTIVITY_HANDLER} = $params{ACTIVITY}->_get_handler(1);
	}
	
	croak "TRANSITION_ID not found!"
		unless defined $self{ART}->_dbh()->fetch_minimalized("
			select 1
			from storia_attivita
			where id_attivita = ".$self{ACTIVITY_ID}."
			and data_esecuzione = to_date(".$self{ART}->_dbh()->quote($params{ID}).",'YYYYMMDDHH24MISS') 
		");

	$self{ACTIVITY_INFO_ACTIVITY_TYPE_ID} = $self{ART}->_dbh()->fetch_minimalized("
		select id_tipo_attivita
		from attivita
		where id_attivita = ".$self{ACTIVITY_ID}."
		and rownum<2
	");
		
	$self{ID} = $params{ID};
	
	$self{_WORKSPACE} = {};
	
	my $closure = sub {
		my $field = shift;
		if (@_) { $self{$field} = shift }
		return $self{$field};
	};
	$closure = bless( $closure, $class );
	$closure->_fetch_records || return undef;
	$self{ATTACHMENTS} = new API::ART::Activity::History::Transition::Attachment(TRANSITION => $closure);
	
	return $closure;
}

=pod

=head2 I<package>->B<art>( )

Restituisce l'istanza <API::ART>

=cut

sub art {
	shift->('ART');
}

=pod

=head2 I<package>->B<activity>( ) (deprecato)

Restituisce l'istanza <API::ART::Activity> a cui afferisce il I<package>

=cut

sub activity {
	my $self = shift;

	return API::ART::Activity::Factory->new(ART => $self->art(), ID => $self->activity_id());
}

=pod

=head2 I<package>->B<activity_id>( )

Restituisce l'id dell'attività a cui afferisce il I<package>

=cut

sub activity_id {
	shift->('ACTIVITY_ID');
}

=pod

=head2 I<package>->B<activity_handler>( )

Restituisce un'istanza <SIRTI::ART::Events>

=cut

sub activity_handler {
	shift->('ACTIVITY_HANDLER');
}

=pod

=head2 I<package>->B<activity_info_properties>( )

Restituisce un'array con le properties dell'attività

=cut

sub activity_info_properties {
	shift->('ACTIVITY_INFO_PROPERTIES');
}

=pod

=head2 I<package>->B<activity_info_activity_type_id>( )

Restituisce l'id_del tipo_attivita

=cut

sub activity_info_activity_type_id {
	shift->('ACTIVITY_INFO_ACTIVITY_TYPE_ID');
}

=pod

=head2 I<package>->I<metodi>( )

Restituisce l'elemento della transizione in base al metodo tra i seguenti:

=over 4

=over 4

=item ->B<id> : id univoco che identifica la transizione (timestamp)

=item ->B<user_id>|B<user_name> : id o nome utente che effettua la transizione

=item ->B<current_user_id>|B<current_user_name> : id o nome utente a cui viene assegnata l'attivitE<agrave>

=item ->B<current_user_change_reason> : motivazione per la quale E<egrave> stata modificata l'assegnazione dell'attivitE<agrave>

=item ->B<action_id>|B<action_name> : id o nome dell'azione

=item ->B<from_status_id>|B<from_status_name> : id o nome dello stato di partenza

=item ->B<to_status_id>|B<to_status_name> : id o nome dello stato di arrivo

=item ->B<transition_date> : data della transizione espressa nel formato C<$art->info('DEFAULT_DATE_FORMAT')>;

=item ->B<description> : descrizione della transizione

=item ->B<properties> : <arrayref> delle proprietE<agrave>

=item ->B<transition_properties> : <hashref> chiave => valore delle proprietE<agrave>

=item ->B<attachments> : <arrayref> degli allegati

=back

=back

=cut


sub id { $_[0]->('_WORKSPACE')->{ID} }
sub user_id { $_[0]->('_WORKSPACE')->{USER_ID} }
sub current_user_id { $_[0]->('_WORKSPACE')->{CURRENT_USER_ID} }
sub action_id { $_[0]->('_WORKSPACE')->{ACTION_ID} }
sub from_status_id { $_[0]->('_WORKSPACE')->{FROM_STATUS_ID} }
sub to_status_id { $_[0]->('_WORKSPACE')->{TO_STATUS_ID} }
sub user_name { $_[0]->art()->get_user_name($_[0]->user_id) }
sub current_user_name { defined $_[0]->current_user_id ? $_[0]->art()->get_user_name($_[0]->current_user_id) : undef  }
sub action_name { $_[0]->art()->get_activity_action_name($_[0]->action_id) }
sub from_status_name { $_[0]->art()->get_activity_status_name($_[0]->from_status_id) }
sub to_status_name { $_[0]->art()->get_activity_status_name($_[0]->to_status_id) }
sub transition_date { $_[0]->('_WORKSPACE')->{TRANSITION_DATE} }
sub description { $_[0]->('_WORKSPACE')->{DESCRIPTION} }
sub properties { $_[0]->('_WORKSPACE')->{PROPERTIES} } #FIXME da pensare ad un API::ART::Activity::History::Transition::Property
sub transition_properties { $_[0]->('_WORKSPACE')->{TRANSITION_PROPERTIES} }
sub attachments { $_[0]->('ATTACHMENTS') }
sub current_user_change_reason { $_[0]->('_WORKSPACE')->{CURRENT_USER_CHANGE_REASON} }

=pod

=head2 I<package>->B<serialize>( [ OUTPUT => <scalar> | RAW => <boolean> ] )

Restituisce l'oggetto perl modificato con i parametri in seguito descritti:

=over 4

=over 4

=item B<OUTPUT> => <scalar>

accetta 'id' o 'name' (default), ed indica se rappresentare i valori delle chiavi (action, from_status, to_status, user e current_user)
con il loro id o il loro name, e ritorna un hashref.

=item B<RAW> => <boolean>

accetta un booleano 0 (default) o 1 per ritornare la struttura hashref interna dell'oggetto API::ART::Activity::History::Transition indicata solo per uso debug.

=back

=back

=cut

sub serialize { #TODO parametro facoltativo formato (perl hash, json, xml, ...TBD) o una callback
	my ($self, %params) = @_;
	my $errmsg;
	my $art = $self->art();
	$art->clear_last_error();

	croak $errmsg
		unless	$self->check_named_params(
			 ERRMSG 				=> \$errmsg
			,IGNORE_EXTRA_PARAMS 	=> 0
			,PARAMS 				=> \%params
			,MANDATORY => {	}
			,OPTIONAL => {
				OUTPUT	=> { isa => 'SCALAR', list => [ 'id', 'name' ] }
				,RAW	=> { isa => 'SCALAR', list => [ 0, 1 ] }
			}
		);
	
	$params{RAW} = 0 unless defined $params{RAW};
	
	return { %{$self->('WORKSPACE')} } if $params{RAW};

	my $sx = $params{OUTPUT} || 'name';
	my $usx = uc $sx;
	my $new_structure = {};
	
	$new_structure = {
		"CURRENT_USER_$usx" => $self->${\"current_user_$sx"}(),
		"USER_$usx" 		=> $self->${\"user_$sx"},
		"ACTION_$usx" 		=> $self->${\"action_$sx"}(),
		"FROM_STATUS_$usx"	=> $self->${\"from_status_$sx"},
		"TO_STATUS_$usx" 	=> $self->${\"to_status_$sx"},
		"DESCRIPTION"		=> $self->description,
		"TRANSITION_DATE"	=> $self->transition_date,
		"CURRENT_USER_CHANGE_REASON"		=> $self->current_user_change_reason()
	};
	
	$new_structure->{"PROPERTIES"} = $self->properties || [];
	$new_structure->{"TRANSITION_PROPERTIES"} = $self->transition_properties || {};
	$new_structure->{"ATTACHMENTS"} = $self->attachments->list;
	return undef unless defined $new_structure->{"ATTACHMENTS"};
	
	return $new_structure;

}

=pod

=head2 I<package>->B<snapshot>( [ TYPE => <scalar>, PROPERTY => <array> ] )

Restituisce sempre un hashref property_name => property_value in base ai parametri opzionali di seguito descritti:

=over 4

=over 4

=item B<TYPE> => <scalar>

accetta 'before' o 'after' (default), ed indica se si vuol sapere il valore delle proprieta' prima o dopo la transizione in esame

=item B<ACTION> => <arrayref>

accetta una lista di proprieta' di cui si vuol sapere il valore

=back

=back

=cut

sub snapshot {
	my $self = shift;
	my %params = @_;
	my $errmsg;

	croak $errmsg
		unless	$self->check_named_params(
			 ERRMSG 				=> \$errmsg
			,IGNORE_EXTRA_PARAMS 	=> 0
			,PARAMS 				=> \%params
			,MANDATORY => {	}
			,OPTIONAL => {
				TYPE		=> { isa => 'SCALAR', list => [ '', 'before', 'after' ] }
				,PROPERTY	=> { isa => 'ARRAY' }
			}
		);

	my $events = $self->activity_handler();

	@{$params{PROPERTY}} = @{$self->activity_info_properties()} unless $params{PROPERTY};
	my @properties = map { { NAME => $_, ID => $self->art()->get_activity_property_id($_)  } } @{$params{PROPERTY}} ;
	
	my $action_date;
	if (defined $params{TYPE} && $params{TYPE} eq 'after'){
		my $db = $self->art()->_dbh();
	
		my $sql = "select to_char((to_date(?, 'yyyymmddhh24miss')+1/86400), 'yyyymmddhh24miss') from dual";
		
		my $prepare = $self->art()->_create_prepare(__PACKAGE__.'_PREPARE_AD', $sql);
		return undef unless defined $prepare;

		unless ($action_date = $self->art()->_create_prepare(__PACKAGE__.'_PREPARE_AD')->fetch_minimalized($self->id)) {
			$self->art()->last_error($db->get_errormessage);
			return undef;
		}

	} else {
		$action_date = $self->id;
	}

	my $results = eval {
		$events->activity_selection(
			ATTIVITA		=>  [ $self->activity_id() ]
			,TYPE			=>  'HASHREF'
			,TDTA           =>  \@properties
			,ACTION_DATE	=>	$action_date
		)
	};
	croak 'errore' if $@;
	$results = shift (@$results);
	
	my %result = map { $_ => $results->{$_} } @{$params{PROPERTY}};
	
	return \%result;
}




1;


__END__

=pod

=head1 NOTES

n/a

=head1 BUGS

Eventuali bug riscontrati dovranno essere segnalati su B<ART - Global Services>: L<https://services.sirti.net/artit/> aprendo un apposito DEV-TICKET.

=head1 HISTORY

=over

=item Ver. 0.01

Prima release del modulo

=back

=head1 AUTHOR

Alvaro Livraghi <<EMAIL>>

=cut

