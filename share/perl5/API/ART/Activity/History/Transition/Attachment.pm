################################################################################
#
# API::ART::Activity::History::Transition::Attachment
#
################################################################################


=head1 NAME

B<API::ART::Activity::History::Transition::Attachment> - Classe per la visualizzazione degli allegati delle transizioni di un'attivitE<agrave>.

=head1 SYNOPSIS

	my $attachments = API::ART::Activity::History::Transition::Attachment->new(TRANSITION => <API::ART::Activity::History::Transition>);
	
	print Dumper $attachments->list;
	
=head1 DESCRIPTION

Questo package mette a disposizione i metodi per ottenere le informazioni sugli allegati della transizione.

=cut

package API::ART::Activity::History::Transition::Attachment;

use strict;
use warnings;
use Carp;
use base qw(API::Ancestor);


################################################################################
#  P R O P R I E T A '   P U B B L I C H E
################################################################################

our $VERSION = '0.01';


################################################################################
#  M E T O D I   P R I V A T I
################################################################################

sub _get_attachments {
	my ($self,%params) = @_;
	my $events = $self->_activity_handler;
	my $path = $params{PATH}||$ENV{TMP}||$ENV{TMPDIR}||'/tmp';
	delete $params{PATH};
	my $files = [];
	my $rc = $events->detach_attachments($self->activity_id(), $self->_transition_id, $files, $path, %params);
	$self->art()->last_error($rc)
		and return undef
			if $rc ne "";

	return $files;
}

sub _delete_attachment {
	my ($self,%params) = @_;
	my $art = $self->art();
	
	return undef unless defined $self->_prepare_query_del;
	
	my $del_attachment = $self->_prepare_query_del->execute($art->user()->id(), $self->activity_id(), $self->_transition_date(), $art->get_default_date_format, $params{ID});

	return 1;
}

sub _prepare_query {
	return shift->art()->_create_prepare(
		__PACKAGE__.'_PREPARE_GET'
		, "
			select aa.ID_ATTIVITA
				, aa.ID_ACTION
				, aa.NOME_FILE_CLIENT
				, aa.NOME_FILE_SERVIZIO
				, aa.DIMENSIONE
				, aa.TITOLO
				, aa.DESCRIZIONE
				, aa.REVISIONE
				, to_char(DATA_RIFERIMENTO, 'YYYY-MM-DD') DATA_RIFERIMENTO
				, tata.id_tipo_documento TIPO_DOCUMENTO
				, aa.id_allegato ID
			from ALLEGATI_AZIONE aa
				left join tipi_attivita_tipi_allegati tata on tata.id = aa.id_tipo_allegato_tipo_doc
			where aa.ID_ATTIVITA = ?
				and aa.id_action = to_date(?, ?)
				and aa.data_cancellazione is null
			order by aa.id_allegato
		"
	);
}

sub _prepare_query_del {
	return shift->art()->_create_prepare(
		__PACKAGE__.'_PREPARE_DEL'
		, "
			update ALLEGATI_AZIONE aa
			set id_operatore_cancellazione = ?
				, data_cancellazione = systimestamp
			where aa.ID_ATTIVITA = ?
				and aa.id_action = to_date(?, ?)
				and aa.id_allegato = ?
		"
	);
}

sub _fetch_records {
	my $self = shift;
	my $art = $self->art();
	
	return undef unless defined $self->_prepare_query;
	
	my $find_attachments = $self->_prepare_query->fetchall_hashref($self->activity_id(), $self->_transition_date(), $art->get_default_date_format);
	
	if (scalar @$find_attachments > 0) {
		$self->('_WORKSPACE', []);
		# NB: lascio questo codice commentato se si vuole fare il check sul numero di allegato attesi
		#my $events = $self->_activity_handler;
		#my $size = $events->detach_attachments($self->activity_id(), $self->_transition_id(), [], '', SIZE_ONLY => 1);
		#$self->art()->last_error($size)
		#	and return undef
		#		if ref ($size) ne 'ARRAY';
		#
		#$self->art()->last_error('Inconsistency found between files and sizes!')
		#	and return undef
		#		if scalar @{$size} != scalar @$find_attachments;
		
		for (my $i=0; $i<scalar @$find_attachments; $i++){
			my $attach={};
			for my $key (keys %{$find_attachments->[$i]}){
				$attach->{$key} = $find_attachments->[$i]->{$key};
			}
			
			#$attach->{SIZE} = (grep { $_->{NOME_FILE_CLIENT} eq $attach->{NOME_FILE_CLIENT} } @{$size})[0]->{SIZE};
			$attach->{SIZE} = $find_attachments->[$i]->{DIMENSIONE};
			$attach->{SIZE} = $attach->{SIZE}*1 if defined $attach->{SIZE};
			
			push @{$self->('_WORKSPACE')}, $attach;
		}
	}
	
	return 1;
}

################################################################################
#  M E T O D I   P U B B L I C I
################################################################################

=pod

=head1 METHODS

=head2 I<package>->B<new>( TRANSITION => I<API::ART::Activity::History::Transition> )

Il metodo B<new()> E<egrave> il costruttore di classe e accetta i seguenti parametri:

=over 4

=over 4

=item B<TRANSITION> obbligatorio

Un'istanza della classe API::ART::Activity::History::Transition

=back

=back

=cut

sub new {
	my $this = shift;
	my $class = ref($this) || $this;
	my %self;
	my $errmsg;
	my %params = @_;
	
	croak $errmsg
		unless	$class->check_named_params(
			 ERRMSG 				=> \$errmsg
			,IGNORE_EXTRA_PARAMS 	=> 0
			,PARAMS 				=> \%params
			,MANDATORY => {
				TRANSITION => { isa => undef, inherits => [ 'API::ART::Activity::History::Transition' ] }
			}
			,OPTIONAL => {
				FILTERS	 => { isa => 'HASH' }
			}
		);
		
	$self{ART} = $params{TRANSITION}->art();
	$self{TRANSITION_ID} = $params{TRANSITION}->id();
	$self{TRANSITION_DATE} = $params{TRANSITION}->transition_date();
	$self{TRANSITION_USER_ID} = $params{TRANSITION}->user_id();
	$self{ACTIVITY_ID} = $params{TRANSITION}->activity_id();
	$self{ACTIVITY_INFO_PROPERTIES}	= $params{TRANSITION}->activity_info_properties();
	$self{ACTIVITY_HANDLER}	= $params{TRANSITION}->activity_handler();

	$self{_WORKSPACE} = [];
	
	my $closure = sub {
		my $field = shift;
		if (@_) { $self{$field} = shift }
		return $self{$field};
	};
	$closure = bless( $closure, $class );
	return $closure;
}

=pod

=head2 I<package>->B<art>( )

Restituisce l'istanza <API::ART>

=cut

sub art { shift->('ART'); }

=pod

=head2 I<package>->B<activity_id>( )

Restituisce l'id dell'attività a cui afferisce il I<package>

=cut

sub activity_id { shift->('ACTIVITY_ID'); }

sub _activity_info_properties { shift->('ACTIVITY_INFO_PROPERTIES'); }

sub _activity_handler { shift->('ACTIVITY_HANDLER'); }

sub _transition_id { shift->('TRANSITION_ID'); }

sub _transition_date { shift->('TRANSITION_DATE'); }

sub _transition_user_id { shift->('TRANSITION_USER_ID'); }

=pod

=head2 I<package>->B<read>( <integer>, <tmpdir> )

Restituisce il <filehandle> aperto del file allegato nella posizione indicata dal parametro <integer>. E<Egrave> necessario chiudere il <filehandle> dopo averlo utilizzato.
Opzionalmente puo' essere passato il parametro <tmpdir>.

=cut

sub read {
	my $self = shift;
	my $ordinal = shift;
	my $path = shift;
	
	if (scalar (@{$self->('_WORKSPACE')}) == 0){
		return undef
			unless defined $self->_fetch_records;
	}

	$self->art()->last_error("Wrong read() parameter! it must be an integer !!")
		&& return undef
			if (!defined $ordinal || $ordinal !~ /^\d+$/);

	my @attachments_found = grep {$_->{ID} eq $ordinal} @{$self->('_WORKSPACE')};
	
	if (scalar @attachments_found) {
		my $att = $attachments_found[0];
		my $name = $att->{NOME_FILE_SERVIZIO};
		my $files = $self->_get_attachments(EMIT_INPUT_FD => 1, FILENAME => $name, PATH => $path);
		return undef
			unless defined $files;
		return $files->[0];
	} else {
		$self->art()->last_error("File not available for ordinal $ordinal");
		return undef;
	}
	
}

=head2 I<package>->B<delete>( <integer> )

Restituisce 1 in caso di rimozione effettuata, undef in caso negativo

=cut

sub delete {
	my $self = shift;
	my $ordinal = shift;
	
	if (scalar (@{$self->('_WORKSPACE')}) == 0){
		return undef
			unless defined $self->_fetch_records;
	}

	$self->art()->last_error("Wrong read() parameter! it must be an integer !!")
		&& return undef
			if (!defined $ordinal || $ordinal !~ /^\d+$/);

	my @attachments_found = grep {$_->{ID} eq $ordinal} @{$self->('_WORKSPACE')};
	
	if (scalar @attachments_found) {
		my $att = $attachments_found[0];
		unless ($self->art()->user()->is_admin()){
			if ($self->_transition_user_id() ne $self->art()->user()->id()){
				$self->art()->last_error("User not authorized to delete attachment");
				return undef;
			}
		}
		my $del = $self->_delete_attachment(ID => $att->{ID});
		return undef
			unless defined $del;
	} else {
		$self->art()->last_error("File not available for ordinal $ordinal");
		return undef;
	}

	return 1;
	
}

=pod

=head2 I<package>->B<list>( )

Restituisce la lista degli allegati

=cut

sub list {
	my $self = shift;
	if (scalar (@{$self->('_WORKSPACE')}) == 0){
		return undef 
			unless defined $self->_fetch_records;
	}
	return scalar (@{$self->('_WORKSPACE')}) > 0 ? [ map { {
		ID => $_->{ID}
		,NOME_FILE_CLIENT => $_->{NOME_FILE_CLIENT}
		,SIZE => $_->{SIZE}
		,FILENAME => $_->{NOME_FILE_CLIENT}  # ridondante per retrocompatibilità tra transizion e activity
		,TITLE => $_->{TITOLO}
		,DESCRIPTION => $_->{DESCRIZIONE}
		,REVISION => $_->{REVISIONE}
		,REF_DATE => $_->{DATA_RIFERIMENTO}
		,DOC_TYPE => $_->{TIPO_DOCUMENTO}
		,TRANSITION_ID => $self->_transition_id()*1
		,TRANSITION_DATE => $self->_transition_date()
		,SEQUENCE => $_->{ID}
		,OWNER_NAME => $self->art()->get_user_name($self->_transition_user_id())
		,OWNER_ID => $self->_transition_user_id()
		,OWNER_FIRST_NAME => $self->art()->get_user_info($self->_transition_user_id())->{FIRST_NAME}
		,OWNER_LAST_NAME => $self->art()->get_user_info($self->_transition_user_id())->{LAST_NAME}
		,DOWNLOAD_COUNT => undef # NYI
	 } } @{$self->('_WORKSPACE')} ] : [];
}


1;


__END__

=pod

=head1 NOTES

n/a

=head1 BUGS

Eventuali bug riscontrati dovranno essere segnalati su B<ART - Global Services>: L<https://services.sirti.net/artit/> aprendo un apposito DEV-TICKET.

=head1 HISTORY

=over

=item Ver. 0.01

Prima release del modulo

=back

=head1 AUTHOR

Alvaro Livraghi <<EMAIL>>

=cut
