package API::ART::Activity::MQ::Consumer::NotificationService;

use strict;
use warnings;
use Carp;
use Data::Dumper;
use JSON;
use File::Temp qw/ tempdir /;

use API::ART::Activity::Factory;
use SIRTI::ART::RemoteActivity::Target;

use base 'SIRTI::Queue::EventConsumer';

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_event_types
{
	return ['SEND_NOTIFICATION'];
}

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_source_refs
{
	return [];
}

sub init
{
	my $self = shift;
	my $logger = $self->logger();
	my $art = $self->art();
	
	$self->{RA_QUEUE} = eval {
	
		$logger->debug("Creazione RA TARGET session per verifica pending");
		
		SIRTI::ART::RemoteActivity::Target->new(
			DB => $art->_dbh(),
			SOURCE_CONTEXT => $self->work_context('QUEUE_SOURCE_CONTEXT'),
			TARGET_CONTEXT => $self->work_context('QUEUE_TARGET_CONTEXT'),
			SESSION_DESCRIPTION => 'Inizializzazione coda per verifica pending'
		);
	};
	
	if ( $@ || !defined( $self->{RA_QUEUE} ) ) {
		my $msg = "Non posso inizializzare le Remote Activity TARGET: $@";
		$logger->fatal( "Non posso inizializzare le Remote Activity TARGET: $@" );
		croak $msg;
	} 
}

#
# override
#
# params
# EVENT : oggetto di tipo SIRTI::Queue::Event
#
# return:
# oggetto di tipo SIRTI::Queue::Response o undef (e set last_error()) in caso di problemi fatali
#
sub consume_event()
{
	my $self	= shift;
	my %params	= @_;
	my $event	= $params{EVENT};
	my $ra_istance = $self->{RA_QUEUE} ;
	my $art		= $self->art();
	my $errmsg;
	
	$self->logger()->info( "Elaboro un evento..." );
	$self->logger()->info( "EVENT NAME: " . $event->get_event_name() );
	$self->logger()->info( "SOURCE_REF: " . $event->get_source_ref() );
	
	for ('ACTIVITY_ID', 'LAST_VAR_DATE', 'DATE_FORMAT'){
		unless (defined $event->get_data()->{$_}){
			my $msg = "Parametro ".$_." obbligatorio mancante!";
			$self->logger()->error( $msg );
			return $self->consume( STATUS => 2, TARGET_REF => 'KO', REASON => $msg);
		}
	}
	
	my $id_attivita = $event->get_data()->{ACTIVITY_ID};
	
	my $activity = API::ART::Activity::Factory->new(ART => $art, ID => $id_attivita);
	
	if ($art->last_error()) {
		$self->logger()->error( "Si e' verificato un errore istanziando l'oggetto attivita': ".$art->last_error() );
		return $self->consume(
			REASON     => "ID attivita $id_attivita non trovato!",
			STATUS     => '1',
			TARGET_REF => $id_attivita
		);
	}

	$self->logger()->info("Agganciata l'attivita' con id $id_attivita");
	
	# se presente la chiave recipients significa che è una customizzazione rispetto al comportamento standard e quindi
	# devono essere inviate tutte le notifiche
	unless (defined $event->get_data()->{RECIPIENTS}){
		for ('CONTACT_GROUP_ID'){
			unless (defined $event->get_data()->{$_}){
				my $msg = "Parametro ".$_." obbligatorio mancante!";
				$self->logger()->error( $msg );
				return $self->consume( STATUS => 3, TARGET_REF => 'KO', REASON => $msg);
			}
		}
		# verifico se ci sono altre RA pending sullo stesso SOURCE_REF
		my @id = $ra_istance->find_pending( 
				EVENT		=> $self->get_managed_event_types,
				SOURCE_REF	=> $event->get_source_ref()
			);
		
		# lo scalar non può mai essere minore di 1 in quanto c'è sempre almeno l'id dell'evento che si sta gestendo
		if (scalar @id > 1){
			# se ne trovo più di uno significa che esiste un evento di notifica successivo non ancora gestito
			# per il quale devo inviare la notifica e quindi segno come gia' lavorato con nulla di fatto quello corrente
			my $msg = "Evento da non inoltrare in quanto presente notifica successiva";
			$self->logger()->info( $msg );
			return $self->consume(
				REASON     => $msg,
				STATUS     => '0',
				TARGET_REF => $id_attivita
			);
		}
	}
	
	# recupero il formato della data per reimpostarlo in coda
	my $ddf = $art->get_default_date_format();
	
	unless ($art->set_default_date_format($event->get_data()->{DATE_FORMAT})) {
		my $msg = "Si e' verificato un errore nell'impostazione del default_date_format : ".$art->last_error();
		$self->logger()->error( $msg );
		return $self->consume(
			REASON     => $msg,
			STATUS     => '2',
			TARGET_REF => $id_attivita
		);
	}
	
	my $sql_date_convert = "
		select to_char(to_date(?,?),?) From dual
	";
	
	$art->_create_prepare(__PACKAGE__.'_date_format', $sql_date_convert);
	
	my $end_date = $art->_create_prepare(__PACKAGE__.'_date_format')->fetch_minimalized($event->get_data()->{LAST_VAR_DATE}, $event->get_data()->{DATE_FORMAT}, $art->get_default_date_format());
	
	# recupero la history fino alla data in cui è stata richiesta la notifica in modo poi da recuperare l'ultimo step
	# e inviare la mail con quelle info
	my $history = $activity->history(FILTERS => {
		END_DATE => $end_date
	});
	
	unless ($history) {
		my $msg = "Si e' verificato un errore istanziando la history : ".$art->last_error();
		$self->logger()->error( $msg );
		return $self->consume(
			REASON     => $msg,
			STATUS     => '3',
			TARGET_REF => $id_attivita
		);
	}
	
	my $last = $history->last();
	
	my $contacts = [];
	my $map_custom_recipients = {
		RECIPIENTS		=> 'TO',
		RECIPIENTS_CC	=> 'CC',
	};
	
	# recupero i contatti nelle due gestioni
	# per i recipients custom (sia TO che CC) viene inviata un'unica mail con tutti i destinatari
	if (defined $event->get_data()->{RECIPIENTS} || defined $event->get_data()->{RECIPIENTS_CC}){
		my $contact_in = {};
		for my $k (keys %$map_custom_recipients){
			if (defined $event->get_data()->{$k}){
				if (ref ($event->get_data()->{$k}) eq 'ARRAY'){
					$contact_in->{$map_custom_recipients->{$k}} = join (',', @{$event->get_data()->{$k}});
				} else {
					$contact_in->{$map_custom_recipients->{$k}} = $event->get_data()->{$k};
				}
			}
		}
		$contacts = [$contact_in];
	} else {
		# recupero le eventuali mail da inviare
		my $num_groups = scalar @{$activity->system()->info('GROUPS')};

		my $query_contatti =  "
			select gc.label
				, gc.email \"TO\"
				, gc.cc
				, gc.bcc
				, tasc.message
				, tasc.send_only_message
			from TIPI_ATTIVITA_STATI_CONTATTI tasc
				join gruppi_contatti gc on gc.id = tasc.id_gruppo_contatto
				join gruppi g on g.id_gruppo = gc.id_gruppo
			where tasc.id_tipo_attivita = ?
				and tasc.id_stato = ?
				and gc.id = ?
				and gc.abilitato = 'Y'
				and gc.id_gruppo in (".join(',', map {'?'} @{$activity->system()->info('GROUPS')}).")
		";
		
		$art->_create_prepare(__PACKAGE__.'_cont_'.$num_groups, $query_contatti);
		
		my @bind_values = (
			$activity->info('ACTIVITY_TYPE_ID'),
			$last->to_status_id(),
			$event->get_data()->{CONTACT_GROUP_ID}
		);
		
		push (@bind_values, map {$art->get_group_id($_)} @{$activity->system()->info('GROUPS')});

		$contacts = $art->_create_prepare(__PACKAGE__.'_cont_'.$num_groups)->fetchall_hashref(@bind_values);
	}

	my $actDump;
	
	for my $contact (@{$contacts}){
		
		my @bind_values_sql_date_email = (
			$event->get_data()->{LAST_VAR_DATE},
			$art->get_default_date_format(),
			'YYYYMMDDHH24MISS'
		);
		
		my $date = $art->_create_prepare(__PACKAGE__.'_date_format')->fetch_minimalized(@bind_values_sql_date_email);
		
		# limito la lunghezza del subject per speficihe RFC
		my $subject = ($self->work_context('ART_APPLICATION_NAME')||'ART'). ' - '.$activity->cached_info('ACTIVITY_TYPE_NAME').' - '.$activity->id().' - '.$activity->cached_info('DESCRIPTION');
		if (length $subject > 60){
			$subject = substr($subject, 0,60).'...';
		}
		
		my $p_send_email = {
			SUBJECT => $subject
			,REF =>  $activity->id().' - GRUPPI_CONTATTI - '.$date
		};

		for my $dest ('TO', 'CC', 'BCC'){
			next unless defined $contact->{$dest};
			my @email_list = split (',', $contact->{$dest});
			my @email_list_def;

			MAIL: for my $em (@email_list){
				if ($em eq '["info"]["creationUserObj"]["email"]'){
					$actDump = $activity->dump()
						unless defined $actDump;
					push @email_list_def, $actDump->{info}->{creationUserObj}->{email}
						if (defined $actDump->{info}->{creationUserObj}->{email});
					next MAIL;
				}
				
				my $map = {
					currentUserObj => $last->current_user_id(),
					ownerObj => $last->user_id()
				};

				for (keys %$map){
					if ($em eq '["info"]["'.$_.'"]["email"]'){
						if (defined $map->{$_}){
							my $uo = $art->get_user_structure($map->{$_});
							push @email_list_def, $uo->{email}
								if (defined $uo->{email});
						}
						next MAIL;
					}
				}
				push @email_list_def, $em
			}
			$p_send_email->{$dest} = \@email_list_def
		}
	
		$p_send_email->{SENDER} = $self->work_context('SENDER') if defined $self->work_context('SENDER');
		
		# recupero le info dell'utente
		my $query_utente = "
			select nome_operatore
				, cognome_operatore
			from operatori
			where id_operatore =  ?
		";
		
		$art->_create_prepare(__PACKAGE__.'_utente', $query_utente);
		
		my ($nome, $cognome) = $art->_create_prepare(__PACKAGE__.'_utente')->fetch_minimalized($last->user_id());
		
		my $creation_user = $art->get_user_info($activity->cached_info('CREATION_USER_ID'));
		my $date_format = $self->work_context('DATE_FORMAT');
	
		my @bind_values_sql_date_convert_date = (
			$activity->cached_info('CREATION_DATE'),
			$art->get_default_date_format(),
			$date_format
		);
		
		my $creation_date = $art->_create_prepare(__PACKAGE__.'_date_format')->fetch_minimalized(@bind_values_sql_date_convert_date);
		
		@bind_values_sql_date_convert_date = (
			$activity->info('LAST_VAR_DATE'),
			$art->get_default_date_format(),
			$date_format
		);
		
		my $last_var_date = $art->_create_prepare(__PACKAGE__.'_date_format')->fetch_minimalized(@bind_values_sql_date_convert_date);
		
		my $step_description = $last->description();
		# sostituisco i ritorni a capo con uno spazio
		$step_description =~ s/\r\n/ /g;
		
		if (defined $contact->{SEND_ONLY_MESSAGE} && $contact->{SEND_ONLY_MESSAGE} eq 'Y'){
			$p_send_email->{BODY} = $contact->{MESSAGE};
		} else {
			if (defined $contact->{MESSAGE}){
				$p_send_email->{BODY} =$contact->{MESSAGE}."\n";
			}

			$p_send_email->{BODY}.= defined $event->get_data()->{CUSTOM_MESSAGE_BEFORE} ? $event->get_data()->{CUSTOM_MESSAGE_BEFORE} : '';
			
			$p_send_email->{BODY}.= "
On ".$last_var_date." user ".$nome." ".$cognome." updated activity ".$activity->id()." of type ".$activity->cached_info('ACTIVITY_TYPE_NAME')."

".$activity->cached_info('DESCRIPTION')."\n".
			(
				defined $self->work_context('RESTART_HOMEPAGE')
					? "\nClick the following link for details: ".$self->work_context('RESTART_HOMEPAGE').'?action=view&activityId='.$activity->id()."\n"
					: ''
			)."
SUMMARY

\tActivity Id: ".$activity->id()."
\tCreation Date: ".$creation_date."
\tCreation User: ".$creation_user->{LAST_NAME}.", ".$creation_user->{FIRST_NAME}."
\tStatus: ".$last->to_status_name()."
\tLast Variation Date: ".$last_var_date."
\tTransition Annotation: ".$step_description;
		
			my $check_date = $art->_create_prepare(__PACKAGE__.'_date_format')->fetch_minimalized($event->get_data()->{LAST_VAR_DATE}, $event->get_data()->{DATE_FORMAT}, $date_format);
			
			my $snapshot;
			if ($check_date eq $last_var_date){
				$snapshot = $activity->activity_property();
			} else {
				$snapshot = $last->snapshot(TYPE => 'after');
			}
			
			if ($activity->activity_property()){
				
				my $enum_activity_properties = $art->enum_activity_property(
					ACTIVITY_TYPE_NAME => $activity->cached_info('ACTIVITY_TYPE_NAME'),
					SHOW_ONLY_WITH_VISIBILITY => 1,
					EXTENDED_OUTPUT => 1
				);
				
				$p_send_email->{BODY} .="\n\nDETAILS\n";
				
				my $sql_dta_convert_isodate = "
					select to_char(to_timestamp_tz(?,?),?) From dual
				";
			
				$art->_create_prepare(__PACKAGE__.'_property_tz', $sql_dta_convert_isodate);
				
				# recupero gli eventuali gruppi delle activity_property
				my $act_groups = $art->get_activity_properties_group(
					ACTIVITY_TYPE_NAME => [$activity->cached_info('ACTIVITY_TYPE_NAME')],
					, SHOW_ONLY_WITH_VISIBILITY => 1
				);
				
				for my $act_group (@{$act_groups}){
					
					next unless scalar @{$act_group->{PROPERTIES}};
					
					my $act_prop_group_presenti = 0;
					
					for my $prop (@{$act_group->{PROPERTIES}}){
						my $key = $prop->{NAME};
						my $tipo_ui = $art->_lookup()->tipo_ui_tdta_nome($key);
						if ($tipo_ui =~/^(TAGS|MOOKUP)$/){
							my $array = eval{ from_json($snapshot->{$key})};
							next if !defined $array || scalar (@{$array}) == 0;
						} else {
							next unless defined $snapshot->{$key};
							if ($tipo_ui =~/^(ISODAT)$/){
								my @bind_values_sql_dta_convert_isodate = (
									$snapshot->{$key},
									$art->get_default_iso_date_format_oracle(),
									$self->work_context('TIMESTAMP_TZ_FORMAT')
								);
								
								$snapshot->{$key} = $art->_create_prepare(__PACKAGE__.'_property_tz')->fetch_minimalized(@bind_values_sql_dta_convert_isodate);
							} elsif ($tipo_ui =~/^(CURRENCY)$/){
								$snapshot->{$key} .= ' '.$art->_lookup()->valuta_tdta_nome($key);
							}
						}
						
						# scrivo il nome del raggruppamento se presente almeno un AP
						unless ($act_prop_group_presenti){
							$p_send_email->{BODY}.="\n\t--- ".(defined $act_group->{GROUP} ? $act_group->{GROUP} : 'OTHER')." ---";
							$act_prop_group_presenti = 1;
						}
						# sostituisco i ritorni a capo con uno spazio
						$snapshot->{$key} =~ s/\r\n/ /g;
						$p_send_email->{BODY}.="\n\t".$enum_activity_properties->{$key}->{LABEL}.": ".$snapshot->{$key};
					}
					$p_send_email->{BODY}.="\n" if $act_prop_group_presenti;
				}
			}
			
			$p_send_email->{BODY}.="\nThis is an automatically generated email, please do not reply";
			
			$p_send_email->{BODY}.= defined $event->get_data()->{CUSTOM_MESSAGE_AFTER} ? $event->get_data()->{CUSTOM_MESSAGE_AFTER} : '';
		}
		
		# gestisco eventuali allegati
		if (defined $event->get_data()->{ATTACHMENTS}){
			# ricostruisco l'oggetto dalla stringa json
			my $attachments_list = eval{from_json($event->get_data()->{ATTACHMENTS})};
			if ($@){
				my $msg = "Si e' verificato un errore nella deserializzazione del json ATTACHMENTS: ".$@;
				$self->logger()->error( $msg );
				return $self->consume(
					REASON     => $msg,
					STATUS     => '4',
					TARGET_REF => $id_attivita
				);
			}
			if (scalar @{$attachments_list}){
				# creare cartella temporanea
				my $tempdir = eval{tempdir ( DIR => $ENV{TMPDIR} )};
				if ($@){
					my $msg = "Si e' verificato un errore nella creazione della directory temporanea per la gestione degli allegati: ".$@;
					$self->logger()->error( $msg );
					return $self->consume(
						REASON     => $msg,
						STATUS     => '4',
						TARGET_REF => $id_attivita
					);
				}
				$p_send_email->{ATTACHMENTS_DIRECTORY} = $tempdir;
				my $allegati_globali;
				# il codice è scritto in questo modo per ottimizzare il recupero del numero più basso possibili di allegati
				for my $attach (@{$attachments_list}){
					if ($attach->{TRANSITION_ID}){
						my $transition = eval{
								API::ART::Activity::History::Transition->new(
								ID => $attach->{TRANSITION_ID},
								ACTIVITY => $activity
								)
						};
						if ($@){
							my $msg = "Impossibile recuperare la transizione ".$attach->{TRANSITION_ID}.": ".$@;
							$self->logger()->warn( $msg );
							next;
						}

						my $allegati = $transition->attachments();
						unless (defined $allegati){
							my $msg = "Impossibile recuperare gli allegati per la transizione ".$attach->{TRANSITION_ID}.": ".$@;
							$self->logger()->error( $msg );
							return $self->consume(
								REASON     => $msg,
								STATUS     => '6',
								TARGET_REF => $id_attivita
							);
						}
						my $lista = $allegati->list();
						for (my $i=0; $i<scalar @{$lista}; $i++){
							# in presenza della SEQUENCE_ID gestisco solo quelli corretti
							next
								if defined $attach->{SEQUENCE_ID} && $attach->{SEQUENCE_ID} != $i;
							# in presenza della TYPE gestisco solo quelli corretti
							next
								if defined $attach->{TYPE} && $attach->{TYPE} ne $lista->[$i]->{DOC_TYPE};
							my $fh = $allegati->read($i);
							unless (defined $fh){
								$self->logger()->warn( $art->last_error() );
								next;
							}
							#print STDERR "tempdir = $tempdir\n";
							my $nome_file = $tempdir.'/'.$lista->[$i]->{FILENAME};
							open(my $fhw, '>', $nome_file) or die $!;
							while (<$fh>){
								print $fhw $_;
							}
							close($fhw);
							close($fh);
						}

					} elsif ($attach->{TYPE}){
						$allegati_globali = $activity->attachment_list()
							unless defined $allegati_globali;
						for my $att (@{$allegati_globali}) {
							next
								if (
									defined $att->{DOC_TYPE}
									&&
									$att->{DOC_TYPE} ne $attach->{TYPE}
								);
							my $fh = $activity->get_attachment( TRANSITION_ID => $att->{TRANSITION_ID} , SEQUENCE => $att->{SEQUENCE});
							unless (defined $fh){
								$self->logger()->warn( $art->last_error() );
								next;
							}
							my $nome_file = $tempdir.'/'.$att->{FILENAME};
							open(my $fhw, '>', $nome_file) or die $!;
							while (<$fh>){
								print $fhw $_;
							}
							close($fhw);
							close($fh);
						}
					}
				}
			}
		}


		$self->logger()->debug(Dumper $p_send_email);
		
		unless ($art->send_email(%$p_send_email)){
			my $msg = "Si e' verificato un errore nell'invio della richiesta di notifica : ".$art->last_error();
			$self->logger()->error( $msg );
			return $self->consume(
				REASON     => $msg,
				STATUS     => '3',
				TARGET_REF => $id_attivita
			);
		};
	}
	
	unless ($art->set_default_date_format($ddf)) {
		my $msg = "Si e' verificato un errore nell'impostazione del default_date_format : ".$art->last_error();
		$self->logger()->error( $msg );
		return $self->consume(
			REASON     => $msg,
			STATUS     => '2',
			TARGET_REF => $id_attivita
		);
	}
	
	return $self->consume( STATUS => 0, TARGET_REF => 'OK', REASON => ("Lavorato"));
}

sub finish
{

}
1;
