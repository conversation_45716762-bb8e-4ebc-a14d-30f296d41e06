package API::ART::Collection::Activity;

use strict;
use warnings;
use Carp;
use Time::HiRes qw(gettimeofday);

use SIRTI::ART::Events;
use API::ART::Activity::Binding::Status;
use API::ART::Activity::Aging;
use API::ART::Collection::System;

use base qw(API::ART::Collection::Base);

our $VERSION = '0.01';


# Classe degli oggetti da generare col metodo ->create()
our $DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity';

=head1 NAME

B<API::ART::Collection::Activity> - Gestione collezione attivita'

=head1 SYNOPSIS

	# Uses needed packages
	use API::ART;
	use API::ART::Collection::Activity;

	# Create API::ART instance...
	my $art = API::ART->new(.......);
	# Create API::ART::Collection::Activity instance...
	my $c = API::ART::Collection::Activity->new(ART => $art);

	# Ritorna un ARRAYREF con gli ID di tutte le attivita' definite in ART
	# (OVVIAMENTE DEPRECATO!!!!!!)
	$c->find_id();
	 
	# Ritorna un ARRAYREF di oggetti API::ART::Activity di tutte le attivita' definite in ART
	# FORTEMENTE ED OVVIAMENTE DEPRECATO!!!!!!
	$c->find_object();
	 
	# Ritorna un ARRAYREF con l'ID delle attivita' denominate 'PLUTO'
	$c->find_id( DESCRIPTION_EQUAL => 'PLUTO' );
	 
	# Ritorna un ARRAYREF di oggetti API::ART::Activity di
	# tutte le attivita' il cui nome inizia con la parola 'PIPP' ed e'
	# seguito da un qualsiasi carattere arbitrario
	$c->find_object( DESCRIPTION_LIKE => 'PIPP_' );
	 
	# Ritorna un ARRAYREF con gli ID di tutte le attivita' appartenenti al sistema 'disney' e 'warner_bros'
	$c->find_id( ACTIVITY_TYPE_NAME => [ 'disney' , 'warner_bros' ], SORT => [ {ACTIVITY_TYPE_NAME=> 1}, {ACTIVITY_PROPERTY => {SUPERVISOR: 1}}] );

	# Ritorna un ARRAYREF di oggetti API::ART::Activity di
	# tutte le attivita' NON in uno stato finale nome inizia con 'PIPPO'
	$c->find_object( DESCRIPTION_LIKE => 'PIPPO%' , ACTIVE => 1 );
	
	# Esegue il blocco di codice CALLBACK per ogni attivita' di tipo 'disney' che si
	# trovano negli stati 'AFFAMATO' o 'ASSONNATO', che hanno le proprieta'
	# 'SESSO' e 'ETA' impostate rispettivamente a 'M' e '15'
	# e la cui data di ultima variazione sia precedente o uguale al 03-11-2011
	$c->find_exec(
		ACTIVITY_TYPE_NAME => [
			'disney'
		],
		STATUS_IN => [
			'AFFAMATO',
			'ASSONNATO'
		],
		ACTIVITY_PROPERTIES_EQUAL => {
			'SESSO' => 'M',
			'ETA' => '15'
		},
		LAST_MODIFY_LE => {
			'DATE_VALUE' => '03-11-2011',
			'DATE_FORMAT' => 'DD-MM-YYYY'
		},
		PARENT => [
			{
				UP_LEVEL => 1
				,ACTIVITY_TYPE_NAME => ['EPEL_IC']
				,ID_IN => [6038]
				,ACTIVITY_PROPERTIES_EQUAL => {
					SUPERVISOR => '<EMAIL>'
				}
			}
		],
		CALLBACK => sub () {
			my $s = shift; # Riferimento ad un oggetto API::ART::System
			# azioni da compiere
			return $rc; # Serve a popolare l'arrayref ritornato da find_exec()
		}
	);
	
	
=head1 DESCRIPTION

Questo package consente di lavorare con insiemi di attivita' di ART attraverso un'interfaccia OO.

=head1 METHODS

Di seguito i metodi esposti dalla classe API::ART::Activity.

=cut

#
# Metodi privati
#

sub find_dashboards_summary {
	my $self = shift;
	my %params = @_;
	
	$params{__DASHBOARDS_SUMMARY__} = 1;
	my $ids = $self->_find_id(%params) || return undef;
	
	my $set;
	
	for my $r (@{$ids}){
		$set->{$r->[1]}++;
	}
	
	my $ret = [];
	for my $k (keys %{$set}){
		push @{$ret}, {
			"context" => $k,
			"label" => $k, # FIXME: da fixare con descrizione stato
			"count" => $set->{$k},
		};
	}
	
	my @sorted =  sort { $a->{context} cmp $b->{context} } @{$ret};
	
	return \@sorted;
}

# Esegue la ricerca di attivita e ritorna un ARRAYREF di ACTIVITY_ID
sub _find_id {
	my $self = shift;
	my %params = @_;
	#
	# @ACTIVITY_TYPE_ID || @ACTIVITY_TYPE_NAME
	# @SYSTEM_ID
	# @PARENT
	# @ID_IN
	# $ID_EQUAL
	# $ID_LIKE
	# $ACTIVE => [0] || 1 || -1 (ALL || ACTIVE || NOT ACTIVE )
	# $UPDATE_AGING => [0] || 1 || -1 (ALL || UPDATE_AGING_ENABLED || UPDATE_AGING_DISABLED )
	# TODO $SUPENDED => [0] || 1
	# $EQUAL || $LIKE
	# @DESCRIPTION_IN
	# $DESCRIPTION_EQUAL
	# $DESCRIPTION_LIKE
	# @ACTIVITY_TYPE_NAME_IN
	# $ACTIVITY_TYPE_NAME_EQUAL
	# $ACTIVITY_TYPE_NAME_LIKE
	# @SYSTEM_TYPE_NAME_IN
	# $SYSTEM_TYPE_NAME_EQUAL
	# $SYSTEM_TYPE_NAME_LIKE
	# @SYSTEM_DESCRIPTION_IN
	# $SYSTEM_DESCRIPTION_EQUAL
	# $SYSTEM_DESCRIPTION_LIKE
	# @STATUS
	# @STATUS_IN
	# $STATUS_EQUAL
	# $STATUS_LIKE
	# %PROPERTIES
	# %ACTIVITY_PROPERTIES_IN
	# %ACTIVITY_PROPERTIES_EQUAL
	# %ACTIVITY_PROPERTIES_NOT_EQUAL
	# %ACTIVITY_PROPERTIES_LIKE
	# %ACTIVITY_PROPERTIES_IS_NULL
	# %ACTIVITY_PROPERTIES_IS_NOT_NULL
	# %SYSTEM_PROPERTIES_IN
	# %SYSTEM_PROPERTIES_EQUAL
	# %SYSTEM_PROPERTIES_NOT_EQUAL
	# %SYSTEM_PROPERTIES_LIKE
	# %SYSTEM_PROPERTIES_IS_NULL
	# %SYSTEM_PROPERTIES_IS_NOT_NULL
	# @LAST_MODIFY_LE
	# $CASE_SENSITIVE => 0 || [1]
	# @SORT
	# $EXTENDED_OUTPUT
	# $SHOW_ONLY_WITH_VISIBILITY
	# $SHOW_NO_SUDO
	# $ASSIGNED_TO_ME
	# $ONLY_COUNT
	# $CREATED_BY_ME
	# $LAST_VAR_DATE_LT
	# $LAST_VAR_DATE_LE
	# $LAST_VAR_DATE_EQUAL
	# $LAST_VAR_DATE_GE
	# $LAST_VAR_DATE_GT
	# $CREATION_DATE_LT
	# $CREATION_DATE_LE
	# $CREATION_DATE_EQUAL
	# $CREATION_DATE_GE
	# $CREATION_DATE_GT
	# $DATE_FORMAT
	# @ASSIGNED_TO
	# @CREATED_BY
	#
	
	$self->art()->clear_last_error();
	
	$params{PARENT} = [] if not defined $params{PARENT};
	
	my $date_format = defined $params{DATE_FORMAT} ? $params{DATE_FORMAT} : $self->art()->get_default_date_format();
	
	for my $checks (\%params, @{$params{PARENT}}){
		# Controllo parametri
		for (
			['ACTIVITY_TYPE_NAME', 'ACTIVITY_TYPE_ID']
			, ['STATUS', 'STATUS_IN'] 
			, ['LIKE', 'EQUAL']
			, ['EQUAL', 'DESCRIPTION_EQUAL'] 
			, ['LIKE', 'DESCRIPTION_LIKE']  
		) {
			$self->art()->last_error("Expecting only one between ".$_->[0]." and ".$_->[1]." parameters!")
				&& (return undef)
					if defined $checks->{$_->[0]} && defined $checks->{$_->[1]};
		}
		
		for (
			'ACTIVITY_TYPE_NAME'
			,'ACTIVITY_TYPE_ID'
			,'ACTIVITY_TYPE_NAME_IN'
			,'ID_IN'
			,'DESCRIPTION_IN'
			,'SYSTEM_TYPE_NAME_IN'
			,'SYSTEM_DESCRIPTION_IN'
			,'STATUS'
			,'STATUS_IN'
			,'SYSTEM_ID'
			,'ASSIGNED_TO'
			,'CREATED_BY'
		) {
			$self->art()->last_error($_." must be an ARRAYREF (of SCALAR)!")
				&& (return undef)
					unless !defined($checks->{$_}) || ref $checks->{$_} eq 'ARRAY';
		}
		
		for (
			'PARENT'
		) {
			$self->art()->last_error($_." must be an ARRAYREF (of HASH)!")
				&& (return undef)
					unless !defined($checks->{$_}) || ref $checks->{$_} eq 'ARRAY';
		}
		
		for (
			'ID_EQUAL'
			,'ID_LIKE'
			,'DESCRIPTION_EQUAL'
			,'DESCRIPTION_LIKE'
			,'STATUS_EQUAL'
			,'STATUS_LIKE'
			,'ACTIVITY_TYPE_NAME_EQUAL'
			,'ACTIVITY_TYPE_NAME_LIKE'
			,'SYSTEM_TYPE_NAME_EQUAL'
			,'SYSTEM_TYPE_NAME_LIKE'
			,'SYSTEM_DESCRIPTION_EQUAL'
			,'SYSTEM_DESCRIPTION_LIKE'
		){
			$self->art()->last_error("Param ".$_." must be a STRING!")
				&& (return undef)
					if ref ($checks->{$_});
		}
		
		for (
			'ID_EQUAL'
		){
			$self->art()->last_error("Param ".$_." must be a positive integer")
				&& (return undef)
					if defined $checks->{$_} && $checks->{$_} !~/^\d+$/;
		}
		
		for (
			'ID_LIKE'
		){
			if (defined $checks->{$_}){
				my $check_ripulito = $checks->{$_};
				$check_ripulito =~s/%//g;
				$self->art()->last_error("Param ".$_." must be a positive integer")
					&& (return undef)
						if $check_ripulito !~/^\d+$/;
			}
		}
				
		if (defined $checks->{ID_IN}){
			for my $id_in (@{$checks->{ID_IN}}){
				$self->art()->last_error("Param ID_IN must contains positive integers")
					&& (return undef)
						if $id_in !~/^\d+$/;
			}
		}
		
		for (
			'PROPERTIES'
			, 'ACTIVITY_PROPERTIES_EQUAL'
			, 'ACTIVITY_PROPERTIES_NOT_EQUAL'
			, 'ACTIVITY_PROPERTIES_IN' 
			, 'ACTIVITY_PROPERTIES_LIKE'
			, 'ACTIVITY_PROPERTIES_IS_NULL'
			, 'ACTIVITY_PROPERTIES_IS_NOT_NULL'
			, 'SYSTEM_PROPERTIES_EQUAL'
			, 'SYSTEM_PROPERTIES_NOT_EQUAL'
			, 'SYSTEM_PROPERTIES_IN' 
			, 'SYSTEM_PROPERTIES_LIKE'
			, 'SYSTEM_PROPERTIES_IS_NULL'
			, 'SYSTEM_PROPERTIES_IS_NOT_NULL'
			, 'LAST_MODIFY_LE'
		) {
			$self->art()->last_error($_." must be an HASHREF!")
				&& (return undef)
					unless !defined($checks->{$_}) || ref $checks->{$_} eq 'HASH';
		}
	
		
		$self->art()->last_error("ACTIVE must be 0, 1 o -1!")
			&& (return undef)
				if defined $checks->{ACTIVE} && ! grep /^$checks->{ACTIVE}$/, qw(0 1 -1);
		
		$self->art()->last_error("UPDATE_AGING must be 0, 1 o -1!")
			&& (return undef)
				if defined $checks->{UPDATE_AGING} && ! grep /^$checks->{UPDATE_AGING}$/, qw(0 1 -1);
		
		for my $check_key (
			'CASE_SENSITIVE'
			, 'EXTENDED_OUTPUT'
			, 'SHOW_ONLY_WITH_VISIBILITY'
			, 'SHOW_NO_SUDO'
			, 'ASSIGNED_TO_ME'
			, 'CREATED_BY_ME'
			, 'ONLY_COUNT'
		) {
			$self->art()->last_error($check_key." must be 0 or 1!")
				&& (return undef)
					if defined $checks->{$check_key} && ! grep /^$checks->{$check_key}$/, qw(0 1);
		}

		$self->art()->last_error("Param SHOW_ONLY_WITH_VISIBILITY and SHOW_NO_SUDO are mutally exclusive")
			&& (return undef)
				if ($checks->{SHOW_ONLY_WITH_VISIBILITY} && $checks->{SHOW_NO_SUDO});
		
		$self->art()->last_error("ACTIVITY_TYPE_NAME|ACTIVITY_TYPE_ID not available with ACTIVITY_TYPE_NAME_IN|ACTIVITY_TYPE_NAME_EQUAL|ACTIVITY_TYPE_NAME_LIKE")
			&& (return undef)
				if (
					(
						defined $checks->{ACTIVITY_TYPE_NAME}
						||
						defined $checks->{ACTIVITY_TYPE_ID}
					)
					&&
					(
						defined $checks->{ACTIVITY_TYPE_NAME_IN}
						||
						defined $checks->{ACTIVITY_TYPE_NAME_EQUAL}
						||
						defined $checks->{ACTIVITY_TYPE_NAME_LIKE}
					)
				);
		
		for my $check_key (
			'LAST_VAR_DATE_LT'
			, 'LAST_VAR_DATE_LE'
			, 'LAST_VAR_DATE_EQUAL'
			, 'LAST_VAR_DATE_GE'
			, 'LAST_VAR_DATE_GT'
			, 'CREATION_DATE_LT'
			, 'CREATION_DATE_LE'
			, 'CREATION_DATE_EQUAL'
			, 'CREATION_DATE_GE'
			, 'CREATION_DATE_GT'
		) {
			next unless defined $checks->{$check_key};
			$self->art()->last_error($check_key." must be a date in the format ".$date_format)
				&& (return undef)
					if $self->art()->_dbh()->test_date($checks->{$check_key}, $date_format);
		}
	}
	
	$params{ACTIVE} = 0 unless defined $params{ACTIVE};
	$params{UPDATE_AGING} = 0 unless defined $params{UPDATE_AGING};
	$params{CASE_SENSITIVE} = 1 unless defined $params{CASE_SENSITIVE};
	
	$self->art()->last_error("ONLY_COUNT can be used only with EXTENDED_OUTPUT")
		&& (return undef)
			if ($params{ONLY_COUNT} && !$params{EXTENDED_OUTPUT});
	
	$self->art()->last_error("LIMIT must be a INTEGER!")
		&& (return undef)
			if (defined $params{LIMIT} && ( ref $params{LIMIT} ne '' || $params{LIMIT} !~/^\d+$/));
	
	$self->art()->last_error("SKIP must be a INTEGER!")
		&& (return undef)
			if (defined $params{SKIP} && ( ref $params{SKIP} ne '' || $params{SKIP} !~/^\d+$/));
	
	$self->art()->last_error("SORT must be an ARRAY!")
		&& (return undef)
			unless !defined($params{SORT}) || ref $params{SORT} eq 'ARRAY';
	
	my $check_parent = [];
	foreach my $parent (@{$params{PARENT}}){
		$self->art()->last_error("PARENT: only one element of the array can have UP_LEVEL = ".$parent->{UP_LEVEL})
			&& (return undef)
				if grep {$_ eq $parent->{UP_LEVEL}} @{$check_parent};
		push @{$check_parent}, $parent->{UP_LEVEL};
	}

	
	my $fields = ', 1 "DUMMY"';
	my $join_table = '';
	
	$params{DESCRIPTION_LIKE} = $params{LIKE} if defined $params{LIKE};
	$params{DESCRIPTION_EQUAL} = $params{EQUAL} if defined $params{EQUAL};
	$params{DESCRIPTION_IN} = [] unless defined $params{DESCRIPTION_IN};
	$params{ID_LIKE} = '' unless defined $params{ID_LIKE};
	$params{ID_EQUAL} = '' unless defined $params{ID_EQUAL};
	$params{ID_IN} = [] unless defined $params{ID_EQUAL};
	$params{ACTIVITY_TYPE_NAME_LIKE} = '' unless defined $params{ACTIVITY_TYPE_NAME_LIKE};
	$params{ACTIVITY_TYPE_NAME_EQUAL} = '' unless defined $params{ACTIVITY_TYPE_NAME_EQUAL};
	$params{ACTIVITY_TYPE_NAME_IN} = [] unless defined $params{ACTIVITY_TYPE_NAME_IN};
	$params{SYSTEM_TYPE_NAME_LIKE} = '' unless defined $params{SYSTEM_TYPE_NAME_LIKE};
	$params{SYSTEM_TYPE_NAME_EQUAL} = '' unless defined $params{SYSTEM_TYPE_NAME_EQUAL};
	$params{SYSTEM_TYPE_NAME_IN} = [] unless defined $params{SYSTEM_TYPE_NAME_IN};
	$params{SYSTEM_DESCRIPTION_LIKE} = '' unless defined $params{SYSTEM_DESCRIPTION_LIKE};
	$params{SYSTEM_DESCRIPTION_EQUAL} = '' unless defined $params{SYSTEM_DESCRIPTION_EQUAL};
	$params{SYSTEM_DESCRIPTION_IN} = [] unless defined $params{SYSTEM_DESCRIPTION_IN};
	
	my $orderBy= '';
	
	my @sort_keys = (
		'ACTIVITY_ID',
		'ACTIVITY_TYPE_ID',
		'ACTIVITY_TYPE_NAME',
		'SYSTEM_TYPE_ID',
		'SYSTEM_TYPE_NAME',
		'SYSTEM_ID',
		'CREATION_DATE',
		'DESCRIPTION',
		'STATUS_NAME',
		'LAST_VAR_DATE',
		'ACTIVITY_PROPERTY',
		'SYSTEM_PROPERTY',
		'SYSTEM_DESCRIPTION'
	);
	
	my @properties_keys = ();
	my @properties_keys_parent = ();
	my @system_properties_keys = ();
	my @system_properties_keys_parent = ();
	
	my @used_join = ();
	my @where = ();
	
	if ( $params{'SHOW_ONLY_WITH_VISIBILITY'} ){
		push @where, 'exists (
			select 1
			from permission_sistemi   ps
			where ps.id_sistema = a.id_sistema
			AND ps.id_gruppo_abilitato IN (
				' . join( ',', @{ $self->{ART}->user()->su()->activity()->groups() }) . '
			)
			and rownum<2
		)';
	}

	if ( $params{'SHOW_NO_SUDO'} ){
		push @where, 'exists (
			select 1
			from permission_sistemi   ps
			where ps.id_sistema = a.id_sistema
			AND ps.id_gruppo_abilitato IN (
				' . join( ',', @{ $self->{ART}->user()->groups() }) . '
			)
			and rownum<2
		)';
	}
	
	for (
			'ACTIVITY_TYPE_NAME_EQUAL'
			,'ACTIVITY_TYPE_NAME_IN'
			,'ACTIVITY_TYPE_NAME_LIKE'
		){
		if ( 
			(ref $params{$_} && ref $params{$_} eq 'ARRAY' && scalar @{$params{$_}})
			||
			(!ref $params{$_} && $params{$_})
		){
			unless (grep {$_ eq 'TA' } @used_join){
				$join_table.= ' join tipi_attivita ta on ta.id_tipo_attivita = a.id_tipo_attivita';
				push @used_join, 'TA';
			}
		}
	}
	
	my $ret_remap = undef;
	if ($params{ACTIVITY_TYPE_NAME_EQUAL} || ($params{ACTIVITY_TYPE_NAME_IN} && scalar @{$params{ACTIVITY_TYPE_NAME_IN}} == 1)){
		if ($self->{ART}->flatdta_support()){
			my $ret_remap_table = $self->{ART}->get_flatdta_list_remap_table_by_activity_type(ACTIVITY_TYPE_NAME => ($params{ACTIVITY_TYPE_NAME_EQUAL}||$params{ACTIVITY_TYPE_NAME_IN}->[0]));
			if (defined $ret_remap_table){
				$ret_remap = $self->{ART}->get_flatdta_list_remap_property_by_activity_type(ACTIVITY_TYPE_NAME => ($params{ACTIVITY_TYPE_NAME_EQUAL}||$params{ACTIVITY_TYPE_NAME_IN}->[0]));
				unless (grep {$_ eq 'PT' } @used_join){
					$join_table.= ' join '.$ret_remap_table.' pt on pt.id_attivita = a.id_attivita';
					push @used_join, 'PT';
				}
			}
		}
	}
	for (
			'SYSTEM_TYPE_NAME_EQUAL'
			,'SYSTEM_TYPE_NAME_LIKE'
			,'SYSTEM_TYPE_NAME_IN'
			,'SYSTEM_DESCRIPTION_EQUAL'
			,'SYSTEM_DESCRIPTION_LIKE'
			,'SYSTEM_DESCRIPTION_IN'
		){
		if ( 
			(ref $params{$_} && ref $params{$_} eq 'ARRAY' && scalar @{$params{$_}})
			||
			(!ref $params{$_} && $params{$_})
		){
			unless (grep {$_ eq 'S' } @used_join){
				$join_table.= ' join sistemi s on s.id_sistema = a.id_sistema';
				push @used_join, 'S';
			}
		}
	}
	
	for (
			'SYSTEM_TYPE_NAME_EQUAL'
			,'SYSTEM_TYPE_NAME_LIKE'
			,'SYSTEM_TYPE_NAME_IN'
		){
		if ( 
			(ref $params{$_} && ref $params{$_} eq 'ARRAY' && scalar @{$params{$_}})
			||
			(!ref $params{$_} && $params{$_})
		){
			unless (grep {$_ eq 'TS' } @used_join){
				$join_table.= ' join tipi_sistema ts on ts.id_tipo_sistema = s.id_tipo_sistema';
				push @used_join, 'TS';
			}
		}
	}
	
	if ( $params{ID_LIKE} ) {
		push @where, 'a.id_attivita like '.$self->art()->_dbh()->quote($params{ID_LIKE});
	}
	if ( $params{ID_EQUAL} ) {
		push @where, 'a.id_attivita = '.$self->art()->_dbh()->quote($params{ID_EQUAL});
	}
	if ( $params{ID_IN} ) {
		my @activityId_ripulito;
		for my $activityId (@{$params{ID_IN}}){
			push @activityId_ripulito, $self->art()->_dbh()->quote($activityId) if defined $activityId;
		}
		if (scalar(@activityId_ripulito)){
			push @where, 'a.id_attivita IN (' . join(',', @activityId_ripulito) . ')';
		}
	}
	
	if ($params{ASSIGNED_TO_ME}){
		push @where, 'a.id_operatore_corrente = '.$self->art()->user()->id();
	}
	
	if ($params{ASSIGNED_TO} && scalar @{$params{ASSIGNED_TO}}){
		my @remap_user = map($self->art()->get_user_id($_), @{$params{ASSIGNED_TO}});
		push @where, 'a.id_operatore_corrente in ('. join (',', @remap_user) .')';
	}
	
	if ($params{CREATED_BY_ME}){
		push @where, 'a.id_op_creazione = '.$self->art()->user()->id();
	}
	
	if ($params{CREATED_BY} && scalar @{$params{CREATED_BY}}){
		my @remap_user = map($self->art()->get_user_id($_), @{$params{CREATED_BY}});
		push @where, 'a.id_op_creazione in ('. join (',', @remap_user) .')';
	}
	
	my $type = [];
	if ( defined $params{ACTIVITY_TYPE_ID} ) {
		for my $activity_type_id (@{$params{ACTIVITY_TYPE_ID}}){
			my $ati = $self->art()->test_activity_type_id($activity_type_id);
			$self->art()->last_error("ACTIVITY_TYPE_ID ".$activity_type_id." not found!")
				&& (return undef)
					unless $ati;
			push @$type, $activity_type_id;
		}
	} elsif ( defined $params{ACTIVITY_TYPE_NAME} ) {
		for my $activity_type_name (@{$params{ACTIVITY_TYPE_NAME}}){
			my $sqlActivityType;
			if ($params{CASE_SENSITIVE}){
				$sqlActivityType = "select ID_TIPO_ATTIVITA from tipi_attivita where nome_tipo_attivita = ".$self->art()->_dbh()->quote($activity_type_name);
			} else {
				$sqlActivityType = "select ID_TIPO_ATTIVITA from tipi_attivita where nome_tipo_attivita = upper(".$self->art()->_dbh()->quote($activity_type_name).")";
			}
			my $ati = $self->art()->_dbh()->fetch_minimalized($sqlActivityType);
			$self->art()->last_error("ACTIVITY_TYPE_NAME ".$activity_type_name." not found!")
				&& (return undef)
					unless $ati;
			push @$type, $ati;
		}
	} elsif ( $params{ACTIVITY_TYPE_NAME_EQUAL} || $params{ACTIVITY_TYPE_NAME_IN} || $params{ACTIVITY_TYPE_NAME_LIKE}){
		if ( $params{ACTIVITY_TYPE_NAME_EQUAL} ) {
			push @where, "ta.nome_tipo_attivita = ${\$self->art()->_dbh()->quote($params{ACTIVITY_TYPE_NAME_EQUAL})}" if $params{CASE_SENSITIVE};
			push @where, "ta.nome_tipo_attivita = upper( ${\$self->art()->_dbh()->quote($params{ACTIVITY_TYPE_NAME_EQUAL})} )" unless $params{CASE_SENSITIVE};
		}
		if ( $params{ACTIVITY_TYPE_NAME_LIKE} ) {
			push @where, "ta.nome_tipo_attivita LIKE ${\$self->art()->_dbh()->quote($params{ACTIVITY_TYPE_NAME_LIKE})}" if $params{CASE_SENSITIVE};
			push @where, "ta.nome_tipo_attivita LIKE upper( ${\$self->art()->_dbh()->quote($params{ACTIVITY_TYPE_NAME_LIKE})} )" unless $params{CASE_SENSITIVE};
		}
		if ( scalar(@{$params{ACTIVITY_TYPE_NAME_IN}}) ) {
			push @where, "ta.nome_tipo_attivita in (" . join(',', map { $self->art()->_dbh()->quote($_) } @{$params{ACTIVITY_TYPE_NAME_IN}}) . ')' if $params{CASE_SENSITIVE};
			push @where, "ta.nome_tipo_attivita in (" . join(',', map { "upper(".$self->art()->_dbh()->quote($_).")" } @{$params{ACTIVITY_TYPE_NAME_IN}}) . ')' unless $params{CASE_SENSITIVE};
		}
	}
	
	if ( scalar @$type ) {
		push @where, 'a.id_tipo_attivita IN (' . join(',', @$type) . ')';
	}
	
	if ( defined $params{SYSTEM_ID}  &&  scalar(@{$params{SYSTEM_ID}}) > 0 ) {
		my @systemId_ripulito;
		for my $systemId (@{$params{SYSTEM_ID}}){
			push @systemId_ripulito, $systemId if $systemId;
		}
		if (scalar(@systemId_ripulito)){
			my @table_id_sistemi;
			for my $id_sistema_tabella (@systemId_ripulito){
				push @table_id_sistemi, 'select '.$id_sistema_tabella." from dual";
			}
			push @where, 'a.id_sistema in (' . join(' union all ', @table_id_sistemi) . ')';
		}
	}
	
	# Solo ticket in stati NON finali
	push @where, "a.stato_corrente NOT IN (select id_stato from stati where flag_stato_partenza IN ('C','Q'))"
		if $params{ACTIVE} == 1;
	# Solo ticket in stati finali
	push @where, "a.stato_corrente IN (select id_stato from stati where flag_stato_partenza IN ('C','Q'))"
		if $params{ACTIVE} == -1;
	
	# Solo ticket per i quali è possibile aggiornare l'aging
	push @where, "a.aggiorna_aging = '1'"
		if $params{UPDATE_AGING} == 1;
	# Solo ticket in stati finali
	push @where, "a.aggiorna_aging is null"
		if $params{UPDATE_AGING} == -1;
	
	if ( $params{DESCRIPTION_EQUAL} ) {
		push @where, "a.descrizione = ${\$self->art()->_dbh()->quote($params{DESCRIPTION_EQUAL})}" if $params{CASE_SENSITIVE};
		push @where, "upper(a.descrizione) = upper( ${\$self->art()->_dbh()->quote($params{DESCRIPTION_EQUAL})} )" unless $params{CASE_SENSITIVE};
	}
	if ( $params{DESCRIPTION_LIKE} ) {
		push @where, "a.descrizione LIKE ${\$self->art()->_dbh()->quote($params{DESCRIPTION_LIKE})}" if $params{CASE_SENSITIVE};
		push @where, "upper(a.descrizione) LIKE upper( ${\$self->art()->_dbh()->quote($params{DESCRIPTION_LIKE})} )" unless $params{CASE_SENSITIVE};
	}
	if ( scalar(@{$params{DESCRIPTION_IN}}) ) {
		push @where, "a.descrizione in (" . join(',', map { $self->art()->_dbh()->quote($_) } @{$params{DESCRIPTION_IN}}) . ')' if $params{CASE_SENSITIVE};
		push @where, "upper(a.descrizione) in (" . join(',', map { "upper(".$self->art()->_dbh()->quote($_).")" } @{$params{DESCRIPTION_IN}}) . ')' unless $params{CASE_SENSITIVE};
	}
	
	if ( $params{LAST_VAR_DATE_LT} ) {
		push @where, "a.data_ult_varstat < to_date(".$self->art()->_dbh()->quote($params{LAST_VAR_DATE_LT}).",".$self->art()->_dbh()->quote($date_format).")";
	}
	if ( $params{LAST_VAR_DATE_LE} ) {
		push @where, "a.data_ult_varstat <= to_date(".$self->art()->_dbh()->quote($params{LAST_VAR_DATE_LE}).",".$self->art()->_dbh()->quote($date_format).")";
	}
	if ( $params{LAST_VAR_DATE_EQUAL} ) {
		push @where, "a.data_ult_varstat = to_date(".$self->art()->_dbh()->quote($params{LAST_VAR_DATE_EQUAL}).",".$self->art()->_dbh()->quote($date_format).")";
	}
	if ( $params{LAST_VAR_DATE_GE} ) {
		push @where, "a.data_ult_varstat > to_date(".$self->art()->_dbh()->quote($params{LAST_VAR_DATE_GE}).",".$self->art()->_dbh()->quote($date_format).")";
	}
	if ( $params{LAST_VAR_DATE_GT} ) {
		push @where, "a.data_ult_varstat >= to_date(".$self->art()->_dbh()->quote($params{LAST_VAR_DATE_GT}).",".$self->art()->_dbh()->quote($date_format).")";
	}
	
	if ( $params{CREATION_DATE_LT} ) {
		push @where, "a.data_creazione < to_date(".$self->art()->_dbh()->quote($params{CREATION_DATE_LT}).",".$self->art()->_dbh()->quote($date_format).")";
	}
	if ( $params{CREATION_DATE_LE} ) {
		push @where, "a.data_creazione <= to_date(".$self->art()->_dbh()->quote($params{CREATION_DATE_LE}).",".$self->art()->_dbh()->quote($date_format).")";
	}
	if ( $params{CREATION_DATE_EQUAL} ) {
		push @where, "a.data_creazione = to_date(".$self->art()->_dbh()->quote($params{CREATION_DATE_EQUAL}).",".$self->art()->_dbh()->quote($date_format).")";
	}
	if ( $params{CREATION_DATE_GE} ) {
		push @where, "a.data_creazione > to_date(".$self->art()->_dbh()->quote($params{CREATION_DATE_GE}).",".$self->art()->_dbh()->quote($date_format).")";
	}
	if ( $params{CREATION_DATE_GT} ) {
		push @where, "a.data_creazione >= to_date(".$self->art()->_dbh()->quote($params{CREATION_DATE_GT}).",".$self->art()->_dbh()->quote($date_format).")";
	}
	
	if ( $params{SYSTEM_TYPE_NAME_EQUAL} ) {
		push @where, "ts.nome_tipo_sistema = ${\$self->art()->_dbh()->quote($params{SYSTEM_TYPE_NAME_EQUAL})}" if $params{CASE_SENSITIVE};
		push @where, "ts.nome_tipo_sistema = upper( ${\$self->art()->_dbh()->quote($params{SYSTEM_TYPE_NAME_EQUAL})} )" unless $params{CASE_SENSITIVE};
	}
	if ( $params{SYSTEM_TYPE_NAME_LIKE} ) {
		push @where, "ts.nome_tipo_sistema LIKE ${\$self->art()->_dbh()->quote($params{SYSTEM_TYPE_NAME_LIKE})}" if $params{CASE_SENSITIVE};
		push @where, "ts.nome_tipo_sistema LIKE upper( ${\$self->art()->_dbh()->quote($params{SYSTEM_TYPE_NAME_LIKE})} )" unless $params{CASE_SENSITIVE};
	}
	if ( scalar(@{$params{SYSTEM_TYPE_NAME_IN}}) ) {
		push @where, "ts.nome_tipo_sistema in (" . join(',', map { $self->art()->_dbh()->quote($_) } @{$params{SYSTEM_TYPE_NAME_IN}}) . ')' if $params{CASE_SENSITIVE};
		push @where, "ts.nome_tipo_sistema in (" . join(',', map { "upper(".$self->art()->_dbh()->quote($_).")" } @{$params{SYSTEM_TYPE_NAME_IN}}) . ')' unless $params{CASE_SENSITIVE};
	}
	
	if ( $params{SYSTEM_DESCRIPTION_EQUAL} ) {
		push @where, "s.descrizione = ${\$self->art()->_dbh()->quote($params{SYSTEM_DESCRIPTION_EQUAL})}" if $params{CASE_SENSITIVE};
		push @where, "upper(s.descrizione) = upper( ${\$self->art()->_dbh()->quote($params{SYSTEM_DESCRIPTION_EQUAL})} )" unless $params{CASE_SENSITIVE};
	}
	if ( $params{SYSTEM_DESCRIPTION_LIKE} ) {
		push @where, "s.descrizione LIKE ${\$self->art()->_dbh()->quote($params{SYSTEM_DESCRIPTION_LIKE})}" if $params{CASE_SENSITIVE};
		push @where, "upper(s.descrizione) LIKE upper( ${\$self->art()->_dbh()->quote($params{SYSTEM_DESCRIPTION_LIKE})} )" unless $params{CASE_SENSITIVE};
	}
	if ( scalar(@{$params{SYSTEM_DESCRIPTION_IN}}) ) {
		push @where, "s.descrizione in (" . join(',', map { $self->art()->_dbh()->quote($_) } @{$params{SYSTEM_DESCRIPTION_IN}}) . ')' if $params{CASE_SENSITIVE};
		push @where, "upper(s.descrizione) in (" . join(',', map { "upper(".$self->art()->_dbh()->quote($_).")" } @{$params{SYSTEM_DESCRIPTION_IN}}) . ')' unless $params{CASE_SENSITIVE};
	}
	
	$params{PARENT} = [] unless defined $params{PARENT};
	
	# Filtra x nome stati
	$params{STATUS_LIKE} = '' unless defined $params{STATUS_LIKE};
	
	if (defined $params{STATUS}){
		for my $status (@{$params{STATUS}}){
			my $sqlStatus;
			if ($params{CASE_SENSITIVE}){
				$sqlStatus = "select ID_STATO from stati where nome = ".$self->art()->_dbh()->quote($status);
			} else {
				$sqlStatus = "select ID_STATO from stati where upper(nome) = upper(".$self->art()->_dbh()->quote($status).")";
			}
			
			my $idStato = $self->art()->_dbh()->fetch_minimalized($sqlStatus);
			$self->art()->last_error("STATUS ".$status." not found!")
				&& return undef
					unless $idStato;
		}
	}
	
	my @status = ();
	push @status, $params{STATUS_EQUAL} if $params{STATUS_EQUAL};
	push @status, @{$params{STATUS_IN}} if $params{STATUS_IN};
	push @status, @{$params{STATUS}} if $params{STATUS};
	if ( scalar(@status) > 0 ) {
		push @where, 'a.stato_corrente IN (select id_stato From stati where nome in ('.join(",", map {$self->art()->_dbh()->quote($_)} @status).'))' if $params{CASE_SENSITIVE};
		push @where, 'a.stato_corrente IN (select id_stato From stati where upper(nome) in ('.join(",", map {"upper(".$self->art()->_dbh()->quote($_).")"} @status).'))' unless $params{CASE_SENSITIVE};
	}
	if ( $params{STATUS_LIKE} ) {
		push @where, 'a.stato_corrente IN (select id_stato From stati where nome like '.$self->art()->_dbh()->quote($params{STATUS_LIKE}).')' if $params{CASE_SENSITIVE};
		push @where, 'a.stato_corrente IN (select id_stato From stati where upper(nome) like upper('.$self->art()->_dbh()->quote($params{STATUS_LIKE}).'))' unless $params{CASE_SENSITIVE}
	}
	
	sub _list_act_props {
		my $self = shift;
		my $type = shift;
		my $act_props_conf = {};
		my $act_props = [];
		if (scalar @{$type}){
			my $tmp_act_props_conf = $self->art()->get_activity_properties_group(ACTIVITY_TYPE_ID => $type);
			return undef unless $tmp_act_props_conf;
			if (scalar @{$type} == 1){
				$act_props_conf->{$type->[0]} = $tmp_act_props_conf ;	
			} else {
				$act_props_conf = $tmp_act_props_conf ;
			}
		} else {
			$act_props_conf = $self->art()->get_activity_properties_group();
			return undef unless $act_props_conf;
		}
		
		for my $act_prop (keys %{$act_props_conf}){
			for my $p (@{$act_props_conf->{$act_prop}}){
				for my $p_hash (@{$p->{PROPERTIES}}){
					push @{$act_props} , $p_hash->{NAME} if ! grep { $_ eq $p_hash->{NAME} } @{$act_props} ;
				}
			}
		}
		return $act_props;
	};
	
	sub _check_act_props {
		my $self = shift;
		my $type_properties = shift;
		my $act_props = shift;
		my $type = shift;
		my %params = @_;
		my $properties_keys = [];
		my $properties_keys_parent = [];
		my $where = [];
		for my $key (keys %{$params{$type_properties}}) {
			if ($type_properties ne 'PROPERTIES'){
				if ($type_properties eq 'ACTIVITY_PROPERTIES_IN') {
					$self->art()->last_error("ACTIVITY_PROPERTIES_IN must be an HASHREF of ARRAY!")
						&& (return undef)
						 if ref ($params{$type_properties}->{$key}) ne 'ARRAY';
				} elsif ($type_properties =~/^(ACTIVITY_PROPERTIES_IS_NULL|ACTIVITY_PROPERTIES_IS_NOT_NULL)$/) {
					$self->art()->last_error("$type_properties of $key must be equal to 1!")
						&& (return undef)
						 if $params{$type_properties}->{$key} != 1;
				}
			
				$self->art()->last_error("PROPERTIES $key is not an ACTIVITY_PROPERTY")
					&& (return undef)
						unless grep {$_ eq $key} @{$act_props};
				
				if ($params{UP_LEVEL}){
					push @{$where}, " exists (
								select 1 from ap ap
									join TIPI_DATI_TECNICI_ATTIVITA tdta on ap.id_tipo_dato_tecnico_attivita = tdta.id_tipo_dato_tecnico_attivita
								where ap.disabilitato is null
									and ap.id_tipo_attivita = a".$params{UP_LEVEL}.".id_tipo_attivita
									and ap.id_tipo_dato_tecnico_attivita = ".$self->art()->get_activity_property_id($key)."
									". (scalar @{$type}>0 ? ('and ap.id_tipo_attivita in ('.(join ',', @{$type}).')') :'' )."		
									and rownum<2
								)" unless grep {defined $_ && $_ eq $key} @{$properties_keys_parent};
				} else {
					push @{$where}, " exists (
								select 1 from ap ap
									join TIPI_DATI_TECNICI_ATTIVITA tdta on ap.id_tipo_dato_tecnico_attivita = tdta.id_tipo_dato_tecnico_attivita
								where ap.disabilitato is null
									and ap.id_tipo_attivita = a.id_tipo_attivita
									and ap.id_tipo_dato_tecnico_attivita = ".$self->art()->get_activity_property_id($key)."
									". (scalar @{$type}>0 ? ('and ap.id_tipo_attivita in ('.(join ',', @{$type}).')') :'' )."		
									and rownum<2
								)" unless grep {defined $_ && $_ eq $key} @{$properties_keys};
				}
				
			}
			push @{$properties_keys}, $key if ! $params{UP_LEVEL} && ! grep {$_ eq $key} @{$properties_keys};
			push @{$properties_keys_parent->[$params{UP_LEVEL}]}, $key if $params{UP_LEVEL} && ! grep {$_ eq $key} @{$properties_keys_parent->[$params{UP_LEVEL}]};
		}
		
		if ($params{UP_LEVEL}){
			return [$where, $properties_keys_parent];
		} else {
			return [$where, $properties_keys];
		}
	};
	
	sub _check_system_props {
		my $self = shift;
		my $type_properties = shift;
		my %params = @_;
		my $properties_keys = [];
		my $properties_keys_parent = [];
		for my $key (keys %{$params{$type_properties}}) {

			if ($type_properties eq 'SYSTEM_PROPERTIES_IN') {
				$self->art()->last_error("SYSTEM_PROPERTIES_IN must be an HASHREF of ARRAY!")
					&& (return undef)
					 if ref ($params{$type_properties}->{$key}) ne 'ARRAY';
			} elsif ($type_properties =~/^(SYSTEM_PROPERTIES_IS_NULL|SYSTEM_PROPERTIES_IS_NOT_NULL)$/) {
				$self->art()->last_error("$type_properties of $key must be equal to 1!")
					&& (return undef)
					 if $params{$type_properties}->{$key} != 1;
			}

			push @{$properties_keys}, $key if ! $params{UP_LEVEL} && ! grep {$_ eq $key} @{$properties_keys};
			push @{$properties_keys_parent->[$params{UP_LEVEL}]}, $key if $params{UP_LEVEL} && ! grep {$_ eq $key} @{$properties_keys_parent->[$params{UP_LEVEL}]};
		}
		
		if ($params{UP_LEVEL}){
			return [$properties_keys_parent];
		} else {
			return [$properties_keys];
		}
	};
	
	my $act_props = [];
	
	if ($params{ACTIVITY_PROPERTIES_EQUAL} || $params{ACTIVITY_PROPERTIES_IN} || $params{ACTIVITY_PROPERTIES_LIKE} || $params{ACTIVITY_PROPERTIES_IS_NULL} || $params{ACTIVITY_PROPERTIES_IS_NOT_NULL} || $params{ACTIVITY_PROPERTIES_NOT_EQUAL}){
		$act_props = $self->_list_act_props($type)|| return undef;
	}
	
	for my $type_properties ('PROPERTIES', 'ACTIVITY_PROPERTIES_EQUAL', 'ACTIVITY_PROPERTIES_IN', 'ACTIVITY_PROPERTIES_LIKE', 'ACTIVITY_PROPERTIES_IS_NULL' ,'ACTIVITY_PROPERTIES_IS_NOT_NULL', 'ACTIVITY_PROPERTIES_NOT_EQUAL'){
		if ($params{$type_properties}){
			my $res = $self->_check_act_props($type_properties, $act_props, $type,%params)|| return undef;
			push @where, @{$res->[0]};
			for my $r (@{$res->[1]}){
				push @properties_keys, $r unless grep {$_ eq $r} @properties_keys;
			}
		}
	}
	
	for my $type_properties ('SYSTEM_PROPERTIES_EQUAL', 'SYSTEM_PROPERTIES_IN', 'SYSTEM_PROPERTIES_LIKE', 'SYSTEM_PROPERTIES_IS_NULL', 'SYSTEM_PROPERTIES_IS_NOT_NULL', 'SYSTEM_PROPERTIES_NOT_EQUAL'){
		if ($params{$type_properties}){
			my $res = $self->_check_system_props($type_properties, %params)|| return undef;
			for my $r (@{$res->[0]}) {
				push @system_properties_keys, $r if ! grep {$_ eq $r } @system_properties_keys;
			}
		}
	}
	
	my $ret_remap_parent = {};

	for my $level (@{$params{PARENT}}) {
		my $type_parent = [];
		if ( defined $level->{ACTIVITY_TYPE_ID} ) {
			for my $activity_type_id (@{$level->{ACTIVITY_TYPE_ID}}){
				my $ati = $self->art()->test_activity_type_id($activity_type_id);
				$self->art()->last_error("ACTIVITY_TYPE_ID ".$activity_type_id." not found!")
					&& (return undef)
						unless $ati;
				push @$type_parent, $activity_type_id;
			}
		} elsif ( defined $level->{ACTIVITY_TYPE_NAME} ) {
			for my $activity_type_name (@{$level->{ACTIVITY_TYPE_NAME}}){
				my $sqlActivityType;
				if ($params{CASE_SENSITIVE}){
					$sqlActivityType = "select ID_TIPO_ATTIVITA from tipi_attivita where nome_tipo_attivita = ".$self->art()->_dbh()->quote($activity_type_name);
				} else {
					$sqlActivityType = "select ID_TIPO_ATTIVITA from tipi_attivita where nome_tipo_attivita = upper(".$self->art()->_dbh()->quote($activity_type_name).")";
				}
				my $ati = $self->art()->_dbh()->fetch_minimalized($sqlActivityType);
				$self->art()->last_error("ACTIVITY_TYPE_NAME ".$activity_type_name." not found!")
					&& (return undef)
						unless $ati;
				push @$type_parent, $ati;
			}
		}
		
		for (my $i=1; $i<=$level->{UP_LEVEL};$i++){
			unless (grep {$_ eq 'APAF'.$i } @used_join){
				$join_table.= ' join a_padre_a_figlio apaf'.$i.' on apaf'.$i.'.figlio = a.id_Attivita' if $i == 1;
				$join_table.= ' join a_padre_a_figlio apaf'.$i.' on apaf'.$i.'.figlio = apaf'.($i-1).'.padre' if $i > 1;
				push @used_join, 'APAF'.$i;
			}
			
			if ( defined $level->{SYSTEM_ID}  &&  scalar(@{$level->{SYSTEM_ID}}) > 0 ) {
				unless (grep {$_ eq 'A'.$i } @used_join){
					$join_table.= ' join attivita a'.$i.' on a'.$i.'.id_attivita = apaf'.$i.'.padre';
					push @used_join, 'A'.$i;
				}
			}
			
			if (
				$level->{ACTIVE}
				||
				$level->{UPDATE_AGING}
				||
				$level->{DESCRIPTION_EQUAL}
				||
				$level->{DESCRIPTION_LIKE}
				||
				$level->{DESCRIPTION_IN} 
				||
				$level->{STATUS_EQUAL}
				||
				$level->{STATUS_LIKE}
				||
				$level->{STATUS_IN}
				||
				$level->{LAST_VAR_DATE_LT}
				||
				$level->{LAST_VAR_DATE_LE}
				||
				$level->{LAST_VAR_DATE_EQUAL}
				||
				$level->{LAST_VAR_DATE_GE}
				||
				$level->{LAST_VAR_DATE_GT}
				||
				$level->{CREATION_DATE_LT}
				||       
				$level->{CREATION_DATE_LE}
				||       
				$level->{CREATION_DATE_EQUAL}
				||       
				$level->{CREATION_DATE_GE}
				||       
				$level->{CREATION_DATE_GT}
			) {
				unless (grep {$_ eq 'A'.$i } @used_join){
					$join_table.= ' join attivita a'.$i.' on a'.$i.'.id_attivita = apaf'.$i.'.padre';
					push @used_join, 'A'.$i;
				}
			}
			
			if (
				$level->{SYSTEM_DESCRIPTION_EQUAL}
				||
				$level->{SYSTEM_DESCRIPTION_LIKE}
				||
				$level->{SYSTEM_DESCRIPTION_IN} 
			) {
				unless (grep {$_ eq 'A'.$i } @used_join){
					$join_table.= ' join attivita a'.$i.' on a'.$i.'.id_attivita = apaf'.$i.'.padre';
					push @used_join, 'A'.$i;
				}
				
				unless (grep {$_ eq 'S'.$i } @used_join){
					$join_table.= ' join sistemi s'.$i.' on a'.$i.'.id_sistema = s'.$i.'.id_sistema';
					push @used_join, 'S'.$i;
				}
			}
			
			if (
				$level->{SYSTEM_TYPE_NAME_EQUAL}
				||
				$level->{SYSTEM_TYPE_NAME_LIKE}
				||
				$level->{SYSTEM_TYPE_NAME_IN} 
			) {
				unless (grep {$_ eq 'A'.$i } @used_join){
					$join_table.= ' join attivita a'.$i.' on a'.$i.'.id_attivita = apaf'.$i.'.padre';
					push @used_join, 'A'.$i;
				}
				
				unless (grep {$_ eq 'S'.$i } @used_join){
					$join_table.= ' join sistemi s'.$i.' on a'.$i.'.id_sistema = s'.$i.'.id_sistema';
					push @used_join, 'S'.$i;
				}
				
				unless (grep {$_ eq 'TS'.$i } @used_join){
					$join_table.= ' join tipi_sistema ts'.$i.' on s'.$i.'.id_tipo_sistema = ts'.$i.'.id_tipo_sistema';
					push @used_join, 'TS'.$i;
				}
			}
			
			if ( scalar @$type_parent ) {
				unless (grep {$_ eq 'A'.$i } @used_join){
					$join_table.= ' join attivita a'.$i.' on a'.$i.'.id_attivita = apaf'.$i.'.padre';
					push @used_join, 'A'.$i;
				}
				unless (grep {$_ eq 'TA'.$i } @used_join){
					$join_table.= ' join tipi_attivita ta'.$i.' on ta'.$i.'.id_tipo_attivita = a'.$i.'.id_tipo_attivita';
					push @used_join, 'TA'.$i;
				}
			}

		}
		
		if ( defined $level->{ACTIVITY_TYPE_NAME_EQUAL} || defined $level->{ACTIVITY_TYPE_NAME_IN} || defined $level->{ACTIVITY_TYPE_NAME_LIKE}){
			unless (grep {$_ eq 'A'.$level->{UP_LEVEL} } @used_join){
				$join_table.= ' join attivita a'.$level->{UP_LEVEL}.' on a'.$level->{UP_LEVEL}.'.id_attivita = apaf'.$level->{UP_LEVEL}.'.padre';
				push @used_join, 'A'.$level->{UP_LEVEL};
			}
			unless (grep {$_ eq 'TA'.$level->{UP_LEVEL} } @used_join){
				$join_table.= ' join tipi_attivita ta'.$level->{UP_LEVEL}.' on ta'.$level->{UP_LEVEL}.'.id_tipo_attivita = a'.$level->{UP_LEVEL}.'.id_tipo_attivita';
				push @used_join, 'TA'.$level->{UP_LEVEL};
			}
			if ( defined $level->{ACTIVITY_TYPE_NAME_EQUAL} ) {
				push @where, 'ta'.$level->{UP_LEVEL}.'.nome_tipo_attivita = '.$self->art()->_dbh()->quote($level->{ACTIVITY_TYPE_NAME_EQUAL}) if $params{CASE_SENSITIVE};
				push @where, 'ta'.$level->{UP_LEVEL}.'.nome_tipo_attivita = upper( '.$self->art()->_dbh()->quote($level->{ACTIVITY_TYPE_NAME_EQUAL}).' )' unless $params{CASE_SENSITIVE};
			}
			if ( defined $level->{ACTIVITY_TYPE_NAME_LIKE} ) {
				push @where, 'ta'.$level->{UP_LEVEL}.'.nome_tipo_attivita LIKE '.$self->art()->_dbh()->quote($level->{ACTIVITY_TYPE_NAME_LIKE}) if $params{CASE_SENSITIVE};
				push @where, 'ta'.$level->{UP_LEVEL}.'.nome_tipo_attivita LIKE upper( '.$self->art()->_dbh()->quote($level->{ACTIVITY_TYPE_NAME_LIKE}).' )' unless $params{CASE_SENSITIVE};
			}
			if ( defined $level->{ACTIVITY_TYPE_NAME_IN} && scalar(@{$level->{ACTIVITY_TYPE_NAME_IN}}) ) {
				push @where, 'ta'.$level->{UP_LEVEL}.'.nome_tipo_attivita in (' . join(',', map { $self->art()->_dbh()->quote($_) } @{$level->{ACTIVITY_TYPE_NAME_IN}}) . ')' if $params{CASE_SENSITIVE};
				push @where, 'ta'.$level->{UP_LEVEL}.'.nome_tipo_attivita in (' . join(',', map { "upper(".$self->art()->_dbh()->quote($_).")" } @{$level->{ACTIVITY_TYPE_NAME_IN}}) . ')' unless $params{CASE_SENSITIVE};
			}
			if ($level->{ACTIVITY_TYPE_NAME_EQUAL} || ($level->{ACTIVITY_TYPE_NAME_IN} && scalar @{$level->{ACTIVITY_TYPE_NAME_IN}} == 1)){
				if ($self->{ART}->flatdta_support()){
					my $ret_remap_table = $self->{ART}->get_flatdta_list_remap_table_by_activity_type(ACTIVITY_TYPE_NAME => ($level->{ACTIVITY_TYPE_NAME_EQUAL}||$level->{ACTIVITY_TYPE_NAME_IN}->[0]));
					if (defined $ret_remap_table){
						unless (grep {$_ eq 'PT'.$level->{UP_LEVEL} } @used_join){
							$join_table.= ' join '.$ret_remap_table.' pt'.$level->{UP_LEVEL}.' on pt'.$level->{UP_LEVEL}.'.id_attivita = apaf'.$level->{UP_LEVEL}.'.padre';
							push @used_join, 'PT'.$level->{UP_LEVEL};
						}
						$ret_remap_parent->{$level->{UP_LEVEL}} = $self->{ART}->get_flatdta_list_remap_property_by_activity_type(ACTIVITY_TYPE_NAME => ($level->{ACTIVITY_TYPE_NAME_EQUAL}||$level->{ACTIVITY_TYPE_NAME_IN}->[0]));
					}
				}
			}
		}
		
		if ( $level->{ID_LIKE} ) {
			push @where, 'apaf'.$level->{UP_LEVEL}.'.padre like '.$self->art()->_dbh()->quote($level->{ID_LIKE});
		}
		if ( $level->{ID_EQUAL} ) {
			push @where, 'apaf'.$level->{UP_LEVEL}.'.padre = '.$level->{ID_EQUAL};
		}
		if ( $level->{ID_IN} ) {
			push @where, 'apaf'.$level->{UP_LEVEL}.'.padre in (' . join(",", @{$level->{ID_IN}}) . ')';
		}
		
		push @where, 'a'.$level->{UP_LEVEL}.'.id_tipo_attivita IN (' . join(',', @$type_parent) . ')' if ( scalar @$type_parent );
		
		if (defined $level->{SYSTEM_ID} && scalar(@{$level->{SYSTEM_ID}})){
			my @table_id_sistemi;
			for my $id_sistema_tabella (@{$level->{SYSTEM_ID}}){
				push @table_id_sistemi, 'select '.$id_sistema_tabella." from dual";
			}
			push @where, 'a'.$level->{UP_LEVEL}.'.id_sistema IN (' . join(' union all ', @table_id_sistemi) . ')';
		}
		
		if (defined $level->{ACTIVE} ){
			# Solo ticket in stati NON finali
			push @where, "a".$level->{UP_LEVEL}.".stato_corrente NOT IN (select id_stato from stati where flag_stato_partenza IN ('C','Q'))"
				if $level->{ACTIVE} == 1;
			# Solo ticket in stati finali
			push @where, "a".$level->{UP_LEVEL}.".stato_corrente IN (select id_stato from stati where flag_stato_partenza IN ('C','Q'))"
				if $level->{ACTIVE} == -1;
		}
		
		if (defined $level->{UPDATE_AGING} ){
			# Solo ticket per cui è possibile aggiornare l'aging
			push @where, "a".$level->{UP_LEVEL}.".update_aging = '1'"
				if $level->{UPDATE_AGING} == 1;
			# Solo ticket per cui è non possibile aggiornare l'aging
			push @where, "a".$level->{UP_LEVEL}.".update_aging is null"
				if $level->{UPDATE_AGING} == -1;
		}
		
		if ( $params{SHOW_ONLY_WITH_VISIBILITY} ){
			unless (grep {$_ eq 'A'.$level->{UP_LEVEL} } @used_join){
				$join_table.= ' join attivita a'.$level->{UP_LEVEL}.' on a'.$level->{UP_LEVEL}.'.id_attivita = apaf'.$level->{UP_LEVEL}.'.padre';
				push @used_join, 'A'.$level->{UP_LEVEL};
			}
			push @where, 'exists (
				select 1
				from permission_sistemi   ps'.$level->{UP_LEVEL}.'
				where ps'.$level->{UP_LEVEL}.'.id_sistema = a'.$level->{UP_LEVEL}.'.id_sistema
				AND ps'.$level->{UP_LEVEL}.'.id_gruppo_abilitato IN (
					' . join( ',', @{ $self->{ART}->user()->su()->activity()->groups() }) . '
				)
				and rownum<2
			)';
		}

		if ( $params{SHOW_NO_SUDO} ){
			unless (grep {$_ eq 'A'.$level->{UP_LEVEL} } @used_join){
				$join_table.= ' join attivita a'.$level->{UP_LEVEL}.' on a'.$level->{UP_LEVEL}.'.id_attivita = apaf'.$level->{UP_LEVEL}.'.padre';
				push @used_join, 'A'.$level->{UP_LEVEL};
			}
			push @where, 'exists (
				select 1
				from permission_sistemi   ps'.$level->{UP_LEVEL}.'
				where ps'.$level->{UP_LEVEL}.'.id_sistema = a'.$level->{UP_LEVEL}.'.id_sistema
				AND ps'.$level->{UP_LEVEL}.'.id_gruppo_abilitato IN (
					' . join( ',', @{ $self->{ART}->user()->groups() }) . '
				)
				and rownum<2
			)';
		}
		
		if ( $level->{ASSIGNED_TO_ME} ){
			push @where, "a".$level->{UP_LEVEL}.".id_operatore_corrente = ".$self->art()->user()->id();
		}
		
		if ( $level->{ASSIGNED_TO} && scalar @{$level->{ASSIGNED_TO}}){
			my @remap_user = map($self->art()->get_user_id($_), @{$level->{ASSIGNED_TO}});
			push @where, "a".$level->{UP_LEVEL}.".id_operatore_corrente in (". join (',', @remap_user) .")";
		}
		
		if ($params{CREATED_BY_ME}){
			push @where, "a".$level->{UP_LEVEL}.".id_op_creazione = ".$self->art()->user()->id();
		}
		
		if ($params{CREATED_BY} && scalar @{$params{CREATED_BY}}){
			my @remap_user = map($self->art()->get_user_id($_), @{$params{CREATED_BY}});
			push @where, "a".$level->{UP_LEVEL}.".id_op_creazione in (". join (',', @remap_user) .")";
		}
		
		if ( $level->{DESCRIPTION_EQUAL} ) {
			push @where, "a".$level->{UP_LEVEL}.".descrizione = ${\$self->art()->_dbh()->quote($level->{DESCRIPTION_EQUAL})}" if $level->{CASE_SENSITIVE};
			push @where, "upper(a".$level->{UP_LEVEL}.".descrizione) = upper( ${\$self->art()->_dbh()->quote($level->{DESCRIPTION_EQUAL})} )" unless $level->{CASE_SENSITIVE};
		}
		if ( $level->{DESCRIPTION_LIKE} ) {
			push @where, "a".$level->{UP_LEVEL}.".descrizione LIKE ${\$self->art()->_dbh()->quote($level->{DESCRIPTION_LIKE})}" if $level->{CASE_SENSITIVE};
			push @where, "upper(a".$level->{UP_LEVEL}.".descrizione) LIKE upper( ${\$self->art()->_dbh()->quote($level->{DESCRIPTION_LIKE})} )" unless $level->{CASE_SENSITIVE};
		}
		if ( $level->{DESCRIPTION_IN} && scalar(@{$level->{DESCRIPTION_IN}}) ) {
			push @where, "a".$level->{UP_LEVEL}.".descrizione in (" . join(',', map { $self->art()->_dbh()->quote($_) } @{$level->{DESCRIPTION_IN}}) . ')' if $level->{CASE_SENSITIVE};
			push @where, "upper(a".$level->{UP_LEVEL}.".descrizione) in (" . join(',', map { "upper(".$self->art()->_dbh()->quote($_).")" } @{$level->{DESCRIPTION_IN}}) . ')' unless $level->{CASE_SENSITIVE};
		}
		
		if ( $level->{LAST_VAR_DATE_LT} ) {
			push @where, "a".$level->{UP_LEVEL}.".data_ult_varstat < to_date(".$self->art()->_dbh()->quote($level->{LAST_VAR_DATE_LT}).",".$self->art()->_dbh()->quote($date_format).")";
		}
		if ( $level->{LAST_VAR_DATE_LE} ) {
			push @where, "a".$level->{UP_LEVEL}.".data_ult_varstat <= to_date(".$self->art()->_dbh()->quote($level->{LAST_VAR_DATE_LE}).",".$self->art()->_dbh()->quote($date_format).")";
		}
		if ( $level->{LAST_VAR_DATE_EQUAL} ) {
			push @where, "a".$level->{UP_LEVEL}.".data_ult_varstat = to_date(".$self->art()->_dbh()->quote($level->{LAST_VAR_DATE_EQUAL}).",".$self->art()->_dbh()->quote($date_format).")";
		}
		if ( $level->{LAST_VAR_DATE_GE} ) {
			push @where, "a".$level->{UP_LEVEL}.".data_ult_varstat > to_date(".$self->art()->_dbh()->quote($level->{LAST_VAR_DATE_GE}).",".$self->art()->_dbh()->quote($date_format).")";
		}
		if ( $level->{LAST_VAR_DATE_GT} ) {
			push @where, "a".$level->{UP_LEVEL}.".data_ult_varstat >= to_date(".$self->art()->_dbh()->quote($level->{LAST_VAR_DATE_GT}).",".$self->art()->_dbh()->quote($date_format).")";
		}
		
		if ( $level->{CREATION_DATE_LT} ) {
			push @where, "a".$level->{UP_LEVEL}.".data_creazione < to_date(".$self->art()->_dbh()->quote($level->{CREATION_DATE_LT}).",".$self->art()->_dbh()->quote($date_format).")";
		}
		if ( $level->{CREATION_DATE_LE} ) {
			push @where, "a".$level->{UP_LEVEL}.".data_creazione <= to_date(".$self->art()->_dbh()->quote($level->{CREATION_DATE_LE}).",".$self->art()->_dbh()->quote($date_format).")";
		}
		if ( $level->{CREATION_DATE_EQUAL} ) {
			push @where, "a".$level->{UP_LEVEL}.".data_creazione = to_date(".$self->art()->_dbh()->quote($level->{CREATION_DATE_EQUAL}).",".$self->art()->_dbh()->quote($date_format).")";
		}
		if ( $level->{CREATION_DATE_GE} ) {
			push @where, "a".$level->{UP_LEVEL}.".data_creazione > to_date(".$self->art()->_dbh()->quote($level->{CREATION_DATE_GE}).",".$self->art()->_dbh()->quote($date_format).")";
		}
		if ( $level->{CREATION_DATE_GT} ) {
			push @where, "a".$level->{UP_LEVEL}.".data_creazione >= to_date(".$self->art()->_dbh()->quote($level->{CREATION_DATE_GT}).",".$self->art()->_dbh()->quote($date_format).")";
		}
		
		if ( $level->{SYSTEM_TYPE_NAME_EQUAL} ) {
			push @where, "ts".$level->{UP_LEVEL}.".nome_tipo_sistema = ${\$self->art()->_dbh()->quote($level->{SYSTEM_TYPE_NAME_EQUAL})}" if $level->{CASE_SENSITIVE};
			push @where, "ts".$level->{UP_LEVEL}.".nome_tipo_sistema = upper( ${\$self->art()->_dbh()->quote($level->{SYSTEM_TYPE_NAME_EQUAL})} )" unless $level->{CASE_SENSITIVE};
		}
		if ( $level->{SYSTEM_TYPE_NAME_LIKE} ) {
			push @where, "ts".$level->{UP_LEVEL}.".nome_tipo_sistema LIKE ${\$self->art()->_dbh()->quote($level->{SYSTEM_TYPE_NAME_LIKE})}" if $level->{CASE_SENSITIVE};
			push @where, "ts".$level->{UP_LEVEL}.".nome_tipo_sistema LIKE upper( ${\$self->art()->_dbh()->quote($level->{SYSTEM_TYPE_NAME_LIKE})} )" unless $level->{CASE_SENSITIVE};
		}
		if ( $level->{SYSTEM_TYPE_NAME_IN} && scalar(@{$level->{SYSTEM_TYPE_NAME_IN}}) ) {
			push @where, "ts".$level->{UP_LEVEL}.".nome_tipo_sistema in (" . join(',', map { $self->art()->_dbh()->quote($_) } @{$level->{SYSTEM_TYPE_NAME_IN}}) . ')' if $level->{CASE_SENSITIVE};
			push @where, "ts".$level->{UP_LEVEL}.".nome_tipo_sistema in (" . join(',', map { "upper(".$self->art()->_dbh()->quote($_).")" } @{$level->{SYSTEM_TYPE_NAME_IN}}) . ')' unless $level->{CASE_SENSITIVE};
		}
		
		if ( $level->{SYSTEM_DESCRIPTION_EQUAL} ) {
			push @where, "s".$level->{UP_LEVEL}.".descrizione = ${\$self->art()->_dbh()->quote($level->{SYSTEM_DESCRIPTION_EQUAL})}" if $level->{CASE_SENSITIVE};
			push @where, "upper(s".$level->{UP_LEVEL}.".descrizione) = upper( ${\$self->art()->_dbh()->quote($level->{SYSTEM_DESCRIPTION_EQUAL})} )" unless $level->{CASE_SENSITIVE};
		}
		if ( $level->{SYSTEM_DESCRIPTION_LIKE} ) {
			push @where, "s".$level->{UP_LEVEL}.".descrizione LIKE ${\$self->art()->_dbh()->quote($level->{SYSTEM_DESCRIPTION_LIKE})}" if $level->{CASE_SENSITIVE};
			push @where, "upper(s".$level->{UP_LEVEL}.".descrizione) LIKE upper( ${\$self->art()->_dbh()->quote($level->{SYSTEM_DESCRIPTION_LIKE})} )" unless $level->{CASE_SENSITIVE};
		}
		if ( $level->{SYSTEM_DESCRIPTION_IN} && scalar(@{$level->{SYSTEM_DESCRIPTION_IN}}) ) {
			push @where, "s".$level->{UP_LEVEL}.".descrizione in (" . join(',', map { $self->art()->_dbh()->quote($_) } @{$level->{SYSTEM_DESCRIPTION_IN}}) . ')' if $level->{CASE_SENSITIVE};
			push @where, "upper(s".$level->{UP_LEVEL}.".descrizione) in (" . join(',', map { "upper(".$self->art()->_dbh()->quote($_).")" } @{$level->{SYSTEM_DESCRIPTION_IN}}) . ')' unless $level->{CASE_SENSITIVE};
		}
		
		my @status = ();
		push @status, $level->{STATUS_EQUAL} if $level->{STATUS_EQUAL};
		push @status, @{$level->{STATUS_IN}} if $level->{STATUS_IN};
		if ( scalar(@status) > 0 ) {
			push @where, 'a'.$level->{UP_LEVEL}.'.stato_corrente IN (select id_stato From stati where nome in ('.join(",", map {$self->art()->_dbh()->quote($_)} @status).'))' if $params{CASE_SENSITIVE};
			push @where, 'a'.$level->{UP_LEVEL}.'.stato_corrente IN (select id_stato From stati where upper(nome) in ('.join(",", map {"upper(".$self->art()->_dbh()->quote($_).")"} @status).'))' unless $params{CASE_SENSITIVE};
		}
		if ( $level->{STATUS_LIKE} ) {
			push @where, 'a'.$level->{UP_LEVEL}.'.stato_corrente IN (select id_stato From stati where nome like '.$self->art()->_dbh()->quote($level->{STATUS_LIKE}).')' if $params{CASE_SENSITIVE};
			push @where, 'a'.$level->{UP_LEVEL}.'.stato_corrente IN (select id_stato From stati where upper(nome) like upper('.$self->art()->_dbh()->quote($level->{STATUS_LIKE}).'))' unless $params{CASE_SENSITIVE};
		}
		
		my $act_props = [];
	
		if ($level->{ACTIVITY_PROPERTIES_EQUAL} || $level->{ACTIVITY_PROPERTIES_IN} || $level->{ACTIVITY_PROPERTIES_LIKE} || $level->{ACTIVITY_PROPERTIES_IS_NULL} || $level->{ACTIVITY_PROPERTIES_IS_NOT_NULL} || $level->{ACTIVITY_PROPERTIES_NOT_EQUAL}){
			$act_props = $self->_list_act_props($type_parent)|| return undef;
		}
		
		for my $type_properties ('ACTIVITY_PROPERTIES_EQUAL', 'ACTIVITY_PROPERTIES_IN', 'ACTIVITY_PROPERTIES_LIKE', 'ACTIVITY_PROPERTIES_IS_NULL', 'ACTIVITY_PROPERTIES_IS_NOT_NULL', 'ACTIVITY_PROPERTIES_NOT_EQUAL'){
			if ($level->{$type_properties}){
				my $res = $self->_check_act_props($type_properties, $act_props, $type_parent,%{$level})|| return undef;
				push @where, @{$res->[0]};
				for (my $i=0; $i < scalar (@{$res->[1]}); $i++) {
					if ($properties_keys_parent[$i]){
						for my $r (@{$res->[1]->[$i]}){
							push @{$properties_keys_parent[$i]}, $r unless grep {$_ eq $r} @{$properties_keys_parent[$i]};
						}
					} else {
						foreach (@{$res->[1]->[$i]}){
							if ($properties_keys_parent[$i]){
								push @{$properties_keys_parent[$i]}, $_;
							} else {
								$properties_keys_parent[$i] = [$_];
							}
						}
					}
				}
			}
		}
		
		for my $type_properties ('SYSTEM_PROPERTIES_EQUAL', 'SYSTEM_PROPERTIES_IN', 'SYSTEM_PROPERTIES_LIKE', 'SYSTEM_PROPERTIES_IS_NULL', 'SYSTEM_PROPERTIES_IS_NOT_NULL', 'SYSTEM_PROPERTIES_NOT_EQUAL'){
			if ($level->{$type_properties}){
				unless (grep {$_ eq 'A'.$level->{UP_LEVEL} } @used_join){
					$join_table.= ' join attivita a'.$level->{UP_LEVEL}.' on a'.$level->{UP_LEVEL}.'.id_attivita = apaf'.$level->{UP_LEVEL}.'.padre';
					push @used_join, 'A'.$level->{UP_LEVEL};
				}
				my $res = $self->_check_system_props($type_properties, %{$level})|| return undef;
				
				for (my $i=0; $i < scalar (@{$res->[0]}); $i++) {
					if ($system_properties_keys_parent[$i]){
						for my $r (@{$res->[0]->[$i]}){
							push @{$system_properties_keys_parent[$i]}, $r unless grep {$_ eq $r} @{$system_properties_keys_parent[$i]};
						}
					} else {
						foreach (@{$res->[0]->[$i]}){
							$system_properties_keys_parent[$i] = [$_];
						}
					}
				}
			}
		}
	}
	
	my $pre='';
	my $post='';
	my $skip_limit='';
	
	if (defined $params{LIMIT} && defined $params{SKIP}){
		if ($params{__DASHBOARDS_SUMMARY__}){
			$pre = "select es2.ACTIVITY_ID
						, (select stato_corrente from v_attivita vds where vds.id = es2.ACTIVITY_ID) STATO_CORRENTE
					From (
						select es.ACTIVITY_ID from (
			";
			$post = ") es
			) es2
			";
		}
		$skip_limit = "OFFSET ".$params{SKIP}." ROWS FETCH NEXT ".$params{LIMIT}." ROWS ONLY";
	} elsif (defined $params{LIMIT}){
		if ($params{__DASHBOARDS_SUMMARY__}){
			$pre = "select es.ACTIVITY_ID , (select stato_corrente from v_attivita vds where vds.id = es2.ACTIVITY_ID) STATO_CORRENTE from (";
			$post = ") es ";
		}
		$skip_limit = "OFFSET 0 ROWS FETCH NEXT ".$params{LIMIT}." ROWS ONLY";
	} elsif (defined $params{SKIP}){
		if ($params{__DASHBOARDS_SUMMARY__}){
			$pre = "select es2.ACTIVITY_ID
					, (select stato_corrente from v_attivita vds where vds.id = es2.ACTIVITY_ID) STATO_CORRENTE
				From (
						select es.ACTIVITY_ID from (
			";
			$post = ") es
			) es2
			";
		}
		$skip_limit = "OFFSET ".$params{SKIP}." ROWS";
	}
	
	# Filtri sulla data ultima variazione
	if ( $params{LAST_MODIFY_LE} ) {
		if ( defined $params{LAST_MODIFY_LE}{DATE_VALUE} && $params{LAST_MODIFY_LE}{DATE_FORMAT} ) {
			my $date_value = $self->art()->quote($params{LAST_MODIFY_LE}{DATE_VALUE});
			my $date_format = $self->art()->quote($params{LAST_MODIFY_LE}{DATE_FORMAT});
			push @where, "a.data_ult_varstat <= to_date($date_value,$date_format)";
		}
	}
	
	sub _manage_properties {
		my $self = shift;
		my %params = @_;
		
		my $best_filter;
		my %hash_fields;
		my %hash_fields_filter;
		
		my $fields = '';
		my $fields_filter = '';
		my $where = [];
		
		my $dta_description;
		my $i = 0;
		my $rownum_threshold = 1000;	# Soglia minima record
		my $max_properties = 1e20;		# Soglia minima record
		my $property_criteria = undef;	# Campo da usare come filtro sugli id_attivita
		my $property_criteria_index = undef;	# Campo da usare come filtro sugli id_attivita

		for my $property_type ('MASTER', 'PARENT') {
			
			my @properties_keys = ();
			my %properties_keys_index = ();
			my %ppp = ();
			
			if ($property_type eq 'MASTER'){
				@properties_keys = @{$params{$property_type}};
				foreach my $key (@properties_keys) {
					$properties_keys_index{$key} = [''];
					$ppp{$properties_keys_index{$key}->[0]} = $params{PARAMS};
				}
			} else {
				for (my $parent_index = 0; scalar @{$params{$property_type}} > $parent_index; $parent_index++ ) {
					foreach my $parent_index_dta (@{$params{$property_type}->[$parent_index]}){
						push @properties_keys, $parent_index_dta if defined $parent_index_dta && ! grep {$_ eq $parent_index_dta} @properties_keys;
						if (defined $properties_keys_index{$parent_index_dta}){
							push @{$properties_keys_index{$parent_index_dta}}, $parent_index;
						} else {
							$properties_keys_index{$parent_index_dta} = [$parent_index];
						}
						
						my @params_index = grep {$_->{UP_LEVEL} eq $parent_index} @{$params{PARAMS}->{PARENT}};
						
						$ppp{$parent_index} = $params_index[0];
						
					}
				}
			}
			
			foreach my $key (@properties_keys) {
				
				my $confronto_desc_dta = undef;
				
				my $tipo_ui = $self->art()->_lookup()->tipo_ui_tdta_nome($key);
				
				my $isodate = undef;
				
				if ($tipo_ui eq 'ISODAT'){
					foreach my $index_pt (@{$properties_keys_index{$key}}) {
						if (defined $ppp{$index_pt}->{PROPERTIES}->{$key} && $ppp{$index_pt}->{PROPERTIES}->{$key} ne ''){
							$isodate = $self->art()->format_iso_date($ppp{$index_pt}->{PROPERTIES}->{$key});
							$self->art()->last_error("PROPERTY ".$key." must be a valid ISO date string representation!")
								&& return 0
									unless $isodate;
							if ($index_pt eq '' && exists $params{RET_REMAP}->{$key}){
								$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" = to_timestamp_tz(".$self->art()->_dbh()->quote($ppp{$index_pt}->{PROPERTIES}->{$key}).",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm')";
							} elsif ($index_pt ne '' && exists $params{RET_REMAP_PARENT}->{$index_pt}->{$key}){ 
								$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" = to_timestamp_tz(".$self->art()->_dbh()->quote($ppp{$index_pt}->{PROPERTIES}->{$key}).",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm')";
							} else {
								$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND to_timestamp_tz(\"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm') = to_timestamp_tz(".$self->art()->_dbh()->quote($ppp{$index_pt}->{PROPERTIES}->{$key}).",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm')";
							}
						}
						if (defined $ppp{$index_pt}->{ACTIVITY_PROPERTIES_EQUAL}->{$key} && $ppp{$index_pt}->{ACTIVITY_PROPERTIES_EQUAL}->{$key} ne ''){
							$isodate = $self->art()->format_iso_date($ppp{$index_pt}->{ACTIVITY_PROPERTIES_EQUAL}->{$key});
							$self->art()->last_error("PROPERTY ".$key." must be a valid ISO date string representation!")
								&& return 0
									unless $isodate;
							if ($index_pt eq '' && exists $params{RET_REMAP}->{$key}){
								$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" = to_timestamp_tz(".$self->art()->_dbh()->quote($ppp{$index_pt}->{ACTIVITY_PROPERTIES_EQUAL}->{$key}).",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm')";
							} elsif ($index_pt ne '' && exists $params{RET_REMAP_PARENT}->{$index_pt}->{$key}){ 
								$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" = to_timestamp_tz(".$self->art()->_dbh()->quote($ppp{$index_pt}->{ACTIVITY_PROPERTIES_EQUAL}->{$key}).",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm')";
							} else {
								$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND to_timestamp_tz(\"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm') = to_timestamp_tz(".$self->art()->_dbh()->quote($ppp{$index_pt}->{ACTIVITY_PROPERTIES_EQUAL}->{$key}).",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm')";
							}
							
						}
						if (defined $ppp{$index_pt}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$key} && $ppp{$index_pt}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$key} ne ''){
							$isodate = $self->art()->format_iso_date($ppp{$index_pt}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$key});
							$self->art()->last_error("PROPERTY ".$key." must be a valid ISO date string representation!")
								&& return 0
									unless $isodate;
							if ($index_pt eq '' && exists $params{RET_REMAP}->{$key}){
								$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" != to_timestamp_tz(".$self->art()->_dbh()->quote($ppp{$index_pt}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$key}).",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm')";
							} elsif ($index_pt ne '' && exists $params{RET_REMAP_PARENT}->{$index_pt}->{$key}){ 
								$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" != to_timestamp_tz(".$self->art()->_dbh()->quote($ppp{$index_pt}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$key}).",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm')";
							} else {
								$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND to_timestamp_tz(\"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm') != to_timestamp_tz(".$self->art()->_dbh()->quote($ppp{$index_pt}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$key}).",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm')";
							}
							
						}
						if (defined $ppp{$index_pt}->{ACTIVITY_PROPERTIES_IN}->{$key} && $ppp{$index_pt}->{ACTIVITY_PROPERTIES_IN}->{$key} ne ''){
							for my $date (@{$ppp{$index_pt}->{ACTIVITY_PROPERTIES_IN}->{$key}}){
								$isodate = $self->art()->format_iso_date($date);
								$self->art()->last_error("PROPERTY ".$key." must contain valid ISO date string representation!")
									&& return 0
										unless $isodate;
							}
							if ($index_pt eq '' && exists $params{RET_REMAP}->{$key}){
								$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" in (" . join(",", map { "to_timestamp_tz(".$self->art()->_dbh()->quote($_).",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm')" } @{$ppp{$index_pt}->{ACTIVITY_PROPERTIES_IN}->{$key}}) . ')';
							} elsif ($index_pt ne '' && exists $params{RET_REMAP_PARENT}->{$index_pt}->{$key}){ 
								$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" in (" . join(",", map { "to_timestamp_tz(".$self->art()->_dbh()->quote($_).",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm')" } @{$ppp{$index_pt}->{ACTIVITY_PROPERTIES_IN}->{$key}}) . ')';
							} else {
								$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND to_timestamp_tz(\"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm') in (" . join(",", map { "to_timestamp_tz(".$self->art()->_dbh()->quote($_).",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm')" } @{$ppp{$index_pt}->{ACTIVITY_PROPERTIES_IN}->{$key}}) . ')';
							}
							
						}
						if (defined $ppp{$index_pt}->{ACTIVITY_PROPERTIES_IS_NULL}->{$key}){
							$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND ".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)." is null";
						}
						if (defined $ppp{$index_pt}->{ACTIVITY_PROPERTIES_IS_NOT_NULL}->{$key}){
							$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND ".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)." is not null";
						}
						
						# Costruisce blocco per estrarre la property
						if ($index_pt eq '' && exists $params{RET_REMAP}->{$key}){
							$hash_fields{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = ", pt.\"".$params{RET_REMAP}->{$key}->{REMAP_NAME}."\" \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" --".$key."\n";
						} elsif ($index_pt ne '' && exists $params{RET_REMAP_PARENT}->{$index_pt}->{$key}){ 
							$hash_fields{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = ", pt".$index_pt.".\"".$params{RET_REMAP_PARENT}->{$index_pt}->{$key}->{REMAP_NAME}."\" \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" --".$key."\n";
						} else {
							$hash_fields{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = ", property_value(a".$index_pt.".id_attivita,".$self->art()->_dbh()->quote($key).") \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" --".$key."\n";
						}
					}
				} else {
					foreach my $index_pt (@{$properties_keys_index{$key}}) {
						if (exists $ppp{$index_pt}->{PROPERTIES}->{$key}) {
							$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" = ".$self->art()->_dbh()->quote($ppp{$index_pt}->{PROPERTIES}->{$key}) if $params{PARAMS}->{CASE_SENSITIVE};
							$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND upper(\"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\") = upper(".$self->art()->_dbh()->quote($ppp{$index_pt}->{PROPERTIES}->{$key}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
						}
						if (exists $ppp{$index_pt}->{ACTIVITY_PROPERTIES_EQUAL}->{$key}) {
							$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" = ".$self->art()->_dbh()->quote($ppp{$index_pt}->{ACTIVITY_PROPERTIES_EQUAL}->{$key}) if $params{PARAMS}->{CASE_SENSITIVE};
							$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND upper(\"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\") = upper(".$self->art()->_dbh()->quote($ppp{$index_pt}->{ACTIVITY_PROPERTIES_EQUAL}->{$key}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
						}
						if (exists $ppp{$index_pt}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$key}) {
							$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" != ".$self->art()->_dbh()->quote($ppp{$index_pt}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$key}) if $params{PARAMS}->{CASE_SENSITIVE};
							$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND upper(\"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\") != upper(".$self->art()->_dbh()->quote($ppp{$index_pt}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$key}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
						}
						if  (exists $ppp{$index_pt}->{ACTIVITY_PROPERTIES_IN}->{$key}) {
							$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" in (" . join(",", map { $self->art()->_dbh()->quote($_) } @{$ppp{$index_pt}->{ACTIVITY_PROPERTIES_IN}->{$key}}) . ')' if $params{PARAMS}->{CASE_SENSITIVE};
							$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND upper(\"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\") in (" . join(",", map { "upper(".$self->art()->_dbh()->quote($_).")" } @{$ppp{$index_pt}->{ACTIVITY_PROPERTIES_IN}->{$key}}) . ')' unless $params{PARAMS}->{CASE_SENSITIVE};
						}
						if  (exists $ppp{$index_pt}->{ACTIVITY_PROPERTIES_LIKE}->{$key}) {
							$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" like ".$self->art()->_dbh()->quote($ppp{$index_pt}->{ACTIVITY_PROPERTIES_LIKE}->{$key}) if $params{PARAMS}->{CASE_SENSITIVE};
							$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND upper(\"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\") like upper(".$self->art()->_dbh()->quote($ppp{$index_pt}->{ACTIVITY_PROPERTIES_LIKE}->{$key}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
						}
						if  (exists $ppp{$index_pt}->{ACTIVITY_PROPERTIES_IS_NULL}->{$key}) {
							$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" is null"
						}
						if  (exists $ppp{$index_pt}->{ACTIVITY_PROPERTIES_IS_NOT_NULL}->{$key}) {
							$hash_fields_filter{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = "\n AND \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" is not null"
						}
						# Costruisce blocco per estrarre la property
						if ($tipo_ui =~/^(NUMBER|CURRENCY)$/){
							if ($index_pt eq '' && exists $params{RET_REMAP}->{$key}){
								$hash_fields{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = ", to_number_if_valid(pt.\"".$params{RET_REMAP}->{$key}->{REMAP_NAME}."\") \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" --".$key."\n";
							} elsif ($index_pt ne '' && exists $params{RET_REMAP_PARENT}->{$index_pt}->{$key}){ 
								$hash_fields{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = ", to_number_if_valid(pt".$index_pt.".\"".$params{RET_REMAP_PARENT}->{$index_pt}->{$key}->{REMAP_NAME}."\") \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" --".$key."\n";
							} else {
								$hash_fields{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = ", to_number_if_valid(property_value(a".$index_pt.".id_attivita,".$self->art()->_dbh()->quote($key).")) \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" --".$key."\n";
							}
						} else {
							if ($index_pt eq '' && exists $params{RET_REMAP}->{$key}){
								$hash_fields{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = ", pt.\"".$params{RET_REMAP}->{$key}->{REMAP_NAME}."\" \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" --".$key."\n";
							} elsif ($index_pt ne '' && exists $params{RET_REMAP_PARENT}->{$index_pt}->{$key}){ 
								$hash_fields{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = ", pt".$index_pt.".\"".$params{RET_REMAP_PARENT}->{$index_pt}->{$key}->{REMAP_NAME}."\" \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" --".$key."\n";
							} else {
								$hash_fields{$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)} = ", property_value(a".$index_pt.".id_attivita,".$self->art()->_dbh()->quote($key).") \"".$property_type.$index_pt."-".$self->art()->get_activity_property_id($key)."\" --".$key."\n";
							}
						}
					}
				}

				foreach my $index_pt (@{$properties_keys_index{$key}}) {
					# Cerco il numero di righe che rispettano il singolo criterio
					my $count;
					if ( scalar(@properties_keys) > 1 ) {
						my $min_rows = $max_properties < $rownum_threshold ? $max_properties : $rownum_threshold;

						my $to_add = 0;
						
						if ($tipo_ui eq 'ISODAT'){
							if (defined $ppp{$index_pt}->{PROPERTIES}->{$key} && $ppp{$index_pt}->{PROPERTIES}->{$key} ne ''){
								$confronto_desc_dta = " and to_timestamp_tz(dta.descrizione,'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm') = to_timestamp_tz(".$self->art()->_dbh()->quote($ppp{$index_pt}->{PROPERTIES}->{$key}).",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm')";
							}
							if (defined $ppp{$index_pt}->{ACTIVITY_PROPERTIES_EQUAL}->{$key} && $ppp{$index_pt}->{ACTIVITY_PROPERTIES_EQUAL}->{$key} ne ''){
								$confronto_desc_dta = " and to_timestamp_tz(dta.descrizione,'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm') = to_timestamp_tz(".$self->art()->_dbh()->quote($ppp{$index_pt}->{ACTIVITY_PROPERTIES_EQUAL}->{$key}).",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm')";
							}
							if (defined $ppp{$index_pt}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$key} && $ppp{$index_pt}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$key} ne ''){
								$confronto_desc_dta = " and to_timestamp_tz(dta.descrizione,'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm') != to_timestamp_tz(".$self->art()->_dbh()->quote($ppp{$index_pt}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$key}).",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm')";
							}
							if (defined $ppp{$index_pt}->{ACTIVITY_PROPERTIES_IN}->{$key} && $ppp{$index_pt}->{ACTIVITY_PROPERTIES_IN}->{$key} ne ''){
								$confronto_desc_dta = " and to_timestamp_tz(dta.descrizione,'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm') in (" . join(",", map { "to_timestamp_tz(".$self->art()->_dbh()->quote($_).",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm')" } @{$ppp{$index_pt}->{ACTIVITY_PROPERTIES_IN}->{$key}}) . ')';
							}
						} else {
							if (exists $ppp{$index_pt}->{PROPERTIES}->{$key}) {
								$confronto_desc_dta = " and dta.descrizione = ".$self->art()->_dbh()->quote($ppp{$index_pt}->{PROPERTIES}->{$key}) if $params{PARAMS}->{CASE_SENSITIVE};
								$confronto_desc_dta = " and upper(dta.descrizione) = upper(".$self->art()->_dbh()->quote($ppp{$index_pt}->{PROPERTIES}->{$key}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
							}
							if (exists $ppp{$index_pt}->{ACTIVITY_PROPERTIES_EQUAL}->{$key}) {
								$confronto_desc_dta = " and dta.descrizione = ".$self->art()->_dbh()->quote($ppp{$index_pt}->{ACTIVITY_PROPERTIES_EQUAL}->{$key}) if $params{PARAMS}->{CASE_SENSITIVE};
								$confronto_desc_dta = " and upper(dta.descrizione) = upper(".$self->art()->_dbh()->quote($ppp{$index_pt}->{ACTIVITY_PROPERTIES_EQUAL}->{$key}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
							}
							if (exists $ppp{$index_pt}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$key}) {
								$confronto_desc_dta = " and dta.descrizione != ".$self->art()->_dbh()->quote($ppp{$index_pt}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$key}) if $params{PARAMS}->{CASE_SENSITIVE};
								$confronto_desc_dta = " and upper(dta.descrizione) != upper(".$self->art()->_dbh()->quote($ppp{$index_pt}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$key}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
							}
							if  (exists $ppp{$index_pt}->{ACTIVITY_PROPERTIES_IN}->{$key}) {
								$confronto_desc_dta = " and dta.descrizione in (" . join(",", map { $self->art()->_dbh()->quote($_) } @{$ppp{$index_pt}->{ACTIVITY_PROPERTIES_IN}->{$key}}) . ')' if $params{PARAMS}->{CASE_SENSITIVE};
								$confronto_desc_dta = " and upper(dta.descrizione) in (" . join(",", map { "upper(".$self->art()->_dbh()->quote($_).")" } @{$ppp{$index_pt}->{ACTIVITY_PROPERTIES_IN}->{$key}}) . ')' unless $params{PARAMS}->{CASE_SENSITIVE};
							}
							if  (exists $ppp{$index_pt}->{ACTIVITY_PROPERTIES_LIKE}->{$key}) {
								$confronto_desc_dta = " and dta.descrizione like ".$self->art()->_dbh()->quote($ppp{$index_pt}->{ACTIVITY_PROPERTIES_LIKE}->{$key}) if $params{PARAMS}->{CASE_SENSITIVE};
								$confronto_desc_dta = " and upper(dta.descrizione) like upper(".$self->art()->_dbh()->quote($ppp{$index_pt}->{ACTIVITY_PROPERTIES_LIKE}->{$key}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
							}
						}
						if (defined $ppp{$index_pt}->{ACTIVITY_PROPERTIES_IS_NULL}->{$key} && $ppp{$index_pt}->{ACTIVITY_PROPERTIES_IS_NULL}->{$key} ne ''){
							$confronto_desc_dta = " and dta.descrizione is null";
							$to_add = 1; # necessario in quando la query di count non è in grado di valutare correttamente il caso in cui il dato_tecnico_attivita non esista
						}
						if (defined $ppp{$index_pt}->{ACTIVITY_PROPERTIES_IS_NOT_NULL}->{$key} && $ppp{$index_pt}->{ACTIVITY_PROPERTIES_IS_NOT_NULL}->{$key} ne ''){
							$confronto_desc_dta = " and dta.descrizione is not null";
						}
						
						$count = $self->art()->_dbh()->fetch_minimalized(
										qq{
											select count ('x')+$to_add
											from  dati_tecnici_attivita dta
											where dta.id_tipo_dato_tecnico_attivita = ${\$self->art()->get_activity_property_id($key)} -- $key
											  $confronto_desc_dta
											  and rownum <= $min_rows -- Evita che property poco selettive rallentino l'esecuzione
										}
									);
						# Ritorno arrayref vuoto se non esesite almeno una propeprty/riga che rispetta il criterio
						return [] if $count == 0;
					}
					# Memorizzo il miglior criterio di ricerca per settarlo nei filtri sugli id_attivita
					# NB: se e' impostata la ricerca come ACTIVITY_PROPERTIES_IS_NULL l'activity property non puo'
					# essere utilizzata in quanto non sarebbe efficiente
					if ( !exists $ppp{$index_pt}->{ACTIVITY_PROPERTIES_IS_NULL}->{$key} && (scalar(@properties_keys) == 1 || $count < $max_properties )) {
						$max_properties = $count if $count;
						$best_filter = $property_type.$index_pt."-".$self->art()->get_activity_property_id($key);
						$property_criteria = $key;
						$property_criteria_index = $index_pt;
						$dta_description = '';
						if ($index_pt eq '' && exists $params{RET_REMAP}->{$key}){
							$dta_description = " pt.\"".$key."\" = to_timestamp_tz(".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{PROPERTIES}->{$property_criteria}).",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm')" if defined $ppp{$property_criteria_index}->{PROPERTIES}->{$property_criteria} && $tipo_ui eq 'ISODAT';
							if (exists $ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_EQUAL}->{$property_criteria}){
								$dta_description.= " pt.\"".$key."\" = ".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_EQUAL}->{$property_criteria}) if $params{PARAMS}->{CASE_SENSITIVE};
								$dta_description.= " upper(pt.\"".$key."\") = upper(".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_EQUAL}->{$property_criteria}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
							} elsif (exists $ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$property_criteria}){
								$dta_description.= " pt.\"".$key."\" != ".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$property_criteria}) if $params{PARAMS}->{CASE_SENSITIVE};
								$dta_description.= " upper(pt.\"".$key."\") != upper(".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$property_criteria}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
							} elsif (exists $ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_IN}->{$property_criteria}){
								$dta_description.= " pt.\"".$key."\" in (" . join(",", map { $self->art()->_dbh()->quote($_) } @{$ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_IN}->{$property_criteria}}) . ')' if $params{PARAMS}->{CASE_SENSITIVE};
								$dta_description.= " upper(pt.\"".$key."\") in (" . join(",", map { "upper(".$self->art()->_dbh()->quote($_).")" } @{$ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_IN}->{$property_criteria}}) . ')' unless $params{PARAMS}->{CASE_SENSITIVE};
							} elsif (exists $ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_LIKE}->{$property_criteria}){
								$dta_description.= " pt.\"".$key."\" like ".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_LIKE}->{$property_criteria}) if $params{PARAMS}->{CASE_SENSITIVE};
								$dta_description.= " upper(pt.\"".$key."\") like upper(".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_LIKE}->{$property_criteria}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
							} elsif (exists $ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_IS_NULL}->{$property_criteria}){
								$dta_description.= " pt.\"".$key."\" is null";
							} elsif (exists $ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_IS_NOT_NULL}->{$property_criteria}){
								$dta_description.= " pt.\"".$key."\" is not null";
							} elsif (exists $ppp{$property_criteria_index}->{PROPERTIES}->{$property_criteria}){
								$dta_description.= " pt.\"".$key."\" = ".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{PROPERTIES}->{$property_criteria}) if $params{PARAMS}->{CASE_SENSITIVE};
								$dta_description.= " upper(pt.\"".$key."\") = upper(".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{PROPERTIES}->{$property_criteria}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
							}
						} elsif ($index_pt ne '' && exists $params{RET_REMAP_PARENT}->{$index_pt}->{$key}){
							$dta_description = " pt$index_pt.\"".$key."\" = to_timestamp_tz(".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{PROPERTIES}->{$property_criteria}).",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm')" if defined $ppp{$property_criteria_index}->{PROPERTIES}->{$property_criteria} && $tipo_ui eq 'ISODAT';
							if (exists $ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_EQUAL}->{$property_criteria}){
								$dta_description.= " pt$index_pt.\"".$key."\" = ".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_EQUAL}->{$property_criteria}) if $params{PARAMS}->{CASE_SENSITIVE};
								$dta_description.= " upper(pt$index_pt.\"".$key."\") = upper(".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_EQUAL}->{$property_criteria}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
							} elsif (exists $ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$property_criteria}){
								$dta_description.= " pt$index_pt.\"".$key."\" != ".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$property_criteria}) if $params{PARAMS}->{CASE_SENSITIVE};
								$dta_description.= " upper(pt$index_pt.\"".$key."\") != upper(".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$property_criteria}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
							} elsif (exists $ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_IN}->{$property_criteria}){
								$dta_description.= " pt$index_pt.\"".$key."\" in (" . join(",", map { $self->art()->_dbh()->quote($_) } @{$ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_IN}->{$property_criteria}}) . ')' if $params{PARAMS}->{CASE_SENSITIVE};
								$dta_description.= " upper(pt$index_pt.\"".$key."\") in (" . join(",", map { "upper(".$self->art()->_dbh()->quote($_).")" } @{$ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_IN}->{$property_criteria}}) . ')' unless $params{PARAMS}->{CASE_SENSITIVE};
							} elsif (exists $ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_LIKE}->{$property_criteria}){
								$dta_description.= " pt$index_pt.\"".$key."\" like ".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_LIKE}->{$property_criteria}) if $params{PARAMS}->{CASE_SENSITIVE};
								$dta_description.= " upper(pt$index_pt.\"".$key."\") like upper(".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_LIKE}->{$property_criteria}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
							} elsif (exists $ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_IS_NULL}->{$property_criteria}){
								$dta_description.= " pt$index_pt.\"".$key."\" is null";
							} elsif (exists $ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_IS_NOT_NULL}->{$property_criteria}){
								$dta_description.= " pt$index_pt.\"".$key."\" is not null";
							} elsif (exists $ppp{$property_criteria_index}->{PROPERTIES}->{$property_criteria}){
								$dta_description.= " pt$index_pt.\"".$key."\" = ".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{PROPERTIES}->{$property_criteria}) if $params{PARAMS}->{CASE_SENSITIVE};
								$dta_description.= " upper(pt$index_pt.\"".$key."\") = upper(".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{PROPERTIES}->{$property_criteria}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
							}
						} else {
							$dta_description = "and to_timestamp_tz(dta2.descrizione,'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm') = to_timestamp_tz(".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{PROPERTIES}->{$property_criteria}).",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm')" if defined $ppp{$property_criteria_index}->{PROPERTIES}->{$property_criteria} && $tipo_ui eq 'ISODAT';
							if (exists $ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_EQUAL}->{$property_criteria}){
								$dta_description.= " and dta2.descrizione = ".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_EQUAL}->{$property_criteria}) if $params{PARAMS}->{CASE_SENSITIVE};
								$dta_description.= " and upper(dta2.descrizione) = upper(".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_EQUAL}->{$property_criteria}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
							} elsif (exists $ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$property_criteria}){
								$dta_description.= " and dta2.descrizione != ".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$property_criteria}) if $params{PARAMS}->{CASE_SENSITIVE};
								$dta_description.= " and upper(dta2.descrizione) != upper(".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_NOT_EQUAL}->{$property_criteria}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
							} elsif (exists $ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_IN}->{$property_criteria}){
								$dta_description.= " and dta2.descrizione in (" . join(",", map { $self->art()->_dbh()->quote($_) } @{$ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_IN}->{$property_criteria}}) . ')' if $params{PARAMS}->{CASE_SENSITIVE};
								$dta_description.= " and upper(dta2.descrizione) in (" . join(",", map { "upper(".$self->art()->_dbh()->quote($_).")" } @{$ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_IN}->{$property_criteria}}) . ')' unless $params{PARAMS}->{CASE_SENSITIVE};
							} elsif (exists $ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_LIKE}->{$property_criteria}){
								$dta_description.= " and dta2.descrizione like ".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_LIKE}->{$property_criteria}) if $params{PARAMS}->{CASE_SENSITIVE};
								$dta_description.= " and upper(dta2.descrizione) like upper(".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_LIKE}->{$property_criteria}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
							} elsif (exists $ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_IS_NULL}->{$property_criteria}){
								$dta_description.= " and dta2.descrizione is null";
							} elsif (exists $ppp{$property_criteria_index}->{ACTIVITY_PROPERTIES_IS_NOT_NULL}->{$property_criteria}){
								$dta_description.= " and dta2.descrizione is not null";
							} elsif (exists $ppp{$property_criteria_index}->{PROPERTIES}->{$property_criteria}){
								$dta_description.= " and dta2.descrizione = ".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{PROPERTIES}->{$property_criteria}) if $params{PARAMS}->{CASE_SENSITIVE};
								$dta_description.= " and upper(dta2.descrizione) = upper(".$self->art()->_dbh()->quote($ppp{$property_criteria_index}->{PROPERTIES}->{$property_criteria}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
							}
						}
					}
				}
				
			}
			
		}
		
		# Costruisco la where condition per abbattere il numero di record da cui estrarre le property
		# la aggiungo solo se è stata identificata un actvitiy property che ottimizza la ricerca
		if (defined $property_criteria){
			if (
				($property_criteria_index eq '' && exists $params{RET_REMAP}->{$property_criteria})
				||
				($property_criteria_index ne '' && exists $params{RET_REMAP_PARENT}->{$property_criteria_index}->{$property_criteria})
			){
				push @{$where}, $dta_description;
			} else {
				push @{$where}, "
						a".$property_criteria_index.".id_attivita in (
							select dta2.id_Attivita
							from dati_tecnici_attivita dta2
							where (dta2.id_Attivita, dta2.data_esecuzione, dta2.id_tipo_dato_tecnico_attivita) in (
								select dta.id_attivita, max(data_esecuzione), dta.id_tipo_dato_tecnico_attivita
								from  dati_tecnici_attivita dta
								where dta.id_tipo_dato_tecnico_attivita = ".$self->art()->get_activity_property_id($property_criteria)." -- $property_criteria
								group by dta.id_attivita, dta.id_tipo_dato_tecnico_attivita
							)
								".$dta_description."
						)
				";
			}
		}

		for my $key (keys %hash_fields){
			$fields.= $hash_fields{$key};
		}
		
		for my $key (keys %hash_fields_filter){
			$fields_filter.= $hash_fields_filter{$key} if (!defined $best_filter || $key ne $best_filter);
		}
		
		return [$fields, $fields_filter, $where];
		
	};
	
	sub _manage_system_properties {
		my $self = shift;
		my %params = @_;
		
		my $fields;
		my $fields_filter;
		my @used_join = $params{USED_JOIN};
		my $join_table = $params{JOIN_TABLE};
		
		my $i = 0;
		my $rownum_threshold = 1000;	# Soglia minima record
		my $max_properties = 1e20;		# Soglia minima record
		my $property_criteria = undef;	# Campo da usare come filtro sugli id_attivita
		my $property_criteria_index = undef;	# Campo da usare come filtro sugli id_attivita

		for my $property_type ('MASTER', 'PARENT') {
			
			my @properties_keys = ();
			my %properties_keys_index = ();
			my %ppp = ();
			
			if ($property_type eq 'MASTER'){
				@properties_keys = @{$params{$property_type}};
				foreach my $key (@properties_keys) {
					$properties_keys_index{$key} = [''];
					$ppp{$properties_keys_index{$key}->[0]} = $params{PARAMS};
				}
			} else {
				for (my $parent_index = 0; scalar @{$params{$property_type}} > $parent_index; $parent_index++ ) {
					foreach my $parent_index_dta (@{$params{$property_type}->[$parent_index]}){
						push @properties_keys, $parent_index_dta if defined $parent_index_dta && ! grep {$_ eq $parent_index_dta} @properties_keys;
						if (defined $properties_keys_index{$parent_index_dta}){
							push @{$properties_keys_index{$parent_index_dta}}, $parent_index;
						} else {
							$properties_keys_index{$parent_index_dta} = [$parent_index];
						}
						
						my @params_index = grep {$_->{UP_LEVEL} eq $parent_index} @{$params{PARAMS}->{PARENT}};
						
						$ppp{$parent_index} = $params_index[0];
						
					}
				}
			}
			
			foreach my $key (@properties_keys) {
				
				foreach my $index_pt (@{$properties_keys_index{$key}}) {
					if (exists $ppp{$index_pt}->{SYSTEM_PROPERTIES_EQUAL}->{$key}) {
						$fields_filter .= "\n AND \"S".$property_type.$index_pt."-".$self->art()->get_system_property_id($key)."\" = ".$self->art()->_dbh()->quote($ppp{$index_pt}->{SYSTEM_PROPERTIES_EQUAL}->{$key}) if $params{PARAMS}->{CASE_SENSITIVE};
						$fields_filter .= "\n AND upper(\"S".$property_type.$index_pt."-".$self->art()->get_system_property_id($key)."\") = upper(".$self->art()->_dbh()->quote($ppp{$index_pt}->{SYSTEM_PROPERTIES_EQUAL}->{$key}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
					}
					if (exists $ppp{$index_pt}->{SYSTEM_PROPERTIES_NOT_EQUAL}->{$key}) {
						$fields_filter .= "\n AND \"S".$property_type.$index_pt."-".$self->art()->get_system_property_id($key)."\" != ".$self->art()->_dbh()->quote($ppp{$index_pt}->{SYSTEM_PROPERTIES_NOT_EQUAL}->{$key}) if $params{PARAMS}->{CASE_SENSITIVE};
						$fields_filter .= "\n AND upper(\"S".$property_type.$index_pt."-".$self->art()->get_system_property_id($key)."\") != upper(".$self->art()->_dbh()->quote($ppp{$index_pt}->{SYSTEM_PROPERTIES_NOT_EQUAL}->{$key}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
					}
					if  (exists $ppp{$index_pt}->{SYSTEM_PROPERTIES_IN}->{$key}) {
						$fields_filter .= "\n AND \"S".$property_type.$index_pt."-".$self->art()->get_system_property_id($key)."\" in (" . join(",", map { $self->art()->_dbh()->quote($_) } @{$ppp{$index_pt}->{SYSTEM_PROPERTIES_IN}->{$key}}) . ')' if $params{PARAMS}->{CASE_SENSITIVE};
						$fields_filter .= "\n AND upper(\"S".$property_type.$index_pt."-".$self->art()->get_system_property_id($key)."\") in (" . join(",", map { "upper(".$self->art()->_dbh()->quote($_).")" } @{$ppp{$index_pt}->{SYSTEM_PROPERTIES_IN}->{$key}}) . ')' unless $params{PARAMS}->{CASE_SENSITIVE};
					}
					if  (exists $ppp{$index_pt}->{SYSTEM_PROPERTIES_LIKE}->{$key}) {
						$fields_filter .= "\n AND \"S".$property_type.$index_pt."-".$self->art()->get_system_property_id($key)."\" like ".$self->art()->_dbh()->quote($ppp{$index_pt}->{SYSTEM_PROPERTIES_LIKE}->{$key}) if $params{PARAMS}->{CASE_SENSITIVE};
						$fields_filter .= "\n AND upper(\"S".$property_type.$index_pt."-".$self->art()->get_system_property_id($key)."\") like upper(".$self->art()->_dbh()->quote($ppp{$index_pt}->{SYSTEM_PROPERTIES_LIKE}->{$key}).")" unless $params{PARAMS}->{CASE_SENSITIVE};
					}
					if (exists $ppp{$index_pt}->{SYSTEM_PROPERTIES_IS_NULL}->{$key}) {
						$fields_filter .= "\n AND \"S".$property_type.$index_pt."-".$self->art()->get_system_property_id($key)."\" is null";
					}
					if (exists $ppp{$index_pt}->{SYSTEM_PROPERTIES_IS_NOT_NULL}->{$key}) {
						$fields_filter .= "\n AND \"S".$property_type.$index_pt."-".$self->art()->get_system_property_id($key)."\" is not null";
					}
					# Costruisce blocco per estrarre la property

					if ($self->art()->is_system_property_name_multiplex($key)){
						unless (grep {$_ eq 'DATI_TECNICI_'.$self->art()->get_system_property_id($key)} @used_join){
							$join_table.=" join dati_tecnici dtmult".$self->art()->get_system_property_id($key)." on a.id_sistema = dtmult".$self->art()->get_system_property_id($key).".id_sistema and dtmult".$self->art()->get_system_property_id($key).".id_tipo_dato_tecnico = ".$self->art()->get_system_property_id($key);
							push @used_join, 'DATI_TECNICI_'.$self->art()->get_system_property_id($key);
						}
						$fields.= ", dtmult".$self->art()->get_system_property_id($key).".descrizione \"S".$property_type.$index_pt."-".$self->art()->get_system_property_id($key)."\" --".$key."\n" if $params{PARAMS}->{CASE_SENSITIVE};
						$fields.= ", upper(dtmult".$self->art()->get_system_property_id($key).".descrizione) \"S".$property_type.$index_pt."-".$self->art()->get_system_property_id($key)."\" --".$key."\n" unless $params{PARAMS}->{CASE_SENSITIVE};
					} else {
						unless (grep {$_ eq 'DATI_TECNICI_'.$self->art()->get_system_property_id($key)} @used_join){
							$join_table.=" left join dati_tecnici dts".$self->art()->get_system_property_id($key)." on a.id_sistema = dts".$self->art()->get_system_property_id($key).".id_sistema and dts".$self->art()->get_system_property_id($key).".id_tipo_dato_tecnico = ".$self->art()->get_system_property_id($key);
							push @used_join, 'DATI_TECNICI_'.$self->art()->get_system_property_id($key);
						}
						#$fields .= ", (select valore From v_dt_sistemi where id_sistema = a".$index_pt.".id_sistema and nome = ".$self->art()->_dbh()->quote($key)." and attivo = 1) \"S".$property_type.$index_pt."-".$self->art()->get_system_property_id($key)."\" --".$key."\n";
						$fields.= ", ''||dts".$self->art()->get_system_property_id($key).".descrizione \"S".$property_type.$index_pt."-".$self->art()->get_system_property_id($key)."\" --".$key."\n";
					}
				}

			}
			
		}
		
		return [$fields, $fields_filter, $join_table, @used_join];
		
	};
	
	my $fields_filter = 'DUMMY = 1';
	if (scalar @properties_keys || scalar @properties_keys_parent) {
		my $res = $self->_manage_properties(MASTER => \@properties_keys, PARENT => \@properties_keys_parent, PARAMS => \%params, RET_REMAP => $ret_remap, RET_REMAP_PARENT => $ret_remap_parent);
		return undef unless $res;
		unless (scalar @{$res}){
			if ($params{EXTENDED_OUTPUT}){
				return {
					COUNT => 0
					,RESULTS => []
				};
			} else {
				return [];
			}
		};
		$fields.= $res->[0];
		$fields_filter.= $res->[1];
		push @where, @{$res->[2]};
	}
	
	if (scalar @system_properties_keys || scalar @system_properties_keys_parent) {
		my $res = $self->_manage_system_properties(MASTER => \@system_properties_keys, PARENT => \@system_properties_keys_parent, PARAMS => \%params, JOIN_TABLE => $join_table , USED_JOIN => \@used_join );
		return undef unless $res;
		unless (scalar @{$res}){
			if ($params{EXTENDED_OUTPUT}){
				return {
					COUNT => 0
					,RESULTS => []
				};
			} else {
				return [];
			}
		};
		$fields.= $res->[0];
		$fields_filter.= $res->[1];
		$join_table = $res->[2];
		@used_join = $res->[3];
	}
	
	# se non è definito un ordinamento imposto come default l'id_attivita
	$params{SORT} = [] unless defined $params{SORT};
	# aggiungo solo se non esiste già
	my @sortKeys;
	for my $k (@{$params{SORT}}){
		push @sortKeys, keys %{$k};
	}
	push @{$params{SORT}}, {ACTIVITY_ID => 1} unless grep {$_ eq 'ACTIVITY_ID'} @sortKeys;
	
	my @used_sort_fields = ();
	my @used_sort_fields_property = (); 
	
	for my $sort_index (@{$params{SORT}}){
		$self->art()->last_error("SORT field must be an HASH!")
			&& (return undef)
				if ref $sort_index ne 'HASH';
		
		$self->art()->last_error("Every SORT field must contain one key!")
			&& return undef
				if scalar keys %{$sort_index} != 1;
		
		my @key = keys %{$sort_index};
		my $key_name = $key[0];
		
		my $parent_level = '';
		
		if ($key_name =~ /^PARENT_/ ){
			my @split = split ('_',$key_name);
			
			shift @split;
			$parent_level = shift @split;
			
			my $parent_key = join ('_', @split);
			
			$self->art()->last_error("Cannot sort by ".$key_name)
				&& (return undef)
					unless grep { $_ eq $parent_key } @sort_keys;
			
			$key_name = $parent_key;
		} else {
			$self->art()->last_error("Cannot sort by ".$key_name)
				&& (return undef)
					unless grep { $_ eq $key_name } @sort_keys;
		}
		
		my @val = values %{$sort_index};
		
		my $index_sort;
		my $key_order;
		if ($key_name ne 'ACTIVITY_PROPERTY' && $key_name ne 'SYSTEM_PROPERTY'){
			$self->art()->last_error("Sort field ".($parent_level ? 'PARENT_'.$parent_level.'_'.$key_name: $key_name)." duplicate!")
				&& (return undef)
					if grep {$_ eq ($parent_level ? 'PARENT_'.$parent_level.'_'.$key_name: $key_name)} @used_sort_fields;
			
			push @used_sort_fields, ($parent_level ? 'PARENT_'.$parent_level.'_'.$key_name: $key_name);
			
			$key_order = '"'.$parent_level.$key_name.'"';
			$index_sort = $val[0];
			if ($key_name =~/^(ACTIVITY_TYPE_ID)$/){
				$fields .= ", a".$parent_level.".id_tipo_attivita \"".$parent_level.$key_name."\"";
			} elsif ($key_name =~/^(ACTIVITY_TYPE_NAME)$/){
				$fields .= ", ta".$parent_level.".nome_tipo_attivita \"".$parent_level.$key_name."\"";
				unless (grep {$_ eq 'TA'.$parent_level} @used_join){
					$join_table.=" join tipi_attivita ta".$parent_level." on ta".$parent_level.".id_tipo_attivita = a".$parent_level.".id_tipo_attivita";
					push @used_join, 'TA'.$parent_level;
				}
			} elsif ($key_name =~/^(SYSTEM_TYPE_ID|SYSTEM_TYPE_NAME)$/){
				unless (grep {$_ eq 'S'.$parent_level} @used_join){
					$join_table.=" join sistemi s".$parent_level." on s".$parent_level.".id_sistema = a".$parent_level.".id_sistema";
					push @used_join, 'S'.$parent_level;
				}
				if ($key_name eq 'SYSTEM_TYPE_ID'){
					$fields .= ", s".$parent_level.".id_tipo_sistema \"".$parent_level.$key_name."\"";
				} elsif ($key_name eq 'SYSTEM_TYPE_NAME'){
					$fields .= ", ts".$parent_level.".nome_tipo_sistema \"".$parent_level.$key_name."\"";
					unless (grep {$_ eq 'TS'.$parent_level} @used_join){
						$join_table.=" join tipi_sistema ts".$parent_level." on ts".$parent_level.".id_tipo_sistema = s".$parent_level.".id_tipo_sistema";
						push @used_join, 'TS'.$parent_level;
					}
				}
			} elsif ($key_name =~/^(SYSTEM_ID)$/){
				$fields .= ", a".$parent_level.".id_sistema \"".$parent_level.$key_name."\"";
			} elsif ($key_name =~/^(CREATION_DATE)$/){
				$fields .= ", a".$parent_level.".DATA_CREAZIONE \"".$parent_level.$key_name."\"";
			} elsif ($key_name =~/^(DESCRIPTION)$/){
				$fields .= ", a".$parent_level.".descrizione \"".$parent_level.$key_name."\"";
			} elsif ($key_name =~/^(SYSTEM_DESCRIPTION)$/){
				$fields .= ", s".$parent_level.".descrizione \"".$parent_level.$key_name."\"";
			} elsif ($key_name =~/^(STATUS_NAME)$/){
				$fields .= ", st".$parent_level.".NOME \"".$parent_level.$key_name."\"";
				unless (grep {$_ eq 'ST'.$parent_level} @used_join){
					$join_table.=" join stati st".$parent_level." on st".$parent_level.".id_stato = a".$parent_level.".stato_corrente";
					push @used_join, 'ST'.$parent_level;
				}
			} elsif ($key_name =~/^(LAST_VAR_DATE)$/){
				$fields .= ", a".$parent_level.".data_ult_varstat \"".$parent_level.$key_name."\"";
			}
			
		} else {
			$self->art()->last_error("SORT field must be an HASH!")
				&& (return undef)
					if ref $val[0] ne 'HASH';
			
			$self->art()->last_error("Every SORT field must contain one key!")
				&& return undef
					if scalar keys %{$val[0]} != 1;
			
			my @key_props = keys %{$val[0]};
			my $key_props_name = $key_props[0];
			my @val_props = values %{$val[0]};
			my $val_props_name = $val_props[0];
			
			$self->art()->last_error(
				"Sort field ".
					($key_name eq 'SYSTEM_PROPERTY' ? 'SYSTEM': '')
					." PROPERTIES ".
					($parent_level ? 
						'PARENT_'.$parent_level.'_'.($key_name eq 'SYSTEM_PROPERTY' ? 'SYSTEM_': '').'PROPERTY_'.$key_props_name 
						: ($key_name eq 'SYSTEM_PROPERTY' ? 'SYSTEM_': '').$key_props_name)
					." duplicate!"
				)
				&& (return undef)
					if grep {$_ eq ($parent_level ? 'PARENT_'.$parent_level.'_'.($key_name eq 'SYSTEM_PROPERTY' ? 'SYSTEM_': '').'PROPERTY_'.$key_props_name : ($key_name eq 'SYSTEM_PROPERTY' ? 'SYSTEM_': '').$key_props_name)} @used_sort_fields_property;
			
			push @used_sort_fields_property, ($parent_level ? 'PARENT_'.$parent_level.($key_name eq 'SYSTEM_PROPERTY' ? 'SYSTEM': '').'_PROPERTY_'.$key_props_name : ($key_name eq 'SYSTEM_PROPERTY' ? 'SYSTEM_': '').$key_props_name);
			
			if ($key_name eq 'ACTIVITY_PROPERTY'){
				
				unless (defined $self->art()->test_activity_property_id($key_props_name)){
					$self->art()->last_error($key_props_name.' is not an ACTIVITY PROPERTY');
					return undef;
				}
				
				my $name = $self->art()->get_activity_property_id($key_props_name);
				
				my $tipo_ui = $self->art()->_lookup()->tipo_ui_tdta_nome($key_props_name);

				
				$self->art()->last_error("Can not sort on field PROPERTIES ".$key_props_name." of TYPE ".$tipo_ui)
					&& return undef
						if ($tipo_ui =~/^(MOOKUP|TAGS)$/);
				
				if ($parent_level){
					$name = 'PARENT'.$parent_level.'-'.$name;
					unless (grep {$_ eq $key_props_name } @{$properties_keys_parent[$parent_level]} ) {
						if (exists $ret_remap_parent->{$parent_level}->{$key_props_name}){ 
							if ($tipo_ui =~/^(NUMBER|CURRENCY)$/){
								$fields .= ", to_number_if_valid(pt$parent_level.\"".$ret_remap_parent->{$parent_level}->{$key_props_name}->{REMAP_NAME}."\") \"".$name ."\" --".$key_props_name."\n";
							} else {
								$fields .= ", pt$parent_level.\"".$ret_remap_parent->{$parent_level}->{$key_props_name}->{REMAP_NAME}."\" \"".$name ."\" --".$key_props_name."\n";
							}
						} else {
							if ($tipo_ui =~/^(NUMBER|CURRENCY)$/){
								$fields .= ", to_number_if_valid(property_value(a".$parent_level.".id_attivita,".$self->art()->_dbh()->quote($key_props_name).")) \"".$name ."\" --".$key_props_name."\n";
							} else {
								$fields .= ", property_value(a".$parent_level.".id_attivita,".$self->art()->_dbh()->quote($key_props_name).") \"".$name ."\" --".$key_props_name."\n";
							}
						}
					}
				} else {
					$name = 'MASTER-'.$name;
					unless (grep {$_ eq $key_props_name } @properties_keys ){
						if (exists $ret_remap->{$key_props_name}){ 
							if ($tipo_ui =~/^(NUMBER|CURRENCY)$/){
								$fields .= ", to_number_if_valid(pt.\"".$ret_remap->{$key_props_name}->{REMAP_NAME}."\") \"".$name ."\" --".$key_props_name."\n";
							} else {
								$fields .= ", pt.\"".$ret_remap->{$key_props_name}->{REMAP_NAME}."\" \"".$name ."\" --".$key_props_name."\n";
							}
						} else {
							if ($tipo_ui =~/^(NUMBER|CURRENCY)$/){
								$fields .= ", to_number_if_valid(property_value(a".$parent_level.".id_attivita,".$self->art()->_dbh()->quote($key_props_name).")) \"".$name ."\" --".$key_props_name."\n";
							} else {
								$fields .= ", property_value(a".$parent_level.".id_attivita,".$self->art()->_dbh()->quote($key_props_name).") \"".$name ."\" --".$key_props_name."\n";
							}
						}
					}
				}
				
				if ($tipo_ui eq 'ISODAT'){
					$key_order = "to_timestamp_tz(\"".$name."\",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm')";
				} else {
					$key_order = '"'.$name.'"';
				}
			} else {

				unless (defined $self->art()->test_system_property_id($key_props_name)){
					$self->art()->last_error($key_props_name.' is not a SYSTEM_PROPERTY');
					return undef;
				}
				
				my $name = $self->art()->get_system_property_id($key_props_name);
				
				my $tipo_tdts = $self->art()->_lookup()->tipo_tdts_nome($key_props_name);

				if ($parent_level){
					$name = 'SPARENT'.$parent_level.'-'.$name;
					unless (grep {$_ eq $key_props_name } @{$system_properties_keys_parent[$parent_level]} ) {
						if ($self->art()->is_system_property_name_multiplex($key_props_name)){
							unless (grep {$_ eq 'DATI_TECNICI_'.$self->art()->get_system_property_id($key_props_name)} @used_join){
								$join_table.=" join dati_tecnici dtmult".$self->art()->get_system_property_id($key_props_name)." on a.id_sistema = dtmult".$self->art()->get_system_property_id($key_props_name).".id_sistema and dtmult".$self->art()->get_system_property_id($key_props_name).".id_tipo_dato_tecnico = ".$self->art()->get_system_property_id($key_props_name);
								push @used_join, 'DATI_TECNICI_'.$self->art()->get_system_property_id($key_props_name);
							}
							if ($tipo_tdts =~/^(INTEGER|LONG|FLOAT)$/){
								$fields.= ", to_number_if_valid(dtmult".$self->art()->get_system_property_id($key_props_name).".descrizione) \"".$name."\" --".$key_props_name."\n";
							} else {
								$fields.= ", dtmult".$self->art()->get_system_property_id($key_props_name).".descrizione \"".$name."\" --".$key_props_name."\n";
							}
						} else {
							unless (grep {$_ eq 'DATI_TECNICI_'.$self->art()->get_system_property_id($key_props_name)} @used_join){
								$join_table.=" left join dati_tecnici dts".$self->art()->get_system_property_id($key_props_name)." on a.id_sistema = dts".$self->art()->get_system_property_id($key_props_name).".id_sistema and dts".$self->art()->get_system_property_id($key_props_name).".id_tipo_dato_tecnico = ".$self->art()->get_system_property_id($key_props_name);
								push @used_join, 'DATI_TECNICI_'.$self->art()->get_system_property_id($key_props_name);
							}
							if ($tipo_tdts =~/^(INTEGER|LONG|FLOAT)$/){
								#$fields .= ", to_number_if_valid((select valore From v_dt_sistemi where id_sistema = a".$parent_level.".id_sistema and nome = ".$self->art()->_dbh()->quote($key_props_name)." and attivo = 1)) \"".$name."\" --".$key_props_name."\n";
								$fields.= ", to_number_if_valid(dts".$self->art()->get_system_property_id($key_props_name).".descrizione) \"".$name."\" --".$key_props_name."\n";
							} else {
								#$fields .= ", (select valore From v_dt_sistemi where id_sistema = a".$parent_level.".id_sistema and nome = ".$self->art()->_dbh()->quote($key_props_name)." and attivo = 1) \"".$name."\" --".$key_props_name."\n";
								$fields.= ", ''||dts".$self->art()->get_system_property_id($key_props_name).".descrizione \"".$name."\" --".$key_props_name."\n";
							}
						}
					}
						;
				} else {
					$name = 'SMASTER-'.$name;
					unless (grep {$_ eq $key_props_name } @system_properties_keys ){
						if ($self->art()->is_system_property_name_multiplex($key_props_name)){
							unless (grep {$_ eq 'DATI_TECNICI_'.$self->art()->get_system_property_id($key_props_name)} @used_join){
								$join_table.=" join dati_tecnici dtmult".$self->art()->get_system_property_id($key_props_name)." on a.id_sistema = dtmult".$self->art()->get_system_property_id($key_props_name).".id_sistema and dtmult".$self->art()->get_system_property_id($key_props_name).".id_tipo_dato_tecnico = ".$self->art()->get_system_property_id($key_props_name);
								push @used_join, 'DATI_TECNICI_'.$self->art()->get_system_property_id($key_props_name);
							}
							if ($tipo_tdts =~/^(INTEGER|LONG|FLOAT)$/){
								$fields.= ", to_number_if_valid(dtmult".$self->art()->get_system_property_id($key_props_name).".descrizione) \"".$name."\" --".$key_props_name."\n";
							} else {
								$fields.= ", dtmult".$self->art()->get_system_property_id($key_props_name).".descrizione \"".$name."\" --".$key_props_name."\n";
							}
						} else {
							unless (grep {$_ eq 'DATI_TECNICI_'.$self->art()->get_system_property_id($key_props_name)} @used_join){
								$join_table.=" left join dati_tecnici dts".$self->art()->get_system_property_id($key_props_name)." on a.id_sistema = dts".$self->art()->get_system_property_id($key_props_name).".id_sistema and dts".$self->art()->get_system_property_id($key_props_name).".id_tipo_dato_tecnico = ".$self->art()->get_system_property_id($key_props_name);
								push @used_join, 'DATI_TECNICI_'.$self->art()->get_system_property_id($key_props_name);
							}
							if ($tipo_tdts =~/^(INTEGER|LONG|FLOAT)$/){
								#$fields .= ", to_number_if_valid((select valore From v_dt_sistemi where id_sistema = a".$parent_level.".id_sistema and nome = ".$self->art()->_dbh()->quote($key_props_name)." and attivo = 1)) \"".$name."\" --".$key_props_name."\n";
								$fields.= ", to_number_if_valid(dts".$self->art()->get_system_property_id($key_props_name).".descrizione) \"".$name."\" --".$key_props_name."\n";
							} else {
								#$fields .= ", (select valore From v_dt_sistemi where id_sistema = a".$parent_level.".id_sistema and nome = ".$self->art()->_dbh()->quote($key_props_name)." and attivo = 1) \"".$name."\" --".$key_props_name."\n";
								$fields.= ", ''||dts".$self->art()->get_system_property_id($key_props_name).".descrizione \"".$name."\" --".$key_props_name."\n";
							}
						}
					}
				}
				
				$key_order = '"'.$name.'"';
			}
			
			$index_sort = $val_props_name;
			
		}
		
		$self->art()->last_error("SORT order can be only 1 or -1!")
			&& return undef
				if (!defined $index_sort || $index_sort !~/^(1|-1)$/);
		
		$orderBy.= ($orderBy ? ', ' : '' ). $key_order. ($index_sort eq '1' ? ' ASC': ' DESC' );
		
	}
	
	$orderBy = ($orderBy ne '' ? ' order by ' : '') . $orderBy;
	
	my $where = '';
	$where = 'WHERE ' . join("\n\t\tAND ", @where) if scalar(@where);
	
	my $elementsQuery = "
		SELECT r.*
			FROM (
		$pre
		select	ACTIVITY_ID R
		from	(
					SELECT a.id_attivita ACTIVITY_ID
						   $fields
					FROM   attivita a
					$join_table
					$where
				)
		where	$fields_filter
		$orderBy
		$post
		) R
		WHERE 1=1
		$skip_limit
	";
	my $countQuery = "
		SELECT COUNT(1) R 
			FROM (
		$pre
		select	ACTIVITY_ID
		from	(
					SELECT a.id_attivita ACTIVITY_ID
						   $fields
					FROM   attivita a
					$join_table
					$where
				)
		where	$fields_filter
		$post
		) R
		WHERE 1=1
	";
	my $dashboardsSummaryQuery = "
		SELECT r.*
			FROM (
		$pre
		select	ACTIVITY_ID
			, (select stato_corrente from v_attivita vds where vds.id = ACTIVITY_ID) STATO_CORRENTE
		from	(
					SELECT a.id_attivita ACTIVITY_ID
						   $fields
					FROM   attivita a
					$join_table
					$where
				)
		where	$fields_filter
		$orderBy
		$post
		) R
		WHERE 1=1
		$skip_limit
	";
	my $finalQuery;
	if ($params{__DASHBOARDS_SUMMARY__}) {
		$finalQuery = $dashboardsSummaryQuery;
	} elsif ($params{EXTENDED_OUTPUT}){
		if ($params{ONLY_COUNT}){
			$finalQuery = $countQuery;
		} else {
			$finalQuery = $countQuery."
				union all
				select *
				from (
			".
					$elementsQuery
			."
				)
			";
		}
	} else {
		$finalQuery = $elementsQuery;
	}

	#print STDERR $finalQuery;
	# Query di selezione
	# la modifica a NLS_NUMERIC_CHARACTERS serve per la gestione dei campi numerici
	my $old_nls_lang = $self->art()->_dbh()->get_session_parameters('NLS_NUMERIC_CHARACTERS');
	$self->art()->_dbh()->set_session_parameters('NLS_NUMERIC_CHARACTERS' => '.,');
	my $result = $self->art()->_dbh()->fetchall_hashref($finalQuery);
	$self->art()->_dbh()->set_session_parameters('NLS_NUMERIC_CHARACTERS' => $old_nls_lang->{NLS_NUMERIC_CHARACTERS});
	
	my $results;
	if ($params{__DASHBOARDS_SUMMARY__}) {
		@$results = map {[$_->{ACTIVITY_ID}, $_->{STATO_CORRENTE}]} @{$result};
	} elsif ($params{EXTENDED_OUTPUT}){
		$results->{COUNT} = $result->[0]->{R}+0;
		shift(@{$result});
		unless ($params{ONLY_COUNT}){
			$results->{RESULTS} = [];
			my $skip = 0;
			for my $res (@{$result}){
				push @{$results->{RESULTS}}, $res->{R};
			}
		}
 	} else {
		@$results = map { $_->{R} } @{$result};
	}
	return $results;
}

sub _get_collection_system { shift->{__COLLECTION_SYSTEM__}}
	

=head2 I<package>->B<new>( ART => I<API::ART> )

Il metodo B<new()> e' il costruttore di classe e richiede un solo argomento:

=over 4

=item B<ART>

Un'istanza della classe API::ART

=back

=cut
sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $self  = $class->SUPER::new(@_);
	
	$self->{__COLLECTION_SYSTEM__} = API::ART::Collection::System->new(ART => $self->art());
	
#	# Impostazione classe degli oggetti da generare col metodo ->create()
#	$self->{__CLASS_TO_CREATE__} = 'API::ART::Activity';
	$self = bless( $self, $class );
	return $self;
}


=head2 I<object>->B<art>()

Ritorna il riferimento all'oggetto API::ART passato come argomento al costruttore.

=head2 I<object>->B<find_id>()

=head2 I<object>->B<find_object>()

I due metodi sono simili con l'unica differenza che B<find_id()> ritorna
un I<arrayref> di ACTIVITY_ID, mentre B<find_object()> ritorna un I<arrayref> di
riferimenti ad oggetti di tipo B<API::ART::Activity>.

Entrambi possono operare sui seguenti parametri opzionali:

=over 4

=item B<ID_EQUAL>

Effettua una ricerca puntuale sull'id_attivita

=item B<ID_IN>

Effettua una ricerca sulle id_attivita elencate

=item B<ID_LIKE>

Effettua una ricerca approssimativa sull'id_attivita (i caratteri jolly '%' e '_' devono essere specificati dall'utilizzatore della classe)

=item B<ACTIVITY_TYPE_ID>

Un I<arrayref> con l'elenco degli ID degli ACTIVTY_TYPE

=item B<ACTIVITY_TYPE_NAME>

DEPRECATO: Un I<arrayref> con l'elenco dei NAME degli ACTIVTY_TYPE. (utilizzare ACTIVITY_TYPE_NAME_IN)

=item B<ACTIVITY_TYPE_NAME_EQUAL>

Consente di filtrare le attivita' in base al nome del tipo attivita': effettua una ricerca esatta.

=item B<ACTIVITY_TYPE_NAME_IN>

Consente di filtrare le attivita' in base ad un elenco di nomi del tipo attivita': effettua una ricerca esatta.

=item B<ACTIVITY_TYPE_NAME_LIKE>

Consentono di filtrare le attivita' in base al nome del tipo attivita': effettua una ricerca approssimativa (i caratteri jolly '%' e '_' devono essere
specificati dall'utilizzatore della classe).

=item B<SYSTEM_ID>

Un I<arrayref> con l'elenco degli ID dei sistemi (SYSTEM_ID) a cui le proprieta'
cercate debbono essere legate

=item B<ACTIVE>

Uno dei seguenti valori:

=over 4

=item 0 (default)

Tutti i ticket

=item 1

Solo i ticket che si trovano in uno stato NON finale

=item -1

Solo i ticket che si trovano in uno stato finale

=back

=item B<UPDATE_AGING>

Uno dei seguenti valori:

=over 4

=item 0 (default)

Tutti i ticket

=item 1

Solo i ticket per cui è possibile aggiornare l'aging

=item -1

Solo i ticket per cui non è possibile aggiornare l'aging

=back

=item B<SUPENDED>

(TODO)

=item B<EQUAL> oppure B<LIKE>

DEPRECATO: da sostituire con il nuovi metodi DESCRIPTION_*. Consentono di filtrare le attivita' in base al loro nome: B<EQUAL> effettua una ricerca esatta
mentre B<LIKE> una ricerca approssimativa (i caratteri jolly '%' e '_' devono essere
specificati dall'utilizzatore della classe).

=item B<DESCRIPTION_EQUAL>

Consente di filtrare le attivita' in base al loro nome: effettua una ricerca esatta.

=item B<DESCRIPTION_IN>

Consente di filtrare le attivita' in base ad un elenco di loro nomi: effettua una ricerca esatta.

=item B<DESCRIPTION_LIKE>

Consentono di filtrare le attivita' in base al loro nome: effettua una ricerca approssimativa (i caratteri jolly '%' e '_' devono essere
specificati dall'utilizzatore della classe).

=item B<CASE_SENSITIVE>

Prevede una ricerca sensibile alle maiuscole/minuscole

=item B<LAST_VAR_DATE_EQUAL>

Consente di filtrare le attivita' in base alla data di ultima variazione: effettua una ricerca esatta.

=item B<LAST_VAR_DATE_LT>

Consente di filtrare le attivita' in base alla data di ultima variazione: effettua una ricerca minore.

=item B<LAST_VAR_DATE_LE>

Consente di filtrare le attivita' in base alla data di ultima variazione: effettua una ricerca minore o uguale.

=item B<LAST_VAR_DATE_GT>

Consente di filtrare le attivita' in base alla data di ultima variazione: effettua una ricerca maggiore.

=item B<LAST_VAR_DATE_GE>

Consente di filtrare le attivita' in base alla data di ultima variazione: effettua una ricerca maggiore o uguale.

=item B<CREATION_DATE_EQUAL>

Consente di filtrare le attivita' in base alla data di creazione: effettua una ricerca esatta.

=item B<CREATION_DATE_LT>

Consente di filtrare le attivita' in base alla data di creazione: effettua una ricerca minore.

=item B<CREATION_DATE_LE>

Consente di filtrare le attivita' in base alla data di creazione: effettua una ricerca minore o uguale.

=item B<CREATION_DATE_GT>

Consente di filtrare le attivita' in base alla data di creazione: effettua una ricerca maggiore.

=item B<CREATION_DATE_GE>

Consente di filtrare le attivita' in base alla data di creazione: effettua una ricerca maggiore o uguale.

=item B<STATUS> 

DEPRECATO: utilizzare STATUS_IN. Un I<arrayref> con l'elenco dei NAME degli ACTIVITY_STATUS

=item B<STATUS_EQUAL> 

Uno I<scalar> con il NAME dell'ACTIVITY_STATUS

=item B<STATUS_IN> 

Un I<arrayref> con l'elenco dei NAME degli ACTIVITY_STATUS

=item B<STATUS_LIKE>

Effettua una ricerca approssimativa sul nome dello stato (i caratteri jolly '%' e '_' devono essere specificati dall'utilizzatore della classe)

=item B<PROPERTIES>

DEPRECATO: utilizzare le ricerche nelle ACTIVITY_PROPERTIES. Un I<hashref> con le coppie NAME => VALUE delle ACTIVITY_PROPERTY

=item B<ACTIVITY_PROPERTIES_EQUAL>

Un I<hashref> con le coppie NAME => VALUE delle ACTIVITY_PROPERTY

=item B<ACTIVITY_PROPERTIES_NOT_EQUAL>

Un I<hashref> con le coppie NAME => VALUE delle ACTIVITY_PROPERTY

=item B<ACTIVITY_PROPERTIES_IN>

Un I<hashref> con le coppie NAME => <array> di VALUE delle ACTIVITY_PROPERTY

=item B<ACTIVITY_PROPERTIES_LIKE>

Un I<hashref> con le coppie NAME => 'STRING' ricerca approssimativa sul valore del dato tecnico attivita (i caratteri jolly '%' e '_' devono essere specificati dall'utilizzatore della classe)

=item B<ACTIVITY_PROPERTIES_IS_NULL>

Un I<hashref> con le coppie NAME => 1 delle ACTIVITY_PROPERTY per le quali il valore deve essere null

=item B<ACTIVITY_PROPERTIES_IS_NOT_NULL>

Un I<hashref> con le coppie NAME => 1 delle ACTIVITY_PROPERTY per le quali il valore deve essere not null

=item B<SYSTEM_TYPE_NAME_EQUAL>

Consente di filtrare le attivita' in base al nome del tipo sistema: effettua una ricerca esatta.

=item B<SYSTEM_TYPE_NAME_IN>

Consente di filtrare le attivita' in base ad un elenco di nomi del tipo sistema: effettua una ricerca esatta.

=item B<SYSTEM_TYPE_NAME_LIKE>

Consentono di filtrare le attivita' in base al nome del tipo sistema: effettua una ricerca approssimativa (i caratteri jolly '%' e '_' devono essere
specificati dall'utilizzatore della classe).

=item B<SYSTEM_DESCRIPTION_EQUAL>

Consente di filtrare le attivita' in base al nome del sistema: effettua una ricerca esatta.

=item B<SYSTEM_DESCRIPTION_IN>

Consente di filtrare le attivita' in base ad un elenco di nomi del sistema: effettua una ricerca esatta.

=item B<SYSTEM_DESCRIPTION_LIKE>

Consentono di filtrare le attivita' in base al nome del sistema: effettua una ricerca approssimativa (i caratteri jolly '%' e '_' devono essere
specificati dall'utilizzatore della classe).

=item B<SYSTEM_PROPERTIES_EQUAL>

Un I<hashref> con le coppie NAME => VALUE delle SYSTEM_PROPERTY. NB: In caso di SYSTEM_PROPERTY di tipo MULTIPLEX dovra' essere cmq passato uno scalare e si avra' corrispondenza se almeno uno dei valori corrisponde.

=item B<SYSTEM_PROPERTIES_NOT_EQUAL>

Un I<hashref> con le coppie NAME => VALUE delle SYSTEM_PROPERTY. NB: In caso di SYSTEM_PROPERTY di tipo MULTIPLEX dovra' essere cmq passato uno scalare e si avra' corrispondenza se almeno uno dei valori corrisponde.

=item B<SYSTEM_PROPERTIES_IN>

Un I<hashref> con le coppie NAME => <array> di VALUE delle SYSTEM_PROPERTY. NB: In caso di SYSTEM_PROPERTY di tipo MULTIPLEX si avra' corrispondenza se almeno uno dei valori della SYSTEM_PROPERTY corrisponde.

=item B<SYSTEM_PROPERTIES_LIKE>

Un I<hashref> con le coppie NAME => 'STRING' ricerca approssimativa sul valore del dato tecnico (i caratteri jolly '%' e '_' devono essere specificati dall'utilizzatore della classe). NB: In caso di SYSTEM_PROPERTY di tipo MULTIPLEX dovra' essere cmq passato uno scalare e si avra' corrispondenza se almeno uno dei valori corrisponde.

=item B<SYSTEM_PROPERTIES_IS_NULL>

Un I<hashref> con le coppie NAME => 1 delle SYSTEM_PROPERTY per le quali il valore deve essere null

=item B<SYSTEM_PROPERTIES_IS_NOT_NULL>

Un I<hashref> con le coppie NAME => 1 delle SYSTEM_PROPERTY per le quali il valore deve essere not null

=item B<PARENT>

Permette di filtrare sulle attivita' I<padri> specificando di quanti livelli la ricerca deve salire. 
Deve essere passato Un I<arrayref> di I<hash> contenenti la chiave I<UP_LEVEL> (con il livello di risalita) e le chiavi descritte in questo documento a meno delle chiavi I<PARENT>, I<SORT>, I<SKIP>, I<LIMIT>, I<EXTENDED_OUTPUT>, I<ONLY_COUNT> e I<DATE_FORMAT>.
Esempio: per cercare le attivita' il cui padre e' di tipo I<KART> e' necessario passare la seguente struttura:

PARENT=> {
	UP_LEVEL => 1
	,ACTIVITY_TYPE_NAME => ['KART']
}

=item B<SORT>

Un I<arrayref> contenete hash nella forma {KEY => SORT}

SORT puo' valere '1' per ordinamento ascendente oppure '-1' per ordinamento discendente mentre e KEY possibili sono:

=over 4

=item ACTIVITY_ID

=item ACTIVITY_TYPE_ID

=item ACTIVITY_TYPE_NAME

=item SYSTEM_TYPE_ID

=item SYSTEM_TYPE_NAME

=item SYSTEM_ID

=item CREATION_DATE

=item DESCRIPTION

=item SYSTEM_DESCRIPTION

=item STATUS_NAME

=item LAST_VAR_DATE

=item ACTIVITY_PROPERTY

In caso di filtro sulle property sara' necessario specificarlo come hashref {KEY => SORT}

=item SYSTEM_PROPERTY

In caso di filtro sulle property sara' necessario specificarlo come hashref {KEY => SORT}

=item PARENT_I<UP_LEVEL>_*

In caso di ordinamento sulle caratteristiche delle attivita' I<padri> e' necessario anteporre il prefisso B<PARENT_I<UP_LEVEL>_>

=back

=item B<SKIP>

Numero di record da skippare

=item B<LIMIT>

Numero massimo di record restituiti

=item B<EXTENDED_OUTPUT>

Restituisce un HASHREF con le chiavi COUNT (con il totale dei risultati trovati senza applicare i filtri SKIP e LIMIT) e RESULTS con l'array delle attivita' 

=item B<SHOW_ONLY_WITH_VISIBILITY>

Restituisce solo le attivita' per cui l'utente ha visibilita' (eventualmente con SUDO)

=item B<SHOW_NO_SUDO>

Restituisce solo le attivita' per cui l'utente ha visibilita' (solo no SUDO)

=item B<ASSIGNED_TO_ME>

Restituisce solo le attivita' assegnate all'utente

=item B<ASSIGNED_TO>

Restituisce solo le attivita' assegnate ad un elenco di utenti specificato

=item B<CREATED_BY_ME>

Restituisce solo le attivita' create dall'utente

=item B<CREATED_BY>

Restituisce solo le attivita' create da un elenco di utenti specificato

=item B<ONLY_COUNT>

Restituisce solo il conteggio delle attivita' trovate nella chiave COUNT. NB: puo' essere utilizzato solo in presenza del paramentro I<EXTENDED_OUTPUT>

=item B<DATE_FORMAT>

Formato date

=back

=head2 I<object>->B<find_exec>()

Questo metodo opera analogamente a B<find_id()> e B<find_object()> ma accetta in input
il parametro B<CALLBACK> che consente di eseguire un blocco di codice per ogni oggetto
trovato.

Ritorna un I<arrayref> in cui ogni elemento rappresenta il B<return code> del codice
di B<CALLBACK> che puo' essere qualsiasi tipo di dato atto a tracciare l'esecuzione del
blocco di codice per ogni sistema, in modo che il chiamante abbia la possibilita' di
eseguire operazioni successive sulla base dell'esito dell'elaborazione.

=cut

=head2 I<object>->B<create>( I<hash> )

Consente di creare una nuova attivita' sulla base delle informazioni passate nella chiamata al metodo stesso.

Se l'operazione ha successo, il metodo ritorna il riferimento ad oggetto di tipo API::ART::Activity altrimenti
ritorna I<undef> ed imposta il messaggio di errore nella proprieta' B<last_error()> dell'oggetto API::ART
passato nel costruttore.

Di seguito i parametri del metodo B<create()>:

=over 4

=item B<SYSTEM_ID>

ID del sistema a cui si vuole associare la nuova attivita'. Se non specificato, deve essere specificato il paramentro B<SYSTEM>.

=item B<SYSTEM>

Hashref contenente le chiavi necessarie per la creazione di un nuovo sistema. Se non specificato, deve essere specificato il paramentro B<SYSTEM_ID>.

=over 4

=item B<SYSTEM_TYPE_ID> | B<SYSTEM_TYPE_NAME>

ID e NOME del tipo di sistema da creare

=item B<SYSTEM_CATEGORY_ID> | B<SYSTEM_CATEGORY_NAME>

ID e NOME della categoria da assegnare al sistema da creare

=item B<SYSTEM_CLASS_ID> | B<SYSTEM_CLASS_NAME>

ID e NOME della classe da assegnare al sistema da creare

=item B<OBJECT_TYPE_ID> | B<OBJECT_TYPE_NAME>

ID e NOME del tipo-oggetto da assegnare al sistema da creare

=item B<DESCRIPTION>

Descrizione da associare al nuovo sistema

=item B<DATE>

Data di creazione del sistema; se omesso verra' utilizzato I<sysdate>

=item B<GROUPS>

ARRAYREF con l'elenco dei nomi dei gruppi che dovranno avere visibilita' sul sistema 

=item B<PROPERTIES> I<hashref>

Un I<hashref> delle proprieta' da associare al sistema

=back

=item B<ACTIVITY_TYPE_ID> | B<ACTIVITY_TYPE_NAME>

ID e NOME del tipo attivita' da creare

=item B<DESCRIPTION>

Descrizione da associare alla nuova attivita'

=item B<DATE>

Data di creazione dell'attivita'; se omesso verra' utilizzato I<sysdate>

=item B<DATE_FORMAT>

Formato da utilizzare per l'interpretazione di B<DATE>; se omesso verra' utilizzato API::ART::Info('DEFAULT_DATE_FORMAT')

=item B<DEST_USER> I<scalar>

Nome dell'utente a cui deve essere assegnata l'attivita'

=item B<PROPERTIES> I<hashref>

Un I<hashref> delle proprieta' da associare all'apertura dell'attivita'

=item B<ATTACHMENTS> I<arrayref>

Un I<arryref> con i path (assoluti o relativi) dei file da allegare all'apertura dell'attivita'

=item B<ID_CUSTOM> I<ACTIVITY_ID>

Fornisce l'I<ACTIVITY_ID> con il quale verra' creata l'attivita'

=item B<CREATION_USER> I<LOGIN>

Fornisce la I<LOGIN> dell'operatore che deve essere impostato come creatore dell'attività. Parametro disponibile solo se API::ART: è istanziata da un operatore ADMIN

=back

=cut
sub create {
	my $self = shift;
	
	#my $savepoint_name = 'ACTIVITY_CREATE';
	
	#$self->{ART}->_dbh()->do( "savepoint $savepoint_name" );
	
	my $savepoint_name =
		sprintf("ACTIVITY_CREATE");
	
	# evita loop infiniti tramite bindings
	my $first_in_binding;
	unless (defined $self->art->in_binding_create_counter() ){
		$first_in_binding = 1;
		$self->art->in_binding_create_counter_init();
		
		# la transazione sul DB inizia con il primo step
		$self->art()->_dbh()->do( "savepoint $savepoint_name" );
	}
	
	my $out = $self->_create(@_);
	
	# il primo step di un'eventuale catena di binding
	# si occupa di rimuovere l'hash con il contatore
	# degli stati attraversati ed effettuare il rollback
	# del savepoint di tutta la transazione
	if ($first_in_binding){
		$self->art->in_binding_create_counter_reset();
		$self->art()->user()->su()->activity()->disable() if (defined $ENV{ART_BINDING_SUDO_GROUP} && ! defined $self->art->in_binding_status_counter());
		
		unless ( $out ){
			# in caso di errore rollback di tutta la transazione
			$self->art()->_dbh()->do( "rollback to savepoint $savepoint_name" )
		}
	}
	
	return $out
}

sub _create {
	my $self = shift;
	my %params = @_;
	local $@;
	
	$self->art()->clear_last_error();
	
	$self->art()->last_error("Missing SYSTEM_ID or SYSTEM parameters!")
		&& return undef
			unless ( defined($params{SYSTEM_ID}) || defined($params{SYSTEM}));
	
	$self->art()->last_error("Cannot use SYSTEM_ID and SYSTEM parameters!")
		&& return undef
			if (defined($params{SYSTEM_ID}) && defined($params{SYSTEM}));
	
	$self->art()->last_error("Bad SYSTEM_ID '$params{SYSTEM_ID}'!")
		&& return undef
			if ( defined $params{SYSTEM_ID} && $params{SYSTEM_ID} !~ /^\d+$/ );
	
	$self->art()->last_error("Bad SYSTEM: must be a HASH!")
		&& return undef
			if ( defined $params{SYSTEM} && ref($params{SYSTEM}) ne 'HASH' );
	
	if (defined $params{SYSTEM}){
		
		# Se SYSTEM->DATE_FORMAT non viene indicato eredita, se specificato, il formato di DATE_FORMAT
		$params{SYSTEM}->{DATE_FORMAT} = $params{DATE_FORMAT} unless defined $params{SYSTEM}->{DATE_FORMAT};

		my $system = $self->_get_collection_system->create(%{$params{SYSTEM}});
		$self->art()->last_error("Error in creating system: ".$self->art()->last_error())
			&& return undef 
				unless $system;
		
		$params{SYSTEM_ID} = $system->id(); 
	}
	
	$self->{SYSTEM} = API::ART::System->new( ART => $self->art(), ID => $params{SYSTEM_ID});
	return undef unless defined $self->{SYSTEM}; # last_error() viene impostato da API::ART::System ;-)
	
	# ACTIVITY_TYPE
	$self->art()->last_error("Concurrent missing of ACTIVITY_TYPE_ID and ACTIVITY_TYPE_NAME parameters!")
		&& return undef
			unless ( defined($params{ACTIVITY_TYPE_ID}) || defined($params{ACTIVITY_TYPE_NAME}) );
	$self->art()->last_error("Expectiong one of ACTIVITY_TYPE_ID and ACTIVITY_TYPE_NAME parameters!")
		&& return undef
			if ( defined($params{ACTIVITY_TYPE_ID}) && defined($params{ACTIVITY_TYPE_NAME}) );
	if ( defined $params{ACTIVITY_TYPE_ID} ) {
		$self->art()->last_error("ACTIVITY_TYPE_ID '$params{ACTIVITY_TYPE_ID}' undefined!")
			&& return undef
				unless $self->art()->test_activity_type_id($params{ACTIVITY_TYPE_ID});
		$params{ACTIVITY_TYPE_NAME} = $self->art()->get_activity_type_name($params{ACTIVITY_TYPE_ID});
	} else {
		$self->art()->last_error("ACTIVITY_TYPE_NAME '$params{ACTIVITY_TYPE_NAME}' undefined!")
			&& return undef
				unless $self->art()->test_activity_type_name($params{ACTIVITY_TYPE_NAME});
		$params{ACTIVITY_TYPE_ID} = $self->art()->get_activity_type_id($params{ACTIVITY_TYPE_NAME});
	}
	
	# DESCRIPTION
	$self->art()->last_error("Missing DESCRIPTION!")
		&& return undef
			unless defined $params{DESCRIPTION};
	
	# DATE_FORMAT
	$params{DATE_FORMAT} = $self->art()->info('DEFAULT_DATE_FORMAT') unless defined $params{DATE_FORMAT};
	# DATE
	$params{DATE} = $self->art()->_dbh()->get_sysdate($params{DATE_FORMAT}) unless defined $params{DATE};
	my $test_date = $self->art()->_dbh()->test_date($params{DATE}, $params{DATE_FORMAT});
	$self->art()->last_error("Invalid DATE '$params{DATE}': '$test_date'!") && return 0 if $test_date;
	
	##### Determinazione gruppo abilitato all'esecuzione dell'azione di apertura
	my ($group_id, $dest_status_id) = $self->art()->_test_permission(
		ACTIVITY_TYPE_ID	=> $params{ACTIVITY_TYPE_ID},
		STATUS_ID			=> $self->art()->get_activity_status_id('START'),
		ACTION_ID			=> $self->art()->get_activity_action_id($self->art()->get_activity_action_open_name()),
		GROUPS_ID			=> $self->art()->user()->su()->activity()->groups(),
		TARGET_SYSTEM_ID	=> $params{SYSTEM_ID},
	);
	$self->art()->last_error("User '".$self->art()->user()->name()."' not allowed for action '".$self->art()->get_activity_action_open_name()."'!" )
		&& return 0
			unless defined $group_id;
	# DEST_USER
	$self->art()->last_error("Unknown DEST_USER '$params{DEST_USER}'!")
		&& return 0
			if $params{DEST_USER} && !$self->art()->test_user_name($params{DEST_USER}=uc($params{DEST_USER}));
	##### Determino se l'operatore destinatario appartiene ad gruppo abilitato alla gestione dello stato destinazoione
	unless ( $self->art()->is_activity_final_status( ID => $dest_status_id) ) {
		$self->art()->last_error("DEST_USER '$params{DEST_USER}' not allowed to manage destination status '${\$self->art()->get_activity_status_name($dest_status_id)}'!")
			&& return 0
				if defined($params{DEST_USER}) &&
					!$self->art()->_test_permission(
						ACTIVITY_TYPE_ID => $params{ACTIVITY_TYPE_ID},
						STATUS_ID => $dest_status_id,
						GROUPS_ID => $self->art()->get_user_groups( $self->art()->get_user_id($params{DEST_USER}) )
				);
	}
	
	# PROPERTIES
	$params{PROPERTIES} = {} unless defined $params{PROPERTIES};
	$self->art()->last_error("PROPERTIES must be an hash-ref or undefined!") &&
		return undef
			unless ref($params{PROPERTIES}) eq 'HASH';
	
	# ATTACHMENTS
	$params{ATTACHMENTS} = [] unless defined $params{ATTACHMENTS};
	$self->art()->last_error("ATTACHMENTS must be an array-ref or undefined!")
		&& return undef
			unless ref($params{ATTACHMENTS}) eq 'ARRAY';
	
	# ID_CUSTOM
	$self->art()->last_error("Bad ID_CUSTOM '$params{ID_CUSTOM}'!")
		&& return undef
			if ( defined $params{ID_CUSTOM} && $params{ID_CUSTOM} !~ /^\d+$/ );
	
	# CREATION_USER
	$self->art()->last_error("CREATION_USER param available only for admin-users")
		&& return undef
			if ( defined $params{CREATION_USER} && !$self->art()->user()->is_admin() );

	# verifica se ID_CUSTOM e' abilitato
	if (defined $params{ID_CUSTOM}){

		unless (defined($self->{ID_CUSTOM_ENABLED})){
			$self->{ID_CUSTOM_ENABLED} = $self->{ART}->_dbh()->fetch_minimalized(
				"select 1 from user_objects where object_name = 'TRAB_FORCED' and object_type = 'PACKAGE'"
			);
		}

		unless ($self->{ID_CUSTOM_ENABLED}){
			$self->art()->last_error("ID_CUSTOM not enabled in this ART instance (check package TRAB_FORCED)");
			return undef;
		}

	}

	# Creazione handler per la gestione delle attivita (classe SIRTI::ART::Events)
	my $handler;
	eval {
		$handler = SIRTI::ART::Events->new(
			DB => $self->{ART}->_dbh(),
			ID_TIPO_SISTEMA => $self->{SYSTEM}->info('SYSTEM_TYPE_ID'),
			ID_TIPO_ATTIVITA => $params{ACTIVITY_TYPE_ID},
			ID_GRUPPO_ABILITATO => $group_id,
			LOOKUP_ART => $self->{ART}->_lookup(),
			SAVE_ALL_DTA => 1, # Forza la memorizzazione di tutti i DTA anche nel caso il nuovo valore non cambi
		);
	};
	$self->art()->last_error("Error creating activity-handler!\n" . $@)
		&& return undef
			unless defined $handler;
	
	# recupero eventuali behaviour sul tipo attivita
	my $behaviours = $self->art()->_dbh()->fetchall_hashref("
		select vtab.behaviour_label label -- per ora non viene usata
			, vtab.behaviour_class class
		from V_TIPI_ATTIVITA_BEHAVIOUR vtab
		where vtab.id_tipo_attivita = ".$params{ACTIVITY_TYPE_ID}."
		and vtab.event_label = 'on_create'
		and vtab.disable_date is null
		order by vtab.event_id
	");
	
	my $p = {
		id_operatore => defined $params{CREATION_USER} ? $self->art()->get_user_id($params{CREATION_USER}) : $self->{ART}->user->id(), #$user_id,
		id_operatore_corrente => (defined($params{DEST_USER}) ? $self->art()->get_user_id($params{DEST_USER}) : undef),
		id_sistema => $params{SYSTEM_ID},
		attivita_descrizione => $params{DESCRIPTION},
		data_esecuzione => $self->art()->format_date($params{DATE}, $params{DATE_FORMAT}),
		record => $params{PROPERTIES},
		allegati => $params{ATTACHMENTS},
		id_attivita_custom => $params{ID_CUSTOM},
		art => $self->art()
	};
	
	my @uuid = gettimeofday();
	my $uid = 'c'.$uuid[0].$uuid[1];
	
	# imposto savepoint
	$self->art()->_dbh()->do("SAVEPOINT ".$uid);
	
	my $behs_managed = [];
	# eseguo behaviour before se presente
	for my $b (@{$behaviours}){
		# Imposto il nome della classe che gestice il comportamento
		my $class = $b->{CLASS};
		
		CLASS: {
			local $@;
			eval "require $class;";
			if ($@){
				$self->art()->_dbh()->do("rollback to savepoint ".$uid);
				$self->art()->last_error("Unable to require class ($class): " .$@);
				return undef;
			}
		}
		
		if (my $method = $class->can('before')){
			my $ret = $method->($self->art(), $b, $p);
			unless (defined $ret){
				$self->art()->_dbh()->do("rollback to savepoint ".$uid);
				$self->art()->last_error("Unable to execute behaviour on_create: " .$self->art()->last_error());
				return undef;
			}
			push @{$behs_managed}, {
				LABEL => $b->{LABEL},
				CLASS => $b->{CLASS},
				BEFORE_RESPONSE => $ret
			};
		}
	}
	
			
	# Creazione attivita
	my $activity_id;
	eval {
		$activity_id = $handler->create_new_activity(%{$p});
	};
	$self->art()->last_error("Error creating new activity!\n" . $@)
		&& $self->art()->_dbh()->do("rollback to savepoint ".$uid)
		&& return undef
			unless defined $activity_id;
	
	# Verifica creazione
	if ( $activity_id =~ /^\d+$/ ) {
		# Determino quale classe utilizzare per la creazione dell'istanza basandomi sulla
		# proprieta' DEFAULT_CLASS_TO_CREATE della classe API::ART::Collection::Activity o
		# di una classe da essa derivata
		my $class = eval '$' . ref($self) . '::DEFAULT_CLASS_TO_CREATE';
		# Ritorno un riferimento all'oggetto che astrae l'attivita' appena creata
		my $activity = $class->new(
			ART => $self->art(),
			ID => $activity_id
		) || return;
		
		# invio la notifica per ogni gruppo di contatto se l'istanza supporta questa funzionalità
		if ($self->art()->notification_activity_support()){
			my $recipients = $activity->_get_groups_contacts_values();
			unless (defined $recipients){
				# rollback savepoint
				$self->art()->_dbh()->do("rollback to savepoint ".$uid);
				return undef;
			}

			unless ($activity->send_notification(CONFIGURED_RECIPIENTS => $recipients)){
				# rollback savepoint
				$self->art()->_dbh()->do("rollback to savepoint ".$uid);
				return undef;
			};
		}

		# gestisco l'aging se è presente la configurazione per il tipo_attivita
		if (defined $activity->info("AGING")->{CALENDAR_ID}){
			my $ret = API::ART::Activity::Aging::do($activity);
			unless (defined $ret){
				$self->art()->_dbh()->do("rollback to savepoint ".$uid);
				$self->art()->last_error("Unable to execute aging: " .$self->art()->last_error());
				return undef;
			}
		}
		
		
		# eseguo behaviour after se presente
		for my $b (@{$behs_managed}){
			# Imposto il nome della classe che gestice il comportamento
			my $class = $b->{CLASS};
			
			# la require l'ho già fatta prima
			if (my $method = $class->can('after')){
				my $ret = $method->($b,$b->{BEFORE_RESPONSE},$activity);
				unless (defined $ret){
					$self->art()->_dbh()->do("rollback to savepoint ".$uid);
					$self->art()->last_error("Unable to execute behaviour on_create: " .$self->art()->last_error());
					return undef;
				}
			}
		}
		
		# solo se sono attive le Binding richiama
		# l'ingresso nello stato iniziale
		if ($activity->isa("API::ART::Activity::Binding")){
			
			### Creata l'attivita' quindi mi merito il SUDO.
			### NB: se la create e' stata la prima azione aggiungo gruppo SUDO
			###		, se la create e' stata invocata da un precedente binding avevo gia' sudato e risudo
			if (defined $ENV{ART_BINDING_SUDO_GROUP}) {
				$self->art()->user()->su()->activity()->enable( $self->art()->get_group_id($ENV{ART_BINDING_SUDO_GROUP}) );
			}
		
			my $binding_status =
				API::ART::Activity::Binding::Status->new($activity);
			unless ($binding_status->on_enter_status(
					$activity->get_current_status_name
			)){
				$self->art()->_dbh()->do("rollback to savepoint ".$uid);
				return undef;
			}
		}
		
		return $activity;
		
	} else {
		$self->art()->last_error("Error creating new activity ($activity_id)");
		return undef;
	}
}

1;
