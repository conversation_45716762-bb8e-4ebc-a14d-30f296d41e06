package API::ART::Collection::Base;

############################################
#
# Foundation-class per le classi Collection
#
############################################

use strict;
use warnings;
use Carp;

use SIRTI::ART::Events;

use base qw(API::Ancestor);

our $VERSION = '0.01';


# Classe degli oggetti da generare col metodo ->create() 
our $DEFAULT_CLASS_TO_CREATE = undef; #### Da overloadare nel subclassamento!!!


#
# Metodi privati
#

# Esegue la ricerca vera e propria (metodo da overloadare nel subclassamento)
sub _find_id {
	my $self = shift;
	my %params = @_;
	return {};
}

# Ritorna il nome della classe che identifica il tipo di elemento gestito dalla Collection
# La proprieta' __SUB_CLASS__ ***DEVE*** essere overloadata nel subclassamento della Collection 
sub _sub_class { $_[0]->{__SUB_CLASS__} }



=head2 I<package>->B<new>( ART => I<API::ART> )

Il metodo B<new()> e' il costruttore di classe e richiede un solo argomento:

=over 4

=item B<ART>

Un'istanza della classe API::ART

=back

=cut
sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	local $@;
	#
	# ART
	#
	# Sintassi
	my $usage = sub {
		my $errmsg = shift;
		my $msg    = "";
		$msg .= "$class : $errmsg\n\n" if defined $errmsg;
		$msg .= "Usage:\n";
		$msg .= "\t$class";
		$msg .= '->new( ART => API::ART->new() )';
		$msg .= "\n\n";
		croak $msg;
	};

	# Controlli sui parametri
	$usage->('Missing ART!') unless ref($params->{ART}) eq 'API::ART';
	my $self  = $class->SUPER::new();
	$self->{ART} = $params->{ART};
	$self = bless( $self, $class );

	# Consente di non dover fare la use della classe DEFAULT_CLASS_TO_CREATE nella classe che subclassa
	$self->{__CLASS_FOR_FIND__} = eval '$' . ref($self) . '::DEFAULT_CLASS_TO_CREATE';
	eval "use $self->{__CLASS_FOR_FIND__}";
	$usage->(qq{Missing '$self->{__CLASS_FOR_FIND__}' !}."\n".$@) if $@;

	return $self;
}


=head2 I<object>->B<art>()

Ritorna il riferimento all'oggetto API::ART passato come argomento al costruttore.

=cut
sub art { return $_[0]->{ART} }


=head2 I<object>->B<create>()

# Creazione nuovo elemento della Collection

=cut
sub create {
	my $self = shift;
	my %params = @_;
	return undef;
}


=head2 I<object>->B<find_id>()

=head2 I<object>->B<find_object>()

I due metodi sono simili con l'unica differenza che B<find_id()> ritorna 
un I<arrayref> di id, mentre B<find_object()> ritorna un I<arrayref> di
riferimenti ad oggetti.

I parametri consentiti sono descritti dalle documentazioni dei relativi metodi delle classi API::ART::Collection::Activity e API::ART::System::Activity

=back

=cut
sub find_id {
	my $self = shift;
	return $self->_find_id(@_);
}
sub find_object {
	my $self = shift;
	my %params = @_;
	my $id = $self->_find_id(%params) || return undef;
	local $@;
	# Determino quale classe utilizzare per la creazione dell'istanza basandomi sulla 
	# proprieta' DEFAULT_CLASS_TO_CREATE overloadata nel subclassamento di API::ART::Collection::Base
	#my $class = eval '$' . ref($self) . '::DEFAULT_CLASS_TO_CREATE';
	if ($params{ONLY_COUNT}){
		return {
			COUNT => $id->{COUNT}
		}
	} elsif ($params{EXTENDED_OUTPUT}){
		return {
			COUNT => $id->{COUNT}
			,RESULTS => [ map { $self->{__CLASS_FOR_FIND__}->new( ART => $self->art() , ID => $_ ) } @{$id->{RESULTS}} ]
		}
	} else {
		return [ map { $self->{__CLASS_FOR_FIND__}->new( ART => $self->art() , ID => $_ ) } @$id ];
	}
	#return [ map { $self->_sub_class()->new( ART => $self->art() , ID => $_ ) } @$id ];
}

=head2 I<object>->B<find_exec>()

Questo metodo opera analogamente a B<find_id()> e B<find_object()> ma accetta in input
il parametro B<CALLBACK> che consente di eseguire un blocco di codice per ogni oggetto
trovato.

Se presente il parametro B<EXTENDED_OUTPUT> impostato a 1 ritorna un I<hashref> con la chiave B<COUNT> con il numero di record trovati a meno dell'applicazione dei filtri LIMIT e SKIP e la chiave
B<RESULTS> che rappresenta un I<arrayref> in cui ogni elemento rappresenta il B<return code> del codice 
di B<CALLBACK> che puo' essere qualsiasi tipo di dato atto a tracciare l'esecuzione del
blocco di codice per ogni sistema, in modo che il chiamante abbia la possibilita' di 
eseguire operazioni successive sulla base dell'esito dell'elaborazione.
Se il parametro B<EXTENDED_OUTPUT> non e' presente o e' impostato a 0 ritorna esclusivamente l'I<arrayref> sopra descritto.

=cut
sub find_exec {
	my $self = shift;
	my %params = @_;
	local $@;
	
	$self->art()->clear_last_error();
	
	# Controllo parametri
	$self->art()->last_error("Missing CALLBACK parameter!") 
		&& (return undef)
			unless defined $params{CALLBACK};
	$self->art()->last_error("Parameter CALLBACK must be a CODEREF!") 
		&& (return undef)
			unless ref $params{CALLBACK} eq 'CODE';
			
	#my $class = eval '$' . ref($self) . '::DEFAULT_CLASS_TO_CREATE';
	
	my $found = $self->find_id(%params);
	return undef unless defined $found;
	
	my $count;
	my $results;
	my $out;
	
	if ($params{EXTENDED_OUTPUT}){
		$count = $found->{COUNT};
		$results = $found->{RESULTS};
	} else {
		$results = $found;
	}
	
	my $tmp_out = eval {
		[ map {
				$params{CALLBACK}->(
					$self->{__CLASS_FOR_FIND__}->new( ART => $self->art() , ID => $_ )
					,$count
				);
			} @{$results}];
	};
	
	$self->art()->last_error("Error creating $self->{__CLASS_FOR_FIND__} object: $@") 
		&& (return undef)
			if $@;
	
	if ($params{EXTENDED_OUTPUT}){
		$out->{COUNT} = $count;
		$out->{RESULTS} = $tmp_out;
	} else {
		$out = $tmp_out;
	}
	
	return $out;
}


1;
