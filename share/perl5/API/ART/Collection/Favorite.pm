package API::ART::Collection::Favorite;

use strict;
use warnings;
use Carp;
use JSON;

use base qw(API::Ancestor);

use API::ART::Favorite;

our $VERSION = '0.01';

# Classe degli oggetti da generare col metodo ->create()
our $DEFAULT_CLASS_TO_CREATE = 'API::ART::Favorite';

=head1 NAME

B<API::ART::Collection::Favorite> - Gestione collezione preferiti

=head1 SYNOPSIS

	# Uses needed packages
	use API::ART;
	use API::ART::Collection::Favorite;

	# Create API::ART instance...
	my $art = API::ART->new(.......);
	# Create API::ART::Collection::Favorite instance...
	my $c = API::ART::Collection::Favorite->new(ART => $art);

	# Ritorna un ARRAYREF di oggetti API::ART::Favorite di tutte i favorite definiti in ART per l'utente
	$c->find();
	
=head1 DESCRIPTION

Questo package consente di lavorare con insiemi di favorite di ART attraverso un'interfaccia OO.

=head1 METHODS

Di seguito i metodi esposti dalla classe API::ART::Collection::Favorite.

=cut

#
# Metodi privati
#

# Esegue la ricerca di Favorite
sub find {

	my $self = shift;
	my %params = @_;
	
	$self->art()->clear_last_error();
	
	my $prepare = $self->art()->_create_prepare(__PACKAGE__.'__find', "
		select id
		from preferiti
		where id_operatore = ?
			and data_cancellazione is null
		order by descrizione
	");

	my @bind_values = (
		$self->art()->user()->id(),
	);

	my $search;
	eval{$search = $self->art()->_create_prepare(__PACKAGE__.'__find')->fetchall_hashref(@bind_values)};
	if(SIRTI::DB::iserror()) {
		$self->art()->last_error("Unable to select favorite: ".SIRTI::DB::get_errormessage());
		return undef;
	}

	my $favorites = [];

	my $class = eval '$' . ref($self) . '::DEFAULT_CLASS_TO_CREATE';
	for my $id (@{$search}) {
		my $favorite = $class->new(
			ART => $self->art(),
			ID => $id->{ID}
		) || return;

		push @{$favorites}, $favorite;
	}

	return $favorites;
}


=head2 I<package>->B<new>( ART => I<API::ART> )

Il metodo B<new()> e' il costruttore di classe e richiede un solo argomento:

=over 4

=item B<ART>

Un'istanza della classe API::ART

=back

=cut
sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self  = $class->SUPER::new($params);

	$self->{ART} = $params->{ART};
	
	$self = bless( $self, $class );
	return $self;
}


=head2 I<object>->B<art>()

Ritorna il riferimento all'oggetto API::ART passato come argomento al costruttore.

=cut

=head2 I<object>->B<find>()

Ritorna un I<arrayref> di riferimenti ad oggetti di tipo B<API::ART::Favorite>.

=cut

=head2 I<object>->B<create>( I<hash> )

Consente di creare una nuovo favorite sulla base delle informazioni passate nella chiamata al metodo stesso.

Se l'operazione ha successo, il metodo ritorna il riferimento ad oggetto di tipo API::ART::Favorite altrimenti
ritorna I<undef> ed imposta il messaggio di errore nella proprieta' B<last_error()> dell'oggetto API::ART
passato nel costruttore.

Di seguito i parametri del metodo B<create()>:

=over 4

=item B<DESCRIPTION>

Descrizione del favorite da inserire

=item B<URL>

Url del favorite (path relativo  all'applicazione)

=item B<ADDITIONAL_KEYS>

Un hashref con le chiavi da inserire (viene serializzato in JSON e inserito in tabella)

=back

=cut


sub create {
	my $self = shift;
	my $errmsg;
	my %params = @_;
	
	$self->art()->clear_last_error();

	$self->art()->last_error($errmsg)
		&& return undef
			unless	$self->check_named_params(
				ERRMSG 				=> \$errmsg
				,IGNORE_EXTRA_PARAMS 	=> 0
				,PARAMS 				=> \%params
				,MANDATORY => {
					DESCRIPTION => { isa => 'SCALAR' }
					,URL => { isa => 'SCALAR' }
				}
				,OPTIONAL => {
					ADDITIONAL_KEYS => { isa => 'HASH' }
				}
			);
	
	# verifico se per l'url attuale è già presente un favorite per l'utente
	my $find = $self->find();
	return undef unless defined $find;

	for my $book (@{$find}){
		if ($params{URL} eq $book->url()){
			$self->art()->last_error("Favorite already present for URL ".$params{URL});
			return undef;
		}
		if ($params{DESCRIPTION} eq $book->description()){
			$self->art()->last_error("Favorite already present with description ".$params{DESCRIPTION});
			return undef;
		}
	}

	my $favorite_id = $self->art()->_dbh()->fetch_minimalized("select seq_preferiti.nextval from dual");

	my $prepare = $self->art()->_create_prepare(__PACKAGE__.'__insert', "
		insert into PREFERITI (
			ID,
			ID_OPERATORE,
			DESCRIZIONE,
			URL,
			CHIAVI_AGGIUNTIVE
		) values (
			?,
			?,
			?,
			?,
			?
		)
	");

	my @bind_values = (
		$favorite_id,
		$self->art()->user()->id(),
		$params{DESCRIPTION},
		$params{URL},
		defined $params{ADDITIONAL_KEYS} ? to_json($params{ADDITIONAL_KEYS}): undef
	);

	my $savepoint_name =
		sprintf("FAVORITE_CREATE");
	
	# la transazione sul DB inizia con il primo step
	$self->art()->_dbh()->do( "savepoint $savepoint_name" );

	eval{$self->art()->_create_prepare(__PACKAGE__.'__insert')->execute(@bind_values)};
	if(SIRTI::DB::iserror()) {
		$self->art()->_dbh()->do( "rollback to savepoint $savepoint_name" );
		$self->art()->last_error("Unable to insert favorite: ".SIRTI::DB::get_errormessage());
		return undef;
	}

	my $class = eval '$' . ref($self) . '::DEFAULT_CLASS_TO_CREATE';
	# Ritorno un riferimento all'oggetto che astrae l'attivita' appena creata
	my $favorite = $class->new(
		ART => $self->art(),
		ID => $favorite_id
	);
	unless($favorite){
		$self->art()->_dbh()->do( "rollback to savepoint $savepoint_name" );
		return undef;
	}
	
	return $favorite;

}

=head2 I<object>->B<art>()

Ritorna il riferimento all'oggetto API::ART passato come argomento al costruttore.

=cut
sub art { return $_[0]->{ART} }

if (__FILE__ eq $0) {

	use API::ART;
	use Data::Dumper;

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			PASSWORD => 'pippo123',
			USER => 'root',
			DEBUG => 0
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $coll = API::ART::Collection::Favorite->new(ART => $art);
	
	my $f = $coll->create(
		URL => 'pippo.html1',
		DESCRIPTION => 'Pippo1',
	);
	
	if( defined $f) {
		print STDERR "id = ".$f->id()."\n";
		print STDERR "user_id = ".$f->user_id()."\n";
		print STDERR "description = ".$f->description()."\n";
		print STDERR "url = ".$f->url()."\n";
		print STDERR "creation_date = ".$f->creation_date()."\n";
		print STDERR "last_update_date = ".$f->last_update_date()."\n";
		print STDERR "cancel_date = ".$f->cancel_date()."\n"
			if defined $f->cancel_date();
		print STDERR "additional_keys = ".Dumper($f->additional_keys())."\n";

	} else {
		print STDERR $art->last_error();
		die;
	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
