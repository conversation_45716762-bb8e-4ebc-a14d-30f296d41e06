package API::ART::Collection::System;

use strict;
use warnings;
use Carp;

use SIRTI::ART::Events;

use base qw(API::ART::Collection::Base);

our $VERSION = '0.01';


# Classe degli oggetti da generare col metodo ->create() 
our $DEFAULT_CLASS_TO_CREATE = 'API::ART::System';


=head1 NAME

B<API::ART::Collection::System> - Gestione collezione sistemi

=head1 SYNOPSIS

	# Uses needed packages
	use API::ART;
	use API::ART::Collection::System;

	# Create API::ART instance...
	my $art = API::ART->new(.......);
	# Create API::ART::Collection::System instance...
	my $c = API::ART::Collection::System->new(ART => $art);

	# Ritorna un ARRAYREF con gli ID di tutti i sistemi definiti in ART
	$c->find_id();
	 
	# Ritorna un ARRAYREF con l'ID del sistema denominato 'PLUTO'
	$c->find_id( EQUAL => 'PLUTO' );
	 
	# Ritorna un ARRAYREF di oggetti API::ART::System di 
	# tutti i sistemi il cui nome inizia con la parola 'PIPP' ed e'
	# seguito da un qualsiasi carattere arbitrario
	$c->find_object( LIKE => 'PIPP_' );
	 
	# Ritorna un ARRAYREF con gli ID di tutti i sistemi di tipo 'disney' e 'warner_bros'
	$c->find_id( SYSTEM_TYPE_NAME => [ 'disney' , 'warner_bros' ], SORT => [ {SYSTEM_TYPE_NAME=> 1}, {PROPERTY => {SUPERVISOR: 1}}] ); 

	# Ritorna un ARRAYREF di oggetti API::ART::System di 
	# tutti i sistemi NON ATTIVI il cui nome inizia con 'PIPPO'
	$c->find_object( LIKE => 'PIPPO%' , ACTIVE => 0 );
	
	# Esegue il blocco di codice CALLBACK per ogni sistema di tipo 'disney'
	$c->find_exec(
		SYSTEM_TYPE_NAME => [ 'disney' ],
		CALLBACK => sub () {
			my $s = shift; # Riferimento ad un oggetto API::ART::System
			# azioni da compiere
			return $rc; # Serve a popolare l'arrayref ritornato da find_exec()
		}
	);
	
	
=head1 DESCRIPTION

Questo package consente di lavorare con insiemi di sistemi di ART attraverso un'interfaccia OO.

=head1 METHODS

Di seguito i metodi esposti dalla classe API::ART::System.

=cut


#
# Metodi privati
#

# Esegue la ricerca di sistemi e ritorna un ARRAYREF di SYSTEM_ID
sub _find_id {
	my $self = shift;
	my %params = @_;
	#
	# @ID_IN
	# $ID_EQUAL
	# $ID_LIKE
	# @SYSTEM_TYPE_ID || @SYSTEM_TYPE_NAME
	# @SYSTEM_TYPE_NAME_IN
	# $SYSTEM_TYPE_NAME_EQUAL
	# $SYSTEM_TYPE_NAME_LIKE
	# $ACTIVE => 0 || 1
	# $SUPENDED => 0 || 1
	# $EQUAL || $LIKE
	# $CASE_SENSITIVE => 0 || [1]
	# @DESCRIPTION_IN
	# $DESCRIPTION_EQUAL
	# $DESCRIPTION_LIKE
	# %PROPERTIES => { NAME => VALUE, ... }
	# %PROPERTIES_IS_NULL
	# %PROPERTIES_IS_NOT_NULL
	# $PROPERTIES_IN
	# $PROPERTIES_EQUAL
	# $PROPERTIES_NOT_EQUAL
	# $PROPERTIES_LIKE
	# @SORT
	# $EXTENDED_OUTPUT
	# $SHOW_ONLY_WITH_VISIBILITY => [0] || 1
	# $SHOW_NO_SUDO => [0] || 1
	#
	
	# Controllo parametri
	
	$self->art()->clear_last_error();
	
	# SYSTEM_TYPE_NAME and SYSTEM_TYPE_ID
	$self->art()->last_error("Expecting only one between SYSTEM_TYPE_NAME and SYSTEM_TYPE_ID parameters!") 
		&& return undef
			if ( defined($params{SYSTEM_TYPE_ID}) && defined($params{SYSTEM_TYPE_NAME}) );
	
	for (
			'SYSTEM_TYPE_NAME',
			'SYSTEM_TYPE_ID',
			'ID_IN',
			'DESCRIPTION_IN',
			'SYSTEM_TYPE_NAME_IN',
		) {
			$self->art()->last_error($_." must be an ARRAYREF (of SCALAR)!")
				&& (return undef)
					unless !defined($params{$_}) || ref $params{$_} eq 'ARRAY';
		}
		
	my $type = [];
	if ( defined $params{SYSTEM_TYPE_ID} ) {
		foreach my $id (@{$params{SYSTEM_TYPE_ID}}) {
			$self->art()->last_error("SYSTEM_TYPE_ID '$id' not valid!") 
				&& return undef
					unless ( $self->art()->test_system_type_id($id) );
		}
		$type = $params{SYSTEM_TYPE_ID};
	} elsif ( defined $params{SYSTEM_TYPE_NAME} ) {
		foreach my $name (@{$params{SYSTEM_TYPE_NAME}}) {
			$self->art()->last_error("SYSTEM_TYPE_NAME '$name' not valid!") 
				&& return undef
					unless ( $self->art()->test_system_type_name($name) );
			push @$type, $self->art()->get_system_type_id($name);
		}
	}
	
	# ACTIVE
	if ( defined $params{ACTIVE} && ! grep /^$params{ACTIVE}$/, qw(0 1) ) {
		$self->art()->last_error("ACTIVE must be 0 or 1!"); 
		return undef;
	}
	
	# SUSPENDED
	if ( defined $params{SUSPENDED} && ! grep /^$params{SUSPENDED}$/, qw(0 1) ) {
		$self->art()->last_error("SUSPENDED must be 0 or 1!"); 
		return undef;
	}
	
	# CASE_SENSITIVE
	$params{CASE_SENSITIVE} = 1 unless defined $params{CASE_SENSITIVE};
	$self->art()->last_error("CASE_SENSITIVE must be 0 or 1!") 
		&& (return undef)
			unless grep /^$params{CASE_SENSITIVE}$/, qw(0 1);
	
	# SHOW_ONLY_WITH_VISIBILITY
	$params{SHOW_ONLY_WITH_VISIBILITY} = 0 unless defined $params{SHOW_ONLY_WITH_VISIBILITY};
	$self->art()->last_error("SHOW_ONLY_WITH_VISIBILITY must be 0 or 1!") 
		&& (return undef)
			unless grep /^$params{SHOW_ONLY_WITH_VISIBILITY}$/, qw(0 1);
	
	# SHOW_NO_SUDO
	$params{SHOW_NO_SUDO} = 0 unless defined $params{SHOW_NO_SUDO};
	$self->art()->last_error("SHOW_NO_SUDO must be 0 or 1!") 
		&& (return undef)
			unless grep /^$params{SHOW_NO_SUDO}$/, qw(0 1);
	
	$self->art()->last_error("Param SHOW_ONLY_WITH_VISIBILITY and SHOW_NO_SUDO are mutally exclusive")
		&& (return undef)
			if ($params{SHOW_ONLY_WITH_VISIBILITY} && $params{SHOW_NO_SUDO});

	for ('ID_EQUAL','ID_LIKE', 'DESCRIPTION_EQUAL', 'DESCRIPTION_LIKE', 'STATUS_EQUAL', 'STATUS_LIKE'){
		$self->art()->last_error("Param ".$_." must be a STRING!")
			&& (return undef)
				if ref ($params{$_});
	}

	for (
		'ID_EQUAL'
	){
			$self->art()->last_error("Param ".$_." must be a positive integer")
					&& (return undef)
							if defined $params{$_} && $params{$_} !~/^\d+$/;
	}

	for (
			'ID_LIKE'
	){
			if (defined $params{$_}){
					my $check_ripulito = $params{$_};
					$check_ripulito =~s/%//g;
					$self->art()->last_error("Param ".$_." must be a positive integer")
							&& (return undef)
									if $check_ripulito !~/^\d+$/;
			}
	}

	if (defined $params{ID_IN}){
			for my $id_in (@{$params{ID_IN}}){
					$self->art()->last_error("Param ID_IN must contains positive integers")
							&& (return undef)
									if $id_in !~/^\d+$/;
			}
	}


	
	$self->art()->last_error("Expecting only one between EQUAL and DESCRIPTION_EQUAL parameters!")
		&& (return undef)
			if $params{EQUAL} && $params{DESCRIPTION_EQUAL};
	$self->art()->last_error("Expecting only one between LIKE and DESCRIPTION_LIKE parameters!")
		&& (return undef)
			if $params{LIKE} && $params{DESCRIPTION_LIKE};
	
	for (
			'PROPERTIES'
			, 'PROPERTIES_EQUAL'
			, 'PROPERTIES_NOT_EQUAL'
			, 'PROPERTIES_IN' 
			, 'PROPERTIES_LIKE'
			, 'PROPERTIES_IS_NULL'
			, 'PROPERTIES_IS_NOT_NULL'
		) {
			$self->art()->last_error($_." must be an HASHREF!")
				&& (return undef)
					unless !defined($params{$_}) || ref $params{$_} eq 'HASH';
		}
	
	for my $t (
		'PROPERTIES_IS_NULL'
		,'PROPERTIES_IS_NOT_NULL'
	) {
		if (defined $params{$t}){
			for my $k (keys %{$params{$t}}){
				$self->art()->last_error("$t of $k must be equal to 1!")
					&& (return undef)
						if $params{$t}->{$k} != 1;
			}
		}
	}
	
	# Trasformo l'eventuale PROPERTIES (deprecata) in PROPERTIES_EQUAL; in caso di sovrapposizione di property utilizzo quella già presente in PROPERTIES_EQUAL 
	if (defined $params{PROPERTIES}) {
		$params{PROPERTIES_EQUAL} = {} unless defined $params{PROPERTIES_EQUAL};
		for my $sp (keys %{$params{PROPERTIES}}) {
			unless (exists $params{PROPERTIES_EQUAL}->{$sp}) {
				$params{PROPERTIES_EQUAL}->{$sp} = $params{PROPERTIES}->{$sp};
			}
		}
		delete $params{PROPERTIES};
	}

	if (defined $params{PROPERTIES_IN}){
		for my $key (keys %{$params{PROPERTIES_IN}}){
			$self->art()->last_error("PROPERTIES_IN must be an HASHREF of ARRAY!")
				&& return undef
					 if ref ($params{PROPERTIES_IN}->{$key}) ne 'ARRAY';
		}
	}
		
	# Trasformo l'eventuale SYSTEM_STYPE_NAME (deprecata) in SYSTEM_TYPE_NAME_IN mergiando i due array
	if (defined $params{SYSTEM_TYPE_NAME}) {
		$params{SYSTEM_TYPE_NAME_IN} = [] unless defined $params{SYSTEM_TYPE_NAME_IN};
		for my $st (@{$params{SYSTEM_TYPE_NAME}}) {
			push @{$params{SYSTEM_TYPE_NAME_IN}}, $st;
		}
		delete $params{SYSTEM_TYPE_NAME};
	}
	
	$self->art()->last_error("SYSTEM_TYPE_ID not allowed with SYSTEM_TYPE_NAME_IN|SYSTEM_TYPE_NAME_EQUAL|SYSTEM_TYPE_NAME_LIKE")
		&& (return undef)
			if (
				(
					defined $params{SYSTEM_TYPE_ID}
				)
				&&
				(
					defined $params{SYSTEM_TYPE_NAME_IN}
					||
					defined $params{SYSTEM_TYPE_NAME_EQUAL}
					||
					defined $params{SYSTEM_TYPE_NAME_LIKE}
				)
			);
	
	
	# EQUAL and LIKE
	$params{DESCRIPTION_LIKE} = $params{LIKE} if defined $params{LIKE};
	$params{DESCRIPTION_EQUAL} = $params{EQUAL} if defined $params{EQUAL};
	$params{DESCRIPTION_IN} = [] unless defined $params{DESCRIPTION_IN};
	$params{ID_LIKE} = '' unless defined $params{ID_LIKE};
	$params{ID_EQUAL} = '' unless defined $params{ID_EQUAL};
	$params{ID_IN} = [] unless defined $params{ID_EQUAL};
	$params{SYSTEM_TYPE_NAME_LIKE} = '' unless defined $params{SYSTEM_TYPE_NAME_LIKE};
	$params{SYSTEM_TYPE_NAME_EQUAL} = '' unless defined $params{SYSTEM_TYPE_NAME_EQUAL};
	$params{SYSTEM_TYPE_NAME_IN} = [] unless defined $params{SYSTEM_TYPE_NAME_IN};
	
	$self->art()->last_error("Expecting only one between EQUAL and LIKE parameters!") 
		&& (return undef)
			if $params{LIKE} && $params{EQUAL};
	
	$self->art()->last_error("LIMIT must be a INTEGER!")
		&& (return undef)
			if (defined $params{LIMIT} && ( ref $params{LIMIT} ne '' || $params{LIMIT} !~/^\d+$/));
	
	$self->art()->last_error("SKIP must be a INTEGER!")
		&& (return undef)
			if (defined $params{SKIP} && ( ref $params{SKIP} ne '' || $params{SKIP} !~/^\d+$/));
	
	$self->art()->last_error("SORT must be an ARRAY!")
		&& (return undef)
			unless !defined($params{SORT}) || ref $params{SORT} eq 'ARRAY';
	
	my $fields = ', 1 "DUMMY"';
	my $join_table = '';
	my @used_join = ();
	my @used_fields;
	
	for (
			'SYSTEM_TYPE_NAME_EQUAL'
			,'SYSTEM_TYPE_NAME_IN'
			,'SYSTEM_TYPE_NAME_LIKE'
		){
		if ( 
			(ref $params{$_} && ref $params{$_} eq 'ARRAY' && scalar @{$params{$_}})
			||
			(!ref $params{$_} && $params{$_})
		){
			unless (grep {$_ eq 'TIPI_SISTEMA' } @used_join){
				$join_table.=" join tipi_sistema ts on ts.id_tipo_sistema = s.id_tipo_sistema";
				push @used_join, 'TIPI_SISTEMA';
			}
		}
	}
	
	my $orderBy= '';
	
	my @sort_keys = (
		'SYSTEM_TYPE_ID',
		'SYSTEM_TYPE_NAME',
		'SYSTEM_ID',
		'CREATION_DATE',
		'DISABLING_DATE',
		'ENDING_DATE',
		'SYSTEM_NAME',
		'PROPERTY'
	);
	
	my @properties_keys = ();
	my @properties_keys_nulls = ();
	my @properties_keys_not_nulls = ();
	my @properties_keys_equal = ();
	my @properties_keys_not_equal = ();
	my @properties_keys_in = ();
	my @properties_keys_like = ();
	
	if (defined $params{PROPERTIES}){
		@properties_keys = keys %{$params{PROPERTIES}};
	}
	@properties_keys_nulls = keys %{$params{PROPERTIES_IS_NULL}} if exists $params{PROPERTIES_IS_NULL};
	@properties_keys_not_nulls = keys %{$params{PROPERTIES_IS_NOT_NULL}} if exists $params{PROPERTIES_IS_NOT_NULL};
	@properties_keys_equal = keys %{$params{PROPERTIES_EQUAL}} if exists $params{PROPERTIES_EQUAL};
	@properties_keys_not_equal = keys %{$params{PROPERTIES_NOT_EQUAL}} if exists $params{PROPERTIES_NOT_EQUAL};
	@properties_keys_in = keys %{$params{PROPERTIES_IN}} if exists $params{PROPERTIES_IN};
	@properties_keys_like = keys %{$params{PROPERTIES_LIKE}} if exists $params{PROPERTIES_LIKE};
	
	# se non è definito un ordinamento imposto come default l'id_sistema
	$params{SORT} = [] unless defined $params{SORT};
	# aggiungo solo se non esiste già
	my @sortKeys;
	for my $k (@{$params{SORT}}){
		push @sortKeys, keys %{$k};
	}
	push @{$params{SORT}}, {SYSTEM_ID => 1} unless grep {$_ eq 'SYSTEM_ID'} @sortKeys;
	
	my @used_sort_fields = ();
	my @used_sort_fields_property = (); 
	
	for my $sort_index (@{$params{SORT}}){
		$self->art()->last_error("SORT field must be an HASH!")
			&& (return undef)
				if ref $sort_index ne 'HASH';
		
		$self->art()->last_error("Every SORT field must contain one key!")
			&& return undef
				if scalar keys %{$sort_index} != 1;
		
		my @key = keys %{$sort_index};
		
		$self->art()->last_error("Cannot sort by ".$key[0])
			&& (return undef)
				unless grep { $_ eq $key[0] } @sort_keys;
		
		my @val = values %{$sort_index};
		
		my $index_sort;
		my $key_order;
		if ($key[0] ne 'PROPERTY'){
			$self->art()->last_error("Sort field ".$key[0]." duplicate!")
				&& (return undef)
					if grep {$_ eq $key[0]} @used_sort_fields;
			
			push @used_sort_fields, $key[0];
			
			$key_order = $key[0];
			$index_sort = $val[0];
			if ($key[0] =~/^(SYSTEM_TYPE_ID|SYSTEM_TYPE_NAME)$/){
				if ($key[0] eq 'SYSTEM_TYPE_ID'){
					$fields .= ", s.id_tipo_sistema ".$key[0];
				} elsif ($key[0] eq 'SYSTEM_TYPE_NAME'){
					$fields .= ", ts.nome_tipo_sistema ".$key[0];
					unless (grep {$_ eq 'TIPI_SISTEMA'} @used_join){
						$join_table.=" join tipi_sistema ts on ts.id_tipo_sistema = s.id_tipo_sistema";
						push @used_join, 'TIPI_SISTEMA';
					}
				}
			} elsif ($key[0] =~/^(CREATION_DATE)$/){
				$fields .= ", s.data_inizio_osservazione ".$key[0];
			} elsif ($key[0] =~/^(DISABLING_DATE)$/){
				$fields .= ", s.data_sospensione ".$key[0];
			} elsif ($key[0] =~/^(ENDING_DATE)$/){
				$fields .= ", s.data_dismissione ".$key[0];
			} elsif ($key[0] =~/^(SYSTEM_NAME)$/){
				$fields .= ", s.descrizione ".$key[0];
			}
			
		} else {
			$self->art()->last_error("SORT field must be an HASH!")
				&& (return undef)
					if ref $val[0] ne 'HASH';
			
			$self->art()->last_error("Every SORT field must contain one key!")
				&& return undef
					if scalar keys %{$val[0]} != 1;
			
			my @key_props = keys %{$val[0]};
			my @val_props = values %{$val[0]};
			
			$self->art()->last_error("Sort field PROPERTIES ".$key_props[0]." duplicate!")
				&& (return undef)
					if grep {$_ eq $key_props[0]} @used_sort_fields_property;
			
			push @used_sort_fields_property, $key_props[0];
			
			$self->art()->last_error("PROPERTY ".$key_props[0]. " not found!")
				&& return undef
					unless $self->art()->test_system_property_name($key_props[0]);
			
			
			$key_order = '"'.$self->art()->get_system_property_id($key_props[0]).'"';
			$index_sort = $val_props[0];
			
			my $search_property=0;
			for my $pk ('PROPERTIES', 'PROPERTIES_EQUAL', 'PROPERTIES_IN', 'PROPERTIES_LIKE', 'PROPERTIES_IS_NULL', 'PROPERTIES_IS_NOT_NULL', 'PROPERTIES_NOT_EQUAL'){
				if (
					defined $params{$pk}
					&&
					(grep {$_ eq $key_props[0] } keys %{$params{$pk}} )
				){
					$search_property = 1;
					last;
				}
			}

			my $tipo_tdts = $self->art()->_lookup()->tipo_tdts_nome($key_props[0]);
			
			unless ($search_property){
				if ($self->art()->is_system_property_name_multiplex($key_props[0])){
					unless (grep {$_ eq 'DATI_TECNICI_'.$self->art()->get_system_property_id($key_props[0])} @used_join){
						$join_table.=" join dati_tecnici dtmult".$self->art()->get_system_property_id($key_props[0])." on s.id_sistema = dtmult".$self->art()->get_system_property_id($key_props[0]).".id_sistema and dtmult".$self->art()->get_system_property_id($key_props[0]).".id_tipo_dato_tecnico = ".$self->art()->get_system_property_id($key_props[0]);
						push @used_join, 'DATI_TECNICI_'.$self->art()->get_system_property_id($key_props[0]);
					}
					unless (grep {$_ eq $self->art()->get_system_property_id($key_props[0])} @used_fields){
						if ($tipo_tdts =~/^(INTEGER|LONG|FLOAT)$/){
							$fields.= "
								, to_number_if_valid(dtmult".$self->art()->get_system_property_id($key_props[0]).".descrizione) \"".$self->art()->get_system_property_id($key_props[0])."\" --$key_props[0]
							";
						} else {
							$fields.= "
								, dtmult".$self->art()->get_system_property_id($key_props[0]).".descrizione \"".$self->art()->get_system_property_id($key_props[0])."\" --$key_props[0]
							";
						}
						push @used_fields, $self->art()->get_system_property_id($key_props[0]);
					}
				} else {
					# Costruisce blocco per estrarre la property
					unless (grep {$_ eq $self->art()->get_system_property_id($key_props[0])} @used_fields){
						if ($tipo_tdts =~/^(INTEGER|LONG|FLOAT)$/){
							$fields .= "
								,to_number_if_valid((SELECT dt.descrizione
									FROM   dati_tecnici dt
									WHERE  dt.id_tipo_dato_tecnico = ".$self->art()->get_system_property_id($key_props[0])."
										AND dt.id_sistema = s.id_sistema
										and rownum<2
								)) \"".$self->art()->get_system_property_id($key_props[0]) ."\" --".$key_props[0]."
							";
						} else {
							$fields .= "
								,(SELECT dt.descrizione
									FROM   dati_tecnici dt
									WHERE  dt.id_tipo_dato_tecnico = ".$self->art()->get_system_property_id($key_props[0])."
										AND dt.id_sistema = s.id_sistema
										and rownum<2
								) \"".$self->art()->get_system_property_id($key_props[0]) ."\" --".$key_props[0]."
							";
						}
						push @used_fields, $self->art()->get_system_property_id($key_props[0]);
					}
				}
			}
		}
		
		$self->art()->last_error("SORT order can be only 1 or -1!")
			&& return undef
				if (!defined $index_sort || $index_sort !~/^(1|-1)$/);
		
		$orderBy.= ($orderBy ? ', ' : '' ). $key_order.' '. ($index_sort eq '1' ? 'ASC': 'DESC' );
		
	}
	
	$orderBy = ($orderBy ne '' ? ' order by ' : '') . $orderBy;
	
	my $skip_limit='';
	
	if (defined $params{LIMIT} && defined $params{SKIP}){
		$skip_limit = "OFFSET ".$params{SKIP}." ROWS FETCH NEXT ".$params{LIMIT}." ROWS ONLY";
	} elsif (defined $params{LIMIT}){
		$skip_limit = "OFFSET 0 ROWS FETCH NEXT ".$params{LIMIT}." ROWS ONLY";
	} elsif (defined $params{SKIP}){
		$skip_limit = "OFFSET ".$params{SKIP}." ROWS";
	}
	
	my $count_join_tipi_Sistema = '';

	# Condizioni WHERE
	my @where = ();
	if ( scalar @$type ) {
		push @where, 's.id_tipo_sistema in (' . join(',', @$type) . ')';
		$count_join_tipi_Sistema = 'join sistemi scount on scount.id_Sistema = dt.id_Sistema and scount.id_tipo_sistema in (' . join(',', @$type) . ')';
	} elsif ( $params{SYSTEM_TYPE_NAME_EQUAL} || $params{SYSTEM_TYPE_NAME_IN} || $params{SYSTEM_TYPE_NAME_LIKE}){
		if ( $params{SYSTEM_TYPE_NAME_EQUAL} ) {
			if ($params{CASE_SENSITIVE}){
				push @where, "ts.nome_tipo_sistema = ${\$self->art()->_dbh()->quote($params{SYSTEM_TYPE_NAME_EQUAL})}" ;
				$count_join_tipi_Sistema = 'join sistemi scount on scount.id_Sistema = dt.id_Sistema and scount.id_tipo_sistema = '.$self->art()->get_system_type_id($params{SYSTEM_TYPE_NAME_EQUAL});
			} else {
				push @where, "ts.nome_tipo_sistema = upper( ${\$self->art()->_dbh()->quote($params{SYSTEM_TYPE_NAME_EQUAL})} )";
				$count_join_tipi_Sistema = 'join sistemi scount on scount.id_Sistema = dt.id_Sistema and scount.id_tipo_sistema = '.$self->art()->get_system_type_id(uc($params{SYSTEM_TYPE_NAME_EQUAL}));
			}
			
		}
		if ( $params{SYSTEM_TYPE_NAME_LIKE} ) {
			if ($params{CASE_SENSITIVE}){
				push @where, "ts.nome_tipo_sistema LIKE ${\$self->art()->_dbh()->quote($params{SYSTEM_TYPE_NAME_LIKE})}";
				$count_join_tipi_Sistema = "
					join sistemi scount on scount.id_Sistema = dt.id_Sistema
					join tipi_sistema tscount on tscount.id_tipo_sistema = scount.id_tipo_sistema and tscount.nome_tipo_sistema LIKE ${\$self->art()->_dbh()->quote($params{SYSTEM_TYPE_NAME_LIKE})}
				";
			} else {
				push @where, "ts.nome_tipo_sistema LIKE upper( ${\$self->art()->_dbh()->quote($params{SYSTEM_TYPE_NAME_LIKE})} )";
				$count_join_tipi_Sistema = "
					join sistemi scount on scount.id_Sistema = dt.id_Sistema
					join tipi_sistema tscount on tscount.id_tipo_sistema = scount.id_tipo_sistema and tscount.nome_tipo_sistema LIKE upper(${\$self->art()->_dbh()->quote($params{SYSTEM_TYPE_NAME_LIKE})})
				";
			}
			
		}
		if ( scalar(@{$params{SYSTEM_TYPE_NAME_IN}}) ) {
			if ($params{CASE_SENSITIVE}){
				push @where, "ts.nome_tipo_sistema in (" . join(',', map { $self->art()->_dbh()->quote($_) } @{$params{SYSTEM_TYPE_NAME_IN}}) . ')';
				$count_join_tipi_Sistema = 'join sistemi scount on scount.id_Sistema = dt.id_Sistema and scount.id_tipo_sistema in ('.join(',', map { $self->art()->get_system_type_id($_) } @{$params{SYSTEM_TYPE_NAME_IN}} ).')';
			} else {
				push @where, "ts.nome_tipo_sistema in (" . join(',', map { "upper(".$self->art()->_dbh()->quote($_).")" } @{$params{SYSTEM_TYPE_NAME_IN}}) . ')';
				$count_join_tipi_Sistema = 'join sistemi scount on scount.id_Sistema = dt.id_Sistema and scount.id_tipo_sistema in ('.join(',', map { $self->art()->get_system_type_id(uc($_)) } @{$params{SYSTEM_TYPE_NAME_IN}} ).')';
			}
			
		}
	}
	
	if (defined $params{ACTIVE}){
		if ( $params{ACTIVE} ) {
			push @where, 's.data_dismissione IS NULL'; 
			push @where, 's.data_sospensione IS NULL';		
		}
		else {
			push @where, '(s.data_dismissione IS NOT NULL OR s.data_sospensione IS NOT NULL)';		
		}
	}
	
	if (defined $params{SUSPENDED}){ 
		push @where, 's.data_sospensione IS ' . ( $params{SUSPENDED} ? 'NOT' : '' ) . ' NULL';		
	}
	 
	if ($params{SHOW_ONLY_WITH_VISIBILITY}){ 
		push @where, 'exists (
			select 1
			from permission_sistemi   ps
			where ps.id_sistema = s.id_sistema
			AND ps.id_gruppo_abilitato IN (
				' . join( ',', @{ $self->{ART}->user()->su()->activity()->groups() }) . '
			)
			and rownum<2
		)';
	}

	if ($params{SHOW_NO_SUDO}){ 
		push @where, 'exists (
			select 1
			from permission_sistemi   ps
			where ps.id_sistema = s.id_sistema
			AND ps.id_gruppo_abilitato IN (
				' . join( ',', @{ $self->{ART}->user()->groups() }) . '
			)
			and rownum<2
		)';
	}
	 
	if ( $params{DESCRIPTION_EQUAL} ) {
		push @where, "s.descrizione = ${\$self->art()->_dbh()->quote($params{DESCRIPTION_EQUAL})}" if $params{CASE_SENSITIVE};
		push @where, "upper(s.descrizione) = upper( ${\$self->art()->_dbh()->quote($params{DESCRIPTION_EQUAL})} )" unless $params{CASE_SENSITIVE};
	}
	if ( $params{DESCRIPTION_LIKE} ) {
		push @where, "s.descrizione LIKE ${\$self->art()->_dbh()->quote($params{DESCRIPTION_LIKE})}" if $params{CASE_SENSITIVE};
		push @where, "upper(s.descrizione) LIKE upper( ${\$self->art()->_dbh()->quote($params{DESCRIPTION_LIKE})} )" unless $params{CASE_SENSITIVE};
	}
	if ( scalar(@{$params{DESCRIPTION_IN}}) ) {
		push @where, "s.descrizione in (" . join(',', map { $self->art()->_dbh()->quote($_) } @{$params{DESCRIPTION_IN}}) . ')' if $params{CASE_SENSITIVE};
		push @where, "upper(s.descrizione) in upper(" . join(',', map { $self->art()->_dbh()->quote($_) } @{$params{DESCRIPTION_IN}}) . ')' unless $params{CASE_SENSITIVE};
	}
	
	if ( $params{ID_LIKE} ) {
		push @where, 's.id_sistema like '.$self->art()->_dbh()->quote($params{ID_LIKE});
	}
	if ( $params{ID_EQUAL} ) {
		push @where, 's.id_sistema = '.$self->art()->_dbh()->quote($params{ID_EQUAL});
	}
	if ( $params{ID_IN} ) {
		for (@{$params{ID_IN}}){
			$_ = $self->art()->_dbh()->quote($_);
		}
		push @where, 's.id_sistema in (' . join(",", @{$params{ID_IN}}) . ')';
	}
	
	my $fields_filter = 'DUMMY = 1';

	my $where_filter;
	for my $index_properties (
		{type => 'PROPERTIES', list => [@properties_keys]}
		,{type => 'PROPS_NULL', list => [@properties_keys_nulls]}
		,{type => 'PROPS_NOT_NULL', list => [@properties_keys_not_nulls]}
		,{type => 'PROPERTIES_EQUAL', list => [@properties_keys_equal]}
		,{type => 'PROPERTIES_NOT_EQUAL', list => [@properties_keys_not_equal]}
		,{type => 'PROPERTIES_IN', list => [@properties_keys_in]}
		,{type => 'PROPERTIES_LIKE', list => [@properties_keys_like]}
	) {
		my @list_properties_keys = @{$index_properties->{list}};
		if ( scalar @list_properties_keys  > 0 ) {
			my $i = 0;
			my $rownum_threshold = 1000;	# Soglia minima record
			my $max_properties = 1e20;		# Soglia minima record
			my $property_criteria = undef;	# Campo da usare come filtro sugli id_attivita
			foreach my $key (@list_properties_keys) {
				
				my $min_rows = $max_properties < $rownum_threshold ? $max_properties : $rownum_threshold;
				my $confronto_desc_dt = undef;
				
				if ($index_properties->{type} =~/^(PROPERTIES|PROPERTIES_EQUAL)$/){
					$confronto_desc_dt = "
						select count ('x')
						from  dati_tecnici dt
						where dt.id_tipo_dato_tecnico = ".$self->art()->get_system_property_id($key)."
						".(
							$params{CASE_SENSITIVE}
								? "and dt.descrizione = ".$self->art()->_dbh()->quote($params{$index_properties->{type}}->{$key})
								: "and upper(dt.descrizione) = upper(".$self->art()->_dbh()->quote($params{$index_properties->{type}}->{$key}).")"
						)."
							and rownum <= $min_rows -- Evita che property poco selettive rallentino l'esecuzione
					";
					$fields_filter .= "\n AND \"".$self->art()->get_system_property_id($key)."\" = ".$self->art()->_dbh()->quote($params{$index_properties->{type}}->{$key}) if $params{CASE_SENSITIVE};
					$fields_filter .= "\n AND upper(\"".$self->art()->get_system_property_id($key)."\") = upper(".$self->art()->_dbh()->quote($params{$index_properties->{type}}->{$key}).")" unless $params{CASE_SENSITIVE};
				} elsif ($index_properties->{type} =~/^(PROPERTIES_NOT_EQUAL)$/){
					$confronto_desc_dt = "
						select count ('x')
						from  dati_tecnici dt
						where dt.id_tipo_dato_tecnico = ".$self->art()->get_system_property_id($key)."
						".(
							$params{CASE_SENSITIVE}
								? "and dt.descrizione != ".$self->art()->_dbh()->quote($params{$index_properties->{type}}->{$key})
								: "and upper(dt.descrizione) != upper(".$self->art()->_dbh()->quote($params{$index_properties->{type}}->{$key}).")"
						)."
							and rownum <= $min_rows -- Evita che property poco selettive rallentino l'esecuzione
					";
					$fields_filter .= "\n AND \"".$self->art()->get_system_property_id($key)."\" != ".$self->art()->_dbh()->quote($params{$index_properties->{type}}->{$key}) if $params{CASE_SENSITIVE};
					$fields_filter .= "\n AND upper(\"".$self->art()->get_system_property_id($key)."\") != upper(".$self->art()->_dbh()->quote($params{$index_properties->{type}}->{$key}).")" unless $params{CASE_SENSITIVE};
				} elsif ($index_properties->{type} =~/^(PROPERTIES_LIKE)$/){
					$confronto_desc_dt = "
						select count ('x')
						from  dati_tecnici dt
						where dt.id_tipo_dato_tecnico = ".$self->art()->get_system_property_id($key)."
						".(
							$params{CASE_SENSITIVE}
								? "and dt.descrizione like ".$self->art()->_dbh()->quote($params{$index_properties->{type}}->{$key})
								: "and upper(dt.descrizione) like upper(".$self->art()->_dbh()->quote($params{$index_properties->{type}}->{$key}).")"
						)."
							and rownum <= $min_rows -- Evita che property poco selettive rallentino l'esecuzione
					";
					$fields_filter .= "\n AND \"".$self->art()->get_system_property_id($key)."\" like ".$self->art()->_dbh()->quote($params{$index_properties->{type}}->{$key}) if $params{CASE_SENSITIVE};
					$fields_filter .= "\n AND upper(\"".$self->art()->get_system_property_id($key)."\") like upper(".$self->art()->_dbh()->quote($params{$index_properties->{type}}->{$key}).")" unless $params{CASE_SENSITIVE};
				} elsif ($index_properties->{type} =~/^(PROPERTIES_IN)$/){
					$confronto_desc_dt = "
						select count ('x')
						from  dati_tecnici dt
						where dt.id_tipo_dato_tecnico = ".$self->art()->get_system_property_id($key)."
						".(
							$params{CASE_SENSITIVE}
								? "and dt.descrizione in (".join(",", map { $self->art()->_dbh()->quote($_) } @{$params{$index_properties->{type}}->{$key}}).")"
								: "and upper(dt.descrizione) in (".join(",", map { "upper(".$self->art()->_dbh()->quote($_).")" } @{$params{$index_properties->{type}}->{$key}}).")"
						)."
							and rownum <= $min_rows -- Evita che property poco selettive rallentino l'esecuzione
					";
					$fields_filter .= "\n AND \"".$self->art()->get_system_property_id($key)."\" in (".join(",", map { $self->art()->_dbh()->quote($_) } @{$params{$index_properties->{type}}->{$key}}).")" if $params{CASE_SENSITIVE};
					$fields_filter .= "\n AND upper(\"".$self->art()->get_system_property_id($key)."\") in (".join(",", map { "upper(".$self->art()->_dbh()->quote($_).")" } @{$params{$index_properties->{type}}->{$key}}).")" unless $params{CASE_SENSITIVE};
				} elsif ($index_properties->{type} eq 'PROPS_NULL'){
					$confronto_desc_dt = qq{
						select count (distinct 'x')
						from  dati_tecnici dt
						where not exists (
						  select dt2.id_sistema
						  from dati_tecnici dt2
						  where dt2.id_sistema = dt.id_sistema
						  and dt2.id_tipo_dato_tecnico = ${\$self->art()->get_system_property_id($key)}
						)
						and rownum <= $min_rows -- Evita che property poco selettive rallentino l'esecuzione
					};
					$fields_filter .= "\n AND \"".$self->art()->get_system_property_id($key)."\" is null";
				} elsif ($index_properties->{type} eq 'PROPS_NOT_NULL'){
					$confronto_desc_dt = qq{
						select count ('x')
						from  dati_tecnici dt
						where dt.id_tipo_dato_tecnico = ${\$self->art()->get_system_property_id($key)}
						  and dt.descrizione is not null
						  and rownum <= $min_rows -- Evita che property poco selettive rallentino l'esecuzione
					};
					$fields_filter .= "\n AND \"".$self->art()->get_system_property_id($key)."\" is not null";
				}

				# Se esiste una sola chiave/property di ricerca evito la count
				if ( scalar(@list_properties_keys) == 1 ) {

					$property_criteria = $key;

				} else {
					
					# print STDERR $confronto_desc_dt."\n";
					# Cerco il numero di righe che rispettano il singolo criterio
					my $count = $self->art()->_dbh()->fetch_minimalized($confronto_desc_dt);
					# Ritorno subito se non esiste almeno una propeprty/riga che rispetta il criterio
					if ($count == 0){
						if ($params{EXTENDED_OUTPUT}){
							return {
								COUNT => $count,
								RESULTS => []
							}
						} else {
							return [];
						}
					}
					# Memorizzo il miglior criterio di ricerca per settarlo nei filtri sugli id_attivita
					if ( $count < $max_properties ) {
						$max_properties = $count;
						$property_criteria = $key;
					}
				}

				my $tipo_tdts = $self->art()->_lookup()->tipo_tdts_nome($key);
			
				if ($self->art()->is_system_property_name_multiplex($key)){
					unless (grep {$_ eq 'DATI_TECNICI_'.$self->art()->get_system_property_id($key)} @used_join){
						$join_table.=" join dati_tecnici dtmult".$self->art()->get_system_property_id($key)." on s.id_sistema = dtmult".$self->art()->get_system_property_id($key).".id_sistema and dtmult".$self->art()->get_system_property_id($key).".id_tipo_dato_tecnico = ".$self->art()->get_system_property_id($key);
						push @used_join, 'DATI_TECNICI_'.$self->art()->get_system_property_id($key);
					}
					unless (grep {$_ eq $self->art()->get_system_property_id($key)} @used_fields){
						if ($tipo_tdts =~/^(INTEGER|LONG|FLOAT)$/){
							$fields.= "
								, to_number_if_valid(dtmult".$self->art()->get_system_property_id($key).".descrizione) \"".$self->art()->get_system_property_id($key)."\" --$key
							";
						} else {
							$fields.= "
								, dtmult".$self->art()->get_system_property_id($key).".descrizione \"".$self->art()->get_system_property_id($key)."\" --$key
							";

						}
						push @used_fields, $self->art()->get_system_property_id($key);
					}
				} else {
					# Costruisce blocco per estrarre la property
					unless (grep {$_ eq $self->art()->get_system_property_id($key)} @used_fields){
						if ($tipo_tdts =~/^(INTEGER|LONG|FLOAT)$/){
							$fields .= " 
								,to_number_if_valid((SELECT dt.descrizione
									FROM   dati_tecnici dt
									WHERE  dt.id_tipo_dato_tecnico = ${\$self->art()->get_system_property_id($key)}
										AND dt.id_sistema = s.id_sistema
										AND ROWNUM<2
										)) \"".$self->art()->get_system_property_id($key)."\" --$key
							";
						} else {
							$fields .= " 
								,(SELECT dt.descrizione
									FROM   dati_tecnici dt
									WHERE  dt.id_tipo_dato_tecnico = ${\$self->art()->get_system_property_id($key)}
										AND dt.id_sistema = s.id_sistema
										AND ROWNUM<2
										) \"".$self->art()->get_system_property_id($key)."\" --$key
							";
						}
						push @used_fields, $self->art()->get_system_property_id($key);
					}
				}
			}
			next if defined $where_filter;
			# Costruisco la where condition per abbattere il numero di record da cui estrarre le property
			if ($index_properties->{type} =~/^(PROPERTIES|PROPERTIES_EQUAL)$/){
				$where_filter = "
					s.id_sistema in (
						select dt.id_sistema
						from  dati_tecnici dt
							$count_join_tipi_Sistema
						where dt.id_tipo_dato_tecnico = ".$self->art()->get_system_property_id($property_criteria)." -- $property_criteria
							".(
								$params{CASE_SENSITIVE}
									? "and dt.descrizione = ".$self->art()->_dbh()->quote($params{$index_properties->{type}}->{$property_criteria})
									: "and upper(dt.descrizione) = upper(".$self->art()->_dbh()->quote($params{$index_properties->{type}}->{$property_criteria}).")"
							)."
					)
				";
			} elsif ($index_properties->{type} =~/^(PROPERTIES_NOT_EQUAL)$/){
				$where_filter = "
					s.id_sistema in (
						select dt.id_sistema
						from  dati_tecnici dt
							$count_join_tipi_Sistema
						where dt.id_tipo_dato_tecnico = ".$self->art()->get_system_property_id($property_criteria)." -- $property_criteria
							".(
								$params{CASE_SENSITIVE}
									? "and dt.descrizione != ".$self->art()->_dbh()->quote($params{$index_properties->{type}}->{$property_criteria})
									: "and upper(dt.descrizione) != upper(".$self->art()->_dbh()->quote($params{$index_properties->{type}}->{$property_criteria}).")"
							)."
					)
				";
			} elsif ($index_properties->{type} =~/^(PROPERTIES_LIKE)$/){
				$where_filter = "
					s.id_sistema in (
						select dt.id_sistema
						from  dati_tecnici dt
							$count_join_tipi_Sistema
						where dt.id_tipo_dato_tecnico = ".$self->art()->get_system_property_id($property_criteria)." -- $property_criteria
							".(
								$params{CASE_SENSITIVE}
									? "and dt.descrizione like ".$self->art()->_dbh()->quote($params{$index_properties->{type}}->{$property_criteria})
									: "and upper(dt.descrizione) like upper(".$self->art()->_dbh()->quote($params{$index_properties->{type}}->{$property_criteria}).")"
							)."
					)
				";
			} elsif ($index_properties->{type} =~/^(PROPERTIES_IN)$/){
				$where_filter = "
					s.id_sistema in (
						select dt.id_sistema
						from  dati_tecnici dt
							$count_join_tipi_Sistema
						where dt.id_tipo_dato_tecnico = ".$self->art()->get_system_property_id($property_criteria)." -- $property_criteria
							".(
								$params{CASE_SENSITIVE}
									? "and dt.descrizione in (".join(",", map { $self->art()->_dbh()->quote($_) } @{$params{$index_properties->{type}}->{$property_criteria}}).")"
									: "and upper(dt.descrizione) in (".join(",", map { "upper(".$self->art()->_dbh()->quote($_).")" } @{$params{$index_properties->{type}}->{$property_criteria}}).")"
							)."
					)
				";
			} elsif ($index_properties->{type} eq 'PROPS_NULL'){
				$where_filter = "
					s.id_sistema in (
						select distinct dt.id_sistema
						from dati_Tecnici dt
							$count_join_tipi_Sistema
						where not exists (
						  select dt2.id_sistema
						  from dati_tecnici dt2
						  where dt2.id_sistema = dt.id_sistema
						  and dt2.id_tipo_dato_tecnico = ".$self->art()->get_system_property_id($property_criteria)." -- $property_criteria
						)
					)
				";
			} elsif ($index_properties->{type} eq 'PROPS_NOT_NULL'){
				$where_filter = "
					s.id_sistema in (
						select dt.id_sistema
						from  dati_tecnici dt
							$count_join_tipi_Sistema
						where dt.id_tipo_dato_tecnico = ".$self->art()->get_system_property_id($property_criteria)." -- $property_criteria
						and dt.descrizione is not null
					)
				";
			}
			push @where, $where_filter;
		}
	}
	
	# TODO Verificare se sia il caso di limitare la selezione almeno ad un criterio per evitare eventuali sovraccarichi
	my $where = '';
	$where = 'WHERE ' . join("\n\t\tAND ", @where) if scalar(@where);
	my $elementsQuery = qq(
		select	distinct SYSTEM_ID R
		from	(
					SELECT s.id_sistema SYSTEM_ID
						   $fields
					FROM   sistemi s
					$join_table
					$where
				)
		where	$fields_filter
		$orderBy
		$skip_limit
	);
	my $countQuery = qq(
		select	COUNT(1) R
		from	(
					SELECT s.id_sistema SYSTEM_ID
						   $fields
					FROM   sistemi s
					$join_table
					$where
				)
		where	$fields_filter
	);
	my $finalQuery;

	if ($params{EXTENDED_OUTPUT}){
		if ($params{ONLY_COUNT}){
			$finalQuery = $countQuery;
		} else {
			$finalQuery = $countQuery."
				union all
				select *
				from (
			".
					$elementsQuery
			."
				)
			";
		}
	} else {
		$finalQuery = $elementsQuery;
	}
	
	# print STDERR $finalQuery;
	
	# Query di selezione
	# la modifica a NLS_NUMERIC_CHARACTERS serve per la gestione dei campi numerici
	my $old_nls_lang = $self->art()->_dbh()->get_session_parameters('NLS_NUMERIC_CHARACTERS');
	$self->art()->_dbh()->set_session_parameters('NLS_NUMERIC_CHARACTERS' => '.,');
	my $result = $self->art()->_dbh()->fetchall_hashref($finalQuery);
	$self->art()->_dbh()->set_session_parameters('NLS_NUMERIC_CHARACTERS' => $old_nls_lang->{NLS_NUMERIC_CHARACTERS});

	my $results;
	if ($params{EXTENDED_OUTPUT}){
		$results->{COUNT} = $result->[0]->{R}+0;
		shift(@{$result});
		unless ($params{ONLY_COUNT}){
			$results->{RESULTS} = [];
			my $skip = 0;
			for my $res (@{$result}){
				push @{$results->{RESULTS}}, $res->{R};
			}
		}
 
	} else {
		@$results = map { $_->{R} } @{$result};
	}
	return $results;
}



=head2 I<package>->B<new>( ART => I<API::ART> )

Il metodo B<new()> e' il costruttore di classe e richiede un solo argomento:

=over 4

=item B<ART>

Un'istanza della classe API::ART

=back

=cut
#sub new {
#	my $this  = shift;
#	my $class = ref($this) || $this;
#	my $self  = $class->SUPER::new(@_);
#	$self = bless( $self, $class );
#	return $self;
#}


=head2 I<object>->B<art>()

Ritorna il riferimento all'oggetto API::ART passato come argomento al costruttore.

=head2 I<object>->B<find_id>()

=head2 I<object>->B<find_object>()

I due metodi sono simili con l'unica differenza che B<find_id()> ritorna 
un I<arrayref> di SYSTEM_ID, mentre B<find_object()> ritorna un I<arrayref> di
riferimenti ad oggetti di tipo B<API::ART::System>.

Entrambi possono operare sui seguenti parametri opzionali:

=over 4

=item B<ID_EQUAL>

Effettua una ricerca puntuale sull'id_sistema

=item B<ID_IN>

Effettua una ricerca sugli id_sistema elencate

=item B<ID_LIKE>

Effettua una ricerca approssimativa sull'id_sistema (i caratteri jolly '%' e '_' devono essere specificati dall'utilizzatore della classe)

=item B<SYSTEM_TYPE_ID>

Un I<arrayref> con l'elenco degli ID degli SYSTEM_TYPE

B<NOTA>: non utilizzabile contestualmente a B<SYSTEM_TYPE_NAME_*>

=item B<SYSTEM_TYPE_NAME>

Un I<arrayref> con l'elenco dei NAME degli SYSTEM_TYPE

B<DEPRECATO in favore di SYSTEM_TYPE_NAME_IN>.

=item B<SYSTEM_TYPE_NAME_EQUAL>

Consente di filtrare i sistemi in base al nome del tipo sistema: effettua una ricerca esatta.

=item B<SYSTEM_TYPE_NAME_IN>

Consente di filtrare i sistemi in base ad un elenco di nomi del tipo sistema: effettua una ricerca esatta.

=item B<SYSTEM_TYPE_NAME_LIKE>

Consentono di filtrare i sistemi in base al nome del tipo sistema: effettua una ricerca approssimativa (i caratteri jolly '%' e '_' devono essere
specificati dall'utilizzatore della classe).

=item B<ACTIVE>

1 (default) oppure 0 indicando se i sistemi da trovare debbono essere attivi o dismessi/dismessi

=item B<SUSPENDED>

1 oppure 0 (default) indicando se i sistemi da trovare debbono essere sospesi o meno

=item B<EQUAL> oppure B<LIKE>

DEPRECATO: da sostituire con il nuovi metodi DESCRIPTION_*. Consentono di filtrare i sistemi in base al loro nome: B<EQUAL> effettua una riecrca esatta
mentre B<LIKE> una ricerca approssimativa (i caratteri jolly '%' e '_' devono essere
specificati dall'utilizzatore della classe).

=item B<DESCRIPTION_EQUAL>

Consente di filtrare i sistemi in base al loro nome: effettua una ricerca esatta.

=item B<DESCRIPTION_IN>

Consente di filtrare i sistemi in base ad un elenco di loro nomi: effettua una ricerca esatta.

=item B<DESCRIPTION_LIKE>

Consentono di filtrare i sistemi in base al loro nome: effettua una ricerca approssimativa (i caratteri jolly '%' e '_' devono essere
specificati dall'utilizzatore della classe).

=item B<CASE_SENSITIVE>

Prevede una ricerca sensibile alle maiuscole/minuscole

=item B<PROPERTIES>

Un I<hashref> con le coppie NAME => VALUE delle SYSTEM_PROPERTY. NB: In caso di PROPERTY di tipo MULTIPLEX dovra' essere cmq passato uno scalare e si avra' corrispondenza se almeno uno dei valori corrisponde.

B<DEPRECATO in favore di PROPERTIES_EQUAL>: le chiavi contenute sono riversate in PROPERTIES_EQUAL e, in caso di collisione, saranno utilizzate i valori gia' presenti in PROPERTIES_EQUAL

=item B<PROPERTIES_EQUAL>

Un I<hashref> con le coppie NAME => VALUE delle PROPERTY

=item B<PROPERTIES_NOT_EQUAL>

Un I<hashref> con le coppie NAME => VALUE delle PROPERTY

=item B<PROPERTIES_IN>

Un I<hashref> con le coppie NAME => <array> di VALUE delle PROPERTY

=item B<PROPERTIES_LIKE>

Un I<hashref> con le coppie NAME => 'STRING' ricerca approssimativa sul valore del dato tecnico (i caratteri jolly '%' e '_' devono essere specificati dall'utilizzatore della classe)

=item B<PROPERTIES_IS_NULL>

Un I<hashref> con le coppie NAME => 1 delle SYSTEM_PROPERTY per le quali il valore deve essere null

=item B<PROPERTIES_IS_NOT_NULL>

Un I<hashref> con le coppie NAME => 1 delle SYSTEM_PROPERTY per le quali il valore deve essere not null

=item B<SORT>

Un I<arrayref> contenete hash nella forma {KEY => SORT}

Le KEY possibili sono:

=over 4

=item SYSTEM_TYPE_ID

=item SYSTEM_TYPE_NAME

=item SYSTEM_ID

=item CREATION_DATE

=item DISABLING_DATE

=item ENDING_DATE

=item SYSTEM_NAME

=item PROPERTY

In caso di filtro sulle property sara' necessario specificarlo come hashref {KEY => SORT}

=back

=item B<SKIP>

Numero di record da skippare

=item B<LIMIT>

Numero massimo di record restituiti

=item B<EXTENDED_OUTPUT>

Restituisce un HASHREF con le chiavi COUNT (con il totale dei risultati trovati senza applicare i filtri SKIP e LIMIT) e RESULTS con l'array dei sistemi

=item B<SHOW_ONLY_WITH_VISIBILITY>

Restituisce solo i sistemi per cui l'utente ha visibilita' (eventualmente con SUDO)

=item B<SHOW_NO_SUDO>

Restituisce solo i sistemi per cui l'utente ha visibilita' (solo no SUDO)

=back

=head2 I<object>->B<find_exec>()

Questo metodo opera analogamente a B<find_id()> e B<find_object()> ma accetta in input
il parametro B<CALLBACK> che consente di eseguire un blocco di codice per ogni oggetto
trovato.

Ritorna un I<arrayref> in cui ogni elemento rappresenta il B<return code> del codice 
di B<CALLBACK> che puo' essere qualsiasi tipo di dato atto a tracciare l'esecuzione del
blocco di codice per ogni sistema, in modo che il chiamante abbia la possibilita' di 
eseguire operazioni successive sulla base dell'esito dell'elaborazione.

=cut


=head2 I<object>->B<create>()

Consente di creare un nuovo sistema sulla base delle informazioni passate nella chiamata al metodo stesso.

Se l'operazione ha successo, il metodo ritorna il riferimento ad oggetto di tipo API::ART::System altrimenti
ritorna I<undef> ed imposta il messaggio di errore nella proprieta' B<last_error()> dell'oggetto API::ART 
passato nel costruttore.

Di seguito i parametri del metodo B<create()>: 

=over 4

=item B<SYSTEM_TYPE_ID> | B<SYSTEM_TYPE_NAME>

ID e NOME del tipo di sistema da creare

=item B<SYSTEM_CATEGORY_ID> | B<SYSTEM_CATEGORY_NAME>

ID e NOME della categoria da assegnare al sistema da creare

=item B<SYSTEM_CLASS_ID> | B<SYSTEM_CLASS_NAME>

ID e NOME della classe da assegnare al sistema da creare

=item B<OBJECT_TYPE_ID> | B<OBJECT_TYPE_NAME>

ID e NOME del tipo-oggetto da assegnare al sistema da creare

=item B<DESCRIPTION>

Descrizione da associare al nuovo sistema

=item B<DATE>

Data di creazione del sistema; se omesso verra' utilizzato I<sysdate>

=item B<DATE_FORMAT>

Formato da utilizzare per l'interpretazione di B<DATE>; se omesso verra' utilizzato API::ART::Info('DEFAULT_DATE_FORMAT')

=item B<GROUPS>

ARRAYREF con l'elenco dei nomi dei gruppi che dovranno avere visibilita' sul sistema 

=item B<PROPERTIES> I<hashref>

Un I<hashref> delle proprieta' da associare al sistema

=back

=cut
sub create {
	my $self = shift;
	my %params = @_;
	my $rc = 0;
	my $art = $self->art();
	my $db = $art->_dbh();
	my $errmsg = ''; 
	local $@;

	$art->clear_last_error();

	$art->last_error($errmsg)
	&& return undef
		unless $self->check_named_params(
			 PARAMS		=> \%params
			,MANDATORY	=> {
				 DESCRIPTION	=> { isa => 'SCALAR' , pattern => qr{^.{1,255}$} }
				,GROUPS			=> { isa => 'ARRAY' , min => 1 }
			}
			,OPTIONAL	=> {
				  SYSTEM_TYPE_ID		=> { isa => 'SCALAR' , pattern => qr{^\d+$} }
				 ,SYSTEM_TYPE_NAME		=> { isa => 'SCALAR' , pattern => qr{^.+$} }
				 ,SYSTEM_CATEGORY_ID 	=> { isa => 'SCALAR' , pattern => qr{^\d+$} }
				 ,SYSTEM_CATEGORY_NAME	=> { isa => 'SCALAR' , pattern => qr{^.+$} }
				 ,SYSTEM_CLASS_ID 		=> { isa => 'SCALAR' , pattern => qr{^\d+$} }
				 ,SYSTEM_CLASS_NAME		=> { isa => 'SCALAR' , pattern => qr{^.+$} }
				 ,OBJECT_TYPE_ID		=> { isa => 'SCALAR' , pattern => qr{^\d+$} }
				 ,OBJECT_TYPE_NAME 		=> { isa => 'SCALAR' , pattern => qr{^.+$} }
				 ,DATE					=> { isa => 'SCALAR' , pattern => qr{^[0-9 \-\/:]+$} }
				 ,DATE_FORMAT 			=> { isa => 'SCALAR' , pattern => qr{^.+$} }
				 ,PROPERTIES			=> { isa => 'HASH' }
			}
			,ERRMSG		=> \$errmsg
		);
	
	# SYSTEM_TYPE
	$art->last_error("Concurrent missing of SYSTEM_TYPE_ID and SYSTEM_TYPE_NAME parameters!") 
		&& return undef 
			unless ( defined($params{SYSTEM_TYPE_ID}) || defined($params{SYSTEM_TYPE_NAME}) );
	$art->last_error("Expectiong one of SYSTEM_TYPE_ID and SYSTEM_TYPE_NAME parameters!") 
		&& return undef 
			if ( defined($params{SYSTEM_TYPE_ID}) && defined($params{SYSTEM_TYPE_NAME}) );
	if ( defined $params{SYSTEM_TYPE_ID} ) {
		$art->last_error("SYSTEM_TYPE_ID '$params{SYSTEM_TYPE_ID}' undefined!") 
			&& return undef 
				unless $art->test_system_type_id($params{SYSTEM_TYPE_ID});
		$params{SYSTEM_TYPE_NAME} = $art->get_system_type_name($params{SYSTEM_TYPE_ID});
	} else {
		$art->last_error("SYSTEM_TYPE_NAME '$params{SYSTEM_TYPE_NAME}' undefined!") 
			&& return undef 
				unless $art->test_system_type_name($params{SYSTEM_TYPE_NAME});
		$params{SYSTEM_TYPE_ID} = $art->get_system_type_id($params{SYSTEM_TYPE_NAME});
	} 
	
	# SYSTEM_CATEGORY
	$art->last_error("Concurrent missing of SYSTEM_CATEGORY_ID and SYSTEM_CATEGORY_NAME parameters!") 
		&& return undef 
			unless ( defined($params{SYSTEM_CATEGORY_ID}) || defined($params{SYSTEM_CATEGORY_NAME}) );
	$art->last_error("Expectiong one of CATEGORY_TYPE_ID and CATEGORY_TYPE_NAME parameters!") 
		&& return undef 
			if ( defined($params{SYSTEM_CATEGORY_ID}) && defined($params{SYSTEM_CATEGORY_NAME}) );
	if ( defined $params{SYSTEM_CATEGORY_ID} ) {
		$art->last_error("SYSTEM_CATEGORY_ID '$params{SYSTEM_CATEGORY_ID}' undefined!") 
			&& return undef 
				unless $art->test_system_category_id($params{SYSTEM_CATEGORY_ID});
		$params{SYSTEM_CATEGORY_NAME} = $art->get_system_category_name($params{SYSTEM_CATEGORY_ID});
	} else {
		$art->last_error("SYSTEM_CATEGORY_NAME '$params{SYSTEM_CATEGORY_NAME}' undefined!") 
			&& return undef 
				unless $art->test_system_category_name($params{SYSTEM_CATEGORY_NAME});
		$params{SYSTEM_CATEGORY_ID} = $art->get_system_category_id($params{SYSTEM_CATEGORY_NAME});
	} 
	
	# SYSTEM_CLASS
	$art->last_error("Concurrent missing of SYSTEM_CLASS_ID and SYSTEM_CLASS_NAME parameters!") 
		&& return undef 
			unless ( defined($params{SYSTEM_CLASS_ID}) || defined($params{SYSTEM_CLASS_NAME}) );
	$art->last_error("Expectiong one of CATEGORY_TYPE_ID and CATEGORY_TYPE_NAME parameters!") 
		&& return undef 
			if ( defined($params{SYSTEM_CLASS_ID}) && defined($params{SYSTEM_CLASS_NAME}) );
	if ( defined $params{SYSTEM_CLASS_ID} ) {
		$art->last_error("SYSTEM_CLASS_ID '$params{SYSTEM_CLASS_ID}' undefined!") 
			&& return undef 
				unless $art->test_system_class_id($params{SYSTEM_CLASS_ID});
		$params{SYSTEM_CLASS_NAME} = $art->get_system_class_name($params{SYSTEM_CLASS_ID});
	} else {
		$art->last_error("SYSTEM_CLASS_NAME '$params{SYSTEM_CLASS_NAME}' undefined!") 
			&& return undef 
				unless $art->test_system_class_name($params{SYSTEM_CLASS_NAME});
		$params{SYSTEM_CLASS_ID} = $art->get_system_class_id($params{SYSTEM_CLASS_NAME});
	} 
	
	# OBJECT_TYPE
	$art->last_error("Concurrent missing of OBJECT_TYPE_ID and OBJECT_TYPE_NAME parameters!") 
		&& return undef 
			unless ( defined($params{OBJECT_TYPE_ID}) || defined($params{OBJECT_TYPE_NAME}) );
	$art->last_error("Expectiong one of CATEGORY_TYPE_ID and CATEGORY_TYPE_NAME parameters!") 
		&& return undef 
			if ( defined($params{OBJECT_TYPE_ID}) && defined($params{OBJECT_TYPE_NAME}) );
	if ( defined $params{OBJECT_TYPE_ID} ) {
		$art->last_error("OBJECT_TYPE_ID '$params{OBJECT_TYPE_ID}' undefined!") 
			&& return undef 
				unless $art->test_object_type_id($params{OBJECT_TYPE_ID});
		$params{OBJECT_TYPE_NAME} = $art->get_object_type_name($params{OBJECT_TYPE_ID});
	} else {
		$art->last_error("OBJECT_TYPE_NAME '$params{OBJECT_TYPE_NAME}' undefined!") 
			&& return undef 
				unless $art->test_object_type_name($params{OBJECT_TYPE_NAME});
		$params{OBJECT_TYPE_ID} = $art->get_object_type_id($params{OBJECT_TYPE_NAME});
	}
	 
	# DESCRIPTION
	$art->last_error("Missing DESCRIPTION!") 
		&& return undef 
			unless defined $params{DESCRIPTION};
	
	# DATE_FORMAT
	$params{DATE_FORMAT} = $art->info('DEFAULT_DATE_FORMAT') unless defined $params{DATE_FORMAT}; 
	# DATE 
	$params{DATE} = $art->_dbh()->get_sysdate($params{DATE_FORMAT}) unless defined $params{DATE};
	my $test_date = $art->_dbh()->test_date($params{DATE}, $params{DATE_FORMAT});
	$art->last_error("Invalid DATE '$params{DATE}': '$test_date'!") && return undef if $test_date;
	
	# GROUPS
	$art->last_error("Missing GROUPS!") 
		&& return undef 
			unless defined $params{GROUPS};
	$params{GROUPS} = [] unless defined $params{GROUPS};
	$art->last_error("GROUPS must be an array-ref!") 
		&& return undef 
			unless ref($params{GROUPS}) eq 'ARRAY';
	$art->last_error("At least one group-name must be specified in GROUPS parameter!") 
		&& return undef 
			if scalar(@{$params{GROUPS}}) == 0;
	my $root_found = 0;
	foreach my $group ( @{$params{GROUPS}} ) {
		$art->last_error("Bad GROUP name '$group'!") 
			&& return undef 
				unless $art->test_group_name($group);
		$root_found = (uc($group) eq 'ROOT' || $root_found ? 1 : 0);
	}
	push @{$params{GROUPS}}, 'ROOT' unless $root_found;
#	
#	# PROPERTY
#	$params{PROPERTIES} = {} unless defined $params{PROPERTIES};
#	$art->last_error("PROPERTIES must be an hash-ref or undefined!") && 
#		return undef 
#			unless ref($params{PROPERTIES}) eq 'HASH';

	# Creazione sistema
	my $sql = qq/
		INSERT INTO sistemi (
			 id_sistema
			,id_tipo_sistema
			,descrizione
			,id_classe_sistema
			,id_categoria_sistema
			,data_inizio_osservazione
			,id_tipo_oggetto
			,data_dismissione
			,data_sospensione
			,ha_sottosistemi
			,manutenibile
			,autoparse
		) VALUES (
			 seq_sistemi.NEXTVAL
			,$params{SYSTEM_TYPE_ID}
			,${ \$db->quote($params{DESCRIPTION}) }
			,$params{SYSTEM_CLASS_ID}
			,$params{SYSTEM_CATEGORY_ID}
			,TO_DATE('$params{DATE}', '${ \$params{DATE_FORMAT} }')
			,$params{OBJECT_TYPE_ID}
			,NULL
			,NULL
			,NULL
			,NULL
			,NULL
		)
	/;
	$rc = 0;
	eval { $rc = $db->do($sql); };
	$art->last_error("Error creating system!\n" . $@) 
		&& return undef
			unless $rc;

	# Determino l'ID del nuovo sistema
	my $system_id = eval { $db->fetch_minimalized(qq/SELECT seq_sistemi.CURRVAL FROM DUAL/) };
	$art->last_error("Error fetching system ID!\n" . $@) 
		&& return undef
			unless $system_id;

	# Creazione permessi
	my $sep = $";
	$" = "','";
	$sql = qq/
		INSERT INTO permission_sistemi 
		SELECT	 $system_id
				,id_gruppo from gruppi
		WHERE	nome IN ( '@{$params{GROUPS}}' )
	/;
	$" = $sep;
	$rc = 0;
	eval { $rc = $db->do($sql); };
	$art->last_error("Error setting system permission!\n" . $@) 
		&& return undef
			unless $rc;

	# Determino quale classe utilizzare per la creazione dell'istanza basandomi sulla 
	# proprieta' DEFAULT_CLASS_TO_CREATE della classe API::ART::Collection::System o
	# di una classe da essa derivata
	my $class = eval '$' . ref($self) . '::DEFAULT_CLASS_TO_CREATE';
	# Ritorno un riferimento all'oggetto che astrae il sistema appena creato
	my $system = $class->new(
		ART	=> $art, 
		ID	=> $system_id 
	);
	$art->last_error("Error creating system instance for ID='$system_id' (class='$class')!\n" . $@) 
		&& return undef
			unless $system;
	
	# Inserimento property
	return undef
		unless $system->set_property(
			 PROPERTIES		=> $params{PROPERTIES}
			,DATE			=> $params{DATE}
			,DATE_FORMAT	=> $params{DATE_FORMAT}
		);

	return $system;
}

1;
