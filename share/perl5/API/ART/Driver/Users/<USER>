package API::ART::Driver::Users::JSON;

use strict;
use warnings;
use Carp;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);
use Getopt::Long;
Getopt::Long::Configure (qw(bundling no_ignore_case no_auto_abbrev no_pass_through));
use JSON;

sub options {
	return "

	$0 -r ".__PACKAGE__."  [<opzioni>]
	
	Questo driver accetta un file JSON contenente un ARRAY di oggetti che descrivono l'utente come documentato nell'API::ART::REST
	POST /api/art/instance/users
	
	<opzioni>:
		--oFile=<FILENAME> - File di input
	
";
}

sub new {
	my $class = shift;
    my $self  = bless { }, $class;
    my %params = @_;
	
	croak 'Missing mandatory param API' unless $params{API};
    $self->{API} = $params{API};
    $self->{DB} = $params{API}->_dbh();
    
    $self->{LOGGER} = Log::Log4perl->get_logger( 'COMMON::LIB::' . __PACKAGE__ );
    
	GetOptions (
		'oFile=s'=>	\$self->{FILE}
	) or croak( options );
	
	croak "You must specify an input file!" unless defined $self->{FILE};
	
    croak 'File does not exists' unless -e $self->{FILE};
    croak 'File is not a plain file' unless -f $self->{FILE};

	return bless( $self, $class );
}

sub _art { shift->{API} }

sub _db { shift->{DB} }

sub _logger { shift->{LOGGER} }

sub get {
	my $self = shift;
	
	my $results;
	
	$self->_art()->last_error("Couldn't open file ".$self->{FILE}.", $!")
		&& return undef
			unless(open(DATA, "<".$self->{FILE}));

	my $remap = {
		"password"		=> 'PASSWORD',
		"lastName"		=> 'LAST_NAME',
		"firstName"		=> 'FIRST_NAME',
		"email"			=> 'EMAIL',
		"mobilePhone"	=> 'MOBILE_PHONE',
		"roles"			=> 'ROLES',
		"roleAliases"	=> 'ROLE_ALIASES',
		"groups"		=> 'GROUPS',
	};
	
	my $full_json_string;
	
	while(<DATA>){
		$full_json_string.=$_;
	}
	
	$self->_art()->last_error("Couldn't close file properly")
		&& return undef
			unless close(DATA);
	
	my $full_json_array = eval{from_json($full_json_string)};

	$self->_art()->last_error('Invalid JSON: '.$@)
		&& return undef
			if $@;
	
	$self->_art()->last_error('Invalid JSON: not an ARRAY of objects')
		&& return undef
			if ref ($full_json_array) ne 'ARRAY';
	
	for my $row (@{$full_json_array}){
		my $name = $row->{username};
		$self->_art()->last_error('Missing key username for row '. Dumper $row)
			&& return undef
				unless (defined $name);
		for my $key (keys %{$row}){
			$row->{$remap->{$key}} = $row->{$key} if exists $remap->{$key};
			delete $row->{$key};
		}
		
		if (exists $row->{GROUPS}) {
			$self->_art()->last_error('GROUPS param must be an arrayref of at least one element for row '. Dumper $row)
				&& return undef
					unless (ref $row->{GROUPS} eq 'ARRAY' && scalar @{$row->{GROUPS}});
			my @tmp = @{$row->{GROUPS}};
			$row->{GROUPS} = {};
			$row->{GROUPS}->{PRIMARY} = shift @tmp;
			if (scalar @tmp){
				$row->{GROUPS}->{SECONDARY} = [ @tmp ] ;
			} else {
				$row->{GROUPS}->{SECONDARY} = [] ;
			}
		}

		if (exists $results->{$name}){
			$self->_art()->last_error('Found duplicate record for name '.$name);
			return undef;
		} else {
			$results->{$name} = $row;
		}
	}

	return $results;
}

1;
