package API::ART::Driver::Users::Baseline;

use strict;
use warnings;
use Carp;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);
use Getopt::Long;
Getopt::Long::Configure (qw(bundling no_ignore_case no_auto_abbrev no_pass_through));
use JSON;

sub options {
	return "

	$0 -r ".__PACKAGE__."  [<opzioni>]
	
	Questo driver recupera tutti gli utenti di un ART Baseline che appartengono ad almeno uno dei gruppi definiti nell'istanza ART
	
	<opzioni>:
		--oBaselineConnectString=<CS> - Connect string allo schema art del db della Baseline (default ENV SQLID_BASELINE)
		--oSkipPassword               - non allinea la password, usare nel caso in cui l'autenticazione non venga fatta sul backend di ART
	
";
}

sub new {
	my $class = shift;
	my $self  = bless { }, $class;
	my %params = @_;
	
	croak 'Missing mandatory param API' unless $params{API};
	
	$self->{API} = $params{API};
	$self->{DB} = $params{API}->_dbh();
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'COMMON::LIB::' . __PACKAGE__ );
	
	$self->{BASELINE_CONNECT_STRING} = $ENV{SQLID_BASELINE};
	$self->{SKIP_PASSWORD} = undef;
	
	GetOptions (
		 'oBaselineConnectString=s'	=> \$self->{BASELINE_CONNECT_STRING}
		,'oSkipPassword'			=> \$self->{SKIP_PASSWORD}
	) or croak( options );
	
	eval { $self->{BASELINE_DB} = SIRTI::DB->new( $self->{BASELINE_CONNECT_STRING} ); };
	if( $@ ) {
		croak "Unable to connect to BASELINE database: $@";
	}
	
	return bless( $self, $class );
}

sub _art { shift->{API} }

sub _db { shift->{DB} }

sub _logger { shift->{LOGGER} }

sub _baseline_db { shift->{BASELINE_DB} }

sub get {
	my $self = shift;
	
	my $results;
	
	my @in_local_gruppi;
	my $in_local_gruppi = '';
	for my $r (@{$self->_db()->fetchall_arrayref("select nome from gruppi")}) {
		push @in_local_gruppi, 'select '.$self->_db()->quote($r->[0]).' from dual';
	}
	$in_local_gruppi = join (' union all ', @in_local_gruppi);
	
	# verifico se nel db è presente la gestione dei gruppi privati
	my $sql_private = "
		select 1 From user_tab_cols
		where table_name = 'GRUPPI'
		and column_name = 'PRIVATO'
	";
	
	my $is_private = $self->_baseline_db()->fetch_minimalized($sql_private);
	
	my $sql = "
		with op_gruppi as (
			select id_operatore, id_gruppo from operatori
			union 
			select id_operatore, id_gruppo from operatori_gruppi
		)
		select
			o.LOGIN_OPERATORE
			,o.password_operatore MD5_PASSWORD
			,o.NOME_OPERATORE
			,o.COGNOME_OPERATORE
			,o.MOBILE_PHONE
			,o.EMAIL
			,g.NOME GRUPPO
		from
			operatori o
				join op_gruppi og on o.ID_OPERATORE = og.ID_OPERATORE
				join gruppi g on g.ID_GRUPPO = og.ID_GRUPPO and g.nome in ($in_local_gruppi)
		where 1=1
			and o.morto is null
			and o.service_user is null
			".($is_private ? "and g.privato is null": "")."
		order by
			o.login_operatore, g.nome
	";
	
	$self->_logger()->trace("SQL reperimento utenti baseline: ".$sql);
	
	my %ret = ();
	for my $r (@{$self->_baseline_db()->fetchall_hashref($sql)}) {
		$ret{$r->{LOGIN_OPERATORE}}->{GROUPS} = {
			 PRIMARY => $r->{GRUPPO}
			,SECONDARY => []
		}
			unless exists $ret{$r->{LOGIN_OPERATORE}};
		$ret{$r->{LOGIN_OPERATORE}}->{MD5_PASSWORD} = $r->{MD5_PASSWORD}
			unless $self->{SKIP_PASSWORD};
		$ret{$r->{LOGIN_OPERATORE}}->{FIRST_NAME} = $r->{NOME_OPERATORE};
		$ret{$r->{LOGIN_OPERATORE}}->{LAST_NAME} = $r->{COGNOME_OPERATORE};
		$ret{$r->{LOGIN_OPERATORE}}->{MOBILE_PHONE} = $r->{MOBILE_PHONE};
		$ret{$r->{LOGIN_OPERATORE}}->{EMAIL} = $r->{EMAIL};
		push @{$ret{$r->{LOGIN_OPERATORE}}->{GROUPS}->{SECONDARY}}, $r->{GRUPPO}
			unless $r->{GRUPPO} eq $ret{$r->{LOGIN_OPERATORE}}->{GROUPS}->{PRIMARY} || grep { $r->{GRUPPO} eq $_ } @{$ret{$r->{LOGIN_OPERATORE}}->{GROUPS}->{SECONDARY}};
	}
	
	return \%ret;
	
}

1;
