package API::ART::Driver::Users::SIA_ORACLE;

use strict;
use warnings;
use Carp;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);
use Getopt::Long;
Getopt::Long::Configure (qw(bundling no_ignore_case no_auto_abbrev no_pass_through));

sub options {
	return "

	$0 -r ".__PACKAGE__."  [<opzioni>]
	
	Questo driver recupera l'elenco degli utenti abilitati ad una determinata applicazione (vedi parametri --oInternalResource e 
	--oExternalResource) dal Sistema Informativo Aziendale (S.I.A.) di SIRTI.
	Deve essere specificata almeno un parametro tra --oInternalResource e --oExternalResource
	
	Per convenzione nell'applicazione ART devono essere presenti degli alias ai ruoli nel formato <RISORSA>_<PROFILO>
	(esempio: WPRTLE_SUPERVIS)
	
	<opzioni>:
		--oDBSiaConnectString=<DBSIACONNECTSTRING> - Nome del Database Oracle del S.I.A.
		--oInternalResource=<INTERNALRESOURCE> - Sigla della risorsa interna associata all'applicazione
		--oExternalResource=<EXTERNALRESOURCE> - Sigla della risorsa esterna associata all'applicazione

";
}

sub new {
	my $class = shift;
	my $self  = bless { }, $class;
	my %params = @_;

	croak 'Missing mandatory param API' unless $params{API};
	$self->{API} = $params{API};
	$self->{DB} = $params{API}->_dbh();

	$self->{LOGGER} = Log::Log4perl->get_logger( 'COMMON::LIB::' . __PACKAGE__ );
	$self->{INTERNAL_RESOURCE} = [];
	$self->{EXTERNAL_RESOURCE} = [];

	GetOptions (
		 'oDBSiaConnectString=s'=>	\$self->{DBSIACONNECTSTRING}
		,'oInternalResource=s@' =>	\$self->{INTERNAL_RESOURCE}
		,'oExternalResource=s@' =>	\$self->{EXTERNAL_RESOURCE}
	) or croak( options );

	for ('DBSIACONNECTSTRING') {
		croak "Missing DB param $_" unless defined $self->{$_};
	}
	
	croak "You must specifiy al least one of InternalResource or ExternalResource"
		unless @{$self->{INTERNAL_RESOURCE}} or @{$self->{EXTERNAL_RESOURCE}};
	
	croak "Connection failed to DB SIA with error: ".SIRTI::DB::get_errormessage()
		unless $self->{DBSIADBH} = SIRTI::DB->new($self->{DBSIACONNECTSTRING});
	
	my @queries = ();

	push @queries, sprintf qq{
		SELECT
            TRIM(A.USERID) USERNAME
            ,TRIM(C.NOME) FIRST_NAME
            ,TRIM(C.COGNOME) LAST_NAME
            ,TRIM(D.RISORSA)||'_'||TRIM(D.PROFILO) ROLE
            ,LOWER(TRIM(B.EMAIL)) EMAIL
            ,(
				Select LISTAGG(pt.telefono, ',') WITHIN GROUP (ORDER BY pt.telefono)
				From ANAG.persone_telefoni pt
				where pt.cid = c.cid
					and pt.tipo = '2'
				Group By pt.cid) NUMTEL
            ,b.SOC
        FROM SISTEMA.ABIL_GRUPPI_UTENTI A
            ,SISTEMA.ABIL_GRUPPI_RISORSE X
            ,ANAG.UTENTI B
            ,ANAG.PERSONE C
            ,SISTEMA.ABIL_PROFILI D
        WHERE X.RISORSA in ( %s )
            AND X.GRUPPO=A.GRUPPO
            AND A.DATA_F_VALID>SYSDATE
            AND A.USERID=B.USERID
            AND B.SOC=C.SOC
            AND B.CID=C.CID
            AND C.DATA_F_VALID>SYSDATE
            AND D.RISORSA=X.RISORSA
            AND D.USERID=A.USERID
            AND D.DATA_F_VALID>SYSDATE
            AND (C.DATA_CESSAZIONE is null or C.DATA_CESSAZIONE > sysdate)
            and (a.userid, 1) in (
                select A1.USERID, count(distinct b1.soc)
                FROM SISTEMA.ABIL_GRUPPI_UTENTI A1
                    ,SISTEMA.ABIL_GRUPPI_RISORSE X1
                    ,ANAG.UTENTI B1
                    ,ANAG.PERSONE C1
                    ,SISTEMA.ABIL_PROFILI D1
                WHERE X1.RISORSA = X.RISORSA
                    AND X1.GRUPPO=A1.GRUPPO
                    AND A1.DATA_F_VALID>SYSDATE
                    AND A1.USERID=B1.USERID
                    AND B1.SOC=C1.SOC
                    AND B1.CID=C1.CID
                    AND C1.DATA_F_VALID>SYSDATE
                    AND D1.RISORSA=X.RISORSA
                    AND D1.USERID=A.USERID
                    AND D1.DATA_F_VALID>SYSDATE
                group by A1.USERID
                having count(distinct b1.soc) = 1
            )
	    union
		-- la union serve per gestire il transitorio in fase di split societario dell'ottobre 2022:
		-- ci aspettiamo che dopo lo split questa seconda parte restituisca sempre 0 record
		-- nell'eventualità consideriamo solo quelli con soc diverso da IT10
		SELECT
            TRIM(A.USERID) USERNAME
            ,TRIM(C.NOME) FIRST_NAME
            ,TRIM(C.COGNOME) LAST_NAME
            ,TRIM(D.RISORSA)||'_'||TRIM(D.PROFILO) ROLE
            ,LOWER(TRIM(B.EMAIL)) EMAIL
            ,(
				Select LISTAGG(pt.telefono, ',') WITHIN GROUP (ORDER BY pt.telefono)
				From ANAG.persone_telefoni pt
				where pt.cid = c.cid
					and pt.tipo = '2'
				Group By pt.cid) NUMTEL
            ,b.SOC
        FROM SISTEMA.ABIL_GRUPPI_UTENTI A
            ,SISTEMA.ABIL_GRUPPI_RISORSE X
            ,ANAG.UTENTI B
            ,ANAG.PERSONE C
            ,SISTEMA.ABIL_PROFILI D
        WHERE X.RISORSA in ( %s )
            AND X.GRUPPO=A.GRUPPO
            AND A.DATA_F_VALID>SYSDATE
            AND A.USERID=B.USERID
            AND B.SOC=C.SOC
            AND B.CID=C.CID
            AND C.DATA_F_VALID>SYSDATE
            AND D.RISORSA=X.RISORSA
            AND D.USERID=A.USERID
            AND D.DATA_F_VALID>SYSDATE
            and b.soc not in ('IT10')
            AND (C.DATA_CESSAZIONE is null or C.DATA_CESSAZIONE > sysdate)
            and a.userid in (
                select userid
                from (
                    select A1.USERID, count(distinct b1.soc)
                    FROM SISTEMA.ABIL_GRUPPI_UTENTI A1
                        ,SISTEMA.ABIL_GRUPPI_RISORSE X1
                        ,ANAG.UTENTI B1
                        ,ANAG.PERSONE C1
                        ,SISTEMA.ABIL_PROFILI D1
                    WHERE X1.RISORSA= X.RISORSA
                        AND X1.GRUPPO=A1.GRUPPO
                        AND A1.DATA_F_VALID>SYSDATE
                        AND A1.USERID=B1.USERID
                        AND B1.SOC=C1.SOC
                        AND B1.CID=C1.CID
                        AND C1.DATA_F_VALID>SYSDATE
                        AND D1.RISORSA=X.RISORSA
                        AND D1.USERID=A.USERID
                        AND D1.DATA_F_VALID>SYSDATE
                    group by A1.USERID
                    having count(distinct b1.soc) > 1
                )
            )
	}, join(',', map {$self->{DBSIADBH}->quote($_)} @{$self->{INTERNAL_RESOURCE}})
	 , join(',', map {$self->{DBSIADBH}->quote($_)} @{$self->{INTERNAL_RESOURCE}})
		if @{$self->{INTERNAL_RESOURCE}};

	push @queries, sprintf qq{
		-- NB: la distinct è necessaria in quanto un'utenza extranet potrebbe avere la risorsa assegnata per più di una società.
		--     Esempio: la risorsa WPSOOFE potrebbe essere associata all'utenza XT***** sia per la società IT49 che IT50
		SELECT distinct 
			TRIM(A.USERID) USERNAME
			,TRIM(C.NOME) FIRST_NAME
			,TRIM(C.COGNOME) LAST_NAME
			,TRIM(D.RISORSA)||'_'||TRIM(D.PROFILO) ROLE
			,LOWER(TRIM(B.EMAIL)) EMAIL
			,nvl(TRIM(C.TELEFONO_mobile),trim(c.telefono_Fisso)) NUMTEL
			,b.soc -- NB: da qui uscirà sempre IT10 in quanto tutti gli utenti extranet rimarranno sulla società IT10 (Holding)
		FROM SISTEMA.ABIL_GRUPPI_UTENTI A
			,SISTEMA.ABIL_GRUPPI_RISORSE X
			,ANAG.UTENTI B
			,ANAG.PERSONE_ESTERNE C
			,SISTEMA.ABIL_PROFILI D
		WHERE X.RISORSA in ( %s )
			AND X.GRUPPO=A.GRUPPO
			AND A.DATA_F_VALID>SYSDATE
			AND A.USERID=B.USERID
			AND B.SOC=C.SOC
			AND B.CID=C.CID
			AND C.DATA_F_VALID>SYSDATE
			AND D.RISORSA=X.RISORSA
			AND D.USERID=A.USERID
			AND D.DATA_F_VALID>SYSDATE
	}, join(',', map {$self->{DBSIADBH}->quote($_)} @{$self->{EXTERNAL_RESOURCE}})
		if @{$self->{EXTERNAL_RESOURCE}};
	
	$self->{DBSIAQUERY} = join "\nunion\n", @queries;
	
	$self->{LOGGER}->debug("DBSIAQUERY: " . $self->{DBSIAQUERY});
	
	$self->{DBSIASTH} = $self->{DBSIADBH}->create_prepare($self->{DBSIAQUERY});
	
	croak "Unable to prepare query: ".SIRTI::DB::get_errormessage()
		if (SIRTI::DB::get_errormessage());
	
	return bless( $self, $class );
}

sub _art { shift->{API} }

sub _db { shift->{DB} }

sub _logger { shift->{LOGGER} }

sub _dbSIA { shift->{DBSIADBH} }

sub _dbSIAsth { shift->{DBSIASTH} };

sub get {
	my $self = shift;
	
	my $results = {};
	
	my $sth = $self->_dbSIAsth();
	
	unless ($sth->execute()){
		$self->_art()->last_error('Unable to execute query: '.$self->_dbSIA()->errstr);
		return undef;
	}
	
	my $rows = $sth->fetchall_arrayref();
	
	for my $row (@{$rows}){
		$self->_logger()->debug( Dumper $row );
		my ($username, $firstName, $lastName, $roleAlias, $email, $numtel, $soc) = @$row;
		if (exists $results->{$username}){
			# non può mai succedere che un utente abbia visibilità su più di una compagnia data una certa applicazione
			if ($results->{$username}->{COMPANY} ne $soc){
				$self->_art()->last_error('Find more than one company for username '.$username);
				return undef;
			}
			push @{$results->{$username}->{ROLE_ALIASES}}, $roleAlias;
		} else {
			$results->{$username} = {
				'MOBILE_PHONE' => $numtel,
				'FIRST_NAME' => $firstName,
				'LAST_NAME' => $lastName,
				'EMAIL' => $email,
				'ROLE_ALIASES' => [
					$roleAlias
				],
				'COMPANY' => $soc,
			};
		}
	}

	return $results;
}

1;
