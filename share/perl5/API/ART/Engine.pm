package API::ART::Engine;

##################################################
#
# API::ART::Engine
#
##################################################

use strict;
use warnings;
use Carp qw(verbose croak);

use base qw(API::Ancestor);

use API::ART::Activity;
use API::ART::Activity::Factory;
use API::ART::Collection::Activity;
use API::ART::System;
use API::ART::Collection::System;

our $VERSION = '0.01';

# REGEXP
my $REGEXP_NUMBER = qr{^\d+$};

sub new {

    my $this  = shift;
    my $class = ref($this) || $this;
    
    my $self  = $class->SUPER::new()
    	|| croak ("non riesco ad istanziare API::Ancestor");
    
    my %param = @_;
    
    $self->{ART} = $param{ART}
    	|| croak("richiesto parametro ART");
    
    croak "richiesto parametro API::ART"
    	if (ref $self->{ART} ne 'API::ART');
    
	$self->{CollectionActivity} =
		API::ART::Collection::Activity->new( ART => $self->{ART} )
			|| croak("errore inizializzazione API::ART::Collection::Activity");

	$self->{CollectionSystem} =
		API::ART::Collection::System->new( ART => $self->{ART} )
			|| croak("errore inizializzazione API::ART::Collection::System");
	
	# abilita binding solo se esplicitamente richiesto
	if ($param{BINDING}){
		# binding in apertura
		$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';
		$self->{BINDING} = 1;
	}
	
    return bless( $self, $class );
}

##################################################################################
#
# H A N D L E
#
########################################################################################
#
# SYSTEM_TYPE_NAME: nome del TIPO_SISTEMA su cui creare il sistema
# SYSTEM_DESCRIPTION: descrizione sistema da creare
# OBJECT_TYPE_NAME: relativo al sistema da creare
#
# SYSTEM_ID: indica il sistema su cui creare l'attivita'
# UPDATE_SYSTEM_PROPERTIES: vero se occorre aggiornare le system properties
# INCLUDE_SYSTEM_PROPERTIES: se UPDATE_SYSTEM_PROPERTIES aggiorna solo le system properties
#   definite in questo arrayref
# EXCLUDE_SYSTEM_PROPERTIES: se UPDATE_SYSTEM_PROPERTIES aggiorna tutte le system properties
#   eccetto quelle definite in questo arrayref
#
# ACTIVITY_TYPE_NAME: nome del tipo attivita da creare
# ACTIVITY_ID_CREATE: id_attivita da creare
# ACTIVITY_DESCRIPTION: descrizione attivita' da creare
# ACTIVITY_DATE: data creazione attivita
#
# ACTIVITY: l'istanza dell'attivita' da movimentare
# ACTIVITY_ID: indica l'id_attivita' da movimentare
# PARENT_ID: ACTIVITY_ID viene adottato PARENT_ID
# ACTION_NAME: ACTIVITY_ID viene steppato tramite l'azione NOME_AZIONE
# STEP_DESCRIPTION: descrizione dello step
# STEP_DATE: data step (in caso di azioni multiple e' obblgiatorio un arrayref di STEP_DATE
#	crescenti e di numerosita' uguale a quella del'array ACTION_NAME)
# ATTACHMENTS: elenco allegati. Gli allegati vengono messi solo nella prima azione (eventualmente
#   l'azione di apertura)
#
# STATUS_NAME: viene eseguita un'azione virtuale che porta in questo stato
# VIRTUAL_ACTION_NAME: nome dell'azione virtuale tipo update da utilizzare
# VIRTUAL_ACTION_FORCED: permette l'azione virtuale in stati finali
#
# DISABLE_BINDING_INSIDE_STEP: 1 -> disabilita i binding negli step (anche virtuali)
#
# SAVE_ALL_PROPERTIES: se 0 non salva tutte le properties che rimangono invariate (default 0)
#                      ATTENZIONE: non considera il valore impostato nell'oggetto API::ART
#
# PROPERTIES: hash con i dati da storicizzare (DT sistema e DTA)
#
########################################################################################


sub handle{
	my $self = shift;
	# in caso di errore viene effettuato un rollback sulla subtransazione della procedura
	return $self->_atomic_transaction( 'T_HANDLE', \&_handle, @_ );
}

sub _handle{
	my $self   = shift;
    my %params = @_;
    my $errmsg;
    my $db = $self->{ART}->_dbh();
    my $step_date_isa = "SCALAR";
    
	if (exists $params{ACTION_NAME} && ref($params{ACTION_NAME}) eq 'ARRAY' && defined $params{STEP_DATE}){
		if (scalar (@{$params{ACTION_NAME}}) == 1) {
			# OK se STEP_DATE e' uno scalar oppure scalar STEP_DATE==scalar ACTION_NAME ed impostiamo isa di STEP_DATE = scalar o ARRAY
			if (ref (\$params{STEP_DATE}) eq 'SCALAR') {
				$step_date_isa = "SCALAR";
			} elsif (ref ($params{STEP_DATE}) eq 'ARRAY' && scalar(@{$params{ACTION_NAME}}) == scalar(@{$params{STEP_DATE}})) {
				$step_date_isa = "ARRAY";
			} else {
				$self->{ART}->last_error("HANDLE: Wrong parameter STEP_DATE, expecting a SCALAR or a single element arrayref");
				return undef;
			}

		} elsif (scalar (@{$params{ACTION_NAME}}) > 1) {
			#OK se scalar STEP_DATE = scalar ACTION_NAME impostiamo isa di STEP_DATE = ARRAY
			if (ref ($params{STEP_DATE}) eq 'ARRAY' && scalar(@{$params{ACTION_NAME}}) == scalar(@{$params{STEP_DATE}})) {
				$step_date_isa = "ARRAY";
			} else {
				$self->{ART}->last_error("HANDLE: Wrong parameter STEP_DATE, expecting an arrayref of the same size of ACTION_NAME");
				return undef;
			}
		}
		
		if ($step_date_isa eq "ARRAY") {
			# check che verifica il fatto che ogni data sia successiva a quella precedente
			my $sql = "select TO_CHAR(TO_DATE(?, ?), 'YYYYMMDDHH24MISS') newdate from dual";
			my ($cur_value, $error);
			my $previous_value = 0;
			
			for ( @{$params{STEP_DATE}} ) {
				$cur_value = $self->{ART}->_create_prepare(__PACKAGE__.'_CHECK_DATES',$sql)->fetch_minimalized($_,$self->{ART}->info('DEFAULT_DATE_FORMAT'));
				if ($cur_value <= $previous_value) {
					$self->{ART}->last_error("HANDLE: Wrong array STEP_DATE, the value $cur_value is not bigger than $previous_value");
					return undef;
				}
				$previous_value = $cur_value;
			}
		}
    }
    my $system;
    my $activity;
    
    $self->{USED_PROPERTIES} 	= {};
    $self->{UNUSED_PROPERTIES} 	= {};
	
	# check parametri
	$self->{ART}->last_error("HANDLE: " . $errmsg)
		&& return undef
			unless $self->check_named_params(
				PARAMS => \%params
				, MANDATORY => {
					
				}
				, OPTIONAL => {
					ACTIVITY 					=>	{isa => undef, inherits => [ 'API::ART::Activity' ]},
					ACTIVITY_ID 				=>	{isa => 'SCALAR', pattern => $REGEXP_NUMBER},
					SYSTEM_ID 					=>	{isa => 'SCALAR', pattern => $REGEXP_NUMBER},
					PARENT_ID 					=>	{isa => 'SCALAR', pattern => $REGEXP_NUMBER},
					ACTIVITY_ID_CREATE			=>	{isa => 'SCALAR', pattern => $REGEXP_NUMBER},
					SYSTEM_TYPE_NAME 			=>	{isa => 'SCALAR'},
					SYSTEM_GROUPS				=>	{isa => 'ARRAY'},
					OBJECT_TYPE_NAME 			=>	{isa => 'SCALAR'},
					ACTIVITY_TYPE_NAME 			=>	{isa => 'SCALAR'},
					ACTIVITY_DATE	 			=>	{isa => 'SCALAR'},
					ACTION_NAME 				=>	{isa => 'ARRAY'},
					ACTION_FORCED 				=>  {isa => 'SCALAR'},
					VIRTUAL_ACTION_NAME 		=>	{isa => 'SCALAR'},
					VIRTUAL_ACTION_FORCED		=>	{isa => 'SCALAR'},
					STATUS_NAME 				=>	{isa => 'SCALAR'},
					SYSTEM_DESCRIPTION			=>	{isa => 'SCALAR'},
					ACTIVITY_DESCRIPTION		=>	{isa => 'SCALAR'},
					STEP_DESCRIPTION			=>	{isa => 'SCALAR'},
					STEP_DATE					=>	{isa => $step_date_isa},
					ATTACHMENTS					=>	{isa => 'ARRAY'},
					PROPERTIES 					=>	{isa => 'HASH'},
					DISABLE_BINDING_INSIDE_STEP =>	{isa => 'SCALAR'},
					UPDATE_SYSTEM_PROPERTIES	=>  {isa => 'SCALAR', list => [0,1]},
					EXCLUDE_SYSTEM_PROPERTIES	=>  {isa => 'ARRAY'},
					INCLUDE_SYSTEM_PROPERTIES	=>  {isa => 'ARRAY'},
					SAVE_ALL_PROPERTIES			=>  {isa => 'SCALAR', list => [0,1]},
				}
				, IGNORE_EXTRA_PARAMS => 0
				, ERRMSG =>\$errmsg
			);
			
	#----------------------------
	# imposta valori di default
	#----------------------------
	unless (exists $params{PROPERTIES}){
		$params{PROPERTIES} = {};
	}
	
	unless (exists $params{ATTACHMENTS}){
		$params{ATTACHMENTS} = [];
	}
	
	#-----------------------------------------
	# verifica consistenza parametri in input
	#-----------------------------------------

	$self->_check_parameters(%params)
		|| return undef;
	
	#------------------------
	# operazioni sui sistemi
	#------------------------
	
	# creazione
	if (defined $params{SYSTEM_TYPE_NAME}){
		$system = $self->_create_system(%params) || return undef;
	}
	
	# istanziazione
	if (defined $params{SYSTEM_ID}){
		$system = API::ART::System->new( ART => $self->{ART}, ID => $params{SYSTEM_ID} )
			|| return undef;
	}
	
	#---------------------------------------
	# aggiornamento proprieta' del sistema
	#---------------------------------------
	if ($params{UPDATE_SYSTEM_PROPERTIES}){
		
		unless (defined $system){
			croak "sistema non definito";
		}
		
		$self->_update_system_properties(%params, SYSTEM=>$system)
			|| return undef;
	}
	
	#---------------------------
	# operazioni sull'attivita'
	#---------------------------
	if ($params{ACTIVITY_TYPE_NAME}){
		$activity = $self->_create_activity(%params, SYSTEM=>$system)
			|| return undef;
		# azzero la chiave ATTACHMENTS per evitare che gli eventuali step successivi duplichino gli allegati se presenti
		$params{ATTACHMENTS} = [];
	}
	
	if (defined $params{ACTIVITY}){
		$activity = $params{ACTIVITY};
	} elsif (defined $params{ACTIVITY_ID}){
		$activity = $self->_get_activity_by_id(  ART => $self->{ART}, ID => $params{ACTIVITY_ID} )
			|| return undef;
	}
	
	#------------
	# adozione
	#------------
	if (defined $params{PARENT_ID}){
		$self->_adopt_activity(%params, ACTIVITY=> $activity)
			|| return undef;
	}
	
	#----------------
	# activity step
	#----------------
	if (defined $params{ACTION_NAME}){
		$self->_step_activity(%params, ACTIVITY=> $activity)
			|| return undef;
	}
	
	#----------------
	# azioni virtuali
	#----------------
	if (defined $params{STATUS_NAME} || defined $params{VIRTUAL_ACTION_NAME}){
		$self->_virtual_step_activity(%params, ACTIVITY=> $activity)
			|| return undef;
	}
	
	#--------------------------
	# update unused properties
	#--------------------------
	$self->_update_unused_properties($params{PROPERTIES});
	
	#----------------
	# results
	#----------------
	$self->{LastSystem}=$system;
	$self->{LastActivity}=$activity;
	
	return 1;
}

##################
# PUBLIC GETTERS
##################

sub get_last_activity{
	my $self = shift;
	return $self->{LastActivity};
}

sub get_last_system{
	my $self = shift;
	return $self->{LastSystem};
}

sub get_last_used_properties{
	my $self = shift;
	return $self->{USED_PROPERTIES};
}

sub get_last_unused_properties{
	my $self = shift;
	return $self->{UNUSED_PROPERTIES};
}

################################################
#
# METODI PRIVATI
#
################################################

#--------------------
# CHECK PARAMETERS
#--------------------

sub _get_activity_by_id{
	my $self = shift;
	my %params = @_;
	
	# utilizza le binding se richiesto
	return
		$self->{BINDING} ?
			API::ART::Activity::Factory->new( ART => $self->{ART}, ID => $params{ID} ) :
			API::ART::Activity->new( ART => $self->{ART}, ID => $params{ID} );
}

sub _check_parameters{
	my $self = shift;
	my %params = @_;
	
	# SYSTEM_TYPE_NAME e SYSTEM_ID non possono esser settati contemporaneamente
	if (
		defined $params{SYSTEM_TYPE_NAME} &&
		defined $params{SYSTEM_ID}
	){
		$self->{ART}->last_error("_check_parameters: SYSTEM_TYPE_NAME e SYSTEM_ID non possono coesistere");
		return undef;
	}
	
	# e' richiesto un sistema per creare un'attivita'
	if (
		$params{ACTIVITY_TYPE_NAME} &&
		! $params{SYSTEM_TYPE_NAME} &&
		! $params{SYSTEM_ID}
	){
		$self->{ART}->last_error("_check_parameters: ACTIVITY_TYPE_NAME richiede SYSTEM_TYPE_NAME o SYSTEM_ID");
		return undef;
	};
	
	# ACTIVITY_TYPE_NAME e ACTIVITY_ID non possono essere impostati contemporaneamente
	if (
		defined $params{ACTIVITY_TYPE_NAME} &&
		defined $params{ACTIVITY_ID}
	){
		$self->{ART}->last_error("_check_parameters: ACTIVITY_TYPE_NAME e ACTIVITY_ID non possono coesistere");
		return undef;
	}
	
	# ACTION_NAME e STATUS_NAME non possono essere impostati contemporaneamente
	if (
		defined $params{ACTION_NAME} &&
		defined $params{STATUS_NAME}
	){
		$self->{ART}->last_error("_check_parameters: ACTION_NAME e STATUS_NAME non possono coesistere");
		return undef;
	}
	
	# ACTION_NAME e VIRTUAL_ACTION_NAME non possono essere impostati contemporaneamente
	if (
		defined $params{ACTION_NAME} &&
		defined $params{VIRTUAL_ACTION_NAME}
	){
		$self->{ART}->last_error("_check_parameters: ACTION_NAME e VIRTUAL_ACTION_NAME non possono coesistere");
		return undef;
	}
	
	# VIRTUAL_ACTION_FORCED richiede VIRTUAL_ACTION_NAME
	if (
		! defined $params{VIRTUAL_ACTION_NAME} &&
		defined $params{VIRTUAL_ACTION_FORCED}
	) {
		$self->{ART}->last_error("_check_parameters: VIRTUAL_ACTION_FORCED richiede VIRTUAL_ACTION_NAME");
		return undef;
	}
	
	return 1;
}

#-----------------
# SYSTEM
#-----------------

sub _create_system{
	my $self = shift;
    my %params = @_;
    my $errmsg;
	
	# check parametri
	$self->{ART}->last_error("_create_system: " . $errmsg)
		&& return undef
			unless $self->check_named_params(
				PARAMS => \%params
				, MANDATORY => {
					SYSTEM_TYPE_NAME 	=>	{isa => 'SCALAR'},
					SYSTEM_DESCRIPTION 	=>	{isa => 'SCALAR'},
					OBJECT_TYPE_NAME 	=>	{isa => 'SCALAR'},
					SYSTEM_GROUPS		=>	{isa => 'ARRAY'},
				}
				, OPTIONAL => {
			
				}
				, IGNORE_EXTRA_PARAMS => 1
				, ERRMSG =>\$errmsg
			);
	
	return $self->{CollectionSystem}->create(
		SYSTEM_TYPE_NAME	=> $params{SYSTEM_TYPE_NAME},
		DESCRIPTION 		=> $params{SYSTEM_DESCRIPTION},
		GROUPS				=> $params{SYSTEM_GROUPS},
		SYSTEM_CATEGORY_ID	=> 1,
		SYSTEM_CLASS_ID		=> 1,
		OBJECT_TYPE_NAME	=> $params{OBJECT_TYPE_NAME}
	);
}

sub _update_system_properties{
	my $self   = shift;
    my %params = @_;
    my $errmsg;
	
	# check parametri
	$self->{ART}->last_error("_update_system_properties: " . $errmsg)
		&& return undef
			unless $self->check_named_params(
				PARAMS => \%params
				, MANDATORY => {
					SYSTEM 		=>	{isa => 'API::ART::System'},
					PROPERTIES	=> 	{isa => 'HASH'}
				}
				, OPTIONAL => {
			
				}
				, IGNORE_EXTRA_PARAMS => 1
				, ERRMSG =>\$errmsg
			);
	
	unless (scalar keys %{$params{PROPERTIES}}){
		return 1;
	}
	
	my $system = $params{SYSTEM};
	my $system_info = $system->info();
	
	return undef
		unless defined $system_info;
		
	my $system_properties = $system_info->{PROPERTIES};
	
	# FIXME: workaround bug system properties API::ART ?
	$system_properties = [ grep {defined $_ } @{$system_properties} ];
	
	if (defined $params{INCLUDE_SYSTEM_PROPERTIES}) {
		my %tmp;
		@tmp{ @{$params{INCLUDE_SYSTEM_PROPERTIES}} } = ();
		$system_properties = [ grep { exists $tmp{$_} } @{$system_properties} ];
	}

	if (defined $params{EXCLUDE_SYSTEM_PROPERTIES}) {
		my %tmp;
		@tmp{ @{$params{EXCLUDE_SYSTEM_PROPERTIES}} } = ();
		$system_properties = [ grep { ! exists $tmp{$_} } @{$system_properties} ];
	}

	# estrae tutte le properties da aggiornare nel sistema
	my $properties_to_update =
		$self->_filter_properties(
			PROPERTIES 			=> $params{PROPERTIES},
			FILTER_PROPERTIES 	=> $system_properties,
		);
	
	$self->_mark_used_properties($properties_to_update);
	
	return $system->set_property(PROPERTIES=>$properties_to_update);
}

#-----------------
# ACTIVITY
#-----------------

sub _create_activity{
	my $self   = shift;
    my %params = @_;
    my $errmsg;
	
	# check parametri
	$self->{ART}->last_error("_create_activity: " . $errmsg)
		&& return undef
			unless $self->check_named_params(
				PARAMS => \%params
				, MANDATORY => {
					ACTIVITY_TYPE_NAME 		=>	{isa => 'SCALAR'},
					ACTIVITY_DESCRIPTION 	=>	{isa => 'SCALAR'},
					SYSTEM					=>	{isa => 'API::ART::System'},
				}
				, OPTIONAL => {
					ACTIVITY_ID_CREATE		=> 	{isa => 'SCALAR'}
					,ACTIVITY_DATE			=>	{isa => 'SCALAR'}
					,ATTACHMENTS			=>	{isa => 'ARRAY'}
				}
				, IGNORE_EXTRA_PARAMS => 1
				, ERRMSG =>\$errmsg
			);
	
	my $activity_open_properties =
		$self->_filter_activity_properties_by_action(
			PROPERTIES			=> $params{PROPERTIES},
			ACTIVITY_TYPE_NAME	=> $params{ACTIVITY_TYPE_NAME},
			ACTION				=> $self->{ART}->get_activity_action_open_name(),
		) || return undef;
	
	$self->_mark_used_properties($activity_open_properties);
	
	return $self->{CollectionActivity}->create(
		SYSTEM_ID			=> $params{SYSTEM}->id(),
		ID_CUSTOM			=> $params{ACTIVITY_ID_CREATE},
		DESCRIPTION 		=> $params{ACTIVITY_DESCRIPTION},
		ACTIVITY_TYPE_NAME	=> $params{ACTIVITY_TYPE_NAME},
		PROPERTIES			=> $activity_open_properties,
		DATE				=> $params{ACTIVITY_DATE},
		ATTACHMENTS			=> $params{ATTACHMENTS}
	);
}

sub _step_activity{
	my $self = shift;
	my %params = @_;
	my $errmsg;
	
	# check parametri
	$self->{ART}->last_error("_step_activity: " . $errmsg)
		&& return undef
			unless $self->check_named_params(
				PARAMS => \%params
				, MANDATORY => {
					#ACTIVITY 			=>	{isa => 'API::ART::Activity'},
					ACTION_NAME			=>	{isa => 'ARRAY'},
					PROPERTIES			=> 	{isa => 'HASH'},
				}
				, OPTIONAL => {
					STEP_DESCRIPTION			=>	{isa => 'SCALAR'},
					ATTACHMENTS					=>	{isa => 'ARRAY'},
					DISABLE_BINDING_INSIDE_STEP =>  {isa => 'SCALAR'},
					ACTION_FORCED 				=>  {isa => 'SCALAR'}
				}
				, IGNORE_EXTRA_PARAMS => 1
				, ERRMSG =>\$errmsg
			);
	
	$params{ATTACHMENTS} = [] unless defined $params{ATTACHMENTS};
		
	my $activity_info = $params{ACTIVITY}->info();
		
	my $activity_type_name =
			$self->{ART}->get_activity_type_name($activity_info->{ACTIVITY_TYPE_ID});
	my $i = 0;
	for my $action (@{$params{ACTION_NAME}}){
		my $step_date;
		unless($params{ACTIVITY}->can_do_action(NAME => $action)){
			$self->{ART}->last_error(sprintf "_step_activity: attivita nello stato %s non movimentabile tramite azione: %s"
				,$params{ACTIVITY}->get_current_status_name()
				,$action
			);
			return undef;
		}
		
		if (ref(\$params{STEP_DATE}) eq "SCALAR") {
			$step_date = $params{STEP_DATE};
		} else {
			$step_date = $params{STEP_DATE}->[$i];
		}
		$i++;
		
		
		my $activity_step_properties =
			$self->_filter_activity_properties_by_action(
				PROPERTIES			=> $params{PROPERTIES},
				ACTIVITY_TYPE_NAME	=> $activity_type_name,
				ACTION				=> $action,
			) || return undef;
		
		$self->_mark_used_properties($activity_step_properties);
		
		my %step_params = (
			DESCRIPTION 		=> $params{STEP_DESCRIPTION},
			ACTION				=> $action,
			SAVE_ALL_PROPERTIES => $params{SAVE_ALL_PROPERTIES} ? 1 : 0,
			PROPERTIES			=> $activity_step_properties ,
			DATE				=> $step_date,
			ATTACHMENTS			=> $params{ATTACHMENTS},
			IGNORE_FINAL_STATUS	=> $params{ACTION_FORCED}
		);

		# disabilita il binding (consapevolmente) negli step
		$step_params{DISABLE_BINDING_INSIDE_STEP} =
			$params{DISABLE_BINDING_INSIDE_STEP}
				if defined $params{DISABLE_BINDING_INSIDE_STEP};

		$params{ACTIVITY}->step( %step_params ) || return undef;
		# azzero la chiave ATTACHMENTS per evitare che gli eventuali step successivi duplichino gli allegati se presenti
		$params{ATTACHMENTS} = [];
	}
	
	return 1;
}

sub _virtual_step_activity{
	my $self = shift;
	my %params = @_;
	my $errmsg;
	
	# check parametri
	$self->{ART}->last_error("_virtual_step_activity: " . $errmsg)
		&& return undef
			unless $self->check_named_params(
				PARAMS => \%params
				, MANDATORY => {
					#ACTIVITY 				=> {isa => 'API::ART::Activity'},
					PROPERTIES				=> {isa => 'HASH'},
				}
				, OPTIONAL => {
					STATUS_NAME					=> {isa => 'SCALAR'},
					VIRTUAL_ACTION_NAME			=> {isa => 'SCALAR'},
					VIRTUAL_ACTION_FORCED 		=> {isa => 'SCALAR'},
					STEP_DESCRIPTION			=> {isa => 'SCALAR'},
					DISABLE_BINDING_INSIDE_STEP => {isa => 'SCALAR'},
				}
				, IGNORE_EXTRA_PARAMS 		=> 1
				, ERRMSG =>\$errmsg
			);
		
	my $activity_info = $params{ACTIVITY}->info();
	
	my $activity_step_properties =
		$self->_filter_properties(
			PROPERTIES			=> $params{PROPERTIES},
			FILTER_PROPERTIES	=> $activity_info->{PROPERTIES},
		);
	
	$self->_mark_used_properties($activity_step_properties);

	# effettua il controllo SOLO se non si tratta di una forzatura
	unless($params{VIRTUAL_ACTION_FORCED}){
		unless($params{ACTIVITY}->can_update()){
			$self->{ART}->last_error("azione virtuale update non possibile");
			return undef;
		}
	}
	
	# effettua un LOOP se non e' definito lo stato destinazione
	unless (defined $params{STATUS_NAME}){
		$params{STATUS_NAME} = $params{ACTIVITY}->get_current_status_name();
	}
	
	my %update_params = (
		ACTION				=> $params{VIRTUAL_ACTION_NAME},
		IGNORE_FINAL_STATUS	=> $params{VIRTUAL_ACTION_FORCED},
		DEST_STATUS			=> $params{STATUS_NAME},
		DESCRIPTION 		=> $params{STEP_DESCRIPTION},
		PROPERTIES			=> $activity_step_properties,
		SAVE_ALL_PROPERTIES => $params{SAVE_ALL_PROPERTIES} ? 1 : 0,
	);

	# disabilita il binding (consapevolmente) negli step virtuali
	$update_params{DISABLE_BINDING_INSIDE_STEP} =
		$params{DISABLE_BINDING_INSIDE_STEP}
			if defined $params{DISABLE_BINDING_INSIDE_STEP};

	# effettua uno step virtuale
	$params{ACTIVITY}->update( %update_params ) || return undef;
	
	return 1;
}

sub _adopt_activity{
	my $self =shift;
	my %params = @_;
	my $errmsg;
	
	# check parametri
	$self->{ART}->last_error("_adopt_activity: " . $errmsg)
		&& return undef
			unless $self->check_named_params(
				PARAMS => \%params
				, MANDATORY => {
					#ACTIVITY 			=>	{isa => 'API::ART::Activity'},
					PARENT_ID			=>	{isa => 'SCALAR'},
				}
				, OPTIONAL => {
				}
				, IGNORE_EXTRA_PARAMS => 1
				, ERRMSG =>\$errmsg
			);
	
	if ($params{ACTIVITY}->parent_id()){
		$self->{ART}->last_error(sprintf "_adopt_activity: attivita %d gia' figlia di %d"
			,$params{ACTIVITY}->id()
			,$params{ACTIVITY}->parent_id()
		);
		return undef;
	}
	
	my $parent = $self->_get_activity_by_id( ART => $self->{ART}, ID => $params{PARENT_ID} )
		|| return undef;
	
	return $parent->adopt_children(CHILDREN=>[$params{ACTIVITY}]);
}

#-----------------
# PROPERTIES
#-----------------

sub _filter_activity_properties_by_action{
	my $self   = shift;
    my %params = @_;
    my $errmsg;
	
	# check parametri
	$self->{ART}->last_error("_filter_activity_properties_by_action: " . $errmsg)
		&& return undef
			unless $self->check_named_params(
				PARAMS => \%params
				, MANDATORY => {
					PROPERTIES 			=>	{isa => 'HASH'},
					ACTIVITY_TYPE_NAME	=>	{isa => 'SCALAR'},
					ACTION 				=>	{isa => 'SCALAR'},
				}
				, OPTIONAL => {
				}
				, IGNORE_EXTRA_PARAMS => 0
				, ERRMSG =>\$errmsg
			);
			
	my $action_properties_info = $self->{ART}->get_action_properties(
		$params{ACTIVITY_TYPE_NAME},
		$params{ACTION},
	) || return undef;
	
	my @action_properties_names = map {$_->{NOME_TDTA}} @{$action_properties_info};
	
	return $self->_filter_properties(
			PROPERTIES			=> $params{PROPERTIES},
			FILTER_PROPERTIES 	=> \@action_properties_names,
	);
}

sub _filter_properties{
	my $self = shift;
	my %params = @_;
	my $errmsg;
	
	croak "_filter_properties: internal error!"
		unless $self->check_named_params(
			PARAMS => \%params
			, MANDATORY => {
				PROPERTIES 			=>	{isa => 'HASH'},
				FILTER_PROPERTIES	=>	{isa => 'ARRAY'},
			}
			, OPTIONAL => {
			}
			, IGNORE_EXTRA_PARAMS => 0
			, ERRMSG =>\$errmsg
		);
	
	my $filter_properties = {};
	for (@{$params{FILTER_PROPERTIES}}){
		if (exists $params{PROPERTIES}->{$_}){
			$filter_properties->{$_} = $params{PROPERTIES}->{$_}
		}
	}
	
	return $filter_properties;
}

#----------------------------
# USED AND UNUSED PROPERTIES
#----------------------------

sub _mark_used_properties{
	my $self = shift;
	my $used_properties = shift;
	for (keys %{$used_properties}){
		$self->{USED_PROPERTIES}{$_}=$used_properties->{$_};
	}
}

sub _update_unused_properties{
	my $self = shift;
	my $properties = shift;
	for (keys %{$properties}){
		next if exists $self->{USED_PROPERTIES}{$_};
		$self->{UNUSED_PROPERTIES}{$_}=$properties->{$_};
	}
	return 1;
}

#----------------------------
# ATOMIC TRANSACTION
#----------------------------

sub _atomic_transaction{
	my $self = shift;
	my $savepoint_name = shift;
	my $procedure_ref = shift;
	
	$self->_savepoint_begin($savepoint_name);
	
	my $return_value = $procedure_ref->($self, @_);
	
	# si assume che in caso di errore out == undef
	unless (defined $return_value){
		$self->_savepoint_rollback($savepoint_name);
	}
	
	return $return_value;
}

sub _savepoint_begin{
	my $self = shift;
	my $savepoint_name = shift;
	$self->{ART}->_dbh()->do( "savepoint $savepoint_name" );
}

sub _savepoint_rollback{
	my $self = shift;
	my $savepoint_name = shift;
	$self->{ART}->_dbh()->do( "rollback to savepoint $savepoint_name" );
}

######################################################################
# UNIT TEST
######################################################################
if ( __FILE__ eq $0 ) {
	

}

1;
