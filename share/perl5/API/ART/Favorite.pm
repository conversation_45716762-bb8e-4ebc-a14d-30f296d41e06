package API::ART::Favorite;

use strict;
use warnings;
use Carp;
use JSON;

use base qw(API::Ancestor);

our $VERSION = '0.01';

=head1 NAME

B<API::ART::Favorite> - Gestione preferiti

=head1 SYNOPSIS

	# Uses needed packages
	use API::ART;
	use API::ART::Favorite;

	# Create API::ART instance...
	my $art = API::ART->new(.......);
	# Create API::ART::Favorite instance...
	my $a = API::ART::Favorite->new(ART => $art, ID => 349522);

	print "URL: ", $a->url, "\n";

=head1 DESCRIPTION

Questo package consente di accedere alle attivita' di ART attraverso un'interfaccia OO.

=head1 METHODS

Di seguito i metodi esposti dalla classe API::ART::Favorite.

=cut

#
# Metodi privati
#
sub _which_info {
	my ($self, $key, $cache) = @_;
	my $info = $self->_info($cache);
	return $info->{$key} if defined $key;
	return $info;
}
sub _info {
	my ($self) = @_;

	#
	# TODO => ottimizzazione richiesta attraverso lock in modo da non eseguire
	# sempre la query ma solo nel caso in cui l'attivita' non sia lockata dall'utente
	#
	my $date_format = $self->art()->info('DEFAULT_DATE_FORMAT');
	
	my $sql = qq(
		SELECT id_operatore USER_ID,
			url,
			descrizione DESCRIPTION,
			chiavi_aggiuntive ADDITIONAL_KEYS,
			TO_CHAR (cast(DATA_CREAZIONE as date), ?) CREATION_DATE,
			TO_CHAR (cast(DATA_CANCELLAZIONE as date), ?) CANCEL_DATE,
			TO_CHAR (cast(data_ultimo_aggiornamento as date), ?) LAST_UPDATE_DATE
		FROM   preferiti p
		WHERE  p.id = ?
	);
	$self->{ART}->_create_prepare(__PACKAGE__.'_info', $sql);
	
	my $res = $self->{ART}->_create_prepare(__PACKAGE__.'_info')->fetchall_hashref(
		 $self->art()->get_default_date_format(),
		 $self->art()->get_default_date_format(),
		 $self->art()->get_default_date_format(),
		 $self->{ID},
	);
	$self->{ART}->last_error(__PACKAGE__." : FAVORITE_ID ".$self->{ID}." not found!")
		&& return undef
			unless scalar @{$res};

	$self->{INFO} = $res->[0];

	$self->{INFO}->{ADDITIONAL_KEYS} = defined $self->{INFO}->{ADDITIONAL_KEYS} ? from_json($self->{INFO}->{ADDITIONAL_KEYS}) : {};
	
	return $self->{INFO};
}

=head2 I<API::ART::Activity>->B<new>( ART => I<API::ART> , ID => I<activity_id> [ , SAVE_ALL_PROPERTIES => I<save> ] )

Il metodo B<new()> e' il costruttore di classe e richiede due argomenti obbligatori:

=over 4

=item B<ART>

Un'istanza della classe API::ART

=item B<ID>

L'ID del preferito da utilizzare

=back

=cut
sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	#
	# ART
	# ID
	#
	# Sintassi
	my $usage = sub {
		my $errmsg = shift;
		my $msg    = "";
		$msg .= "$class : $errmsg\n\n" if defined $errmsg;
		$msg .= "Usage:\n";
		$msg .= "\t$class";
		$msg .= '->new( ART => API::ART->new(), ID => $id )';
		$msg .= "\n\n";
		croak $msg;
	};

	# Controlli sui parametri
	$usage->('Missing ART!') unless ref($params->{ART}) eq 'API::ART';
	$usage->('Missing ID!') unless defined $params->{ID};
	
	my $self  = $class->SUPER::new();
	
	$self->{ART} = $params->{ART};
	$self->{ID} = $params->{ID};
	$self->{ART}->clear_last_error();

	return undef unless $self->info();
	
	return bless( $self, $class );
}


=head2 I<object>->B<art>()

Ritorna il riferimento all'oggetto API::ART passato come argomento al costruttore.

=cut
sub art { return $_[0]->{ART} }


=head2 I<object>->B<id>()

Ritorna l'ID del preferito.

=cut
sub id { return $_[0]->{ID} }

=head2 I<object>->B<info>()

B<info>() ritorna informazioni sull'istanza dell'oggetto API::ART::Favorite rileggendole dal DB.

Senza argomenti ritornano un I<hashref> contenente tutte le informazioni disponibili:

=over 4

=item B<USER_ID>

ID dell'operatore

=item B<URL>

L'url del preferito

=item B<DESCRIPTION>

Descrizione del preferito

=item B<ADDITIONAL_KEYS>

Chiavi aggiuntive del preferito

=item B<CREATION_DATE>

Data creazione del preferito

=item B<CANCEL_DATE>

Data cancellazione del preferito

=item B<LAST_UPDATE_DATE>

Data ultimo aggiornamento del preferito

=back

=cut

sub info {
	my ($self, $key) = @_;
	return $self->_which_info((defined $key ? $key : undef));
}

=head2 I<object>->B<url>()

Ritorna l'url del preferito.

=cut
sub url { $_[0]->info('URL') }

=head2 I<object>->B<description>()

Ritorna la descrizione del preferito.

=cut
sub description { $_[0]->info('DESCRIPTION') }

=head2 I<object>->B<user_id>()

Ritorna la descrizione del preferito.

=cut
sub user_id { $_[0]->info('USER_ID') }

=head2 I<object>->B<additional_keys>()

Ritorna la struttura delle chiavi addizionali

=cut
sub additional_keys { $_[0]->info('ADDITIONAL_KEYS') }

=head2 I<object>->B<creation_date>()

Ritorna la data di creazione del preferito

=cut
sub creation_date { $_[0]->info('CREATION_DATE') }

=head2 I<object>->B<cancel_date>()

Ritorna la data di cancellazione del preferito

=cut
sub cancel_date { $_[0]->info('CANCEL_DATE') }

=head2 I<object>->B<last_update_date>()

Ritorna la data di ultimo aggiornamento del preferito

=cut
sub last_update_date { $_[0]->info('LAST_UPDATE_DATE') }

=head2 I<object>->B<update>( [DESCRIPTION=>I<scalar>, URL=>I<scalar>, ADDITIONAL_KEYS=>I<hashref>] )

Esegue l'aggiornamento del preferito

Di seguito il significato degli argomenti del metodo B<update()>:

=over 4

=item B<DESCRIPTION>

Aggiorna la descrizione

=item B<URL>

Aggiorna l'url

=item B<ADDITIONAL_KEYS>

Aggiorna le chiavi aggiuntive (in formato JSON)

B<ATTENZIONE!> - Le precedente chiavi vengono completamente sovrascritte dalle nuove

=back

=cut

sub update {
	my ( $self, %params ) = @_;
	my $errmsg;
	
	$self->art()->last_error($errmsg)
		&& return undef
			unless	$self->check_named_params(
				ERRMSG 				=> \$errmsg
				,IGNORE_EXTRA_PARAMS 	=> 0
				,PARAMS 				=> \%params
				,MANDATORY => {}
				,OPTIONAL => {
					DESCRIPTION => { isa => 'SCALAR' },
					URL => { isa => 'SCALAR' },
					ADDITIONAL_KEYS => { isa => 'HASH' },
				}
			);
	
	for my $k ('DESCRIPTION', 'URL'){
		if (defined $params{$k} && $params{$k} eq ''){
			$self->{ART}->last_error("Field ".$k." can not be empty");
			return undef;
		}
	}

	# verifico che non esista già un preferito con queste caratteristiche
	my $sql = qq(
		SELECT 1
		FROM   preferiti p
		WHERE  p.id != ?
			and p.id_operatore = ?
			and p.data_cancellazione is null
			and (
				p.descrizione = ?
				or
				p.url = ?
			)
	);
	$self->{ART}->_create_prepare(__PACKAGE__.'_check', $sql);

	my $res = $self->{ART}->_create_prepare(__PACKAGE__.'_check')->fetch_minimalized(
		$self->id(),
		$self->{ART}->user()->id(),
		defined $params{DESCRIPTION} ? $params{DESCRIPTION} : $self->{INFO}->{DESCRIPTION},
		defined $params{URL} ? $params{URL} : $self->{INFO}->{URL},
	);
	$self->{ART}->last_error("Favorite with the same description or url already exists!")
		&& return undef
			if $res;

	my $savepoint_name =
		sprintf("FAVORITE_UPDATE");
	
	# la transazione sul DB inizia con il primo step
	$self->art()->_dbh()->do( "savepoint $savepoint_name" );

	my $prepare = $self->art()->_create_prepare(__PACKAGE__.'__update', "
		update preferiti
		set descrizione = ?
			,url = ?
			,chiavi_aggiuntive = ?
			,data_ultimo_aggiornamento = systimestamp
		where id = ?
	");

	my @bind_values = (
		defined $params{DESCRIPTION} ? $params{DESCRIPTION} : $self->{INFO}->{DESCRIPTION},
		defined $params{URL} ? $params{URL} : $self->{INFO}->{URL},
		defined $params{ADDITIONAL_KEYS} ? to_json($params{ADDITIONAL_KEYS}) : to_json($self->{INFO}->{ADDITIONAL_KEYS}),
		$self->id
	);

	eval{$self->art()->_create_prepare(__PACKAGE__.'__update')->execute(@bind_values)};
	if(SIRTI::DB::iserror()) {
		$self->art()->last_error("Unable to update favorite: ".SIRTI::DB::get_errormessage());
		return undef;
	}

	# aggiorno le info
	$self->info();

	return 1;
}

=head2 I<object>->B<cancel>()

Esegue la cancellazione del preferito

=cut

sub cancel {
	my ( $self, %params ) = @_;
	my $errmsg;

	$self->art()->last_error("Favorite already cancelled")
		&& return undef
			if ($self->cancel_date());

	my $savepoint_name =
		sprintf("FAVORITE_UPDATE");
	
	# la transazione sul DB inizia con il primo step
	$self->art()->_dbh()->do( "savepoint $savepoint_name" );

	my $prepare = $self->art()->_create_prepare(__PACKAGE__.'__cancel', "
		update preferiti
		set data_cancellazione = systimestamp
		where id = ?
	");

	my @bind_values = (
		$self->id
	);

	eval{$self->art()->_create_prepare(__PACKAGE__.'__cancel')->execute(@bind_values)};
	if(SIRTI::DB::iserror()) {
		$self->art()->last_error("Unable to cancel favorite: ".SIRTI::DB::get_errormessage());
		return undef;
	}

	# aggiorno le info
	$self->info();

	return 1;
}

if (__FILE__ eq $0) {

	use API::ART;
	use Data::Dumper;

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			PASSWORD => 'pippo123',
			USER => 'root',
			DEBUG => 0
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $f = API::ART::Favorite->new(ART => $art, ID => 3);
	
	if( defined $f) {
		print STDERR "id = ".$f->id()."\n";
		print STDERR "user_id = ".$f->user_id()."\n";
		print STDERR "description = ".$f->description()."\n";
		print STDERR "url = ".$f->url()."\n";
		print STDERR "creation_date = ".$f->creation_date()."\n";
		print STDERR "last_update_date = ".$f->last_update_date()."\n";
		print STDERR "cancel_date = ".$f->cancel_date()."\n"
			if defined $f->cancel_date();
		print STDERR "additional_keys = ".Dumper($f->additional_keys())."\n";
		if ($f->update(
			DESCRIPTION => 'Pippo2'
		)){
			print STDERR "update ok\n";
		} else {
			print STDERR $art->last_error();
			die;
		}

	} else {
		print STDERR $art->last_error();
		die;
	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
