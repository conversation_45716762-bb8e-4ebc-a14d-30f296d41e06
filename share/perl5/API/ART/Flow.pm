package API::ART::Flow;
use strict;
use warnings;
use utf8;
use open qw(:std :utf8);
use Tie::IxHash;
use API::ART::Flow::Transition;

sub new {
    my ( $this, %param ) = @_;
    my $class = ref $this || $this;
    my $self = {
        __ART__ => $param{ART},
        __DB__  => $param{ART}->_dbh,
        __O__   => $param{OPTION},
        __TA__  => $param{TIPO_ATTIVITA},
    };
    bless $self, $class;
    if ( exists $param{FILE} ) {
        if ( -f $param{FILE} ) {
            $self->{FLUSSO} = $self->flusso_da_file($param{FILE});
        } else {
            die sprintf 'Input file not found: %s', $param{FILE};
        }
    }
    return $self;
}

sub art { return shift->{__ART__}; }
sub db { return shift->{__DB__}; }
sub tipo_attivita { return shift->{__TA__}; }

sub tipi_sistema {
    my ( $self ) = @_;
    if ( !exists $self->{__TS__} ) {
        my $p = $self->db->create_prepare(q{
            select  ts.nome_tipo_sistema
            from    tipi_sistema ts
            join    attivita_per_tipi_sistema apts
            on      apts.id_tipo_sistema = ts.id_tipo_sistema
            where   apts.id_tipo_attivita = (
                select  ta.id_tipo_attivita
                from    tipi_attivita ta
                where   ta.nome_tipo_attivita = ?
                and     rownum < 2
            )
        });
        my $result = $p->fetchall_arrayref( $self->tipo_attivita );
        $p->finish;
        $self->{__TS__} = [ map { $_->[0] } @{$result} ];
    }
    return $self->{__TS__};
}

sub option {
    my ($self, $name) = @_;
    return defined $name ? $self->{__O__}->{$name} : $self->{__O__};
}

# Leggere da YAML
sub flusso_da_file {
    my ( $self, $file ) = @_;
    my $tipo_attivita;
    my @result;
    my $azione_corrente;
    my $index_tdta;
    open my $in, '<', $file or die $!;
    while ( <$in> ) {

        s{\/\/.*}{};
        next if /^\s*$/;

        if ( !defined $tipo_attivita and /\S/xms ) {
            ( $tipo_attivita ) = m{^ \s* (\S+) \s* $ }xms;
            $self->{__TA__} = $tipo_attivita;
            next;
        } elsif ( /^ \s+ ==== \s+ (\S+) \s*$/xms ) {
            push @{$self->{__TS__}}, $1;
            next;
        } elsif ( /^ \s+ ([-*]) \s* (\S+) \s* (\S+) \s* $/xms ) {
            die 'Errore Interno' unless defined $azione_corrente;
            if ( defined $azione_corrente ) {
                if ( $result[-1]->[4] ) {
                    $result[-1]->[4] .= sprintf ',%d:%s:%s:%s',
                        $index_tdta++,
                        $3,
                        $2,
                        $1 eq '*' ? 'Y' : 'N',
                    ;
                } else {
                    $result[-1]->[4] .= sprintf '%d:%s:%s:%s',
                        $index_tdta++,
                        $3,
                        $2,
                        $1 eq '*' ? 'Y' : 'N',
                    ;
                }
            }
        }
        next unless /->/xms;

        chomp;
        my ( $stato_iniziale, $azione, $stato_finale, $gruppi_abilitati ) = m{
            ^
            \s* (\S+) \s*
                ->
            \s* (\S+) \s*
                ->
            \s* (\S+) \s*
            (.*)
            \s*
            $
        }xms;
        $azione_corrente = $azione;
        $gruppi_abilitati =~ s{\s+}{,}g;
        $index_tdta = 0;
        push @result, [
            $stato_iniziale,
            $azione,
            $stato_finale,
            $gruppi_abilitati,
            '',
        ];
    }

    my $flusso = [ map { API::ART::Flow::Transition->new($_) } @result ];

    return $flusso;
}

sub flusso {
    my ( $self ) = @_;

    if ( !exists $self->{FLUSSO} ) {
=pod
        my $p = $self->db->create_prepare(q{
            select  v.stato_iniziale,
                    v.action,
                    v.stato_finale,
                    v.gruppi,
                    listagg( (
                        select  distinct
                                      tdtaa.posizione
                            || '§' || tdta.descrizione
                            || '§' || tdta.etichetta
                            || '§' || tdta.tipo_ui
                            || '§' || tdtaa.obbligatorio
                            || '§' || tdtaa.solo_lettura
                            || '§' || tdtaa.nascosto
                            || '§' || tdtaa.prepopola
                            || '§' || tdta.valori
                            || '§' || tdta.valore_default
                            || '§' || tdta.valore_predefinito
                            || '§' || tdta.suggerimento
                        from    tipi_dati_tecnici_attivita tdta
                        where   tdta.id_tipo_dato_tecnico_attivita = tdtaa.id_tipo_dato_tecnico_attivita
                        and     tdtaa.id_action = v.id_action_permessa
                    ),'¶') within group (order by 1) tdta
            from (
                select  pa.id_tipo_attivita,
                        pa.id_action_permessa,
                        (select si.nome from stati  si where si.id_stato  = pa.id_stato_iniziale ) stato_iniziale,
                        (select ac.nome from action ac where ac.id_action = pa.id_action_permessa) action,
                        (select sf.nome from stati  sf where sf.id_stato  = pa.id_stato_finale   ) stato_finale,
                        listagg ( (
                            select  g.nome
                            from    gruppi g
                            where   g.id_gruppo = pa.id_gruppo_abilitato
                        ),'§') within group (order by 1) gruppi
                from    permission_action pa
                where   pa.id_tipo_attivita = (
                    select  ta.id_tipo_attivita
                    from    tipi_attivita ta
                    where   ta.nome_tipo_attivita = ?
                    and     rownum < 2
                )
                group by pa.id_tipo_attivita, pa.id_stato_iniziale, pa.id_action_permessa, pa.id_stato_finale
            ) v
            left join   tipi_dati_tecnici_att_action tdtaa
            on          tdtaa.id_action = v.id_action_permessa
            and         tdtaa.id_tipo_attivita = v.id_tipo_attivita
            group by v.stato_iniziale, v.action, v.stato_finale, v.gruppi
        });
=cut
        my $p = $self->db->create_prepare(q{
            select  v.stato_iniziale,
                    v.action,
                    v.stato_finale,
                    v.gruppi
            from (
                select  pa.id_tipo_attivita,
                        pa.id_action_permessa,
                        (select si.nome from stati  si where si.id_stato  = pa.id_stato_iniziale ) stato_iniziale,
                        (select ac.nome from action ac where ac.id_action = pa.id_action_permessa) action,
                        (select sf.nome from stati  sf where sf.id_stato  = pa.id_stato_finale   ) stato_finale,
                        listagg ( (
                            select  g.nome
                            from    gruppi g
                            where   g.id_gruppo = pa.id_gruppo_abilitato
                        ),'§') within group (order by 1) gruppi
                from    permission_action pa
                where   pa.id_tipo_attivita = (
                    select  ta.id_tipo_attivita
                    from    tipi_attivita ta
                    where   ta.nome_tipo_attivita = ?
                    and     rownum < 2
                )
                group by pa.id_tipo_attivita, pa.id_stato_iniziale, pa.id_action_permessa, pa.id_stato_finale
            ) v
            order by v.stato_iniziale, v.action, v.stato_finale
        });
        my $ptdta = $self->db->create_prepare(q{
            select  distinct
                tdtaa.posizione,
                tdta.descrizione,
                tdta.etichetta,
                tdta.tipo_ui,
                tdtaa.obbligatorio,
                tdtaa.solo_lettura,
                tdtaa.nascosto,
                tdtaa.prepopola,
                tdta.valori,
                tdta.valore_default,
                tdta.valore_predefinito,
                tdta.suggerimento
            from    tipi_dati_tecnici_att_action tdtaa
            join    tipi_dati_tecnici_attivita tdta
            on      tdta.id_tipo_dato_tecnico_attivita = tdtaa.id_tipo_dato_tecnico_attivita
            where   tdtaa.id_action = (
                select  ac.id_action
                from    action ac
                where   ac.nome = ?
                and     rownum < 2
            )
            and     tdtaa.id_tipo_attivita = (
                select  ta.id_tipo_attivita
                from    tipi_attivita ta
                where   ta.nome_tipo_attivita = ?
                and     rownum < 2
            )
            order by tdtaa.posizione
        });
        my $result = $p->fetchall_arrayref( $self->tipo_attivita );
        $p->finish;
        for my $record ( @{$result}) {
            my ( $stato_iniziale, $action, $stato_finale, $gruppi ) = @{$record};
            # FIXME Provvisorio retrocompatibile
            my $tdta = $ptdta->fetchall_arrayref( $action, $self->tipo_attivita );
            if ( @{$tdta} ) {
                my $string = join('¶',map { join('§',map { $_ //= '' } @{$_}) } @{$tdta});
                push @{$record}, $string;
            }
        }
        $self->{FLUSSO} = [ map { API::ART::Flow::Transition->new($_) }  @{$result} ];
        #$self->{FLUSSO} = $result; #FIXME
    }
    return $self->{FLUSSO};
}

# Breadth-first search
sub flusso_ordinato {
    my ( $self ) = @_;
    if (!exists $self->{FLUSSO_ORDINATO} ) {
        my $flusso_input = $self->flusso;
        my $flusso_tmp = {};
        for my $transizione ( @{$flusso_input} ) {
            my $stato_iniziale = $transizione->stato_iniziale;
            push @{$flusso_tmp->{$stato_iniziale}}, $transizione;
        }
        my $flusso = [];
        my @stack = qw(START);
        my $seen = { START => 0 };
        while ( @stack ) {
            while ( my $status = shift @stack ) {
                my $transizioni = $flusso_tmp->{$status};
                for my $transizione ( @{$transizioni} ) {
                    $transizione->level($seen->{$status}) if $self->option('level');
                    push @{$flusso}, $transizione;
                    my $stato_finale = $transizione->stato_finale;
                    unless ( exists $seen->{$stato_finale} ) {
                        push @stack, $stato_finale;
                        $seen->{$stato_finale} = $seen->{$status}+1;
                    }
                }
            }
        }
        $self->{FLUSSO_ORDINATO} = $flusso;
    }
    return $self->{FLUSSO_ORDINATO};
}

# Breadth-first search, versione senza oggetti
sub flusso_strutturato {
    my ( $self ) = @_;
    if (!exists $self->{FLUSSO_STRUTTURATO} ) {
        my $flusso_input = $self->flusso;
        my $flusso_tmp = {};
        for my $transizione ( @{$flusso_input} ) {
            my $stato_iniziale = $transizione->stato_iniziale;
            push @{$flusso_tmp->{$stato_iniziale}}, $transizione;
        }
        my $flusso = [];
        my @stack = qw(START ___ANY_STATUS___);
        my $seen = { START => 0, ___ANY_STATUS___ => -1  };
        while ( @stack ) {
            while ( my $status = shift @stack ) {
                my $transizioni = $flusso_tmp->{$status};
                for my $transizione ( @{$transizioni} ) {
                    my $struttura = $transizione->struttura;
                    if ( $self->option('level') ) {
                        $struttura->{level} = $transizione->level($seen->{$status});
                    }
                    push @{$flusso}, $struttura;
                    my $stato_finale = $transizione->stato_finale;
                    unless ( exists $seen->{$stato_finale} ) {
                        push @stack, $stato_finale;
                        $seen->{$stato_finale} = $seen->{$status}+1;
                    }
                }
            }
        }
        # Aggiunge in coda le transizioni orfane non raggiungibili dagli stati iniziali
        while ( my ( $stato_iniziale, $transizioni ) = each %{$flusso_tmp} ) {
            if ( !exists $seen->{$stato_iniziale} ) {
                push @{$flusso}, map { $_->struttura } @{$transizioni};
            }
        }
        tie my %structure, 'Tie::IxHash', (
            tipo_attivita => $self->tipo_attivita,
            tipi_sistema  => $self->tipi_sistema,
            transizioni   => $flusso,
        );
        $self->{FLUSSO_STRUTTURATO} = \%structure;
    }
    return $self->{FLUSSO_STRUTTURATO};
}

# Pieno di regole ad-hoc
sub flusso_in_stadi {
    my ( $self ) = @_;
    if ( !exists $self->{FLUSSO_IN_STADI} ) {
        $self->{__SEEN__} = {};
        $self->{__IDENTIFIED__} = {};
        $self->{__STADI__} = [];
        $self->{FLUSSO_IN_STADI} = [];
        for my $transizione ( @{$self->flusso_ordinato} ) {
            my $stadio = $self->identifica_stadio( $transizione );
            $self->push_stadio( $transizione, $stadio );
        }
    }
    return $self->{FLUSSO_IN_STADI};
}

sub push_stadio {
    my ( $self, $transizione, $stadio ) = @_;
    if ( defined $stadio ) {
        if ( !$self->{__SEEN__}->{$stadio} ) {
            printf "Stadio Identificato: %s\n", $stadio if $self->option('verbose');
            push @{$self->{__STADI__}}, $stadio;
            push @{$self->{__SEEN__}->{$stadio}}, $transizione;
            push @{$self->{FLUSSO_IN_STADI}}, { $stadio => $self->{__SEEN__}->{$stadio} };
        } else {
            push @{$self->{__SEEN__}->{$stadio}}, $transizione;
        }
    } else {
        push @{$self->{FLUSSO_IN_STADI}}, $transizione;
    }
    return;
}

###################################################################################################
###################################################################################################
###                                                                                             ###
### identifica_stadio:                                                                          ###
### Contiene le regole ad-hoc per identificare nel flusso uno stadio e gli stati corrispondenti ###
###                                                                                             ###
###################################################################################################
###################################################################################################
sub identifica_stadio {
    my ( $self, $transizione ) = @_;
    my $stadio;
    my $stato_iniziale = $transizione->stato_iniziale;
    my $action         = $transizione->action;
    my $stato_finale   = $transizione->stato_finale;
#########
# AVVIO #
#########
    if (
            (   $stato_iniziale eq 'ATTESA_AVVIO_DISMISSIONE' and $stato_finale eq 'AVVIO_DISMISSIONE' )
                or
            (   $stato_iniziale eq 'AVVIO_DISMISSIONE' and $stato_finale eq 'ATTESA_TIMEOUT_AVVIO' )
                or
            (   $stato_iniziale eq 'AVVIO_DISMISSIONE' and $action eq 'AVVIO' )
     ) {
        $stadio = 'AVVIO';
################
# DIFFERIMENTO #
################
    } elsif (
        $stato_finale eq 'DIFFERIMENTO'
    ) {
        $stadio = 'DIFFERIMENTO';
#######################
# CONF_BILLING_NO_GNP #
#######################
    } elsif (
        $stato_finale eq 'ATTESA_AVVIO_CONF_BILLING_NO_GNP'
    ) {
        $stadio = 'CONF_BILLING_NO_GNP';
###############
# CALCOLO_DAC #
###############
    } elsif (
        $stato_iniziale eq 'CALCOLO_DAC_IN_CORSO'
    ) {
        $stadio = 'CALCOLO_DAC';
#######################
# VERIFICA_CONFORMITA #
#######################
    } elsif ( $action eq 'RIAVVIO_VERIFICA_CONFORMITA' ) {
        $stadio = 'VERIFICA_CONFORMITA';
##################
# STADI standard #
##################
    } elsif (
        $stato_iniziale =~ m{^ATTESA_(?:AVVIO_)?(.*)$}
            and
        $stato_finale !~ m{^(?:ERRORE$|RECESSO$|ANNULLATO$|ATTESA_)}
    ) {
        $stadio = $1;
##########################
# STADI già identificati #
##########################
    } else {
        my $flag;
        LS: for my $test (@{$self->{__STADI__}}) {
            if (
                (
                    $stato_iniziale eq "ATTESA_AVVIO_${test}"
                        or
                    $stato_iniziale eq "ATTESA_${test}"
                        or
                    $stato_iniziale eq "${test}_IN_CORSO"
                        or
                    $stato_iniziale eq "${test}_KO"
                ) and (
                    $stato_finale eq "ATTESA_AVVIO_${test}"
                        or
                    $stato_finale eq "ATTESA_${test}"
                        or
                    $stato_finale eq "${test}_OK"
                        or
                    $stato_finale eq "CHECKPOINT_${test}_OK"
                        or
                    $stato_finale eq "${test}_KO"
                )
            ) {
            #if ( $stato_iniziale =~ m{(?:^(?:CHECKPOINT_)?${test}_(?:IN_CORSO|OK|KO)|_${test}$)} and $stato_finale =~ m{(?:^(?:CHECKPOINT_)?${test}_|_${test}$)} ) {
                $stadio = $test;
                last LS;
            }
        }
    }

    # Identificazione della seconda fetta di un panino (emistadio ESITO)
    if ( defined $stadio ) {
        my $stadio_esito = sprintf 'ESITO_%s', $stadio;
        if ( $self->{__IDENTIFIED__}->{$stadio_esito} ) {
            my $sso = sprintf '%s_OK', $stadio;
            my $ssk = sprintf '%s_KO', $stadio;
            my $sav = sprintf 'ATTESA_AVVIO_%s', $stadio;
            if ( $stato_iniziale eq $sso or $stato_finale eq $sso or $stato_iniziale eq $ssk or $stato_finale eq $ssk ) {
                if ( $stato_finale eq $sav ) {
                    undef $stadio;
                } else {
                    $stadio = $stadio_esito;
                }
            }
        }

        $self->{__IDENTIFIED__}->{$stadio} = 1 if defined $stadio;
    }
    return $stadio;
}

1;
