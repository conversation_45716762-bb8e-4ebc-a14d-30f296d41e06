package API::ART::Flow::Output::DOT;
use strict;
use warnings;
use utf8;
use open qw(:std :utf8);
use YAML;

my $action_cfg = {
    normal => {
        color => 'black',
        fontcolor => 'black',
    },
    ok => {
        color => '#00c000',
        fontcolor => '#00c000',
        #tailport => 'sw',
        #headport => 'nw',
    },
    ko => {
        color => '#c00000',
        fontcolor => '#c00000',
        #tailport => 'se',
        #headport => 'ne',
    },
    'APERTURA' => {
        color => 'blue',
        fontcolor => 'blue',
        #weight => 1000,
        #constraint => 'false',
        #minlen => 3,
    },
    'ANNULLAMENTO' => {
        color => '#c00000',
        fontcolor => '#c00000',
        style => 'dashed',
        #weight => 1000,
        #constraint => 'false',
        #minlen => 3,
    },
};

my $state_cfg = {
    normal => {
        fillcolor => 'white',
        color     => 'black',
        style     => 'filled',
        shape     => 'ellipse',
        fontcolor => 'black',
    },
    ok => {
        color     => 'black',
        style     => 'filled',
        shape     => 'ellipse',
        fontcolor => 'black',
    },
    ko => {
        fillcolor => '#ffc0c0',
        color     => 'black',
        style     => 'filled',
        shape     => 'ellipse',
        fontcolor => 'black',
    },
    '___ANY_STATUS___' => {
        fillcolor => 'white',
        color     => 'purple',
        style     => 'filled',
        shape     => 'circle',
        fontcolor => 'purple',
    },
    'START' => {
        fillcolor => 'blue',
        color     => 'black',
        style     => 'filled',
        shape     => 'diamond',
        fontcolor => 'white',
    },
    'APERTA' => {
        fillcolor => '#f0fff0',
        color     => '#00c000',
        style     => 'filled',
        shape     => 'ellipse',
        fontcolor => 'black',
    },
    'ANNULLATO' => {
        fillcolor => '#fff0f0',
        color     => '#c00000',
        style     => 'filled',
        shape     => 'rectangle',
        fontcolor => 'black',
    },
    'ANNULLATA' => {
        fillcolor => '#fff0f0',
        color     => '#c00000',
        style     => 'filled',
        shape     => 'rectangle',
        fontcolor => 'black',
    },
    'ERROR' => {
        fillcolor => '#fff0f0',
        color     => '#c00000',
        style     => 'filled',
        shape     => 'rectangle',
        fontcolor => 'black',
    },
    'CHIUSA' => {
        fillcolor => 'grey',
        color     => 'black',
        style     => 'filled',
        shape     => 'rectangle',
        fontcolor => 'black',
    },
    'DONE' => {
        fillcolor => 'grey',
        color     => 'black',
        style     => 'filled',
        shape     => 'rectangle',
        fontcolor => 'black',
    },
};

sub new {
    my ( $this, %param ) = @_;
    my $class = ref $this || $this;
    my $self = bless { %param }, $class;
    return $self;
}

sub option {
    my ( $self, $key ) = @_;
    return $self->{option}->{$key};
}

sub header {
    my ( $self, $tipo_attivita, $graph_name ) = @_;
    my $header = sprintf q(digraph %s {
    label="%s";
    labelloc="t";
    margin=0.0;
    center=1;
    rankdir="TB";
    ratio="auto";
    splines="true";
    remincross="true";
    center="true";
    nodesep=0.125;
    fontname="Calibri,Helvetica";
    fontsize=14;
    style="bold";
    //concentrate="true";
    ),  $graph_name, $tipo_attivita;
    return $header;
}

sub footer {
    my ( $self, $tipo_attivita, $graph_name ) = @_;
    my $footer = qq(}\n);
    return $footer;
}


sub action_config {
    my ( $self, $action ) = @_;
    my $cfg;
    if ( $action =~ m/OK/i ) {
        $cfg = $action_cfg->{ok};
    } elsif ( $action =~ m/KO/i ) {
        $cfg = $action_cfg->{ko};
    } else {
        $cfg = exists $action_cfg->{$action} ? $action_cfg->{$action} : $action_cfg->{normal};
    }
    return %$cfg;
}

sub state_config {
    my ( $self, $state ) = @_;
    my $cfg;
    if ( $state =~ m/OK/i ) {
        $cfg = $state_cfg->{ok};
    } elsif ( $state =~ m/KO/i ) {
        $cfg = $state_cfg->{ko};
    } else {
        $cfg = exists $state_cfg->{$state} ? $state_cfg->{$state} : $state_cfg->{normal};
    }
    return %$cfg;
}

sub scrivi_flusso {
    my ( $self, $flusso ) = @_;
    my %seen;
    my @stati;
    my $tipo_attivita = $flusso->{tipo_attivita};
    ( my $graph_name = $tipo_attivita ) =~ s{[:\?]}{_}g;
    my $dotfile = sprintf '%s.dot', $graph_name;
    my $outfile = sprintf '%s.%s',  $graph_name, $self->option('output_format');

    open my $out, '>', $dotfile or die $!;
    my $header = $self->header( $tipo_attivita, $graph_name );
    print $out $header;

    for my $t ( @{$flusso->{transizioni}} ) {
        my %config = $self->action_config($t->{action});
        unless ( grep { $_ !~ m{^(ROOT|ADMIN)$} } @{$t->{gruppi}} ) {
            $config{style} = 'dashed';
        }
        printf $out qq(%s"%s" -> "%s" [label="%s",%s]\n),
            ' ' x 8,
            $t->{stato_iniziale},
            $t->{stato_finale},
            $t->{action},
            join(',',map { sprintf '%s="%s"', $_, $config{$_} } keys %config ),
        ;
        push @stati, $t->{stato_iniziale} unless $seen{$t->{stato_iniziale}};
        $seen{$t->{stato_iniziale}}++;
        push @stati, $t->{stato_finale} unless $seen{$t->{stato_finale}};
        $seen{$t->{stato_finale}}++;
    }
    for my $s ( @stati ) {
        my %config = $self->state_config($s);
        printf $out qq(%s"%s" [%s]\n),
            ' ' x 4,
            $s,
            join(',',map { sprintf '%s="%s"', $_, $config{$_} } keys %config ),
        ;
    }
    my $footer = $self->footer( $tipo_attivita, $graph_name );
    print $out $footer;
    close $out or die $!;

    if ( $self->option('output_format') ne 'none' ) {
        if ( $self->option('output_format') ne 'pdf' ) {
            open STDOUT, '>', $outfile or die $!;
            system($self->option('engine'),'-T',$self->option('output_format'),$dotfile) == 0 or
                die sprintf "Errore nell'esecuzione del comando esterno '%s': %s\nrieseguire il comando %s con il parametro --keep ed eseguire manualmente '%s'", $self->option('engine'), $?, $0, $self->option('engine');
        } else {
            my $ps2file = sprintf '%s.ps2',  $graph_name;
            open STDOUT, '>', $ps2file or die $!;
            system($self->option('engine'),'-T','ps2',$dotfile) == 0 or
                die sprintf "Errore nell'esecuzione del comando esterno '%s': %s\nrieseguire il comando %s con il parametro --keep ed eseguire manualmente '%s'", $self->option('engine'), $?, $0, $self->option('engine');
            system('ps2pdf',$ps2file,$outfile);
            unlink $ps2file;
        }
    }
    unlink $dotfile or die $! unless $self->option('keep_dotfile');
    close(STDOUT);

    return;
}

1;
