package API::ART::Flow::SystemType;
use strict;
use warnings;
use utf8;
use open qw(:std :utf8);
use Tie::IxHash;

sub new {
    my ( $this, %param ) = @_;
    my $class = ref $this || $this;
    my $self = {
        __ART__ => $param{ART},
        __DB__  => $param{ART}->_dbh,
        __O__   => $param{OPTION},
        __TS__  => $param{TIPO_SISTEMA},
    };
    bless $self, $class;
    return $self;
}

sub art { return shift->{__ART__}; }
sub db { return shift->{__DB__}; }
sub tipo_sistema { return shift->{__TS__}; }

sub option {
    my ($self, $name) = @_;
    return defined $name ? $self->{__O__}->{$name} : $self->{__O__};
}

sub struttura {
    my ( $self ) = @_;

    if ( !exists $self->{STRUTTURA} ) {

        my $pts = $self->db->create_prepare(q{
			select	--ts.id_tipo_sistema,
					ts.nome_tipo_sistema,
					ts.descrizione,
					ts.manutenibile,
					ts.prefisso_import_automatico
            from	tipi_sistema ts
			where	ts.nome_tipo_sistema = ?
			and		rownum < 2
        });
        my $ptdt = $self->db->create_prepare(q{
            select  --tdt.id_tipo_dato_tecnico,
					tdt.nome,
					tdt.descrizione,
					tdt.tipo,
					tdt.form_sequence,
					tdt.form_group,
					tdt.secret_value,
					tdt.multiplex_value,
					tdt.um,
					tdt.misura,
					tdt.contatore,
					tstdt.alias,
					tdt.valore_min,
					tdt.valore_max,
					tdt.precisione,
					--tdt.url,
					--tdt.telnet,
					--tdt.flagfile,
					--tdt.non_modificabile,
					--tdt.flag,
					--tdt.flag_valore_vincolato,
					--tdt.lista_oggetti,
					tdt.morto
            from    tipi_dati_tecnici tdt
            join    tipi_sistema_tipi_dati_tecnici tstdt
            on      tstdt.id_tipo_dato_tecnico = tdt.id_tipo_dato_tecnico
			where	tstdt.id_tipo_sistema = (
				select	ts.id_tipo_sistema
				from	tipi_sistema ts
				where	ts.nome_tipo_sistema = ?
				and		rownum < 2
			)
			order by
					tdt.nome
        });
		my @keys = map { lc $_ } @{$pts->get_sth->{NAME}};
		my @keys2 = map { lc $_ } @{$ptdt->get_sth->{NAME}};
        my $result = $pts->fetchall_arrayref($self->tipo_sistema);
		die 'Internal Error' if @{$result} > 1;
        $pts->finish;
        tie my %stasis, 'Tie::IxHash', ();
        for my $record ( @{$result} ) {
			@stasis{@keys} =  @{$record};
			#map { delete $stasis{$_} unless defined $stasis{$_} } keys %stasis;
            my $result2 = $ptdt->fetchall_arrayref($self->tipo_sistema);
			for my $record2 ( @{$result2} ) {
				tie my %tdt, 'Tie::IxHash', ();
				@tdt{@keys2} = @{$record2};
				#map { delete $tdt{$_} unless defined $tdt{$_} } keys %tdt;
				push @{$stasis{tdt}}, \%tdt;
			}
        }
		$self->{STRUTTURA} = \%stasis
    }
    return $self->{STRUTTURA};
}

1;
