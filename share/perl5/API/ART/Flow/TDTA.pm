package API::ART::Flow::TDTA;
use strict;
use warnings;
use utf8;
use open qw(:std :utf8);
use Tie::IxHash;

use constant {
    POSIZIONE           =>  0,
    DESCRIZIONE         =>  1,
    ETICHETTA           =>  2,
    TIPO_UI             =>  3,
    OBBLIGATORIO        =>  4,
    SOLO_LETTURA        =>  5,
    NASCOSTO            =>  6,
    PREPOPOLA           =>  7,
    VALORI              =>  8,
    VALORE_DEFAULT      =>  9,
    VALORE_PREDEFINITO  => 10,
    SUGGERIMENTO        => 11,
};

sub new {
    my ( $this, $array ) = @_;
    my $class = ref $this || $this;
    return bless $array, $class;
}

sub posizione           { return shift->[POSIZIONE] };
sub descrizione         { return shift->[DESCRIZIONE] };
sub etichetta           { return shift->[ETICHETTA] };
sub tipo_ui             { return shift->[TIPO_UI] };
sub obbligatorio        { return shift->[OBBLIGATORIO]};
sub solo_lettura        { return shift->[SOLO_LETTURA]};
sub nascosto            { return shift->[NASCOSTO]};
sub prepopola           { return shift->[PREPOPOLA]};
sub valori              { return shift->[VALORI]};
sub valore_default      { return shift->[VALORE_DEFAULT]};
sub valore_predefinito  { return shift->[VALORE_PREDEFINITO]};
sub suggerimento        { return shift->[SUGGERIMENTO]};

sub struttura {
    my ( $self ) = @_;
    tie my %struttura, 'Tie::IxHash', ();
    for my $campo ( qw(posizione descrizione etichetta tipo_ui obbligatorio solo_lettura nascosto prepopola valori valore_default valore_predefinito suggerimento) ) {
        $struttura{$campo} = $self->$campo if defined $self->$campo;
    }
    return \%struttura;
}

1;

