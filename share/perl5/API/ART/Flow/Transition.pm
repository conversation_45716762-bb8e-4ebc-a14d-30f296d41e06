package API::ART::Flow::Transition;
use strict;
use warnings;
use utf8;
use open qw(:std :utf8);
use Tie::IxHash;
use API::ART::Flow::TDTA;

use constant {
    STATO_INIZIALE => 0,
    ACTION         => 1,
    STATO_FINALE   => 2,
    GRUPPI         => 3,
    TDTA           => 4,
    LEVEL          => 5,
};

sub new {
    my ( $this, $array ) = @_;
    my $class = ref $this || $this;
    $array->[GRUPPI] = [ sort { $a cmp $b } split /§/, $array->[GRUPPI] ]
        if defined $array->[GRUPPI];
    $array->[LEVEL] = 0  unless defined $array->[LEVEL];
    if ( defined $array->[TDTA] ) {
        $array->[TDTA]  = [
            sort { $a->[0] <=> $b->[0] }
            map { API::ART::Flow::TDTA->new([ split /§/, $_ ]) }
            split /¶/, $array->[TDTA]
        ];
    }
    return bless $array, $class;
}

sub stato_iniziale { return shift->[STATO_INIZIALE] };
sub stato_finale   { return shift->[STATO_FINALE] };
sub action         { return shift->[ACTION] };
sub gruppi         { return shift->[GRUPPI] };
sub tdta           { return shift->[TDTA] };
sub level          { 
    my ($self,$value) = @_;
    if ( defined $value ) {
        $self->[LEVEL] = $value;
    }
    return $self->[LEVEL];
}

sub struttura {
    my ( $self ) = @_;
    tie my %struttura, 'Tie::IxHash', (
        stato_iniziale => $self->stato_iniziale,
        action         => $self->action,
        stato_finale   => $self->stato_finale,
        gruppi         => $self->gruppi,
    );
    if ( defined $self->tdta ) {
        $struttura{tdta} = [ map { $_->struttura } @{$self->tdta} ];
    }
    return \%struttura;
}

1;

