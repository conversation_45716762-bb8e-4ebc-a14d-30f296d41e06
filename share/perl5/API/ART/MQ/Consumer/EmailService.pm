package API::ART::MQ::Consumer::EmailService;

use strict;
use warnings;
use Carp;
use utf8;

use Email::Stuffer;

use base 'SIRTI::Queue::EventConsumer';

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_event_types
{
	return ['SEND_EMAIL'];
}

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_source_refs
{
	return [];
}

sub init
{

}

#
# override
#
# params
# EVENT : oggetto di tipo SIRTI::Queue::Event
#
# return:
# oggetto di tipo SIRTI::Queue::Response o undef (e set last_error()) in caso di problemi fatali
#
sub consume_event()
{
	my $self	= shift;
	my %params	= @_;
	my $event	= $params{EVENT};
	my $art		= $self->art();
	my $errmsg;

	$self->logger()->info( "Elaboro un evento..." );
	$self->logger()->info( "EVENT NAME: " . $event->get_event_name() );
	$self->logger()->info( "SOURCE_REF: " . $event->get_source_ref() );
	
	for ('SUBJECT'){
		unless (defined $event->get_data()->{$_}){
			my $msg = "Parametro ".$_." obbligatorio mancante!";
			$self->logger()->error( $msg );
			return $self->consume( STATUS => 2, TARGET_REF => 'KO', REASON => $msg);
		}
	}

	if (!defined $event->get_data()->{BODY} && !defined $event->get_data()->{BODY_HTML}){
		my $msg = "Almeno un parametro tra BODY e BODY_HTML deve essere presente";
		$self->logger()->error( $msg );
		return $self->consume( STATUS => 2, TARGET_REF => 'KO', REASON => $msg);
	}

	if (!defined $event->get_data()->{TO} && !defined $event->get_data()->{CC} && !defined $event->get_data()->{BCC}){
		my $msg = "Obbligatorio specificare almeno un paramentro tra TO,CC e BCC!";
                $self->logger()->error( $msg );
                return $self->consume( STATUS => 3, TARGET_REF => 'KO', REASON => $msg);
	}

	my $obj_email = Email::Stuffer->subject($event ->get_data()->{'SUBJECT'});

	$obj_email->to( ref($event->get_data()->{'TO'}) ? join(',', @{$event->get_data()->{'TO'}}) : $event->get_data()->{'TO'})
		if defined $event->get_data()->{'TO'};
	
	$obj_email->cc( ref($event->get_data()->{'CC'}) ? join(',', @{$event->get_data()->{'CC'}}) : $event->get_data()->{'CC'})
		if defined $event->get_data()->{'CC'};
	
	$obj_email->bcc( ref($event->get_data()->{'BCC'}) ? join(',', @{$event->get_data()->{'BCC'}}) : $event->get_data()->{'BCC'})
		if defined $event->get_data()->{'BCC'};
		
	if (defined $event->get_data()->{'SENDER'} || $self->work_context('ART_DEFAULT_MAIL_SENDER')){
		$obj_email->header("From"	=> $event->get_data()->{'SENDER'}||$self->work_context('ART_DEFAULT_MAIL_SENDER'));
	}
	if (defined $event->get_data()->{'REPLY_TO'} || $self->work_context('ART_DEFAULT_REPLY_TO')){
		$obj_email->header("Reply-To"	=> $event->get_data()->{'REPLY_TO'}||$self->work_context('ART_DEFAULT_REPLY_TO'));
	}

	my $body;
	if (exists $event->get_data()->{'BODY_HTML'}){
		$body = $event->get_data()->{'BODY_HTML'};
		utf8::decode($body);
		$obj_email->html_body($body);
	} else {
		$body = $event->get_data()->{'BODY'};
		utf8::decode($body);
		$obj_email->text_body($body);
	}

	my @files;
	if (defined $event->get_data()->{'ATTACHMENTS_DIRECTORY'}){
		my $dir;
		unless(opendir($dir, $event->get_data()->{'ATTACHMENTS_DIRECTORY'})){
			my $reason = sprintf 'Apertura directory temporanea %s fallita: %s', $event->get_data()->{'ATTACHMENTS_DIRECTORY'}, $!;
			$self->logger->warn($reason );
			return $self->skip( REASON => $reason );
		}
		@files = grep { -f $event->get_data()->{'ATTACHMENTS_DIRECTORY'}.'/'.$_ } readdir $dir;
		closedir $dir;

		for my $attach (@files){
			eval{$obj_email->attach_file($event->get_data()->{'ATTACHMENTS_DIRECTORY'}.'/'.$attach);};
			if ($@){
				$self->logger()->warn($@);	
			}
		}
	}
	
	my $res_email = 1;
	
	if ($self->work_context('ART_EMAIL_SERVICE_DONT_SEND')){
		$self->logger()->warn("ATTENZIONE! Modalita' simulazione: l'email non verra' inviata!!!");
	} else {
		$res_email = $obj_email->send;
		if ($event->get_data->{'ATTACHMENTS_DIRECTORY'}){
			for my $attach (@files){
				unlink $event->get_data->{'ATTACHMENTS_DIRECTORY'}.'/'.$attach;
			}
			my $tmpdir = $event->get_data->{'ATTACHMENTS_DIRECTORY'};
			rmdir $tmpdir or $self->logger->warn(sprintf 'Rimozione directory temporanea %s fallita: %s', $tmpdir, $! );
		}
	}
	
	if ($res_email){
		$self->logger()->info("Email relativa al SOURCE_REF ".$event->get_source_ref()." elaborata correttamente");
		return $self->consume( STATUS => 0, TARGET_REF => 'OK', REASON => ($self->work_context('ART_EMAIL_SERVICE_DONT_SEND') ? "Lavorato in modalita' simulazione: l'email non e' stata inviata" : "Lavorato"));
	} else {
		my $msg = "Impossibile inviare email relativa al SOURCE_REF ".$event->get_source_ref();
		$self->logger()->error( $msg );
		return $self->consume( STATUS => 1, TARGET_REF => 'KO', REASON => $msg);
	}
}

sub finish
{

}
1;
