#
# ART_WS_AUTH_BY_JWT -> false, true
# ART_WS_AUTH_BY_JWT_MODE -> 'peer', 'slave' (qualcunaltro ha staccato il JWT), default 'slave'
# 
# scompare ART_WS_USE_CACHE perché se si usa JWT si usa sempre anche memcached
# 
# ART_WS_CACHE_MEMCACHED_SERVER -> non è strettamente legata al MEMCACHED
#

package API::ART::REST;

use strict;
use warnings FATAL => 'all';
use Carp;
use utf8;
use Digest::MD5 qw(md5_hex);
use Data::Dumper;
use File::Copy;
use File::Basename;
use File::Temp qw/ tempfile /;
use JSON;
use Array::Utils qw(:all);
use File::Scan::ClamAV;
use MIME::Types;
use SIRTI::ART::CGI::Charset;
use Encode;
use URI::Escape;
use YAML;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{COM}/share/locale" );
use POSIX; 

use Dancer2;
use Dancer2::FileUtils qw(read_file_content read_glob_content);
use HTTP::Status qw(:constants :is status_message);

use API::ART;
use API::ART::Repository;
use API::ART::Activity::Factory;
use API::ART::System;
use API::ART::Favorite;
use API::ART::Collection::Activity;
use API::ART::Collection::System;
use API::ART::Collection::Favorite;
use SIRTI::ART::Lookup::ART;
use SIRTI::Cache;
use SIRTI::Reports;

set serializer => 'APIARTREST';

if(_art_auth_by_jwt()) {
	eval "use Dancer2::Plugin::JWT;";
	croak "Unable to load Dancer2::Plugin::JWT: $@" if $@;
}

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = "API::ART::Activity::Factory";

my $prefix = defined $ENV{ART_WS_ROUTES_PREFIX} ? $ENV{ART_WS_ROUTES_PREFIX} : '/api/art';
$prefix =~ s#/+$##;

my $elk_config_file;
if (defined $ENV{ELASTICSEARCH_CONFIG_FILE}){
	eval {
		$elk_config_file = YAML::LoadFile($ENV{ELASTICSEARCH_CONFIG_FILE});
		info "Instance ELK configuration loaded from ".$ENV{ELASTICSEARCH_CONFIG_FILE}." file";
		debug "Instance ELK configuration is: " . Dumper($elk_config_file);
	};
	if ($@){
		my $err = shift;
		croak "Instance ELK configuration file not loaded: " . $err||'';
	}
}

my $mt = MIME::Types->new;

my $LOOKUP;

# numero massimo di righe esportabili nelle rotte GET activities, systems e dashboard data
my $OBJECTS_EXPORT_LIMIT = 500;
my $ELASTICSEARCH_OBJECTS_EXPORT_LIMIT = 10000;

sub _art_auth_by_jwt {
	return $ENV{ART_WS_AUTH_BY_JWT};
}

sub _art_auth_by_jwt_mode {
	return defined $ENV{ART_WS_AUTH_BY_JWT_MODE} ? $ENV{ART_WS_AUTH_BY_JWT_MODE} : 'slave';
}

sub _art_session_expire {
	my $x = $ENV{ART_WS_SESSION_EXPIRE}||$ENV{ART_WS_AUTH_BY_JWT_SESSION_EXPIRE}||60*60;
	return $x;
}

sub _art_cache_expire {
	my $x = $ENV{ART_WS_CACHE_EXPIRE}||$ENV{ART_WS_AUTH_BY_JWT_CACHE_EXPIRE}||60*60;
	return $x;
}

sub _art_auth_by_proxy {
	return $ENV{ART_WS_AUTH_BY_PROXY};
}

sub _art_auth_by_proxy_header{
	return $ENV{ART_WS_AUTH_BY_PROXY_HEADER} ? lc($ENV{ART_WS_AUTH_BY_PROXY_HEADER}) : 'x-webauth-user';
}

sub _art_cache_memcached_server {
	return $ENV{ART_WS_CACHE_MEMCACHED_SERVER};
}

sub _art_cors_allowed_origins{
	# *,     sirti.it    , google.com
	my $x = $ENV{ART_WS_CORS_ALLOWED_ORIGINS} ? $ENV{ART_WS_CORS_ALLOWED_ORIGINS} : undef;
	if (defined $x) {
		my @parts = split(',', $x);
		for my $p (@parts){
			$p =~ s/^\s+|\s+$//g;
		}
		return \@parts;
	} else {
		return undef;
	}
}

sub get_sid {
	if(
		request->header('X-API-KEY')
		||
		(
			_art_auth_by_proxy()
			&&
			request->header(_art_auth_by_proxy_header)
		)){
		return session->id();
	} elsif (_art_auth_by_jwt && jwt()){
		return jwt()->{sid};
	} else {
		return session->id();
	}
}

sub _get_elasticsearch {
	croak __("ELASTICSEARCH_NODES not defined")
		unless $ENV{ELASTICSEARCH_NODES};
	if ( !defined vars->{_es} ) {
		require Search::Elasticsearch;
		my $es = Search::Elasticsearch->new(
			nodes => [split(',', $ENV{ELASTICSEARCH_NODES})]
		);
		var _es => $es;
	}
	return vars->{_es};
}

# croak "Bad configuration: you cannot use jwt auth mode and auth proxy mode together"
# 	if _art_auth_by_jwt() && _art_auth_by_proxy();

croak "Bad configuration: jwt auth mode can be 'peer' or 'slave'"
	if _art_auth_by_jwt() && _art_auth_by_jwt_mode() !~ /^(peer|slave)$/;

croak "Bad configuration: cache memcached server must be defined if you use jwt auth"
	if _art_auth_by_jwt() && !defined _art_cache_memcached_server();

croak "Bad configuration: cache memcached server must be defined if you use proxy auth"
	if _art_auth_by_proxy() && !defined _art_cache_memcached_server();

croak "Bad configuration: CORS allowed origin can not have the wildcard '*'"
	if defined _art_cors_allowed_origins() && grep {$_ eq '*'} @{_art_cors_allowed_origins()};

croak "Bad configuration: missing environment for antivirus!"
	if (!defined $ENV{CLAMAV_DAEMON_PORT} && !($ENV{CLAMAV_HOST} && $ENV{CLAMAV_PORT}));

my $CACHE;

# setto il parametro AUTH della API::ART::new basandomi sugli ENV
my $AUTH = {
	TYPE => 'LOCAL'
};
if (defined $ENV{ART_AUTH_TYPE}) {
	$AUTH->{TYPE} = $ENV{ART_AUTH_TYPE};
	if($ENV{ART_AUTH_TYPE} eq 'RADIUS') {
		
		$AUTH->{PARAMS}->{RADIUS} = [];
		
		my $radius_intranet = {
			SERVER => $ENV{RADIUS_SERVER},
			SECRET => $ENV{RADIUS_SECRET}
		};
		$radius_intranet->{DOMAIN} = $ENV{RADIUS_DOMAIN} if defined $ENV{RADIUS_DOMAIN} && $ENV{RADIUS_DOMAIN} ne '';
		push @{$AUTH->{PARAMS}->{RADIUS}}, $radius_intranet;

		if (defined $ENV{RADIUS_SERVER_EXT} && $ENV{RADIUS_SERVER_EXT} ne '') {
			my $radius_extranet = {
				SERVER => $ENV{RADIUS_SERVER_EXT},
				SECRET => $ENV{RADIUS_SECRET_EXT}
			};
			$radius_extranet->{DOMAIN} = $ENV{RADIUS_DOMAIN_EXT} if defined $ENV{RADIUS_DOMAIN_EXT} && $ENV{RADIUS_DOMAIN_EXT} ne '';
			push @{$AUTH->{PARAMS}->{RADIUS}}, $radius_extranet;
		};
		
		my $radius_intranet_bkp = {
			SERVER => $ENV{RADIUS_SERVER_BKP},
			SECRET => $ENV{RADIUS_SECRET_BKP}
		};
		$radius_intranet_bkp->{DOMAIN} = $ENV{RADIUS_DOMAIN} if defined $ENV{RADIUS_DOMAIN} && $ENV{RADIUS_DOMAIN} ne '';
		push @{$AUTH->{PARAMS}->{RADIUS}}, $radius_intranet_bkp;
		
		if (defined $ENV{RADIUS_SERVER_EXT} && $ENV{RADIUS_SERVER_EXT} ne '') {
			my $radius_extranet_bkp = {
				SERVER => $ENV{RADIUS_SERVER_EXT_BKP},
				SECRET => $ENV{RADIUS_SECRET_EXT_BKP}
			};
			$radius_extranet_bkp->{DOMAIN} = $ENV{RADIUS_DOMAIN_EXT} if defined $ENV{RADIUS_DOMAIN_EXT} && $ENV{RADIUS_DOMAIN_EXT} ne '';
			push @{$AUTH->{PARAMS}->{RADIUS}}, $radius_extranet_bkp;
		};
		
	}
}

my $property_prefix = 'ap_';
my $system_property_prefix = 'sp_';
my $parent_prefix = 'parent_';

sub _normalize_activities_sort_filter {
	my $sort = shift;
	
	my $api = vars->{api_art};
	
	unless (ref $sort){
		$sort = [$sort];
	}	
	
	my @sort_fields = ();
	
	my $sort_keys = {
		id => 'ACTIVITY_ID'
		,type => 'ACTIVITY_TYPE_NAME'
		,systemType => 'SYSTEM_TYPE_NAME'
		,creationDate => 'CREATION_DATE'
		,description => 'DESCRIPTION'
		,status => 'STATUS_NAME'
		,lastVarDate	=> 'LAST_VAR_DATE'
		,systemDescription => 'SYSTEM_DESCRIPTION'
	};
	
	for my $s (@{$sort}){
		
		my $asc_desc = $s =~/^-/ ? -1 : 1;
		$s =~s/^-//;
		if ($s !~/^$parent_prefix/) {
			$api->last_error("Cannot sort by $s")
				&& return undef
					unless (exists $sort_keys->{$s} || $s =~/^$property_prefix/ || $s =~/^$system_property_prefix/);	
		} else {
			my @split = split ('_', $s);
			shift @split;
			shift @split;
			my $sort_key = join ('_', @split);
			$api->last_error("Cannot sort by $s")
				&& return undef
					unless (exists $sort_keys->{$sort_key} || $sort_key =~/^$property_prefix/ || $s =~/^$system_property_prefix/);
		}
		
		
		if ($s =~/^$property_prefix/){
			$s =~s/^$property_prefix//;
			push @sort_fields, { ACTIVITY_PROPERTY => { $s => $asc_desc } };
		} elsif ($s =~/^$system_property_prefix/){
			$s =~s/^$system_property_prefix//;
			push @sort_fields, { SYSTEM_PROPERTY => { $s => $asc_desc } };
		} elsif ($s =~/^$parent_prefix/){
			$s =~s/^$parent_prefix//;
			my @split = split('_', $s);
			my $level = shift @split;
			$s = join ('_', @split);
			
			if ($s =~/^$property_prefix/){
				$s =~s/^$property_prefix//;
				push @sort_fields, {'PARENT_'.$level.'_ACTIVITY_PROPERTY'=> {$s => $asc_desc}};	
			} else {
				push @sort_fields, {'PARENT_'.$level.'_'.$sort_keys->{$s} => $asc_desc };	
			}
		} else {
			push @sort_fields, { $sort_keys->{$s} => $asc_desc };
		}
	}
	
	return \@sort_fields;
}

sub _normalize_activities_sort_filter_for_elastic {
	my $sort = shift;
	my $elk_mapping = shift;

	# use Data::Dumper;
	# print STDERR Dumper keys %{$elk_mapping->{mappings}->{properties}->{properties}->{properties}};
	# print STDERR Dumper keys %{$elk_mapping->{mappings}->{properties}->{system}->{properties}->{properties}->{properties}};
	
	my $api = vars->{api_art};
	
	unless (ref $sort){
		$sort = [$sort];
	}	
	
	my @sort_fields = ();
	
	my $sort_keys = {
		id => 'id'
		,type => 'info.type.keyword'
		,systemType => 'system.info.type.keyword'
		,creationDate => 'info.creationDate'
		,description => 'info.description.keyword'
		,status => 'info.status.keyword'
		,lastVarDate	=> 'info.lastVarDate'
		,systemDescription => 'system.info.description.keyword'
	};
	
	for my $s (@{$sort}){
		
		my $asc_desc = $s =~/^-/ ? 'desc' : 'asc';
		$s =~s/^-//;
		
		$api->last_error("Cannot sort by $s")
			&& return undef
				unless (exists $sort_keys->{$s} || $s =~/^$property_prefix/ || $s =~/^$system_property_prefix/);	
		
		if ($s =~/^$property_prefix/){
			$s =~s/^$property_prefix//;
			#print STDERR Dumper $elk_mapping->{mappings}->{properties}->{properties}->{properties}->{$s};
			if ($elk_mapping->{mappings}->{properties}->{properties}->{properties}->{$s}->{type} =~/^(text)$/){
				push @sort_fields, { 'properties.'.$s.'.keyword' => $asc_desc };
			} else {
				push @sort_fields, { 'properties.'.$s => $asc_desc };
			}
		} elsif ($s =~/^$system_property_prefix/){
			$s =~s/^$system_property_prefix//;
			#print STDERR Dumper $elk_mapping->{mappings}->{properties}->{system}->{properties}->{properties}->{properties}->{$s};
			my $stype = 'system.properties.';
			if (exists $elk_mapping->{mappings}->{properties}->{system}->{properties}->{propertiesArray}->{properties}->{$s}){
				$stype = 'system.propertiesArray.';
			}
			if ($elk_mapping->{mappings}->{properties}->{system}->{properties}->{properties}->{properties}->{$s}->{type} =~/^(text)$/){
				push @sort_fields, { $stype.$s.'.keyword' => $asc_desc };
			} else {
				push @sort_fields, { $stype.$s => $asc_desc };
			}
		} else {
			push @sort_fields, { $sort_keys->{$s} => $asc_desc };
		}
	}
	
	return \@sort_fields;
}

sub _normalize_activities_search_filter {
	
	my %params = @_;
	
	my $p = $params{OBJ};
	
	my %query_params = %{$params{QUERY_PARAMS}};
	
	my $api = vars->{api_art};
	
	my @level_present = ('');
	
	while ( my ($key, $value) = each %query_params ) {
		if ($key =~ m/^$parent_prefix/){
			my @split = split ('_', $key);
			shift @split;
			my $level = shift @split;
			$level = $parent_prefix.$level.'_';
			push @level_present,  $level unless grep {$_ eq $level } @level_present;
		}
	}
	
	for my $level (@level_present) {
		
		my %tmp;
		
		if (
			! defined $query_params{$level.'type_in'}
			&&
			! defined $query_params{$level.'type_begins'}
			&&
			! defined $query_params{$level.'type_ends'}
			&&
			! defined $query_params{$level.'type_contains'}
			&&
			defined $query_params{$level.'type'}
		) { 
			$tmp{ACTIVITY_TYPE_NAME}	= $query_params{$level.'type'};
		}
		
		$tmp{SYSTEM_ID}	= $query_params{$level.'systemId'} if defined($query_params{$level.'systemId'});
		$tmp{STATUS_IN}	= $query_params{$level.'status_in'} if defined($query_params{$level.'status_in'});
		$tmp{DESCRIPTION_IN}	= $query_params{$level.'description_in'} if defined($query_params{$level.'description_in'});
		$tmp{SYSTEM_DESCRIPTION_IN}	= $query_params{$level.'systemDescription_in'} if defined($query_params{$level.'systemDescription_in'});
		$tmp{ACTIVITY_TYPE_NAME_IN}	= $query_params{$level.'type_in'} if defined($query_params{$level.'type_in'});
		$tmp{SYSTEM_TYPE_NAME_IN}	= $query_params{$level.'systemType_in'} if defined($query_params{$level.'systemType_in'});
		$tmp{ID_IN}	= $query_params{$level.'id_in'} if defined($query_params{$level.'id_in'});
		$tmp{ASSIGNED_TO}	= $query_params{$level.'assignedTo'} if defined($query_params{$level.'assignedTo'});
		$tmp{CREATED_BY}	= $query_params{$level.'createdBy'} if defined($query_params{$level.'createdBy'});
		
		### facciamo in modo che le chiavi gia' definite siano tutte gestite come array
		while ( my ($key, $value) = each %tmp ) {
			unless (ref $value){
				$tmp{$key} = [$value];
			}	
		}
		
		$tmp{SHOW_ONLY_WITH_VISIBILITY}	= 1 if defined $query_params{$level.'showOnlyWithVisibility'} && $query_params{$level.'showOnlyWithVisibility'} == 1;
		$tmp{ASSIGNED_TO_ME}	= 1 if defined $query_params{$level.'assignedToMe'} && $query_params{$level.'assignedToMe'} == 1;
		$tmp{CREATED_BY_ME}		= 1 if defined $query_params{$level.'createdByMe'} && $query_params{$level.'createdByMe'} == 1;
		
		$tmp{STATUS_EQUAL}	= $query_params{$level.'status_equal'} if defined($query_params{$level.'status_equal'});
		$tmp{STATUS_LIKE} = undef;
		$tmp{STATUS_LIKE}	= $query_params{$level.'status_begins'}.'%' if defined($query_params{$level.'status_begins'});
		$tmp{STATUS_LIKE}	.= '%'.$query_params{$level.'status_contains'}.'%' if defined($query_params{$level.'status_contains'});
		$tmp{STATUS_LIKE}	.= '%'.$query_params{$level.'status_ends'} if defined($query_params{$level.'status_ends'});
		$tmp{DESCRIPTION_EQUAL}	= $query_params{$level.'description_equal'} if defined($query_params{$level.'description_equal'});
		$tmp{DESCRIPTION_LIKE}	= undef;
		$tmp{DESCRIPTION_LIKE}	= $query_params{$level.'description_begins'}.'%' if defined($query_params{$level.'description_begins'});
		$tmp{DESCRIPTION_LIKE}	.= '%'.$query_params{$level.'description_contains'}.'%' if defined($query_params{$level.'description_contains'});
		$tmp{DESCRIPTION_LIKE}	.= '%'.$query_params{$level.'description_ends'} if defined($query_params{$level.'description_ends'});
		$tmp{ACTIVITY_TYPE_NAME_EQUAL}	= $query_params{$level.'type_equal'} if defined($query_params{$level.'type_equal'});
		$tmp{ACTIVITY_TYPE_NAME_LIKE}	= undef;
		$tmp{ACTIVITY_TYPE_NAME_LIKE}	= $query_params{$level.'type_begins'}.'%' if defined($query_params{$level.'type_begins'});
		$tmp{ACTIVITY_TYPE_NAME_LIKE}	.= '%'.$query_params{$level.'type_contains'}.'%' if defined($query_params{$level.'type_contains'});
		$tmp{ACTIVITY_TYPE_NAME_LIKE}	.= '%'.$query_params{$level.'type_ends'} if defined($query_params{$level.'type_ends'});
		$tmp{SYSTEM_TYPE_NAME_EQUAL}	= $query_params{$level.'systemType_equal'} if defined($query_params{$level.'systemType_equal'});
		$tmp{SYSTEM_TYPE_NAME_LIKE}	= undef;
		$tmp{SYSTEM_TYPE_NAME_LIKE}	= $query_params{$level.'systemType_begins'}.'%' if defined($query_params{$level.'systemType_begins'});
		$tmp{SYSTEM_TYPE_NAME_LIKE}	.= '%'.$query_params{$level.'systemType_contains'}.'%' if defined($query_params{$level.'systemType_contains'});
		$tmp{SYSTEM_TYPE_NAME_LIKE}	.= '%'.$query_params{$level.'systemType_ends'} if defined($query_params{$level.'systemType_ends'});
		$tmp{SYSTEM_DESCRIPTION_EQUAL}	= $query_params{$level.'systemDescription_equal'} if defined($query_params{$level.'systemDescription_equal'});
		$tmp{SYSTEM_DESCRIPTION_LIKE}	= undef;
		$tmp{SYSTEM_DESCRIPTION_LIKE}	= $query_params{$level.'systemDescription_begins'}.'%' if defined($query_params{$level.'systemDescription_begins'});
		$tmp{SYSTEM_DESCRIPTION_LIKE}	.= '%'.$query_params{$level.'systemDescription_contains'}.'%' if defined($query_params{$level.'systemDescription_contains'});
		$tmp{SYSTEM_DESCRIPTION_LIKE}	.= '%'.$query_params{$level.'systemDescription_ends'} if defined($query_params{$level.'systemDescription_ends'});
		$tmp{ID_EQUAL}	= $query_params{$level.'id_equal'} if defined($query_params{$level.'id_equal'});
		$tmp{ID_LIKE}	= undef; 
		$tmp{ID_LIKE}	= $query_params{$level.'id_begins'}.'%' if defined($query_params{$level.'id_begins'});
		$tmp{ID_LIKE}	.= '%'.$query_params{$level.'id_contains'}.'%' if defined($query_params{$level.'id_contains'});
		$tmp{ID_LIKE}	.= '%'.$query_params{$level.'id_ends'} if defined($query_params{$level.'id_ends'});
		
		if (defined($query_params{$level.'active'})){
			$api->last_error("Param active repeated")
				&& return undef
					if (ref $query_params{$level.'active'});
			$tmp{ACTIVE}	= $query_params{$level.'active'} ;
		}
		
		$tmp{ACTIVITY_PROPERTIES_EQUAL} = {};
		$tmp{ACTIVITY_PROPERTIES_NOT_EQUAL} = {};
		$tmp{ACTIVITY_PROPERTIES_LIKE} = {};
		$tmp{ACTIVITY_PROPERTIES_IN} = {};
		$tmp{ACTIVITY_PROPERTIES_IS_NULL} = {};
		$tmp{ACTIVITY_PROPERTIES_IS_NOT_NULL} = {};
		
		while ( my ($key, $value) = each %query_params ) {
			my $prefix = undef;
			if ($key =~/^$level$property_prefix/){
				$prefix = $property_prefix;
			} elsif ($key =~/^$level$system_property_prefix/){
				$prefix = $system_property_prefix;
			}
			
			if( defined $prefix && $key =~ m/^$level$prefix(.+)_(.+)$/ ) {

				my @split = split('_',$key);
				my $type;
				if ($level) {
					$api->last_error("Bad param ".$key)
						&& return undef
							if (scalar @split<5);
					$type = pop @split;
					shift @split;
					shift @split;
					shift @split;
				} else {
					$api->last_error("Bad param ".$key)
						&& return undef
							if (scalar @split<3);
					$type = pop @split;
					shift @split;
				}
				
				my $property_type;
				
				if ($prefix eq $property_prefix){
					$property_type = 'ACTIVITY_PROPERTIES_';
				} else {
					$property_type = 'SYSTEM_PROPERTIES_';
				}
				
				if ($type =~/^(equal|notEqual|begins|ends|contains|isNull|isNotNull)$/) {
					
					$api->last_error("Param ".$key." repeated")
						&& return undef
							if (ref $value);
					my $property_name = join ('_', @split);
					
					if ($type eq 'equal'){
						$tmp{$property_type.uc($type)}{$property_name} = $value;
					} elsif ($type eq 'notEqual'){
						$tmp{$property_type."NOT_EQUAL"}{$property_name} = $value;
					} elsif ($type eq 'isNull'){
						$tmp{$property_type."IS_NULL"}{$property_name} = $value;
					} elsif ($type eq 'isNotNull'){
						$tmp{$property_type."IS_NOT_NULL"}{$property_name} = $value;
					} else {
						$tmp{$property_type.'LIKE'}{$property_name} = undef if ! defined $tmp{$property_type.'LIKE'}{$property_name};
						$tmp{$property_type.'LIKE'}{$property_name} .= $value.'%' if $type eq 'begins';
						$tmp{$property_type.'LIKE'}{$property_name} .= '%'.$value.'%' if $type eq 'contains';
						$tmp{$property_type.'LIKE'}{$property_name} .= '%'.$value if $type eq 'ends';
					}
				} elsif ($type =~/^(in)$/) {
					unless (ref $value){
						$value = [$value];
					};
					$tmp{$property_type.uc($type)}{join ('_', @split)} = $value;
				} else {
					$api->last_error("Bad param ".$key);
					return undef;
				}
			}
		}
		
		if ($level){
			my @split = split ('_', $level);
			$tmp{UP_LEVEL} = $split[1];
			if (defined $p->{PARENT}){
				push @{$p->{PARENT}}, \%tmp;
			} else {
				$p->{PARENT} = [\%tmp];
			}
		} else{
			foreach my $key (keys %tmp) {
				$p->{$key} = $tmp{$key};
			}
		}
	}
	
	return 1;
}

sub _normalize_activities_search_filter_for_elastic {
	
	my %params = @_;
	
	my $p = $params{OBJ};
	
	my %query_params = %{$params{QUERY_PARAMS}};

	my $elk_mapping = $params{ELK_MAPPINGS};
	
	my $api = vars->{api_art};
	
	### FIXME: Filtri da implementare
	# - property e systemProperty non ricercabili e ordinabili + tipizzazione per .keyword
	# NB
	# - include/Exclude # essendo usato solo dalle griglie in questo momento prendiamo tutte le chiavi
	# 		escludendo solo la history
	# 		includeSystem
	# 		excludeInfo
	# 		excludeProperties
	#		non fare uscire la history

	### per ora supportiamo solo il primo livello e non la ricerca negli antenati

	my $case_insensitive = $query_params{caseSensitive} ? $JSON::false : $JSON::true;

	my @must;
	my @must_not;
	my @should;
	
	if (
		! defined $query_params{'type_in'}
		&&
		! defined $query_params{'type_begins'}
		&&
		! defined $query_params{'type_ends'}
		&&
		! defined $query_params{'type_contains'}
		&&
		defined $query_params{'type'}
	) { 
		push @must, {"regexp" => {"info.type.keyword" => {
			"value" => quotemeta($query_params{'type'}),
			"case_insensitive" => $case_insensitive,
		}}};
	}
	
	push @must, {"match" => {"system.id" => $query_params{'systemId'}}} if defined($query_params{'systemId'});
	my $map_key = {
		id_in => "id.keyword",
		status_in => "info.status.keyword",
		description_in => "info.description.keyword",
		systemDescription_in => "system.info.description.keyword",
		type_in => "info.type.keyword",
		systemType_in => "system.info.type.keyword",
	};
	
	for my $k (keys %$map_key){
		if (defined($query_params{$k})){
			unless (ref($query_params{$k})){
				$query_params{$k} = [$query_params{$k}];
			}

			my @tmp_should;
			for my $s (@{$query_params{$k}}){
				push @tmp_should, {
					"regexp" => {
						$map_key->{$k} => {
							"value" => quotemeta($s),
							"case_insensitive" => $case_insensitive,
						}
					}
				};
			}

			push @must,
				{
					"bool" => {
						"should" => \@tmp_should,
						"minimum_should_match" => 1,
					}
				};
		}
	}
	push @must, {"match" => {"info.currentUser.keyword" => $query_params{'assignedTo'}}} if defined($query_params{'assignedTo'});
	push @must, {"match" => {"info.creationUser.keyword" => $query_params{'createdBy'}}} if defined($query_params{'createdBy'});
	
	if (defined $query_params{'showOnlyWithVisibility'} && $query_params{'showOnlyWithVisibility'} == 1){
			my @tmp_should;
			my @groups = map {$api->get_group_name($_)} @{$api->user()->groups()};
			for my $s (@groups){
				push @tmp_should, {
					"match" => {
						"system.info.groups.keyword" => $s
					}
				};
			}

			push @must,
				{
					"bool" => {
						"should" => \@tmp_should,
						"minimum_should_match" => 1,
					}
				};
		}
	push @must, {"match" => {"info.currentUser.keyword" => $api->user()->name()}} if $query_params{'assignedToMe'} && $query_params{'assignedToMe'} == 1;
	push @must, {"match" => {"info.creationUser.keyword" => $api->user()->name()}} if defined $query_params{'createdByMe'} && $query_params{'createdByMe'} == 1;


	my $map_like = {
		id => "id.keyword",
		status => "info.status.keyword",
		description => "info.description.keyword",
		type => "info.type.keyword",
		systemType => "system.info.type.keyword",
		systemDescription => "system.info.description.keyword",
	};
	for my $k (keys %$map_like){
		push @must, {"match" => {$map_like->{$k} => $query_params{$k.'_equal'}}} if defined($query_params{$k.'_equal'});
		push @must, {"regexp" => {$map_like->{$k} => {
			"value" => quotemeta($query_params{$k.'_begins'}).'.*',
			"case_insensitive" => $case_insensitive,
		}}} if defined($query_params{$k.'_begins'});
		push @must, {"regexp" => {$map_like->{$k} => {
			"value" => '.*'.quotemeta($query_params{$k.'_contains'}).'.*',
			"case_insensitive" => $case_insensitive,
		}}} if defined($query_params{$k.'_contains'});
		push @must, {"regexp" => {$map_like->{$k} => {
			"value" => '.*'.quotemeta($query_params{$k.'_ends'}),
			"case_insensitive" => $case_insensitive,
		}}} if defined($query_params{$k.'_ends'});
	}

	if (defined($query_params{'active'})){
		$api->last_error("Param active repeated")
			&& return undef
				if (ref $query_params{'active'});
		if ($query_params{'active'}){
			push @must, {"match" => {"info.active" => $query_params{'active'} ? $JSON::true : $JSON::false}};
		}
	}
	
	# FIXME: l'implementazione standard permette di distingeure tra ACTIVITY_PROPERTY e PROPERTY. Come la gestiamo?
	while ( my ($key, $value) = each %query_params ) {
		my $prefix = undef;
		if ($key =~/^$property_prefix/){
			$prefix = $property_prefix;
		} elsif ($key =~/^$system_property_prefix/){
			$prefix = $system_property_prefix;
		}
		
		if( defined $prefix && $key =~ m/^$prefix(.+)_(.+)$/ ) {

			my @split = split('_',$key);
			my $type;
			if (0) {
				$api->last_error("Bad param ".$key)
					&& return undef
						if (scalar @split<5);
				$type = pop @split;
				shift @split;
				shift @split;
				shift @split;
			} else {
				$api->last_error("Bad param ".$key)
					&& return undef
						if (scalar @split<3);
				$type = pop @split;
				shift @split;
			}
			
			my $property_name = join ('_', @split);
			my $property_type;
			
			if ($prefix eq $property_prefix){
				$property_type = 'properties.';
			} else {
				$property_type = 'system.properties.';
				if (exists $elk_mapping->{mappings}->{properties}->{system}->{properties}->{propertiesArray}->{properties}->{$property_name}){
					$property_type = 'system.propertiesArray.';
				}
			}
			
			if ($type =~/^(equal|notEqual|begins|ends|contains|isNull|isNotNull)$/) {
				
				$api->last_error("Param ".$key." repeated")
					&& return undef
						if (ref $value);
				if ($type eq 'equal'){
					push @must, {"regexp" => {$property_type.$property_name.".keyword" => {
						"value" => quotemeta($value),
						"case_insensitive" => $case_insensitive,
						}}
					};
				} elsif ($type eq 'notEqual'){
					push @must_not, {"regexp" => {$property_type.$property_name.".keyword" => {
						"value" => quotemeta($value),
						"case_insensitive" => $case_insensitive,
						}}
					};
				} elsif ($type eq 'isNull'){
					push @must_not, {"exists" => { "field" => $property_type.$property_name }};
				} elsif ($type eq 'isNotNull'){
					push @must, {"exists" => { "field" => $property_type.$property_name }};
				} else {
					# $tmp{$property_type.'LIKE'}{$property_name} = undef if ! defined $tmp{$property_type.'LIKE'}{$property_name};
					my $value = quotemeta($value);

					push @must, {"regexp" => {$property_type.$property_name.".keyword" => {
						"value" => $value.'.*',
						"case_insensitive" => $case_insensitive,
					}}} if $type eq 'begins';
					push @must, {"regexp" => {$property_type.$property_name.".keyword" => {
						"value" => '.*'.$value.'.*',
						"case_insensitive" => $case_insensitive,
					}}} if $type eq 'contains';
					push @must, {"regexp" => {$property_type.$property_name.".keyword" => {
						"value" => '.*'.$value,
						"case_insensitive" => $case_insensitive,
					}}} if $type eq 'ends';
				}
			} elsif ($type =~/^(in)$/) {
				unless (ref $value){
					$value = [$value];
				};
				my @tmp_should;
				for my $s (@{$value}){
					push @tmp_should, {
						"regexp" => {
							$property_type.$property_name.".keyword" =>
							{
								"value" => quotemeta($s),
								"case_insensitive" => $case_insensitive,
							}
						}
					};
				}

				push @must,
					{
						"bool" => {
							"should" => \@tmp_should,
							"minimum_should_match" => 1,
						}
					};
			} else {
				$api->last_error("Bad param ".$key);
				return undef;
			}
		}
	}
	
	$p->{query} = {
		"bool" => {
			"must" => \@must,
			"must_not" => \@must_not,
			"should" => \@should,
			"minimum_should_match" => scalar @should ? 1 : 0,
		}
	};

	return 1;
}

sub _normalize_systems_sort_filter {
	my $sort = shift;
	
	my $api = vars->{api_art};
	
	unless (ref $sort){
		$sort = [$sort];
	}
	
	my @sort_fields = ();
	
	my $sort_keys = {
		id => 'SYSTEM_ID'
		,type => 'SYSTEM_TYPE_NAME'
		,creationDate => 'CREATION_DATE'
		,disablingDate => 'DISABLING_DATE'
		,endingDate => 'ENDING_DATE'
		,description => 'SYSTEM_NAME'
	};
	
	for my $s (@{$sort}){
		
		my $asc_desc = $s =~/^-/ ? -1 : 1;
		$s =~s/^-//;
		
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Cannot sort by $s")
			unless (exists $sort_keys->{$s} || $s =~/^$system_property_prefix/);
		
		if ($s =~/^$system_property_prefix/){
			$s =~s/^$system_property_prefix//;
			push @sort_fields, { PROPERTY => { $s => $asc_desc } };
		} else {
			push @sort_fields, { $sort_keys->{$s} => $asc_desc };
		}
	}
	
	return \@sort_fields;
}

sub _normalize_systems_search_filter {
	
	my %params = @_;
	
	my $p = $params{OBJ};
	
	my %query_params = %{$params{QUERY_PARAMS}};
	
	my $api = vars->{api_art};
	
	$p->{DESCRIPTION_IN}		= $query_params{description_in} if defined($query_params{description_in});
	$p->{ID_IN}					= $query_params{id_in} if defined($query_params{id_in});
	$p->{SYSTEM_TYPE_NAME}		= $query_params{type} if defined($query_params{type});
	$p->{SYSTEM_TYPE_NAME_IN}	= $query_params{type_in} if defined($query_params{type_in});
	
	### facciamo in modo che le chiavi gia' definite siano tutte gestite come array
	while ( my ($key, $value) = each %{$p} ) {
		unless (ref $value){
			$p->{$key} = [$value];
		}	
	}
	
	$p->{DESCRIPTION_EQUAL}			= $query_params{description_equal} if defined($query_params{description_equal});
	$p->{DESCRIPTION_LIKE}			= undef;
	$p->{DESCRIPTION_LIKE}			= $query_params{'description_begins'}.'%' if defined($query_params{'description_begins'});
	$p->{DESCRIPTION_LIKE}			.= '%'.$query_params{'description_contains'}.'%' if defined($query_params{'description_contains'});
	$p->{DESCRIPTION_LIKE}			.= '%'.$query_params{'description_ends'} if defined($query_params{'description_ends'});
	$p->{ID_EQUAL}					= $query_params{id_equal} if defined($query_params{id_equal});
	$p->{ID_LIKE}					= undef;
	$p->{ID_LIKE}					= $query_params{'id_begins'}.'%' if defined($query_params{'id_begins'});
	$p->{ID_LIKE}					.= '%'.$query_params{'id_contains'}.'%' if defined($query_params{'id_contains'});
	$p->{ID_LIKE}					.= '%'.$query_params{'id_ends'} if defined($query_params{'id_ends'});
	$p->{SYSTEM_TYPE_NAME_EQUAL}	= $query_params{'type_equal'} if defined($query_params{type_equal});
	$p->{SYSTEM_TYPE_NAME_LIKE}		= undef;
	$p->{SYSTEM_TYPE_NAME_LIKE}		= $query_params{'type_begins'}.'%' if defined($query_params{'type_begins'});
	$p->{SYSTEM_TYPE_NAME_LIKE}		.= '%'.$query_params{'type_contains'}.'%' if defined($query_params{'type_contains'});
	$p->{SYSTEM_TYPE_NAME_LIKE}		.= '%'.$query_params{'type_ends'} if defined($query_params{'type_ends'});
	
	for ('active', 'suspended'){
		if (defined($query_params{$_})){
			$api->last_error("Param ".$_." repeated")
				&& return undef
					if (ref $query_params{$_});
			$p->{uc($_)}	= $query_params{$_} ;
		}
	}
	
	while ( my ($key, $value) = each %query_params ) {
		
		my $prefix = $system_property_prefix;
		if ( $key =~/^$prefix(.+)_(equal|notEqual|begins|ends|contains|isNull|isNotNull)$/ ){
		
			my @split = split('_',$key);
			$api->last_error("Bad param ".$key)
				&& return undef
					if (scalar @split<3);
			my $type = pop @split;
			shift @split;
			
			my $property_name = join ('_', @split);
			
			# per gestire la compatibilita' con la vecchia chiamata deprecata faccio questo check
			if ($api->test_system_property_name($property_name)){
				
				my $property_type = 'PROPERTIES_';
				
				if ($type =~/^(equal|notEqual|begins|ends|contains|isNull|isNotNull)$/) {
					
					$api->last_error("Param ".$key." repeated")
						&& return undef
							if (ref $value);
					
					if ($type eq 'equal'){
						$p->{$property_type.uc($type)}{$property_name} = $value;
					} elsif ($type eq 'notEqual'){
						$p->{$property_type."NOT_EQUAL"}{$property_name} = $value;
					} elsif ($type eq 'isNull'){
						$p->{$property_type."IS_NULL"}{$property_name} = $value;
					} elsif ($type eq 'isNotNull'){
						$p->{$property_type."IS_NOT_NULL"}{$property_name} = $value;
					} else {
						$p->{$property_type.'LIKE'}{$property_name} = undef if ! defined $p->{$property_type.'LIKE'}{$property_name};
						$p->{$property_type.'LIKE'}{$property_name} .= $value.'%' if $type eq 'begins';
						$p->{$property_type.'LIKE'}{$property_name} .= '%'.$value.'%' if $type eq 'contains';
						$p->{$property_type.'LIKE'}{$property_name} .= '%'.$value if $type eq 'ends';
					}
				} elsif ($type =~/^(in)$/) {
					unless (ref $value){
						$value = [$value];
					};
					$p->{$property_type.uc($type)}{join ('_', @split)} = $value;
				} else {
					$api->last_error("Bad param ".$key);
					return undef;
				}
				next;
			}
		}
		
		# COMPATIBILITA con param deprecato: qui ci arriva solo se e' stato gestito dall'if precedente dove c'è anche un next
		
		if( $key =~ s/^$system_property_prefix(.+)$/$1/ ) {
			$p->{PROPERTIES} = {} unless exists $p->{PROPERTIES};
			$api->last_error("Param ".$system_property_prefix.$key." repeated")
				&& return undef
					if (ref $value);
			$p->{PROPERTIES}{$key} = $value;
		}
	}
	
	return 1;
}

sub _is_cors { return request->header('origin') }

sub _has_acrm { return request->header('access-control-request-method') ? lc(request->header('access-control-request-method')) : undef; }

sub is_preflight { return is_options() && _is_cors() && _has_acrm() }

sub is_options { return lc(request->method()) eq 'options' }

sub handle_cors_request {
	
	# ref http://www.html5rocks.com/en/tutorials/cors/
	
	my %params = @_;
	
	debug "Sono in handle_cors_request";
	debug "Headers:\n" . Dumper request->headers();
	
	# verifico che non si tratti di una CORS Preflight
	if ( is_options() && !is_preflight() ) {
		# nella options restituisco l'elenco dei metodi 'Allow'ed dalla risorsa
		response_headers('Allow' => uc(join(', ', @{$params{METHODS}})));
		# se definiti restituisco gli header indicati nella chiamata alla funzione
		if ( defined $params{EXPOSED_HEADERS} ) {
			for my $k ( keys %{$params{EXPOSED_HEADERS}} ) {
				response_headers($k => $params{EXPOSED_HEADERS}->{$k})
					if ( defined $params{EXPOSED_HEADERS}->{$k} );
			}
		}
	}
	
	my $origin = _is_cors();
	
	debug "Non e' una CORS"
		&& return
			unless defined $origin;
	
	# e' una CORS
	debug "E' una CORS con Origin: $origin";
	
	my $acrm = _has_acrm();
	
	debug "E' una CORS ".(defined $acrm ? "con ACRM: $acrm" : "senza ACRM");

	# recupero gli origin autorizzati ad accedere all'applicazione
	my $allowed_origins = _art_cors_allowed_origins();

	my %headers = ();

	if (defined $allowed_origins){ # se definiito ho un array con gli origin autorizzati e verifico se il chiamante è uno di quelli
		if (grep {$_ eq $origin} @{$allowed_origins}){
			$headers{'Access-Control-Allow-Origin'} = $origin;
		}
		# se non lo trovo non restituisco nella response l'header Access-Control-Allow-Origin: in questo modo il browser non procederà alle chiamate
	} else { # se non definito significa che l'applicazione accetta chiamate da qualunque origin
		$headers{'Access-Control-Allow-Origin'} = $origin;
	}

	if ( is_preflight() ) {
		
		debug "E' una CORS Preflight Request";
		
		my $methods = $params{METHODS};
		
		return
			unless scalar grep { lc($_) eq $acrm } @$methods;
		
		debug "ACRM $acrm consentito";
		
		# FIXME: implementare la validazione su Access-Control-Request-Headers
		$headers{'Access-Control-Allow-Methods'} = uc(join(', ', @$methods));
		$headers{'Access-Control-Allow-Headers'} = 'accept, content-type, content-length, x-api-key';
		$headers{'Access-Control-Allow-Credentials'} = 'true';
		
		if( _art_auth_by_jwt ) {
			$headers{'Access-Control-Allow-Headers'} .= ', '.lc(jwt_request_auth_header());
		}
		if ( _art_auth_by_proxy() ){
			$headers{'Access-Control-Allow-Headers'} .= ', '._art_auth_by_proxy_header();
		}
		
		response_headers %headers;
		
	} else {
		
		debug "E' una CORS Request con method ".lc(request->method());
		
		# NOTA: verificare come è possibile discriminare quali headers accettare ed esporre a seconda della rotta.
		#        Per ora content-disposition lo inviamo sempre anche se serve solo nelle rotte degli attachments
		$headers{'Access-Control-Expose-Headers'} = 'content-type, content-length, content-disposition, x-api-key';
		$headers{'Access-Control-Allow-Credentials'} = 'true';
		
		if( is_options() ) {
			$headers{'Access-Control-Expose-Headers'} .= ', allow';
		}
		
		if( _art_auth_by_jwt ) {
			# NOTA: negli ambienti vecchi bisogna configurare Dancer2::Plugin::JWT in config.yml
			# aggiungendo la chiave response_token_header_name valorizzata come request_auth_header
			$headers{'Access-Control-Expose-Headers'} .= ', '.lc(jwt_token_header_name());
		}
		if ( _art_auth_by_proxy() ){
			$headers{'Access-Control-Expose-Headers'} .= ', '._art_auth_by_proxy_header;
		}
		
		if( config->{environment} !~ /^production/ ) {
			$headers{'Access-Control-Expose-Headers'} .= ', x-process-pid';
		}
		
		# se definiti espongo gli header indicati nella chiamata alla funzione
		if( defined $params{EXPOSED_HEADERS} ) {
			for my $k ( keys %{$params{EXPOSED_HEADERS}} ) {
				$headers{'Access-Control-Expose-Headers'} .= ', '.$k;
			}
		}
		
		response_headers %headers;
		
	}

};

sub send_ok {

	my %params = @_;
	$params{CODE} = HTTP_OK unless $params{CODE};

	debug "Entro in send_ok";

	unless ($params{IGNORE_SESSION}) {

		my $api = vars->{api_art};

		if (
			request->header('X-API-KEY')
			||
			(
				_art_auth_by_proxy()
				&&
				request->header(_art_auth_by_proxy_header)
			)
		) {
			# in questa modalità non esiste la cache
			return send_ko(INTERNAL_ERROR_MSG => __("Unable to manage param CACHE without cache server"))
				if exists $params{CACHE} && ! defined _art_cache_memcached_server();

			
			$api->disconnect();
			$api->lookup_delete();
			
			if (request->header('X-API-KEY')) {
				session api_art => $api; # NOTA: la sessione viene comunque sovrascritta
			} else { # sono in modalità _art_auth_by_proxy
				# se ho il cache serve lo uso
				# inserisco in cache le credenziali in caso di login o faccio il refresh per gestire scadenza della sessione
				eval {$CACHE->set(get_sid(), { username => vars->{username} }, { expire => _art_session_expire() })};
				return send_ko(INTERNAL_ERROR_MSG => __x("Unable to save credentials in cache server: {error}", error => $@))
					if $@;
				
				unless ( request->is_delete() && (request->path() =~ m{$prefix/sessions/[^/]+$} || request->path() =~ m{$prefix/session$}) ) { ### non sono nella rotta di logout
					# inserisco in cache l'API::ART (non compressa, non serializzata) in caso di login o faccio il refresh per gestire scadenza dell'oggetto
					# NOTA: la cache viene comunque sovrascritta
					my $cache = vars->{cache}||{};
					if (defined $params{CACHE}){
						return send_ko(INTERNAL_ERROR_MSG => __x("Param {param} must be an hashref", param => 'CACHE'))
							if ref ($params{CACHE}) ne 'HASH';
						for my $c (keys %{$params{CACHE}}){
							$cache->{$c} = $params{CACHE}->{$c};
						}
					}
					eval {$CACHE->set(get_sid()."-".$ENV{ARTID}, { api => $api, cache => $cache, elk_mappings => vars->{elk_mappings} }, { expire => _art_cache_expire() })};
					return send_ko(INTERNAL_ERROR_MSG => __x("Unable to save API::ART in cache server: {error}", error => $@))
						if $@;
				} else { ### rotta di logout
					# ripulisco la chache, sia master che app
					eval{ $CACHE->delete(get_sid()); };
					return send_ko(INTERNAL_ERROR_MSG => __x("Unable to delete credentials in cache server: {error}", error => $@))
						if $@;
					eval{ $CACHE->delete(get_sid()."-".$ENV{ARTID}); };
					return send_ko(CODE => HTTP_NOT_FOUND, INTERNAL_ERROR_MSG => __x("Unable to delete API::ART in cache server: {error}", error => $@))
						if $@;
					# eseguo logout per la gestione della sessione impostando l'env _WSART_LOGGED_USER perchè con il logout
					# l'utente non sarà più disponibile e non potrà essere impostato dall'hook after
					$ENV{_WSART_LOGGED_USER} = $api->user()->name();
					eval{ $api->logout(); };
					return send_ko(INTERNAL_ERROR_MSG => __x("Unable to logout from API::ART: {error}", error => $@))
						if $@;
					app->destroy_session;
				}
			}
		} elsif(_art_auth_by_jwt()) {
			if(_art_auth_by_jwt_mode() eq 'peer') {
				if( is_login_route() ) { ### rotta di login
					jwt({ sid => session->id() });
					$params{MSG}->{token} = jwt_raw_token();
				}
				if ( request->is_delete() && (request->path() =~ m{$prefix/sessions/[^/]+$} || request->path() =~ m{$prefix/session$}) ) { ### rotta di logout
					# ripulisco la chache, sia master che app
					eval{ $CACHE->delete(get_sid()); };
					return send_ko(INTERNAL_ERROR_MSG => __x("Unable to delete credentials in cache server: {error}", error => $@))
						if $@;
					eval{ $CACHE->delete(get_sid()."-".$ENV{ARTID}); };
					return send_ko(INTERNAL_ERROR_MSG => __x("Unable to delete API::ART in cache server: {error}", error => $@))
						if $@;
					# eseguo logout per la gestione della sessione impostando l'env _WSART_LOGGED_USER perchè con il logout
					# l'utente non sarà più disponibile e non potrà essere impostato dall'hook after
					$ENV{_WSART_LOGGED_USER} = $api->user()->name();
					eval{ $api->logout(); };
					return send_ko(INTERNAL_ERROR_MSG => __x("Unable to logout from API::ART: {error}", error => $@))
						if $@;
					app->destroy_session;
				} else {
					# inserisco in cache le credenziali in caso di login o faccio il refresh per gestire scadenza della sessione
					eval {$CACHE->set(get_sid(), { username => vars->{username} }, { expire => _art_session_expire() })};
					return send_ko(INTERNAL_ERROR_MSG => __x("Unable to save credentials in cache server: {error}", error => $@))
						if $@;
				}
			}
			
			$api->disconnect();
			$api->lookup_delete();
			
			unless ( request->is_delete() && (request->path() =~ m{$prefix/sessions/[^/]+$} || request->path() =~ m{$prefix/session$}) ) { ### non sono nella rotta di logout
				# inserisco in cache l'API::ART (non compressa, non serializzata) in caso di login o faccio il refresh per gestire scadenza dell'oggetto
				# NOTA: la cache viene comunque sovrascritta
				my $cache = vars->{cache}||{};
				if (defined $params{CACHE}){
					return send_ko(INTERNAL_ERROR_MSG => __x("Param {param} must be an hashref", param => 'CACHE'))
						if ref ($params{CACHE}) ne 'HASH';
					for my $c (keys %{$params{CACHE}}){
						$cache->{$c} = $params{CACHE}->{$c};
					}
				}
				eval {$CACHE->set(get_sid()."-".$ENV{ARTID}, { api => $api, cache => $cache, elk_mappings => vars->{elk_mappings} }, { expire => _art_cache_expire() })};
				return send_ko(INTERNAL_ERROR_MSG => __x("Unable to save API::ART in cache server: {error}", error => $@))
					if $@;
			}
		} else {
			# in questa modalità non esiste la cache
			return send_ko(INTERNAL_ERROR_MSG => __("Unable to manage param CACHE without cache server"))
				if exists $params{CACHE} && ! defined _art_cache_memcached_server();

			if (request->is_delete() && (request->path() =~ m{$prefix/sessions/[^/]+$} || request->path() =~ m{$prefix/session$}) ) { ### rotta di logout
				# eseguo logout per la gestione della sessione impostando l'env _WSART_LOGGED_USER perchè con il logout
				# l'utente non sarà più disponibile e non potrà essere impostato dall'hook after
				$ENV{_WSART_LOGGED_USER} = $api->user()->name();
				eval{ $api->logout(); };
				return send_ko(INTERNAL_ERROR_MSG => __x("Unable to logout from API::ART: {error}", error => $@))
					if $@;
				
				$api->disconnect();
				$api->lookup_delete();
				
				app->destroy_session;
			} else {
				$api->disconnect();
				$api->lookup_delete();
				
				if( is_login_route() ) { ### rotta di login
					$params{MSG}->{token} = get_sid();
				}
				session api_art => $api; # NOTA: la sessione viene comunque sovrascritta
			}
		}
	}

	status($params{CODE});
	return defined $params{MSG} ? $params{MSG} : undef;
	
}

sub send_file_ok {
	
	my ( $fh, %params ) = @_;

	my $api = vars->{api_art};
	
	if ($api){
		$api->disconnect();
		
		$api->lookup_delete();

		if (
			request->header('X-API-KEY')
			||
			(
				_art_auth_by_proxy()
				&&
				request->header(_art_auth_by_proxy_header)
			)
		) {
			if (request->header('X-API-KEY')) {
				session api_art => $api; # NOTA: la sessione viene comunque sovrascritta
			} else { # sono in modalità _art_auth_by_proxy
				eval {$CACHE->set(get_sid(), { username => vars->{username} }, { expire => _art_session_expire() })};
				return send_ko(INTERNAL_ERROR_MSG => __x("Unable to save credentials in cache server: {error}", error => $@))
					if $@;
				# faccio il refresh in cache dell'API::ART (non compressa, non serializzata) per gestire scadenza dell'oggetto
				# NOTA: la cache viene comunque sovrascritta
				my $cache = vars->{cache}||{};
				if (defined $params{CACHE}){
					return send_ko(INTERNAL_ERROR_MSG => __x("Param {param} must be an hashref", param => 'CACHE'))
						if ref ($params{CACHE}) ne 'HASH';
					for my $c (keys %{$params{CACHE}}){
						$cache->{$c} = $params{CACHE}->{$c};
					}
				}
				eval {$CACHE->set(get_sid()."-".$ENV{ARTID}, { api => $api, cache => $cache, elk_mappings => vars->{elk_mappings} }, { expire => _art_cache_expire() })};
				return send_ko(INTERNAL_ERROR_MSG => __x("Unable to save API::ART in cache server: {error}", error => $@))
					if $@;
			}
		} elsif(_art_auth_by_jwt){
			# nel caso peer faccio il refresh delle credenziali nella cache per gestire la scadenza della sessione
			if(_art_auth_by_jwt_mode() eq 'peer') {
				eval {$CACHE->set(get_sid(), { username => vars->{username} }, { expire => _art_session_expire() })};
				return send_ko(INTERNAL_ERROR_MSG => __x("Unable to save credentials in cache server: {error}", error => $@))
					if $@;
			}
			# faccio il refresh in cache dell'API::ART (non compressa, non serializzata) per gestire scadenza dell'oggetto
			# NOTA: la cache viene comunque sovrascritta
			my $cache = vars->{cache}||{};
			if (defined $params{CACHE}){
				return send_ko(INTERNAL_ERROR_MSG => __x("Param {param} must be an hashref", param => 'CACHE'))
					if ref ($params{CACHE}) ne 'HASH';
				for my $c (keys %{$params{CACHE}}){
					$cache->{$c} = $params{CACHE}->{$c};
				}
			}
			eval {$CACHE->set(get_sid()."-".$ENV{ARTID}, { api => $api, cache => $cache, elk_mappings => vars->{elk_mappings} }, { expire => _art_cache_expire() })};
			return send_ko(INTERNAL_ERROR_MSG => __x("Unable to save API::ART in cache server: {error}", error => $@))
				if $@;
		} else {
			eval {$CACHE->set(get_sid(), { username => vars->{username} }, { expire => _art_session_expire() })};
			return send_ko(INTERNAL_ERROR_MSG => __x("Unable to save credentials in cache server: {error}", error => $@))
				if $@;
			# faccio il refresh in cache dell'API::ART (non compressa, non serializzata) per gestire scadenza dell'oggetto
			# NOTA: la cache viene comunque sovrascritta
			my $cache = vars->{cache}||{};
			if (defined $params{CACHE}){
				return send_ko(INTERNAL_ERROR_MSG => __x("Param {param} must be an hashref", param => 'CACHE'))
					if ref ($params{CACHE}) ne 'HASH';
				for my $c (keys %{$params{CACHE}}){
					$cache->{$c} = $params{CACHE}->{$c};
				}
			}
			eval {$CACHE->set(get_sid()."-".$ENV{ARTID}, { api => $api, cache => $cache, elk_mappings => vars->{elk_mappings} }, { expire => _art_cache_expire() })};
			return send_ko(INTERNAL_ERROR_MSG => __x("Unable to save API::ART in cache server: {error}", error => $@))
				if $@;
			session api_art => $api; # NOTA: la sessione viene comunque sovrascritta
		}
	}
	delete $params{CACHE} if exists $params{CACHE};

	send_file($fh, %params);
}

sub send_ko {
	
	my %params = @_;
	
	my $api = vars->{api_art};
	if ($api){
		$api->disconnect();
		$api->lookup_delete();
	}
	
	$params{CODE} = HTTP_INTERNAL_SERVER_ERROR unless $params{CODE};
	$params{ERROR_MSG} = status_message($params{CODE}) unless $params{ERROR_MSG};
	$params{INTERNAL_ERROR_MSG} = $params{INTERNAL_ERROR_MSG} ? $params{INTERNAL_ERROR_MSG} : $params{ERROR_MSG};
	my $return = {
		 UUID		=> vars->{uuid}
		,message	=> $params{ERROR_MSG}
		,internalMessage => $params{INTERNAL_ERROR_MSG}
	};
	info "Error:\n" . Dumper $return;
	delete $return->{internalMessage}
		if config->{environment} =~ /^production/;
	status($params{CODE});
	return $return;

}

sub do_api_save {
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	my $api = shift;

	# NB: se l'environment è diverso da "production"" possiamo utilizzare il GET param "rollback" per evitare il commit	
	if ( $query_params{rollback} && config->{environment} !~ /^production/ ) {
		debug "do rollback";
		$api->cancel;
	} else {
		debug "do commit";
		$api->save;
	}

}

sub do_api_cancel {
	
	my $api = shift;
	debug "do rollback";
	$api->cancel;

}

sub remove_in_hook_after {
	
	my $file = shift;

	croak "You must invoke this function with a valid filename"
		unless defined $file && -f $file;

	my $to_remove_in_hook_after = vars->{_to_remove_in_hook_after};
	push @$to_remove_in_hook_after, $file;
	
	var _to_remove_in_hook_after => $to_remove_in_hook_after;
	
}

#	Restituisce un array con
#		- API::ART: oggetto API::ART, undef in caso di errore
#		- HTTP_STATUS: codice HTTP di errore (verificare se HTTP::Status::is_client_error
#			o HTTP::Status::is_server_error), undef se il restore è andato a buon fine
#		- ERROR_MESSAGE: undef se il restore è andato a buon fine
#		- INTERNAL_ERROR_MESSAGE: undef se il restore è andato a buon fine
sub _restore_api_art {
	
	my $api;
	my $cache = {};
	my $elk_mappings;
	
	my $is_api_restored;
	# gestione apiKey
	if (request->header('X-API-KEY')){
		# per ora trascuro cache
		debug 'Creazione nuova istanza API::ART dalle credenziali recuperate dall\'API-KEY';
		my %params_new = (
			ARTID => $ENV{ARTID}
			,API_KEY => request->header('X-API-KEY')
			,AUTOSAVE => 0
			,DEBUG => $ENV{ART_DB_DEBUG}
			,DEFAULT_DATE_FORMAT => 'YYYYMMDDHH24MISS'
		);
		#$params_new{PASSWORD} = $cache_master->{password} if defined $cache_master->{password};
		if ($ENV{WSART_SESSION_DURATION}){
			$params_new{SID} = get_sid(); 
			$params_new{SESSION_DURATION} = $ENV{WSART_SESSION_DURATION};
		}
		
		eval {
			$api = API::ART->new(%params_new);
		};
		if ($@) {
			# gli operatori devono essere allineati: perché non riesco a loggare l'utente?
			return undef, HTTP_FORBIDDEN, status_message(HTTP_FORBIDDEN), __x("Unable to create API::ART with API-KEY {apiKey}: {error}", apiKey => request->header('X-API-KEY'), error => $@);
		}
		# verifico se l'API_KEY è disponibile solo per certi IP
		my $api_key_content = $api->user()->get_api_key_content();
		return undef, HTTP_FORBIDDEN, status_message(HTTP_FORBIDDEN), __x("Unable to manage API-KEY {apiKey} content", apiKey => request->header('X-API-KEY'))
			unless defined $api_key_content;
		if (exists $api_key_content->{CONTENT}->{ipList}){
			my $x_forwarded_for_string = request->header('X-FORWARDED-FOR');
			if (defined $x_forwarded_for_string){
				my @x_forwarded_for_list = split(',', $x_forwarded_for_string);
				$x_forwarded_for_list[0]=~ s/^\s+|\s+$//g;
				# si deve considerare solo il primo IP in quanto è quello del "vero chiamante"
				return undef, HTTP_FORBIDDEN, status_message(HTTP_FORBIDDEN), __x("Unable to manage API-KEY {apiKey} for IP {ip}", apiKey => request->header('X-API-KEY'), ip => $x_forwarded_for_list[0])
					unless grep {$_ eq $x_forwarded_for_list[0]} @{$api_key_content->{CONTENT}->{ipList}};
			}
		}

		$is_api_restored = 0;

	} else {
		if (
			_art_auth_by_proxy()
			&&
			request->header(_art_auth_by_proxy_header)

		) {
			if (defined session('username') && (lc(session('username')) ne lc(request->header(_art_auth_by_proxy_header())))) {
				# l'utente ha effettuato il logout sull'auth-proxy e il login con un nuovo utente
				# distruggo la sessione per far staccare un nuovo session id
				app->destroy_session();
			}
			# memorizzo l'utente corrente nella sessione per successivi confronti
			session username => request->header(_art_auth_by_proxy_header());
			# verifico nella cache la presenza e validità del sid
			# se non lo trovo 401
			my $cache_master = $CACHE->get(get_sid());
			unless($cache_master) {
				# non verifico l'errore perché la chiave potrebbe non essere presente
				eval{ $CACHE->delete(get_sid()."-".$ENV{ARTID}); };
			}
			# se ok verifico se l'api art (non serializzata, non compressa) di questa app è presente nella cache
			my $cache_local_app = $CACHE->get(get_sid()."-".$ENV{ARTID});
			if($cache_local_app) {
				# se è presente la ripristino
				debug 'Ripristino istanza API::ART dal cache server';
				$api = $cache_local_app->{api};
				$cache = $cache_local_app->{cache};
				$elk_mappings = $cache_local_app->{elk_mappings};
				$is_api_restored = 1;
				var elk_mappings => $elk_mappings;
			} else {
				var api_art => $api;
				# se _art_auth_by_proxy() reinstanzio sempre l'API::ART
				debug 'Creazione nuova istanza API::ART da username ' . lc(request->header(_art_auth_by_proxy_header())) . ' raccolta nel header';
				my %params_new = (
					ARTID => $ENV{ARTID}
					,USER => request->header(_art_auth_by_proxy_header())
					,AUTOSAVE => 0
					,DEBUG => $ENV{ART_DB_DEBUG}
					,DEFAULT_DATE_FORMAT => 'YYYYMMDDHH24MISS'
				);
				if ($ENV{WSART_SESSION_DURATION}){
					$params_new{SID} = get_sid();
					$params_new{SESSION_DURATION} = $ENV{WSART_SESSION_DURATION};
				}
				eval {
					$api = API::ART->new(%params_new);
				};
				if ($@) {
					return undef, HTTP_UNAUTHORIZED, status_message(HTTP_UNAUTHORIZED), __x("Username not found in local db {username}: {error}", username => request->header(_art_auth_by_proxy_header()), error => $@);
				}
				var api_art => $api;
				info 'User '.$api->user()->name().' auth by '.$api->user()->auth_type;
				$is_api_restored = 0;
			}
		} elsif (_art_auth_by_jwt){
			# verifico nella cache la presenza e validità del sid presente nel JWT
			# se non lo trovo 401
			my $cache_master = $CACHE->get(get_sid());
			unless($cache_master) {
				# non verifico l'errore perché la chiave potrebbe non essere presente
				eval{ $CACHE->delete(get_sid()."-".$ENV{ARTID}); };
				return undef, HTTP_UNAUTHORIZED, status_message(HTTP_UNAUTHORIZED), __x("No valid session in cache for sid {sid}", sid => get_sid());
			}
			# se ok verifico se l'api art (non serializzata, non compressa) di questa app è presente nella cache
			my $cache_local_app = $CACHE->get(get_sid()."-".$ENV{ARTID});
			if($cache_local_app) {
				# se è presente la ripristino
				debug 'Ripristino istanza API::ART dal cache server';
				$api = $cache_local_app->{api};
				$cache = $cache_local_app->{cache};
				$elk_mappings = $cache_local_app->{elk_mappings};
				var elk_mappings => $elk_mappings;
				$is_api_restored = 1;
			} else {
				# altrimenti la istanzio
				debug 'Creazione nuova istanza API::ART dalle credenziali recuperate dal cache server';
				my %params_new = (
					ARTID => $ENV{ARTID}
					,USER => $cache_master->{username}
					,AUTOSAVE => 0
					,DEBUG => $ENV{ART_DB_DEBUG}
					,DEFAULT_DATE_FORMAT => 'YYYYMMDDHH24MISS'
					,AUTH => $AUTH
				);
				$params_new{PASSWORD} = $cache_master->{password} if defined $cache_master->{password};
				if ($ENV{WSART_SESSION_DURATION}){
					$params_new{SID} = get_sid(); 
					$params_new{SESSION_DURATION} = $ENV{WSART_SESSION_DURATION};
				}
				
				eval {
					$api = API::ART->new(%params_new);
				};
				if ($@) {
					# gli operatori devono essere allineati: perché non riesco a loggare l'utente?
					return undef, HTTP_FORBIDDEN, status_message(HTTP_FORBIDDEN), __x("Unable to create API::ART despite a valid JWT and a valid cache for user {username}: {error}", username => $cache_master->{username}, error => $@);
				}
				$is_api_restored = 0;
			}
			# salvo username e password per metterle nel cache server
			var username => $cache_master->{username};
			var password => $cache_master->{password};
		} else {
			debug 'Ripristino istanza API::ART dalla sessione';
			$api = session('api_art');
			$is_api_restored = 1;
		}
	}

	unless (defined $api){
		return undef, HTTP_FORBIDDEN, status_message(HTTP_FORBIDDEN), __("Unable to create API::ART in any mode");
	}

	if($is_api_restored) {
		debug "Ripristino connessione di API::ART e SIRTI::ART::Lookup::ART";
		
		$api->connect();
		
		$LOOKUP = eval{SIRTI::ART::Lookup::ART->new($api->_dbh())};
		
		$api->lookup_restore($LOOKUP);
	}
	
	return $api, undef, undef, undef, $cache;

}

sub force_file_content_length {
	
	my $size = shift;
	var _attachment_size => $size;
	
}

# riceve in input una data nel formato yyyymmddhh24miss e ritorna un ISODate
sub format_iso_date {
	my ($date) = @_;
	
	my $api = vars->{api_art};
	
	return $api->get_iso_date_from_date($date);
}

# restituisce un oggetto che modellizza l'attività
# accetta come primo parametro l'API::ART, come secondo parametro l'oggetto attività e quindi opzionalmente un hash con i seguenti parametri booleani
# EXCLUDE_INFO, EXCLUDE_PROPERTIES, INCLUDE_SYSTEM
# DEPRECATED: utilizzare $activity->dump( ... )
sub remap_activity {
	
	my ($api, $act, %params) = @_;
	
	return $act->dump(
		 EXCLUDE_INFO		=> $params{EXCLUDE_INFO} ? 1 : 0
		,EXCLUDE_PROPERTIES => $params{EXCLUDE_PROPERTIES} ? 1 : 0
		,SYSTEM				=> {
			EXCLUDE_ALL	=> $params{INCLUDE_SYSTEM} ? 0 : 1
		}
	);
	
}

# ritorna le activity info rimappate, undef e set last_error in caso di errore
# DEPRECATED: utilizzare $activity->dump()->{info}
sub remap_activity_info {
	
	my ($api, $info) = @_;
	
	my $activity = eval { API::ART::Activity::Factory->new( ART => $api, ID =>$info->{ACTIVITY_ID} ) };
	$api->last_error($@ || $api->last_error())
		&& return undef
			if $@ || !$activity;
	
	my $ret = $activity->dump();
	return undef
		unless defined $ret;
	
	return $ret->{info};

}

# ritorna le system info rimappate, undef e set last_error in caso di errore
# DEPRECATED: utilizzare $system->dump()->{info}
sub remap_system_info {
	
	my ($api, $info) = @_;
	
	my $system = eval { API::ART::System->new( ART => $api, ID =>$info->{SYSTEM_ID} ) };
	$api->last_error($@ || $api->last_error())
		&& return undef
			if $@ || !$system;
	
	my $ret = $system->dump();
	return undef
		unless defined $ret;
	
	return $ret->{info};

}

sub _remap_keys {
	
	my ($key, %params) = @_;
	
	return $key if $params{USE_DOLLAR} && $key !~/^\$/;
	
	my $prefix= $params{USE_DOLLAR} ? '$' : '';
	
	$key =~s/^\$//;
	
	my $remapped_info = {
		'SYSTEM_NAME'					=> 'description'
		, 'ACTIVE'						=> 'active'
		, 'DISABLED'					=> 'disabled'
		, 'ENDED'						=> 'ended'
		, 'CREATION_DATE'				=> 'creationDate'
		, 'DISABLING_DATE'				=> 'disablingDate'
		, 'ENDING_DATE'					=> 'endingDate'
		, 'SYSTEM_NAME'					=> 'description'
		, 'LAST_VAR_DATE'				=> 'lastVarDate'
		, 'PARENT_ID'					=> 'parentId'
		, 'SYSTEM_ID'					=> 'systemId'
		, 'DESCRIPTION'					=> 'description'
		, 'DATE_NEXT_STEP'				=> 'dateNextStep'
		, 'CURRENT_USER_ID'				=> 'currentUserId'
		, 'SYSTEM_TYPE_ID'				=> 'systemTypeId'
		, 'OWNER_USER_ID'				=> 'ownerId'
		, 'STATUS_ID'					=> 'statusId'
		, 'ACTIVITY_TYPE_ID'			=> 'activityTypeId'
		, 'ACTIVITY_TYPE_NAME'			=> 'activityTypeName'
		, 'SYSTEM_TYPE_NAME'			=> 'systemTypeName'
		, 'ACTIVITY_ID'					=> 'activityId'
		, 'CURRENT_USER_NAME'			=> 'currentUsername'
		, 'OWNER_USER_NAME'				=> 'ownerName'
		, 'OWNER'						=> 'owner'
		, 'OWNER_NAME'					=> 'owner'
		, 'OWNER_FIRST_NAME'			=> 'ownerFirstName'
		, 'OWNER_LAST_NAME'				=> 'ownerLastName'
		, 'STATUS_NAME'					=> 'statusName'
		, 'EMAIL'						=> 'email'
		, 'NAME'						=> 'username'
		, 'FIRST_NAME'					=> 'firstName'
		, 'LAST_NAME'					=> 'lastName'
		, 'MOBILE_PHONE'				=> 'mobilePhone'
		, 'SERVICE_USER'				=> 'serviceUser'
		, 'GROUPS'						=> 'groups'
		, 'CHILDREN'					=> 'children'
		, 'TRANSITION_DATE'				=> 'transitionDate'
		, 'TRANSITION_ID'				=> 'transitionId'
		, 'FROM_STATUS_NAME'			=> 'fromStatus'
		, 'USER_NAME'					=> 'username'
		, 'ACTION_NAME'					=> 'action'
		, 'TO_STATUS_NAME'				=> 'toStatus'
		, 'PROPERTIES'					=> 'properties'
		, 'ATTACHMENTS'					=> 'attachments'
		, 'SEQUENCE'					=> 'sequence'
		, 'FILENAME'					=> 'fileName'
		, 'DOWNLOAD_COUNT'				=> 'downloadCount'
		, 'SIZE'						=> 'size'
		, 'CURRENT_USER_CHANGE_REASON'	=> 'currentUserChangeReason'
	};

	return $prefix.$remapped_info->{$key};

}

sub _normalize_locale {
	# FIXME: per ora il rimappamento tra il language nel formato http (it, it-IT) e quello in formato locale
	# viene fatto cercando la prima occorrenza di un valore nel formato xx-YY nell’header http e tradotto nel
	# formato xx_YY accettato da locale. Se ad esempio si riceve un’header nel formato "it,en-US;q=0.8,en;q=0.6,fr;q=0.4"
	# verrà utilizzato l’inglese. Tutto ciò è decisamente tricky, bisogna indagare su un metodo più corretto...
	my $str = shift;
	my $code = 'en_US';
	for my $c (map { s/;.*//; s/-/_/; $_ } split(/,/, $str)) {
		if(length($c) > 2) {
			$code = $c;
			last;
		}
	}
	return $code;
}

sub get_authenticated_user {
	my $api = vars->{api_art};
	my $user = $api->user();
	my $ret = $api->get_user_structure($user->id());
}

sub is_login_route {
	return request->is_post() && request->path() =~ m{$prefix/sessions$};
}

# NOTA: viene chiamata dall'hook before eccetto per le OPTIONS.
#		Può essere utilizzata per gestire business logic basate sull'utente nel caso in cui 
#		vengano esposte delle rotte OPTIONS customizzate
#		
#		Restituisce un array con 
#			- HTTP_STATUS: verificare se è HTTP::Status::is_error
#			- ERROR_MESSAGE: undef se !HTTP::Status::is_error
#			- INTERNAL_ERROR_MESSAGE: undef se !HTTP::Status::is_error
sub init_api_art {
	
	# 401 se sono in modalità jwt e non ho il jwt oppure se sono in auth locale e non c'é una sessione attiva 
	### FIXME: capire come e se deve essere implementato questo controllo
	# if (!request->header('X-API-KEY') && ((_art_auth_by_jwt() && !jwt()) || (!_art_auth_by_proxy() && !_art_auth_by_jwt() && !session('api_art')))) {
	# 	app->destroy_session unless _art_auth_by_jwt; # NOTA: non faccio nulla nel caso jwt perché non so quale è la chiave
	# 	return (HTTP_UNAUTHORIZED, status_message(HTTP_UNAUTHORIZED), __("No JWT or active session"));
	# }
	
	my ($api, $error_code, $error_message, $error_internal_message, $cache) = _restore_api_art();
	unless($api) {
		return ($error_code, $error_message, $error_internal_message);
	}
	
	var api_art => $api;
	var api_art_repo => API::ART::Repository->new (ART => $api);
	var cache => $cache;
	
	return (HTTP_OK, undef, undef);
	
}

############ HOOKS ################

sub hook_before {}

hook before => sub {
	
	$CACHE = SIRTI::Cache->new(DRIVER => "SIRTI::Cache::Driver::Memcached", SERVER => _art_cache_memcached_server())
		if defined _art_cache_memcached_server();

	var code_locale => defined $ENV{HTTP_ACCEPT_LANGUAGE} ? _normalize_locale($ENV{HTTP_ACCEPT_LANGUAGE}) : 'en_US';
	setlocale (LC_ALL, vars->{'code_locale'});

	my $uuid = md5_hex(rand());
	var uuid => $uuid;
	$ENV{_WSART_UUID} = $uuid;
	
	debug "Dancer2::VERSION -> ".$Dancer2::VERSION;
	
	debug "Sono in hook before";
	
	var _to_remove_in_hook_after => [];
	
	my $isOptionsNotPreflightAttach = 0;

	if (
		is_options()
		&&
		!is_preflight
		&&
		(
			request->path() =~ m{$prefix/activities/\d+/history/\d+/attachments/\d+}
			||
			request->path() =~ m{$prefix/activities/\d+/attachments}
		)
	){
		$isOptionsNotPreflightAttach = 1;
	}

	unless ($isOptionsNotPreflightAttach){
		return
			if is_options();
		
		handle_cors_request();
	}
	
	# rotta che non necessita autenticazione per recuperare risorse da condividire (esempio: immagini)
	return
		if request->is_get() && request->path() =~ m{$prefix/shared-resources/}; 
	
	if( is_login_route() ) { # rotta di login
		if(_art_auth_by_jwt) {
			# ripulisco la cache (implicitamente siamo in modalità peer)
			if(jwt()) {
				# non verifico l'errore perché la chiave potrebbe non essere presente
				eval{ $CACHE->delete(get_sid()); };
				eval{ $CACHE->delete(get_sid()."-".$ENV{ARTID}); };
			}
		} else {
			# NOTA: non può essere percorsa se _art_auth_by_proxy()
			if(session('api_art')) {
				app->destroy_session;
			}
		}
		return;
	}
	
	my ($result, $error_message, $error_internal_message) = init_api_art();
	# se e' un errore mi blocco
	if (is_error($result)){
		info "Error:\n" .Dumper {
			 UUID		=> vars->{uuid}
			,message	=> $error_message
			,internalMessage => $error_internal_message
		};
		status($result);
		halt(); # FIXME: bisognerebbe ritornare la struttura $error ma la funzione halt() non lo permette
	}
};

sub hook_after {}

hook after => sub {
	
	debug "Sono in hook after";
	
	if( config->{environment} !~ /^production/ ) {
		response_headers 'X-Process-Pid' => $$;
	}
		
	my $to_remove_in_hook_after = vars->{_to_remove_in_hook_after};
	
	for my $file (@$to_remove_in_hook_after) {
		debug "Unlinking file $file";
		unlink $file;
	}
	
	# nella rotta di logout viene distrutto l'oggetto API::ART::User e di conseguenza non è possibile recuperare questo info
	# e quindi l'env _WSART_LOGGED_USER è stato impostata precedentemente
	$ENV{_WSART_LOGGED_USER} = vars->{api_art}->user()->name() if defined vars->{api_art} && ! defined $ENV{_WSART_LOGGED_USER};

	$CACHE->call_driver_method('disconnect_all')
		if defined $CACHE;
	
};

sub hook_after_file_render {}

hook after_file_render => sub {
	
	my $response = shift;
	
	debug "Sono in hook after_file_render";
	response_headers 'Content-Length' => vars->{_attachment_size} if defined vars->{_attachment_size};
	# raccologo il content-disposition per poter gestire i nomi file con caratteri estesi
	my $contentDisposition = $response->response->header('Content-Disposition');
	$contentDisposition =~ s {
		^(attachment;\s*?)filename="([^"]*?)"(;?.*?)$
	} {
		my ($p1, $p2, $p3) = ($1, $2, $3);
		my $is_utf8 = 1;
		eval {
			$is_utf8 = SIRTI::ART::CGI::Charset::isutf8($p2);
		};
		if ($is_utf8){
			my $origp2 = SIRTI::ART::CGI::Charset::encode_iso88591($p2);
			$p2 = uri_escape_utf8($p2);
			qq/${p1}filename="${origp2}"; filename*=UTF-8''${p2}${p3}/;
		} else {
			$contentDisposition;
		}
	}sexi
	;
	# reimposto il Content-Disposition con il nuovo valore
	response_headers 'Content-Disposition' => $contentDisposition;

};

=pod
@apiDefine ContentTypeJsonHeader
@apiHeader {String} Content-Type It must be set to <code>application/json</code>. 
=cut

=pod
@apiDefine TransitionProperties
@apiParam {Object} [properties] An object representing the transition properties, where the keys are the NAMEs of the properties.
@apiParam {String} properties.NAME The name of the transition property.
=cut

=pod
@apiDefine DateAndFormat
@apiParam {String} [date=Now()] Operation date - *** NYI ***
=cut

prefix $prefix;

######## ROUTE /shared-resources/:ID ###########

=pod
@api {get} /api/art/shared-resources/:ID Get shared resource
@apiName getSharedResources
@apiGroup Shared
@apiDescription Get shared resource

@apiExample {HTTP} Example usage:
GET /api/art/shared-resources/cJ9jvFO0zu HTTP/1.1

@apiSuccess {File} anonymous The binary file.

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK
Content-Disposition: attachment
Content-Length: 34114
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

=cut

get '/shared-resources/:ID' => sub {
	
	# NOTA: la use non serve perchè è interna al modulo API::ART
	my $sr = eval{API::ART::SharedResources->new()};
	return send_ko(INTERNAL_ERROR_MSG => $@)
		if $@;
		
	my $file = $sr->get(RESOURCE_ID => param('ID'));
	
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("Resource not found"), INTERNAL_ERROR_MSG => $sr->last_error())
		unless defined $file;
	
	send_file_ok($file, content_type => "octet/stream");
	
};

=pod
@api {delete} /api/art/session Logout
@apiName Logout
@apiGroup Session
@apiDescription Logout

@apiParam {Number=0,1} [skipTokenCheck=0] WARNING: can be used only in development environment.

@apiExample {HTTP} Example usage:
DELETE http://localhost/api/art/sessions/VSaOEAAARtHoKM3oOfFXOSF4nE3rrOzy

@apiSuccessExample Success-Response:
HTTP/1.1 204 No content

=cut

# TODO:
# - implementare GET
# - implementare PUT per login
######## ROUTE /session ###########

options '/session' => sub { handle_cors_request( METHODS => [ 'DELETE' ] ); return send_ok(IGNORE_SESSION => 1); };

sub route_session_del {}

del '/session' => sub {
		
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return send_ok(CODE => HTTP_NO_CONTENT);

};

######## ROUTE /sessions ###########

options '/sessions' => sub { handle_cors_request( METHODS => [ 'POST' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {post} /api/art/sessions Login
@apiName Login
@apiGroup Session
@apiDescription Login

@apiExample {HTTP} Example usage:
POST http://localhost/api/art/sessions
Content-Type: application/json
Accept: application/json

{
  "username" : "FOO",
  "password" : "BAR"
}

@apiUse ContentTypeJsonHeader

@apiParam {String} username Username of the user.
@apiParam {String} password Password of the user.

@apiSuccess {Object} user The user informations.
@apiSuccess {String} user.username The username of the user logged in.
@apiSuccess {String} user.firstName The fist name of the user logged in.
@apiSuccess {String} user.lastName The last name of the user logged in.
@apiSuccess {String} user.email The email address of the user logged in.
@apiSuccess {String} user.mobilePhone The mobile phone number of the user logged in.

@apiSuccess {String[]} user.groups The groups the user belongs to.
@apiSuccess {Boolean} user.admin <code>true</code> if the user is administrator, <code>false</code> otherwise.
@apiSuccess {Boolean} user.serviceUser <code>true</code> if the user is a service user, <code>false</code> otherwise.
@apiSuccess {String} token The session token. I has to be used to close a session.

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "user" : {
    "username": "JOHN",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "mobilePhone": null,
    "groups": [
      "ADMIN",
      "PM"
    ],
    "admin" : true,
    "serviceUser" : false,
    "disabled" : false
  },
  "token" : "VSaOEAAARtHoKM3oOfFXOSF4nE3rrOzy"
}

=cut

sub route_sessions_post {}

if((!_art_auth_by_jwt() && !_art_auth_by_proxy()) || (_art_auth_by_jwt() && _art_auth_by_jwt_mode() eq 'peer')) {
	post '/sessions' => sub {
	
		my $api_art;
	
		my %body = request->params('body');
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
			unless (keys %body);
		
		# FIXME: elimino l'eventuale dominio inserito nella username per fare in modo che gli utenti extranet possano collegarsi utilizzando
		# <EMAIL> come sono costretti a fare sul portale aziendale
		# questo fix verra' eliminato quando i sistemisti configureranno il radius server definitivo
		#$body{username} =~ s/@.*$// if defined $body{username};
		
		debug 'Creazione nuova istanza API::ART da credenziali passate in POST';
		my %params_new = (
			 ARTID => $ENV{ARTID}
			,USER => $body{username}
			,PASSWORD => $body{password}
			,AUTOSAVE => 0
			,DEBUG => $ENV{ART_DB_DEBUG}
			,DEFAULT_DATE_FORMAT => 'YYYYMMDDHH24MISS'
			,AUTH => $AUTH
		);
		if ($ENV{WSART_SESSION_DURATION}){
			# non utilizziamo il metodo get_sid in quanto essendo in fase di login è necessario staccare
			# il nuovo SID
			$params_new{SID} = session->id();
			$params_new{SESSION_DURATION} = $ENV{WSART_SESSION_DURATION};
		}
		eval {
			$api_art = API::ART->new(%params_new);
		};
		if ($@) {
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Incorrect username or password"), INTERNAL_ERROR_MSG => __x("Incorrect username or password for user {username}: {error}", username => $body{username}||"", error => $@));
		}
		
		# salvo username per metterle nel cache server
		if(_art_auth_by_jwt()) {
			var username => $body{username};
		}
		
		var api_art => $api_art;
	
		my $ret = {
			 user => get_authenticated_user()
		};
	
		info 'User '.$api_art->user()->name().' auth by '.$api_art->user()->auth_type;
	
		$LOOKUP = $api_art->lookup_get()
			unless defined $LOOKUP;
		
		return send_ok(MSG => $ret);
	
	};
}

######## ROUTE /sessions:TOKEN ###########

options '/sessions/:TOKEN' => sub { handle_cors_request( METHODS => [ 'DELETE' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {delete} /api/art/sessions/:TOKEN Logout (Deprecato utilizzare DELETE /api/art/session)
@apiName Logout Session
@apiGroup Session
@apiDescription Logout Session

@apiParam {Number=0,1} [skipTokenCheck=0] WARNING: can be used only in development environment.

@apiExample {HTTP} Example usage:
DELETE http://localhost/api/art/sessions/VSaOEAAARtHoKM3oOfFXOSF4nE3rrOzy

@apiSuccessExample Success-Response:
HTTP/1.1 204 No content

=cut

sub route_sessions_token_del {}

# la rotta di logout viene esposta solo in modalità jwt peer e cookie
if((_art_auth_by_jwt && _art_auth_by_jwt_mode() eq 'peer') || _art_auth_by_proxy) {
	del '/sessions/:TOKEN' => sub {
	
		my %query_params = defined request->params('query') ? request->params('query') : ();
		my $token = defined request->header(_art_auth_by_proxy_header) ? get_sid() :
			(_art_auth_by_jwt && _art_auth_by_jwt_mode() eq 'peer') ? jwt_raw_token() : get_sid();
		
		unless ( config->{environment} !~ /^production/ && $query_params{skipTokenCheck} ) {
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Bad token value"), INTERNAL_ERROR_MSG => __x("You must use the value {sessionid}", sessionid => $token))
				unless param('TOKEN') eq $token;
		}
		
		return send_ok(CODE => HTTP_NO_CONTENT);
	
	};
}

######## ROUTE /favorites ###########

options '/favorites' => sub { handle_cors_request( METHODS => [ 'POST', 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };

=pod
@api {post} /api/art/favorites Create Favorite
@apiName createFavorites
@apiGroup Favorites
@apiDescription Create new favorite

@apiExample {HTTP} Example usage:
POST http://localhost/api/art/favorites
Content-Type: application/json
Accept: application/json

{
  "description" : "DII",
  "url" : "/artsoen/home",
  "additionalKeys" : {
    "contracts" : ["FTTH","CREATION"],
    "customer": "TIM"
  },
  
}

@apiParam {String} description Description of the favorite to create.
@apiParam {String} url Url of the favorite to create.
@apiParam {Object} additionalKeys Hash with the addition keys for the favorite to create

@apiSuccess {Number} id The id of the favorite created.
@apiSuccess {String} description The description of the favorite created.
@apiSuccess {String} url The URL of the favorite created.
@apiSuccess {String} creationDate The creation date of the favorite created.
@apiSuccess {String} lastUpdateDate The last update date of the favorite created.
@apiSuccess {String} cancelDate The cancel date of the favorite created.
@apiSuccess {Object} additionalKeys The additional keys of the favorite created.

@apiSuccessExample Success-Response:
HTTP/1.1 201 CREATED

{
  "id": 1,
  "cancelDate": null,
  "lastUpdateDate": "2021-07-01T09:27:27.*********+02:00",
  "creationDate": "2021-07-01T09:27:27.*********+02:00",
  "url": "www.google.com",
  "additionalKeys": {
    "paperino": [
      "qui",
      "quo",
      "qua"
    ],
    "pippo": "pluto"
  },
  "description": "test"
}

=cut

sub _get_favorite_structure{
	
	my %params = @_;

	my $api = vars->{api_art};

	my $favorite = $params{FAVORITE};
	my $info = $favorite->info();
	
	return {
		"id" => $favorite->id()*1,
		"url" => $info->{URL},
		"lastUpdateDate" => $api->get_iso_date_from_date($info->{LAST_UPDATE_DATE}),
		"cancelDate" => $info->{CANCEL_DATE} ? $api->get_iso_date_from_date($info->{CANCEL_DATE}) : undef,
		"creationDate" => $api->get_iso_date_from_date($info->{CREATION_DATE}),
		"description" => $info->{DESCRIPTION},
		"additionalKeys" => $info->{ADDITIONAL_KEYS},
	};
}

post '/favorites' => sub {
	
	my $api = vars->{api_art};
    
    my %body = request->params('body');
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
		unless (keys %body);
	
	for my $k ('description','url'){
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing {paramname} param", paramname => $k))
			unless (defined $body{$k});
	}

	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Bad value for {paramname} param", paramname => "additionalKeys"))
		if (exists $body{additionalKeys} && ref($body{additionalKeys}) ne 'HASH');
	
	my %create_params = (
		DESCRIPTION		=> $body{description}
		,URL			=> $body{url}
	);
	$create_params{ADDITIONAL_KEYS} = $body{additionalKeys}
		if exists $body{additionalKeys}; 

	my $ca = API::ART::Collection::Favorite->new( ART => $api );
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless $ca;

	my $favorite = $ca->create(%create_params);
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
		unless $favorite;
	
	my $ret = _get_favorite_structure(FAVORITE => $favorite);
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_CREATED, MSG => $ret);
	
};

=pod
@api {get} /api/art/favorites Get Favorites
@apiName getFavorites
@apiGroup Favorites
@apiDescription Get favorites

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/favorites
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
  {
    "id": 1,
	"cancelDate": null,
    "lastUpdateDate": "2021-07-01T09:27:27.*********+02:00",
    "creationDate": "2021-07-01T09:27:27.*********+02:00",
    "url": "www.google.com",
    "additionalKeys": {
      "paperino": [
        "qui",
        "quo",
        "qua"
      ],
      "pippo": "pluto"
    },
    "description": "test"
  }
]
=cut

get '/favorites' => sub {
	
	my $api = vars->{api_art};
    
	my $ca = API::ART::Collection::Favorite->new( ART => $api );
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless $ca;

	my $favorites = $ca->find();
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
		unless $favorites;
	
	my $ret = [];

	for my $favorite (@{$favorites}){
		push @{$ret}, _get_favorite_structure(FAVORITE => $favorite);
	}
	
	return send_ok(MSG => $ret);
	
};

######## ROUTE /favorites/:ID ###########

options '/favorites/:ID' => sub { handle_cors_request( METHODS => [ 'GET', 'PUT', 'DELETE' ] ); return send_ok(IGNORE_SESSION => 1); };

=pod
@api {put} /api/art/favorites/:ID Update Favorite
@apiName updateFavorite
@apiGroup Favorites
@apiDescription Update favorite

@apiUse ContentTypeJsonHeader

@apiExample {HTTP} Example usage:
PUT http://localhost/api/art/favorites/1
Content-Type: application/json

{
  "url": "www.sirti.it",
  "description": "SIRTI",
  "additionalKeys": {}
}

@apiParam {String} description Description of the favorite to update.
@apiParam {String} url Url of the favorite to update.
@apiParam {Object} additionalKeys Hash with the addition keys for the favorite to update

@apiSuccessExample Success-Response:
HTTP/1.1 204 No content

=cut

put '/favorites/:ID' => sub {
	
	my $api = vars->{api_art};

	my %body = request->params('body');
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
		unless (keys %body);
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Bad value for {paramname} param", paramname => "additionalKeys"))
		if (exists $body{additionalKeys} && ref($body{additionalKeys}) ne 'HASH');
	
	my $map = {
		description => 'DESCRIPTION',
		url => 'URL',
		additionalKeys => 'ADDITIONAL_KEYS',
	};

	my %update_params = ();
	for my $k (keys %$map){
		$update_params{$map->{$k}} = $body{$k}
			if exists $body{$k};
	}

	my $favorite = API::ART::Favorite->new( ART => $api, ID => param('ID'));
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless $favorite;

	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
		unless $favorite->update(%update_params);
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_NO_CONTENT);
};

=pod
@api {get} /api/art/favorites/:ID Get Favorite
@apiName getFavorite
@apiGroup Favorites
@apiDescription Get favorite

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/favorites/:ID
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "id": 1,
  cancelDate": null,
  "lastUpdateDate": "2021-07-01T09:27:27.*********+02:00",
  "creationDate": "2021-07-01T09:27:27.*********+02:00",
  "url": "www.google.com",
  "additionalKeys": {
    "paperino": [
      "qui",
      "quo",
      "qua"
    ],
    "pippo": "pluto"
  },
  "description": "test"
}

=cut

get '/favorites/:ID' => sub {
	
	my $api = vars->{api_art};
    
	my $favorite = API::ART::Favorite->new( ART => $api, ID => param('ID'));
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless $favorite;
	
	return send_ok(MSG => _get_favorite_structure(FAVORITE => $favorite));
	
};

=pod
@api {del} /api/art/favorites/:ID Delete Favorite
@apiName deleteFavorite
@apiGroup Favorites
@apiDescription Delete favorite

@apiUse ContentTypeJsonHeader

@apiExample {HTTP} Example usage:
DELETE http://localhost/api/art/favorites/1
Content-Type: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "id": 1,
  cancelDate": "2021-07-01T09:57:27.*********+02:00",
  "lastUpdateDate": "2021-07-01T09:27:27.*********+02:00",
  "creationDate": "2021-07-01T09:27:27.*********+02:00",
  "url": "www.google.com",
  "additionalKeys": {
    "paperino": [
      "qui",
      "quo",
      "qua"
    ],
    "pippo": "pluto"
  },
  "description": "test"
}

=cut

del '/favorites/:ID' => sub {
	
	my $api = vars->{api_art};

	my $favorite = API::ART::Favorite->new( ART => $api, ID => param('ID'));
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless $favorite;

	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
		unless $favorite->cancel();

	my $ret = _get_favorite_structure(FAVORITE => $favorite);

	do_api_save( $api );
	
	return send_ok(MSG => $ret);
};

######## ROUTE /instance/types/activities ###########

options '/instance/types/activities' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/instance/types/activities Activity types list
@apiName instanceTypesActivities
@apiGroup Instance
@apiDescription Return activity types list

@apiSuccess {Object[]} anonymous The activity type informations.
@apiSuccess {String} anonymous.name The name of the activity type.
@apiSuccess {String} anonymous.description The description of the activity type.
@apiSuccess {Boolean} anonymous.canAssign  <code>true</code> if the activity can be assigned and the user has privilige to assigne the activity to a user, <code>false</code> otherwise.
@apiSuccess {Boolean} anonymous.canDeassign <code>true</code> if the activity can be deassigned and the user has privilige to deassigne the activity to a user, <code>false</code> otherwise.
@apiSuccess {Boolean} anonymous.hasDashboard <code>true</code> if the activity type provides dashboard, <code>false</code> otherwise.
@apiSuccess {Boolean} anonymous.hasReport <code>true</code> if the activity type provides report, <code>false</code> otherwise.
@apiSuccess {String} anonymous.UIroute The UI route of the activity type.
@apiSuccess {String} anonymous.documentTypes The array with the document types available for the activity type.
@apiSuccess {Boolean} anonymous.canUpdateActivityProperties <code>true</code> if the user can update activity property, <code>false</code> otherwise.


@apiExample {HTTP} Example usage:
GET http://localhost/api/art/instance/types/activities
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
  {
    "name": "FDM",
    "description": "Finestra di Manutenzione",
    "canAssign": true,
    "canDeassign": false,
    "hasDashboard": false,
    "hasReport": false,
    "UIroute": undef,
    "documentTypes": [],
    "canUpdateActivityProperties": true
  },
  {
    "name": "WO",
    "description": "WO",
    "canAssign": true,
    "canDeassign": false,
    "hasDashboard": true,
    "hasReport": false,
    "UIroute": undef,
    "documentTypes": [],
    "canUpdateActivityProperties": false
  },
  {
    "name": "RR",
    "description": "Richiesta Risorse",
    "canAssign": true,
    "canDeassign": false,
    "hasDashboard": true,
    "hasReport": false,
    "UIroute": undef,
    "documentTypes": [],
    "canUpdateActivityProperties": true
  },
  {
    "name": "KARTATT",
    "description": "gestione kart",
    "canAssign": true,
    "canDeassign": false,
    "hasDashboard": true,
    "hasReport": false,
    "UIroute": undef,
    "documentTypes": [],
    "canUpdateActivityProperties": false
  },
  {
    "name": "WOB",
    "description": "WorkOrder Bloccante",
    "canAssign": true,
    "canDeassign": false,
    "hasDashboard": true,
    "hasReport": false,
    "UIroute": undef,
    "documentTypes": [
      {
        "id": 1,
        "documentTypeId": "Altro",
        "description": "Documento generico" 
      }
    ],
    "canUpdateActivityProperties": false
  }
]
=cut

get '/instance/types/activities' => sub {
	
	my $api = vars->{api_art};
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	my %ret = ();
	
	my $activity_types = eval{$api->enum_activity_type(EXTENDED_OUTPUT => 1, SHOW_ONLY_WITH_VISIBILITY => 1)};
	
	return send_ko(INTERNAL_ERROR_MSG => $@)
		if ($@);
	
	my $ret = [];
	for my $ta (@{$activity_types}) {
		my $r = {
			 name							=> $ta->{ACTIVITY_TYPE_NAME}
			,description					=> $ta->{ACTIVITY_TYPE_DESCRIPTION}
			,canAssign						=> $ta->{ACTIVITY_TYPE_CAN_ASSIGN} ? $JSON::true : $JSON::false
			,canDeassign					=> $ta->{ACTIVITY_TYPE_CAN_DEASSIGN} ? $JSON::true : $JSON::false
			,hasDashboard					=> $ta->{ACTIVITY_TYPE_HAS_DASHBOARD} ? $JSON::true : $JSON::false
			,hasReport						=> $ta->{ACTIVITY_TYPE_HAS_REPORT} ? $JSON::true : $JSON::false
			,UIroute						=> $ta->{ACTIVITY_TYPE_UI_ROUTE}
			,documentTypes					=> []
			,canUpdateActivityProperties	=> $ta->{ACTIVITY_TYPE_AP_GROUPS_PERM} ? $JSON::true : $JSON::false
		};
		for my $t (@{$ta->{ACTIVITY_TYPE_DOCUMENT_TYPES}}){
			push @{$r->{documentTypes}}, {
				id => $t->{ID},
				documentTypeId => $t->{ID_TIPO_DOCUMENTO},
				description => $t->{DESCRIZIONE},
				propertyName => $t->{NOME_TIPO_DATO_TECNICO_ATT}
			};
		}
		
		push @$ret, $r;
	}

	return send_ok(MSG => $ret);
		
		
};

######## ROUTE /instance/types/activities/properties ###########

options '/instance/types/activities/properties' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/instance/types/activities/properties Activity properties
@apiName instanceTypesActivitiesProperties
@apiGroup Instance
@apiDescription Return activity properties

@apiParam {String} [type] Returns the property informations suitable only for the specific activity type.
@apiParam {String} [action] Returns the property informations suitable only for the action; you can use it only if defined type param.

@apiSuccess {Object[]} anonymous The property informations.
@apiSuccess {Object} anonymous.NAME The NAME of the property.
@apiSuccess {String} anonymous.NAME.type The type of the property.
@apiSuccess {String} anonymous.NAME.label The label of the property.
@apiSuccess {Object} anonymous.NAME.predefinedValue The predefined value of the property
@apiSuccess {string} anonymous.NAME.predefinedValue.type The type of predefined value of the property
@apiSuccess {string} [anonymous.NAME.predefinedValue.value] The value of predefined value of the property if applicable
@apiSuccess {String} [anonymous.NAME.hint] The hint of the property
@apiSuccess {Object[]} [anonymous.NAME.values] TBD
@apiSuccess {String} [anonymous.NAME.target] TBD
@apiSuccess {String} [anonymous.NAME.template] TBD
@apiSuccess {String} [anonymous.NAME.groupBy] TBD
@apiSuccess {String} [anonymous.NAME.context] TBD
@apiSuccess {String} [anonymous.NAME.contextKey] TBD: defined only if context defined
@apiSuccess {String} [anonymous.NAME.displayProperty] TBD: defined only if context defined
@apiSuccess {String} [anonymous.NAME.keyProperty] TBD: defined only if context defined
@apiSuccess {String} [anonymous.NAME.minLength] TBD: defined only if context defined
@apiSuccess {String} [anonymous.NAME.addFromAutocompleteOnly] TBD: defined only if context defined
@apiSuccess {String} [anonymous.NAME.queryKey] TBD: defined only if context defined
@apiSuccess {String} [anonymous.NAME.responseDataKey] TBD: defined only if context defined
@apiSuccess {Object[]} [anonymous.NAME.currency] Defined only if type = 'CURRENCY'
@apiSuccess {Object[]} [anonymous.NAME.fractions] Defined only if type = 'CURRENCY'
	
@apiExample {HTTP} Example usage:
GET http://localhost/api/art/instance/types/activities/properties
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "forecastEndDate":{
    "predefinedValue":null,
    "label":"Data prevista FL",
    "type":"DAY"
  },
  "execProgRev":{
    "predefinedValue":{
      "value":"0",
      "type":"const"
    },
    "label":"Revisione progetto esecutivo",
    "type":"NUMBER"
  },
  "accountablePhoneNumber":{
    "predefinedValue":{
      "value":"[\"info\"][\"currentUserObj\"][\"mobilePhone\"]",
      "type":"bracketNotation"
    },
    "label":"Telefono responsabile attività",
    "type":"STRING"
  },
  "dateDocCIL":{
    "predefinedValue":{
      "type":"now"
    },
    "label":"Data emissione CIL",
    "type":"DAY"
  }
}

=cut

sub handler_instance_types_activities_properties_get {
	
	my %input_params = @_;
	
	my %query_params = defined $input_params{QUERY_PARAMS} ? %{$input_params{QUERY_PARAMS}} : ();
	
	my $api = vars->{api_art};
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Param type can't be an ARRAY")
		if defined $query_params{'type'} && ref $query_params{'type'};
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Param action can't be an ARRAY")
		if defined $query_params{'action'} && ref $query_params{'action'};
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Param action can be used only if defined param type")
		if defined $query_params{'action'} && !defined $query_params{'type'};
	
	my %p = (
		EXTENDED_OUTPUT	=>	1
	);
	
	$p{ACTIVITY_TYPE_NAME} = $query_params{'type'} if defined $query_params{'type'};
	$p{ACTION} = $query_params{'action'} if defined $query_params{'action'};
	
	# il rest mostra sempre solo quello su cui si ha visibilità
	$p{SHOW_ONLY_WITH_VISIBILITY} = 1;
	
	my $results = eval{$api->enum_activity_property(%p)};
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Bad TYPE"), INTERNAL_ERROR_MSG => $@)
		if ($@);
		
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless defined $results;
	
	my $ret = {};
	
	while ( my ($key, $value) = each %{$results} ) {
		
		$ret->{$key} = {
			 type => $value->{TYPE},
			 label => $value->{LABEL},
			 predefinedValue => $value->{PREDEFINED_VALUE}
		};
		
		$ret->{$key}->{hint} = $value->{HINT} if defined $value->{HINT};
		$ret->{$key}->{target} = _remap_keys( $value->{TARGET}, USE_DOLLAR => 1 ) if defined $value->{TARGET};
		$ret->{$key}->{template} = $value->{TEMPLATE} if defined $value->{TEMPLATE};
		$ret->{$key}->{groupBy} = $value->{GROUP_BY} if defined $value->{GROUP_BY};
		$ret->{$key}->{match} = $value->{MATCH} if defined $value->{MATCH};
		$ret->{$key}->{scope} = $value->{SCOPE} if defined $value->{SCOPE};
		$ret->{$key}->{layout} = $value->{LAYOUT} if defined $value->{LAYOUT};
		$ret->{$key}->{inputFormat} = $value->{INPUT_FORMAT} if defined $value->{INPUT_FORMAT};
		if (defined $value->{ATTRIBUTES}) {
			$ret->{$key}->{attributes} = {};
			$ret->{$key}->{attributes}->{baseurl} = $value->{ATTRIBUTES}->{BASEURL} if defined $value->{ATTRIBUTES}->{BASEURL};
			$ret->{$key}->{attributes}->{params} = $value->{ATTRIBUTES}->{PARAMS} if defined $value->{ATTRIBUTES}->{PARAMS};
		}

		if ($value->{TYPE} =~/^(LIST)$/){
			$ret->{$key}->{'values'} = $value->{VALUES};
		} elsif ($value->{TYPE} =~/^(LOOKUP|MOOKUP)$/){
			if (defined $value->{VALUES}){
				if($value->{LAYOUT} eq 'TABLE') {
					$ret->{$key}->{'values'} = $value->{VALUES};
				} else {
					$ret->{$key}->{'values'} = [] ;
					for my $val (@{$value->{VALUES}}){
						my $new_values = {};
						while ( my ($key_val, $value_val) = each %{$val} ) {
							$new_values->{_remap_keys( $key_val, USE_DOLLAR => 1 )} = $value_val;
						}
						push @{$ret->{$key}->{'values'}}, $new_values;
					}
				}
			}
			if (defined $value->{ORDER_BY}){
				$ret->{$key}->{'orderBy'} = [] ;
				for my $val (@{$value->{ORDER_BY}}){
					while ( my ($key_val, $value_val) = each %{$val} ) {
						push @{$ret->{$key}->{'orderBy'}}, {_remap_keys( $key_val, USE_DOLLAR => 1 ) => $value_val};
					}
				}
			}
			if($value->{LAYOUT} =~ /^(AUTOCOMPLETE|SELECT)$/) {
				$ret->{$key}->{uri} = $value->{URI};
				$ret->{$key}->{context} = $value->{CONTEXT};
				$ret->{$key}->{contextKey} = $value->{CONTEXT_KEY} if defined $value->{CONTEXT_KEY};
				$ret->{$key}->{displayProperty} = $value->{DISPLAY_PROPERTY} if defined $value->{DISPLAY_PROPERTY};
				$ret->{$key}->{keyProperty} = $value->{KEY_PROPERTY} if defined $value->{KEY_PROPERTY};
				$ret->{$key}->{queryKey} = $value->{QUERY_KEY} if defined $value->{QUERY_KEY};
				$ret->{$key}->{responseDataKey} = $value->{RESPONSE_DATA_KEY} if defined $value->{RESPONSE_DATA_KEY};
				$ret->{$key}->{minLength} = $value->{MIN_LENGTH} if defined $value->{MIN_LENGTH};
				$ret->{$key}->{addFromAutocompleteOnly} = $value->{ADD_FROM_AUTOCOMPLETE_ONLY} ? $JSON::true : $JSON::false;
			}
		} elsif ($value->{TYPE} eq 'TAGS'){
			$ret->{$key}->{'values'} = [] ;
			for my $val (@{$value->{VALUES}}){
				push @{$ret->{$key}->{'values'}}, {count => $val->{COUNT}, tag => $val->{TAG}};
			}
		} elsif ($value->{TYPE} eq 'RADIO'){
			$ret->{$key}->{'values'} = $value->{VALUES} ;
		} elsif ($value->{TYPE} eq 'POPUP'){
			$ret->{$key}->{'values'} = $value->{VALUES} ;
		} elsif ($value->{TYPE} eq 'CURRENCY'){
			$ret->{$key}->{'currency'} = $value->{CURRENCY} ;
			$ret->{$key}->{'fractions'} = $value->{FRACTIONS} ;
		}
	}
	
	return send_ok(MSG => $ret);
		
};

get '/instance/types/activities/properties' => sub {
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return handler_instance_types_activities_properties_get(QUERY_PARAMS => \%query_params);
	
};

######## ROUTE /instance/types/activities/:TYPE ###########

options '/instance/types/activities/:TYPE' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/instance/types/activities/:TYPE Activity type info
@apiName instanceTypesActivitiesTypeInfo
@apiGroup Instance
@apiDescription Return activity type info

@apiSuccess {Object} STATUS Object representing the actions for the status named STATUS
@apiSuccess {Object} STATUS.ACTION Object representing the properties for the action named ACTION
@apiSuccess {Object[]} STATUS.ACTION.properties Object representing the features of the property
@apiSuccess {Boolean} STATUS.ACTION.assignable DEPRECATED: see the definition of the key <code>isAssignType</code> returned by API <strong>Instance - Activity type actions</strong> <code>GET /api/art/instance/types/activities/:TYPE/actions</code>.
@apiSuccess {Boolean} STATUS.ACTION.assignNotification DEPRECATED: see the definition of the key <code>doAssignNotification</code> returned by API <strong>Instance - Activity type actions</strong> <code>GET /api/art/instance/types/activities/:TYPE/actions</code>.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/instance/types/activities/EPEL_IC
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "OPEN":{
    "TAKE_IN_CHARGE":{
      "assignable": false,
      "properties":[

      ]
    },
    "REVOCATION":{
      "assignable": true,
      "properties":[
        {
          "name":"REASON",
          "hidden":false,
          "mandatory":true,
          "prePopulate":false,
          "readOnly":false
        }
      ]
    }
  },
  "UPDATING_EFFORT":{
    "UPDATE_TOTAL_EFFORT":{
      "assignable": false,
      "properties":[
        {
          "name":"TOTAL_EFFORT",
          "hidden":false,
          "mandatory":true,
          "prePopulate":false,
          "readOnly":false
        }
      ]
    }
  }
}

=cut

get '/instance/types/activities/:TYPE' => sub {
	
	my $api = vars->{api_art};
    
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	my %ret = ();

	my $activity_type_id = $api->get_activity_type_id(param('TYPE'));
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Invalid activity type name {type}", type => param('TYPE')))
		unless defined $activity_type_id;

	my $status = eval{$api->enum_activity_status(ACTIVITY_TYPE_NAME => param('TYPE'))};
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Bad TYPE", INTERNAL_ERROR_MSG => $@)
		if ($@);
	
	while ( my ($key_status, $value_status) = each %{$status} ) {
		my $actions = {};
		if ($key_status eq 'START'){
			$actions->{$api->get_activity_action_open_name()}	= 'A'
				if $api->test_permission(
					ACTIVITY_TYPE_ID => $activity_type_id
					,STATUS_ID => $api->get_activity_status_id ($key_status)
					,GROUPS_ID => $api->user()->su()->activity()->groups()
					,ACTION_ID => 'A'
				);
		}else {
			$actions = $api->enum_activity_action(ACTIVITY_TYPE_NAME => param('TYPE'), INITIAL_STATUS_ID => $value_status);
		}
		
		while ( my ($key_action, $value_action) = each %{$actions} ) {
			my $props = $api->get_action_properties(param('TYPE'), $key_action);
			$ret{$key_status}->{$key_action}->{properties} = [];
			$ret{$key_status}->{$key_action}->{assignable} = $api->is_action_name_assignable($key_action) ? $JSON::true : $JSON::false;
			$ret{$key_status}->{$key_action}->{assignNotification} = $ret{$key_status}->{$key_action}->{assignable} && $api->email_support() ? $JSON::true : $JSON::false;
			for my $prop (@{$props}){
				push @{$ret{$key_status}->{$key_action}->{properties}}, {
					name => $prop->{NOME_TDTA}
					, mandatory => ($prop->{OBBLIGATORIO} eq 'Y' ? $JSON::true : $JSON::false)
					, readOnly => ($prop->{SOLO_LETTURA} eq 'Y' ? $JSON::true : $JSON::false)
					, hidden => ($prop->{NASCOSTO} eq 'Y' ? $JSON::true : $JSON::false)
					, prePopulate => ($prop->{PREPOPOLA} eq 'Y' ? $JSON::true : $JSON::false)
				};
			}
		}
	}
	
	return send_ok(MSG => \%ret);
		
		
};

######## ROUTE /instance/types/activities/:TYPE/properties ###########

options '/instance/types/activities/:TYPE/properties' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/instance/types/activities/:TYPE/properties Activity type properties
@apiName instanceTypesActivitiesTypeProperties
@apiGroup Deprecated
@apiDescription Return activity type properties

**DEPRECATED**: use route **Activity properties**

@apiParam {String} [action] Returns the property informations suitable only for the action.

@apiSuccess {Object[]} anonymous The property informations.
@apiSuccess {Object} anonymous.NAME The NAME of the property.
@apiSuccess {String} anonymous.NAME.type The type of the property.
@apiSuccess {String} anonymous.NAME.label The label of the property.
@apiSuccess {Object} anonymous.NAME.predefinedValue The predefined value of the property
@apiSuccess {string} anonymous.NAME.predefinedValue.type The type of predefined value of the property
@apiSuccess {string} [anonymous.NAME.predefinedValue.value] The value of predefined value of the property if applicable
@apiSuccess {String} [anonymous.NAME.hint] The hint of the property
@apiSuccess {Object[]} [anonymous.NAME.values] TBD
@apiSuccess {String} [anonymous.NAME.target] TBD
@apiSuccess {String} [anonymous.NAME.template] TBD
@apiSuccess {String} [anonymous.NAME.groupBy] TBD
@apiSuccess {String} [anonymous.NAME.context] TBD
@apiSuccess {String} [anonymous.NAME.contextKey] TBD: defined only if context defined
@apiSuccess {String} [anonymous.NAME.displayProperty] TBD: defined only if context defined
@apiSuccess {String} [anonymous.NAME.keyProperty] TBD: defined only if context defined
@apiSuccess {String} [anonymous.NAME.minLength] TBD: defined only if context defined
@apiSuccess {String} [anonymous.NAME.addFromAutocompleteOnly] TBD: defined only if context defined
@apiSuccess {String} [anonymous.NAME.queryKey] TBD: defined only if context defined
@apiSuccess {String} [anonymous.NAME.responseDataKey] TBD: defined only if context defined
	
@apiExample {HTTP} Example usage:
GET http://localhost/api/art/instance/types/activities/EPEL_IC/properties
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "forecastEndDate":{
    "predefinedValue":null,
    "label":"Data prevista FL",
    "type":"DAY"
  },
  "execProgRev":{
    "predefinedValue":{
      "value":"0",
      "type":"const"
    },
    "label":"Revisione progetto esecutivo",
    "type":"NUMBER"
  },
  "accountablePhoneNumber":{
    "predefinedValue":{
      "value":"[\"info\"][\"currentUserObj\"][\"mobilePhone\"]",
      "type":"bracketNotation"
    },
    "label":"Telefono responsabile attività",
    "type":"STRING"
  },
  "dateDocCIL":{
    "predefinedValue":{
      "type":"now"
    },
    "label":"Data emissione CIL",
    "type":"DAY"
  }
}

=cut

get '/instance/types/activities/:TYPE/properties' => sub {
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	$query_params{type} = param('TYPE');
	
	return handler_instance_types_activities_properties_get(QUERY_PARAMS => \%query_params);
	
};

######## ROUTE /instance/types/activities/:TYPE/activityProperties ###########

options '/instance/types/activities/:TYPE/activityProperties' => sub { handle_cors_request( METHODS => [ 'GET', 'PUT' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/instance/types/activities/:TYPE/activityProperties Activity type activity properties
@apiName instanceTypeActivityTypeActivityProperties
@apiGroup Instance
@apiDescription Return the activity properties designed for the activity TYPE, showing the organization of the groups of them.

@apiSuccess {Object[]} anonymous Object representing the activity properties group
@apiSuccess {String} [anonymous.group] Name of the group
@apiSuccess {Number} [anonymous.id] Id of the group
@apiSuccess {Object[]} anonymous.properties Object representing the activity property
@apiSuccess {String} anonymous.properties.name Name of the property
@apiSuccess {Boolean} anonymous.properties.readOnly <code>true</code> if the property is readOnly, <code>false</code> otherwise.
@apiSuccess {Boolean} anonymous.properties.expired <code>false</code> if the property is no longer associated to at least an action of the activity type, <code>true</code> otherwise.
@apiSuccess {Boolean} anonymous.properties.nullable <code>true</code> if the property can be set to null, <code>false</code> otherwise.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/instance/types/activities/EPCCL/activityProperties
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
  {
    "group": "GROUP_1"
    ,"id" : 1
    ,"properties": [
      {
        "expired":false,
        "readOnly":false,
        "name":"END_DATE",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"ENGAGE_DESCRIPTION",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"ESTIMATED_EFFORT",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"ESTIMATED_EFFORT_OUTLOOK",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"ID_EPRL",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"PROJECT_COMMESSA",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"REASON",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"RESOURCE",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"RESOURCE_ENGAGE_DESC",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"RESOURCE_ENGAGE_END_DATE",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"RESOURCE_ENGAGE_SHORT_DESC",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"RESOURCE_ENGAGE_START_DATE",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"RESOURCE_LIST",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"RESOURCE_LIST_BL",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"RESOURCE_LIST_OG",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"RESOURCE_REVOKE",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"SCOPE",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"START_DATE",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"SUPERVISOR",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"TOTAL_ACCOUNTED_EFFORT",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"TOTAL_ESTIMATED_EFFORT",
        "nullable":false
      }
    ]
  },
  {
  	"properties":[
      {
        "expired":false,
        "readOnly":false,
        "name":"ACCOUNTED_EFFORT",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"CATEGORY_LIST",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"COMPETENCE_CENTER_MANAGER",
        "nullable":false
      }
    ]
  }
]

=cut

get '/instance/types/activities/:TYPE/activityProperties' => sub {
	
	my $api = vars->{api_art};
    
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Invalid activity type name {type}", type => param('TYPE')))
		unless defined $api->test_activity_type_name(param('TYPE'));

	my $results = eval{$api->get_activity_properties_group(SHOW_ONLY_WITH_VISIBILITY => 1, ACTIVITY_TYPE_NAME => [param('TYPE')])};
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Bad TYPE", INTERNAL_ERROR_MSG => $@)
		if ($@);
	
	my $rets = [];
	
	
	for my $res (@{$results}){
		
		my $ret = {};
		$ret->{group} = $res->{GROUP} if defined $res->{GROUP};
		$ret->{id} = $res->{ID} if defined $res->{ID};
		$ret->{properties} = [];
		for my $prop (@{$res->{PROPERTIES}}){
			push @{$ret->{properties}}, {
				name => $prop->{NAME}
				,readOnly => $prop->{READ_ONLY} eq 'Y' ? $JSON::true : $JSON::false 
				,expired => $prop->{EXPIRED} eq 'Y' ? $JSON::true : $JSON::false 
				,nullable => $prop->{NULLABLE} eq 'Y' ? $JSON::true : $JSON::false 
			};
		}
		
		push @{$rets}, $ret;
	}
	
	return send_ok(MSG => $rets);
		
		
};

=pod
@api {put} /api/art/instance/types/activities/:TYPE/activityProperties Modify activity type activity properties
@apiName instanceModifyActivityProperties
@apiGroup Instance
@apiDescription Set the activity properties designed for the activity TYPE, with the organization of the groups of them.

@apiUse ContentTypeJsonHeader

@apiExample {HTTP} Example usage:
PUT http://localhost/api/art/instance/types/activities/EPCCL/activityProperties
Content-Type: application/json

{
  "ap": [
    {
      "group": "Predefiniti",
      "properties": [
        {
          "name": "DATA_RICHIESTA",
          "readOnly": true,
          "nullable": false
        }
      ]
    },
    {
      "group": "Richiesta",
      "properties": [
        {
          "name": "ID_PRATICA_SOFIA",
          "readOnly": true,
          "nullable": true
        }
      ]
    },
    {
      "properties": [
        {
          "name": "TIPO_INTERVENTO",
          "readOnly": false,
          "nullable": false
        },
        {
          "name": "NUMERO_CODA",
          "readOnly": false,
          "nullable": false
        }
      ]
    }
  ]
}

@apiParam {Object[]} ap Object representing the activity properties group
@apiParam {String} [ap.group] Name of the group
@apiParam {Object[]} ap.properties Object representing the activity property
@apiParam {String} ap.properties.name Name of the property
@apiParam {Boolean} ap.properties.readOnly <code>true</code> if the property is readOnly, <code>false</code> otherwise
@apiParam {Boolean} ap.properties.nullable <code>true</code> if the property can be set to null, <code>false</code> otherwise

@apiSuccessExample Success-Response:
HTTP/1.1 204 No content

=cut

put '/instance/types/activities/:TYPE/activityProperties' => sub {
	
	my $api = vars->{api_art};

	my %body = request->params('body');
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
		unless (keys %body);
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Invalid activity type name {type}", type => param('TYPE')))
		unless defined $api->test_activity_type_name(param('TYPE'));

	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing {paramname} param", paramname => "ap"))
		unless ($body{ap});
	return send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => __x("{paramname} param must be an array", paramname => "ap"))
		unless (ref($body{ap}) eq 'ARRAY');
	
	my $params = [];
	for my $g (@{$body{ap}}) {
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing {paramname} param", paramname => "ap.properties"))
			unless ($g->{properties});
		return send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => __x("{paramname} param must be an array", paramname => "ap.properties"))
			unless (ref($g->{properties}) eq 'ARRAY');
		my $tmp = {
			PROPERTIES => []
		};
		$tmp->{GROUP} = $g->{group} if defined $g->{group};
		for my $p (@{$g->{properties}}) {
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing {paramname} param", paramname => "ap.properties[].name"))
				unless (defined $p->{name});
			for my $k ('nullable', 'readOnly') {
				return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing {paramname} param", paramname => "ap.properties[].".$k))
					unless (defined $p->{$k});
				return send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => __x("{paramname} param must be a boolean", paramname => "ap.properties[].".$k))
					unless (JSON::is_bool($p->{$k}));
			}
			my $pr = {
				 NAME		=> $p->{name}
				,NULLABLE	=> $p->{nullable} ? 'Y' : 'N'
				,READ_ONLY	=> $p->{readOnly} ? 'Y' : 'N'
			};
			push @{$tmp->{PROPERTIES}}, $pr;
		}
		push @{$params}, $tmp;
	}
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
		unless ($api->set_activity_properties_group(ACTIVITY_TYPE_NAME => param('TYPE'), AP => $params));
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_NO_CONTENT);
};

######## ROUTE /instance/types/activities/:TYPE/statuses ###########

options '/instance/types/activities/:TYPE/statuses' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/instance/types/activities/:TYPE/statuses Activity type statuses 
@apiName instanceTypesActivitiesTypeStatuses
@apiGroup Instance
@apiDescription Return activity type statuses

@apiSuccess {Object} STATUS Object representing the info for the status named STATUS
@apiSuccess {String} STATUS.id Status id
@apiSuccess {String} STATUS.name Status name
@apiSuccess {String} STATUS.description Status description
@apiSuccess {String} STATUS.ui Status custom url
@apiSuccess {Boolean} STATUS.isFinal <code>true</code> if the the status is a final status, <code>false</code> otherwise.
@apiSuccess {Boolean} STATUS.isStarting <code>true</code> if the the status is the starting status, <code>false</code> otherwise.
@apiSuccess {Boolean} STATUS.isVirtual <code>true</code> if the the status is a virtual status, <code>false</code> otherwise.
@apiSuccess {Boolean} STATUS.isAnyStatus <code>true</code> if the the status is a "anyStatus" status, <code>false</code> otherwise.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/instance/types/activities/COLLAUDO_CCS/statuses
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "RMA": {
    "name": "RMA",
    "description": "RMA",
    "isVirtual": false,
    "isFinal": true,
    "isStarting": false,
    "ui": null,
    "isAnyStatus": false,
    "id": "146"
  },
  "ERRORE_IN_CARICO_SIRTI": {
    "name": "ERRORE_IN_CARICO_SIRTI",
    "description": "ERRORE_IN_CARICO_SIRTI",
    "isVirtual": false,
    "isFinal": false,
    "isStarting": false,
    "ui": null,
    "isAnyStatus": false,
    "id": "136"
  }
}
=cut

get '/instance/types/activities/:TYPE/statuses' => sub {
	
	my $api = vars->{api_art};
    
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Invalid activity type name {type}", type => param('TYPE')))
		unless defined $api->test_activity_type_name(param('TYPE'));

	my %ret = ();
	
	my $statuses = $api->get_activity_status_info(ACTIVITY_TYPE_NAME => param('TYPE'));
	
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless defined $statuses;
	
	my $ret = {};
	for my $st (keys %{$statuses}) {
		$ret->{$st} = {
			 id				=> $statuses->{$st}->{ID}
			,name			=> $statuses->{$st}->{NAME}
			,description	=> $statuses->{$st}->{DESCRIPTION}
			,ui				=> $statuses->{$st}->{UI}
			,isFinal		=> $statuses->{$st}->{IS_FINAL}
			,isStarting		=> $statuses->{$st}->{IS_STARTING}
			,isVirtual		=> $statuses->{$st}->{IS_VIRTUAL}
			,isAnyStatus	=> $statuses->{$st}->{IS_ANY_STATUS}
		}
	}

	return send_ok(MSG => $ret);
};

######## ROUTE /instance/types/activities/:TYPE/actions ###########

options '/instance/types/activities/:TYPE/actions' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/instance/types/activities/:TYPE/actions Activity type actions 
@apiName instanceTypesActivitiesTypeActions
@apiGroup Instance
@apiDescription Return activity type actions

@apiSuccess {Object} ACTION Object representing the info for the action named ACTION
@apiSuccess {String} ACTION.id Action id
@apiSuccess {String} ACTION.name Action name
@apiSuccess {String} ACTION.description Action description
@apiSuccess {String} ACTION.ui Action custom url
@apiSuccess {Boolean} ACTION.isStartingType <code>true</code> if the the action is a starting action, <code>false</code> otherwise.
@apiSuccess {Boolean} ACTION.isFinalType <code>true</code> if the the action is a final action, <code>false</code> otherwise.
@apiSuccess {Boolean} ACTION.isSelfAssignType <code>true</code> if the the action is a self assign action, <code>false</code> otherwise.
@apiSuccess {Boolean} ACTION.isSelfDeassignType <code>true</code> if the the action is a self deassign action, <code>false</code> otherwise.
@apiSuccess {Boolean} ACTION.isVirtualAssignType <code>true</code> if the the action is a virtual assign action, <code>false</code> otherwise.
@apiSuccess {Boolean} ACTION.isVirtualDeassignType <code>true</code> if the the action is a virtual deassign action, <code>false</code> otherwise.
@apiSuccess {Boolean} ACTION.isAssignType <code>true</code> if the the action is a assign action, <code>false</code> otherwise.
@apiSuccess {Boolean} ACTION.isParkType <code>true</code> if the the action is a park action, <code>false</code> otherwise.
@apiSuccess {Boolean} ACTION.isUpdateActivityPropertyType <code>true</code> if the the action permit to update activity property, <code>false</code> otherwise.
@apiSuccess {Boolean} ACTION.isVirtualType <code>true</code> if the the action is a virtual action, <code>false</code> otherwise.
@apiSuccess {Boolean} ACTION.doAssignNotification <code>true</code> if the user assignee of the activity must be notified via email, <code>false</code> otherwise.
  Present only if <code>isAssignType=true</code>.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/instance/types/activities/COLLAUDO_CCS/actions
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "TRASFERIMENTO_MATERIALE_ACK": {
    "isVirtualDeassignType": false,
    "isVirtualAssignType": false,
    "isSelfAssignType": false,
    "name": "TRASFERIMENTO_MATERIALE_ACK",
    "isUpdateActivityPropertyType": false,
    "description": "TRASFERIMENTO_MATERIALE_ACK",
    "isFinalType": false,
    "isVirtualType": false,
    "isAssignType": false,
    "isParkType": false,
    "ui": null,
    "isSelfDeassignType": false,
    "id": "182",
    "isStartingType": false
  }
}
=cut

get '/instance/types/activities/:TYPE/actions' => sub {
	
	my $api = vars->{api_art};
    
	my %query_params = defined request->params('query') ? request->params('query') : ();

	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Invalid activity type name {type}", type => param('TYPE')))
		unless defined $api->test_activity_type_name(param('TYPE'));

	my %ret = ();
	
	my $actions = $api->get_activity_action_info(ACTIVITY_TYPE_NAME => param('TYPE'));
	
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless defined $actions;
	
	my $ret = {};
	for my $ac (keys %{$actions}) {
		$ret->{$ac} = {
			 id								=> $actions->{$ac}->{ID}
			,name							=> $actions->{$ac}->{NAME}
			,description					=> $actions->{$ac}->{DESCRIPTION}
			,ui								=> $actions->{$ac}->{UI}
			,isStartingType					=> $actions->{$ac}->{IS_STARTING_TYPE}
			,isFinalType					=> $actions->{$ac}->{IS_FINAL_TYPE}
			,isSelfAssignType				=> $actions->{$ac}->{IS_SELF_ASSIGN_TYPE}
			,isSelfDeassignType				=> $actions->{$ac}->{IS_SELF_DEASSIGN_TYPE}
			,isVirtualAssignType			=> $actions->{$ac}->{IS_VIRTUAL_ASSIGN_TYPE}
			,isVirtualDeassignType			=> $actions->{$ac}->{IS_VIRTUAL_DEASSIGN_TYPE}
			,isAssignType					=> $actions->{$ac}->{IS_ASSIGN_TYPE}
			,isParkType						=> $actions->{$ac}->{IS_PARK_TYPE}
			,isUpdateActivityPropertyType	=> $actions->{$ac}->{IS_UPDATE_ACTIVITY_PROPERTY_TYPE}
			,isVirtualType					=> $actions->{$ac}->{IS_VIRTUAL_TYPE}
		};
		
		$ret->{$ac}->{doAssignNotification} = $actions->{$ac}->{DO_ASSIGN_NOTIFICATION} if ($ret->{$ac}->{isAssignType})
	}

	return send_ok(MSG => $ret);
};

######## ROUTE /instance/types/activityProperties/groups ###########

options '/instance/types/activityProperties/groups' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };
    
=pod
@api {get} /api/art/instance/types/activityProperties/groups Activity properties groups
@apiName instanceActivityPropertiesGroups
@apiGroup Instance
@apiDescription Return the activity properties groups.

@apiSuccess {Object[]} anonymous Group informations
@apiSuccess {Number} anonymous.id The id of the group.
@apiSuccess {String} anonymous.name The name of the group.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/instance/types/activityProperties/groups
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
  {
    "id": 1,
    "name": "Gruppo 1"
  },
  {
    "id": 2,
    "name": "Gruppo 2"
  }
]

=cut

get '/instance/types/activityProperties/groups' => sub {
	
	my $api = vars->{api_art};
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return send_ok(MSG => [ map { { id => $_->{ID}*1, name => $_->{NAME} } } @{$api->get_activity_property_groups()} ]);
};

######## ROUTE /instance/types/systems/:TYPE/systemProperties ###########

options '/instance/types/systems/:TYPE/systemProperties' => sub { handle_cors_request( METHODS => [ 'GET', 'PUT' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /instance/types/systems/:TYPE/systemProperties System type system properties
@apiName instanceTypeSystemsTypeSystemProperties
@apiGroup Instance
@apiDescription Return the SYSTEM properties designed for the SYSTEM TYPE, showing the organization of the groups of them.

@apiSuccess {Object[]} anonymous Object representing the system properties group
@apiSuccess {String} [anonymous.group] Name of the group
@apiSuccess {Number} [anonymous.id] Id of the group
@apiSuccess {Object[]} anonymous.properties Object representing the system property
@apiSuccess {String} anonymous.properties.name Name of the property
@apiSuccess {Boolean} anonymous.properties.readOnly <code>true</code> if the property is readOnly, <code>false</code> otherwise.
@apiSuccess {Boolean} anonymous.properties.expired <code>false</code> if the property is no longer associated to at least an action of the activity type, <code>true</code> otherwise.
@apiSuccess {Boolean} anonymous.properties.nullable <code>true</code> if the property can be set to null, <code>false</code> otherwise.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art//instance/types/systems/:TYPE/systemProperties
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
  {
    "group": "GROUP_1"
    ,"id" : 1
    ,"properties": [
      {
        "expired":false,
        "readOnly":false,
        "name":"END_DATE",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"ENGAGE_DESCRIPTION",
        "nullable":false
      }
    ]
  },
  {
  	"properties":[
      {
        "expired":false,
        "readOnly":false,
        "name":"ACCOUNTED_EFFORT",
        "nullable":false
      },
      {
        "expired":false,
        "readOnly":false,
        "name":"CATEGORY_LIST",
        "nullable":false
      }
    ]
  }
]

=cut

get '/instance/types/systems/:TYPE/systemProperties' => sub {
	
	my $api = vars->{api_art};
    
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Invalid system type name {type}", type => param('TYPE')))
		unless defined $api->test_system_type_name(param('TYPE'));

	my $results = eval{$api->get_system_properties_group(SHOW_ONLY_WITH_VISIBILITY => 1, SYSTEM_TYPE_NAME => [param('TYPE')])};
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Bad TYPE", INTERNAL_ERROR_MSG => $@)
		if ($@);
	
	my $rets = [];
	
	
	for my $res (@{$results}){
		
		my $ret = {};
		$ret->{group} = $res->{GROUP} if defined $res->{GROUP};
		$ret->{id} = $res->{ID} if defined $res->{ID};
		$ret->{properties} = [];
		for my $prop (@{$res->{PROPERTIES}}){
			push @{$ret->{properties}}, {
				name => $prop->{NAME}
				,readOnly => $prop->{READ_ONLY} eq 'Y' ? $JSON::true : $JSON::false 
				,expired => $prop->{EXPIRED} eq 'Y' ? $JSON::true : $JSON::false 
				,nullable => $prop->{NULLABLE} eq 'Y' ? $JSON::true : $JSON::false 
			};
		}
		
		push @{$rets}, $ret;
	}
	
	return send_ok(MSG => $rets);
		
		
};

=pod
@api {put} /api/art/instance/types/systems/:TYPE/systemProperties Modify system type system properties
@apiName instanceModifySystemProperties
@apiGroup Instance
@apiDescription Set the system properties designed for the system TYPE, with the organization of the groups of them.

@apiUse ContentTypeJsonHeader

@apiExample {HTTP} Example usage:
PUT http://localhost/api/art/instance/types/systems/EPCCL/systemProperties
Content-Type: application/json

{
  "ap": [
    {
      "group": "Predefiniti",
      "properties": [
        {
          "name": "DATA_RICHIESTA",
          "readOnly": true,
          "nullable": false
        }
      ]
    },
    {
      "group": "Richiesta",
      "properties": [
        {
          "name": "ID_PRATICA_SOFIA",
          "readOnly": true,
          "nullable": true
        }
      ]
    },
    {
      "properties": [
        {
          "name": "TIPO_INTERVENTO",
          "readOnly": false,
          "nullable": false
        },
        {
          "name": "NUMERO_CODA",
          "readOnly": false,
          "nullable": false
        }
      ]
    }
  ]
}

@apiParam {Object[]} sp Object representing the system properties group
@apiParam {String} [sp.group] Name of the group
@apiParam {Object[]} sp.properties Object representing the activity property
@apiParam {String} sp.properties.name Name of the property
@apiParam {Boolean} sp.properties.readOnly <code>true</code> if the property is readOnly, <code>false</code> otherwise
@apiParam {Boolean} sp.properties.nullable <code>true</code> if the property can be set to null, <code>false</code> otherwise

@apiSuccessExample Success-Response:
HTTP/1.1 204 No content

=cut

put '/instance/types/systems/:TYPE/systemProperties' => sub {
	
	my $api = vars->{api_art};

	my %body = request->params('body');
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
		unless (keys %body);
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Invalid activity type name {type}", type => param('TYPE')))
		unless defined $api->test_activity_type_name(param('TYPE'));

	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing {paramname} param", paramname => "sp"))
		unless ($body{sp});
	return send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => __x("{paramname} param must be an array", paramname => "sp"))
		unless (ref($body{sp}) eq 'ARRAY');
	
	my $params = [];
	for my $g (@{$body{sp}}) {
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing {paramname} param", paramname => "sp.properties"))
			unless ($g->{properties});
		return send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => __x("{paramname} param must be an array", paramname => "sp.properties"))
			unless (ref($g->{properties}) eq 'ARRAY');
		my $tmp = {
			PROPERTIES => []
		};
		$tmp->{GROUP} = $g->{group} if defined $g->{group};
		for my $p (@{$g->{properties}}) {
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing {paramname} param", paramname => "sp.properties[].name"))
				unless (defined $p->{name});
			for my $k ('nullable', 'readOnly') {
				return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing {paramname} param", paramname => "sp.properties[].".$k))
					unless (defined $p->{$k});
				return send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => __x("{paramname} param must be a boolean", paramname => "sp.properties[].".$k))
					unless (JSON::is_bool($p->{$k}));
			}
			my $pr = {
				 NAME		=> $p->{name}
				,NULLABLE	=> $p->{nullable} ? 'Y' : 'N'
				,READ_ONLY	=> $p->{readOnly} ? 'Y' : 'N'
			};
			push @{$tmp->{PROPERTIES}}, $pr;
		}
		push @{$params}, $tmp;
	}
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
		unless ($api->set_system_properties_group(SYSTEM_TYPE_NAME => param('TYPE'), SP => $params));
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_NO_CONTENT);
};

######## ROUTE /instance/types/systems/properties ###########

options '/instance/types/systems/properties' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/instance/types/systems/properties System properties
@apiName instanceTypesSystemsProperties
@apiGroup Instance
@apiDescription Return system properties

@apiParam {String} [type] Returns the property informations suitable only for the specific system type.

@apiSuccess {Object[]} anonymous The property informations.
@apiSuccess {Object} anonymous.NAME The NAME of the property.
@apiSuccess {String} anonymous.NAME.type The type of the property.
@apiSuccess {String} anonymous.NAME.description The description of the property.
@apiSuccess {Boolean} anonymous.NAME.multiplex <code>true</code> if the property is multiplex (array), <code>false</code> otherwise.
@apiSuccess {Boolean} anonymous.NAME.isFile <code>true</code> if the property is a file property, <code>false</code> otherwise.
@apiSuccess {String} anonymous.NAME.um The unity of measurement of the property.
@apiSuccess {Boolean} anonymous.NAME.isMeasure <code>true</code> if the property is a measure property, <code>false</code> otherwise.
@apiSuccess {String} anonymous.NAME.valueMin The minimum value accepted.
@apiSuccess {String} anonymous.NAME.valueMax The maximum value accepted.
@apiSuccess {String} anonymous.NAME.currency The currency the property (exists only if type: CURRENCY).
@apiSuccess {String} anonymous.NAME.currency The fractions the property (exists only if type: CURRENCY).
	
@apiExample {HTTP} Example usage:
GET http://localhost/api/art/instance/types/systems/properties
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
    "API_TDT_03": {
		"isFile": false,
		"type": "STRING",
		"description": "API_TDT_03",
		"multiplex": false,
		"um": "°C",
		"isMeasure": true,
		"valueMin": 10,
		"valueMax": 100,
    },
    "DATA_FIELD_AN": {
		"isFile": false,
		"type": "TIMESTAMP",
		"description": "DATA_FIELD_AN",
		"multiplex": false,
		"um": null,
		"isMeasure": false
    },
    "STATO_INTERVENTO_MASTER": {
		"isFile": false,
		"type": "STRING",
		"description": "Stato intervento master",
		"multiplex": false,
		"um": null,
		"isMeasure": false
    }
}

=cut

sub handler_instance_types_systems_properties_get {
	
	my %input_params = @_;
	
	my %query_params = defined $input_params{QUERY_PARAMS} ? %{$input_params{QUERY_PARAMS}} : ();
	
	my $api = vars->{api_art};
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Param type can't be an ARRAY")
		if defined $query_params{'type'} && ref $query_params{'type'};
	
	my %p = (
		EXTENDED_OUTPUT	=>	1
	);
	
	$p{SYSTEM_TYPE_NAME} = $query_params{'type'} if defined $query_params{'type'};
	
	# il rest mostra sempre solo quello su cui si ha visibilità
	$p{SHOW_ONLY_WITH_VISIBILITY} = 1;
	
	my $results = eval{$api->enum_system_property(%p)};
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Bad TYPE"), INTERNAL_ERROR_MSG => $@)
		if ($@);
		
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless defined $results;
	
	my $ret = {};

	while ( my ($key, $value) = each %{$results} ) {
		
		$ret->{$key} = {
			 type => $value->{TYPE},
			 multiplex => $value->{MULTIPLEX} ? $JSON::true : $JSON::false,
			 description => $value->{DESCRIPTION},
			 um => $value->{UM},
			 isMeasure => $value->{IS_MEASURE} ? $JSON::true : $JSON::false,
			 isFile => $value->{IS_FILE} ? $JSON::true : $JSON::false,
		};

		$ret->{$key}->{valueMin} = $value->{VALUE_MIN} if defined $value->{VALUE_MIN};
		$ret->{$key}->{valueMax} = $value->{VALUE_MAX} if defined $value->{VALUE_MAX};
		# NB: sarebbe da mettere defined al posto di exists ma per evitare problemi di retrocompatibilita lascio cosi'
		$ret->{$key}->{currency} = $value->{CURRENCY} if exists $value->{CURRENCY};
		$ret->{$key}->{fractions} = $value->{FRACTIONS} if exists $value->{FRACTIONS};
	}
	
	return send_ok(MSG => $ret);
		
};

get '/instance/types/systems/properties' => sub {
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return handler_instance_types_systems_properties_get(QUERY_PARAMS => \%query_params);
	
};

######## ROUTE /instance/types/systemProperties/groups ###########

options '/instance/types/systemProperties/groups' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };
    
=pod
@api {get} /api/art/instance/types/systemProperties/groups System properties groups
@apiName instanceSystemPropertiesGroups
@apiGroup Instance
@apiDescription Return the system properties groups.

@apiSuccess {Object[]} anonymous Group informations
@apiSuccess {Number} anonymous.id The id of the group.
@apiSuccess {String} anonymous.name The name of the group.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/instance/types/systemProperties/groups
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
  {
    "id": 1,
    "name": "Gruppo 1"
  },
  {
    "id": 2,
    "name": "Gruppo 2"
  }
]

=cut

get '/instance/types/systemProperties/groups' => sub {
	
	my $api = vars->{api_art};
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return send_ok(MSG => [ map { { id => $_->{ID}*1, name => $_->{NAME} } } @{$api->get_system_property_groups()} ]);
};

######## ROUTE /instance/groups ###########

options '/instance/groups' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };

=pod
@api {get} /api/art/instance/groups Get Groups
@apiName instanceGetGroups
@apiGroup Instance
@apiDescription Return info about groups

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/instance/groups
Accept: application/json
@apiSuccess {Object[]} anonymous The group informations.
@apiSuccess {String} anonymous.id The id of the group.
@apiSuccess {String} anonymous.label The description of the group.
@apiSuccess {Boolean} anonymous.admin <code>true</code> if the group is an admin, <code>false</code> otherwise.
@apiSuccess {Boolean} anonymous.root <code>true</code> if the group is a group root, <code>false</code> otherwise.
@apiSuccess {Boolean} anonymous.autogenerated <code>true</code> if the group is an autogenerated group, <code>false</code> otherwise.
@apiSuccess {Boolean} anonymous.private <code>true</code> if the group is a private group, <code>false</code> otherwise.

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
    {
        "admin": false,
        "autogenerated": false,
        "private": false,
        "label": "Assistente Tecnico",
        "id": "AT",
        "root": false
    },
    {
        "admin": false,
        "autogenerated": true,
        "private": false,
        "label": "0000000033 - SEA SPA",
        "id": "SERVICE_0000000033",
        "root": false
    }
]

=cut

get '/instance/groups' => sub {

    my $api = vars->{api_art};
    
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	my $groups = $api->enum_group();
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless $groups;

	my $ret = [];

	for my $g(values %$groups){
		my $info = $api->get_group_info($g);
		return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
			unless $info;
		push @{$ret}, {
			root => $info->{ROOT} ? $JSON::true : $JSON::false,
			admin => $info->{ADMIN} ? $JSON::true : $JSON::false,
			id => $info->{NAME},
			label => $info->{DESCRIPTION},
			private => $info->{PRIVATE} ? $JSON::true : $JSON::false,
			autogenerated => $info->{AUTOGENERATED} ? $JSON::true : $JSON::false,
		};
	}
	
	return send_ok(MSG => $ret);
	
};

######## ROUTE /instance/roles ###########

options '/instance/roles' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };

=pod
@api {get} /api/art/instance/roles Get Roles
@apiName instanceGetRoles
@apiGroup Instance
@apiDescription Return info about roles

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/instance/roles
Accept: application/json
@apiSuccess {Object[]} anonymous The group informations.
@apiSuccess {String} anonymous.id The id of the role.
@apiSuccess {String} anonymous.label The description of the role.
@apiSuccess {String} anonymous.creationDate The date when the role has been created.
@apiSuccess {String[]} anonymous.aliases The list of the aliases of the role.
@apiSuccess {String[]} anonymous.groups The list of the groups of the role.

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
    {
        "creationDate": "2020-07-08T11:39:53.*********+02:00",
        "aliases": [
            "MINERVAI_ADMIN"
        ],
        "groups": [
            "ADMIN"
        ],
        "label": "Amministratore di sistema",
        "id": "I_ADMIN"
    },
    {
        "creationDate": "2020-07-08T11:39:53.*********+02:00",
        "aliases": [
            "MINERVAI_NOC"
        ],
        "groups": [
            "NOC"
        ],
        "label": "Utente NOC",
        "id": "I_NOC"
    }
]

=cut

get '/instance/roles' => sub {

    my $api = vars->{api_art};
    
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	my $roles = $api->enum_role(EXTENDED_OUTPUT => 1);
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless $roles;

	my $ret = [];

	for my $r(@{$roles}){
		push @{$ret}, {
			id => $r->{NAME},
			label => $r->{DESCRIPTION},
			creationDate => $api->get_iso_date_from_date($r->{CREATION_DATE}),
			groups => [map {$api->get_group_name($_)} @{$r->{GROUPS}}],
			aliases => $r->{ROLE_ALIASES},
		};
	}
	
	return send_ok(MSG => $ret);
	
};

######## ROUTE /instance/authenticatedUser ###########

options '/instance/authenticatedUser' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };

=pod
@api {get} /api/art/instance/authenticatedUser Get Authenticated User
@apiName instanceGetAuthenticatedUser
@apiGroup Instance
@apiDescription Return info about users

@apiParam {Number=1,0} [includeToken=0] If <code>1</code> the session token is also returned. The output format is the same as [Session - Login](#api-Session-Login)

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/instance/authenticatedUser
Accept: application/json

@apiSuccess {String} username The username of the user logged in.
@apiSuccess {Boolean} admin <code>true</code> if the user is an admin, <code>false</code> otherwise.
@apiSuccess {Boolean} userAdmin <code>true</code> if the user is an user admin, <code>false</code> otherwise.
@apiSuccess {Boolean} root <code>true</code> if the user is an user root, <code>false</code> otherwise.
@apiSuccess {String} firstName The fist name of the user logged in.
@apiSuccess {String} lastName The last name of the user logged in.
@apiSuccess {String} fullName The full name of the user logged in.
@apiSuccess {String} email The email address of the user logged in.
@apiSuccess {String} mobilePhone The mobile phone number of the user logged in.
@apiSuccess {String[]} groups The groups the user belongs to.
@apiSuccess {String[]} roles The roles the user belongs to.
@apiSuccess {Boolean} serviceUser <code>true</code> if the user is a service user, <code>false</code> otherwise.
@apiSuccess {Boolean} disabled <code>true</code> if the user is a disabled user, <code>false</code> otherwise.

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "username": "JOHN",
  "admin" : false,
  "userAdmin" : false,
  "root" : false,
  "firstName": "John",
  "lastName": "Doe",
  "fullName": "Doe John",
  "email": "<EMAIL>",
  "mobilePhone": null,
  "groups": [
    "USER",
    "PM"
  ],
  "roles": [
    "AREA_MANAGER"
  ],
  "serviceUser" : false,
  "disabled"	: false,
}

=cut

get '/instance/authenticatedUser' => sub {

    my $api = vars->{api_art};
    
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	if ($query_params{includeToken}) {
		return send_ok(MSG => {
			user => get_authenticated_user(),
			token => 
				defined request->header(_art_auth_by_proxy_header) ? get_sid() :
				_art_auth_by_jwt() ? jwt_raw_token() : get_sid()
		});
	}

	return send_ok(MSG => get_authenticated_user());
	
};

######## ROUTE /instance/users ###########

options '/instance/users' => sub { handle_cors_request( METHODS => [ 'GET', 'POST' ] ); return send_ok(IGNORE_SESSION => 1); };

sub _get_user_structure{
	
	my %params = @_;
	
	my $groups = $params{GROUPS} if defined $params{GROUPS};
	
	$groups = [ $groups]
			if (defined $groups && ref ($groups) ne 'ARRAY' );
	
	my $roles = $params{ROLES} if defined $params{ROLES};
	
	$roles = [ $roles]
			if (defined $roles && ref ($roles) ne 'ARRAY' );

	my $users = $params{USERS} if defined $params{USERS};
	
	$users = [ $users ]
			if (defined $users && ref ($users) ne 'ARRAY' );
	
	my $api = vars->{api_art};
	
	my @ids = ();
	
	if ($groups || $roles){
		if ($groups){
			for my $g (@{$groups}){
				my $group_id = $api->get_group_id($g);
				return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Group $g not found")
					unless defined $group_id;
				if (scalar @ids == 0){
					push @ids, @{$api->get_group_users($group_id)};	
				} else {
					@ids = intersect @ids, @{$api->get_group_users($group_id)};
				}
			}
		}
		if ($roles){
			for my $r (@{$roles}){
				my $role_id = $api->get_role_id($r);
				return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Role $r not found")
					unless defined $role_id;
				if (scalar @ids == 0){
					push @ids, @{$api->get_role_users($role_id)};	
				} else {
					@ids = intersect @ids, @{$api->get_role_users($role_id)};
				}
			}
		}
	} else {
		@ids = values %{$api->enum_user()};
	}
	
	@ids = unique @ids;
	
	my %all_users_by_username = %{$api->enum_user()};
	my %all_users_by_id = reverse %all_users_by_username;
	
	my @usernames = map { $all_users_by_id{$_} } @ids;
	
	my @ret = ();
	for my $username (@usernames) {
		next if defined $users && ! grep { $_ eq $username } @{$users};
		push @ret, $api->get_user_structure($all_users_by_username{$username});
	}
	
	return \@ret;
}

sub _get_dest_users{
	
	my $api = vars->{api_art};
    
    my %params = @_;
    
    my %query_params = defined request->params('query') ? request->params('query') : ();
    
	my $activity = eval { API::ART::Activity::Factory->new( ART => $api, ID => $params{'ID'} ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("Activity ID not found"), INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$activity;
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Status must be a SCALAR!")
		if defined $params{'STATUS'} && ! defined ref($params{'STATUS'});
	
	my $dest_users_params = {
		ACTION_NAME => $params{'ACTION'}
	};
	
	if (defined $params{'STATUS'}){
		$dest_users_params->{STATUS_NAME} = $params{'STATUS'};
	}
	
	my $destUsers = eval{$activity->get_dest_users(%{$dest_users_params})};
	
	return send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => $@)
		if $@;
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
		unless $destUsers;
	
	my @ret;
	
	for my $user (@{$destUsers}){

		my @groups = map { $api->get_group_name($_) } @{$user->{'GROUPS'}};
		push @ret , {
		 	username => $user->{'NAME'}
			,firstName => $user->{'FIRST_NAME'}
			,lastName => $user->{'LAST_NAME'}
			,email => $user->{'EMAIL'}
			,mobilePhone => $user->{'MOBILE_PHONE'}
			,groups => \@groups
			,serviceUser => $user->{'SERVICE_USER'} ? $JSON::true : $JSON::false
			,disabled => $user->{'DISABLED'} ? $JSON::true : $JSON::false
		};

	}
	
	return send_ok(MSG => \@ret);

}
	
=pod
@api {get} /api/art/instance/users Get Users
@apiName instanceGetUsers
@apiGroup Instance
@apiDescription Return info about users

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/instance/users
Accept: application/json

@apiParam {String[]} [roles] Return info of the users filtered by role(s).
@apiParam {String[]} [groups] Return info of the users filtered by group(s).
@apiParam {String[]} [users] Return info of the users filtered by user(s).

@apiSuccess {Object[]} anonymous The user informations.
@apiSuccess {String} anonymous.username The username of the user logged in.
@apiSuccess {Boolean} anonymous.admin <code>true</code> if the user is an admin, <code>false</code> otherwise.
@apiSuccess {Boolean} anonymous.userAdmin <code>true</code> if the user is an user admin, <code>false</code> otherwise.
@apiSuccess {Boolean} anonymous.root <code>true</code> if the user is an user root, <code>false</code> otherwise.
@apiSuccess {String} anonymous.firstName The fist name of the user logged in.
@apiSuccess {String} anonymous.lastName The last name of the user logged in.
@apiSuccess {String} anonymous.fullName The full name of the user logged in.
@apiSuccess {String} anonymous.email The email address of the user logged in.
@apiSuccess {String} anonymous.mobilePhone The mobile phone number of the user logged in.
@apiSuccess {String[]} anonymous.groups The groups the user belongs to.
@apiSuccess {String[]} anonymous.roles The roles the user belongs to.
@apiSuccess {Boolean} anonymous.serviceUser <code>true</code> if the user is a service user, <code>false</code> otherwise.
@apiSuccess {Boolean} anonymous.disabled <code>true</code> if the user is a disabled user, <code>false</code> otherwise.

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
{
  "username": "JOHN",
  "admin" : false,
  "userAdmin" : false,
  "root" : false,
  "firstName": "John",
  "lastName": "Doe",
  "fullName": "Doe John",
  "email": "<EMAIL>",
  "mobilePhone": null,
  "groups": [
    "USER",
    "PM"
  ],
  "roles": [
    "AREA_MANAGER"
  ],
  "serviceUser" : false,
  "disabled"	: false,
}
]

=cut

get '/instance/users' => sub {

    my $api = vars->{api_art};
    
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	my $ret = _get_user_structure(ROLES => $query_params{roles}, GROUPS => $query_params{groups}, USERS => $query_params{users});
	
	return send_ok(MSG => $ret);
	
};

=pod
@api {post} /api/art/instance/users Create User
@apiName instanceCreateUser
@apiGroup Instance
@apiDescription Create new user

@apiExample {HTTP} Example usage:
POST http://localhost/api/art/instance/users
Content-Type: application/json
Accept: application/json

{
  "username" : "DII",
  "firstName" : "DII",
  "lastName" : "PTE_Management IC1",
  "groups" : ["ADMIN","PM1","PM"],
  "password": "pippo"
}

@apiParam {String} username Username of the user to create.
@apiParam {String} password Password of the user to create.
@apiParam {String} firstName First Name of the user to create.
@apiParam {String} lastName Last Name of the user to create.
@apiParam {String[]} [groups] Groups of the user to create. You can use it unless you use <code>roles</code> or <code>roleAliases</code>
@apiParam {String[]} [roles] Roles of the user to create. You can use it unless you use <code>groups</code> or <code>roleAliases</code>
@apiParam {String[]} [roleAliases] Aliases of the roles of the user to create. You can use it unless you use <code>roles</code> or <code>groups</code>
@apiParam {String} [email] The email of the user to create.
@apiParam {String} [mobilePhone] The mobile phone of the user to create.
@apiParam {Boolean} [serviceUser=true] <true> if the user to create must be a serviceUser, <false>otherwise</false>

@apiSuccess {String} username The username of the user logged in.
@apiSuccess {Boolean} admin <code>true</code> if the user is an admin, <code>false</code> otherwise.
@apiSuccess {Boolean} userAdmin <code>true</code> if the user is an user admin, <code>false</code> otherwise.
@apiSuccess {Boolean} root <code>true</code> if the user is an user root, <code>false</code> otherwise.
@apiSuccess {String} firstName The fist name of the user logged in.
@apiSuccess {String} lastName The last name of the user logged in.
@apiSuccess {String} fullName The full name of the user logged in.
@apiSuccess {String} email The email address of the user logged in.
@apiSuccess {String} mobilePhone The mobile phone number of the user logged in.
@apiSuccess {String[]} groups The groups the user belongs to.
@apiSuccess {String[]} roles The roles the user belongs to.
@apiSuccess {Boolean} serviceUser <code>true</code> if the user is a service user, <code>false</code> otherwise.
@apiSuccess {Boolean} disabled <code>true</code> if the user is a disabled user, <code>false</code> otherwise.

@apiSuccessExample Success-Response:
HTTP/1.1 201 CREATED

{
  "username": "JOHN",
  "admin" : false,
  "userAdmin" : false,
  "root" : false,
  "firstName": "John",
  "lastName": "Doe",
  "fullName": "Doe John",
  "email": "<EMAIL>",
  "mobilePhone": null,
  "groups": [
    "USER",
    "PM"
  ],
  "roles": [
    "AREA_MANAGER"
  ],
  "serviceUser" : false,
  "disabled"	: false,
}

=cut

post '/instance/users' => sub {
	
	my $api = vars->{api_art};
    
    my %body = request->params('body');
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
		unless (keys %body);
	
	for my $k ('username','password', 'firstName', 'lastName'){
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing {paramname} param", paramname => $k))
			unless (defined $body{$k});
	}
	
	my %create_params = (
		NAME		=> $body{username}
		,PASSWORD	=> $body{password}
		,FIRST_NAME	=> $body{firstName}
		,LAST_NAME	=> $body{lastName}
	);
	
	if (defined $body{groups} && ref ($body{groups}) eq 'ARRAY' && scalar @{$body{groups}}){
		$create_params{GROUPS}->{PRIMARY} = shift @{$body{groups}};
		$create_params{GROUPS}->{SECONDARY} = $body{groups} if scalar @{$body{groups}};
	}
	
	$create_params{ROLES} = $body{roles} if defined $body{roles};
	$create_params{ROLE_ALIASES} = $body{roleAliases} if defined $body{roleAliases};

	$create_params{SERVICE_USER}	= ($JSON::true eq $body{serviceUser} ? 1 : 0) if defined $body{serviceUser};
	$create_params{EMAIL}			= $body{email} if defined $body{email};
	$create_params{MOBILE_PHONE}	= $body{mobilePhone} if defined $body{mobilePhone};
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
		unless $api->create_user(%create_params);
	
	my $ret = _get_user_structure(USERS => [$body{username}]);
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_CREATED, MSG => $ret->[0]);
	
};

######## ROUTE '/instance/users/:user/groups' ###########

options '/instance/users/:user/groups' => sub { handle_cors_request( METHODS => [ 'PUT' ] ); return send_ok(IGNORE_SESSION => 1); };

=pod
@api {put} /api/art/instance/users/:user/groups Update User Groups
@apiName instanceUpdateUserGroups
@apiGroup Instance
@apiDescription Update user groups

@apiExample {HTTP} Example usage:
PUT http://localhost/api/art/instance/users/LIVRAG/groups
Content-Type: application/json
Accept: application/json

{
  "add" : [ "GROUP1", "GROUP2" ],
  "remove : [ "GROUP3" ]
}

@apiParam {String[]} [add] Groups of the user to add.
@apiParam {String[]} [remove] Groups of the user to remove.
@apiParam {Boolean} [serviceUser=true] <true> if the user to create must be a serviceUser, <false>otherwise</false>

@apiSuccess {String} username The username of the user logged in.
@apiSuccess {Boolean} admin <code>true</code> if the user is an admin, <code>false</code> otherwise.
@apiSuccess {Boolean} userAdmin <code>true</code> if the user is an user admin, <code>false</code> otherwise.
@apiSuccess {Boolean} root <code>true</code> if the user is an user root, <code>false</code> otherwise.
@apiSuccess {String} firstName The fist name of the user logged in.
@apiSuccess {String} lastName The last name of the user logged in.
@apiSuccess {String} fullName The full name of the user logged in.
@apiSuccess {String} email The email address of the user logged in.
@apiSuccess {String} mobilePhone The mobile phone number of the user logged in.
@apiSuccess {String[]} groups The groups the user belongs to.
@apiSuccess {String[]} roles The roles the user belongs to.
@apiSuccess {Boolean} serviceUser <code>true</code> if the user is a service user, <code>false</code> otherwise.
@apiSuccess {Boolean} disabled <code>true</code> if the user is a disabled user, <code>false</code> otherwise.

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "username": "JOHN",
  "admin" : false,
  "userAdmin" : false,
  "root" : false,
  "firstName": "John",
  "lastName": "Doe",
  "fullName": "Doe John",
  "email": "<EMAIL>",
  "mobilePhone": null,
  "groups": [
    "USER",
    "PM"
  ],
  "roles": [
    "AREA_MANAGER"
  ],
  "serviceUser" : false,
  "disabled"	: false,
}

=cut

put '/instance/users/:user/groups' => sub {
	
	my $api = vars->{api_art};
    
    my %body = request->params('body');
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
		unless (keys %body);

    if ( !defined $body{add} and !defined $body{remove} ) {
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("One parameter between {par1}, {par2} must be defined", par1 => 'add', par2 => 'remove'));
	}	

    if ( exists $body{add} ) {
		if ( ref $body{add} eq 'ARRAY' ) {
			my %add_params = (
				NAME	=> param('user')
				,GROUPS	=> $body{add}
			);
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
				unless $api->add_user_groups(%add_params);
		} else {
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Parameter {par1} must be an array", par1 => 'add'));
		}
	} 
	
	if ( exists $body{remove} ) {
		if ( ref $body{remove} eq 'ARRAY' ) {
			my %add_params = (
				NAME	=> param('user')
				,GROUPS	=> $body{remove}
			);
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
				unless $api->remove_user_groups(%add_params);
		} else {
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Parameter {par1} must be an array", par1 => 'add'));
		}
	} 
	

	my $ret = _get_user_structure(USERS => [param('user')]);
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_OK, MSG => $ret->[0]);
	
};


######## ROUTE '/instance/users/:user/groups/:group' ###########

options '/instance/users/:user/groups/:group' => sub { handle_cors_request( METHODS => [ 'PUT', 'DELETE' ] ); return send_ok(IGNORE_SESSION => 1); };

=pod
@api {put} /api/art/instance/users/:user/groups/:group Add User Group
@apiName instanceAddUserGroup
@apiGroup Instance
@apiDescription Add user group

@apiExample {HTTP} Example usage:
PUT http://localhost/api/art/instance/users/LIVRAGH/groups/AT
Content-Type: application/json
Accept: application/json

{
}

@apiSuccess {String} username The username of the user logged in.
@apiSuccess {Boolean} admin <code>true</code> if the user is an admin, <code>false</code> otherwise.
@apiSuccess {Boolean} userAdmin <code>true</code> if the user is an user admin, <code>false</code> otherwise.
@apiSuccess {Boolean} root <code>true</code> if the user is an user root, <code>false</code> otherwise.
@apiSuccess {String} firstName The fist name of the user logged in.
@apiSuccess {String} lastName The last name of the user logged in.
@apiSuccess {String} fullName The full name of the user logged in.
@apiSuccess {String} email The email address of the user logged in.
@apiSuccess {String} mobilePhone The mobile phone number of the user logged in.
@apiSuccess {String[]} groups The groups the user belongs to.
@apiSuccess {String[]} roles The roles the user belongs to.
@apiSuccess {Boolean} serviceUser <code>true</code> if the user is a service user, <code>false</code> otherwise.
@apiSuccess {Boolean} disabled <code>true</code> if the user is a disabled user, <code>false</code> otherwise.

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "username": "JOHN",
  "admin" : false,
  "userAdmin" : false,
  "root" : false,
  "firstName": "John",
  "lastName": "Doe",
  "fullName": "Doe John",
  "email": "<EMAIL>",
  "mobilePhone": null,
  "groups": [
    "USER",
    "PM"
  ],
  "roles": [
    "AREA_MANAGER"
  ],
  "serviceUser" : false,
  "disabled"	: false,
}

=cut

put '/instance/users/:user/groups/:group' => sub {
	
	my $api = vars->{api_art};
    
	my %body = request->params('body');
	
	my %add_params = (
		NAME	=> param('user')
		,GROUPS	=> [ param('group') ]
	);
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
		unless $api->add_user_groups(%add_params);
	
	my $ret = _get_user_structure(USERS => [param('user')]);
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_OK, MSG => $ret->[0]);
	
};

=pod
@api {del} /api/art/instance/users/:user/groups/:group Delete User Group
@apiName instanceDeleteUserGroup
@apiGroup Instance
@apiDescription Delete user group

@apiExample {HTTP} Example usage:
DELETE http://localhost/api/art/instance/users/LIVRAGH/groups/AT
Content-Type: application/json
Accept: application/json

{
}

@apiSuccess {String} username The username of the user logged in.
@apiSuccess {Boolean} admin <code>true</code> if the user is an admin, <code>false</code> otherwise.
@apiSuccess {Boolean} userAdmin <code>true</code> if the user is an user admin, <code>false</code> otherwise.
@apiSuccess {Boolean} root <code>true</code> if the user is an user root, <code>false</code> otherwise.
@apiSuccess {String} firstName The fist name of the user logged in.
@apiSuccess {String} lastName The last name of the user logged in.
@apiSuccess {String} fullName The full name of the user logged in.
@apiSuccess {String} email The email address of the user logged in.
@apiSuccess {String} mobilePhone The mobile phone number of the user logged in.
@apiSuccess {String[]} groups The groups the user belongs to.
@apiSuccess {String[]} roles The roles the user belongs to.
@apiSuccess {Boolean} serviceUser <code>true</code> if the user is a service user, <code>false</code> otherwise.
@apiSuccess {Boolean} disabled <code>true</code> if the user is a disabled user, <code>false</code> otherwise.

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "username": "JOHN",
  "admin" : false,
  "userAdmin" : false,
  "root" : false,
  "firstName": "John",
  "lastName": "Doe",
  "fullName": "Doe John",
  "email": "<EMAIL>",
  "mobilePhone": null,
  "groups": [
    "USER",
    "PM"
  ],
  "roles": [
    "AREA_MANAGER"
  ],
  "serviceUser" : false,
  "disabled"	: false,
}

=cut

del '/instance/users/:user/groups/:group' => sub {
	
	my $api = vars->{api_art};
    
	my %body = request->params('body');
	
	my %add_params = (
		NAME	=> param('user')
		,GROUPS	=> [ param('group') ]
	);
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
		unless $api->remove_user_groups(%add_params);
	
	my $ret = _get_user_structure(USERS => [param('user')]);
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_OK, MSG => $ret->[0]);
	
};


######## ROUTE '/instance/users/:user/roles' ###########

options '/instance/users/:user/roles' => sub { handle_cors_request( METHODS => [ 'PUT' ] ); return send_ok(IGNORE_SESSION => 1); };

=pod
@api {put} /api/art/instance/users/:user/roles Update User Roles
@apiName instanceUpdateUserRoles
@apiGroup Instance
@apiDescription Update user roles

@apiExample {HTTP} Example usage:
PUT http://localhost/api/art/instance/users/LIVRAGH/roles
Content-Type: application/json
Accept: application/json

{
  "add" : [ "ROLE1", "ROLE2" ],
  "remove : [ "ROLE3" ]
}

@apiParam {String[]} [add] Roles of the user to add.
@apiParam {String[]} [remove] Roles of the user to remove.
@apiParam {Boolean} [serviceUser=true] <true> if the user to create must be a serviceUser, <false>otherwise</false>

@apiSuccess {String} username The username of the user logged in.
@apiSuccess {Boolean} admin <code>true</code> if the user is an admin, <code>false</code> otherwise.
@apiSuccess {Boolean} userAdmin <code>true</code> if the user is an user admin, <code>false</code> otherwise.
@apiSuccess {Boolean} root <code>true</code> if the user is an user root, <code>false</code> otherwise.
@apiSuccess {String} firstName The fist name of the user logged in.
@apiSuccess {String} lastName The last name of the user logged in.
@apiSuccess {String} fullName The full name of the user logged in.
@apiSuccess {String} email The email address of the user logged in.
@apiSuccess {String} mobilePhone The mobile phone number of the user logged in.
@apiSuccess {String[]} groups The groups the user belongs to.
@apiSuccess {String[]} roles The roles the user belongs to.
@apiSuccess {Boolean} serviceUser <code>true</code> if the user is a service user, <code>false</code> otherwise.
@apiSuccess {Boolean} disabled <code>true</code> if the user is a disabled user, <code>false</code> otherwise.

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "username": "JOHN",
  "admin" : false,
  "userAdmin" : false,
  "root" : false,
  "firstName": "John",
  "lastName": "Doe",
  "fullName": "Doe John",
  "email": "<EMAIL>",
  "mobilePhone": null,
  "groups": [
    "USER",
    "PM"
  ],
  "roles": [
    "AREA_MANAGER"
  ],
  "serviceUser" : false,
  "disabled"	: false,
}

=cut

put '/instance/users/:user/roles' => sub {
	
	my $api = vars->{api_art};
    
    my %body = request->params('body');

    if ( !defined $body{add} and !defined $body{remove} ) {
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("One parameter between {par1}, {par2} must be defined", par1 => 'add', par2 => 'remove'));
	}	

    if ( exists $body{add} ) {
		if ( ref $body{add} eq 'ARRAY' ) {
			my %add_params = (
				NAME	=> param('user')
				,ROLES	=> $body{add}
			);
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
				unless $api->add_user_roles(%add_params);
		} else {
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Parameter {par1} must be an array", par1 => 'add'));
		}
	} 
	
	if ( exists $body{remove} ) {
		if ( ref $body{remove} eq 'ARRAY' ) {
			my %add_params = (
				NAME	=> param('user')
				,ROLES	=> $body{remove}
			);
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
				unless $api->remove_user_roles(%add_params);
		} else {
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Parameter {par1} must be an array", par1 => 'add'));
		}
	} 
	

	my $ret = _get_user_structure(USERS => [param('user')]);
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_OK, MSG => $ret->[0]);
	
};


######## ROUTE '/instance/users/:user/roles/:role' ###########

options '/instance/users/:user/roles/:role' => sub { handle_cors_request( METHODS => [ 'PUT', 'DELETE' ] ); return send_ok(IGNORE_SESSION => 1); };

=pod
@api {put} /api/art/instance/users/:user/roles/:role Add User Role
@apiName instanceAddUserRole
@apiGroup Instance
@apiDescription Add user role

@apiExample {HTTP} Example usage:
PUT http://localhost/api/art/instance/users/LIVAGH/roles/AT
Content-Type: application/json
Accept: application/json

{
}

@apiSuccess {String} username The username of the user logged in.
@apiSuccess {Boolean} admin <code>true</code> if the user is an admin, <code>false</code> otherwise.
@apiSuccess {Boolean} userAdmin <code>true</code> if the user is an user admin, <code>false</code> otherwise.
@apiSuccess {Boolean} root <code>true</code> if the user is an user root, <code>false</code> otherwise.
@apiSuccess {String} firstName The fist name of the user logged in.
@apiSuccess {String} lastName The last name of the user logged in.
@apiSuccess {String} fullName The full name of the user logged in.
@apiSuccess {String} email The email address of the user logged in.
@apiSuccess {String} mobilePhone The mobile phone number of the user logged in.
@apiSuccess {String[]} groups The groups the user belongs to.
@apiSuccess {String[]} roles The roles the user belongs to.
@apiSuccess {Boolean} serviceUser <code>true</code> if the user is a service user, <code>false</code> otherwise.
@apiSuccess {Boolean} disabled <code>true</code> if the user is a disabled user, <code>false</code> otherwise.

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "username": "JOHN",
  "admin" : false,
  "userAdmin" : false,
  "root" : false,
  "firstName": "John",
  "lastName": "Doe",
  "fullName": "Doe John",
  "email": "<EMAIL>",
  "mobilePhone": null,
  "groups": [
    "USER",
    "PM"
  ],
  "roles": [
    "AREA_MANAGER"
  ],
  "serviceUser" : false,
  "disabled"	: false,
}

=cut

put '/instance/users/:user/roles/:role' => sub {
	
	my $api = vars->{api_art};
    
    my %body = request->params('body');
	
	my %add_params = (
		NAME	=> param('user')
		,ROLES	=> [ param('role') ]
	);
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
		unless $api->add_user_roles(%add_params);
	
	my $ret = _get_user_structure(USERS => [param('user')]);
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_OK, MSG => $ret->[0]);
	
};

=pod
@api {del} /api/art/instance/users/:user/roles/:role Delete User Role
@apiName instanceDeleteUserRole
@apiGroup Instance
@apiDescription Delete user role

@apiExample {HTTP} Example usage:
DELETE http://localhost/api/art/instance/users/LIVRAGH/roles/AT
Content-Type: application/json
Accept: application/json

{
}

@apiSuccess {String} username The username of the user logged in.
@apiSuccess {Boolean} admin <code>true</code> if the user is an admin, <code>false</code> otherwise.
@apiSuccess {Boolean} userAdmin <code>true</code> if the user is an user admin, <code>false</code> otherwise.
@apiSuccess {Boolean} root <code>true</code> if the user is an user root, <code>false</code> otherwise.
@apiSuccess {String} firstName The fist name of the user logged in.
@apiSuccess {String} lastName The last name of the user logged in.
@apiSuccess {String} fullName The full name of the user logged in.
@apiSuccess {String} email The email address of the user logged in.
@apiSuccess {String} mobilePhone The mobile phone number of the user logged in.
@apiSuccess {String[]} groups The groups the user belongs to.
@apiSuccess {String[]} roles The roles the user belongs to.
@apiSuccess {Boolean} serviceUser <code>true</code> if the user is a service user, <code>false</code> otherwise.
@apiSuccess {Boolean} disabled <code>true</code> if the user is a disabled user, <code>false</code> otherwise.

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "username": "JOHN",
  "admin" : false,
  "userAdmin" : false,
  "root" : false,
  "firstName": "John",
  "lastName": "Doe",
  "fullName": "Doe John",
  "email": "<EMAIL>",
  "mobilePhone": null,
  "groups": [
    "USER",
    "PM"
  ],
  "roles": [
    "AREA_MANAGER"
  ],
  "serviceUser" : false,
  "disabled"	: false,
}

=cut

del '/instance/users/:user/roles/:role' => sub {
	
	my $api = vars->{api_art};
    
	my %body = request->params('body');
	
	my %add_params = (
		NAME	=> param('user')
		,ROLES	=> [ param('role') ]
	);
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
		unless $api->remove_user_roles(%add_params);
	
	my $ret = _get_user_structure(USERS => [param('user')]);
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_OK, MSG => $ret->[0]);
	
};


######## ROUTE /systems ###########

options '/systems' => sub { handle_cors_request( METHODS => [ 'GET', 'POST' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/systems Find
@apiName getSystems
@apiGroup System
@apiDescription Returns an array of objects describing the systems matching the specified criteria.

@apiParam {String[]} [type] Filter systems of these types.
@apiParam {String} [type_equal] Filter systems of given type.
@apiParam {String[]} [type_in] Filter systems given a list.
@apiParam {String} [type_begins] Filter systems with type name that begins with passed value.
@apiParam {String} [type_ends] Filter systems with type name that ends with passed value.
@apiParam {String} [type_contains] Filter systems with type name that contains with passed value.
@apiParam {String} [id_equal] Filter systems given an id.
@apiParam {String[]} [id_in] Filter systems given a list of ids.
@apiParam {String} [id_begins] Filter systems with id that begins with passed value.
@apiParam {String} [id_ends] Filter systems with id that ends with passed value.
@apiParam {String} [id_contains] Filter systems with id that contains with passed value.
@apiParam {String} [description_equal] Filter systems with a defined description.
@apiParam {String[]} [description_in] Filter systems given a list of descriptions.
@apiParam {String} [description_begins] Filter systems with description that begins with passed value.
@apiParam {String} [description_ends] Filter systems with description that ends with passed value.
@apiParam {String} [description_contains] Filter systems with description that contains with passed value.
@apiParam {Number=0,1} [active=1] <code>1</code> active systems, <code>0</code> inactive systems.
@apiParam {Number=0,1} [suspended=0] <code>1</code> suspended systems, <code>0</code> not suspended systems.
@apiParam {String} [sp_NAME] Where NAME is the name of the property to filter. Can be repeated for more then one property name.
@apiParam {String} [sp_NAME_equal] Where NAME is the name of the property to filter. Can be repeated for more then one system property name. An exact search of the value will be done.
@apiParam {String} [sp_NAME_notEqual] Where NAME is the name of the property to filter. Can be repeated for more then one system property name. An exact search of the value will be done.
@apiParam {String[]} [sp_NAME_in] Where NAME is the name of the property to filter. Can be repeated for more then one system property name. An exact search of the given list of the values will be done.
@apiParam {String} [sp_NAME_begins] Where NAME is the name of the property to filter. Filter activities with system property that begins with passed value.
@apiParam {String} [sp_NAME_ends] Where NAME is the name of the property to filter. Filter activities system with property that ends with passed value.
@apiParam {String} [sp_NAME_contains] Where NAME is the name of the property to filter. Filter activities with system property that contains with passed value.
@apiParam {Number=1} [sp_NAME_isNull] Where NAME is the name of the property to filter. A search of the NAME with value <code>null</code> will be done.
@apiParam {Number=1} [sp_NAME_isNotNull] Where NAME is the name of the property to filter. A search of the NAME with value <code>not null</code> will be done.
@apiParam {Number=0,1} [includeActivities=0] Include activities list about system.
@apiParam {Number=0,1} [caseSensitive=1] <code>0</code> execute the search in non-CASE_SENSITIVE mode, <code>1</code> execute the search in CASE_SENSITIVE mode.
@apiParam {String[]=[-]id,[-]type,[-]creationDate,[-]disablingDate,[-]endingDate,[-]description,[-]sp_NAME} [sort] If present, it define sort fields. If the field is prefixed by <code>-</code> the sort order is descending.

 example:
```
sort=type&sort=-id&sort=-sp_COMPETENCE_CERTER
```
@apiParam {Number} [limit] Maximum number of results
@apiParam {Number} [skip] Skip the first <code>skip</code> records from the the results set 

@apiSuccess {Number} id The id of the system found.
@apiSuccess {Object} info The info of the system found in the same format returned by API <strong>System - Info</strong> <code>GET /api/art/systems/:ID/info</code>.
@apiSuccess {Object} properties The properties of the system found in the same format returned by API <strong>System - Properties</strong> <code>GET /api/art/systems/:ID/properties</code>.
@apiSuccess {Object[]} activities Objects of the activities of the system in the same format returned by API <strong>Activity - Find</strong> <code>GET /api/art/activities</code>. The key is present only if <code>incluseActivities=1</code>

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/systems?sp_ID_RISORSA=U002313254&sp_DN=**********&sp_ID_ULLCO=C00008734&type=ULL_LINEA_NON_ATTIVA&type=ULL_LINEA_ATTIVA_NP
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
   {
      "id" : 2096,
      "info" : {
        "description" : "PTE_Management IC1",
        "type" : "PTE_MANAGEMENT",
        "active" : true,
        "disabled" : false,
        "ended" : false,
        "creationDate" : "2015-04-17T10:57:35.*********+02:00",
        "disablingDate" : null,
        "endingDate" : null,
        "properties" : [
          "PROJECT_COMMESSA",
          "CUSTOMER",
          "DESCRIPTION",
          "INITIATIVE_NUMBER"
        ],
        "groups" : [
          "ADMIN",
          "ROOT",
          "USER"
        ]
      },
      "properties" : {
         "PROJECT_COMMESSA" : "P406",
         "INITIATIVE_NUMBER" : "7945",
         "DESCRIPTION" : null,
         "CUSTOMER" : "ACME"
      }
   }
]

=cut

=pod
@api {get} /api/art/systems Find Extended
@apiName getSystemsExtended
@apiGroup System
@apiDescription Find systems extended. It accepts all the parameters of API <strong>System - Find</strong>, plus the parameter <code>extendedOutput</code>.

@apiParam {Number=1,0} extendedOutput If <code>1</code> instead of returning an array of objects describing the systems, it returns an hash: the key <code>results</code> contains an array of system objects, 
the key <code>count</code> contains the total count of the results excluding limit and skip parameters. If <code>0</code> has the same behaviour of API <strong>System - Find</strong>
@apiParam {Number=0,1} [onlyCount] <code>0</code> returns an hash with the keys <code>count</code> e <code>results</code>, <code>1</code> returns an hash with only the key <code>count</code>.

@apiSuccess {Object[]} anonymous Object rappresenting the search
@apiSuccess {Number} anonymous.count The total count of the systems found.
@apiSuccess {Object[]} anonymous.results Objects of the systems found.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/systems?extendedOutput=1
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "count":165,
  "results":[
    {
      "info":{
        "disabled":false,
        "disablingDate":null,
        "endingDate":null,
        "active":true,
        "description":"IIN-OPS",
        "properties":[
          "CC",
          "COMPETENCE_CENTER_DESCRIPTION",
          "COMPETENCE_CENTER_MANAGER",
          "FIRST_NAME",
          "LAST_NAME"
        ],
        "ended":false,
        "creationDate":"2015-04-30T15:42:39.*********+02:00",
        "groups":[
          "ADMIN",
          "CCM_IIN-OPS",
          "EPEL_MANAGER",
          "GUEST",
          "ROOT",
          "SUPERVISOR"
        ],
        "type":"COMPETENCE_CENTER"
      },
      "id":2372,
      "properties":{
        "FIRST_NAME":"Edoardo",
        "COMPETENCE_CENTER_MANAGER":"<EMAIL>",
        "CC":"5041",
        "LAST_NAME":"Cottino",
        "COMPETENCE_CENTER_DESCRIPTION":"Outside Plant Solutions & IPR"
      }
    }
  ]
}

=cut

=pod
@api {get} /api/art/systems Find ids
@apiName getSystemsIds
@apiGroup System
@apiDescription Find systems ids. It accepts all the parameters of API <strong>System - Find</strong>, plus the parameter <code>onlyids</code>.

@apiParam {Number=1,0} onlyids If <code>1</code> instead of returning an array of objects describing the systems, it returns an array of system ids,
  if <code>0</code> has the same behaviour of API <strong>System - Find</strong>

@apiSuccess {Number[]} id Ids of the systems found.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/systems?onlyids=1,sp_ID_RISORSA=U002313254&sp_DN=**********&sp_ID_ULLCO=C00008734&type=ULL_LINEA_NON_ATTIVA&type=ULL_LINEA_ATTIVA_NP
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
  1356,
  9281
]

=cut

=cut

=pod
@api {get} /api/art/systems Find ids extended
@apiName getSystemsIdsExtended
@apiGroup System
@apiDescription Find systems ids extended. It accepts all the parameters of API <strong>System - Find Ids</strong>, plus the parameter <code>extendedOutput</code>.

@apiParam {Number=1,0} extendedOutput If <code>1</code> instead of returning an array of ids describing the systems, it returns an hash: the key <code>results</code> contains an array of systems ids, 
the key <code>count</code> contains the total count of the results excluding limit and skip parameters. If <code>0</code> has the same behaviour of API <strong>System - Find Ids</strong>
@apiParam {Number=0,1} [onlyCount] <code>0</code> returns an hash with the keys <code>count</code> e <code>results</code>, <code>1</code> returns an hash with only the key <code>count</code>.

@apiSuccess {Object[]} anonymous Object rappresenting the search
@apiSuccess {Number} anonymous.count The total count of the systems found.
@apiSuccess {Object[]} anonymous.results Ids of the systems found.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/systems?onlyids=1&extendedOutput=1
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "count":165,
  "results":[
    "2372"
  ]
}

=cut

=pod
@api {get} /api/art/systems Find GridOutput
@apiName getSystemsGridOuput
@apiGroup System
@apiDescription Find systems for grid output. It accepts all the parameters of API <strong>System - Find</strong>, plus the parameter <code>gridOutput</code>.

@apiParam {Number=1,0} gridOutput If <code>1</code> instead of returning an array of objects describing the systems, it returns an hash: the key <code>results</code> contains an array of system objects, 
the key <code>count</code> contains the total count of the results excluding limit and skip parameters. If <code>0</code> has the same behaviour of API <strong>System - Find</strong>

NOTE: Param <code>type</code> or <code>id_equal</code> is mandatory

@apiSuccess {Object[]} anonymous Object rappresenting the search
@apiSuccess {Number} anonymous.count The total count of the systems found.
@apiSuccess {Object[]} anonymous.results Objects of the systems found in grid format.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/systems?gridOutput=1&type=NETWORK
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "count": 6,
  "results": {
    "data": [
      {
        "[\"info\"][\"type\"]": "NETWORK"
      },
      {
        "[\"info\"][\"type\"]": "NETWORK"
      },
      {
        "[\"info\"][\"type\"]": "NETWORK"
      },
      {
        "[\"info\"][\"type\"]": "NETWORK"
      },
      {
        "[\"info\"][\"type\"]": "NETWORK"
      },
      {
        "[\"info\"][\"type\"]": "NETWORK"
      }
    ],
    "header": [
      {
        "name": "tipo",
        "type": "string",
        "hidden": false,
        "checkboxFilter": false,
        "id": "[\"info\"][\"type\"]"
      }
    ]
  }
}

=cut

=pod
@api {get} /api/art/systems Find for export
@apiName getSystemsForExport
@apiGroup System
@apiDescription Find systems for export. It accepts all the parameters of API <strong>System - Find</strong>, plus the parameter <code>export</code>.

@apiParam {String=xlsx,csv} export Set the format of the file to download

NOTE: Param <code>type</code> or <code>id_equal</code> is mandatory

@apiExample {HTTP} Example usage:
GET /api/art/systems?export=xlsx&type=NETWORK

@apiSuccess {File} anonymous The binary file.

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK
Content-Disposition: attachment; filename="export_20190401172524.xlsx"
Content-Length: 34114
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

=cut

sub handler_systems_get {
	
	my %input_params = @_;
	
	my %query_params = defined $input_params{QUERY_PARAMS} ? %{$input_params{QUERY_PARAMS}} : ();
	
	my $api = vars->{api_art};
	
	if (defined $query_params{export}) {
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {paramname} must be of type {type}", paramname => "export", type => "String"))
			if ref $query_params{export};
	
		# TRANSLATORS: es. Param values is a list of file extensions (eg. 'xlsx','json','csv')
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {paramname} can be {values}", paramname => "export", values => "'xlsx','csv'"))
			unless $query_params{export} =~ /^(xlsx|csv)$/;
		
		$query_params{gridOutput} = 1;
	}

	if (defined $query_params{extendedOutput}){
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {paramname} can be {values}", paramname => "extendedOutput", values => "0,1"))
			unless $query_params{extendedOutput} =~ /^(0|1)$/;
	}
	
	# checks per gridOutput 
	if ($query_params{gridOutput}) {
		# la modalità gridOutput non accetta alcuni paremtri
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "In gridOutput or export mode cannot be used extendedOutput, onlyCount or onlyids params")
			if ($query_params{extendedOutput} || $query_params{onlyCount} || $query_params{onlyids});
		
		# se sono in modalità gridOutput è obbligatorio passare il parametro id_equal or type in quanto non ancora implementato
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "In gridOutput or export mode param type or id_equal is mandatory")
			if (
				(
					! defined $query_params{type}
					||
					$query_params{type} eq ''
				)
				&&
				(
					! defined $query_params{id_equal}
					||
					$query_params{id_equal} eq ''
				)
				&&
				(
					! defined $query_params{type_equal}
					||
					$query_params{type_equal} eq ''
				)
			);
			
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "In gridOutput or export mode param type can have only one value")
			if (defined $query_params{type} && ref($query_params{type}) eq 'ARRAY' && scalar @{$query_params{type}} != 1);
	}
	
	my $cs = API::ART::Collection::System->new( ART => $api );
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless $cs;
	
	my %p = ();
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
		unless defined _normalize_systems_search_filter(OBJ => \%p, QUERY_PARAMS => \%query_params);
	
	$p{SKIP} = $query_params{skip} if defined($query_params{skip});
	$p{LIMIT} = $query_params{limit} if defined($query_params{limit});
	if (defined $query_params{export}) {
		# in caso di export definisco un numero massimo di oggetti
		if (defined $p{LIMIT}) {
			$p{LIMIT} = $p{LIMIT} <= $OBJECTS_EXPORT_LIMIT ? $p{LIMIT} : $OBJECTS_EXPORT_LIMIT;
		} else {
			$p{LIMIT} = $OBJECTS_EXPORT_LIMIT;
		}
	}
	if(defined $query_params{sort}) {
		$p{SORT} = _normalize_systems_sort_filter($query_params{sort}) ;
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
			unless defined $p{SORT};
	}
	$p{EXTENDED_OUTPUT}	= 1 if (defined $query_params{extendedOutput} && $query_params{extendedOutput} == 1)  || $query_params{gridOutput};
	
	$p{CASE_SENSITIVE}		= $query_params{caseSensitive} if defined($query_params{caseSensitive});
	
	if ($p{EXTENDED_OUTPUT}){
		$p{ONLY_COUNT}	= 1 if defined $query_params{onlyCount} && $query_params{onlyCount} == 1;
	}
	
	# NB: prima della patch del 15/02/2018 se era impostato il parametro onlyids=1 venivano restituiti tutti gli id
	# indipendentemente dalla visibilità sul sistema (nota che il count era diverso dal caso onlyids=0)
	# ora sono coerenti ma si è deciso (il dinamico duo Riz e Ivan) di accettare questo break sulla retrocompatibilità
	# in quanto considerato bug fix 
	$p{SHOW_ONLY_WITH_VISIBILITY} = 1;
	
	my $ret = $p{EXTENDED_OUTPUT} ? {} : [];
	if($query_params{onlyids}) {
		my $systems = eval{$cs->find_id( %p )};
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Uncorrect parameters", INTERNAL_ERROR_MSG => $@ || $api->last_error())
			if ( $@ || !$systems );
		if ($p{EXTENDED_OUTPUT}){
			$ret->{count} = $systems->{COUNT};
			unless ($query_params{onlyCount}) {
				$ret->{results} = $systems->{RESULTS};
			}
			
		} else {
			@{$ret} = map { $_ * 1 } @$systems;
		}
	} else {
		my $systems = eval{$cs->find_object( %p )};
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Uncorrect parameters", INTERNAL_ERROR_MSG => $@ || $api->last_error())
			if ( $@ || !$systems );

		my $results;
		
		if ($p{EXTENDED_OUTPUT}){
			$ret->{count} = $systems->{COUNT};
			unless ($query_params{onlyCount}) {
				$results = $systems->{RESULTS};
			}
		} else {
			$results = $systems;
		}
		
		my $tmp_ret = [];
		
		for my $sys ( @$results ) {
			my %dump_params;
			$dump_params{INCLUDE_ACTIVITIES} = (defined $query_params{includeActivities} && $query_params{includeActivities} == 1) ? $query_params{includeActivities} : 0;
			my $sysdump = $sys->dump(%dump_params);
			return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
				unless defined $sysdump;
			push @{$tmp_ret}, $sysdump;
		}
		
		if ($p{EXTENDED_OUTPUT}){
			unless ($query_params{onlyCount}) {
				if ($query_params{gridOutput}){
					
					# la modalità gridOutput accetta type o id_equal
					# se ho passatto id_equal recupero il tipo dall'unica attività restituita se non ha risultati da errore
					my $gridOutputSystemType;
					if($p{SYSTEM_TYPE_NAME}){
						$gridOutputSystemType = $p{SYSTEM_TYPE_NAME}->[0];
					} else {
						$gridOutputSystemType = $tmp_ret->[0]->{info}->{type} if scalar @{$tmp_ret};
					}
					
					if (defined $gridOutputSystemType){
						# recupero la configurazione
						my $header = $api->get_entity_serialization(FORMAT => (defined $query_params{export} ? uc($query_params{export}) : 'JSON'));
						return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
							unless defined $header;
						
						# se non la trova scala su quella di default
						if (! exists $header->{SYSTEMS}->{$gridOutputSystemType} ){
							my $system_type_id = $api->get_system_type_id($gridOutputSystemType);
							return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Invalid system type name {type}", type => $gridOutputSystemType))
								unless defined $system_type_id;
							my $query = '
								select regexp_replace(regexp_replace(t.percorso,\'\["\',\'\'),\'"\]\',\'_\')||t.posizione "NAME"
									, t.etichetta HEADER
									, t.etichetta JSON_DESCRIPTION
									, t.tipo TYPE
									, t.percorso PATH
									, t.nascosto JSON_HIDDEN
									, t.posizione POSITION
									, null JSON_EXCLUDE
								from INTESTAZIONI_DASHBOARDS t
								where t.tipo_oggetto = \'SISTEMA\'
								and t.id_oggetto = '.$system_type_id.'
								order by t.posizione';
							
							# sovascrivo header tanto non mi serve
							$header = eval{$api->_dbh()->fetchall_hashref($query)};
							for my $h (@{$header}){
								$h->{POSITION} = $h->{POSITION}*1;
								for ('JSON_HIDDEN', 'JSON_EXCLUDE'){
									$h->{$_} = $h->{$_} ? $JSON::true : $JSON::false;
								}
							}
						} else {
							# sovascrivo header tanto non mi serve
							$header = $header->{SYSTEMS}->{$gridOutputSystemType};
						}
						
						# i sistemi trovati in modalità gridOutput sono omogenei basta vedere il primo
						return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Configuration not found for system type ".$gridOutputSystemType)
							unless (scalar @{$header});
						
						my $reports = SIRTI::Reports->instance(DB => $api->_dbh());
						
						my $report_params = {
							 DATA => $tmp_ret
							,COLUMNS => $header
						};
						
						if ($query_params{export}) {
							$report_params->{OUTPUT_FORMAT} = $query_params{export};
						} else { # json
							$report_params->{OUTPUT_FORMAT} = 'json';
							$report_params->{JSON_RETURN_STRING} = 1;
							$report_params->{JSON_USE_ID_INSTEAD_OF_NAME} = 1;
						}
						
						my $report = $reports->report(%$report_params);
						
						unless (defined $report) {
							my $error = $reports->last_error();
							# distruggiamo l'oggetto singleton per evitare che se ci sono dei cursori aperti al suo interno, non permettano la disconnessione dal db
							undef $reports;
							return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $error);
						}
								
						# distruggiamo l'oggetto singleton per evitare che se ci sono dei cursori aperti al suo interno, non permettano la disconnessione dal db
						undef $reports;
						if ($query_params{export}) {
							# gestisco il download del file e return send_file_ok
							remove_in_hook_after( $report );
							return send_file_ok($report, system_path => 1, filename => 'export_'.$api->get_sysdate('YYYYMMDDHH24MISS').'.'.$query_params{export});
						} else {
							$ret->{results} = from_json($report);
						}
					} else {
						$ret->{results} = {
							data => [],
							header => []
						};
					}
				} else {
					$ret->{results} = $tmp_ret;	
				}
			}
			
			
			
		} else {
			$ret = $tmp_ret;
		}
	}
	
	return send_ok(MSG => $ret);
	
}

get '/systems' => sub {

	my %query_params = defined request->params('query') ? request->params('query') : ();
	
    return handler_systems_get(QUERY_PARAMS => \%query_params);

};

=pod
@api {post} /api/art/systems Create
@apiName createSystem
@apiGroup System
@apiDescription Create system

@apiUse ContentTypeJsonHeader

@apiExample {HTTP} Example usage:
POST http://localhost/api/art/systems
Content-Type: application/json
Accept: application/json

{
  "type" : "PTE_MANAGEMENT",
  "objectType" : "DII",
  "description" : "PTE_Management IC1",
  "groups" : ["USER","ADMIN"]
  "properties" : {
    "SUPERVISOR" : "<EMAIL>",
    "IC_NUMBER" : "75884"
  }
}

@apiParam {String} type Type of the system to create.
@apiParam {String} objectType Object Type of the system to create.
@apiParam {String} description Description of the system.
@apiParam {String[]} groups Groups that must have visibility on the just created system.
@apiUse DateAndFormat
@apiParam {Object} [properties] An object representing the properties, where the keys are the NAMEs of the properties.
@apiParam {String} properties.NAME The name of the property.

@apiSuccess {Number} id Id of the system created.

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "id" : 31722454
}

=cut

post '/systems' => sub {

    my $api = vars->{api_art};
    
    my %body = request->params('body');
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
		unless (keys %body);
	
	my $cs = API::ART::Collection::System->new( ART => $api );
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless $cs;
	
	my %p = (
		 SYSTEM_TYPE_NAME		=> $body{type}
		,OBJECT_TYPE_NAME		=> $body{objectType}
		,DESCRIPTION			=> $body{description}
		,GROUPS					=> $body{groups}
		,SYSTEM_CATEGORY_ID		=> 1
        ,SYSTEM_CLASS_ID		=> 1
	);
	$p{PROPERTIES} = $body{properties} if defined($body{properties});
	# FIXME: riabilitare quando fatta la patch alle API::ART
	# $p{DATE} = $body{date} if defined($body{date});
	
	my $system = $cs->create( %p );
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
		unless ( $system );
	
	my $system_id = $system->id();
	
	do_api_save( $api );
	
	return send_ok(MSG => { id => $system_id * 1 });

};

######## ROUTE /systems/:ID ###########

options '/systems/:ID' => sub { handle_cors_request( METHODS => [ 'GET', 'PUT', 'DELETE' ] ); return send_ok(IGNORE_SESSION => 1); };

=pod
@api {get} /systems/:ID Get
@apiName getSystem
@apiGroup System
@apiDescription Returns an object describing the system.

@apiParam {Number=0,1} [excludeInfo=0] Exclude system info.
@apiParam {Number=0,1} [excludeProperties=0] Exclude system properties.

@apiSuccess {Number} id The id of the system found.
@apiSuccess {Object} info The info of the system found in the same format returned by API <strong>System - Info</strong> <code>GET /api/art/systems/:ID/info</code>. Available only if param <code>excludeInfo=0</code> (default).
@apiSuccess {Object} properties The properties of the system found in the same format returned by API <strong>System - Properties</strong> <code>GET /api/art/systems/:ID/properties</code>. Available only if param <code>excludeProperties=0</code> (default).

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/systems/1234
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "info": {
    "disabled": false,
    "disablingDate": null,
    "endingDate": null,
    "active": true,
    "description": "ENEL-FTTH-859",
    "properties": [
      "cadastralCode",
      "centralId",
      "city",
      "cityId",
      "contractId",
      "country",
      "customerId",
      "pfpId",
      "pfpName",
      "pop",
      "projectId",
      "province",
      "ring"
        ],
    "ended": false,
    "creationDate": "2017-07-31T11:31:16.*********+02:00",
    "groups": [
	  "ADMIN",
	  "PROJECT_ENEL_FTTH_859",
	  "PROJECT4RPB_ENEL_FTTH_859",
	  "ROOT"
    ],
  "type": "PROJECT"
  },
  "id": 4519,
  "properties": {
    "country": "Italy",
    "ring": "12W",
    "contractId": "FTTH",
    "centralId": "G273_04",
    "projectId": "859",
    "city": "Palermo",
    "customerId": "ENEL",
    "cadastralCode": null,
    "pfpName": "12w1",
    "pfpId": "2074719",
    "cityId": "2249",
    "province": "Palermo",
    "pop": "04 - PA-ROCCA"
  }
}

=cut

sub handler_systems_ID_get {
	
	my %input_params = @_;
	
	my %query_params = defined $input_params{QUERY_PARAMS} ? %{$input_params{QUERY_PARAMS}} : ();
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	my $api = vars->{api_art};
	
	my $system = eval { API::ART::System->new( ART => $api, ID => $uri_params{ID} ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("System ID not found"), INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$system;
	
	my %p = ();
	$p{EXCLUDE_INFO} = $query_params{excludeInfo} ? 1 : 0;
	$p{EXCLUDE_PROPERTIES} = $query_params{excludeProperties} ? 1 : 0;
	my $ret = $system->dump(%p);
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless defined $ret;
	
	return send_ok(MSG => $ret);
}

get '/systems/:ID' => sub {
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return handler_systems_ID_get(QUERY_PARAMS => \%query_params, URI_PARAMS => \%{params('route')});
	
};

=pod
@api {put} /api/art/systems/:ID Disable/Enable
@apiName systemDisableEnable
@apiGroup System
@apiDescription Disable or Enable system

@apiUse ContentTypeJsonHeader

@apiExample {HTTP} Example usage:
PUT http://localhost/api/art/systems/2104
Content-Type: application/json

{
  "action" : "disable"
}

@apiParam {String=disable,enable} action <code>disable</code> disable the system, <code>enable</code> enable the system.
@apiParam {String} [date=Now()] Disable date. Not available with <code>"action" : "enable"</code> - *** NYI ***


@apiSuccessExample Success-Response:
HTTP/1.1 204 No content

=cut

put '/systems/:ID' => sub {
	
	my $api = vars->{api_art};
	
	my %body = request->params('body');
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
		unless (keys %body);
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing {paramname} param", paramname => "action"))
		unless ($body{action});
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Bad value for {paramname} param", paramname => "action"))
		unless ($body{action} =~ /^(disable|enable)$/);
	
	
	my $system = eval { API::ART::System->new( ART => $api, ID => param('ID') ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("System ID not found"), INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$system;
	
	my $action = $body{action};
	
	my %p = ();
	# FIXME: riabilitare quando fatta la patch alle API::ART
	# $p{DATE}			= params->{date} if defined(params->{date});
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
		unless ( $system->$action(%p) );
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_NO_CONTENT);
	
};

=pod
@api {delete} /api/art/systems/:ID End
@apiName systemEnd
@apiGroup System
@apiDescription End system

@apiExample {HTTP} Example usage:
DELETE http://localhost/api/art/systems/2104

@apiParam {String} [date=Now()] End date - *** NYI ***

@apiSuccessExample Success-Response:
HTTP/1.1 204 No content

=cut

del '/systems/:ID' => sub {
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	my $api = vars->{api_art};
	
	my $system = eval { API::ART::System->new( ART => $api, ID => param('ID') ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => "System ID not found", INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$system;
	
	my %p = ();
	# FIXME: riabilitare quando fatta la patch alle API::ART
	# $p{DATE}			= $query_params{date} if defined($query_params{date});
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
		unless ( $system->end(%p) );
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_NO_CONTENT);

};

######## ROUTE /systems/:ID/info ###########

options '/systems/:ID/info' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/systems/:ID/info Info
@apiName systemInfo
@apiGroup System
@apiDescription Returns system informations

@apiSuccess {String} description The description of the system.
@apiSuccess {String} type The system type
@apiSuccess {Boolean} active <code>true</code> if the system is active, <code>false</code> otherwhise.
@apiSuccess {Boolean} disabled <code>true</code> if the system is disabled, <code>false</code> otherwhise.
@apiSuccess {Boolean} ended <code>true</code> if the system is disabled, <code>false</code> otherwhise.
@apiSuccess {String} creationDate The date when the system has been created.
@apiSuccess {String} disablingDate If the system is disabled is the disable date.
@apiSuccess {String} endingDate If the system is ended is the end date.
@apiSuccess {String[]} properties The property types associated to the system.
@apiSuccess {String[]} groups The groups that can view the system.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/systems/2104/info
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "description" : "PTE_Management IC1",
  "type" : "PTE_MANAGEMENT",
  "active" : true,
  "disabled" : false,
  "ended" : false,
  "creationDate" : "2015-04-17T10:57:35.*********+02:00",
  "disablingDate" : null,
  "endingDate" : null,
  "properties" : [
    "PROJECT_COMMESSA",
    "CUSTOMER",
    "DESCRIPTION",
    "INITIATIVE_NUMBER"
  ],
  "groups" : [
    "ADMIN",
    "ROOT",
    "USER"
  ]
}

=cut

get '/systems/:ID/info' => sub {

	my $api = vars->{api_art};
	
	my $system = eval { API::ART::System->new( ART => $api, ID => param('ID') ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => "System ID not found", INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$system;
	
	my $ret = $system->dump();
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless defined $ret;
	
	return send_ok(MSG => $ret->{info});

};

######## ROUTE /systems/:ID/properties ###########

options '/systems/:ID/properties' => sub { handle_cors_request( METHODS => [ 'GET', 'PUT' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/systems/:ID/properties Properties
@apiName systemProperties
@apiGroup System
@apiDescription Returns an object representing the properties of the system, where the keys are the NAMEs of the properties.

@apiSuccess {String} NAME The value of the property named NAME.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/systems/2104/properties
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "SUPERVISOR" : "<EMAIL>",
  "INITIATIVE_NUMBER" : "17895",
  "CUSTOMER": "ACME"
}

=cut

get '/systems/:ID/properties' => sub {

	my $api = vars->{api_art};
	
	my $system = eval { API::ART::System->new( ART => $api, ID => param('ID') ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => "System ID not found", INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$system;
	
	return send_ok(MSG => $system->property());

};

=pod
@api {put} /api/art/systems/:ID/properties Upsert Properties
@apiName systemUpsertProperties
@apiGroup System
@apiDescription Upsert system properties

@apiUse ContentTypeJsonHeader

@apiExample {HTTP} Example usage:
PUT http://localhost/api/art/systems/2104/properties
Content-Type: application/json

{
  "properties" : {
  	"SUPERVISOR" : "<EMAIL>",
  	"IC_NUMBER" : null
  }
}

@apiParam {Object} properties An object representing the properties, where the keys are the NAMEs of the properties.
   To delete a property set the value to <code>null</code>
@apiParam {String} properties.NAME The name of the property.
@apiUse DateAndFormat

@apiSuccessExample Success-Response:
HTTP/1.1 204 No content

=cut

sub handler_systems_ID_properties_put {
	
	my %input_params = @_;
	
	my %body = defined $input_params{BODY} ? %{$input_params{BODY}} : ();
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
		unless (keys %body);
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	my $api = vars->{api_art};
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing {paramname} param", paramname => "properties"))
		unless ($body{properties});
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Bad value for {paramname} param", paramname => "properties"))
		unless (ref($body{properties}) eq 'HASH');
	
	
	my $system = eval { API::ART::System->new( ART => $api, ID => $uri_params{'ID'} ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => "System ID not found", INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$system;
	
	my %p = ();
	# FIXME: riabilitare quando fatta la patch alle API::ART
	# $p{DATE}			= params->{date} if defined(params->{date});
	
	my @delete_properties = ();
	my $update_properties = {};
	
	while ( my ($key, $value) = each %{$body{properties}} ) {
		if (defined $value){
			$update_properties->{$key} = $value;
		} else {
			push (@delete_properties, $key);
		}
	}
	
	if (scalar(@delete_properties)){
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
			unless ($system->delete_property(@delete_properties));
	}
	
	if (scalar(keys %{$update_properties})){
		$p{PROPERTIES} = $update_properties;
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
			unless ($system->set_property(%p));
	}
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_NO_CONTENT);
}

put '/systems/:ID/properties' => sub {
	
	my %body = request->params('body');
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
		unless (keys %body);
	
	return handler_systems_ID_properties_put(BODY => \%body, URI_PARAMS => \%{params('route')});
	
};

######## ROUTE /systems/:ID/properties/:PROPERTY ###########

options '/systems/:ID/properties/:PROPERTY' => sub { handle_cors_request( METHODS => [ 'GET', 'PUT' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/systems/:ID/properties/:PROPERTY Property
@apiName systemProperty
@apiGroup System
@apiDescription Returns an object representing the property of the system, where the key is the NAME of the property.

@apiSuccess {String} NAME The value of the property named NAME.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/systems/2104/properties/SUPERVISOR
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "SUPERVISOR" : "<EMAIL>"
}

=cut

# aggiungere queryparams extendedOutput 
	# - property_ext + send_file

get '/systems/:ID/properties/:PROPERTY' => sub {

	my $api = vars->{api_art};
	
	my $system = eval { API::ART::System->new( ART => $api, ID => param('ID') ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => "System ID not found", INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$system;

	my $properties_list = $system->info()->{PROPERTIES};

	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => "Property not found")
		unless grep {$_ eq param('PROPERTY')} @{$properties_list};

	my $property_types = $api->enum_system_property(SYSTEM_TYPE_ID => $system->info()->{SYSTEM_TYPE_ID}, EXTENDED_OUTPUT => 1);

	if ($property_types->{param('PROPERTY')}->{TYPE} eq 'BLOB'){
		my $tmp = $system->property_ext(param('PROPERTY'));
		return send_file_ok($tmp->fh_decoded, filename => $tmp->name, content_type => $tmp->mime);
	} else {
		return send_ok(MSG => {param('PROPERTY') => $system->property_ext(param('PROPERTY'))});
	}
};

######## ROUTE /systems/:ID/properties/:PROPERTY/measurements ###########

options '/systems/:ID/properties/:PROPERTY/measurements' => sub { handle_cors_request( METHODS => [ 'GET', 'PUT' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/systems/:ID/properties/:PROPERTY/measurements Property measurements
@apiName systemPropertyMeasurements
@apiGroup System
@apiDescription Returns an object representing the measurements of the property of the system

@apiParam {ISODate} from From Date.
@apiParam {ISODate} to To Date.
@apiParam {String} aggregateBy Aggregate mode. See API::ART::System documentation for available values

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/systems/2104/properties/temperature/measurements?from=2012-11-07T00:00:00.000000+01:00&to=2012-11-08T00:00:00.000000+01:00
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
    [
        "2012-11-07T00:00:00.000000+01:00",
        12.5
    ],
    [
        "2012-11-08T00:00:00.000000+01:00",
        12.3
    ]
]

=cut

get '/systems/:ID/properties/:PROPERTY/measurements' => sub {

	my $api = vars->{api_art};

	my %query_params = defined request->params('query') ? request->params('query') : ();

	for ('from', 'to'){
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing mandatory param {paramname}", paramname => $_))
			unless defined $query_params{$_};
	}
	
	my $system = eval { API::ART::System->new( ART => $api, ID => param('ID') ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => "System ID not found", INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$system;

	my $property_types = $api->enum_system_property(SYSTEM_TYPE_ID => $system->cached_info('SYSTEM_TYPE_ID'), EXTENDED_OUTPUT => 1);

	unless (exists $property_types->{param('PROPERTY')}){
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Property {p} not defined for system", p => param('PROPERTY')));
	}

	if (
		$property_types->{param('PROPERTY')}->{TYPE} eq 'BLOB'
		&&
		$query_params{from} ne $query_params{to}
	) {
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("For property of type BLOB, params 'from' and 'to' must be equal"));
	}

	my $params = {
		PROPERTIES => [param('PROPERTY')],
		FROM => $query_params{from},
		TO => $query_params{to},
	};

	$params->{AGGREGATE_BY} = $query_params{aggregateBy} if defined $query_params{aggregateBy};

	my $measurements = $system->property_measurements(%$params);
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => vars->{api_art}->last_error())
		unless $measurements;
	
	if ($property_types->{param('PROPERTY')}->{TYPE} eq 'BLOB'){
		my $tmp = API::ART::System::Property::BLOB->new(
			SYSTEM => $system,
			PROPERTY => param('PROPERTY'),
			VALUE => $measurements->{param('PROPERTY')}[0]->[1]
		);
		my $decoded = $tmp->fh_decoded;
		my ($fh, $file) = tempfile( DIR => $ENV{ART_REPOSITORY_TMP_TMPFILES});
		while(my $line = <$decoded>){
			print $fh $line;
		}
		close $fh;
		remove_in_hook_after ( $file );
		return send_file_ok($file, system_path => 1, filename => $tmp->name, content_type => $tmp->mime);
	} else {
		return send_ok(MSG => $measurements->{param('PROPERTY')});
	}
};

######## ROUTE /systems/:ID/systemProperties ###########

options '/systems/:ID/systemProperties' => sub { handle_cors_request( METHODS => [ 'GET', 'PUT' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/systems/:ID/systemProperties System properties
@apiName getSystemProperties
@apiGroup System
@apiDescription Return the system properties, showing the organization of the groups of them.

@apiSuccess {Object[]} anonymous Object representing the system properties group
@apiSuccess {String} [anonymous.group] Name of the group
@apiSuccess {Number} [anonymous.id] Id of the group
@apiSuccess {Object[]} anonymous.properties Object representing the system property
@apiSuccess {String} anonymous.properties.name Name of the property
@apiSuccess {String} anonymous.properties.value Value of the property
@apiSuccess {Boolean} anonymous.properties.notYetUsed <code>false</code> if the system made a step where the property is defined, <code>true</code> otherwise.
@apiSuccess {Boolean} anonymous.properties.readOnly <code>true</code> if the property is readOnly, <code>false</code> otherwise.
@apiSuccess {Boolean} anonymous.properties.expired <code>false</code> if the property is no longer associated to at least an action of the activity type, <code>true</code> otherwise.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/systems/2573/systemProperties
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
  {
    "group":"ENGAGEMENT_DETAILS",
    "id":"1",
    "properties":[
      {
        "notYetUsed":false,
        "expired":false,
        "value":"<EMAIL>",
        "name":"COMPETENCE_CENTER_MANAGER",
        "readOnly":true
      },
      {
        "notYetUsed":false,
        "expired":false,
        "value":"200",
        "name":"ESTIMATED_EFFORT_OUTLOOK",
        "readOnly":true
      },
      {
        "notYetUsed":false,
        "expired":false,
        "value":"<EMAIL>",
        "name":"SUPERVISOR",
        "readOnly":true
      },
      {
        "notYetUsed":false,
        "expired":false,
        "value":"76477",
        "name":"PROJECT_COMMESSA",
        "readOnly":true
      }
    ]
  },
  {
    "group":"CATEGORIZATION",
    "id":"3",
    "properties":[
      {
        "notYetUsed":false,
        "expired":false,
        "value":"[\"Proposing\"]",
        "name":"CATEGORY_LIST",
        "readOnly":false
      }
    ]
  },
  {
    "group":"WIP",
    "id":"2",
    "properties":[
      {
        "notYetUsed":false,
        "expired":false,
        "value":"[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"]",
        "name":"RESOURCE_LIST",
        "readOnly":true
      },
      {
        "notYetUsed":false,
        "expired":false,
        "value":"[\"<EMAIL>\"]",
        "name":"RESOURCE_LIST_BL",
        "readOnly":true
      },
      {
        "notYetUsed":false,
        "expired":false,
        "value":"[\"<EMAIL>\",\"<EMAIL>\"]",
        "name":"RESOURCE_LIST_OG",
        "readOnly":true
      },
      {
        "notYetUsed":false,
        "expired":false,
        "value":"226",
        "name":"TOTAL_ESTIMATED_EFFORT",
        "readOnly":false
      },
      {
        "notYetUsed":false,
        "expired":false,
        "value":"169",
        "name":"TOTAL_ACCOUNTED_EFFORT",
        "readOnly":false
      }
    ]
  }
]

=cut

sub handler_systems_ID_systemProperties_get{
	
	my %input_params = @_;
	
	my %query_params = defined $input_params{QUERY_PARAMS} ? %{$input_params{QUERY_PARAMS}} : ();
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	my $api = vars->{api_art};
	
	my $system = eval { API::ART::System->new( ART => $api, ID => $uri_params{'ID'} ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("System ID not found"), INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$system;
	
	
	my $results = eval{$system->system_property_group()};
	
	return send_ko(INTERNAL_ERROR_MSG => $@)
		if ($@);
	
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless ($results);
	
	my $rets = [];
	
	for my $res (@{$results}){
		
		my $ret = {};
		$ret->{group} = $res->{GROUP} if defined $res->{GROUP};
		$ret->{id} = $res->{ID} if defined $res->{ID};
		$ret->{properties} = [];
		for my $prop (@{$res->{PROPERTIES}}){
			push @{$ret->{properties}}, {
				name => $prop->{NAME}
				,value =>  $prop->{VALUE}
				,readOnly => $prop->{READ_ONLY} eq 'Y' ? $JSON::true : $JSON::false 
				,expired => $prop->{EXPIRED} eq 'Y' ? $JSON::true : $JSON::false
				,notYetUsed => $prop->{NOT_YET_USED} eq 'Y' ? $JSON::true : $JSON::false
				,nullable => $prop->{NULLABLE} eq 'Y' ? $JSON::true : $JSON::false
			};
		}
		
		push @{$rets}, $ret;
	}
	
	return send_ok(MSG => $rets);
}

get '/systems/:ID/systemProperties' => sub {
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return handler_systems_ID_systemProperties_get(QUERY_PARAMS => \%query_params, URI_PARAMS => \%{params('route')});
};

######## ROUTE /systems/:ID/groups ###########

options '/systems/:ID/groups' => sub { handle_cors_request( METHODS => [ 'POST', 'GET', 'PUT', 'DELETE' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/systems/:ID/groups Groups viewing the system
@apiName systemGroupsGet
@apiGroup System
@apiDescription Returns the list of the groups that can view the system

@apiSuccess {String[]} group The group name.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/systems/2104/groups
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
  "ADMIN",
  "ROOT",
  "USER"
]

=cut

get '/systems/:ID/groups' => sub {

    my $api = vars->{api_art};
    
    my $system = eval { API::ART::System->new( ART => $api, ID => param('ID') ) };
    return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => "System ID not found", INTERNAL_ERROR_MSG => $@ || $api->last_error())
    	if $@ || !$system;

	return send_ok(MSG => $system->info('GROUPS'));

};

=pod
@api {post} /api/art/systems/:ID/groups Add Groups viewing the system
@apiName systemGroupsPost
@apiGroup System
@apiDescription Add groups to the list of the groups viewing the system. Note that "ROOT" group cannot be used.

@apiUse ContentTypeJsonHeader

@apiParam {String[]} groups The group name.

@apiExample {HTTP} Example usage:
POST http://localhost/api/art/systems/2104/groups
Content-Type: application/json

{
  "groups": [
    "EPEL_MANAGER"
  ]
}

@apiSuccessExample Success-Response:
HTTP/1.1 204 No content

=cut

post '/systems/:ID/groups' => sub {

    my $api = vars->{api_art};

    my %body = request->params('body');
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
		unless (keys %body);
	
	return send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => "groups param must be an array of at least one element")
		if (ref ($body{groups}) ne 'ARRAY' || scalar @{$body{groups}} == 0);

	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "ROOT group cannot be used")
		if grep { $_ eq 'ROOT' } @{$body{groups}};

	my $system = eval { API::ART::System->new( ART => $api, ID => param('ID') ) };
    return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => "System ID not found", INTERNAL_ERROR_MSG => $@ || $api->last_error())
    	if $@ || !$system;
    	
    return send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => "Unable to set_groups: ".$api->last_error())
		unless ($system->set_groups(@{$body{groups}}));
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_NO_CONTENT);

};

=pod
@api {put} /api/art/systems/:ID/groups Override Groups viewing the system
@apiName systemGroupsPut
@apiGroup System
@apiDescription Override the list of the groups viewing the system. Note that "ROOT" group cannot be used.

@apiUse ContentTypeJsonHeader

@apiParam {String[]} groups The group name.

@apiExample {HTTP} Example usage:
PUT http://localhost/api/art/systems/2104/groups
Content-Type: application/json

{
  "groups": [
    "ADMIN",
    "EPEL_MANAGER"
  ]
}

@apiSuccessExample Success-Response:
HTTP/1.1 204 No content

=cut

put '/systems/:ID/groups' => sub {

    my $api = vars->{api_art};
    
    my %body = request->params('body');
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
		unless (keys %body);
	
	return send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => "groups param must be an array of at least one element")
		if (ref ($body{groups}) ne 'ARRAY' || scalar @{$body{groups}} == 0);

	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "ROOT group cannot be used")
		if grep { $_ eq 'ROOT' } @{$body{groups}};

	my $system = eval { API::ART::System->new( ART => $api, ID => param('ID') ) };
    return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => "System ID not found", INTERNAL_ERROR_MSG => $@ || $api->last_error())
    	if $@ || !$system;
    
    return send_ko(CODE => HTTP_INTERNAL_SERVER_ERROR, INTERNAL_ERROR_MSG => "Unable to delete all groups: ".$api->last_error())
		unless ($system->delete_groups(grep { $_ ne 'ROOT' } @{$system->info('GROUPS')}));
    
    return send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => "Unable to set_groups: ".$api->last_error())
		unless ($system->set_groups(@{$body{groups}}));
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_NO_CONTENT);

};

=pod
@api {delete} /api/art/systems/:ID/groups Remove Groups viewing the system
@apiName systemGroupsDelete
@apiGroup System
@apiDescription Remove the groups specified from the list of the groups viewing the system. Note that "ROOT" group cannot be used.

@apiUse ContentTypeJsonHeader

@apiParam {String[]} groups The group name.

@apiExample {HTTP} Example usage:
DELETE http://localhost/api/art/systems/2104/groups
Content-Type: application/json

{
  "groups": [
    "ADMIN",
    "EPEL_MANAGER"
  ]
}

@apiSuccessExample Success-Response:
HTTP/1.1 204 No content

=cut

del '/systems/:ID/groups' => sub {

    my $api = vars->{api_art};
    
    my %body = request->params('body');
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
		unless (keys %body);
	
	return send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => "groups param must be an array of at least one element")
		if (ref ($body{groups}) ne 'ARRAY' || scalar @{$body{groups}} == 0);

	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "ROOT group cannot be used")
		if grep { $_ eq 'ROOT' } @{$body{groups}};

	my $system = eval { API::ART::System->new( ART => $api, ID => param('ID') ) };
    return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => "System ID not found", INTERNAL_ERROR_MSG => $@ || $api->last_error())
    	if $@ || !$system;
    
    return send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => "Unable to delete_groups: ".$api->last_error())
		unless ($system->delete_groups(@{$body{groups}}));
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_NO_CONTENT);

};

######## ROUTE /systems/:ID/children ###########

options '/systems/:ID/children' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/systems/:ID/children Find children
@apiName getSystemChildren
@apiGroup System
@apiDescription Returns an array of objects describing the children systems matching the specified criteria.

@apiParam {String[]} [type] Filter systems of these types.

@apiSuccess {Number} id The id of the system found.
@apiSuccess {Object} info The info of the system found in the same format returned by API <strong>System - Info</strong> <code>GET /api/art/systems/:ID/info</code>.
@apiSuccess {Object} properties The properties of the system found in the same format returned by API <strong>System - Properties</strong> <code>GET /api/art/systems/:ID/properties</code>.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/systems/:ID/children/?type=MACROLAVORO
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
  {
    "info": {
      "disabled": false,
      "disablingDate": null,
      "endingDate": null,
      "active": true,
      "description": "SCAVO-26-mt",
      "properties": [
        "category",
        "contractId",
        "customerId",
        "estimatedDuration",
        "permitsAreaId",
        "projectId",
        "ring",
        "ringId",
        "subCategory",
        "toAssignQuantity",
        "unitOfMeasure"
      ],
      "ended": false,
      "creationDate": "2018-02-06T14:21:16.*********+01:00",
      "groups": [
        "ADMIN",
        "CITY_2249",
        "PROJECT_ENEL_FTTH_993",
        "ROOT"
      ],
      "type": "MACROLAVORO"
    },
    "id": 10375,
    "properties": {
      "unitOfMeasure": "mt",
      "ring": "07",
      "subCategory": "SCAVO",
      "contractId": "FTTH",
      "toAssignQuantity": "26",
      "customerId": "ENEL",
      "projectId": "993",
      "permitsAreaId": "2444370",
      "ringId": "GA3Q",
      "estimatedDuration": "0",
      "category": "SCAVO"
    }
  }
]
=cut

sub handler_systems_ID_children_get {
	
	my %input_params = @_;
	
	my %query_params = defined $input_params{QUERY_PARAMS} ? %{$input_params{QUERY_PARAMS}} : ();
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	my $api = vars->{api_art};
	
	my %p = ();
	
	if (defined $query_params{type}) {
		if (ref $query_params{type}){
			$p{SYSTEM_TYPE_NAME} = $query_params{type};
		} else {
			$p{SYSTEM_TYPE_NAME} = [$query_params{type}];
		}
	}
	
	$p{SHOW_ONLY_WITH_VISIBILITY} = 1;
	
	my $system = eval { API::ART::System->new( ART => $api, ID => $uri_params{'ID'} ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => "System ID not found", INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$system;
	
	my $children = $system->get_children(%p);
	
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless $children;
	
	my @ret = ();
	
	for my $child (@{$children}){
		my $dumpsys = $child->dump();
		return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
			unless $dumpsys;
		
		push @ret, $dumpsys;
	}
	
	return send_ok(MSG => \@ret);
}

get '/systems/:ID/children' => sub {
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return handler_systems_ID_children_get(QUERY_PARAMS => \%query_params, URI_PARAMS => \%{params('route')});
	
};

######## ROUTE /systems/:ID/measurements ###########

options '/systems/:ID/measurements' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /systems/:ID/measurements System measurements
@apiName systemMeasurements
@apiGroup System
@apiDescription Returns an array of object representing the measurements of the system required

@apiParam {ISODate} from From Date.
@apiParam {ISODate} to To Date.
@apiParam {String} f_from The field to use for fromDate. Special value '@TS' (measure timestamp) or '@TS_INSERT' (measure insert date)
@apiParam {String} f_to The field to use for toDate. Special value '@TS' (measure timestamp) or '@TS_INSERT' (measure insert date)
@apiParam {String[]} m Name of the key requested

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/systems/2104/measurements?from=2012-11-07T00:00:00.000000+01:00&to=2012-11-08T00:00:00.000000+01:00&f_from=@TS&t_from=@TS_INSERT&m=T_tsFrom&m=T_tsTo&m=transactionId
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
    {
        "T_tsFrom": "2012-11-08T00:00:00.000000+01:00",
        "T_tsTo": "2012-11-09T00:00:00.000000+01:00",
        "transactionId": "45678ijhg"
    }
]

=cut

get '/systems/:ID/measurements' => sub {

	my $api = vars->{api_art};

	my %query_params = defined request->params('query') ? request->params('query') : ();

	for ('from', 'to', 'f_from', 'f_to', 'm'){
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing mandatory param {paramname}", paramname => $_))
			unless defined $query_params{$_};
	}
	
	my $system = eval { API::ART::System->new( ART => $api, ID => param('ID') ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => "System ID not found", INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$system;

	my $property_types = $api->enum_system_property(SYSTEM_TYPE_ID => $system->cached_info('SYSTEM_TYPE_ID'), EXTENDED_OUTPUT => 1);

	# se è uno solo lo trasformo comunque in ARRAY
	$query_params{m} = [$query_params{m}]
		unless ref $query_params{m};

	my $params = {
		RESPONSE => {
			KEYS => $query_params{m}
		},
		FROM	=> $query_params{from},
		TO		=> $query_params{to},
		F_FROM	=> $query_params{f_from},
		F_TO	=> $query_params{f_to},
	};

	my $measurements = $system->find_property_measurements(%$params);
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => vars->{api_art}->last_error())
		unless $measurements;
	
	return send_ok(MSG => $measurements);

};	

######## ROUTE /activities ###########

options '/activities' => sub { handle_cors_request( METHODS => [ 'GET', 'POST' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/activities Find
@apiName getActivities
@apiGroup Activity
@apiDescription Returns an array of objects describing the activities matching the specified criteria.

@apiParam {cache=1,0} [cache=1] If <code>0</code> instead of returning cached activities, it returns "fresh" activities 
@apiParam {String[]} [type] Filter activities of these types. Overwritten by param type_*
@apiParam {String} [type_equal] Filter activities of one type.
@apiParam {String[]} [type_in] Filter activities given a list of types.
@apiParam {String} [type_begins] Filter activities with type name that begins with passed value.
@apiParam {String} [type_ends] Filter activities with type name that ends with passed value.
@apiParam {String} [type_contains] Filter activities with type name that contains with passed value.
@apiParam {String} [id_equal] Filter activities given an id.
@apiParam {String[]} [id_in] Filter activities given a list of ids.
@apiParam {String} [id_begins] Filter activities with id that begins with passed value.
@apiParam {String} [id_ends] Filter activities with id that ends with passed value.
@apiParam {String} [id_contains] Filter activities with id that contains with passed value.
@apiParam {String} [status_equal] Filter activities in one status.
@apiParam {String[]} [status_in] Filter activities given a list of status.
@apiParam {String} [status_begins] Filter activities with status that begins with passed value.
@apiParam {String} [status_ends] Filter activities with status that ends with passed value.
@apiParam {String} [status_contains] Filter activities with stuats that contains with passed value.
@apiParam {String} [description_equal] Filter activities with a defined description.
@apiParam {String[]} [description_in] Filter activities given a list of descriptions.
@apiParam {String} [description_begins] Filter activities with description that begins with passed value.
@apiParam {String} [description_ends] Filter activities with description that ends with passed value.
@apiParam {String} [description_contains] Filter activities with description that contains with passed value.
@apiParam {Number[]} [systemId] Filter activities created on these systems.
@apiParam {String} [systemType_equal] Filter activities with system of given type.
@apiParam {String[]} [systemType_in] Filter activities given a list of system types.
@apiParam {String} [systemType_begins] Filter activities with system type name that begins with passed value.
@apiParam {String} [systemType_ends] Filter activities with system type name that ends with passed value.
@apiParam {String} [systemType_contains] Filter activities system with type name that contains with passed value.
@apiParam {String} [systemDescription_equal] Filter activities given a system description.
@apiParam {String[]} [systemDescription_in] Filter activities given a list of system descriptions.
@apiParam {String} [systemDescription_begins] Filter activities with system description that begins with passed value.
@apiParam {String} [systemDescription_ends] Filter activities with system description that ends with passed value.
@apiParam {String} [systemDescription_contains] Filter activities with system description that contains with passed value.
@apiParam {Number=0,1,-1} [active=0] <code>0</code> all activities, <code>1</code> only the tickets in a NON final status, <code>-1</code> only the tickets in a final status.
@apiParam {Number=0,1} [showOnlyWithVisibility=0] <code>0</code> all activities: some element can be null if the user has not visibility on the activity, <code>1</code> only the tickets that user can view.
@apiParam {Number=0,1} [assignedToMe] <code>0</code> all activities, <code>1</code> only the tickets that user is assignee
@apiParam {String[]} [assignedTo] Filter activities given a list of assignees.
@apiParam {Number=0,1} [createdByMe] <code>0</code> all activities, <code>1</code> only the tickets created by the user
@apiParam {String[]} [createdBy] Filter activities given a list of creation users.
@apiParam {Number=0,1} [caseSensitive=1] <code>0</code> execute the search in non-CASE_SENSITIVE mode, <code>1</code> execute the search in CASE_SENSITIVE mode.
@apiParam {String} [ap_NAME_equal] Where NAME is the name of the property to filter. Can be repeated for more then one property name. An exact search of the value will be done.
@apiParam {String} [ap_NAME_notEqual] Where NAME is the name of the property to filter. Can be repeated for more then one property name. An exact search of the value will be done.
@apiParam {String[]} [ap_NAME_in] Where NAME is the name of the property to filter. Can be repeated for more then one property name. An exact search of the given list of the values will be done.
@apiParam {String} [ap_NAME_begins] Where NAME is the name of the property to filter. Filter activities with property that begins with passed value.
@apiParam {String} [ap_NAME_ends] Where NAME is the name of the property to filter. Filter activities with property that ends with passed value.
@apiParam {String} [ap_NAME_contains] Where NAME is the name of the property to filter. Filter activities with property that contains with passed value.
@apiParam {Number=1} [ap_NAME_isNull] Where NAME is the name of the property to filter. A search of the NAME with value <code>null</code> will be done.
@apiParam {Number=1} [ap_NAME_isNotNull] Where NAME is the name of the property to filter. A search of the NAME with value <code>not null</code> will be done.
@apiParam {Number=0,1} [includeSystem=0] Include complete information about system.
@apiParam {Number=0,1} [availableActions=0] Include the available actions for current status and user profile.
@apiParam {Number=0,1} [excludeInfo=0] Exclude activity info.
@apiParam {Number=0,1} [excludeProperties=0] Exclude activity properties.
@apiParam {String} [sp_NAME_equal] Where NAME is the name of the system property to filter. Can be repeated for more then one system property name. An exact search of the value will be done.
@apiParam {String} [sp_NAME_notEqual] Where NAME is the name of the system property to filter. Can be repeated for more then one system property name. An exact search of the value will be done.
@apiParam {String[]} [sp_NAME_in] Where NAME is the name of the system property to filter. Can be repeated for more then one system property name. An exact search of the given list of the values will be done.
@apiParam {String} [sp_NAME_begins] Where NAME is the name of the system property to filter. Filter activities with system property that begins with passed value.
@apiParam {String} [sp_NAME_ends] Where NAME is the name of the system property to filter. Filter activities system with property that ends with passed value.
@apiParam {String} [sp_NAME_contains] Where NAME is the name of the system property to filter. Filter activities with system property that contains with passed value.
@apiParam {Number=1} [sp_NAME_isNull] Where NAME is the name of the system property to filter. A search of the NAME with value <code>null</code> will be done.
@apiParam {Number=1} [sp_NAME_isNotNull] Where NAME is the name of the system property to filter. A search of the NAME with value <code>not null</code> will be done.
@apiParam {Miscellaneous} [parent_UPLEVEL_KEY] Using these keys you can filter over parent activity.  
  <code>UPLEVEL</code> specifies how many
  levels the search must go up  (for example: <code>1</code> mean *father*, <code>2</code> mean *grandfather*, etc.).  
  <code>KEY</code> specifies the kind of filter to apply: you can use one of the filter previously defined for the activity.  
  As an example: for finding an activity whom the current status of the father is <code>IN_PROGRESS</code> you must add in the query
  string <code>parent_1_status_equal=IN_PROGRESS</code>.
@apiParam {String[]=[-]id,[-]type,[-]systemType,[-]systemDescription,[-]creationDate,[-]description,[-]status,[-]lastVarDate,[-]ap_NAME},[-]sp_NAME},[-]parent_UPLEVEL_KEY} [sort] If present, it define sort fields. If the field is prefixed by <code>-</code> the sort order is descending.
 example:
```
sort=type&sort=-id&sort=-ap_COMPETENCE_CENTER
```
@apiParam {Number} [limit] Maximum number of results
@apiParam {Number} [skip] Skip the first <code>skip</code> records from the the results set

@apiSuccess {Number} id The id of the activity found.
@apiSuccess {Object} info The info of the activity found in the same format returned by API <strong>Activity - Info</strong> <code>GET /api/art/activities/:ID/info</code>.
@apiSuccess {Object} properties The properties of the activity found in the same format returned by API <strong>Activity - Properties</strong> <code>GET /api/art/activities/:ID/properties</code>.
@apiSuccess {Object} system The system object in the same format returned by API <strong>System - Find</strong> <code>GET /api/art/systems</code>. It's present only if param <code>includeSystem=1</code>  
@apiSuccess {Object} availableActions An array with the available actions for current status and user profile. It's present only if param <code>includeAvailableActions=1</code>  

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/activities?ap_ID_RISORSA_in=U002313254&ap_DN_eqaul=**********&ap_ID_ULLCO_like=C%00008734&type=ULL_LINEA_NON_ATTIVA&type=ULL_LINEA_ATTIVA_NP
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
   {
      "id" : 5699,
      "info" : {
         "owner" : "ROOT",
         "ownerObj": {
           "email": null,
           "firstName": "ROOT",
           "disabled": false,
           "serviceUser": false,
           "groups": [
             "ROOT"
           ],
           "mobilePhone": null,
           "lastName": "ROOT",
           "username": "ROOT"
         },
         "currentUser" : null,
         "currentUserObj": null,
         "lastVarDate" : "2015-04-17T10:57:35.*********+02:00",
         "systemType" : "PTE_MANAGEMENT",
         "description" : "IC1",
         "properties" : [
           "SUPERVISOR",
           "FTE",
           "ESTIMATED_EFFORT_OUTLOOK"
         ],
         "status" : "OPEN",
         "creationDate" : "2015-04-17T10:57:35.*********+02:00",
         "parentId" : null,
         "dateNextStep" : "2015-04-17T10:57:35.*********+02:00",
         "type" : "EPEL_IC",
         "systemId" : 2096,
         "children" : [],
         "isLocked" : true,
         "isLockedByMyself": false,
		 "active": true,
		 "lastTransitionId": 10,
		 "lastUpdateId": 20,
		 "lastUpdateTimestamp": "2015-04-17T10:57:35.*********+02:00",
		 "version": 10,
         "creationUser": "ROOT",
         "creationUserObj": {
           "email": null,
           "firstName": "ROOT",
           "disabled": false,
           "serviceUser": false,
           "groups": [
             "ROOT"
           ],
           "mobilePhone": null,
           "lastName": "ROOT",
           "username": "ROOT"
         }
      },
      "properties" : {
         "ESTIMATED_EFFORT_OUTLOOK" : "75",
         "FTE" : null,
         "SUPERVISOR" : "<EMAIL>"
      }
   }
]
=cut

=pod
@api {get} /api/art/activities Find Extended
@apiName getActivitiesExtended
@apiGroup Activity
@apiDescription Find activities extended. It accepts all the parameters of API <strong>Activity - Find</strong>, plus the parameter <code>extendedOutput</code>.

@apiParam {Number=1,0} extendedOutput If <code>1</code> instead of returning an array of objects describing the activities, it returns an hash: the key <code>results</code> contains an array of activity objects, 
the key <code>count</code> contains the total count of the results excluding limit and skip parameters. If <code>0</code> has the same behaviour of API <strong>Activity - Find</strong>
@apiParam {Number=0,1} [onlyCount] <code>0</code> returns an hash with the keys <code>count</code> e <code>results</code>, <code>1</code> returns an hash with only the key <code>count</code>.
@apiParam {Number=0,1} [includeTotalCount] <code>1</code> add the key <code>totalCount</code> excluding param <code>assignedToMe</code> and <code>createdByMe</code> from the search.

@apiSuccess {Object[]} anonymous Object rappresenting the search
@apiSuccess {Number} anonymous.count The total count of the activities found.
@apiSuccess {Object[]} anonymous.results Objects of the activities found.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/activities?extendedOutput=1&ap_ID_RISORSA_in=U002313254&ap_DN_equal=**********&type=ULL_LINEA_ATTIVA_NP
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "count":69,
  "results":[
    {
      "info":{
         "owner" : "ROOT",
         "ownerObj": {
           "email": null,
           "firstName": "ROOT",
           "disabled": false,
           "serviceUser": false,
           "groups": [
             "ROOT"
           ],
           "mobilePhone": null,
           "lastName": "ROOT",
           "username": "ROOT"
         },
         "agingObj":{
           "calendarId": 11,
           "calendarLabel": "H08-20_1-6"
           "calendarDescription": "Mon-Sat 08-20"
           "update": true,
           "seconds": 72,
           "interval": "+0 00:01",
           "lastVarDate": "2015-04-17T10:57:35.*********+02:00",
           "sla": [
             {
               "targetFrom": 900,
               "targetTo": null,
               "targetLabel": "Soglia allarme",
               "targetLevel": "DANGER",
               "InsertDate": "2018-06-29T15:27:07.*********+02:00"
             }
           ]
         },
         "currentUser" : null,
         "currentUserObj": null,
         "lastVarDate" : "2015-04-17T10:57:35.*********+02:00",
         "systemType" : "PTE_MANAGEMENT",
         "description" : "IC1",
         "properties" : [
           "SUPERVISOR",
           "FTE",
           "ESTIMATED_EFFORT_OUTLOOK"
         ],
         "status" : "OPEN",
         "creationDate" : "2015-04-17T10:57:35.*********+02:00",
         "parentId" : null,
         "dateNextStep" : "2015-04-17T10:57:35.*********+02:00",
         "type" : "EPEL_IC",
         "systemId" : 2096,
         "children" : [],
         "isLocked" : true,
         "isLockedByMyself": false,
		 "active": true,
		 "lastTransitionId": 10,
		 "lastUpdateId": 20,
		 "lastUpdateTimestamp": "2015-04-17T10:57:35.*********+02:00",
		 "version": 10,
         "creationUser": "ROOT",
         "creationUserObj": {
           "email": null,
           "firstName": "ROOT",
           "disabled": false,
           "serviceUser": false,
           "groups": [
             "ROOT"
           ],
           "mobilePhone": null,
           "lastName": "ROOT",
           "username": "ROOT"
         }
      },
      "id":6042,
      "properties":{
        "INCREMENTAL_EFFORT":null,
        "RESOURCE":"<EMAIL>",
        "RESOURCE_ENGAGE_SHORT_DESC":"short desc",
        "COMPETENCE_CENTER_MANAGER":"<EMAIL>",
        "SCOPE":"PTE_MANAGEMENT",
        "REASON":"errore",
        "SUPERVISOR":"<EMAIL>",
        "RESOURCE_ENGAGE_START_DATE":"2015-04-16T00:00:00.*********+02:00",
        "RESOURCE_ENGAGE_END_DATE":"2015-04-16T00:00:00.*********+02:00",
        "CATEGORY_LIST":"[\"pippo\",\"pluto\"]",
        "ACCOUNTED_EFFORT":"25",
        "ENGAGE_DESCRIPTION":"Preparazione documento tecnico",
        "RESOURCE_LIST":"[\"<EMAIL>\"]",
        "RESOURCE_LIST_BL":"[]",
        "ESTIMATED_EFFORT_OUTLOOK":"640",
        "RESOURCE_ENGAGE_DESC":"desc",
        "TOTAL_ESTIMATED_EFFORT":"45.5",
        "ID_EPRL":"6052",
        "ESTIMATED_EFFORT":"45.5",
        "END_DATE":"2015-05-28T22:00:00.000Z",
        "RESOURCE_REVOKE":"<EMAIL>",
        "TOTAL_ACCOUNTED_EFFORT":"25",
        "PROJECT_COMMESSA":"COMMESSA_5041",
        "START_DATE":"2015-05-03T22:00:00.000Z",
        "RESOURCE_LIST_OG":"[\"<EMAIL>\"]"
      }
    }
  ]
}

=cut

=pod
@api {get} /api/art/activities Find ids
@apiName getActivitiesIds
@apiGroup Activity
@apiDescription Find activities ids. It accepts all the parameters of API <strong>Activity - Find</strong>, plus the parameter <code>onlyids</code>.

@apiParam {Number=1,0} onlyids If <code>1</code> instead of returning an array of objects describing the activities, it returns an array of activity ids,
  if <code>0</code> has the same behaviour of API <strong>Activity - Find</strong>

@apiSuccess {Number[]} id Ids of the activities found.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/activities?onlyids=1&ap_ID_RISORSA_in=U002313254&ap_DN_equal=**********&type=ULL_LINEA_ATTIVA_NP
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
  1356,
  9281
]

=cut

=pod
@api {get} /api/art/activities Find ids extended
@apiName getActivitiesIdsExtended
@apiGroup Activity
@apiDescription Find activities ids extended. It accepts all the parameters of API <strong>Activity - Find Ids</strong>, plus the parameter <code>extendedOutput</code>.

@apiParam {Number=1,0} extendedOutput If <code>1</code> instead of returning an array of ids describing the activities, it returns an hash: the key <code>results</code> contains an array of activity ids, 
the key <code>count</code> contains the total count of the results excluding limit and skip parameters. If <code>0</code> has the same behaviour of API <strong>Activity - Find Ids</strong>
@apiParam {Number=0,1} [onlyCount] <code>0</code> returns an hash with the keys <code>count</code> e <code>results</code>, <code>1</code> returns an hash with only the key <code>count</code>.

@apiSuccess {Object[]} anonymous Object rappresenting the search
@apiSuccess {Number} anonymous.count The total count of the activities found.
@apiSuccess {Object[]} anonymous.results Ids of the activities found.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/activities?onlyids=1&extendedOutput=1&ap_DN_equal=**********&type=ULL_LINEA_ATTIVA_NP
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "count":69,
  "results":[
    "6042",
    "6043"
  ]
}

=cut

=pod
@api {get} /api/art/activities Find GridOutput
@apiName getActivitiesGridOutput
@apiGroup Activity
@apiDescription Find activities extended. It accepts all the parameters of API <strong>Activity - Find</strong>, plus the parameter <code>gridOutput</code>.

@apiParam {Number=1,0} gridOutput If <code>1</code> instead of returning an array of objects describing the activities, it returns an hash: the key <code>results</code> contains an array of activity objects, 
the key <code>count</code> contains the total count of the results excluding limit and skip parameters. If <code>0</code> has the same behaviour of API <strong>Activity - Find</strong>

NOTE: Param <code>type</code>, <code>id_equal</code> or <code>type_equal</code> is mandatory

@apiSuccess {Object[]} anonymous Object rappresenting the search
@apiSuccess {Number} anonymous.count The total count of the activities found.
@apiSuccess {Object[]} anonymous.results Objects of the activities found.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/activities?gridOutput=1&type_equal=NETWORK&ap_ID_RISORSA_in=U002313254&ap_DN_equal=**********
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "count": 1,
  "results": {
    "data": [
      {
        "[\"info\"][\"creationDate\"]": "2018-10-31T13:33:53.*********+01:00",
        "[\"properties\"][\"networkId\"]": "000290fjd909"
      }
    ],
    "header": [
      {
        "name": "DATA_CREAZIONE",
        "type": "date",
        "hidden": false,
        "checkboxFilter": false,
        "id": "[\"info\"][\"creationDate\"]"
      },
      {
        "name": "networkId",
        "type": "string",
        "hidden": false,
        "checkboxFilter": false,
        "id": "[\"properties\"][\"networkId\"]"
      }
    ]
  }
}

=cut

=pod
@api {get} /api/art/activities Find for export
@apiName getActivitiesForExport
@apiGroup Activity
@apiDescription Find activities for export. It accepts all the parameters of API <strong>Activity - Find</strong>, plus the parameter <code>export</code>.

@apiParam {String=xlsx,csv} export Set the format of the file to download

NOTE: Param <code>type</code>, <code>id_equal</code> or <code>type_equal</code> is mandatory

@apiExample {HTTP} Example usage:
GET /api/art/activities?export=xlsx&type_equal=NETWORK

@apiSuccess {File} anonymous The binary file.

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK
Content-Disposition: attachment; filename="export_20190401172524.xlsx"
Content-Length: 34114
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

=cut

sub handler_activities_get {
	
	my %input_params = @_;
	
	my %query_params = defined $input_params{QUERY_PARAMS} ? %{$input_params{QUERY_PARAMS}} : ();
	
	my $api = vars->{api_art};
	
	if (defined $query_params{export}) {
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {paramname} must be of type {type}", paramname => "export", type => "String"))
			if ref $query_params{export};
	
		# TRANSLATORS: es. Param values is a list of file extensions (eg. 'xlsx','json','csv')
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {paramname} can be {values}", paramname => "export", values => "'xlsx','csv'"))
			unless $query_params{export} =~ /^(xlsx|csv)$/;
		
		$query_params{gridOutput} = 1;
	}

	if (defined $query_params{extendedOutput}){
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {paramname} can be {values}", paramname => "extendedOutput", values => "0,1"))
			unless $query_params{extendedOutput} =~ /^(0|1)$/;
	}
	
	# checks per gridOutput 
	if ($query_params{gridOutput}) {
		# la modalità gridOutput non accetta alcuni paremtri
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "In gridOutput or export mode cannot be used extendedOutput, onlyCount, onlyids or includeTotalCount params")
			if ($query_params{extendedOutput} || $query_params{onlyCount} || $query_params{onlyids} || $query_params{includeTotalCount});
		
		# se sono in modalità gridOutput è obbligatorio passare il parametro id_equal, type o type_equal in quanto non ancora implementato
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "In gridOutput or export mode param type, id_equal or type_equal is mandatory")
			if (
				(
					! defined $query_params{type}
					||
					$query_params{type} eq ''
				)
				&&
				(
					! defined $query_params{id_equal}
					||
					$query_params{id_equal} eq ''
				)
				&&
				(
					! defined $query_params{type_equal}
					||
					$query_params{type_equal} eq ''
				)
			);
			
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "In gridOutput or export mode param type can have only one value")
			if (defined $query_params{type} && ref($query_params{type}) eq 'ARRAY' && scalar @{$query_params{type}} != 1);
	}
	
	my $ca = API::ART::Collection::Activity->new( ART => $api );
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless $ca;
	
	my %p = ();
	$p{SKIP} = $query_params{skip} if defined($query_params{skip});
	$p{LIMIT} = $query_params{limit} if defined($query_params{limit});
	
	$p{EXTENDED_OUTPUT}	= 1 if (defined $query_params{extendedOutput} && $query_params{extendedOutput} == 1)  || $query_params{gridOutput};
	
	$p{CASE_SENSITIVE}		= $query_params{caseSensitive} if defined($query_params{caseSensitive});
	
	if ($p{EXTENDED_OUTPUT}){
		$p{ONLY_COUNT}	= 1 if defined $query_params{onlyCount} && $query_params{onlyCount} == 1;
	}
	
	my $ret = $p{EXTENDED_OUTPUT} ? {} : [];
	if($query_params{onlyids}) {
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
			unless defined _normalize_activities_search_filter(OBJ => \%p, QUERY_PARAMS => \%query_params);
		if(defined $query_params{sort}) {
			$p{SORT} = _normalize_activities_sort_filter($query_params{sort}) ;
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
				unless defined $p{SORT};
		}
	    my $activities = eval{$ca->find_id( %p )};
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Uncorrect parameters", INTERNAL_ERROR_MSG => $@ || $api->last_error())
			if ( $@ || !$activities );
		if ($p{EXTENDED_OUTPUT}){
			$ret->{count} = $activities->{COUNT};
			unless ($query_params{onlyCount}) {
				$ret->{results} = $activities->{RESULTS};
			}
			if ($query_params{includeTotalCount}) {
				if ($query_params{assignedToMe} || $query_params{createdByMe}){
					# le cancello in ogni caso
					delete $p{ASSIGNED_TO_ME};
					delete $p{CREATED_BY_ME};
					$p{ONLY_COUNT} = 1;
					my $totalCount = eval{$ca->find_id( %p )};
					return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Uncorrect parameters", INTERNAL_ERROR_MSG => $@ || $api->last_error())
						if ( $@ || !$totalCount );
					$ret->{totalCount} = $totalCount->{COUNT};
				} else {
					$ret->{totalCount} = $ret->{count};
				}
			}
		} else {
			@{$ret} = map { $_ * 1 } @$activities;
		}
	} else {
		my $tmp_ret = [];

		my $use_legacy_mode = 1;

		# verifico la presenza delle chiavi parent_ per sapere se posso usare Elasticsearch:
		# se non ci sono procedo altrimenti procedo con la ricerca standard

		# per la modalità ELASTIC deve essere univo il tipo_attivita
		my $type = defined $query_params{type_equal} ? $query_params{type_equal} :
			(ref ($query_params{type}) eq 'ARRAY' && scalar @{$query_params{type}} == 1) ? $query_params{type}->[0] :
			(ref ($query_params{type}) eq 'ARRAY' && scalar @{$query_params{type}} > 1) ? undef :
			$query_params{type};

		# visto che il default è cache=1 lo imposto a 1 se non è definito o per qualsiasi valore != 0
		$query_params{cache} = 1 if (!defined $query_params{cache} || $query_params{cache} != 0);
		
		if (
			$ENV{ELASTICSEARCH_SYNC_DELTA_TIME}
			&&
			$ENV{ELASTICSEARCH_NODES}
			&&
			$ENV{ELASTICSEARCH_INSTANCE_NAME}
			&& 
			$query_params{cache}
			&&
			! defined $query_params{id_equal}
			&&
			defined $type
			&&
			!grep {$_ =~/^$parent_prefix/} @{[keys %query_params]}
			# ($query_params{gridOutput} || $query_params{onlyCount})
		) {

			my $es = _get_elasticsearch;

			ELK_SEARCH: {
				# verifico la presenza della configurazione
				# esiste chiamo ELK solo se è presente il tipo attività ed è abilitata la ricerca
				if (defined $elk_config_file){
					my @conf_elk_type = grep {$_->{name} eq $type} @{$elk_config_file};
					unless (scalar @conf_elk_type){
						debug "Activity type $type not defined in ELK";
						last ELK_SEARCH;
					}
					unless ($conf_elk_type[0]->{ws}->{search}){
						debug "ELK search disabled for activity type $type";
						last ELK_SEARCH;
					}
				}

				my $ping = eval {$es->ping()};
				if ($@){
					my $msg = "Elasticsearch error in ping ".$@; 
					warning $msg;
					last ELK_SEARCH;
				}

				my $index = sprintf 'api-art-activity-%s-%s', $ENV{ELASTICSEARCH_INSTANCE_NAME}, lc($type);

				my $elk_mappings = vars->{elk_mappings};

				unless (exists ($elk_mappings->{$index})){
					# faccio una sola volta la chiamata per i mapping
					eval{
						$elk_mappings->{$index} = $es->indices->get(
							index => $index
						)->{$index};
					};
					last ELK_SEARCH if $@;
					var elk_mappings => $elk_mappings;
				}

				return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
					unless defined _normalize_activities_search_filter_for_elastic(OBJ => \%p, QUERY_PARAMS => \%query_params, ELK_MAPPINGS => $elk_mappings->{$index});

				# verifico che ELK non sia troppo disallineato rispetto a Oracle
				my $res = $es->search(
					index => $index,
					body => {
						"size" => 1, 
						"query" => {
							"match_all" => {}
						},
						"sort" => [{"info.lastVarDate" => "desc"}],
						"_source" => "info.lastVarDate"
					}
				);

				if (scalar @{$res->{hits}->{hits}}){

					my $elk_last_var_date = $res->{hits}->{hits}->[0]->{_source}->{info}->{lastVarDate};

					my $delta_var_date = $api->_dbh()->fetch_minimalized("
						select trunc(((
								select max(data_ult_varstat)
								from v_attivita
								where nome_Tipo_attivita = ".$api->_dbh->quote($type)."
							)- to_date(to_char(to_timestamp_tz(".$api->_dbh->quote($elk_last_var_date).", ".$api->_dbh()->quote($api->get_default_iso_date_format_oracle)."),".$api->_dbh()->quote($api->get_default_date_format)."),".$api->_dbh()->quote($api->get_default_date_format)."))*60*60*24)
						from dual
					");

					if ($delta_var_date <= $ENV{ELASTICSEARCH_SYNC_DELTA_TIME}){
						$use_legacy_mode = 0; # in questo caso utilizzo ELK
						my $body_query = {
							query => $p{query},
						};

						my $res_count = $es->count(
							index => $index,
							body => $body_query
						);

						# print STDERR to_json($body_query)."\n";

						if (defined $query_params{export}) {
							# se restituisce più record del massimo previsto restituisce un errore
							if ($res_count->{count} > $ELASTICSEARCH_OBJECTS_EXPORT_LIMIT){
								return send_ko(CODE => HTTP_NOT_ACCEPTABLE, ERROR_MSG => "Max records exceeded: requested ".$res_count->{count}.", max available ".$ELASTICSEARCH_OBJECTS_EXPORT_LIMIT);
							}
							# in caso di export definisco un numero massimo di oggetti
							if (defined $p{LIMIT}) {
								$p{LIMIT} = $p{LIMIT} <= $ELASTICSEARCH_OBJECTS_EXPORT_LIMIT ? $p{LIMIT} : $ELASTICSEARCH_OBJECTS_EXPORT_LIMIT;
							} else {
								$p{LIMIT} = $ELASTICSEARCH_OBJECTS_EXPORT_LIMIT;
							}
						}

						if ($p{EXTENDED_OUTPUT}){
							$ret->{count} = $res_count->{count};
							if ($query_params{includeTotalCount}) {
								if ($query_params{assignedToMe} || $query_params{createdByMe}){
									my %p_total;
									my %query_params_total;
									for (keys %query_params){
										next if $_ =~/^(assignedToMe|createdByMe)$/; # queste chiavi non si devono considerare
										$query_params_total{$_} = $query_params{$_};
									}
									return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
										unless defined _normalize_activities_search_filter_for_elastic(OBJ => \%p_total, QUERY_PARAMS => \%query_params_total, ELK_MAPPINGS => $elk_mappings->{$index});
									my $res_count_total = $es->count(
										index => $index,
										body => {
											query => $p_total{query},
										}
									);
									$ret->{totalCount} = $res_count_total->{count};
								} else {
									$ret->{totalCount} = $ret->{count};
								}
							}
						}

						if ($res_count->{count} && ! $query_params{onlyCount}){
							my $visibility_property = $api->get_activity_property_visibility(ACTIVITY_TYPE_NAME => lc($type));
							return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
								unless defined $visibility_property;
							if ($query_params{gridOutput}){
								$body_query->{_source} = [ "id" , "info", "system" ];
							} else {
								$body_query->{_source} = [ "id" ];
								push @{$body_query->{_source}}, 'system' if ($query_params{includeSystem});
								push @{$body_query->{_source}}, 'info' unless ($query_params{excludeInfo});
							}

							if ($query_params{gridOutput} || !$query_params{excludeProperties}){
								if ($visibility_property->{ACTIVE}){
									for my $vp (@{$visibility_property->{PROPERTIES}}){
										push @{$body_query->{_source}}, "properties.".$vp->{NAME};
									}
								} else {
									push @{$body_query->{_source}}, "properties";
								}	
							}

							$body_query->{from} = $query_params{skip} if defined($query_params{skip});
							$body_query->{size} = defined($query_params{limit}) ? $query_params{limit} : ($p{LIMIT}||$res_count->{count});

							if(defined $query_params{sort}) {
								$body_query->{sort} = _normalize_activities_sort_filter_for_elastic($query_params{sort}, $elk_mappings->{$index}) ;
								return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
									unless defined $body_query->{sort};
							} else {
								$body_query->{sort} = [
									{ "id" => "asc" }
								];
							}

							#use Data::Dumper;
							#print STDERR to_json($body_query)."\n";

							# print STDERR "cerco con ELK\n";
							my $res = $es->search(
								index => $index,
								body => $body_query
							);
							
							my $cache_system_property;
							# gestico i campi che devono essere modificati
							# - le system properties array perche' in ELK sono memorizzate in una nuova chiave dedicata propertiesArray
							# - la chiave availableActions viene calcolata direttamente in tempo reale quando necessario
							if (
								(grep {$_ eq 'system'}@{$body_query->{_source}})
								||
								($query_params{includeAvailableActions} && grep {$_ eq 'id'}@{$body_query->{_source}})
							){
								for my $actSource (@{$res->{hits}->{hits}}){
									my $act = $actSource->{_source};
									if (grep {$_ eq 'system'}@{$body_query->{_source}}){
										if (exists ($act->{system}->{propertiesArray})){
											for my $propKey (keys %{$act->{system}->{propertiesArray}}){
												$act->{system}->{properties}->{$propKey} = $act->{system}->{propertiesArray}->{$propKey};
											}
										}
										delete $act->{system}->{propertiesArray};
										if (!exists $cache_system_property->{$act->{system}->{info}->{type}}){
											$cache_system_property->{$act->{system}->{info}->{type}} = $api->get_system_property_visibility(SYSTEM_TYPE_NAME => $act->{system}->{info}->{type});
										}
										if ($cache_system_property->{$act->{system}->{info}->{type}}->{ACTIVE}){
											for my $sp (keys %{$act->{system}->{properties}}){
												unless (grep {$_->{NAME} eq $sp}@{$cache_system_property->{$act->{system}->{info}->{type}}->{PROPERTIES}}){
													delete $act->{system}->{properties}->{$sp};
												}
											}
										}
									}
									if ($query_params{includeAvailableActions} && grep {$_ eq 'id'}@{$body_query->{_source}}){
										my $actObj = API::ART::Activity::Factory->new(ART => $api, ID => $act->{id});
										my $dumpAct = $actObj->dump(
											EXCLUDE_INFO => 1,
											SYSTEM => {EXCLUDE_ALL => 1},
											EXCLUDE_PROPERTIES => 1,
											EXCLUDE_HISTORY => 1,
											EXCLUDE_AVAILABLE_ACTIONS => 0
										);
										#use Data::Dumper;
										#print STDERR Dumper $dumpAct;
										$act->{availableActions} = $dumpAct->{availableActions};
									}
								}
							}

							$tmp_ret = [map {$_->{_source}} @{$res->{hits}->{hits}}];
						}
					} else {
						my $msg = "[$type] Exceeded ELASTICSEARCH_SYNC_DELTA_TIME => expected less than ".$ENV{ELASTICSEARCH_SYNC_DELTA_TIME}.", found ".$delta_var_date; 
						warning $msg;
						if (defined $query_params{export}) {
							return send_ko(CODE => HTTP_SERVICE_UNAVAILABLE, ERROR_MSG => "Please retry later", INTERNAL_ERROR_MSG => $msg)
						}
						last ELK_SEARCH;
					}
				}
			}
		}
		
		if ($use_legacy_mode){
			if (defined $query_params{export}) {
				# in caso di export definisco un numero massimo di oggetti
				if (defined $p{LIMIT}) {
					$p{LIMIT} = $p{LIMIT} <= $OBJECTS_EXPORT_LIMIT ? $p{LIMIT} : $OBJECTS_EXPORT_LIMIT;
				} else {
					$p{LIMIT} = $OBJECTS_EXPORT_LIMIT;
				}
			}
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
				unless defined _normalize_activities_search_filter(OBJ => \%p, QUERY_PARAMS => \%query_params);

			if(defined $query_params{sort}) {
				$p{SORT} = _normalize_activities_sort_filter($query_params{sort}) ;
				return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
					unless defined $p{SORT};
			}

			my $activities = eval{$ca->find_object( %p )};
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Uncorrect parameters", INTERNAL_ERROR_MSG => $@ || $api->last_error())
				if ( $@ || !$activities );
			
			my $results;
			
			if ($p{EXTENDED_OUTPUT}){
				if (defined $query_params{export} && $activities->{COUNT} > $OBJECTS_EXPORT_LIMIT) {
					return send_ko(CODE => HTTP_NOT_ACCEPTABLE, ERROR_MSG => "Max records exceeded: requested ".$activities->{COUNT}.", max available ".$OBJECTS_EXPORT_LIMIT);
				}
				$ret->{count} = $activities->{COUNT};
				unless ($query_params{onlyCount}) {
					$results = $activities->{RESULTS};
				}
				if ($query_params{includeTotalCount}) {
					if ($query_params{assignedToMe} || $query_params{createdByMe}){
						# le cancello in ogni caso
						delete $p{ASSIGNED_TO_ME};
						delete $p{CREATED_BY_ME};
						$p{ONLY_COUNT} = 1;
						my $totalCount = eval{$ca->find_object( %p )};
						return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Uncorrect parameters", INTERNAL_ERROR_MSG => $@ || $api->last_error())
							if ( $@ || !$totalCount );
						$ret->{totalCount} = $totalCount->{COUNT};
					} else {
						$ret->{totalCount} = $ret->{count};
					}
				}
			} else {
				$results = $activities;
			}
			
			for my $act ( @$results ) {
				unless ($act) {
					push @{$tmp_ret}, {};
					next;	
				};
				
				my %p = ();
				$p{EXCLUDE_INFO} = $query_params{excludeInfo} ? 1 : 0;
				$p{EXCLUDE_PROPERTIES} = $query_params{excludeProperties} ? 1 : 0;
				$p{SYSTEM}->{EXCLUDE_ALL} = $query_params{includeSystem} ? 0 : 1;
				$p{EXCLUDE_AVAILABLE_ACTIONS} = $query_params{includeAvailableActions} ? 0 : 1;
				my $actdump = $act->dump(%p);
				return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
					unless defined $actdump;
			
				push @{$tmp_ret}, $actdump;
			}
		}
		
		if ($p{EXTENDED_OUTPUT}){
			unless ($query_params{onlyCount}) {
				if ($query_params{gridOutput}){
					
					# la modalità gridOutput accetta type_equal, type o id_equal
					# se ho passatto id_equal recupero il tipo dall'unica attività restituita se non ha risultati da errore
					my $gridOutputActivityType;
					if ($query_params{type_equal}){
						$gridOutputActivityType = $query_params{type_equal};
					} elsif($query_params{type}){
						$gridOutputActivityType = ref($query_params{type}) eq 'ARRAY' ? $query_params{type}->[0] : $query_params{type};
					} else {
						$gridOutputActivityType = $tmp_ret->[0]->{info}->{type} if scalar @{$tmp_ret};
					}
					
					if (defined $gridOutputActivityType) {
						# recupero la configurazione
						my $header = $api->get_entity_serialization(FORMAT => (defined $query_params{export} ? uc($query_params{export}) : 'JSON'));
						return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
							unless defined $header;
						
						# se non la trova scala su quella di default
						if (! exists $header->{ACTIVITIES}->{$gridOutputActivityType} ){
							my $activity_type_id = $api->get_activity_type_id($gridOutputActivityType);
							return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Invalid activity type name {type}", type => $gridOutputActivityType))
								unless defined $activity_type_id;
							my $query = '
								select regexp_replace(regexp_replace(t.percorso,\'\["\',\'\'),\'"\]\',\'_\')||t.posizione "NAME"
									, t.etichetta HEADER
									, t.etichetta JSON_DESCRIPTION
									, t.tipo TYPE
									, t.percorso PATH
									, t.nascosto JSON_HIDDEN
									, t.posizione POSITION
									, null JSON_EXCLUDE
								from INTESTAZIONI_DASHBOARDS t
								where t.tipo_oggetto = \'ATTIVITA\'
								and t.id_oggetto = '.$activity_type_id.'
								order by t.posizione';
							
							# sovascrivo header tanto non mi serve
							$header = eval{$api->_dbh()->fetchall_hashref($query)};
							for my $h (@{$header}){
								$h->{POSITION} = $h->{POSITION}*1;
								for ('JSON_HIDDEN', 'JSON_EXCLUDE'){
									$h->{$_} = $h->{$_} ? $JSON::true : $JSON::false;
								}
							}
						} else {
							# sovascrivo header tanto non mi serve
							$header = $header->{ACTIVITIES}->{$gridOutputActivityType};
						}
						
						# i sistemi trovati in modalità gridOutput sono omogenei basta vedere il primo
						return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Configuration not found for activity type ".$gridOutputActivityType)
							unless (scalar @{$header});
						
						my $reports = SIRTI::Reports->instance(DB => $api->_dbh());
						
						my $report_params = {
							 DATA => $tmp_ret
							,COLUMNS => $header
						};
						
						if ($query_params{export}) {
							$report_params->{OUTPUT_FORMAT} = $query_params{export};
						} else { # json
							$report_params->{OUTPUT_FORMAT} = 'json';
							$report_params->{JSON_RETURN_STRING} = 1;
							$report_params->{JSON_USE_ID_INSTEAD_OF_NAME} = 1;
						}
						
						my $report = $reports->report(%$report_params);
						
						unless (defined $report) {
							my $error = $reports->last_error();
							# distruggiamo l'oggetto singleton per evitare che se ci sono dei cursori aperti al suo interno, non permettano la disconnessione dal db
							undef $reports;
							return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $error);
						}
								
						# distruggiamo l'oggetto singleton per evitare che se ci sono dei cursori aperti al suo interno, non permettano la disconnessione dal db
						undef $reports;
						if ($query_params{export}) {
							# gestisco il download del file e return send_file_ok
							remove_in_hook_after( $report );
							return send_file_ok($report, system_path => 1, filename => 'export_'.$api->get_sysdate('YYYYMMDDHH24MISS').'.'.$query_params{export});
						} else {
							$ret->{results} = from_json($report);
						}
					} else {
						$ret->{results} = {
							data => [],
							header => []
						};
					}
				} else {
					$ret->{results} = $tmp_ret;	
				}
			}
		} else {
			$ret = $tmp_ret;
		}
	}
	
	return send_ok(MSG => $ret);

}

get '/activities' => sub {
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return handler_activities_get(QUERY_PARAMS => \%query_params);
};

=pod
@api {post} /api/art/activities Create
@apiName createActivity
@apiGroup Activity
@apiDescription Create activity

@apiUse ContentTypeJsonHeader

@apiExample {HTTP} Example usage:
POST /api/art/activities HTTP/1.1
Content-Type: application/json
Accept: application/json

{
  "type" : "EPEL_IC",
  "description" : "IC1",
  "systemId": 2104, 
  "properties" : {
    "SUPERVISOR" : "<EMAIL>",
    "IC_NUMBER" : "75884"
  }
}

@apiParam {String} type Type of the activity to create.
@apiParam {String} description Description of the activity.

@apiParam {Number} [systemId] Id of the system to create the activity on. If you don't specify this param, you must
  use <code>system</code> param.

@apiParam {Object} [system] New system where the activity will be created on. If you don't specify this param, you must
  use <code>systemId</code> param.
@apiParam {String} system.type Type of the system to create.
@apiParam {String} system.objectType Object Type of the system to create.
@apiParam {String} system.description Description of the system.
@apiParam {String[]} system.groups Groups that must have visibility on the just created system.
@apiParam {Object} [system.properties] An object representing the properties, where the keys are the NAMEs of the properties.
@apiParam {String} system.properties.NAME The name of the property.
@apiParam {String} [system.date=Now()] Creation date - *** NYI ***
@apiUse DateAndFormat
@apiUse TransitionProperties
@apiParam {String[]} [attachments] Id of temporary file to attach to the activity. The file must have been uploaded with
  <code>POST /api/art/tmpfiles</code> previously.

@apiSuccess {Number} id Id of the activity created.

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "id" : 31722454
}

=cut

post '/activities' => sub {

    my $api = vars->{api_art};
    
    my %body = request->params('body');
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
		unless (keys %body);
    
    
	my $ca = API::ART::Collection::Activity->new( ART => $api );
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless $ca;
	
	my %p = (
		 ACTIVITY_TYPE_NAME		=> $body{type}
		,DESCRIPTION			=> $body{description}
	);
	
	$p{SYSTEM_ID}		= $body{systemId} if defined($body{systemId});
	
	if (defined $body{system}){
		$p{SYSTEM} = {
			 SYSTEM_TYPE_NAME		=> $body{system}{type}
			,OBJECT_TYPE_NAME		=> $body{system}{objectType}
			,DESCRIPTION			=> $body{system}{description}
			,GROUPS					=> $body{system}{groups}
			,SYSTEM_CATEGORY_ID		=> 1
	        ,SYSTEM_CLASS_ID		=> 1
		};
		$p{SYSTEM}{PROPERTIES} = $body{system}{properties} if defined($body{system}{properties});
		# FIXME: riabilitare quando fatta la patch alle API::ART
		# $p{SYSTEM}{DATE} = $body{system}{date} if defined($body{system}{date});
	}
	
	# FIXME: riabilitare quando fatta la patch alle API::ART
	# $p{DATE}			= $body{date} if defined($body{date});
	
	$p{DEST_USER}		= $body{destUser} if defined($body{destUser});
	$p{ID_CUSTOM}		= $body{idCustom} if defined($body{idCustom}); # NB: undocumented
	
	$p{PROPERTIES} = $body{properties} if defined($body{properties});
	
	$p{ATTACHMENTS}	= [];
	if (defined $body{attachments}){
		return send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => "attachment param must be an array of at least one element")
			if (ref ($body{attachments}) ne 'ARRAY');
		
		foreach my $att (@{$body{attachments}}) {
			my $filename = $ENV{ART_REPOSITORY_TMP_TMPFILES}."/".get_sid()."_".$att;
	
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Not existent attachment", INTERNAL_ERROR_MSG => "Not existent attachment: $filename")
				unless -f $filename && -f $filename.".idx";
			
			return send_ko(INTERNAL_ERROR_MSG => "Unable to open index file ".$filename.".idx" . ": " . $!)
				unless (open FD, $filename.".idx");

			binmode FD, ':utf8';
			
			my $idxjson = '';
			while(<FD>) {
				$idxjson .= $_;	
			}
			
			my $idx = eval{ from_json($idxjson) };
			return send_ko(INTERNAL_ERROR_MSG => "Unable to parse JSON idxfile ".$filename.".idx" . ": " . $@)
				if ($@);
			
			return send_ko(INTERNAL_ERROR_MSG => "Unable to copy file from ".$filename." to " . $ENV{ART_REPOSITORY_TMP_TMPFILES}."/".$idx->{filename} . ": " . $!)
				unless (copy($filename,$ENV{ART_REPOSITORY_TMP_TMPFILES}."/".$idx->{filename}));
			
			push @{$p{ATTACHMENTS}}, $ENV{ART_REPOSITORY_TMP_TMPFILES}."/".$idx->{filename};
		}
	}

	my $activity = $ca->create( %p	);
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
		unless ( $activity );
	
	for my $f ( @{$p{ATTACHMENTS}} ) {
		unlink($f);
	}
	
	my $activity_id = $activity->id();
	
	do_api_save( $api );
	
	return send_ok(MSG => { id => $activity_id * 1 });
	
};

######## ROUTE /activities/:ID ###########

options '/activities/:ID' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };

=pod
@api {get} /activities/:ID Get
@apiName getActivity
@apiGroup Activity
@apiDescription Returns an object describing the activity.

@apiParam {Number=0,1} [includeSystem=0] Include complete information about system.
@apiParam {Number=0,1} [includeAvailableActions=0] Include the available actions for current status and user profile.
@apiParam {Number=0,1} [excludeInfo=0] Exclude activity info.
@apiParam {Number=0,1} [excludeProperties=0] Exclude activity properties.

@apiSuccess {Number} id The id of the activity found.
@apiSuccess {Object} info The info of the activity found in the same format returned by API <strong>Activity - Info</strong> <code>GET /api/art/activities/:ID/info</code>. Available only if param <code>excludeInfo=0</code> (default).
@apiSuccess {Object} properties The properties of the activity found in the same format returned by API <strong>Activity - Properties</strong> <code>GET /api/art/activities/:ID/properties</code>. Available only if param <code>excludeProperties=0</code> (default).
@apiSuccess {Object} system The system object in the same format returned by API <strong>System - Find</strong> <code>GET /api/art/systems</code>. Available only if param <code>includeSystem=1</code>  
@apiSuccess {Object} availableActions An array with the available actions for current status and user profile. Available only if param <code>includeAvailableActions=1</code>  

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/activities/1234
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "info": {
     "owner" : "ROOT",
     "ownerObj": {
       "email": null,
       "firstName": "ROOT",
       "disabled": false,
       "serviceUser": false,
       "groups": [
         "ROOT"
       ],
       "mobilePhone": null,
       "lastName": "ROOT",
       "username": "ROOT"
     },
     "currentUser" : null,
     "currentUserObj": null,
     "lastVarDate" : "2015-04-17T10:57:35.*********+02:00",
     "systemType" : "PTE_MANAGEMENT",
     "description" : "IC1",
     "properties" : [
       "SUPERVISOR",
       "FTE",
       "ESTIMATED_EFFORT_OUTLOOK"
     ],
     "attachments": [],
     "status" : "OPEN",
     "creationDate" : "2015-04-17T10:57:35.*********+02:00",
     "parentId" : null,
     "dateNextStep" : "2015-04-17T10:57:35.*********+02:00",
     "type" : "EPEL_IC",
     "systemId" : 2096,
     "children" : [],
     "isLocked" : true,
     "isLockedByMyself": false,
	 "active": true,
     "lastTransitionId": 10,
     "lastUpdateId": 20,
     "lastUpdateTimestamp": "2015-04-17T10:57:35.*********+02:00",
     "version": 10,
     "creationUser": "ROOT",
     "creationUserObj": {
       "email": null,
       "firstName": "ROOT",
       "disabled": false,
       "serviceUser": false,
       "groups": [
         "ROOT"
       ],
       "mobilePhone": null,
       "lastName": "ROOT",
       "username": "ROOT"
     }
  },
  "system": {
    "info": {
      "disabled": false,
      "disablingDate": null,
      "endingDate": null,
      "active": true,
      "description": "**********",
      "properties": [
        "CUSTOMER",
        "IC_DESCRIPTION",
        "INITIATIVE_NUMBER",
        "PROJECT_COMMESSA"
      ],
      "ended": false,
      "creationDate": "2015-06-03T08:33:00.*********+02:00",
      "groups": [
        "ADMIN",
        "EPEL_MANAGER",
        "GUEST",
        "ROOT",
        "SUPERVISOR_AD-IIN"
      ],
      "type": "PTE_MANAGEMENT"
    },
    "id": 3165,
    "properties": {
      "INITIATIVE_NUMBER": null,
      "PROJECT_COMMESSA": null,
      "CUSTOMER": null,
      "IC_DESCRIPTION": null
    }
  },
  "id": 6505,
  "properties": {
    "FLAG_ATI": null,
    "CC": null,
    "ID_EPCCL": null,
    "IC_TECHNICAL_COORDINATOR": "<EMAIL>",
    "INCREMENTAL_EFFORT": null,
    "IC_TYPE": null,
    "OUTCOME_QUARTER": null,
    "TRANCHE": null,
    "TRANCHE_DESCRIPTION": null,
    "CIG": null,
    "FINAL_CUSTOMER": null,
    "3RDP": null,
    "COMPANY": null,
    "BID_DEADLINE_PRESENTATION": null,
    "INCREMENTAL_ACCOUNTED_EFFORT": null,
    "CUSTOMER": "myself",
    "IC_TECHNICAL_COORDINATOR_NAME": "BELOTTI, RIZZARDO",
    "PROJECT_COMMESSA_LIST": [
      "76484 - ATTIVITÀ PROPOSITIVA PER OP E GC CS (12647)",
      "76486 - ATTIVITÀ PROPOSITIVA PER RFI E ITALFERR (12649)",
      "59123 - A2M:MANUT.FIL.LECCE (12393)"
    ],
    "ID_EPCCL_3RDP_REVOKE": null,
    "REASON": null,
    "CC_SYSTEM_ID": null,
    "SUPERVISOR": "<EMAIL>",
    "SUPERVISOR_NAME": "BELOTTI, RIZZARDO",
    "INCREMENTAL_ESTIMATED_EFFORT": null,
    "IC_DESCRIPTION": "test mail technical coordinator",
    "COMPETENCE_CENTER_LIST": [
      "CC-TEST1"
    ],
    "3RDP_EMAIL": null,
    "DEPARTMENT": null,
    "IC_NUMBER": "**********",
    "ID_EPCCL_3RDP": null,
    "CATEGORY_LIST": [
      "Illuminazione",
      "Impianti Elettrici",
      "IMPIANTI SPECIALI"
    ],
    "ACCOUNTED_EFFORT": null,
    "ENGAGE_DESCRIPTION": null,
    "ESTIMATED_EFFORT_OUTLOOK": null,
    "3RDP_LIST": [
      "pippo"
    ],
    "CUP": null,
    "ID_EPCCL_REVOKE": null,
    "ESTIMATED_EFFORT": null,
    "TOTAL_ESTIMATED_EFFORT": null,
    "FLAG_SECRET": null,
    "IC_PARENT": null,
    "IC_OUTCOME_PERIOD": null,
    "END_DATE": null,
    "IC_AGGREGATED_STATUS": null,
    "IC_SALES_FORCE_LINK": null,
    "COMPETENCE_CENTER": null,
    "IC_STATUS": null,
    "TOTAL_ACCOUNTED_EFFORT": null,
    "IC_RECORD_TYPE": null,
    "PROJECT_COMMESSA": null,
    "EXECUTIVE_MANAGEMENT": null,
    "START_DATE": null,
    "PROJECT_COMMESSA_SYSTEM_ID": null,
    "TOTAL_ESTIMATED_EFFORT_OUTLOOK": null,
    "IC_OWNER": null,
    "INCREMENTAL_EST_EFF_OUTLOOK": null
  }
}

=cut

sub handler_activities_ID_get {
	
	my %input_params = @_;
	
	my %query_params = defined $input_params{QUERY_PARAMS} ? %{$input_params{QUERY_PARAMS}} : ();
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	my $api = vars->{api_art};
	
	my $activity = eval { API::ART::Activity::Factory->new( ART => $api, ID => $uri_params{ID} ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("Activity ID not found"), INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$activity;
	
	my %p = ();
	$p{EXCLUDE_INFO} = $query_params{excludeInfo} ? 1 : 0;
	$p{EXCLUDE_PROPERTIES} = $query_params{excludeProperties} ? 1 : 0;
	$p{SYSTEM}->{EXCLUDE_ALL} = $query_params{includeSystem} ? 0 : 1;
	$p{EXCLUDE_AVAILABLE_ACTIONS} = $query_params{includeAvailableActions} ? 0 : 1;
	my $ret = $activity->dump(%p);
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless defined $ret;

	return send_ok(MSG => $ret);
}

get '/activities/:ID' => sub {
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return handler_activities_ID_get(QUERY_PARAMS => \%query_params, URI_PARAMS => \%{params('route')});
	
};

######## ROUTE /activities/:ID/history ###########

options '/activities/:ID/history' => sub { handle_cors_request( METHODS => [ 'POST', 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {post} /api/art/activities/:ID/history Step
@apiName stepActivity
@apiGroup Activity
@apiDescription Step activity

@apiUse ContentTypeJsonHeader

@apiExample {HTTP} Example usage:
POST /api/art/activities/5711/history HTTP/1.1
Content-Type: application/json

{
  "action" : "INITIALIZE",
  "description" : "step!",
  "destUser": "root", 
  "properties" : {
    "SUPERVISOR" : "<EMAIL>",
    "IC_NUMBER" : "75884"
  }
}

@apiParam {String} action Name of the action.
@apiParam {String} [description=Azione eseguita con 'NOME_SCRIPT'] Description of the step.
@apiUse DateAndFormat
@apiParam {String} [destUser] The username of the user to whom the activity must be assigned.
@apiParam {Number=0,1} [assignNotification=0] If <code>1</code> the user to whom the activity must be assigned will receive a notification email.
@apiUse TransitionProperties
@apiParam {String[]} [attachments] Id of temporary file to attach to the activity. The file must have been uploaded with
  <code>POST /api/art/tmpfiles</code> previously.
@apiParam {Number=0,1} [saveAllProperties=1] If <code>0</code> the properties that reamain unchanged after the step aren't saved.
@apiParam {Number=0,1} [ignoreFinalStatus=0] If <code>1</code> the checks about the possibility to close the activity are skipped.
@apiParam {Number=0,1} [virtual=0] If <code>1</code> execute a virtual action.
@apiParam {String} [destStatus] The destination status of the action: ignored if the action is not a virtual action.

@apiSuccessExample Success-Response:
HTTP/1.1 204 No content

=cut

sub handler_activities_ID_history_post {
	
	my %input_params = @_;
	
	my %body = defined $input_params{BODY} ? %{$input_params{BODY}} : ();
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
		unless (keys %body);
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	my $api = vars->{api_art};

	my $activity = eval { API::ART::Activity::Factory->new( ART => $api, ID => $uri_params{'ID'} ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("Activity ID not found"), INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$activity;

	my %p = (
		 ACTION	=> $body{action}
	);
	
	$p{DESCRIPTION}		= $body{description} if defined($body{description});
	# FIXME: riabilitare quando fatta la patch alle API::ART
	# $p{DATE}			= $body{date} if defined($body{date});
	
	$p{DEST_USER}		= $body{destUser} if defined($body{destUser});
	
	my %boolean_params = (
		 "saveAllProperties" => "SAVE_ALL_PROPERTIES"
		,"ignoreFinalStatus" => "IGNORE_FINAL_STATUS"
		,"assignNotification" => "ASSIGN_NOTIFICATION"
		,"virtual" => "VIRTUAL"
	);
	while ( my ($key, $value) = each %boolean_params ) {
		if(defined $body{$key}) {
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Bad value for {paramname} param", paramname => $key))
				unless $body{$key} =~ /^(0|1)$/;
			$p{$value} = $body{$key};
		}
	}
	
	if(defined $p{VIRTUAL}) {
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("destStatus must be defined for virtual actions"))
			unless defined($body{destStatus});
		$p{DEST_STATUS} = $body{destStatus};
	}
	
	$p{PROPERTIES}		= $body{properties} if defined($body{properties});

	$p{ATTACHMENTS}	= [];
	if (defined $body{attachments}){
		return send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => "attachment param must be an array")
			if (ref ($body{attachments}) ne 'ARRAY');
		
		foreach my $att (@{$body{attachments}}) {
			my $filename = $ENV{ART_REPOSITORY_TMP_TMPFILES}."/".get_sid()."_".$att;
	
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Not existent attachment", INTERNAL_ERROR_MSG => "Not existent attachment: $filename")
				unless -f $filename && -f $filename.".idx";
			
			return send_ko(INTERNAL_ERROR_MSG => "Unable to open index file ".$filename.".idx" . ": " . $!)
				unless (open FD, $filename.".idx");
			
			binmode FD, ':utf8';

			my $idxjson = '';
			while(<FD>) {
				$idxjson .= $_;	
			}
			
			my $idx = eval{ from_json($idxjson) };
			return send_ko(INTERNAL_ERROR_MSG => "Unable to parse JSON idxfile ".$filename.".idx" . ": " . $@)
				if ($@);
			
			return send_ko(INTERNAL_ERROR_MSG => "Unable to copy file from ".$filename." to " . $ENV{ART_REPOSITORY_TMP_TMPFILES}."/".$idx->{filename} . ": " . $!)
				unless (copy($filename,$ENV{ART_REPOSITORY_TMP_TMPFILES}."/".$idx->{filename}));
			
			my $attach_obj = {
				FILENAME => $ENV{ART_REPOSITORY_TMP_TMPFILES}."/".$idx->{filename}
			};
			$attach_obj->{'TITLE'} = $idx->{title} if exists $idx->{title};
			$attach_obj->{'DESCRIPTION'} = $idx->{description} if exists $idx->{description};
			$attach_obj->{'REVISION'} = $idx->{revision} if exists $idx->{revision};
			$attach_obj->{'REF_DATE'} = $idx->{refDate} if (exists $idx->{refDate});
			$attach_obj->{'DOC_TYPE'} = $idx->{docType} if (exists $idx->{docType});
			
			push @{$p{ATTACHMENTS}}, $attach_obj;
		}
	}

	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
		unless $activity->step(%p);
	
	for my $f ( @{$p{ATTACHMENTS}} ) {
		unlink($f);	
	}
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_NO_CONTENT);
}

post '/activities/:ID/history' => sub {
	
	my %body = request->params('body');
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
		unless (keys %body);
	
	return handler_activities_ID_history_post(BODY => \%body, URI_PARAMS => \%{params('route')});
};

=pod
@api {get} /api/art/activities/:ID/history History
@apiName activityHistory
@apiGroup Activity
@apiDescription Returns activity history

@apiParam {Number=0,1} [mergeChildren=0] If <code>1</code> will be returned also objects representing child activities.

@apiSuccess {Object[]} anonymous Object rappresenting the step or the child activity.
@apiSuccess {String=TRANSITION,ACTIVITY} anonymous.objType Whether the object represent a transition or a child activity.

@apiSuccess {Number} anonymous.id the Id of the transition or of the child activity.
@apiSuccess {String} anonymous.description Description of the transition or of the child activity.
@apiSuccess {String} [anonymous.transitionDate] If <code>objType=TRANSITION</code>, the date of the transition.
@apiSuccess {String} [anonymous.fromStatus] If <code>objType=TRANSITION</code>, beginning status of the transition.
@apiSuccess {String} [anonymous.action] If <code>objType=TRANSITION</code>, the action done in the transition.
@apiSuccess {String} [anonymous.username] If <code>objType=TRANSITION</code>, the user performing activity transition.
@apiSuccess {Object} [anonymous.usernameObj] If <code>objType=TRANSITION</code>, object rappresenting the user performing activity transition in the same format returned by API <strong>Activity - Get User Info</strong> <code>GET /api/art/instance/users</code>.
@apiSuccess {String} [anonymous.toStatus] If <code>objType=TRANSITION</code>, ending status of the transition.
@apiSuccess {String=PIC,DEPIC,DEPIC_NO_PERMESSI,ASSEGNAZIONE,RIASSEGNAZIONE,ASSEGNAZIONE_VIRTUALE,DEASSEGNAZIONE_VIRTUALE,RIASSEGNAZIONE_VIRTUALE} [anonymous.currentUserChangeReason] If <code>objType=TRANSITION</code>, if not null the reason why the current user is changed.
@apiSuccess {String} [anonymous.currentUsername] If <code>objType=TRANSITION</code>, defined only if <code>currentUserChangeReason</code> is not null, is the user in charge of the activity.
@apiSuccess {Object} [anonymous.currentUsernameObj] If <code>objType=TRANSITION</code>, defined only if <code>currentUserChangeReason</code> is not null, is the object rappresenting the user in charge of the activity in the same format returned by API <strong>Activity - Get User Info</strong> <code>GET /api/art/instance/users</code>.
@apiSuccess {Various} [anonymous.properties] <ul><li>If <code>objType=TRANSITION</code>, <code>Object[]</code> rappresenting the properties of the step</li><li>If <code>objType=ACTIVITY</code>, <code>String[]</code> representing the property types related to the activity.</ul>
@apiSuccess {String} anonymous.properties.name If <code>objType=TRANSITION</code>, the name of the property.
@apiSuccess {String} anonymous.properties.value If <code>objType=TRANSITION</code>, the value of the property.
@apiSuccess {Number} anonymous.properties.order If <code>objType=TRANSITION</code>, the order of the property. <code>null</code> if the property isn't still used by the action.
@apiSuccess {Boolean} anonymous.properties.validity If <code>objType=TRANSITION</code>, <code>true</code> if the property is still used by the action, <code>false</code> otherwise. 
@apiSuccess {Object[]} [anonymous.attachments] If <code>objType=TRANSITION</code>, the list of the attachments in the same format returned by API <strong>Activity - Attachment list</strong> <code>GET /api/art/activities/:ID/attachments</code>.
@apiSuccess {String} [anonymous.type] If <code>objType=ACTIVITY</code>, the activity type.
@apiSuccess {Number} [anonymous.systemId] If <code>objType=ACTIVITY</code>, the id of the system to whom the activity is associated.
@apiSuccess {String} [anonymous.systemType] If <code>objType=ACTIVITY</code>, he type of the system to whom the activity is associated.
@apiSuccess {String} [anonymous.status] If <code>objType=ACTIVITY</code>, he current status of the activity.
@apiSuccess {String} [anonymous.owner] If <code>objType=ACTIVITY</code>, he username of the user that created the activity.
@apiSuccess {Object} [anonymous.ownerObj] If <code>objType=ACTIVITY</code>, the object of the user in the same format returned by API <strong>Activity - Get User Info</strong> <code>GET /api/art/instance/users</code>.
@apiSuccess {String} [anonymous.currentUser] If <code>objType=ACTIVITY</code>, he username of the user to whom the activity is assigned if any, <code>null</code> otherwhise.
@apiSuccess {Object} [anonymous.currentUserObj] If <code>objType=ACTIVITY</code>, the object of the user in the same format returned by API <strong>Activity - Get User Info</strong> <code>GET /api/art/instance/users</code>.
@apiSuccess {String} [anonymous.creationUser] If <code>objType=ACTIVITY</code>, he username of the user that created the activity
@apiSuccess {Object} [anonymous.creationUserObj] If <code>objType=ACTIVITY</code>, the object of the user that created the activity in the same format returned by API <strong>Activity - Get User Info</strong> <code>GET /api/art/instance/users</code>.
@apiSuccess {String} [anonymous.creationDate] If <code>objType=ACTIVITY</code>, he date when the activity has been created.
@apiSuccess {String} [anonymous.lastVarDate] If <code>objType=ACTIVITY</code>, he date of the last variation of the activity.
@apiSuccess {Number} [anonymous.parentId] If <code>objType=ACTIVITY</code>, he id of the activity parent if any, <code>null</code> otherwhise.
@apiSuccess {String} [anonymous.dateNextStep] If <code>objType=ACTIVITY</code>, he minimum date available for the next step.
@apiSuccess {String[]} [anonymous.properties] If <code>objType=ACTIVITY</code>, the property types related to the activity.
@apiSuccess {Number[]} [anonymous.children] If <code>objType=ACTIVITY</code>, the children id list.
@apiSuccess {Boolean} [anonymous.isLocked] If <code>objType=ACTIVITY</code>, <code>true</code> if the activity is locked, <code>false</code> otherwise.
@apiSuccess {Boolean} [anonymous.isLockedByMyself] If <code>objType=ACTIVITY</code>, <code>true</code> if the activity is locked by current session, <code>false</code> otherwise.
@apiSuccess {Number} [anonymous.lastTransitionId] If <code>objType=ACTIVITY</code>, it is the id of the last transition of the activity
@apiSuccess {Number} [anonymous.lastUpdateId] If <code>objType=ACTIVITY</code>, it is the id of the last update of the activity
@apiSuccess {String} [anonymous.lastUpdateTimestamp] If <code>objType=ACTIVITY</code>, it is the timestamp of the last update of the activity
@apiSuccess {Number} [anonymous.version] If <code>objType=ACTIVITY</code>, it is the version number of the activity
@apiSuccess {Object} [anonymous.systemObj] If <code>objType=ACTIVITY</code>, the object of the info of the system to whom the activity is associated in the same format returned by API <strong>System - Info</strong> <code>GET /api/art/systems/:ID/info</code> plus the key <code>id</code>.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/activities/5704/history?mergeChildren=1
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
  {
    "objType":"TRANSITION",
    "id":20150428112218,
    "attachments":[
      {
        "owner":"ROOT",
        "sequence":0,
        "fileName":"master_preferences",
        "transitionDate":"2019-01-08T12:23:18.*********+01:00",
        "transitionId":20190108122318,
        "size":75,
        "description":null,
        "title": null,
        "revision": null,
        "refDate": null,
        "docType": null,
        "downloadCount":null,
        "ownerFirstName":"ROOT",
        "ownerLastName":"ROOT"
      }
    ],
    "fromStatus":"START",
    "currentUsername":null,
    "currentUsernameObj":null,
    "transitionDate":"2015-04-28T11:22:18.*********+02:00",
    "description":"Apertura Attivita",
    "toStatus":"OPEN",
    "username":"BIDMANAGER",
    "usernameObj": {
      "email": null,
      "firstName": "ROOT",
      "disabled": false,
      "serviceUser": false,
      "groups": [
        "ROOT"
      ],
      "mobilePhone": null,
      "lastName": "ROOT",
      "username": "ROOT"
    },
    "properties":[
      {
        "validity":true,
        "order":0,
        "value":"IC201504281101",
        "name":"IC_NUMBER"
      },
      {
        "validity":true,
        "order":1,
        "value":"fai tu!",
        "name":"ENGAGE_DESCRIPTION"
      },
      {
        "validity":true,
        "order":5,
        "value":"PROJECT_COMMESSA_1",
        "name":"PROJECT_COMMESSA"
      }
    ],
    "action":"OPEN"
  },
  {
    "objType": "ACTIVITY",
    "id": 6247,
    "isLockedByMyself": false,
    "lastTransitionId": 10,
    "lastUpdateId": 20,
    "lastUpdateTimestamp": "2015-04-17T10:57:35.*********+02:00",
    "version": 10,
    "status": "PRONTA_PER_ASSEGNAZIONE",
    "children": [],
    "currentUserObj": null,
    "owner": "ROOT",
    "currentUser": null,
    "lastVarDate": "2015-04-28T11:24:02.*********+02:00",
    "systemType": "STEP",
    "isLocked": false,
    "description": "Finestra di manutenzione per STEP_ID = 18211",
    "properties": [
      "DATA_PREVISTA_ATTIVITA",
      "CIRCUITI_DA_MODIFICARE",
      "CLIENTE",
      "ORE_ATTESA_SILENZIO_ASSENSO",
      "STEP_ID",
      "NOTE",
      "DATA_INIZIO_FDM",
      "FDM_CONCORDATA",
      "TITOLO",
      "DATA_FINE_FDM",
      "MOTIVO_INTERVENTO",
      "IMPATTO_SUL_SERVIZIO",
      "TIMEOUT_SILENZIO_ASSENSO",
      "MOTIVAZIONE",
      "LISTA_COD_CLIENTI",
      "ERMES_ID",
      "OWNER_RICHIESTA_FDM",
      "TT_ID",
      "LISTA_CLIENTI"
    ],
    "creationDate": "2015-04-28T11:24:01.*********+02:00",
    "parentId": 6744,
    "creationUser": "ROOT",
    "dateNextStep": "2015-04-28T11:24:02.*********+02:00",
    "systemId": 2579,
    "type": "FDM",
    "ownerObj": {
      "email": "<EMAIL>",
      "firstName": "ROOT",
      "disabled": false,
      "serviceUser": false,
      "groups": [
        "ROOT"
      ],
      "mobilePhone": null,
      "lastName": "ROOT",
      "username": "ROOT"
    },
    "creationUserObj": {
      "email": "<EMAIL>",
      "firstName": "ROOT",
      "disabled": false,
      "serviceUser": false,
      "groups": [
        "ROOT"
      ],
      "mobilePhone": null,
      "lastName": "ROOT",
      "username": "ROOT"
    },
    "systemObj": {
      "disabled": false,
      "disablingDate": null,
      "endingDate": null,
      "active": true,
      "description": "18211",
      "properties": [
        "ESITO_TEST_ACCETTAZIONE"
      ],
      "ended": false,
      "creationDate": "2015-12-22T11:55:32.*********+01:00",
      "groups": [
        "ADMIN",
        "CUSTOMER_USER",
        "ROOT",
        "SIRTI_USER"
      ],
      "type": "STEP",
      "id": 2579
    }
  },
  {
    "objType":"TRANSITION",
    "id":20150428112402,
    "attachments":[

    ],
    "fromStatus":"OPEN",
    "currentUsername":null,
    "transitionDate":"2015-04-28T11:24:02.*********+02:00",
    "description":"asasas",
    "toStatus":"IN_PROGRESS",
    "username":"HR2",
    "properties":[

    ],
    "action":"TAKE_IN_CHARGE"
  }
]

=cut

sub handler_activities_ID_history_get{
	
	my %input_params = @_;
	
	my %query_params = defined $input_params{QUERY_PARAMS} ? %{$input_params{QUERY_PARAMS}} : ();
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	my $api = vars->{api_art};
	
	my $activity = eval { API::ART::Activity::Factory->new( ART => $api, ID => $uri_params{ID} ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("Activity ID not found"), INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$activity;
	
	$query_params{mergeChildren} = 0 unless defined $query_params{mergeChildren};
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Bad value for {paramname} param", paramname => "mergeChildren"))
		if $query_params{mergeChildren} !~ /^(0|1)$/;
	
	my $history = $activity->history(MERGE_CHILDREN => $query_params{mergeChildren});
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless $history;
	
	my @ret = ();
	
	while( my $transition = $history->next() ) {
		my $step = $transition->serialize();
		
		my $step_hash = {};
		
		if (ref ($transition) eq 'API::ART::Activity::History::Transition'){
			$step_hash = $api->serialize($transition);
			$step_hash->{objType} = 'TRANSITION';
		} else {
			
			my $activity = eval { API::ART::Activity::Factory->new( ART => $api, ID => $step->{ACTIVITY_ID} ) };
			return send_ko(INTERNAL_ERROR_MSG => $@ || $api->last_error())
				if $@ || !$activity;
			
			my $actdump = $activity->dump();
			return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
				unless defined $actdump;
			
			$step_hash = $actdump->{info};
			$step_hash->{id} = $actdump->{id};
			
			$step_hash->{systemObj} = $actdump->{system}->{info};
			$step_hash->{systemObj}->{id} = $actdump->{system}->{id};

			$step_hash->{objType} = 'ACTIVITY';
		}
		
		push (@ret, $step_hash);
	}
	
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		if $api->last_error();
	
	return send_ok(MSG => \@ret);
	
}

get '/activities/:ID/history' => sub {
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return handler_activities_ID_history_get(QUERY_PARAMS => \%query_params, URI_PARAMS => \%{params('route')});

};

######## ROUTE /activities/:ID/info ###########

options '/activities/:ID/info' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/activities/:ID/info Info
@apiName activityInfo
@apiGroup Activity
@apiDescription Returns activity infos

@apiSuccess {String} description The description of the activity.
@apiSuccess {String} type The activity type.
@apiSuccess {Number} systemId The id of the system to whom the activity is associated.
@apiSuccess {String} systemType The type of the system to whom the activity is associated.
@apiSuccess {String} status The current status of the activity.
@apiSuccess {String} owner The username of the user that made the last variation to the activity.
@apiSuccess {Object} ownerObj the object of the user in the same format returned by API <strong>Activity - Get User Info</strong> <code>GET /api/art/instance/users</code>.
@apiSuccess {String} currentUser The username of the user to whom the activity is assigned if any, <code>null</code> otherwhise.
@apiSuccess {Object} currentUserObj the object of the user in the same format returned by API <strong>Activity - Get User Info</strong> <code>GET /api/art/instance/users</code>.
@apiSuccess {String} creationUser The username of the user that created the activity
@apiSuccess {Object} creationUserObj the object of the user that created the activity in the same format returned by API <strong>Activity - Get User Info</strong> <code>GET /api/art/instance/users</code>.
@apiSuccess {String} creationDate The date when the activity has been created.
@apiSuccess {String} lastVarDate The date of the last variation of the activity.
@apiSuccess {String[]} properties The property types related to the activity.
@apiSuccess {Number} parentId The id of the activity parent if any, <code>null</code> otherwhise.
@apiSuccess {String} dateNextStep The minimum date available for the next step.
@apiSuccess {Number[]} children The children id list.
@apiSuccess {Boolean} isLocked <code>true</code> if the activity is locked, <code>false</code> otherwise.
@apiSuccess {Boolean} isLockedByMyself <code>true</code> if the activity is locked by current session, <code>false</code> otherwise.
@apiSuccess {Boolean} active <code>true</code> if the activity is active, <code>false</code> otherwise.
@apiSuccess {Number} lastTransitionId It is the id of the last transition of the activity
@apiSuccess {Number} lastUpdateId It is the id of the last update of the activity
@apiSuccess {String} lastUpdateTimestamp It is the timestamp of the last update of the activity
@apiSuccess {Number} version It is the version number of the activity
@apiSuccess {Boolean} isClosed <code>true</code> if the activity is closed, <code>false</code> otherwise.
@apiSuccess {String} closureDate The closure date of the activity.
@apiSuccess {Object[]} attachments The list of the attachments in the same format returned by API <strong>Activity - Attachment list</strong> <code>GET /api/art/activities/:ID/attachments</code>.
@apiSuccess {Object} agingObj the object of the aging
@apiSuccess {Number} agingObj.calendarId The identifier of the calendar
@apiSuccess {String} agingObj.calendarLabel The label of the calendar
@apiSuccess {String} agingObj.calendarDescription The description of the calendar
@apiSuccess {Boolean} agingObj.update <code>true</code> if the aging's activity can be updated, <code>false</code> otherwise.
@apiSuccess {Number} agingObj.seconds The value of the aging in seconds
@apiSuccess {Number} agingObj.interval The value of the aging as interval
@apiSuccess {String} agingObj.lastVarDate The date of the last variation date of the aging
@apiSuccess {String} agingObj.targetLevel The level of the target SLA
@apiSuccess {String} agingObj.targetLabel The label of the target SLA
@apiSuccess {Object} agingObj.sla the object of the sla
@apiSuccess {Number} agingObj.sla.targetFrom: Start value of the sla
@apiSuccess {Number} agingObj.sla.targetTo: End value of the sla
@apiSuccess {String} agingObj.sla.targetLabel: Label of the sla
@apiSuccess {String} agingObj.sla.targetLevel: Level of the sla
@apiSuccess {String} agingObj.sla.insertDate: Insert date of the sla

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/activities/5704/info
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
     "agingObj":{
       "calendarId": 11,
       "calendarLabel": "H08-20_1-6"
       "calendarDescription": "Mon-Sat 08-20"
       "update": true,
       "seconds": 72,
       "interval": "+0 00:01",
       "lastVarDate": "2015-04-17T10:57:35.*********+02:00",
       "sla": [
         {
           "targetFrom": 900,
           "targetTo": null,
           "targetLabel": "Soglia allarme",
           "targetLevel": "DANGER",
           "InsertDate": "2018-06-29T15:27:07.*********+02:00"
         }
       ]
     },
     "owner" : "ROOT",
     "ownerObj": {
       "email": null,
       "firstName": "ROOT",
       "disabled": false,
       "serviceUser": false,
       "groups": [
         "ROOT"
       ],
       "mobilePhone": null,
       "lastName": "ROOT",
       "username": "ROOT"
     },
     "currentUser" : null,
     "currentUserObj": null,
     "lastVarDate" : "2015-04-17T10:57:35.*********+02:00",
     "systemType" : "PTE_MANAGEMENT",
     "description" : "IC1",
     "properties" : [
       "SUPERVISOR",
       "FTE",
       "ESTIMATED_EFFORT_OUTLOOK"
     ],
     "status" : "OPEN",
     "creationDate" : "2015-04-17T10:57:35.*********+02:00",
     "closureDate" : "2015-04-17T10:57:35.*********+02:00",
     "isClosed" : true,
     "parentId" : null,
     "dateNextStep" : "2015-04-17T10:57:35.*********+02:00",
     "type" : "EPEL_IC",
     "systemId" : 2096,
     "children" : [],
     "isLocked" : true,
     "isLockedByMyself": false,
	 "active": true,
     "lastTransitionId": 10,
     "lastUpdateId": 20,
     "lastUpdateTimestamp": "2015-04-17T10:57:35.*********+02:00",
     "version": 10,
     "creationUser": "ROOT",
     "creationUserObj": {
       "email": null,
       "firstName": "ROOT",
       "disabled": false,
       "serviceUser": false,
       "groups": [
         "ROOT"
       ],
       "mobilePhone": null,
       "lastName": "ROOT",
       "username": "ROOT"
     }
}

=cut

get '/activities/:ID/info' => sub {

	my $api = vars->{api_art};
	
	my $activity = eval { API::ART::Activity::Factory->new( ART => $api, ID => param('ID') ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("Activity ID not found"), INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$activity;

	my %p = ();
	$p{EXCLUDE_PROPERTIES} = 1;
	$p{SYSTEM}->{EXCLUDE_ALL} = 1;
	my $actdump = $activity->dump(%p);
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless defined $actdump;

	return send_ok(MSG => $actdump->{info});

};


######## ROUTE /activities/:ID/properties ###########

options '/activities/:ID/properties' => sub { handle_cors_request( METHODS => [ 'GET', 'PUT' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/activities/:ID/properties Properties
@apiName activityProperties
@apiGroup Activity
@apiDescription Returns an object representing the properties of the activity, where the keys are the NAMEs of the properties.

@apiSuccess {String} NAME The value of the property named NAME.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/activities/5711/properties
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
   "SUPERVISOR" : "<EMAIL>"
}

=cut

get '/activities/:ID/properties' => sub {

    my $api = vars->{api_art};
    
    my $activity = eval { API::ART::Activity::Factory->new( ART => $api, ID => param('ID') ) };
    return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("Activity ID not found"), INTERNAL_ERROR_MSG => $@ || $api->last_error())
    	if $@ || !$activity;

	return send_ok(MSG => $activity->property());

};

######## ROUTE /activities/:ID/activityProperties ###########

options '/activities/:ID/activityProperties' => sub { handle_cors_request( METHODS => [ 'GET', 'PUT' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/activities/:ID/activityProperties Activity properties
@apiName getActivityProperties
@apiGroup Activity
@apiDescription Return the activity properties, showing the organization of the groups of them.

@apiSuccess {Object[]} anonymous Object representing the activity properties group
@apiSuccess {String} [anonymous.group] Name of the group
@apiSuccess {Number} [anonymous.id] Id of the group
@apiSuccess {Object[]} anonymous.properties Object representing the activity property
@apiSuccess {String} anonymous.properties.name Name of the property
@apiSuccess {String} anonymous.properties.value Value of the property
@apiSuccess {Boolean} anonymous.properties.notYetUsed <code>false</code> if the activity made a step where the property is defined, <code>true</code> otherwise.
@apiSuccess {Boolean} anonymous.properties.readOnly <code>true</code> if the property is readOnly, <code>false</code> otherwise.
@apiSuccess {Boolean} anonymous.properties.expired <code>false</code> if the property is no longer associated to at least an action of the activity type, <code>true</code> otherwise.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/activities/2573/activityProperties
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
  {
    "group":"ENGAGEMENT_DETAILS",
    "id":"1",
    "properties":[
      {
        "notYetUsed":false,
        "expired":false,
        "value":"<EMAIL>",
        "name":"COMPETENCE_CENTER_MANAGER",
        "readOnly":true
      },
      {
        "notYetUsed":false,
        "expired":false,
        "value":"200",
        "name":"ESTIMATED_EFFORT_OUTLOOK",
        "readOnly":true
      },
      {
        "notYetUsed":false,
        "expired":false,
        "value":"<EMAIL>",
        "name":"SUPERVISOR",
        "readOnly":true
      },
      {
        "notYetUsed":false,
        "expired":false,
        "value":"76477",
        "name":"PROJECT_COMMESSA",
        "readOnly":true
      }
    ]
  },
  {
    "group":"CATEGORIZATION",
    "id":"3",
    "properties":[
      {
        "notYetUsed":false,
        "expired":false,
        "value":"[\"Proposing\"]",
        "name":"CATEGORY_LIST",
        "readOnly":false
      }
    ]
  },
  {
    "group":"WIP",
    "id":"2",
    "properties":[
      {
        "notYetUsed":false,
        "expired":false,
        "value":"[\"<EMAIL>\",\"<EMAIL>\",\"<EMAIL>\"]",
        "name":"RESOURCE_LIST",
        "readOnly":true
      },
      {
        "notYetUsed":false,
        "expired":false,
        "value":"[\"<EMAIL>\"]",
        "name":"RESOURCE_LIST_BL",
        "readOnly":true
      },
      {
        "notYetUsed":false,
        "expired":false,
        "value":"[\"<EMAIL>\",\"<EMAIL>\"]",
        "name":"RESOURCE_LIST_OG",
        "readOnly":true
      },
      {
        "notYetUsed":false,
        "expired":false,
        "value":"226",
        "name":"TOTAL_ESTIMATED_EFFORT",
        "readOnly":false
      },
      {
        "notYetUsed":false,
        "expired":false,
        "value":"169",
        "name":"TOTAL_ACCOUNTED_EFFORT",
        "readOnly":false
      }
    ]
  }
]

=cut

sub handler_activities_ID_activityProperties_get{
	
	my %input_params = @_;
	
	my %query_params = defined $input_params{QUERY_PARAMS} ? %{$input_params{QUERY_PARAMS}} : ();
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	my $api = vars->{api_art};
	
	my $activity = eval { API::ART::Activity::Factory->new( ART => $api, ID => $uri_params{'ID'} ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("Activity ID not found"), INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$activity;
	
	
	my $results = eval{$activity->activity_property_group()};
	
	return send_ko(INTERNAL_ERROR_MSG => $@)
		if ($@);
	
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless ($results);
	
	my $rets = [];
	
	for my $res (@{$results}){
		
		my $ret = {};
		$ret->{group} = $res->{GROUP} if defined $res->{GROUP};
		$ret->{id} = $res->{ID} if defined $res->{ID};
		$ret->{properties} = [];
		for my $prop (@{$res->{PROPERTIES}}){
			push @{$ret->{properties}}, {
				name => $prop->{NAME}
				,value =>  $prop->{VALUE}
				,readOnly => $prop->{READ_ONLY} eq 'Y' ? $JSON::true : $JSON::false 
				,expired => $prop->{EXPIRED} eq 'Y' ? $JSON::true : $JSON::false
				,notYetUsed => $prop->{NOT_YET_USED} eq 'Y' ? $JSON::true : $JSON::false
				,nullable => $prop->{NULLABLE} eq 'Y' ? $JSON::true : $JSON::false
			};
		}
		
		push @{$rets}, $ret;
	}
	
	return send_ok(MSG => $rets);
}

get '/activities/:ID/activityProperties' => sub {
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return handler_activities_ID_activityProperties_get(QUERY_PARAMS => \%query_params, URI_PARAMS => \%{params('route')});
};

=pod
@api {put} /api/art/activities/:ID/activityProperties Upsert Activity Properties
@apiName activityUpsertProperties
@apiGroup Activity
@apiDescription Upsert activity properties. **Note**: you can update only properties defined as Activity Properties

@apiUse ContentTypeJsonHeader

@apiExample {HTTP} Example usage:
PUT http://localhost/api/art/activities/2104/activityProperties
Content-Type: application/json

{
  "properties" : {
  	"SUPERVISOR" : "<EMAIL>",
  	"IC_NUMBER" : null
  }
}

@apiParam {Object} properties An object representing the properties, where the keys are the NAMEs of the properties.
   To delete a property set the value to <code>null</code>
@apiParam {String} properties.NAME The name of the property.
@apiParam {String} [description] Description of the action.
@apiUse DateAndFormat

@apiSuccessExample Success-Response:
HTTP/1.1 204 No content

=cut

sub handler_activities_ID_activityProperties_put {
	
	my %input_params = @_;
	
	my %body = defined $input_params{BODY} ? %{$input_params{BODY}} : ();
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
		unless (keys %body);
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	my $api = vars->{api_art};
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing {paramname} param", paramname => "properties"))
		unless ($body{properties});
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Bad value for {paramname} param", paramname => "properties"))
		unless (ref($body{properties}) eq 'HASH');
	
	my $activity = eval { API::ART::Activity::Factory->new( ART => $api, ID => $uri_params{'ID'} ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("Activity ID not found"), INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$activity;
	
	my %p = (
		PROPERTIES => $body{properties},
		IGNORE_FINAL_STATUS => 1
	);
	
	$p{DESCRIPTION} = $body{description} if defined $body{description};
	# FIXME: riabilitare quando fatta la patch alle API::ART
	# $p{DATE}			= params->{date} if defined(params->{date});
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
		unless $activity->update_properties( %p );
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_NO_CONTENT);
}

put '/activities/:ID/activityProperties' => sub {
	
	my %body = request->params('body');
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
		unless (keys %body);
	
	return handler_activities_ID_activityProperties_put(BODY => \%body, URI_PARAMS => \%{params('route')});
};

######## ROUTE /activities/:ID/assignee ###########

options '/activities/:ID/assignee' => sub { handle_cors_request( METHODS => [ 'PUT' ] ); return send_ok(IGNORE_SESSION => 1); };

=pod
@api {put} /api/art/activities/:ID/assignee Assign/Deassign
@apiName activityAssign
@apiGroup Activity
@apiDescription Assign activity

@apiUse ContentTypeJsonHeader

@apiExample {HTTP} Example usage:
PUT http://localhost/api/art/activities/2104/assignee
Content-Type: application/json

{
  "description" : "assign!",
  "destUser": "root"
}

@apiParam {String} destUser If not <code>null</code> is the username of the user to whom the activity must be assigned, if <code>null</code> the activity is deassigned.
@apiParam {String} [description] Description of the assignment/deassignment.
@apiParam {Number=0,1} [assignNotification=0] If <code>1</code> the user will receive a notification email.

@apiSuccessExample Success-Response:
HTTP/1.1 204 No content

=cut

put '/activities/:ID/assignee' => sub {

    my $api = vars->{api_art};
    
    my %body = request->params('body');
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
		unless (keys %body);
    
    return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing {paramname} param", paramname => "destUser"))
    	unless ($body{destUser});
    
    my $activity = eval { API::ART::Activity::Factory->new( ART => $api, ID => param('ID') ) };
    return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("Activity ID not found"), INTERNAL_ERROR_MSG => $@ || $api->last_error())
    	if $@ || !$activity;

	my %p = ();
	
	$p{DESCRIPTION} = $body{description} if defined $body{description};
	$p{ASSIGN_NOTIFICATION} = $body{assignNotification} if defined $body{assignNotification};
	
	if (defined $body{destUser}){
		$p{DEST_USER} = $body{destUser};
	    return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
    		unless $activity->assign( %p );
	} else {
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
	    	unless $activity->deassign( %p );
	}
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_NO_CONTENT);

};

######## ROUTE /activities/:ID/assignee/users ###########

options '/activities/:ID/assignee/users' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };

=pod
@api {get} /api/art/activities/:ID/assignee/users Assignee Users List
@apiName activityAssignGetUsers
@apiGroup Activity
@apiDescription List all the users whom the activity <code>:ID</code> can be assigned

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/activities/5704/assignee/users
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
  {
    "email": "<EMAIL>",
    "firstName": "IVAN",
    "disabled": false,
    "serviceUser": false,
    "groups": [
      "TECHNICAL_SUPPORT"
    ],
    "mobilePhone": null,
    "lastName": "LONGHI",
    "username": "LONGHI"
  }
]
=cut

get '/activities/:ID/assignee/users' => sub {
	
	my $api = vars->{api_art};
	
	return _get_dest_users(ACTION => $api->_get_activity_virtual_action_assign_name(), ID=>param('ID'));
};

######## ROUTE /activities/:ID/attachments ###########

=pod
@api {options} /activities/:ID/attachments Check methods attachments
@apiName checkMethodsAttachment
@apiGroup Activity
@apiDescription Check the methods availabe for attachments, where <code>ID</code> is the id of the activity.

@apiExample {HTTP} Example usage:
OPTIONS /api/art/activities/6151/attachments HTTP/1.1

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK
Allow: POST,OPTIONS

=cut

sub handler_activities_ID_attachments_options{
	
	my %input_params = @_;
	
	my %query_params = defined $input_params{QUERY_PARAMS} ? %{$input_params{QUERY_PARAMS}} : ();
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	if (is_preflight()){
		handle_cors_request( METHODS => [ 'POST', 'OPTIONS', 'GET' ] );
		return send_ok(IGNORE_SESSION => 1);
	}

	my $api = vars->{api_art};
	
	my $activity = eval { API::ART::Activity::Factory->new( ART => $api, ID => $uri_params{'ID'} ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("Activity ID not found"), INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$activity;

	my $methods = [ 'OPTIONS', 'GET' ];

	if ($activity->can_add_documentation()){
		push @{$methods}, 'POST';
	}

	handle_cors_request( METHODS => $methods );
	return send_ok();
}

options '/activities/:ID/attachments' => sub {

	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return handler_activities_ID_attachments_options(QUERY_PARAMS => \%query_params, URI_PARAMS => \%{params('route')});
};

=pod
@api {get} /api/art/activities/:ID/attachments Attachment list
@apiName attachmentList
@apiGroup Activity
@apiDescription Get activity attachment list, where <code>ID</code> is the id of the activity.

@apiExample {HTTP} Example usage:
GET /api/art/activities/6151/attachments HTTP/1.1

@apiSuccess {Object[]} anonymous Object rappresenting the attachment.
@apiSuccess {Number} anonymous.transitionId The id of the transition of the attachment.
@apiSuccess {String} anonymous.transitionDate The date of the transition of the attachment.
@apiSuccess {Number} anonymous.sequence The ordinal number of the attachment.
@apiSuccess {String} anonymous.fileName The name of the attachment.
@apiSuccess {String} anonymous.title The title of the attachment (can be null).
@apiSuccess {String} anonymous.description The description of the attachment (can be null).
@apiSuccess {String} anonymous.revision The revision of the attachment (can be null).
@apiSuccess {String} anonymous.refDate The reference date of the attachment in the format YYYY-MM-DD (can be null).
@apiSuccess {String} anonymous.docType The document type of the attachment (can be null).
@apiSuccess {Number} anonymous.size The size of the attachment.
@apiSuccess {String} anonymous.owner The username of the user that uploaded the attachment.
@apiSuccess {String} anonymous.ownerFirstName The firstname of the user that uploaded the attachment.
@apiSuccess {String} anonymous.ownerLastName The lastname of the user that uploaded the attachment.
@apiSuccess {Number} anonymous.downloadCount The number of times the hattachment has been downloaded (can be <code>null</code>).

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
  {
    "owner":"ROOT",
    "sequence":0,
    "fileName":"SCARTI%20NPD2%20NOTIFICA_VS_DONOR_DONATING%20del%2029122014.xls",
    "transitionDate":"2015-05-20T11:04:11.*********+02:00",
    "transitionId":20150520110411,
    "title":null,
    "description":null,
    "revisione":null,
    "refDate":null,
    "docType":null,
    "size":84480,
    "downloadCount":null,
    "ownerFirstName":"ROOT",
    "ownerLastName":"ROOT"
  },
  {
    "owner":"ROOT",
    "sequence":1,
    "fileName":"TL22013032007.xml",
    "transitionDate":"2015-05-20T11:04:11.*********+02:00",
    "transitionId":20150520110411,
    "title":null,
    "description":null,
    "revisione":null,
    "refDate":null,
    "docType":null,
    "size":7109,
    "downloadCount":null,
    "ownerFirstName":"ROOT",
    "ownerLastName":"ROOT"
  }
]
=cut

sub handler_activities_ID_attachments_get{
	
	my %input_params = @_;
	
	my %query_params = defined $input_params{QUERY_PARAMS} ? %{$input_params{QUERY_PARAMS}} : ();
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	my $api = vars->{api_art};
	
	my $activity = eval { API::ART::Activity::Factory->new( ART => $api, ID => $uri_params{'ID'} ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("Activity ID not found"), INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$activity;
	
	my $ret = $activity->dump()->{"info"}->{"attachments"};
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless defined $ret;
	
	return send_ok(MSG => $ret);
}

get '/activities/:ID/attachments' => sub {
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return handler_activities_ID_attachments_get(QUERY_PARAMS => \%query_params, URI_PARAMS => \%{params('route')});

};

=pod
@api {post} /activities/:ID/attachments Add activity attachments
@apiName addActivityAttachments
@apiGroup Activity
@apiDescription Add activity attachments

@apiUse ContentTypeJsonHeader

@apiExample {HTTP} Example usage:
POST /api/art/activities/5711/attachments HTTP/1.1
Content-Type: application/json

@apiParam {String[]} [attachments] Id of temporary file to attach to the activity. The file must have been uploaded with
  <code>POST /api/art/tmpfiles</code> previously.

@apiSuccessExample Success-Response:
HTTP/1.1 204 No content

=cut

sub handler_activities_ID_attachments_post {
	
	my %input_params = @_;
	
	my %body = defined $input_params{BODY} ? %{$input_params{BODY}} : ();
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
		unless (keys %body);
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	my $api = vars->{api_art};

	my $activity = eval { API::ART::Activity::Factory->new( ART => $api, ID => $uri_params{'ID'} ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("Activity ID not found"), INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$activity;

	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing mandatory param {paramname}", paramname => 'attachments'))
		unless defined $body{attachments};

	my %p = ();
	
	$p{DESCRIPTION}		= $body{description} if defined($body{description});
	
	$p{ATTACHMENTS}	= [];

	return send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => "attachment param must be an array")
		if (ref ($body{attachments}) ne 'ARRAY');
	
	foreach my $att (@{$body{attachments}}) {
		my $filename = $ENV{ART_REPOSITORY_TMP_TMPFILES}."/".get_sid()."_".$att;

		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Not existent attachment", INTERNAL_ERROR_MSG => "Not existent attachment: $filename")
			unless -f $filename && -f $filename.".idx";
		
		return send_ko(INTERNAL_ERROR_MSG => "Unable to open index file ".$filename.".idx" . ": " . $!)
			unless (open FD, $filename.".idx");
		
		binmode FD, ':utf8';

		my $idxjson = '';
		while(<FD>) {
			$idxjson .= $_;	
		}
		
		my $idx = eval{ from_json($idxjson) };
		return send_ko(INTERNAL_ERROR_MSG => "Unable to parse JSON idxfile ".$filename.".idx" . ": " . $@)
			if ($@);
		
		return send_ko(INTERNAL_ERROR_MSG => "Unable to copy file from ".$filename." to " . $ENV{ART_REPOSITORY_TMP_TMPFILES}."/".$idx->{filename} . ": " . $!)
			unless (copy($filename,$ENV{ART_REPOSITORY_TMP_TMPFILES}."/".$idx->{filename}));
		
		my $attach_obj = {
			FILENAME => $ENV{ART_REPOSITORY_TMP_TMPFILES}."/".$idx->{filename}
		};
		$attach_obj->{'TITLE'} = $idx->{title} if exists $idx->{title};
		$attach_obj->{'DESCRIPTION'} = $idx->{description} if exists $idx->{description};
		$attach_obj->{'REVISION'} = $idx->{revision} if exists $idx->{revision};
		$attach_obj->{'REF_DATE'} = $idx->{refDate} if (exists $idx->{refDate});
		$attach_obj->{'DOC_TYPE'} = $idx->{docType} if (exists $idx->{docType});
		
		push @{$p{ATTACHMENTS}}, $attach_obj;
	}

	return send_ko(CODE => HTTP_FORBIDDEN, ERROR_MSG => $api->last_error())
		unless $activity->can_add_documentation();

	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
		unless $activity->add_documentation(%p);
	
	for my $f ( @{$p{ATTACHMENTS}} ) {
		unlink($f);	
	}
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_NO_CONTENT);
}

post '/activities/:ID/attachments' => sub {
	
	my %body = request->params('body');
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
		unless (keys %body);
	
	return handler_activities_ID_attachments_post(BODY => \%body, URI_PARAMS => \%{params('route')});
};

######## ROUTE /activities/:ID/hierarchy ###########

options '/activities/:ID/hierarchy' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };

=pod
@api {get} /activities/:ID/hierarchy Get Hierarchy
@apiName getActivityHierarchy
@apiGroup Activity
@apiDescription Return a recursive object describing the tree of the activity hierarchy

@apiParam {Number=0,1} [includeSystem=0] Include complete information about system.
@apiParam {Number=0,1} [includeAvailableActions=0] Include the available actions for current status and user profile.
@apiParam {Number=0,1} [excludeInfo=0] Exclude activity info.
@apiParam {Number=0,1} [excludeProperties=0] Exclude activity properties.

@apiSuccess {Object} activity The activity in the same format returned by API <strong>Activity - Get</strong> <code>GET /api/art/activities/:ID</code>.
@apiSuccess {Object[]} children An array of recursive objects of this type   

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/activities/6693/hierarchy?excludeInfo=1&excludeProperties=1
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "children": [
    {
      "children": [],
      "activity": {
        "id": 6750
      }
    },
    {
      "children": [
      {
        "children": [],
        "activity": {
          "id": 6804
        }
      },
      {
        "children": [],
        "activity": {
          "id": 6809
        }
      }
    ],
      "activity": {
        "id": 6702
      }
    },
    {
      "children": [],
      "activity": {
        "id": 6701
      }
    }
  ],
  "activity": {
    "id": 6693
  }
}

=cut

sub _remap_hierarchy {
	my $api = shift;
	my $hierarchy = shift;
	my $tmp = shift;
	my %params = @_;
	
	my %p = ();
	$p{EXCLUDE_INFO} = $params{EXCLUDE_INFO} ? 1 : 0;
	$p{EXCLUDE_PROPERTIES} = $params{EXCLUDE_PROPERTIES} ? 1 : 0;
	$p{SYSTEM}->{EXCLUDE_ALL} = $params{INCLUDE_SYSTEM} ? 0 : 1;
	$p{EXCLUDE_AVAILABLE_ACTIONS} = $params{INCLUDE_AVAILABLE_ACTIONS} ? 0 : 1;
	$tmp->{activity} = $hierarchy->{ACTIVITY}->dump(%p);
	return undef
		unless defined $tmp->{activity};
	
	$tmp->{children} = [];
	
	for (my $i=0;$i<scalar(@{$hierarchy->{CHILDREN}});$i++){
		$tmp->{children}->[$i] = {};
		my $ret = _remap_hierarchy($api, $hierarchy->{CHILDREN}->[$i], $tmp->{children}->[$i], %params);
		return undef
			unless defined $ret;
	}  
	
}

sub handler_activities_ID_hierarchy_get{
	
	my %input_params = @_;
	
	my %query_params = defined $input_params{QUERY_PARAMS} ? %{$input_params{QUERY_PARAMS}} : ();
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	my $api = vars->{api_art};
	
	my $activity = eval { API::ART::Activity::Factory->new( ART => $api, ID => $uri_params{'ID'} ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("Activity ID not found"), INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$activity;
	
	my $hierarchy = $activity->get_hierarchy();
	
	my $tmp = {};
	
	my $ret = _remap_hierarchy($api, $hierarchy, $tmp, EXCLUDE_INFO => $query_params{excludeInfo}, EXCLUDE_PROPERTIES => $query_params{excludeProperties}, INCLUDE_SYSTEM => $query_params{includeSystem}, INCLUDE_AVAILABLE_ACTIONS => $query_params{includeAvailableActions});
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless defined $ret;
	
	return send_ok(MSG => $tmp);
}

get '/activities/:ID/hierarchy' => sub {
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return handler_activities_ID_hierarchy_get(QUERY_PARAMS => \%query_params, URI_PARAMS => \%{params('route')});
};

######## ROUTE /activities/:ID/history/:TRANSITION_ID/attachments/:SEQUENCE ###########

=pod
@api {options} /api/art/activities/:ID/history/:TRANSITION_ID/attachments/:SEQUENCE Check methods attachment
@apiName checkMethodsTransitionAttachment
@apiGroup Activity
@apiDescription Check the methods availabe for transition attachment, where <code>ID</code> is the id of the activity, <code>TRANSITION_ID</code> is the id of the transition (see **Activity - History**)
  and <code>SEQUENCE</code> is the nth zero-based file attached to the step.

@apiExample {HTTP} Example usage:
OPTIONS /api/art/activities/6151/history/20150520110411/attachments/0 HTTP/1.1

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK
Allow: GET,DELETE,OPTIONS

=cut

sub handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_options{
	
	my %input_params = @_;
	
	my %query_params = defined $input_params{QUERY_PARAMS} ? %{$input_params{QUERY_PARAMS}} : ();
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	if (is_preflight()){
		handle_cors_request( METHODS => [ 'GET', 'DELETE', 'OPTIONS' ] );
		return send_ok(IGNORE_SESSION => 1);
	}

	my $api = vars->{api_art};

	my $repo = vars->{api_art_repo};

	my $methods = [ 'OPTIONS' ];

	my $res= $repo->can_get_by_activity_transition(
		ID => $uri_params{'ID'},
		TRANSITION_ID => $uri_params{'TRANSITION_ID'},
		SEQUENCE => $uri_params{'SEQUENCE'},
	);

	if ($res) {
		push @{$methods}, 'GET';
	
		# la DELETE ha senso verificarla solo se l'utente ha visibilita' sull'allegato
		$res= $repo->can_delete_by_activity_transition(
			ID => $uri_params{'ID'},
			TRANSITION_ID => $uri_params{'TRANSITION_ID'},
			SEQUENCE => $uri_params{'SEQUENCE'},
		);

		if (defined $res) {
			push @{$methods}, 'DELETE';
		}
	}

	handle_cors_request( METHODS => $methods );
	return send_ok();
}

options '/activities/:ID/history/:TRANSITION_ID/attachments/:SEQUENCE' => sub {

	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_options(QUERY_PARAMS => \%query_params, URI_PARAMS => \%{params('route')});
};

=pod
@api {get} /api/art/activities/:ID/history/:TRANSITION_ID/attachments/:SEQUENCE Download attachment
@apiName downloadTransitionAttachment
@apiGroup Activity
@apiDescription Download transition attachment, where <code>ID</code> is the id of the activity, <code>TRANSITION_ID</code> is the id of the transition (see **Activity - History**)
  and <code>SEQUENCE</code> is the nth zero-based file attached to the step.

@apiExample {HTTP} Example usage:
GET /api/art/activities/6151/history/20150520110411/attachments/0 HTTP/1.1

@apiSuccess {File} anonymous The binary file.

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK
Content-Disposition: attachment; filename="Requirements - rev.0.04.docx"
Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document
Content-Length: 19472

=cut

sub handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_get{
	
	my %input_params = @_;
	
	my %query_params = defined $input_params{QUERY_PARAMS} ? %{$input_params{QUERY_PARAMS}} : ();
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	my $api = vars->{api_art};

	my $repo = vars->{api_art_repo};

	my $res= $repo->get_by_activity_transition(
		ID => $uri_params{'ID'},
		TRANSITION_ID => $uri_params{'TRANSITION_ID'},
		SEQUENCE => $uri_params{'SEQUENCE'},
	);

	unless(defined $res) {
		return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error());
	}

	
	my $filename = $res->{FILENAME};
	my $size = $res->{SIZE};
	
	force_file_content_length($size);
	
	my $content_type;
	my $mimetype = $mt->mimeTypeOf($filename);
	if(defined $mimetype) {
		$content_type = $mimetype->type;
	} else {
		$content_type = "octet/stream";
	}
	
	send_file_ok($res->{FILE}, filename => $filename, content_type => $content_type);
}

get '/activities/:ID/history/:TRANSITION_ID/attachments/:SEQUENCE' => sub {
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_get(QUERY_PARAMS => \%query_params, URI_PARAMS => \%{params('route')});
	
};

=pod
@api {delete} /api/art/activities/:ID/history/:TRANSITION_ID/attachments/:SEQUENCE Delete attachment
@apiName deleteTransitionAttachment
@apiGroup Activity
@apiDescription Delete transition attachment, where <code>ID</code> is the id of the activity, <code>TRANSITION_ID</code> is the id of the transition (see **Activity - History**)
  and <code>SEQUENCE</code> is the nth zero-based file attached to the step.

@apiExample {HTTP} Example usage:
DELETE /api/art/activities/6151/history/20150520110411/attachments/0 HTTP/1.1

@apiSuccessExample Success-Response:
HTTP/1.1 204 No content

=cut

sub handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_delete{
	
	my %input_params = @_;
	
	my %query_params = defined $input_params{QUERY_PARAMS} ? %{$input_params{QUERY_PARAMS}} : ();
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	my $api = vars->{api_art};

	my $repo = vars->{api_art_repo};

	my $res= $repo->delete_by_activity_transition(
		ID => $uri_params{'ID'},
		TRANSITION_ID => $uri_params{'TRANSITION_ID'},
		SEQUENCE => $uri_params{'SEQUENCE'},
	);

	unless(defined $res) {
		return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error());
	}

	do_api_save( $api );
	
	send_ok(CODE => HTTP_NO_CONTENT);
}

del '/activities/:ID/history/:TRANSITION_ID/attachments/:SEQUENCE' => sub {
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_delete(QUERY_PARAMS => \%query_params, URI_PARAMS => \%{params('route')});
	
};

######## ROUTE /activities/:ID/children ###########

options '/activities/:ID/children' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/activities/:ID/children Find children
@apiName getActivityChildren
@apiGroup Activity
@apiDescription Returns an array of objects describing the children activities matching the specified criteria.

@apiParam {String[]} [type] Filter activities of these types.
@apiParam {Number=0,1} [includeSystem=0] Include complete information about system.

@apiSuccess {Number} id The id of the activity found.
@apiSuccess {Object} info The info of the activity found in the same format returned by API <strong>Activity - Info</strong> <code>GET /api/art/activities/:ID/info</code>.
@apiSuccess {Object} properties The properties of the activity found in the same format returned by API <strong>Activity - Properties</strong> <code>GET /api/art/activities/:ID/properties</code>.
@apiSuccess {Object} system The system object in the same format returned by API <strong>System - Find</strong> <code>GET /api/art/systems</code>. It's present only if param <code>includeSystem=1</code>  

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/activities/5884/children/?type=EPCCL
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
   {
      "id" : 5699,
      "info" : {
        "owner" : "ROOT",
        "ownerObj": {
          "email": null,
          "firstName": "ROOT",
          "disabled": false,
          "serviceUser": false,
          "groups": [
            "ROOT"
          ],
          "mobilePhone": null,
          "lastName": "ROOT",
          "username": "ROOT"
        },
        "currentUser" : null,
        "currentUserObj": null,
        "lastVarDate" : "2015-04-17T10:57:35.*********+02:00",
        "systemType" : "PTE_MANAGEMENT",
        "description" : "IC1",
        "properties" : [
          "SUPERVISOR",
          "FTE",
          "ESTIMATED_EFFORT_OUTLOOK"
        ],
        "status" : "OPEN",
        "creationDate" : "2015-04-17T10:57:35.*********+02:00",
        "parentId" : null,
        "dateNextStep" : "2015-04-17T10:57:35.*********+02:00",
        "type" : "EPEL_IC",
        "systemId" : 2096,
        "children" : [],
        "isLocked" : true,
        "isLockedByMyself": false,
		"active": true,
        "lastTransitionId": 10,
        "lastUpdateId": 20,
        "lastUpdateTimestamp": "2015-04-17T10:57:35.*********+02:00",
        "version": 10,
        "creationUser": "ROOT",
        "creationUserObj": {
          "email": null,
          "firstName": "ROOT",
          "disabled": false,
          "serviceUser": false,
          "groups": [
            "ROOT"
          ],
          "mobilePhone": null,
          "lastName": "ROOT",
          "username": "ROOT"
        }
      },
      "properties" : {
         "ESTIMATED_EFFORT_OUTLOOK" : "75",
         "FTE" : null,
         "SUPERVISOR" : "<EMAIL>"
      },
      "isLocked" : false,
      "isLockedByMyself" : false,
	  "active" : true,
      "lastTransitionId": 10,
      "lastUpdateId": 20,
      "lastUpdateTimestamp": "2015-04-17T10:57:35.*********+02:00",
      "version": 10
   }
]
=cut

sub handler_activities_ID_children_get{
	
	my %input_params = @_;
	
	my %query_params = defined $input_params{QUERY_PARAMS} ? %{$input_params{QUERY_PARAMS}} : ();
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	my $api = vars->{api_art};
	
	my $activity = eval { API::ART::Activity::Factory->new( ART => $api, ID => $uri_params{ID} ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("Activity ID not found"), INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$activity;
	
	my %p = ();
	
	if (defined $query_params{type}) {
		if (ref $query_params{type}){
			$p{ACTIVITY_TYPE_NAME} = $query_params{type};
		} else {
			$p{ACTIVITY_TYPE_NAME} = [$query_params{type}];
		}
	}
	
	my $children = $activity->get_children(%p);
	
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless $children;
	
	my @ret = ();
	
	for my $child (@{$children}){
		# NOTA: con il comando successivo si escludono i figli su cui non si ha visibilità
		# FIXME: andrebbe implementato il parametro SHOW_ONLY_WITH_VISIBILITY in API::ART::Activity->get_children()
		next unless $child;
		
		my $dumpact = $child->dump(SYSTEM => { EXCLUDE_ALL => $query_params{includeSystem} ? 0 : 1 });
		return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
			unless $dumpact;
		
		push @ret, $dumpact;
	}
	
	return send_ok(MSG => \@ret);
}

get '/activities/:ID/children' => sub {
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return handler_activities_ID_children_get(QUERY_PARAMS => \%query_params, URI_PARAMS => \%{params('route')});

};

######## ROUTE /activities/:ID/canDoAction/:ACTION ###########

options '/activities/:ID/canDoAction/:ACTION' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/activities/:ID/canDoAction/:ACTION CanDoAction
@apiName activityCanDoAction
@apiGroup Activity
@apiDescription Check if a step can be done

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/activities/5704/canDoAction/FREEZE
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 204 No content

=cut

sub handler_activities_ID_canDoAction_ACTION_get{
	
	my %input_params = @_;
	
	my %query_params = defined $input_params{QUERY_PARAMS} ? %{$input_params{QUERY_PARAMS}} : ();
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	my $api = vars->{api_art};
	
	my $activity = eval { API::ART::Activity::Factory->new( ART => $api, ID => $uri_params{'ID'} ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("Activity ID not found"), INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$activity;
	
	return send_ko(CODE => HTTP_FORBIDDEN, ERROR_MSG => $api->last_error())
		unless $activity->can_do_action(NAME => $uri_params{'ACTION'});
	
	return send_ok(CODE => HTTP_NO_CONTENT);
}

get '/activities/:ID/canDoAction/:ACTION' => sub {

	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return handler_activities_ID_canDoAction_ACTION_get(QUERY_PARAMS => \%query_params, URI_PARAMS => \%{params('route')});
};

######## ROUTE /activities/:ID/destUsers/:ACTION ###########

options '/activities/:ID/destUsers/:ACTION' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };
	
=pod
@api {get} /api/art/activities/:ID/destUsers/:ACTION destUsers
@apiName activityDestUsers
@apiGroup Activity
@apiDescription List all the users whom the activity <code>:ID</code> can be assigned by the action <code>:ACTION</code>

@apiParam {String} [status] Set the initial status for the action defined.

@apiExample {HTTP} Example usage:
GET http://localhost/api/art/activities/5704/destUsers/FREEZE
Accept: application/json

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
  {
    "email": "<EMAIL>",
    "firstName": "IVAN",
    "disabled": false,
    "serviceUser": false,
    "groups": [
      "TECHNICAL_SUPPORT"
    ],
    "mobilePhone": null,
    "lastName": "LONGHI",
    "username": "LONGHI"
  }
]
=cut

sub handler_activities_ID_destUsers_ACTION_get{
	
	my %input_params = @_;
	
	my %query_params = defined $input_params{QUERY_PARAMS} ? %{$input_params{QUERY_PARAMS}} : ();
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	my %p = (
		ACTION => $uri_params{'ACTION'}
		, ID=>$uri_params{'ID'}
	);
	
	$p{STATUS} = $query_params{status} if defined $query_params{status};
	
	return _get_dest_users(%p);
}

get '/activities/:ID/destUsers/:ACTION' => sub {
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return handler_activities_ID_destUsers_ACTION_get(QUERY_PARAMS => \%query_params, URI_PARAMS => \%{params('route')});

};

######## ROUTE /activities/:ID/lock ###########

options '/activities/:ID/lock' => sub { handle_cors_request( METHODS => [ 'PUT', 'DELETE' ] ); return send_ok(IGNORE_SESSION => 1); };


=pod
@api {put} /api/art/activities/:ID/lock Lock Activity
@apiName activityLock
@apiGroup Activity
@apiDescription Lock Activity

@apiUse ContentTypeJsonHeader

@apiExample {HTTP} Example usage:
PUT http://localhost/api/art/activities/2104/lock

@apiSuccessExample Success-Response:
HTTP/1.1 201 Created

=cut

sub handler_activities_ID_lock_put {
	
	my %input_params = @_;
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	my $api = vars->{api_art};
	
	my $activity = eval { API::ART::Activity::Factory->new( ART => $api, ID => $uri_params{'ID'} ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("Activity ID not found"), INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$activity;
	
	return send_ko(CODE => HTTP_FORBIDDEN, ERROR_MSG => $api->last_error())
		unless $activity->lock();
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_CREATED);
}

put '/activities/:ID/lock' => sub {

	return handler_activities_ID_lock_put(URI_PARAMS => \%{params('route')});

};

=pod
@api {delete} /api/art/activities/:ID/lock Unlock Activity
@apiName activityUnlock
@apiGroup Activity
@apiDescription Unlock Activity

@apiUse ContentTypeJsonHeader

@apiExample {HTTP} Example usage:
DELETE http://localhost/api/art/activities/2104/lock

@apiSuccessExample Success-Response:
HTTP/1.1 204 No content

=cut

sub handler_activities_ID_lock_del {
	
	my %input_params = @_;
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	my $api = vars->{api_art};
	
	my $activity = eval { API::ART::Activity::Factory->new( ART => $api, ID => $uri_params{'ID'} ) };
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("Activity ID not found"), INTERNAL_ERROR_MSG => $@ || $api->last_error())
		if $@ || !$activity;
	
	return send_ko(CODE => HTTP_FORBIDDEN, ERROR_MSG => $api->last_error())
		unless $activity->unlock();
	
	do_api_save( $api );
	
	return send_ok(CODE => HTTP_NO_CONTENT);
}

del '/activities/:ID/lock' => sub {

	return handler_activities_ID_lock_del(URI_PARAMS => \%{params('route')});

};

######## ROUTE /dashboards/:OBJECT_TYPE/:TYPE ###########

options '/dashboards/:OBJECT_TYPE/:TYPE' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };

=pod
@api {get} /dashboards/:OBJECT_TYPE/:TYPE Configuration
@apiName dashboardsConfig
@apiGroup Dashboards
@apiDescription Get dashboard config for :OBJECT_TYPE and :TYPE

@apiParam {Number=0,1} [tracking=0] <code>1</code> created by the user, <code>0</code> all activities.
@apiParam {Number=0,1} [mine=0] <code>1</code> owner by the user, <code>0</code> all activities.
@apiParam {Number=0,1} [cache=1] <code>1</code> searches in cache if available, <code>0</code> otherwise.

@apiExample {HTTP} Example usage:
GET /dashboards/activities/WO HTTP/1.1

@apiSuccess {Number} count The count of the activity.
@apiSuccess {Object} context The 'id' context
@apiSuccess {Object} label The label for the content

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
  {
    "count": "20",
    "context": "01_pre_tec",
    "label": "Prenalisi Tecnica"
  },
  {
    "count": "15",
    "context": "02_block",
    "label": "Attivita' in attesa di sblocco"
  }
]

=cut

sub handler_dashboards_OBJECT_TYPE_TYPE_get{
	
	my %input_params = @_;
	
	my %query_params = defined $input_params{QUERY_PARAMS} ? %{$input_params{QUERY_PARAMS}} : ();
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	my $api = vars->{api_art};
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Dashboards for ".$uri_params{'OBJECT_TYPE'}." not yet implemented"))
		if $uri_params{'OBJECT_TYPE'} ne 'activities';
	
	# prima prendo tutte i tipi attività indipendentemente dalla visibilità per capire se il tipo_attivita ha le network 
	my $activity_types = eval{$api->enum_activity_type(EXTENDED_OUTPUT => 1)};
	
	return send_ko(INTERNAL_ERROR_MSG => $@)
		if ($@);
	
	my $ok = 0;
	for my $ta (@{$activity_types}) {
		# considero solo il record relativo al tipo attività che mi è richiesto
		next if $ta->{ACTIVITY_TYPE_NAME} ne $uri_params{'TYPE'};
		$ok = $ta->{ACTIVITY_TYPE_HAS_DASHBOARD};
		last;
	}
	
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("Activity type does not provide dashboard"))
		unless $ok;

	# poi prendo solo i tipi attività su cui si ha visibilità
	$activity_types = eval{$api->enum_activity_type(EXTENDED_OUTPUT => 1, SHOW_ONLY_WITH_VISIBILITY => 1)};
	
	return send_ko(INTERNAL_ERROR_MSG => $@)
		if ($@);
	
	$ok = 0;
	for my $ta (@{$activity_types}) {
		# considero solo il record relativo al tipo attività che mi è richiesto
		next if $ta->{ACTIVITY_TYPE_NAME} ne $uri_params{'TYPE'};
		$ok = 1;
		last;
	}
	
	return send_ok(MSG => []) unless $ok;
	
	my $collActs = API::ART::Collection::Activity->new( ART => $api );
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless $collActs;
	
	my $where_condition = '';
	
	my $find_params = {
		ACTIVE => 1
		, SHOW_ONLY_WITH_VISIBILITY => 1
		, ACTIVITY_TYPE_NAME_EQUAL => $uri_params{'TYPE'}
	};
	
	$find_params->{CREATED_BY_ME} = 1 if (defined $query_params{'tracking'} && $query_params{'tracking'} == 1);
	$find_params->{ASSIGNED_TO_ME} = 1 if (defined $query_params{'mine'} && $query_params{'mine'} == 1);
	
	# alcune chiavi non possono essere sovascritte
	for (
		'type',
		'type_equal',
		'type_in',
		'type_begins',
		'type_ends',
		'type_contains',
		'active',
		'showOnlyWithVisibility'
	){
		delete $query_params{$_} if exists $query_params{$_};
	}
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
		unless defined _normalize_activities_search_filter(OBJ => $find_params, QUERY_PARAMS => \%query_params);
	
	# di default cerco sempre in ELK se disponibile
	$query_params{cache} = 1 unless defined $query_params{cache};
	my $use_legacy_mode = 1;
	my $acts;

	if (
		$ENV{ELASTICSEARCH_SYNC_DELTA_TIME}
		&&
		$ENV{ELASTICSEARCH_NODES}
		&&
		$ENV{ELASTICSEARCH_INSTANCE_NAME}
		&& 
		$query_params{cache}
	) {

		# print STDERR "\n sono in ELK\n";

		my %p = ();
		
		$query_params{active} = 1;
		$query_params{type_equal} = $uri_params{'TYPE'};
		$query_params{showOnlyWithVisibility} = 1;

		my $es = _get_elasticsearch;

		ELK_SEARCH: {
			# verifico la presenza della configurazione
			# esiste chiamo ELK solo se è presente il tipo attività ed è abilitata la ricerca
			if (defined $elk_config_file){
				my @conf_elk_type = grep {$_->{name} eq $uri_params{'TYPE'}} @{$elk_config_file};
				unless (scalar @conf_elk_type){
					debug "Activity type ".$uri_params{'TYPE'}." not defined in ELK";
					last ELK_SEARCH;
				}
				unless ($conf_elk_type[0]->{ws}->{search}){
					debug "ELK search disabled for activity type ".$uri_params{'TYPE'};
					last ELK_SEARCH;
				}
			}

			my $ping = eval {$es->ping()};
			if ($@){
				my $msg = "Elasticsearch error in ping ".$@; 
				warning $msg;
				last ELK_SEARCH;
			}

			my $index = sprintf 'api-art-activity-%s-%s', $ENV{ELASTICSEARCH_INSTANCE_NAME}, lc($uri_params{'TYPE'});

			my $elk_mappings = vars->{elk_mappings};

			unless (exists ($elk_mappings->{$index})){
				# faccio una sola volta la chiamata per i mapping
				eval{
					$elk_mappings->{$index} = $es->indices->get(
						index => $index
					)->{$index};
				};
				last ELK_SEARCH if $@;
				var elk_mappings => $elk_mappings;
			}

			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
				unless defined _normalize_activities_search_filter_for_elastic(OBJ => \%p, QUERY_PARAMS => \%query_params, ELK_MAPPINGS => $elk_mappings->{$index});

			# verifico che ELK non sia troppo disallineato rispetto a Oracle
			my $res = $es->search(
				index => $index,
				body => {
					"size" => 1, 
					"query" => {
						"match_all" => {}
					},
					"sort" => [{"info.lastVarDate" => "desc"}],
					"_source" => "info.lastVarDate"
				}
			);

			if (scalar @{$res->{hits}->{hits}}){

				my $elk_last_var_date = $res->{hits}->{hits}->[0]->{_source}->{info}->{lastVarDate};

				my $delta_var_date = $api->_dbh()->fetch_minimalized("
					select trunc(((
							select max(data_ult_varstat)
							from v_attivita
							where nome_Tipo_attivita = ".$api->_dbh->quote($uri_params{'TYPE'})."
						)- to_date(to_char(to_timestamp_tz(".$api->_dbh->quote($elk_last_var_date).", ".$api->_dbh()->quote($api->get_default_iso_date_format_oracle)."),".$api->_dbh()->quote($api->get_default_date_format)."),".$api->_dbh()->quote($api->get_default_date_format)."))*60*60*24)
					from dual
				");

				if ($delta_var_date <= $ENV{ELASTICSEARCH_SYNC_DELTA_TIME}){
					$use_legacy_mode = 0; # in questo caso utilizzo ELK
					my $body_query = {
						query => $p{query},
					};

					# $body_query->{_source} = [ "info.status" ];
					# size a 0 viene impostato perchè non ci servono i singoli risultati ma solo la parte aggregata
					$body_query->{size} = 0;
					# nlla sezione aggs è indispesabile definire la size perchè altrimenti vengono restituiti solo i record più numerosi
					# quindi gli impongo il numero totale degli stati presenti nel tipo_attivita
					my $act_type_status = $api->enum_activity_status(ACTIVITY_TYPE_NAME => $uri_params{'TYPE'});
		
					$body_query->{aggs} = {
						"group_by_status" => {
							"terms" => {
								"field" => "info.status.keyword",
								"order" => {
									"_key" => "asc"
								},
								"size" => scalar keys %$act_type_status
							}
						}
					};

					# use Data::Dumper;
					# print STDERR to_json($body_query)."\n";

					# print STDERR "cerco con ELK\n";
					my $res = $es->search(
						index => $index,
						body => $body_query
					);

					#print STDERR Dumper $res->{aggregations}->{group_by_status}->{buckets};
					
					my $tmp_res;
					for (@{$res->{aggregations}->{group_by_status}->{buckets}}){
						push @{$tmp_res}, {
							"count"		=> $_->{doc_count},
							"label"		=> $_->{key},
							"context"	=> $_->{key},
						}
					}
					
					$acts = $tmp_res;
				} else {
					my $msg = "[".$uri_params{'TYPE'}."] Exceeded ELASTICSEARCH_SYNC_DELTA_TIME => expected less than ".$ENV{ELASTICSEARCH_SYNC_DELTA_TIME}.", found ".$delta_var_date; 
					warning $msg;

					last ELK_SEARCH;
				}
			}
		}
	}
	
	if ($use_legacy_mode){

		# cerco solo le attività active del cliente
		$acts = $collActs->find_dashboards_summary(%{$find_params});
		
		return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
			unless defined $acts;
	}
	return send_ok(MSG => $acts);
	
#	my $finalQuery;
#	my @a = @{$acts};
#	my $x = scalar @{$acts};
#	
#	# se non ci sono attività non faccio nulla
#	return send_ok(MSG => []) if $x == 0;
#
#	# la find_id potrebbe restituire più di 1000 attività facendo scoppiare la query
#	# quindi la gestisco con le 'union all'
#	
#	while ($x > 0) {
#		if ($x > 1000){
#			@a = splice (@{$acts}, (scalar @{$acts}> 1000 ? 1000 : scalar @{$acts}));
#		}
#		
#		if ($x < 1000){
#			$finalQuery.= $t." and activity_id in (".join(',', @a).")";
#			last; 
#		} else {
#			$finalQuery.= $t." and activity_id in (".join(',', @{$acts}).")";
#			$finalQuery.= "
#				union all
#			";
#			$x = scalar @a;
#			@{$acts} = @a;
#		}
#	}
#	
#	$finalQuery= "
#		select es.\"context\"
#		, (select st.descrizione from stati st where st.nome = es.\"context\") \"label\"
#		, es.\"count\"
#		from (
#			select \"context\"
#				, sum(\"count\") \"count\"
#			from (
#				".$finalQuery."
#			) t
#			group by t.\"context\"
#		) es
#		order by es.\"context\"
#	";
#	
#	my $res = $api->_dbh()->fetchall_hashref($finalQuery);
#	
#	return send_ok(MSG => $res);
}

get '/dashboards/:OBJECT_TYPE/:TYPE' => sub {
	
	my $api = vars->{api_art};
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return handler_dashboards_OBJECT_TYPE_TYPE_get(QUERY_PARAMS => \%query_params, URI_PARAMS => \%{params('route')});
	
};

######## ROUTE /dashboards/:OBJECT_TYPE/:TYPE/:CONTEXT ###########

options '/dashboards/:OBJECT_TYPE/:TYPE/:CONTEXT' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };

=pod
@api {get} /dashboards/:OBJECT_TYPE/:TYPE/:CONTEXT Data
@apiName dashboardsData
@apiGroup Dashboards
@apiDescription Get dashboard data for :OBJECT_TYPE, :TYPE and :CONTEXT

@apiParam {Number=0,1} [tracking=0] <code>1</code> created by the user, <code>0</code> all activities.
@apiParam {Number=0,1} [mine=0] <code>1</code> owner by the user, <code>0</code> all activities.
@apiParam {String[]=[-]id,[-]type,[-]creationDate,[-]disablingDate,[-]endingDate,[-]description,[-]sp_NAME} [sort] If present, it define sort fields. If the field is prefixed by <code>-</code> the sort order is descending.

 example:
```
sort=type&sort=-id&sort=-sp_COMPETENCE_CERTER
```
@apiParam {Number} [limit] Maximum number of results
@apiParam {Number} [skip] Skip the first <code>skip</code> records from the the results set

@apiExample {HTTP} Example usage:
GET /dashboards/activities/WO/01_pre_tec HTTP/1.1

@apiSuccess {Number} count The total count of the objects found
@apiSuccess {Object} results Results
@apiSuccess {Object} results.data Data in SIRTI::Reports Extended format
@apiSuccess {Object} results.header Header in SIRTI::Reports Extended format

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
  "count":1,
  "results":{
    "data":[
      {
        "info_creationDate_2":"2017-07-26T16:21:59.*********+02:00",
        "properties_requestContext_19":null,
        "info_agingObj_lastVarDate_7":null,
        "properties_permitsAreaId_14":"3",
        "properties_requestType_15":"As-Built",
        "info_isLocked_12":0,
        "info_description_1":"test",
        "info_lastVarDate_4":"2017-09-26T16:11:48.*********+02:00",
        "info_agingObj_seconds_6":null,
        "info_creationUserObj_username_10":"ROOT",
        "info_creationUserObj_fullName_3":"ROOT ROOT",
        "info_currentUserObj_fullName_9":"ROOT ROOT",
        "properties_authority_17":"Società GAS",
        "properties_projectId_13":"862",
        "info_currentUserObj_username_11":"ROOT",
        "properties_requestor_18":null,
        "info_interval_8":"+413 17:24",
        "properties_asBuiltId_16":null,
        "id_0":5739,
        "info_ownerObj_fullName_5":"ROOT ROOT"
      }
    ],
    "header":[
      {
        "name":"Id",
        "position":0,
        "path":"[\"id\"]",
        "description":"Id",
        "type":"number",
        "hidden":false,
        "checkboxFilter":false,
        "id":"id_0"
      },
      {
        "name":"Description",
        "position":1,
        "path":"[\"info\"][\"description\"]",
        "description":"Description",
        "type":"string",
        "hidden":false,
        "checkboxFilter":false,
        "id":"info_description_1"
      },
      {
        "name":"Creation Date",
        "position":2,
        "path":"[\"info\"][\"creationDate\"]",
        "description":"Creation Date",
        "type":"date",
        "hidden":false,
        "checkboxFilter":false,
        "id":"info_creationDate_2"
      },
      {
        "name":"Created By",
        "position":3,
        "path":"[\"info\"][\"creationUserObj\"][\"fullName\"]",
        "description":"Created By",
        "type":"string",
        "hidden":false,
        "checkboxFilter":false,
        "id":"info_creationUserObj_fullName_3"
      },
      {
        "name":"Last Modify Date",
        "position":4,
        "path":"[\"info\"][\"lastVarDate\"]",
        "description":"Last Modify Date",
        "type":"date",
        "hidden":false,
        "checkboxFilter":false,
        "id":"info_lastVarDate_4"
      },
      {
        "name":"Modified By",
        "position":5,
        "path":"[\"info\"][\"ownerObj\"][\"fullName\"]",
        "description":"Modified By",
        "type":"string",
        "hidden":false,
        "checkboxFilter":false,
        "id":"info_ownerObj_fullName_5"
      },
      {
        "name":"Aging",
        "position":6,
        "path":"[\"info\"][\"agingObj\"][\"seconds\"]",
        "description":"Aging",
        "type":"number",
        "hidden":false,
        "checkboxFilter":false,
        "id":"info_agingObj_seconds_6"
      },
      {
        "name":"Last aging update date",
        "position":7,
        "path":"[\"info\"][\"agingObj\"][\"lastVarDate\"]",
        "description":"Last aging update date",
        "type":"date",
        "hidden":false,
        "checkboxFilter":false,
        "id":"info_agingObj_lastVarDate_7"
      },
      {
        "name":"Interval",
        "position":8,
        "path":"[\"info\"][\"interval\"]",
        "description":"Interval",
        "type":"string",
        "hidden":false,
        "checkboxFilter":false,
        "id":"info_interval_8"
      },
      {
        "name":"Managed By",
        "position":9,
        "path":"[\"info\"][\"currentUserObj\"][\"fullName\"]",
        "description":"Managed By",
        "type":"string",
        "hidden":false,
        "checkboxFilter":false,
        "id":"info_currentUserObj_fullName_9"
      },
      {
        "name":"CreationUserLogin",
        "position":10,
        "path":"[\"info\"][\"creationUserObj\"][\"username\"]",
        "description":"CreationUserLogin",
        "type":"string",
        "hidden":true,
        "checkboxFilter":false,
        "id":"info_creationUserObj_username_10"
      },
      {
        "name":"CurrentUserLogin",
        "position":11,
        "path":"[\"info\"][\"currentUserObj\"][\"username\"]",
        "description":"CurrentUserLogin",
        "type":"string",
        "hidden":true,
        "checkboxFilter":false,
        "id":"info_currentUserObj_username_11"
      },
      {
        "name":"IsLocked",
        "position":12,
        "path":"[\"info\"][\"isLocked\"]",
        "description":"IsLocked",
        "type":"number",
        "hidden":true,
        "checkboxFilter":false,
        "id":"info_isLocked_12"
      },
      {
        "name":"projectId",
        "position":13,
        "path":"[\"properties\"][\"projectId\"]",
        "description":"projectId",
        "type":"string",
        "hidden":false,
        "checkboxFilter":false,
        "id":"properties_projectId_13"
      },
      {
        "name":"permitsAreaId",
        "position":14,
        "path":"[\"properties\"][\"permitsAreaId\"]",
        "description":"permitsAreaId",
        "type":"string",
        "hidden":false,
        "checkboxFilter":false,
        "id":"properties_permitsAreaId_14"
      },
      {
        "name":"requestType",
        "position":15,
        "path":"[\"properties\"][\"requestType\"]",
        "description":"requestType",
        "type":"string",
        "hidden":false,
        "checkboxFilter":false,
        "id":"properties_requestType_15"
      },
      {
        "name":"asBuiltId",
        "position":16,
        "path":"[\"properties\"][\"asBuiltId\"]",
        "description":"asBuiltId",
        "type":"string",
        "hidden":false,
        "checkboxFilter":false,
        "id":"properties_asBuiltId_16"
      },
      {
        "name":"authority",
        "position":17,
        "path":"[\"properties\"][\"authority\"]",
        "description":"authority",
        "type":"string",
        "hidden":false,
        "checkboxFilter":false,
        "id":"properties_authority_17"
      },
      {
        "name":"requestor",
        "position":18,
        "path":"[\"properties\"][\"requestor\"]",
        "description":"requestor",
        "type":"string",
        "hidden":false,
        "checkboxFilter":false,
        "id":"properties_requestor_18"
      },
      {
        "name":"requestContext",
        "position":19,
        "path":"[\"properties\"][\"requestContext\"]",
        "description":"requestContext",
        "type":"string",
        "hidden":false,
        "checkboxFilter":false,
        "id":"properties_requestContext_19"
      }
    ]
  }
}

=cut

=pod
@api {get} /dashboards/:OBJECT_TYPE/:TYPE/:CONTEXT Data for export
@apiName dashboardsDataForExport
@apiGroup Dashboards
@apiDescription Get dashboard data for :OBJECT_TYPE, :TYPE and :CONTEXT for export. It accepts all the parameters of API <strong>Dashboards - Data</strong>, plus the parameter <code>export</code>.

@apiParam {String=xlsx,csv} export Set the format of the file to download
@apiParam {cache=1,0} [cache=1] If <code>0</code> instead of returning cached activities, it returns "fresh" activities 

@apiExample {HTTP} Example usage:
GET /dashboards/activities/WO/01_pre_tec?export=xlsx HTTP/1.1

@apiSuccess {File} anonymous The binary file.

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK
Content-Disposition: attachment; filename="export_20190401172524.xlsx"
Content-Length: 34114
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

=cut

sub handler_dashboards_OBJECT_TYPE_TYPE_CONTEXT_get{
	
	my %input_params = @_;
	
	my %query_params = defined $input_params{QUERY_PARAMS} ? %{$input_params{QUERY_PARAMS}} : ();
	
	my %uri_params = defined $input_params{URI_PARAMS} ? %{$input_params{URI_PARAMS}} : ();
	
	my $api = vars->{api_art};
	
	if (defined $query_params{export}) {
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {paramname} must be of type {type}", paramname => "export", type => "String"))
			if ref $query_params{export};
	
		# TRANSLATORS: es. Param values is a list of file extensions (eg. 'xlsx','json','csv')
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {paramname} can be {values}", paramname => "export", values => "'xlsx','csv'"))
			unless $query_params{export} =~ /^(xlsx|csv)$/;
		
	}
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Dashboards for ".$uri_params{'OBJECT_TYPE'}." not yet implemented"))
		if $uri_params{'OBJECT_TYPE'} ne 'activities';
	
	# prima prendo tutte i tipi attività indipendentemente dalla visibilità per capire se il tipo_attivita ha le network 
	my $activity_types = eval{$api->enum_activity_type(EXTENDED_OUTPUT => 1)};
	
	return send_ko(INTERNAL_ERROR_MSG => $@)
		if ($@);
	
	my $ok = 0;
	for my $ta (@{$activity_types}) {
		# considero solo il record relativo al tipo attività che mi è richiesto
		next if $ta->{ACTIVITY_TYPE_NAME} ne $uri_params{'TYPE'};
		$ok = $ta->{ACTIVITY_TYPE_HAS_DASHBOARD};
		last;
	}
	
	return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("Activity type does not provide dashboard"))
		unless $ok;

	# poi prendo solo i tipi attività su cui si ha visibilità
	$activity_types = eval{$api->enum_activity_type(EXTENDED_OUTPUT => 1, SHOW_ONLY_WITH_VISIBILITY => 1)};
	
	return send_ko(INTERNAL_ERROR_MSG => $@)
		if ($@);
	
	$ok = 0;
	for my $ta (@{$activity_types}) {
		# considero solo il record relativo al tipo attività che mi è richiesto
		next if $ta->{ACTIVITY_TYPE_NAME} ne $uri_params{'TYPE'};
		$ok = 1;
		last;
	}
	
	return send_ok(MSG => []) unless $ok;
	
	my $collActs = API::ART::Collection::Activity->new( ART => $api );
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless $collActs;
	
	my $res = {};
	
	# per la configurazione dell'header verifica prima quella custom
	my $header;
	$header = $api->get_entity_serialization(FORMAT => (defined $query_params{export} ? uc($query_params{export}) : 'JSON'));
	return send_ko(INTERNAL_ERROR_MSG => $api->last_error())
		unless defined $header;
	
	# se non la trova scala su quella di default
	if (! exists $header->{ACTIVITIES}->{$uri_params{'TYPE'}} ){
		my $activity_type_id = $api->get_activity_type_id($uri_params{'TYPE'});
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Invalid activity type name {type}", type => $uri_params{'TYPE'}))
			unless defined $activity_type_id;
		my $query = '
			select regexp_replace(regexp_replace(t.percorso,\'\["\',\'\'),\'"\]\',\'_\')||t.posizione "NAME"
				, t.etichetta HEADER
				, t.etichetta JSON_DESCRIPTION
				, t.tipo TYPE
				, t.percorso PATH
				, t.nascosto JSON_HIDDEN
				, t.posizione POSITION
				, null JSON_EXCLUDE
			from INTESTAZIONI_DASHBOARDS t
			where t.tipo_oggetto = \'ATTIVITA\'
			and t.id_oggetto = '.$activity_type_id.'
			order by t.posizione';
		
		# sovascrivo header tanto non mi serve
		$header = eval{$api->_dbh()->fetchall_hashref($query)};
		for my $h (@{$header}){
			$h->{POSITION} = $h->{POSITION}*1;
			for ('JSON_HIDDEN', 'JSON_EXCLUDE'){
				$h->{$_} = $h->{$_} ? $JSON::true : $JSON::false;
			}
		}
	} else {
		# sovascrivo header tanto non mi serve
		$header = $header->{ACTIVITIES}->{$uri_params{'TYPE'}};
	}
	
	# se non ho trovato l'header do un errore
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Configuration not found for object type ".$uri_params{'OBJECT_TYPE'}." and type ".$uri_params{'TYPE'})
		unless (scalar @{$header});
	
	$query_params{cache} = 1 unless defined $query_params{cache};
	
	my $use_legacy_mode = 1;

	my $tmp_ret = [];

	my $ret = {};

	if (
		$ENV{ELASTICSEARCH_SYNC_DELTA_TIME}
		&&
		$ENV{ELASTICSEARCH_NODES}
		&&
		$ENV{ELASTICSEARCH_INSTANCE_NAME}
		&& 
		$query_params{cache}
	) {

		# print STDERR "\n sono in ELK\n";

		my %p = ();
		$p{SKIP} = $query_params{skip} if defined($query_params{skip});
		$p{LIMIT} = $query_params{limit} if defined($query_params{limit});
		
		$p{EXTENDED_OUTPUT}	= 1;

		#$p{ACTIVE} = 1;
		$p{SHOW_ONLY_WITH_VISIBILITY} = 1;
		#$p{ACTIVITY_TYPE_NAME_EQUAL} = $uri_params{'TYPE'};
		#$p{STATUS_EQUAL} = $uri_params{'CONTEXT'};

		$query_params{active} = 1;
		$query_params{type_equal} = $uri_params{'TYPE'};
		$query_params{status_equal} = $uri_params{'CONTEXT'};

		my $es = _get_elasticsearch;

		ELK_SEARCH: {
			# verifico la presenza della configurazione
			# esiste chiamo ELK solo se è presente il tipo attività ed è abilitata la ricerca
			if (defined $elk_config_file){
				my @conf_elk_type = grep {$_->{name} eq $uri_params{'TYPE'}} @{$elk_config_file};
				unless (scalar @conf_elk_type){
					debug "Activity type ".$uri_params{'TYPE'}." not defined in ELK";
					last ELK_SEARCH;
				}
				unless ($conf_elk_type[0]->{ws}->{search}){
					debug "ELK search disabled for activity type ".$uri_params{'TYPE'};
					last ELK_SEARCH;
				}
			}

			my $ping = eval {$es->ping()};
			if ($@){
				my $msg = "Elasticsearch error in ping ".$@; 
				warning $msg;
				last ELK_SEARCH;
			}

			my $index = sprintf 'api-art-activity-%s-%s', $ENV{ELASTICSEARCH_INSTANCE_NAME}, lc($uri_params{'TYPE'});

			my $elk_mappings = vars->{elk_mappings};

			unless (exists ($elk_mappings->{$index})){
				# faccio una sola volta la chiamata per i mapping
				eval{
					$elk_mappings->{$index} = $es->indices->get(
						index => $index
					)->{$index};
				};
				last ELK_SEARCH if $@;
				var elk_mappings => $elk_mappings;
			}

			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
				unless defined _normalize_activities_search_filter_for_elastic(OBJ => \%p, QUERY_PARAMS => \%query_params, ELK_MAPPINGS => $elk_mappings->{$index});

			# verifico che ELK non sia troppo disallineato rispetto a Oracle
			my $res = $es->search(
				index => $index,
				body => {
					"size" => 1, 
					"query" => {
						"match_all" => {}
					},
					"sort" => [{"info.lastVarDate" => "desc"}],
					"_source" => "info.lastVarDate"
				}
			);

			if (scalar @{$res->{hits}->{hits}}){

				my $elk_last_var_date = $res->{hits}->{hits}->[0]->{_source}->{info}->{lastVarDate};

				my $delta_var_date = $api->_dbh()->fetch_minimalized("
					select trunc(((
							select max(data_ult_varstat)
							from v_attivita
							where nome_Tipo_attivita = ".$api->_dbh->quote($uri_params{'TYPE'})."
						)- to_date(to_char(to_timestamp_tz(".$api->_dbh->quote($elk_last_var_date).", ".$api->_dbh()->quote($api->get_default_iso_date_format_oracle)."),".$api->_dbh()->quote($api->get_default_date_format)."),".$api->_dbh()->quote($api->get_default_date_format)."))*60*60*24)
					from dual
				");

				if ($delta_var_date <= $ENV{ELASTICSEARCH_SYNC_DELTA_TIME}){
					$use_legacy_mode = 0; # in questo caso utilizzo ELK
					my $body_query = {
						query => $p{query},
					};

					my $res_count = $es->count(
						index => $index,
						body => $body_query
					);

					# print STDERR to_json($body_query)."\n";

					# se restituisce più record del massimo previsto restituisce un errore
					# in caso di export definisco un numero massimo di oggetti
					if (defined $p{LIMIT}) {
						$p{LIMIT} = $p{LIMIT} <= $ELASTICSEARCH_OBJECTS_EXPORT_LIMIT ? $p{LIMIT} : $ELASTICSEARCH_OBJECTS_EXPORT_LIMIT;
					} else {
						$p{LIMIT} = $ELASTICSEARCH_OBJECTS_EXPORT_LIMIT;
					}
					# il LIMIT richiesto non deve mai superare il massimo degli oggetti richiedibile da ELK
					if ($p{LIMIT} > $ELASTICSEARCH_OBJECTS_EXPORT_LIMIT){
						return send_ko(CODE => HTTP_NOT_ACCEPTABLE, ERROR_MSG => "Max records exceeded: requested ".$res_count->{count}.", max available ".$ELASTICSEARCH_OBJECTS_EXPORT_LIMIT);
					}

					$ret->{count} = $res_count->{count};

					my $visibility_property = $api->get_activity_property_visibility(ACTIVITY_TYPE_NAME => lc($uri_params{'TYPE'}));
					return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
						unless defined $visibility_property;
					$body_query->{_source} = [ "id" , "info", "system" ];
					if ($visibility_property->{ACTIVE}){
						for my $vp (@{$visibility_property->{PROPERTIES}}){
							push @{$body_query->{_source}}, "properties.".$vp->{NAME};
						}
					} else {
						push @{$body_query->{_source}}, "properties";
					}	

					$body_query->{from} = $query_params{skip} if defined($query_params{skip});
					$body_query->{size} = defined($query_params{limit}) ? $query_params{limit} : ($p{LIMIT}||$res_count->{count});

					if(defined $query_params{sort}) {
						$body_query->{sort} = _normalize_activities_sort_filter_for_elastic($query_params{sort}, $elk_mappings->{$index}) ;
						return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
							unless defined $body_query->{sort};
					} else {
						$body_query->{sort} = [
							{ "id" => "asc" }
						];
					}

					# use Data::Dumper;
					# print STDERR to_json($body_query)."\n";

					# print STDERR "cerco con ELK\n";
					my $res = $es->search(
						index => $index,
						body => $body_query
					);
					
					# gestico i campi che devono essere modificati
					# - le system properties array perche' in ELK sono memorizzate in una nuova chiave dedicata propertiesArray
					# - la chiave availableActions viene calcolata direttamente in tempo reale quando necessario
					if (
						(grep {$_ eq 'system'}@{$body_query->{_source}})
						||
						($query_params{includeAvailableActions} && grep {$_ eq 'id'}@{$body_query->{_source}})
					){
						for my $actSource (@{$res->{hits}->{hits}}){
							my $act = $actSource->{_source};
							if (grep {$_ eq 'system'}@{$body_query->{_source}}){
								if (exists ($act->{system}->{propertiesArray})){
									for my $propKey (keys %{$act->{system}->{propertiesArray}}){
										$act->{system}->{properties}->{$propKey} = $act->{system}->{propertiesArray}->{$propKey};
									}
								}
								delete $act->{system}->{propertiesArray};
							}
						}
					}

					$tmp_ret = [map {$_->{_source}} @{$res->{hits}->{hits}}];
				} else {
					my $msg = "[".$uri_params{'TYPE'}."] Exceeded ELASTICSEARCH_SYNC_DELTA_TIME => expected less than ".$ENV{ELASTICSEARCH_SYNC_DELTA_TIME}.", found ".$delta_var_date; 
					warning $msg;
					return send_ko(CODE => HTTP_SERVICE_UNAVAILABLE, ERROR_MSG => "Please retry later", INTERNAL_ERROR_MSG => $msg);

					last ELK_SEARCH;
				}
			}
		}
	}

	if ($use_legacy_mode){
		# print STDERR "\n sono in use_legacy_mode\n";

		my $find_params = {
			ACTIVE => 1
			, SHOW_ONLY_WITH_VISIBILITY => 1
			, ACTIVITY_TYPE_NAME_EQUAL => $uri_params{'TYPE'}
			, STATUS_EQUAL => $uri_params{'CONTEXT'}
			, EXTENDED_OUTPUT => 1
		};
		$find_params->{SKIP} = $query_params{skip} if defined($query_params{skip});
		$find_params->{LIMIT} = $query_params{limit} if defined($query_params{limit});
		if (defined $query_params{export}) {
			# in caso di export definisco un numero massimo di oggetti
			if (defined $find_params->{LIMIT}) {
				$find_params->{LIMIT} = $find_params->{LIMIT} <= $OBJECTS_EXPORT_LIMIT ? $find_params->{LIMIT} : $OBJECTS_EXPORT_LIMIT;
			} else {
				$find_params->{LIMIT} = $OBJECTS_EXPORT_LIMIT;
			}
		}
		if(defined $query_params{sort}) {
			$find_params->{SORT} = _normalize_activities_sort_filter($query_params{sort}) ;
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
				unless defined $find_params->{SORT};
		}
		$find_params->{CASE_SENSITIVE} = $query_params{caseSensitive} if defined($query_params{caseSensitive});
		
		# alcune chiavi non possono essere sovascritte
		for (
			'type',
			'type_equal',
			'type_in',
			'type_begins',
			'type_ends',
			'type_contains',
			'status_equal'.
			'status_in',
			'status_begins',
			'status_ends',
			'status_contains',
			'active',
			'showOnlyWithVisibility'
		){
			delete $query_params{$_} if exists $query_params{$_};
		}
		
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
			unless defined _normalize_activities_search_filter(OBJ => $find_params, QUERY_PARAMS => \%query_params);
		
		# cerco solo le attività active nello stato richiesto per cui l'utente ha visiblità
		my $acts = $collActs->find_object(%{$find_params});
		
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
			unless defined $acts;
		
		$ret->{count} = $acts->{COUNT};
		for my $act (@{$acts->{RESULTS}}){
			push @{$tmp_ret}, $act->dump();
		}
	}

	my $tmp_final = [];
	######
	for my $act (@{$tmp_ret}){
		my $dump = $act;
		# se ho il filtro sul tracking skippo se non l'ha creata l'utente e decremento il totale
		$ret->{count}--
			&& next
				if
					defined $query_params{'tracking'}
					&&
					$query_params{'tracking'} == 1
					&& 
					(
						! exists $dump->{"info"}->{"creationUserObj"}->{"username"}
						||
						$api->user()->name ne $dump->{"info"}->{"creationUserObj"}->{"username"}
					);
		
		# se ho il filtro sul mine skippo se non l'ha in carico l'utente e decremento il totale
		$ret->{count}--
			&& next
				if
					defined $query_params{'mine'}
					&&
					$query_params{'mine'} == 1
					&& 
					(
						! exists $dump->{"info"}->{"currentUserObj"}->{"username"}
						||
						$api->user()->name ne $dump->{"info"}->{"currentUserObj"}->{"username"}
					);
		
		push @{$tmp_final}, $dump;
	}
	
	my $reports = SIRTI::Reports->instance(DB => $api->_dbh());
	
	my $report_params = {
		 DATA => $tmp_final
		,COLUMNS => $header
	};
	
	if ($query_params{export}) {
		$report_params->{OUTPUT_FORMAT} = $query_params{export};
	} else { # json
		$report_params->{OUTPUT_FORMAT} = 'json';
		$report_params->{JSON_RETURN_STRING} = 1;
		$report_params->{JSON_USE_ID_INSTEAD_OF_NAME} = 1;
	}
						
	my $report = $reports->report(%{$report_params});
	
	unless (defined $report) {
		my $error = $reports->last_error();
		# distruggiamo l'oggetto singleton per evitare che se ci sono dei cursori aperti al suo interno, non permettano la disconnessione dal db
		undef $reports;
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $error);
	}
			
	# distruggiamo l'oggetto singleton per evitare che se ci sono dei cursori aperti al suo interno, non permettano la disconnessione dal db
	undef $reports;
	if ($query_params{export}) {
		# gestisco il download del file e return send_file_ok
		remove_in_hook_after( $report );
		return send_file_ok($report, system_path => 1, filename => 'export_'.$api->get_sysdate('YYYYMMDDHH24MISS').'.'.$query_params{export});
	} else {
		$ret->{results} = from_json($report);
	}
	
	return send_ok(MSG => $ret);
}

get '/dashboards/:OBJECT_TYPE/:TYPE/:CONTEXT' => sub {
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return handler_dashboards_OBJECT_TYPE_TYPE_CONTEXT_get(QUERY_PARAMS => \%query_params, URI_PARAMS => \%{params('route')});
	
};

######## ROUTE /reports ###########

options '/reports' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };

=pod
@api {get} /reports Reports overview
@apiName reportsOverview
@apiGroup Reports
@apiDescription Get reports overview

@apiExample {HTTP} Example usage:
GET /reports HTTP/1.1

@apiSuccess {Number} id The id of the report
@apiSuccess {String} type The type of the report: can be activities|custom
@apiSuccess {String} label The label of the report
@apiSuccess {String} description The description of the report
@apiSuccess {String} groupLabel The label of the cluster for the report
@apiSuccess {String} groupDescription The description of the cluster for the report


@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
    {
        "id": 93,
        "groupLabel":"A_TT",
        "groupDescription":"Gestione Trouble Ticket",
        "label":"TT",
        "type":"activities",
        "description":"Gestione Trouble Ticket"
    },
    {
        "id": 94,
        "groupLabel":"A_TT",
        "groupDescription":"Gestione Trouble Ticket",
        "label":"TT - Report avanzato",
        "type":"custom",
        "description":"Report TT avanzato"
    },
    {
        "id": 95,
        "groupLabel":"A_TTB",
        "groupDescription":"Trouble Ticket Blocccanti",
        "label":"TTB",
        "type":"activities",
        "description":"Trouble Ticket Blocccanti"
    }
]

=cut

get '/reports' => sub {
	my $api = vars->{api_art};
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	my $res = [];
	
# NB: NON RIMUOVERE QUESTA PARTE: verrà modificata quando permetteremo che solo alcuni report standard debbano essere visibili	
#	my $activity_types = eval{$api->enum_activity_type(EXTENDED_OUTPUT => 1, SHOW_ONLY_WITH_VISIBILITY => 1)};
#	
#	return send_ko(INTERNAL_ERROR_MSG => $@)
#		if ($@);
#	
#	my @activity_types_dashboards = grep {$_->{ACTIVITY_TYPE_HAS_REPORT} == 1} @{$activity_types};
#	
#	if (scalar @activity_types_dashboards){
#		my $acts = $api->_dbh()->fetchall_hashref("
#			select distinct ta.nome_tipo_attivita
#				, ta.descrizione
#				, att.id_tipo_attivita
#				, 'A_'||ta.nome_tipo_Attivita group_label
#				, nvl(rpt.descrizione,ta.descrizione) group_description
#			from attivita att
#				join tipi_attivita ta on ta.id_tipo_Attivita = att.id_tipo_attivita
#				join permission_sistemi ps on ps.id_sistema = att.id_sistema
#				left join rpt_gruppi rpt on rpt.label = 'A_'||ta.nome_tipo_attivita
#			where ta.nome_tipo_attivita in (".join (',', map {$api->_dbh()->quote($_->{ACTIVITY_TYPE_NAME})} @activity_types_dashboards).")
#				and ps.id_gruppo_abilitato in (".join (',', @{$api->user()->groups()}).")
#			order by 1
#		");
#		
#		
#		
#		for my $act (@{$acts}){
#			push @{$res}, {
#				type => 'activities',
#				description => $act->{DESCRIZIONE},
#				id => $act->{ID_TIPO_ATTIVITA}*1,
#				label => $act->{NOME_TIPO_ATTIVITA},
#				groupLabel => $act->{GROUP_LABEL},
#				groupDescription => $act->{GROUP_DESCRIPTION},
#			}
#		}
#	}
	
	my $customs = $api->_dbh()->fetchall_hashref("
		select *
        from (
            with p as (
                select distinct id_report
                from rpt_permessi
                where id_gruppo in (".join (',', @{$api->user()->groups()}).")
            )
            select r.label
                , r.descrizione
                , rg.label gruppo_label
                , rg.descrizione gruppo_descrizione
                , r.id_report id_report
                , 'custom' tipo
                , r.ordine
            from rpt r
                join p rp on rp.id_report = r.id_report
                left join rpt_gruppi rg on rg.id_gruppo = r.id_gruppo
        )
        union all
        select *
        from (
            with p as (
                select distinct id_report
                from rpt_auto_permessi
                where id_gruppo in (".join (',', @{$api->user()->groups()}).")
            )
            select r.label
                , r.descrizione
                , rg.label gruppo_label
                , rg.descrizione gruppo_descrizione
                , r.id_report id_report
                , 'auto' tipo
                , r.ordine
            from rpt_auto r
                join p rp on rp.id_report = r.id_report
                left join rpt_auto_gruppi rg on rg.id_gruppo = r.id_gruppo
        )
        order by 6 desc, 7 nulls last
	");
	
	for my $c (@{$customs}){
		push @{$res}, {
			type => $c->{TIPO},
			description => $c->{DESCRIZIONE},
			id => $c->{ID_REPORT}*1,
			label => $c->{LABEL},
			groupLabel => $c->{GRUPPO_LABEL},
			groupDescription => $c->{GRUPPO_DESCRIZIONE}
		}
	}
	
	my @sorted = sort { $a->{label} cmp $b->{label} } @{$res};
	
	return send_ok(MSG => \@sorted);
};

######## ROUTE /reports/:OBJECT_TYPE/:TYPE_ID ###########

options '/reports/:OBJECT_TYPE/:TYPE_ID' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };

=pod
@api {get} /reports/:OBJECT_TYPE/:TYPE_ID Report details
@apiName reportDetails
@apiGroup Reports
@apiDescription Get report details

@apiExample {HTTP} Example usage:
GET /reports/activities/78 HTTP/1.1

@apiParam {String=xlsx,json,csv} [format] Report format.
@apiParam {String} dateStart Filter report after this creation date.
@apiParam {String} [dateEnd] Filter report before this creation date.
  Default value: now.

@apiSuccess {File} anonymous The binary file if param 'format' is defined.
@apiSuccess {Number} count The count of the report if params 'format' is not defined.

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

{
    "data":[
        {
            "DESCRIZIONE_ANOMALIA":"asdfasd",
            "DATA_INSORGENZA_ALLARME":"2018-08-02T12:13:18.*********+02:00",
            "LAST_CHANGE_USER":"ROOT ROOT",
            "ACTIVITY_DESCRIPTION":"asdfasd",
            "PROTEZIONE":"SI",
            "STATUS":"DIAGNOSI_PRELIMINARE",
            "SITO_DESTINAZIONE":"MIJET",
            "CODICE_CLIENTE":"EASY00",
            "LOCAZIONE_DESTINAZIONE":"JET MULTIMEDIA SPA - VIA DELLA POSTA 8/10 - MILANO",
            "PROATTIVA":null,
            "DATA_PREVISTA_ATTIVITA":null,
            "MOT":"[\"EASY00ELI11040701\"]",
            "CODICE_TIPO_GUASTO":"IC",
            "LAST_CHANGE_DATE":"2018-08-02T12:13:19.*********+02:00",
            "LOCAZIONE_ORIGINE":"EASYNET SPA - V.LE FULVIO TESTI 7 - MILANO",
            "CREATION_USER":"ROOT ROOT",
            "CURRENT_USER":null,
            "PRIORITA_GUASTO":"Alta",
            "ID_CIRCUITO":"10002",
            "ACTIVITY_ID":7709,
            "CREATION_DATE":"2018-08-02T12:13:18.*********+02:00",
            "DATA_INIZIO_FDM":null,
            "SITO_ORIGINE":"MIEAS",
            "SYSTEM_ID":3440,
            "SYSTEM_NAME":"7709",
            "DATA_FINE_FDM":null,
            "DENOMINAZIONE_CLIENTE":"EASYNET (EASY00)",
            "CIRCUITI":"[\"MIEAS-MIJET-ETH-001\",\"MIEAS-MIJET-VC12-001(1/5)\",\"MIEAS-MIJET-VC12-002(2/5)\",\"MIEAS-MIJET-VC12-003(3/5)\",\"MIEAS-MIJET-VC12-004(4/5)\",\"MIEAS-MIJET-VC12-005(5/5)\"]"
        }
    ],
    "header":[
        {
            "name":"ACTIVITY_ID",
            "checkboxFilter":false,
            "hidden":false,
            "type":"number"
        },
        {
            "name":"ACTIVITY_DESCRIPTION",
            "checkboxFilter":false,
            "hidden":false,
            "type":"string"
        },
        {
            "name":"SYSTEM_ID",
            "checkboxFilter":false,
            "hidden":false,
            "type":"number"
        },
        {
            "name":"SYSTEM_NAME",
            "checkboxFilter":false,
            "hidden":false,
            "type":"string"
        },
        {
            "name":"STATUS",
            "checkboxFilter":false,
            "hidden":false,
            "type":"string"
        },
        {
            "name":"CREATION_USER",
            "checkboxFilter":false,
            "hidden":false,
            "type":"string"
        },
        {
            "name":"CREATION_DATE",
            "checkboxFilter":false,
            "hidden":false,
            "type":"date"
        },
        {
            "name":"LAST_CHANGE_USER",
            "checkboxFilter":false,
            "hidden":false,
            "type":"string"
        },
        {
            "name":"LAST_CHANGE_DATE",
            "checkboxFilter":false,
            "hidden":false,
            "type":"date"
        },
        {
            "name":"CURRENT_USER",
            "checkboxFilter":false,
            "hidden":false,
            "type":"string"
        },
        {
            "name":"DATA_PREVISTA_ATTIVITA",
            "checkboxFilter":false,
            "hidden":false,
            "type":"date"
        },
        {
            "name":"CODICE_TIPO_GUASTO",
            "checkboxFilter":false,
            "hidden":false,
            "type":"string"
        },
        {
            "name":"DESCRIZIONE_ANOMALIA",
            "checkboxFilter":false,
            "hidden":false,
            "type":"string"
        },
        {
            "name":"DATA_INSORGENZA_ALLARME",
            "checkboxFilter":false,
            "hidden":false,
            "type":"date"
        },
        {
            "name":"PRIORITA_GUASTO",
            "checkboxFilter":false,
            "hidden":false,
            "type":"string"
        },
        {
            "name":"PROATTIVA",
            "checkboxFilter":false,
            "hidden":false,
            "type":"string"
        },
        {
            "name":"CODICE_CLIENTE",
            "checkboxFilter":false,
            "hidden":false,
            "type":"string"
        },
        {
            "name":"DENOMINAZIONE_CLIENTE",
            "checkboxFilter":false,
            "hidden":false,
            "type":"string"
        },
        {
            "name":"ID_CIRCUITO",
            "checkboxFilter":false,
            "hidden":false,
            "type":"string"
        },
        {
            "name":"SITO_ORIGINE",
            "checkboxFilter":false,
            "hidden":false,
            "type":"string"
        },
        {
            "name":"LOCAZIONE_ORIGINE",
            "checkboxFilter":false,
            "hidden":false,
            "type":"string"
        },
        {
            "name":"SITO_DESTINAZIONE",
            "checkboxFilter":false,
            "hidden":false,
            "type":"string"
        },
        {
            "name":"LOCAZIONE_DESTINAZIONE",
            "checkboxFilter":false,
            "hidden":false,
            "type":"string"
        },
        {
            "name":"CIRCUITI",
            "checkboxFilter":false,
            "hidden":false,
            "type":"string"
        },
        {
            "name":"PROTEZIONE",
            "checkboxFilter":false,
            "hidden":false,
            "type":"string"
        },
        {
            "name":"MOT",
            "checkboxFilter":false,
            "hidden":false,
            "type":"string"
        },
        {
            "name":"DATA_INIZIO_FDM",
            "checkboxFilter":false,
            "hidden":false,
            "type":"date"
        },
        {
            "name":"DATA_FINE_FDM",
            "checkboxFilter":false,
            "hidden":false,
            "type":"date"
        }
    ]
}

=cut

get '/reports/:OBJECT_TYPE/:TYPE_ID' => sub {
	my $api = vars->{api_art};
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {paramname} must be an integer number", paramname => "TYPE_ID"))
		unless param('TYPE_ID') =~ /^\d+$/;
	
	for ('dateStart'){
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing mandatory param {paramname}", paramname => $_))
			unless defined $query_params{$_};
	}
	
	if (defined $query_params{format}){
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {paramname} must be of type {type}", paramname => "format", type => "String"))
			if ref $query_params{format};
	
		# TRANSLATORS: es. Param values is a list of file extensions (eg. 'xlsx','json','csv')
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {paramname} can be {values}", paramname => "format", values => "'xlsx','json','csv'"))
			unless $query_params{format} =~ /^(xlsx|json|csv)$/;
	}
	
	return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {paramname} must be of type {type}", paramname => "dateStart", type => "String"))
		if ref $query_params{dateStart};
	$query_params{dateStart} = $api->format_iso_date( $query_params{dateStart} );
	return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {paramname} must be a string in ISO format", paramname => "dateStart"))
		if ref $query_params{dateStart};

	return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {paramname} must be of type {type}", paramname => "dateEnd", type => "String"))
		if ref $query_params{dateEnd};
	
	$query_params{dateEnd} = $api->get_iso_date_from_date( $api->get_sysdate() ) unless defined $query_params{dateEnd};
	return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {paramname} must be a string in ISO format", paramname => "dateEnd"))
		if ref $query_params{dateEnd};
	
	my $reports = SIRTI::Reports->instance(DB => $api->_dbh());
	my $query;
	my $tipo_filtro;
	
	if (param('OBJECT_TYPE') eq 'activities'){
		
		# verifico che il report esista
		my $activity_types = eval{$api->enum_activity_type(EXTENDED_OUTPUT => 1, SHOW_ONLY_WITH_VISIBILITY => 1)};
		
		return send_ko(INTERNAL_ERROR_MSG => $@)
			if ($@);
		
		my $ok = 0;
		for my $ta (@{$activity_types}) {
			# considero solo il record relativo al tipo attività che mi è richiesto
			next if $ta->{ACTIVITY_TYPE_ID} ne param('TYPE_ID');
			$ok = $ta->{ACTIVITY_TYPE_HAS_REPORT};
			last;
		}
		
		return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("Activity type does not provide report"))
			unless $ok;
		
		my $dateStart = $api->get_date_from_iso_date( $query_params{dateStart}, 'YYYYMMDD' );
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Invalid dateStart: {error}", error => $api->last_error()))
			unless defined $dateStart;
		my $dateEnd = $api->get_date_from_iso_date( $query_params{dateEnd}, 'YYYYMMDD' );
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Invalid dateEnd: {error}", error => $api->last_error()))
			unless defined $dateEnd;

		$query = "
			select ".(defined $query_params{format} ? 'distinct xt.*' : 'count(distinct xt.activity_id) c')."
			from v_xt_".param('TYPE_ID')." xt
				join permission_sistemi ps on ps.id_sistema = xt.system_id
			where xt.creation_date between
				to_date(".$api->_dbh()->quote($dateStart)."||'000000','YYYYMMDDHH24MISS') and to_date(".$api->_dbh()->quote($dateEnd)."||'235959','YYYYMMDDHH24MISS')
				and ps.id_gruppo_abilitato in (".join (',', @{$api->user()->groups()}).")
		";
	} elsif (param('OBJECT_TYPE') =~/^(custom|auto)$/){
		
		# verifico che il report esista
		#$query = $api->_dbh()->fetch_minimalized("
		my $res = $api->_dbh()->fetchall_hashref("
			with p as (
				select distinct id_report
				from ".(param('OBJECT_TYPE') eq 'custom' ? 'rpt_permessi' : 'rpt_auto_permessi')."
				where id_gruppo in (".join (',', @{$api->user()->groups()}).")
			)
			select r.query, r.tipo_filtro
			from ".(param('OBJECT_TYPE') eq 'custom' ? 'rpt' : 'rpt_auto')." r
				join p rp on rp.id_report = r.id_report
			where r.id_report = ".param('TYPE_ID')."
		");
		
		$query = $res->[0]->{QUERY};
		$tipo_filtro = $res->[0]->{TIPO_FILTRO};
		
		return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("Custom report not found"))
			unless defined $query;
		
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing mandatory params {param} and {param1}", param => 'dateStart', param1 => 'dateEnd'))
			if $tipo_filtro eq 'CREATION_DATE' && (!defined $query_params{dateStart} || !defined $query_params{dateEnd});
		
		# recupero le colonne della query da eseguire
		my $prepare = $api->_dbh()->create_prepare($query);
		my $columns_name = $prepare->get_sth()->{NAME};

		my $keysForFilter = {
			ACTIVITY => [
				'ID_ATTIVITA',
				'ACTIVITY_ID',
				'ID',
				'Id',
				'activityId',
				'idAttivita',
			],
			SYSTEM => [
				'ID_SISTEMA',
				'SYSTEM_ID',
				'systemId',
				'idSistema',
			]
		};
		
		# prendo i gruppi dell'utente (NB: volutamente non prendo quelli 'sudati' perchè per i report non ha senso)
		my $permissionGroups = join (',', @{$api->user()->groups});
		my $permissionFilter = '';
		FIND_FILTER: {
			for my $k (@{$keysForFilter->{SYSTEM}}){
				if (grep {$_ eq $k} @{$columns_name} ){
					$permissionFilter = qq{
						and exists(
							select 1
							from permission_Sistemi ps
							where ps.id_sistema = x.$k
								and ps.id_gruppo_abilitato in ($permissionGroups)
						)
					};
					last FIND_FILTER;
				}
			}
			for my $k (@{$keysForFilter->{ACTIVITY}}){
				if (grep {$_ eq $k} @{$columns_name} ){
					$permissionFilter = qq{
						and exists(
							select 1
							from permission_Sistemi ps
							where ps.id_sistema = (
								Select id_sistema
								from attivita
								where id_attivita = x."$k"
							)
								and ps.id_gruppo_abilitato in ($permissionGroups)
						)
					};
					last FIND_FILTER;
				}
			}
		}

		if ($tipo_filtro eq 'CREATION_DATE'){
			# applico filtro su creation_date
			my $dateStart = $api->get_date_from_iso_date( $query_params{dateStart}, 'YYYYMMDD' );
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Invalid dateStart: {error}", error => $api->last_error()))
				unless defined $dateStart;
			my $dateEnd = $api->get_date_from_iso_date( $query_params{dateEnd}, 'YYYYMMDD' );
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Invalid dateEnd: {error}", error => $api->last_error()))
				unless defined $dateEnd;
			$query = "select ".(defined $query_params{format} ? '*' : 'count(1) c')."
				from (".$query.") x
				where x.creation_date between
					to_date(".$api->_dbh()->quote($dateStart)."||'000000','YYYYMMDDHH24MISS')
					and
					to_date(".$api->_dbh()->quote($dateEnd)."||'235959','YYYYMMDDHH24MISS')
					$permissionFilter
			";
		} elsif ($tipo_filtro eq 'INTERPOLA'){
			eval "require Template";
			if ($@){
				return send_ko(INTERNAL_ERROR_MSG =>  __x("Filter type {type} not supported: missing class Template", type => $tipo_filtro));
			}
			my $template = Template->new({ABSOLUTE => 1});
			
			my ($fh, $file) = tempfile( DIR => $ENV{ART_REPOSITORY_TMP_TMPFILES});
			
			print $fh $query;
			close $fh;
			
			my $output = '';
			unless ($template->process($file,\%query_params, \$output)){
				unlink $file;
				return send_ko(INTERNAL_ERROR_MSG => $template->error());
			} else {
				unlink $file;
				$query = $output;
			}
			if (defined $query_params{format}){
				$query = "select * from (".$query.") where 1=1 $permissionFilter";
			} else {
				$query = "select count(1) c from (".$query.") where 1=1 $permissionFilter";
			}
			
		} else {
			return send_ko(INTERNAL_ERROR_MSG =>  __x("Filter type {type} not supported", type => $tipo_filtro));
		}
		
	} else {
		return send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => __("OBJECT_TYPE not availabel"));
	}
	
	my $res;
	my $report;
	
	# modifico NLS_NUMERIC_CHARACTERS se diverso da '.,' e poi eventualmente lo ripristino
	my $old_NLS_NUMERIC_CHARACTERS = $api->_dbh()->get_session_parameters('NLS_NUMERIC_CHARACTERS')->{NLS_NUMERIC_CHARACTERS};
	if ($old_NLS_NUMERIC_CHARACTERS ne '.,'){
		$api->_dbh()->set_session_parameters('NLS_NUMERIC_CHARACTERS' => '.,');
	} else {
		undef $old_NLS_NUMERIC_CHARACTERS;
	}

	$report = $reports->report(
		 QUERY => $query
		,OUTPUT_FORMAT => (defined $query_params{format} ? $query_params{format} : 'json')
		,ENCODING => 'utf8'
		,JSON_RETURN_STRING => (defined $query_params{format} ? 0 : 1)
		,JSON_DATA_AS_ARRAY => (defined $query_params{format} ? 0 : 1)
		# NB: serve solo per i csv
		# FIXME: implementare i formati per le varie lingue: per ora italiano o fallback in inglese
		,SQL_DATE_FORMAT_STRING => (vars->{'code_locale'} eq 'it_IT' ? 'dd/mm/yyyy hh24:mi:ss' : 'mm/dd/yyyy hh24:mi:ss') 
	);

	if (defined $old_NLS_NUMERIC_CHARACTERS){
		$api->_dbh()->set_session_parameters('NLS_NUMERIC_CHARACTERS' => $old_NLS_NUMERIC_CHARACTERS);
	}

	unless (defined $report) {
		my $error = $reports->last_error();
		# distruggiamo l'oggetto singleton per evitare che se ci sono dei cursori aperti al suo interno, non permettano la disconnessione dal db
		undef $reports;
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $error);
	}

	# distruggiamo l'oggetto singleton per evitare che se ci sono dei cursori aperti al suo interno, non permettano la disconnessione dal db
	undef $reports;
	
	if (defined $query_params{format}){
		remove_in_hook_after( $report );
		
		return send_file_ok($report, system_path => 1, filename => param('TYPE_ID').'_report_'.$api->get_sysdate('YYYYMMDDHH24MISS').'.'.(defined $query_params{format} ? $query_params{format} : '.count'));
	} else {
		my $st = from_json($report);
		return send_ok(MSG => {count => $st->{data}->[0]->[0]})
	}
};

######## ROUTE /graphs/recap/:TYPE ###########

options '/graphs/recap/:TYPE' => sub { handle_cors_request( METHODS => [ 'GET' ] ); return send_ok(IGNORE_SESSION => 1); };

=pod
@api {get} /graphs/recap/:TYPE Graphs
@apiName Graphs
@apiGroup Recap
@apiDescription Get recap graphs

@apiExample {HTTP} Example usage:
GET /graphs/recap/WO HTTP/1.1

@apiParam {Number} [days=5] The number of days for which to get statistics before today.

@apiSuccess {Object[]} anonymous Sets of the graphs.
@apiSuccess {String} anonymous.key Label for the graph
@apiSuccess {Number[]} anonymous.values Array of two items: the first one is the <code>x</code> (timestamp in unix time), the second one is the <code>y</code> (count) value for the graph

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
  {
    "values": [
      [
        1450224000000,
        0
      ],
      [
        1450310400000,
        2
      ],
      [
        1450396800000,
        0
      ],
      [
        1450483200000,
        0
      ],
      [
        1450569600000,
        0
      ],
      [
        1450656000000,
        0
      ]
    ],
    "key": "APERTE"
  }
]

=cut

get '/graphs/recap/:TYPE' => sub {
	
	my $api = vars->{api_art};
	
	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	$query_params{days} = 5 unless defined $query_params{days};
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {paramname} cannot be an Array", paramname => "days"))
		if ref $query_params{days};

	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {paramname} must be an integer number greater or equal than zero", paramname => "days"))
		unless $query_params{days} =~ /^\d+$/;
	
	my $struct = [
		{
			key => 'APERTURE'
			,query => "
				with aa as (
				  select trunc(sa.data_esecuzione) data_esecuzione, 
				         count(1) n
				  from  v_attivita a
				        inner join v_storia_attivita sa ON a.id = sa.id_attivita
				  where a.NOME_TIPO_ATTIVITA = ".$api->_dbh()->quote(param('TYPE'))."
				    and sa.data_esecuzione > sysdate - ".$query_params{days}."
				    and sa.azione = 'APERTURA'
				  group by trunc(sa.data_esecuzione)
				)
				SELECT (cal.day-to_date('19700101','yyyymmdd'))*86400*1000 x, 
				       nvl(sum(aa.n),0) y
				FROM TABLE( f_get_calendar( sysdate - ".$query_params{days}.", sysdate)) cal 
				     left join aa on aa.data_esecuzione = cal.day
				group by cal.day
				ORDER BY cal.day
			"
		}
		,{
			key => 'MOVIMENTAZIONI'
			,query => "
				with aa as (
				  select trunc(sa.data_esecuzione) data_esecuzione, 
				         count(1) n
				  from  v_attivita a
				        inner join v_storia_attivita sa ON a.id = sa.id_attivita
				        inner join stati s on s.nome = sa.stato_risultante
				  where a.NOME_TIPO_ATTIVITA = ".$api->_dbh()->quote(param('TYPE'))."
				    and sa.data_esecuzione > sysdate - ".$query_params{days}."
				    and sa.azione not in ('APERTURA')
				    and s.flag_stato_partenza not in ('C', 'Q')
				  group by trunc(sa.data_esecuzione)
				)
				SELECT (cal.day-to_date('19700101','yyyymmdd'))*86400*1000 x, 
				       nvl(sum(aa.n),0) y
				FROM TABLE( f_get_calendar( sysdate - ".$query_params{days}.", sysdate)) cal 
				     left join aa on aa.data_esecuzione = cal.day
				group by cal.day
				ORDER BY cal.day
			"
		}
		,{
			key => 'CHIUSURE'
			,query => "
				with aa as (
				  select trunc(sa.data_esecuzione) data_esecuzione, 
				         count(1) n
				  from  v_attivita a
				        inner join v_storia_attivita sa ON a.id = sa.id_attivita
				        inner join stati s on s.nome = sa.stato_risultante
				  where a.NOME_TIPO_ATTIVITA = ".$api->_dbh()->quote(param('TYPE'))."
				    and sa.data_esecuzione > sysdate - ".$query_params{days}."
				    and s.flag_stato_partenza in ('C', 'Q')
				  group by trunc(sa.data_esecuzione)
				)
				SELECT (cal.day-to_date('19700101','yyyymmdd'))*86400*1000 x, 
				       nvl(sum(aa.n),0) y
				FROM TABLE( f_get_calendar( sysdate - ".$query_params{days}.", sysdate)) cal
				     left join aa on aa.data_esecuzione = cal.day
				group by cal.day
				ORDER BY cal.day
			"
		}
	];
	
	my $reports = SIRTI::Reports->instance(DB => $api->_dbh());
	
	my $res = [];
	
	for my $elem (@{$struct}){
	
		my $report = $reports->report(
			 QUERY => $elem->{query}
			,OUTPUT_FORMAT => 'json'
			,ENCODING => 'utf8'
			,JSON_RETURN_STRING => 1
			,JSON_DATA_AS_ARRAY => 1
		);
	
		unless (defined $report) {
			my $error = $reports->last_error();
			# distruggiamo l'oggetto singleton per evitare che se ci sono dei cursori aperti al suo interno, non permettano la disconnessione dal db
			undef $reports;
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $error);
		}
		
		my $st = from_json($report);
		
		push @{$res}, {
			values => $st->{data}
			, key => $elem->{key}
		};
		
	}	
	# distruggiamo l'oggetto singleton per evitare che se ci sono dei cursori aperti al suo interno, non permettano la disconnessione dal db
	undef $reports;

	return send_ok(MSG => $res);

};

######## ROUTE /tmpfiles ###########

options '/tmpfiles' => sub { handle_cors_request( METHODS => [ 'POST' ] ); return send_ok(IGNORE_SESSION => 1); };

### FIXME: implementare trash collection!!!!

=pod
@api {post} /api/art/tmpfiles Upload temporary file(s)
@apiName createTmpfile
@apiGroup Tmpfile
@apiDescription Upload temporary file(s) that can be used, for example, to attach to an activity and/or to a step

@apiHeader {String} Content-Type It must be set to <code>multipart/form-data</code>. 

@apiExample {HTTP} Example usage:
POST /api/art/tmpfiles HTTP/1.1
Accept: application/json
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

----WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="attachment"; filename="importazione.docx"
Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document


----WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="attachment"; filename="esportazione.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet


----WebKitFormBoundary7MA4YWxkTrZu0gW

@apiParam {File[]} attachment Files to upload.

@apiSuccess {String[]} id Id of the uploaded file.

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK

[
  "xTalgqtjzg.docx",
  "7wmWlBzH4D.xlsx"
]
=cut

post '/tmpfiles' => sub {

	my $api = vars->{api_art};
	
	my $token = get_sid();
	
	my $ret = [];
	for my $f ( upload('attachment') ) {

		my $basename = $f->basename;
		unless (Encode::is_utf8($basename)){
			$basename =SIRTI::ART::CGI::Charset::encode_utf8($basename);
		}
		
		# questo file temporaneo serve per effettuare  la scansione antivirus se abilitata, altrimenti viene subito rinominato
		my ($fhTemp, $tmpfile) = tempfile( DIR => $ENV{ART_REPOSITORY_TMP_TMPFILES});
		close $fhTemp;
		return send_ko(INTERNAL_ERROR_MSG => "Unable to move file from ".$f->tempname." to " . $tmpfile . ": " . $!)
			unless move($f->tempname, $tmpfile);


		## virus scan ##
		debug "Scanning file ".$tmpfile." for virus";
		
		unless(chmod 0644, $tmpfile) {
			unlink $tmpfile;
			return send_ko(INTERNAL_ERROR_MSG => "Unable to chmod $basename: ".$!);
		}
		my ($av, $file, $virus);
		if ($ENV{CLAMAV_HOST} && $ENV{CLAMAV_PORT}){
			my $data;
			if(open(my $fh, $tmpfile)){
					local $/;
					$data = <$fh>;
					close($fh);
			} else {
					return send_ko(INTERNAL_ERROR_MSG => "Unable to read file: $tmpfile $!");
			}
			$av = new File::Scan::ClamAV(host => $ENV{CLAMAV_HOST}, port => $ENV{CLAMAV_PORT});
			($file, $virus) = $av->streamscan($data);
		} else {
			$av = new File::Scan::ClamAV(port => $ENV{CLAMAV_DAEMON_PORT});
			($file, $virus) = $av->scan($tmpfile);
		}
		if(defined $av->errstr()) {
			unlink $tmpfile;
			return send_ko(INTERNAL_ERROR_MSG => "Unable to scan for viruses $basename: ".$av->errstr());
		}
		if(defined $virus) {
			unlink $tmpfile;
			return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Found virus ".$virus." on file ".$basename);
		}

		my $name = $token."_".basename($f->tempname);;
		my $idx_file = $name.".idx";

		return send_ko(INTERNAL_ERROR_MSG => "Unable to move file from ".$f->tempname." to " . $tmpfile . ": " . $!)
			unless move($tmpfile, $ENV{ART_REPOSITORY_TMP_TMPFILES}."/".$name);

		unless(open WFD, '>'.$ENV{ART_REPOSITORY_TMP_TMPFILES}."/".$idx_file) {
			unlink $ENV{ART_REPOSITORY_TMP_TMPFILES}."/".$name;
			return send_ko(INTERNAL_ERROR_MSG => "Unable to create idx file ".$idx_file.": ".$!);
		}

		binmode WFD, ':utf8';
	
		my $content_type;
		my $mimetype = $mt->mimeTypeOf($ENV{ART_REPOSITORY_TMP_TMPFILES}."/".$name);
		if(defined $mimetype) {
			$content_type = $mimetype->type;
		} else {
			$content_type = "octet/stream";
		}

		print WFD to_json({filename => $basename, content_type => $content_type});
		
		close WFD;
		
		push @{$ret}, basename($f->tempname);
	}
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Missing file"))
		unless scalar @$ret;
	
	return send_ok(MSG => $ret);
	
};

######## ROUTE /tmpfiles/:ID ###########

options '/tmpfiles/:ID' => sub { handle_cors_request( METHODS => [ 'GET', 'DELETE', 'PUT' ] ); return send_ok(IGNORE_SESSION => 1); };

=pod
@api {get} /api/art/tmpfiles/:ID Download temporary file
@apiName downloadTmpfile
@apiGroup Tmpfile
@apiDescription Download temporary file

@apiExample {HTTP} Example usage:
GET /api/art/tmpfiles/cJ9jvFO0zu.xls HTTP/1.1

@apiSuccess {File} anonymous The binary file.

@apiSuccessExample Success-Response:
HTTP/1.1 200 OK
Content-Disposition: attachment; filename="esportazione.xlsx"
Content-Length: 34114
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

=cut

get '/tmpfiles/:ID' => sub {
	
	my $filename = $ENV{ART_REPOSITORY_TMP_TMPFILES}."/".get_sid()."_".param('ID');
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Not existent attachment", INTERNAL_ERROR_MSG => "Not existent attachment: $filename")
		unless -f $filename && -f $filename.".idx";
		
	my $fileidx = from_json(read_file_content($filename.".idx"));
	
	send_file_ok($filename, system_path => 1, filename => $fileidx->{filename}, content_type => $fileidx->{content_type});
	
};

=pod
@api {delete} /api/art/tmpfiles/:ID Delete temporary file
@apiName deleteTmpfile
@apiGroup Tmpfile
@apiDescription Delete temporary file

@apiExample {HTTP} Example usage:
DELETE /api/art/tmpfiles/cJ9jvFO0zu.xls HTTP/1.1

@apiSuccessExample Success-Response:
HTTP/1.1 204 No content

=cut

del '/tmpfiles/:ID' => sub {
	
	my $filename = $ENV{ART_REPOSITORY_TMP_TMPFILES}."/".get_sid()."_".param('ID');
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Not existent attachment", INTERNAL_ERROR_MSG => "Not existent attachment: $filename")
		unless -f $filename && -f $filename.".idx";
	
	return send_ko(INTERNAL_ERROR_MSG => "Unable to unlink $filename: $!")
		unless unlink($filename);
	
	return send_ko(INTERNAL_ERROR_MSG => "Unable to unlink $filename.idx: $!")
		unless unlink($filename.".idx");
	
	return send_ok(CODE => HTTP_NO_CONTENT);
	
};

=pod
@api {put} /api/art/tmpfiles/:ID Update temporary file
@apiName updateTmpfile
@apiGroup Tmpfile
@apiDescription Update temporary file info

@apiUse ContentTypeJsonHeader

@apiExample {HTTP} Example usage:
PUT /api/art/tmpfiles/cJ9jvFO0zu.xls
Content-Type: application/json

{
  "title" : "Titolo",
  "description": "Descrizione",
  "revision": "1.1",
  "refDate": "2019-01-01"
  "docType": "Altro",
}

@apiParam {String} [title] The title of the temporary file
@apiParam {String} [description] The description of the temporary file
@apiParam {String} [revision] The revision reference of the temporary file
@apiParam {String} [refDate] The date reference of the temporary file in the format YYYY-MM-DD
@apiParam {String} [docType] The document type of the temporary file

@apiSuccessExample Success-Response:
HTTP/1.1 204 No content

=cut

put '/tmpfiles/:ID' => sub {

	my $api = vars->{api_art};
	
	my %body = request->params('body');
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
		unless (keys %body);
	
	my @keysUpdate = ('title', 'description', 'revision', 'refDate', 'docType');
	
	for my $k (keys %body){
		return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Unable to update param {param}", param => $k))
			unless grep {$k eq $_} @keysUpdate;
	}
	
	# controllo la data che mi viene passata
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Invalid param {param}", param => 'refDate'))
		if (defined $body{refDate} && $body{refDate} ne "" && $api->_dbh()->test_date($body{refDate}, 'YYYY-MM-DD') ne '');
	
	my $filename = $ENV{ART_REPOSITORY_TMP_TMPFILES}."/".get_sid()."_".param('ID');
	
	my $idx_file = $filename.".idx";
	
	return send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Not existent attachment", INTERNAL_ERROR_MSG => "Not existent attachment: $filename")
		unless -f $filename && -f $idx_file;
		
	my $file_idx_content = from_json(read_file_content($idx_file));
	
	for my $k (keys %body){
		$file_idx_content->{$k} = $body{$k}	
	}
	
	unless(open WFD, '>'.$idx_file) {
		return send_ko(INTERNAL_ERROR_MSG => "Unable to create idx file ".$idx_file.": ".$!);
	}

	binmode WFD, ':utf8';

	print WFD to_json($file_idx_content);
	
	close WFD;
	
	return send_ok(CODE => HTTP_NO_CONTENT);
	
};

any qr{.*} => sub {
	return send_ko(CODE => HTTP_NOT_FOUND);
};


if (__FILE__ eq $0) {
	set port => 10081;
	setting log_path => $ENV{HOME}."/tmp/dancer_log";
	set logger => "console";
	set log => 'debug';
	dance;
}





1;
