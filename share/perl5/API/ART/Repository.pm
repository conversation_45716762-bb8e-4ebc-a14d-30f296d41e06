package API::ART::Repository;

use strict;
use warnings;
use Carp;
use Data::Dumper;

use SIRTI::ART::Events;
use base qw(API::Ancestor);

our $VERSION = '0.01';


=head1 NAME

B<API::ART::Repository> - Gestione repository

=head1 SYNOPSIS

	# Uses needed packages
	use API::ART;
	use API::ART::Repository;

	# Create API::ART instance...
	my $art = API::ART->new(.......);
	# Create API::ART::Repository instance...
	my $repo = API::ART::Repository->new(ART => $art);

	if ($@) {
		die "API::ART::Repository error: $@";
	}

	my $res= $repo->get_by_activity_transition(
		ID => 66135,
		TRANSITION_ID => '20211019105005',
		SEQUENCE => 0,
	);

	if( defined $res) {
		print STDERR "get_by_activity_transition: OK"."\n";
		print STDERR (Dumper $res)."\n";
	} else {
		print STDERR "get_by_activity_transition Error: " . $art->last_error()."\n";
		#die;
	}

	$res= $repo->set_activity_permission_group(
		ID => 66135,
		TRANSITION_ID => '20211019105005',
		SEQUENCE => 0,
		GROUPS => [4678]
	);

	if($res) {
		print STDERR "set_activity_permission_group: OK"."\n";
	} else {
		print STDERR "set_activity_permission_group Error: " . $art->last_error()."\n";
		die;
	}

	$res= $repo->unset_activity_permission_group(
		ID => 66135,
		TRANSITION_ID => '20211019105005',
		SEQUENCE => 0,
		GROUPS => [4678]
	);

	if($res) {
		print STDERR "unset_activity_permission_group: OK"."\n";
	} else {
		print STDERR "unset_activity_permission_group Error: " . $art->last_error()."\n";
		die;
	}

=head1 DESCRIPTION

Questo package consente di accedere al repository di ART attraverso un'interfaccia OO.


=head1 METHODS

Di seguito i metodi esposti dalla classe API::ART::Repository.

=cut

#
# Metodi privati
#



=head2 I<API::ART::Repository>->B<new>( ART => I<API::ART> )

Il metodo B<new()> e' il costruttore di classe e richiede due argomenti obbligatori:

=over 4

=item B<ART>

Un'istanza della classe API::ART

=back

=cut
sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	#
	# ART
	#
	# Sintassi
	my $usage = sub {
		my $errmsg = shift;
		my $msg    = "";
		$msg .= "$class : $errmsg\n\n" if defined $errmsg;
		$msg .= "Usage:\n";
		$msg .= "\t$class";
		$msg .= '->new( ART => API::ART->new() )';
		$msg .= "\n\n";
		croak $msg;
	};

	# Controlli sui parametri
	$usage->('Missing ART!') unless ref($params->{ART}) eq 'API::ART';
	
	my $self  = $class->SUPER::new();
	
	$self->{ART} = $params->{ART};
	$self->{ART}->clear_last_error();

	return bless( $self, $class );
}


=head2 I<object>->B<art>()

Ritorna il riferimento all'oggetto API::ART passato come argomento al costruttore.

=cut
sub art { return $_[0]->{ART} }

=head2 I<object>->B<can_get_by_activity_transition>( ID=>I<scalar> , TRANSITION_ID=>I<scalar>, SEQUENCE=>I<scalar> )

Verifica se l'utente può recuperare l'allegato

Di seguito il significato degli argomenti del metodo B<can_get_by_activity_transition()>:

=over 4

=item B<ID>

Id attività

=item B<TRANSITION_ID>

Identificativo della transizione

=item B<SEQUENCE>

Posizione dell'allegato nella transizione

=back

Restituisce 1 in caso positivo, 0 con last_error in caso negativo

=cut

sub _info_get_by_activity_transition{
	my $self = shift;
	my %params = @_;

	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				ERRMSG				=> \$errmsg
				,PARAMS				=> \%params
				,MANDATORY			=> {
					ID				=> { isa => 'SCALAR', pattern => qr{^\d+$} }
					,TRANSITION_ID	=> { isa => 'SCALAR', pattern => qr{^\d{14}$} }
					,SEQUENCE		=> { isa => 'SCALAR', pattern => qr{^\d+$} }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);

	my $sql = "
		select 
			(select s.id_tipo_sistema from sistemi s where s.id_sistema = att.id_Sistema) ID_TIPO_SISTEMA,
			att.id_tipo_attivita,
			(
				select id_gruppo_abilitato
				from (
				select ps.id_gruppo_Abilitato
				from permission_sistemi ps
				where ps.id_sistema = att.id_sistema
				and ps.id_Gruppo_Abilitato in (".join(',', map {'?'} @{$self->art()->user()->groups()}).")
				union
				select apg.id_Gruppo
				from allegati_permessi_gruppi apg
				where apg.id_allegato = aa.id_allegato
				and apg.id_Gruppo in (".join(',', map {'?'} @{$self->art()->user()->groups()}).")
				and apg.data_rimozione is null
				)
				where rownum<2
			)
			id_gruppo_abilitato,
			aa.nome_file_servizio,
			aa.nome_file_client,
			aa.dimensione
		from attivita att
			join allegati_azione aa on aa.id_attivita = att.id_attivita
		where att.id_attivita = ?
			and aa.id_action = to_date(?,'yyyymmddhh24miss') -- formato cablato in quando si tratta della TRANSITION_ID
			and aa.id_allegato = ?
			and aa.data_cancellazione is null
	";

	my $prepare = $self->art()->_create_prepare(__PACKAGE__.'_gbat'.(scalar @{$self->art()->user()->groups()}), $sql);

	my @bind_params = (
		@{$self->art()->user()->groups()},
		@{$self->art()->user()->groups()},
		$params{ID},
		$params{TRANSITION_ID},
		$params{SEQUENCE},
	);

	my $res = $prepare->fetchall_hashref(@bind_params);

	unless ($res) {
		$self->art()->last_error($self->art()->_dbh()->get_errormessage());
		return undef;
	}

	unless (defined $res->[0]){
		$self->art()->last_error("Attachment with sequence ".$params{SEQUENCE}." not present");
		return undef;
	}

	unless (defined $res->[0]->{ID_GRUPPO_ABILITATO}){
		$self->art()->last_error("Attachment not available for user");
		return undef;
	}

	return $res->[0];

}

sub can_get_by_activity_transition{
	my $self = shift;
	my %params = @_;

	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				ERRMSG				=> \$errmsg
				,PARAMS				=> \%params
				,MANDATORY			=> {
					ID				=> { isa => 'SCALAR', pattern => qr{^\d+$} }
					,TRANSITION_ID	=> { isa => 'SCALAR', pattern => qr{^\d{14}$} }
					,SEQUENCE		=> { isa => 'SCALAR', pattern => qr{^\d+$} }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);

	my $sql = "
		select 
			(
				select id_gruppo_abilitato
				from (
				select ps.id_gruppo_Abilitato
				from permission_sistemi ps
				where ps.id_sistema = att.id_sistema
				and ps.id_Gruppo_Abilitato in (".join(',', map {'?'} @{$self->art()->user()->groups()}).")
				union
				select apg.id_Gruppo
				from allegati_permessi_gruppi apg
				where apg.id_allegato = aa.id_allegato
				and apg.id_Gruppo in (".join(',', map {'?'} @{$self->art()->user()->groups()}).")
				and apg.data_rimozione is null
				)
				where rownum<2
			)
			id_gruppo_abilitato
		from attivita att
			join allegati_azione aa on aa.id_attivita = att.id_attivita
		where att.id_attivita = ?
			and aa.id_action = to_date(?,'yyyymmddhh24miss') -- formato cablato in quando si tratta della TRANSITION_ID
			and aa.id_allegato = ?
			and aa.data_cancellazione is null
	";

	my $prepare = $self->art()->_create_prepare(__PACKAGE__.'_cgbat'.(scalar @{$self->art()->user()->groups()}), $sql);

	my @bind_params = (
		@{$self->art()->user()->groups()},
		@{$self->art()->user()->groups()},
		$params{ID},
		$params{TRANSITION_ID},
		$params{SEQUENCE},
	);

	my $res = $prepare->fetchall_hashref(@bind_params);

	unless ($res) {
		$self->art()->last_error($self->art()->_dbh()->get_errormessage());
		return undef;
	}

	unless (defined $res->[0]){
		$self->art()->last_error("Attachment with sequence ".$params{SEQUENCE}." not present");
		return undef;
	}

	unless (defined $res->[0]->{ID_GRUPPO_ABILITATO}){
		$self->art()->last_error("Attachment not available for user");
		return undef;
	}

	return 1;

}

=head2 I<object>->B<get_by_activity_transition>( ID=>I<scalar> , TRANSITION_ID=>I<scalar>, SEQUENCE=>I<scalar> )

Recupera l'allegato

Di seguito il significato degli argomenti del metodo B<get_by_activity_transition()>:

=over 4

=item B<ID>

Id attività

=item B<TRANSITION_ID>

Identificativo della transizione

=item B<SEQUENCE>

Posizione dell'allegato nella transizione

=back

Restituisce le seguenti chiavi

=over 4

=item B<FILE>

File handle

=item B<FILENAME>

Nome file

=item B<SIZE>

Dimensione file

=back

=cut

sub get_by_activity_transition{
	my $self = shift;
	my %params = @_;

	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				ERRMSG				=> \$errmsg
				,PARAMS				=> \%params
				,MANDATORY			=> {
					ID				=> { isa => 'SCALAR', pattern => qr{^\d+$} }
					,TRANSITION_ID	=> { isa => 'SCALAR', pattern => qr{^\d{14}$} }
					,SEQUENCE		=> { isa => 'SCALAR', pattern => qr{^\d+$} }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);

	return undef unless $self->can_get_by_activity_transition(
		ID				=> $params{ID}
		,TRANSITION_ID	=> $params{TRANSITION_ID}
		,SEQUENCE		=> $params{SEQUENCE}
	);

	my $info = $self->_info_get_by_activity_transition(
		ID				=> $params{ID}
		,TRANSITION_ID	=> $params{TRANSITION_ID}
		,SEQUENCE		=> $params{SEQUENCE}
	);
	return undef unless defined $info;

	my $events = SIRTI::ART::Events->new(
		DB => $self->art()->_dbh(),
		ID_TIPO_SISTEMA => $info->{ID_TIPO_SISTEMA},
		ID_TIPO_ATTIVITA => $info->{ID_TIPO_ATTIVITA},
		ID_GRUPPO_ABILITATO => $info->{ID_GRUPPO_ABILITATO},
	);

	my $path = $ENV{TMP}||$ENV{TMPDIR}||'/tmp';

	my $files = [];
	my $rc = $events->detach_attachments($params{ID}, $params{TRANSITION_ID}, $files, $path, (EMIT_INPUT_FD => 1,FILENAME => $info->{NOME_FILE_SERVIZIO}));
	$self->art()->last_error($rc)
		and return undef
			if $rc ne "";
	
	return {
		FILE => $files->[0],
		FILENAME => $info->{NOME_FILE_CLIENT},
		SIZE => $info->{DIMENSIONE}
	};

}

=head2 I<object>->B<set_activity_permission_group>( ID=>I<scalar> , TRANSITION_ID=>I<scalar>, SEQUENCE=>I<scalar>, GROUPS=>I<arrayref> )

Imposta i permessi sul singolo allegato dato un elenco di gruppi

Di seguito il significato degli argomenti del metodo B<set_activity_permission_group()>:

=over 4

=item B<ID>

Id attività

=item B<TRANSITION_ID>

Identificativo della transizione

=item B<SEQUENCE>

Posizione dell'allegato nella transizione

=item B<GROUPS>

Elenco degli ID_GRUPPO da abilitare alla visibilità dell'allegato

=back

Restituisce 1 in caso di successo, undef con last_error impostato in caso di errore

=cut

sub set_activity_permission_group{
	my $self = shift;
	my %params = @_;

	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				ERRMSG				=> \$errmsg
				,PARAMS				=> \%params
				,MANDATORY			=> {
					ID				=> { isa => 'SCALAR', pattern => qr{^\d+$} }
					,TRANSITION_ID	=> { isa => 'SCALAR', pattern => qr{^\d{14}$} }
					,SEQUENCE		=> { isa => 'SCALAR', pattern => qr{^\d+$} }
					,GROUPS			=> { isa => 'ARRAY' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);

	my $sql = "
		select 
			aa.id_allegato
		from allegati_azione aa
		where aa.id_attivita = ?
			and aa.id_action = to_date(?,'yyyymmddhh24miss') -- formato cablato in quando si tratta della TRANSITION_ID
			and aa.id_allegato = ?
			and aa.data_cancellazione is null
	";

	my $prepare = $self->art()->_create_prepare(__PACKAGE__.'_spag', $sql);

	my @bind_params = (
		$params{ID},
		$params{TRANSITION_ID},
		$params{SEQUENCE},
	);

	my $res = $prepare->fetchall_hashref(@bind_params);

	unless ($res) {
		$self->art()->last_error($self->art()->_dbh()->get_errormessage());
		return undef;
	}

	unless (defined $res->[0]){
		$self->art()->last_error("Attachment with sequence ".$params{SEQUENCE}." not present");
		return undef;
	}

	my $sql_insert = "
		insert into allegati_permessi_gruppi (
			id_allegato,
			id_gruppo,
			id_operatore_assegnazione
		)
		select ?,
			?,
			?
		from dual
		where not exists(
			select 1
			from allegati_permessi_gruppi apg
			where apg.id_allegato = ?
			and id_gruppo = ?
			and apg.data_rimozione is null
		)
	";

	my $prepare_insert = $self->art()->_create_prepare(__PACKAGE__.'_spagi', $sql_insert);

	my $savepoint = 'API__ART__Repository_spagi';
	$self->art()->_dbh()->do("savepoint $savepoint");

	for my $g (@{$params{GROUPS}}){
		unless ($self->art()->test_group_id($g)){
			$self->art()->last_error('Unknown group '.$g);
			$self->art()->_dbh()->do("rollback to savepoint $savepoint");
			return undef;
		}
		@bind_params = (
			$params{SEQUENCE},
			$g,
			$self->art()->user()->id(),
			$params{SEQUENCE},
			$g,
		);

		my $r = $prepare_insert->do(@bind_params);

		unless ($r) {
			$self->art()->last_error($self->art()->_dbh()->get_errormessage());
			$self->art()->_dbh()->do("rollback to savepoint $savepoint");
			return undef;
		}

	}

	return 1;

}

=head2 I<object>->B<unset_activity_permission_group>( ID=>I<scalar> , TRANSITION_ID=>I<scalar>, SEQUENCE=>I<scalar>, GROUPS=>I<arrayref> )

Rimuove i permessi sul singolo allegato dato un elenco di gruppi

Di seguito il significato degli argomenti del metodo B<unset_activity_permission_group()>:

=over 4

=item B<ID>

Id attività

=item B<TRANSITION_ID>

Identificativo della transizione

=item B<SEQUENCE>

Posizione dell'allegato nella transizione

=item B<GROUPS>

Elenco degli ID_GRUPPO da rimuovere alla visibilità dell'allegato

=back

Restituisce 1 in caso di successo, undef con last_error impostato in caso di errore

=cut

sub unset_activity_permission_group{
	my $self = shift;
	my %params = @_;

	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				ERRMSG				=> \$errmsg
				,PARAMS				=> \%params
				,MANDATORY			=> {
					ID				=> { isa => 'SCALAR', pattern => qr{^\d+$} }
					,TRANSITION_ID	=> { isa => 'SCALAR', pattern => qr{^\d{14}$} }
					,SEQUENCE		=> { isa => 'SCALAR', pattern => qr{^\d+$} }
					,GROUPS			=> { isa => 'ARRAY' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);

	my $sql = "
		select 
			aa.id_allegato
		from allegati_azione aa
		where aa.id_attivita = ?
			and aa.id_action = to_date(?,'yyyymmddhh24miss') -- formato cablato in quando si tratta della TRANSITION_ID
			and aa.id_allegato = ?
			and aa.data_cancellazione is null
	";

	my $prepare = $self->art()->_create_prepare(__PACKAGE__.'_spag', $sql);

	my @bind_params = (
		$params{ID},
		$params{TRANSITION_ID},
		$params{SEQUENCE},
	);

	my $res = $prepare->fetchall_hashref(@bind_params);

	unless ($res) {
		$self->art()->last_error($self->art()->_dbh()->get_errormessage());
		return undef;
	}

	unless (defined $res->[0]){
		$self->art()->last_error("Attachment with sequence ".$params{SEQUENCE}." not present");
		return undef;
	}

	my $sql_remove = "
		update allegati_permessi_gruppi
		set id_operatore_rimozione = ?
			, data_rimozione = systimestamp
		where id_allegato = ?
			and id_gruppo = ?
			and data_rimozione is null
	";

	my $prepare_remote = $self->art()->_create_prepare(__PACKAGE__.'_upag', $sql_remove);

	my $savepoint = 'API__ART__Repository_upag';
	$self->art()->_dbh()->do("savepoint $savepoint");

	for my $g (@{$params{GROUPS}}){
		unless ($self->art()->test_group_id($g)){
			$self->art()->last_error('Unknown group '.$g);
			$self->art()->_dbh()->do("rollback to savepoint $savepoint");
			return undef;
		}
		@bind_params = (
			$self->art()->user()->id(),
			$params{SEQUENCE},
			$g,
		);

		my $r = $prepare_remote->do(@bind_params);
		unless ($r) {
			$self->art()->last_error($self->art()->_dbh()->get_errormessage());
			$self->art()->_dbh()->do("rollback to savepoint $savepoint");
			return undef;
		}

	}

	return 1;

}

=head2 I<object>->B<can_delete_by_activity_transition>( ID=>I<scalar> , TRANSITION_ID=>I<scalar>, SEQUENCE=>I<scalar> )

Verifica se l'utente può cancellare "logicamente" l'allegato

Di seguito il significato degli argomenti del metodo B<can_delete_by_activity_transition()>:

=over 4

=item B<ID>

Id attività

=item B<TRANSITION_ID>

Identificativo della transizione

=item B<SEQUENCE>

Posizione dell'allegato nella transizione

=back

Restituisce 1 in caso positivo, 0 con last_error in caso negativo

=cut

sub can_delete_by_activity_transition{
	my $self = shift;
	my %params = @_;

	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				ERRMSG				=> \$errmsg
				,PARAMS				=> \%params
				,MANDATORY			=> {
					ID				=> { isa => 'SCALAR', pattern => qr{^\d+$} }
					,TRANSITION_ID	=> { isa => 'SCALAR', pattern => qr{^\d{14}$} }
					,SEQUENCE		=> { isa => 'SCALAR', pattern => qr{^\d+$} }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);

	my $sql = "
		select 
			sa.id_operatore
		from attivita att
			join allegati_azione aa on aa.id_attivita = att.id_attivita
			join storia_attivita sa on sa.id_attivita = att.id_Attivita and aa.id_Action = sa.data_esecuzione
		where att.id_attivita = ?
			and aa.id_action = to_date(?,'yyyymmddhh24miss') -- formato cablato in quando si tratta della TRANSITION_ID
			and aa.id_allegato = ?
			and aa.data_cancellazione is null
	";

	my $prepare = $self->art()->_create_prepare(__PACKAGE__.'_cdbat', $sql);

	my @bind_params = (
		$params{ID},
		$params{TRANSITION_ID},
		$params{SEQUENCE},
	);

	my $res = $prepare->fetch_minimalized(@bind_params);

	unless ($res) {
		$self->art()->last_error($self->art()->_dbh()->get_errormessage());
		return undef;
	}

	unless (defined $res){
		$self->art()->last_error("Attachment with sequence ".$params{SEQUENCE}." not present");
		return undef;
	}

	unless ($self->art()->user()->is_admin()){
		if ($res ne $self->art()->user()->id()){
			$self->art()->last_error("User not authorized to delete attachment");
			return undef;
		}
	}

	return 1;

}

=head2 I<object>->B<delete_by_activity_transition>( ID=>I<scalar> , TRANSITION_ID=>I<scalar>, SEQUENCE=>I<scalar> )

Rimuove l'allegato

Di seguito il significato degli argomenti del metodo B<delete_by_activity_transition()>:

=over 4

=item B<ID>

Id attività

=item B<TRANSITION_ID>

Identificativo della transizione

=item B<SEQUENCE>

Posizione dell'allegato nella transizione

=back

Restituisce 1 in caso positivo, 0 con last_error in caso negativo

=cut

sub delete_by_activity_transition{
	my $self = shift;
	my %params = @_;

	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				ERRMSG				=> \$errmsg
				,PARAMS				=> \%params
				,MANDATORY			=> {
					ID				=> { isa => 'SCALAR', pattern => qr{^\d+$} }
					,TRANSITION_ID	=> { isa => 'SCALAR', pattern => qr{^\d{14}$} }
					,SEQUENCE		=> { isa => 'SCALAR', pattern => qr{^\d+$} }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);

	return undef unless $self->can_delete_by_activity_transition(
		ID				=> $params{ID}
		,TRANSITION_ID	=> $params{TRANSITION_ID}
		,SEQUENCE		=> $params{SEQUENCE}
	);

	# se può cancellare l'allegato significa che o è admin o l'allegato è stato caricato dallo stesso operatore
	# che lo sta cancellando, quindi sicuramente vede l'attività

	my $activity = eval {API::ART::Activity::Factory->new(ART => $self->art(), ID => $params{ID})};
	if ($@){
		$self->art()->last_error($@);
		return undef;
	}

	return undef
		unless defined $activity;

	return $activity->delete_attachment(
		TRANSITION_ID => $params{TRANSITION_ID}
		,SEQUENCE => $params{SEQUENCE}
	)

}

if (__FILE__ eq $0) {

	use API::ART;

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => $ENV{ART_SCRIPT_USER},
			PASSWORD => $ENV{ART_SCRIPT_PASSWORD}
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $repo = eval{
		API::ART::Repository->new(ART => $art);
	};

	if ($@) {
		die "API::ART::Repository error: $@";
	}

	my $res= $repo->get_by_activity_transition(
		ID => 66135,
		TRANSITION_ID => '20211019105005',
		SEQUENCE => 0,
	);

	if( defined $res) {
		print STDERR "get_by_activity_transition: OK"."\n";
		print STDERR (Dumper $res)."\n";
	} else {
		print STDERR "get_by_activity_transition Error: " . $art->last_error()."\n";
		#die;
	}

	$res= $repo->set_activity_permission_group(
		ID => 66135,
		TRANSITION_ID => '20211019105005',
		SEQUENCE => 0,
		GROUPS => [4678]
	);

	if($res) {
		print STDERR "set_activity_permission_group: OK"."\n";
	} else {
		print STDERR "set_activity_permission_group Error: " . $art->last_error()."\n";
		die;
	}

	# $res= $repo->unset_activity_permission_group(
	# 	ID => 66135,
	# 	TRANSITION_ID => '20211019105005',
	# 	SEQUENCE => 0,
	# 	GROUPS => [4678]
	# );

	# if($res) {
	# 	print STDERR "unset_activity_permission_group: OK"."\n";
	# } else {
	# 	print STDERR "unset_activity_permission_group Error: " . $art->last_error()."\n";
	# 	die;
	# }

	$art->cancel();
}


1;

