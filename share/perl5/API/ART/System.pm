package API::ART::System::Property::BLOB;

use strict;
use warnings;
use Carp;  # qw/verbose croak/;
use JSON;
use API::ART; #service per la base di API::ART::Types::PropertyFile;

use MIME::Base64;
use MIME::Types;
use Compress::Bzip2 qw/:utilities/;

use base 'API::Ancestor';

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = {};
	for ('SYSTEM', 'PROPERTY', 'VALUE'){
		croak "Missing mandatory params $_"
			unless defined $params->{$_};
	}
	$self->{__system} = $params->{SYSTEM};
	$self->{__property} = $params->{PROPERTY};
	$self->{__raw} = $params->{VALUE};
	my $k = from_json($params->{VALUE});
	$self->{__filename} = $k->{f};	# nome del file all'interno del repordsitory sul filesystem 
	$self->{__format} = $k->{o};	# formato di compressione
	$self->{__hash} = $k->{h};		# hash del blob Base64
	$self->{__date} = $k->{d};		# data del salvataggio
	$self->{__mime} = $k->{m};		# mime-type del file decodificato da Base64
	$self->{__name} = $k->{n};		# filename con estensione per download
	return bless($self, $class);
}

sub art { $_[0]->system()->art(); }
sub system { $_[0]->{__system}; }
sub property { $_[0]->{__property}; }
sub	filename { $_[0]->{__filename}; }
sub fullpathname { $_[0]->art()->system_blobs_repository() . $_[0]->filename(); }
sub	format { $_[0]->{__format}; }
sub	hash { $_[0]->{__hash}; }
sub	date { $_[0]->{__date}; }
sub	mime { $_[0]->{__mime}; }
sub	name { $_[0]->{__name}; }
sub fh {
	my $self = shift;
	unless ($self->{__fh}) {
		if ($self->format() eq 'bzip2') {
			open $self->{__fh}, "bzip2 -dc '" . $self->fullpathname() . "' |";
		} else {
			$self->art()->last_error("Unknown format '" . $self->format() . "' for BLOB");
			return undef;
		}
	}
	return $self->{__fh};
}
sub fh_decoded {
	my $self = shift;
	unless ($self->{__fh_decoded}) {
		if ($self->format() eq 'bzip2') {
			open $self->{__fh_decoded}, "bzip2 -dc '" . $self->fullpathname() . "' | base64 -d |";
		} else {
			$self->art()->last_error("Unknown format '" . $self->format() . "' for BLOB");
			return undef;
		}
	}
	return $self->{__fh_decoded};
}
sub DESTROY {
	my $self = shift;
	close $self->{__fh}
		if defined $self->{__fh};
	close $self->{__fh_decoded}
		if defined $self->{__fh_decoded};
}
sub	dump {
	my $self = shift;
	my $bz = bzopen($self->fullpathname(), 'r');
	$self->art()->last_error("Unable to open BLOB " . $self->property() . " for system " . $self->system()->id())
		and return undef
			unless $bz;
	my $blob = '';	
	my $buf = '';
	while (1) {
		my $bytesread = $bz->bzread($buf);
		if ($bytesread > 0) {
			$blob .= $buf;
		} else {
			last;
		}
	}
	$bz->bzclose();
	return $blob;
}
sub dump_decoded {
	my $self = shift;
	return decode_base64($self->dump());
}
sub raw { $_[0]->{__raw}; } # il valore utilizzato quando la property viene richiamata senza EXTENDED_OUTPUT


=head1 NAME

B<API::ART::System::DT::File> - Gestione dati tecnici di tipo file

=head1 SYNOPSIS

	# Uses needed packages
	use API::ART;
	use API::ART::System;
	use API::ART::System::DT::File;

	# Create API::ART instance...
	my $art = API::ART->new(.......);
	# Create API::ART::System instance...
	my $s = API::ART::System->new(ART => $art, ID => 349522);
	
	my $dt_file = API::ART::System::DT::File->new(SYSTEM => $s, ID_DATO_TECNICO => 123) 

	my $filename = $dt_file->get_filename();
	
	my $filehandle = $dt_file->get_filehandle();

=head1 DESCRIPTION

Questo package consente di accedere ai dati tecnici sistema di tipo file

=head1 METHODS

Di seguito i metodi esposti dalla classe API::ART::System::DT::File.

=cut

package API::ART::System::DT::File;

use strict;
use warnings;
use Carp;  # qw/verbose croak/;
use API::ART; #service per la base di API::ART::Types::PropertyFile;

use base 'API::ART::Types::PropertyFile';

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = {};
	for ('SYSTEM', 'ID_DATO_TECNICO'){
		croak "Missing mandatory params $_"
			unless defined $params->{$_};
	}
	
	my $sql_repo = "
		select VALORE ART_REPOSITORY
		from CONFIG_ART
		where CHIAVE = 'ART.REPOSITORY'
	";
	
	$params->{SYSTEM}->art()->_create_prepare(__PACKAGE__.'_repo', $sql_repo);
	
	my $repos = $params->{SYSTEM}->art()->_create_prepare(__PACKAGE__.'_repo')->fetch_minimalized();
	
	croak "Unable to find repository"
			unless defined $repos;
	
	my $sql_filename = "
		select dt.descrizione
		from dati_Tecnici dt
			join tipi_Dati_Tecnici tdt on dt.id_tipo_Dato_Tecnico = tdt.id_tipo_Dato_Tecnico
		where tdt.flagfile = 'Y'
			and id_sistema = ?
			and id_dato_Tecnico = ?
	";
	
	$params->{SYSTEM}->art()->_create_prepare(__PACKAGE__.'_filename', $sql_filename);
	
	my $filename =  $params->{SYSTEM}->art()->_create_prepare(__PACKAGE__.'_filename')->fetch_minimalized($params->{SYSTEM}->id(), $params->{ID_DATO_TECNICO});
	
	croak "Unable to find filename"
			unless defined $filename;
	
	my $dir=$repos.'/inventario/'.substr("0".$params->{ID_DATO_TECNICO},-2);
	
	$self = $this->SUPER::new(ART => $params->{SYSTEM}->art(), INTERNAL_FILENAME => $dir."/".$params->{ID_DATO_TECNICO}, FILENAME => $filename);
	
	return bless ($self, $class);
}

=head2 I<object>->B<get_filename>()

Restituisce il nome file del dato tecnico

=cut

sub get_filename(){
	shift->{FILENAME};
}

=head2 I<object>->B<get_filehandle>()

Restituisce il file handle del dato tecnico

=cut

sub get_filehandle(){
	shift->{HANDLE};
}

###########################################################################################

=head1 NAME

B<API::ART::System::DT::Measure> - Gestione dati tecnici di tipo measure

=head1 SYNOPSIS

	# Uses needed packages
	use API::ART;
	use API::ART::System;
	use API::ART::System::DT::File;

	# Create API::ART instance...
	my $art = API::ART->new(.......);
	# Create API::ART::System instance...
	my $s = API::ART::System->new(ART => $art, ID => 349522);
	
	my $dt_meas = API::ART::System::DT::Measure->new(SYSTEM => $s, ID_DATO_TECNICO => 123, VALUE => '12.5', TS = '2019-05-31T11:00:00.000000000+02:00') 

	my $value = $dt_meas->get_value();
	
	my $ts = $dt_meas->get_ts();

=head1 DESCRIPTION

Questo package consente di accedere ai dati tecnici sistema di tipo measure

=head1 METHODS

Di seguito i metodi esposti dalla classe API::ART::System::DT::Measure.

=cut

package API::ART::System::DT::Measure;

use strict;
use warnings;
use Carp;

use base 'API::Ancestor';

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = {};
	for ('SYSTEM', 'ID_DATO_TECNICO', 'VALUE'){
		croak "Missing mandatory params $_"
			unless defined $params->{$_};
	}

	for ('VALUE'){
		$self->{$_} = $params->{$_};
	}

	$self->{PROPERTY_ID} = $params->{ID_DATO_TECNICO};

	if (defined $params->{TS}){
		# verifico che sia una data valida
		my $is_valid = $params->{SYSTEM}->art()->format_iso_date($params->{TS});
		unless ($is_valid){
			croak "Invalid TS ".$params->{TS}.": ".$params->{SYSTEM}->art()->last_error();
		}
		$self->{TS} = $params->{TS};
	} else {
		$self->{TS} = $params->{SYSTEM}->art()->get_iso_date_from_date($params->{SYSTEM}->art()->_dbh()->get_sysdate());
	}

	$self->{SYSTEM_ID} = $params->{SYSTEM}->id();

	$self->{ART} = $params->{SYSTEM}->art();

	my $property_types = $self->{ART}->enum_system_property(SYSTEM_TYPE_ID => $params->{SYSTEM}->info('TYPE'), EXTENDED_OUTPUT => 1);

	$self->{PROPERTY_TYPE} = $property_types->{$self->{ART}->get_system_property_name($self->{PROPERTY_ID})}->{TYPE};

	return bless ($self, $class);
}

=head2 I<object>->B<art>()

Restituisce l'oggetto API::ART

=cut

sub art(){
	shift->{ART};
}

=head2 I<object>->B<get_value>()

Restituisce il valore del dato tecnico

=cut

sub get_value(){
	shift->{VALUE};
}

=head2 I<object>->B<get_ts>()

Restituisce il timestamp del dato tecnico

=cut

sub get_ts(){
	shift->{TS};
}

=head2 I<object>->B<get_system_id>()

Restituisce l'id del sistema

=cut

sub get_system_id(){
	shift->{SYSTEM_ID};
}

=head2 I<object>->B<get_property_id>()

Restituisce l'id del tipo dato tecnico

=cut

sub get_property_id(){
	shift->{PROPERTY_ID};
}

=head2 I<object>->B<get_property_type>()

Restituisce il tipo del tipo dato tecnico

=cut

sub get_property_type(){
	shift->{PROPERTY_TYPE};
}

sub set_value(){
	my $self = shift;
	my $art = $self->art();
	my $db = $art->_dbh();
	my $errmsg;
	my %params = @_;
	
	$self->art()->last_error($errmsg)
		&& return undef
		unless	$self->check_named_params(
			 ERRMSG 				=> \$errmsg
			,IGNORE_EXTRA_PARAMS 	=> 0
			,PARAMS 				=> \%params
			,MANDATORY => {}
			,OPTIONAL => {
				VALUE	=> { isa => 'SCALAR' }
			}
		);

	my $id_tdt = $self->get_property_id();
	my $system_id = $self->get_system_id();

	# i numeri arrivano tutti con il '.' come separatore decimale
	my $old_nls_lang = $db->get_session_parameters('NLS_NUMERIC_CHARACTERS');
	$db->set_session_parameters('NLS_NUMERIC_CHARACTERS' => '.,');

	$db->do("SAVEPOINT AASDT_set_value");

	# definisco la prepare all'interno del loop perchè viene eseguita solo quando non presente
	my $sth_measure;
	my @bind_params;
	if ($self->get_property_type() eq 'TIMESTAMP'){
		$sth_measure = $art->_create_prepare(__PACKAGE__.'_MERGE_MEAS_TABLE_TS_'.$id_tdt, "
			MERGE INTO DTM_".$id_tdt." e
			USING (
					select ? ID_SISTEMA, to_timestamp_tz(?,?) TS, to_timestamp_tz(?,?) MISURA from dual
			) h
					ON (e.ID_SISTEMA = h.ID_SISTEMA and e.TS = h.TS)
			WHEN MATCHED THEN
					UPDATE SET
							e.misura = h.misura,
							e.ts_insert = systimestamp
			WHEN NOT MATCHED THEN
					INSERT (
							ID_SISTEMA,
							TS,
							MISURA
					)
					VALUES (
							h.ID_SISTEMA,
							h.TS,
							h.MISURA
					)
		");
		return undef
			unless $sth_measure;
		
		@bind_params = (
			$system_id
			,$self->get_ts()
			,$art->get_default_iso_date_format_oracle()
			,(defined $params{VALUE} ? $params{VALUE} : $self->get_value())
			,$art->get_default_iso_date_format_oracle()
		);
	} else {
		$sth_measure = $art->_create_prepare(__PACKAGE__.'_MERGE_MEAS_TABLE_'.$id_tdt, "
			MERGE INTO DTM_".$id_tdt." e
			USING (
					select ? ID_SISTEMA, to_timestamp_tz(?,?) TS, ? MISURA from dual
			) h
					ON (e.ID_SISTEMA = h.ID_SISTEMA and e.TS = h.TS)
			WHEN MATCHED THEN
					UPDATE SET
							e.misura = h.misura,
							e.ts_insert = systimestamp
			WHEN NOT MATCHED THEN
					INSERT (
							ID_SISTEMA,
							TS,
							MISURA
					)
					VALUES (
							h.ID_SISTEMA,
							h.TS,
							h.MISURA
					)
		");
		return undef
			unless $sth_measure;
		
		@bind_params = (
			$system_id
			,$self->get_ts()
			,$art->get_default_iso_date_format_oracle()
			,(defined $params{VALUE} ? $params{VALUE} : $self->get_value())
		);
	}
	unless ($sth_measure->execute(@bind_params)) {
		$db->do("ROLLBACK TO SAVEPOINT AASDT_set_value");
		$self->art()->last_error("Error set_value: " . $db->get_errormessage());
		return undef;	
	}
	$db->set_session_parameters('NLS_NUMERIC_CHARACTERS' => $old_nls_lang->{NLS_NUMERIC_CHARACTERS});

	return 1;
}

sub DESTROY {
	my $self = shift;
	for ('ART','VALUE','TS', 'SYSTEM_ID', 'PROPERTY_ID'){
		undef $self->{$_};
	}

	return 1;
}

###########################################################################################

package API::ART::System;

use strict;
use warnings;
use Carp;
use JSON;
use Digest::MD5 qw(md5_hex);
use UUID qw(uuid4);
use Compress::Bzip2 qw/:utilities/;
use Crypt::Digest::SHA256 qw/sha256_hex/;

use base qw(API::Ancestor);

use constant {
	 A_INFO_FRESH	=> 0
	,A_INFO_CACHED	=> 1
};

use API::ART::Collection::Activity;
use API::ART::Collection::System;

# Classe di default dei "figli" di API::ART::System
our $DEFAULT_CHILDREN_CLASS = 'API::ART::System';

# Classe di default dei "padri" di API::ART::System
our $DEFAULT_PARENT_CLASS = 'API::ART::System';

our $VERSION = '0.01';

=head1 NAME

B<API::ART::System> - Gestione sistemi

=head1 SYNOPSIS

	# Uses needed packages
	use API::ART;
	use API::ART::System;

	# Create API::ART instance...
	my $art = API::ART->new(.......);
	# Create API::ART::System instance...
	my $s = API::ART::System->new(ART => $art, ID => 349522);

	print "This is the NUMTEL system property value: ", $s->properties('NUMTEL'), "\n";

=head1 DESCRIPTION

Questo package consente di accedere ai sistemi di ART attraverso un'interfaccia OO.

=head1 METHODS

Di seguito i metodi esposti dalla classe API::ART::System.

=cut

#
# Metodi privati
#

sub _which_info {
	my ($self, $key, $cache) = @_;
	my $info = $self->_info($cache);
	return $info->{$key} if defined $key;
	return $info;
}
sub _info {
	my ($self, $cache) = @_;
	return $self->{INFO} if $cache eq A_INFO_CACHED;

	#
	# TODO => ottimizzazione richiesta attraverso lock in modo da non eseguire
	# sempre la query
	#
	# Estrae le info principali
	
	my $sql = qq(
		SELECT s.id_sistema "SYSTEM_ID"
		      ,s.id_tipo_sistema "SYSTEM_TYPE_ID"
		      ,s.descrizione "SYSTEM_NAME"
		      ,TO_CHAR (s.data_inizio_osservazione, ?) "CREATION_DATE"
		      ,TO_CHAR (s.data_sospensione, ?) "DISABLING_DATE"
		      ,TO_CHAR (s.data_dismissione, ?) "ENDING_DATE"
		      ,CASE
		          WHEN s.data_sospensione IS NULL
		               AND s.data_dismissione IS NULL
		             THEN 1
		          ELSE 0
		       END "ACTIVE"
		      ,NVL2 (s.data_sospensione
		            ,1
		            ,0
		            ) "DISABLED"
		      ,NVL2 (s.data_dismissione
		            ,1
		            ,0
		            ) "ENDED"
		      , (select a from sistemi_relazione sr where sr.b = s.id_sistema and sr.tipo = 0 and sr.data_fine is null) parent_system_id
		FROM   sistemi s
		where  s.id_sistema = ?
	);
	
	$self->{ART}->_create_prepare(__PACKAGE__.'_info', $sql);
	
	$self->{INFO} = $self->{ART}->_create_prepare(__PACKAGE__.'_info')->fetchall_hashref(
		$self->art()->info('DEFAULT_DATE_FORMAT')
		, $self->art()->info('DEFAULT_DATE_FORMAT')
		, $self->art()->info('DEFAULT_DATE_FORMAT')
		, $self->{ID}
	)->[0];
	
	$self->{INFO}->{SYSTEM_TYPE_NAME} = $self->{ART}->get_system_type_name($self->{INFO}->{SYSTEM_TYPE_ID});
	
	my $sql_gruppi = qq(
		SELECT	g.nome
		FROM	 permission_sistemi ps
				,gruppi g
		WHERE	ps.id_sistema = ?
				and g.id_gruppo = ps.id_gruppo_abilitato
		ORDER BY 1
	);
	
	$self->{ART}->_create_prepare(__PACKAGE__.'_info_gruppi', $sql_gruppi);
	
	# Estrae l'elenco dei gruppi abilitati
	$self->{INFO}->{GROUPS} = [ $self->{ART}->_create_prepare(__PACKAGE__.'_info_gruppi')->fetch_minimalized($self->{ID}) ];
	
	my $can_view = 0;
	for my $g(@{ $self->{ART}->user()->su()->activity()->groups() }){
		my $g_name = $self->{ART}->get_group_name($g);
		if (grep {defined $_ && $_ eq $g_name} @{$self->{INFO}->{GROUPS}}){
			$can_view = 1;
			last;
		}
	}
	
	$self->{ART}->last_error(__PACKAGE__.": USER '${\$self->{ART}->user()->name()}' not allowed for SYSTEM_ID $self->{ID}!")
		&& return undef
			unless $can_view;
		
	unless (exists $self->{INFO}->{PROPERTIES}){
		# Estrae l'elenco delle proprieta' del sistema
		my $sql_properties = qq(
			SELECT tdt.nome "NAME"
				,	tstdt.alias "ALIAS"
			FROM   tipi_sistema_tipi_dati_tecnici tstdt
				,tipi_dati_tecnici tdt
			WHERE  tstdt.id_tipo_dato_tecnico = tdt.id_tipo_dato_tecnico
					and tdt.morto is null
					and tstdt.id_tipo_sistema = ?
			ORDER BY 1
		);
		
		$self->{ART}->_create_prepare(__PACKAGE__.'_info_properties', $sql_properties);

		my $tmp_props = $self->{ART}->_create_prepare(__PACKAGE__.'_info_properties')->fetchall_hashref($self->{INFO}->{SYSTEM_TYPE_ID});
		
		$self->{INFO}->{PROPERTIES} = [];
		$self->{INFO}->{PROPERTIES_ALIAS} = {};
		$self->{INFO}->{PROPERTIES_ALIAS_REVERSE} = {};
		for my $t (@{$tmp_props}){
			push@{$self->{INFO}->{PROPERTIES}}, $t->{NAME};
			if ($self->{ART}->_get_alias_system_property){
				$self->{INFO}->{PROPERTIES_ALIAS}->{$t->{NAME}} = $t->{ALIAS};
				$self->{INFO}->{PROPERTIES_ALIAS_REVERSE}->{$t->{ALIAS}} = $t->{NAME};
			}
		}
	}
	
	return $self->{INFO};
}

sub _get_ancestor{
	my $self = shift;
	
	my $system = $self;
	
	LOOP : {
		if ($system->parent_id()){
			$system = $system->parent();
			redo LOOP;
		}
		return $self->id() == $system->id() ? undef : $system;
	}
}

sub _property {
	my $self = shift;
	my %params = @_;
	
	#
	# TODO => ottimizzazione richiesta attraverso lock in modo da non eseguire
	# sempre la query ma solo nel caso in cui l'attivita' non sia lockata dall'utente
	#

	$self->{PROPERTIES} = {};
	
	my $system_type_id = $self->cached_info('SYSTEM_TYPE_ID');

	my $visibility_property = $self->{ART}->get_system_property_visibility(SYSTEM_TYPE_NAME => $self->cached_info('SYSTEM_TYPE_NAME'));
	return undef unless defined $visibility_property;

	
	my $property_types = $self->{ART}->enum_system_property(SYSTEM_TYPE_ID => $system_type_id, EXTENDED_OUTPUT => 1);
	
	my $num_dt_filter = (defined $params{KEYS} && scalar @{$params{KEYS}}) ? scalar @{$params{KEYS}} : 0; 
	
	for my $tmp_k (@{$params{KEYS}}){
		$tmp_k = $self->info('PROPERTIES_ALIAS_REVERSE')->{$tmp_k} if exists $self->info('PROPERTIES_ALIAS_REVERSE')->{$tmp_k};
	}

	my $sql = "
		select td.nome
			, d.descrizione valore
			, d.id_dato_Tecnico
		from dati_tecnici d
		  join TIPI_DATI_TECNICI tD on (d.ID_TIPO_DATO_TECNICO = tD.ID_TIPO_DATO_TECNICO)
		where d.ID_SISTEMA = ?
		  ".($num_dt_filter > 0 ? "and td.nome in (".join (',', map {'?'} @{$params{KEYS}}).")" : "")."
		  and TD.morto is null
		order by d.id_dato_tecnico
	";

	$self->{ART}->_create_prepare(__PACKAGE__.'__props_'.$num_dt_filter, $sql);
	
	my @bind_values = (
		$self->id()
	);
	
	push (@bind_values, @{$params{KEYS}}) if $num_dt_filter > 0;
	
	my $properties = $self->{ART}->_create_prepare(__PACKAGE__.'__props_'.$num_dt_filter)->fetchall_hashref(@bind_values);
	
	for my $kk (keys %{$property_types}){
		# se non è tra le chiavi richieste passo oltre
		next if scalar @{$params{KEYS}} && !grep {$_ eq $kk} @{$params{KEYS}};
		# se non è tra quelle visibili dall'utente passo oltre
		next if ($visibility_property->{ACTIVE} && ! grep {$_->{NAME} eq $kk} @{$visibility_property->{PROPERTIES}});

		my @rows = grep {$_->{NOME} eq $kk} @$properties;
		
		if ($property_types->{$kk}->{MULTIPLEX}){
			$self->{PROPERTIES}->{$kk} = [] unless defined $self->{PROPERTIES}->{$kk};
			for my $rm (@rows) {
				if ($params{EXTENDED_OUTPUT} && $property_types->{$kk}->{IS_FILE}){
					push @{$self->{PROPERTIES}->{$kk}}, API::ART::System::DT::File->new(SYSTEM => $self, ID_DATO_TECNICO => $rm->{ID_DATO_TECNICO});
				} else {
					push @{$self->{PROPERTIES}->{$kk}}, $rm->{VALORE};
				}
			}
		} else {
			# se non ho nessun risultato deve essere presente la chiave valorizzata ad undef
			if (scalar @rows == 0){
				$self->{PROPERTIES}->{$kk} = undef;
				next;
			}

			my $blob;
			if ($property_types->{$kk}->{TYPE} eq 'BLOB') {
				$blob = API::ART::System::Property::BLOB->new(
					SYSTEM => $self,
					PROPERTY => $kk,
					VALUE => $rows[0]->{VALORE}
				);
				unless ($blob) {
					$self->{PROPERTIES}->{$kk} = undef;
					next;
				}
			}

			if ($params{EXTENDED_OUTPUT}) {
				# imposto il valore: eventualmente lo sovrascrivo nei casi necessari
				$self->{PROPERTIES}->{$kk} = $rows[0]->{VALORE};
				if ($property_types->{$kk}->{IS_FILE}) {
					# FIXME deprecare?
					$self->{PROPERTIES}->{$kk} = API::ART::System::DT::File->new(SYSTEM => $self, ID_DATO_TECNICO => $rows[0]->{ID_DATO_TECNICO});
				}
				if ($property_types->{$kk}->{TYPE} eq 'BLOB') {
					$self->{PROPERTIES}->{$kk} = $blob;
				}
			} else {
				if ($property_types->{$kk}->{TYPE} eq 'BLOB') {
					# restituisce uno scalare per coerenza con le altre property
					$self->{PROPERTIES}->{$kk} = $blob->raw();
				} else {
					$self->{PROPERTIES}->{$kk} = $rows[0]->{VALORE};
				}
			}
		}
	}

	return $self->{PROPERTIES};
}

sub _handler { return $_[0]->{HANDLER} }


=head2 I<package>->B<new>( ART => I<API::ART> , ID => I<ystem_id> )

Il metodo B<new()> e' il costruttore di classe e richiede due argomenti obbligatori:

=over 4

=item B<ART>

Un'istanza della classe API::ART

=item B<ID>

L'ID dell'attivita' da utilizzare

=back

=cut
sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	#
	# ART
	# ID
	#
	# Sintassi
	my $usage = sub {
		my $errmsg = shift;
		my $msg    = "";
		$msg .= "$class : $errmsg\n\n" if defined $errmsg;
		$msg .= "Usage:\n";
		$msg .= "\t$class";
		$msg .= '->new( ART => API::ART->new(), ID => $id )';
		$msg .= "\n\n";
		croak $msg;
	};

	# Controlli sui parametri
	$usage->('Missing ART!') unless ref($params->{ART}) eq 'API::ART';
	$usage->('Missing ID!') unless defined $params->{ID};
	my $self  = $class->SUPER::new();
	$self->{ART} = $params->{ART};
	$self->{ID} = $params->{ID};
	$self->{ART}->clear_last_error();
			
	$self = bless( $self, $class );

	# Valorizza info
	return undef unless $self->info();

	my $sql = "
		select event_label
			, behaviour_label
			, behaviour_class
		From v_tipi_sistema_behaviour
		where id_tipo_sistema = ?
			and disable_date is null
		order by execution_order
	";

	$self->{ART}->_create_prepare(__PACKAGE__.'_new_behav', $sql);
	
	my @bind_values = (
		$self->{INFO}->{SYSTEM_TYPE_ID}
	);
	
	my $behs = $self->{ART}->_create_prepare(__PACKAGE__.'_new_behav')->fetchall_hashref(@bind_values);
	
	# normalizzo i risultati per avere un hash leggibile facilmente
	$self->{BEHAVIOURS} = {};
	for my $b (@{$behs}){
		if (exists $self->{BEHAVIOURS}->{$b->{EVENT_LABEL}}){
			push @{$self->{BEHAVIOURS}->{$b->{EVENT_LABEL}}},{ LABEL => $b->{BEHAVIOUR_LABEL}, CLASS => $b->{BEHAVIOUR_CLASS}};
		} else {
			$self->{BEHAVIOURS}->{$b->{EVENT_LABEL}} = [{ LABEL => $b->{BEHAVIOUR_LABEL}, CLASS => $b->{BEHAVIOUR_CLASS}}];
		}
	}
	
	return $self;
}

sub _get_behaviours { shift->{BEHAVIOURS} };

sub _manage_behaviours {
	my $self = shift;
	my %params = @_;

	# imposto il savepoint
	$self->art()->_dbh()->do("SAVEPOINT AASman_beh_".$self->id());
	
	# gestico il comportamento sullo step
	if (!defined $params{EVENT} || $params{EVENT} eq 'on_change'){
		$self->art()->_dbh()->do("rollback to SAVEPOINT AASman_beh_".$self->id())
			&& return undef
				unless defined $self->_execute_behaviours(BEHAVIOUR_EVENT => ['on_change'], SYSTEM => $self);
	}
	
	# se non è definita o sto scendendo gestisco i figli
	if (!defined $params{EVENT} || $params{EVENT} eq 'on_father_change'){
		# recupero solo i figli che hanno questo tipo di evento
		my $children = $self->get_children(BEHAVIOUR_EVENT => ['on_father_change']);
		return undef unless defined $children;
		
		for my $ch (@{$children}){
			$self->art()->_dbh()->do("rollback to SAVEPOINT AASman_beh_".$self->id())
				&& return undef
					unless defined $ch->_execute_behaviours(BEHAVIOUR_EVENT => ['on_father_change'], SYSTEM => $ch);
		}
	}

	# se non è definita o sto salendo gestisco il padre
	if (!defined $params{EVENT} || $params{EVENT} eq 'on_child_change'){
		# recupero il padre se e' presente ed eseguo il suo comportamento
		if ($self->parent_id()){
			$self->art()->_dbh()->do("rollback to SAVEPOINT AASman_beh_".$self->id())
				&& return undef
					unless defined $self->parent()->_execute_behaviours(BEHAVIOUR_EVENT => ['on_child_change'], SYSTEM => $self);
		}
	}
	
	return 1;
}

sub _execute_behaviours{
	my $self = shift;
	my %params = @_;
	
	# imposto default
	$params{BEHAVIOUR_EVENT} = ['on_father_change', 'on_child_change', 'on_change'] unless defined $params{BEHAVIOUR_EVENT};
	
	my $behaviours = $self->_get_behaviours();
	
	# imposto il savepoint
	$self->art()->_dbh()->do("SAVEPOINT AASex_be_".$self->id());
	
	# giro sugli eventi richiesti
	for my $b (@{$params{BEHAVIOUR_EVENT}}){
		# giro sui comportamenti che ha il sistema
		for my $h (@{$behaviours->{$b}}){
			# Imposto il nome della classe che gestice il comportamento
			my $class = defined $h->{CLASS} ? $h->{CLASS} : 'API::ART::System::Behaviours::'.$h->{LABEL};
			
			CLASS: {
				local $@;
				eval "require $class;";
				if ($@){
					$self->art()->_dbh()->do("rollback to SAVEPOINT AASex_be_".$self->id());
					$self->art()->last_error("Unable to require class ($class): " .$@);
					return undef;
				}
			}
			
			if (my $method = $class->can('do')){
				my $ret = $method->($params{SYSTEM}, $b);
				unless (defined $ret){
					$self->art()->_dbh()->do("rollback to SAVEPOINT AASex_be_".$self->id());
					$self->art()->last_error("Unable to execute behaviour $b: " .$self->art()->last_error());
					return undef;
				}
			}
		}
	}
	return 1;
}

=head2 I<object>->B<art>()

Ritorna il riferimento all'oggetto API::ART passato come argomento al costruttore.

=cut
sub art { return $_[0]->{ART} }


=head2 I<object>->B<id>()

Ritorna l'ID del sistema.

=cut
sub id { return $_[0]->{ID} }


=head2 I<object>->B<name>()

Ritorna il nome del sistema (shortcut di I<object>->info('SYSTEM_NAME')).

=cut
sub name { return $_[0]->cached_info('SYSTEM_NAME') }


=head2 I<object>->B<info>( [I<scalar>] )

Ritorna informazioni sull'istanza dell'oggetto API::ART::System.

Senza argomenti ritorna un I<hashref> contenente tutte le informazioni disponibili:

=over 4

=item B<SYSTEM_ID>

ID del sistema corrente (sinonimo del metodo B<id()>)

=item B<SYSTEM_TYPE_ID>

ID della tipologia del sistema

=item B<SYSTEM_TYPE_NAME>

Tipologia del sistema

=item B<SYSTEM_NAME>

Nome del sistema

=item B<ACTIVE>

1 se il sistema e' attivivo oppure 0 se sospeso (vedere B<DISABLED>) o dismesso (vedere B<ENDED>)

=item B<DISABLED>

1 se il sistema e' sospeso oppure 0 (vedere B<DISABLING_DATE>)

=item B<ENDED>

1 se il sistema e' dismesso oppure 0 (vedere B<ENDING_DATE>)

=item B<CREATION_DATE>

Data di inizio osservazione del sistema espressa nel formato definito da API::ART::Info('DEFAULT_DATE_FORMAT')

=item B<DISABLING_DATE>

Data di sospensione del sistema espressa nel formato definito da API::ART::Info('DEFAULT_DATE_FORMAT')

=item B<ENDING_DATE>

Data di dismissione del sistema espressa nel formato definito da API::ART::Info('DEFAULT_DATE_FORMAT')

=item B<PROPERTIES>

Ritorna un I<arrayref> con l'elenco dei nomi delle proprieta' definite per il sistema

=item B<PROPERTIES_ALIAS>

Ritorna un I<hashre> con le coppie chiave => valore (nome => alias)

=item B<PROPERTIES_ALIAS_REVERSE>

Ritorna un I<hashre> con le coppie chiave => valore (alias => nome)

=item B<GROUPS>

Ritorna un I<arrayref> con l'elenco dei nomi dei gruppi con visibilita' sul sistema

=back

=cut
sub info {
	my ($self, $key) = @_;
	return $self->_which_info((defined $key ? $key : undef), A_INFO_FRESH);
}
sub cached_info {
	my ($self, $key) = @_;
	return $self->_which_info((defined $key ? $key : undef), A_INFO_CACHED);
}

=head2 I<object>->B<property>( [ I<property1> [ , I<property2> [ , I<propertyN> ] ] ] )

Se viene passato il nome di una sola proprieta' ritorna uno I<scalar>.

Se vengono passati piu' nomi di proprieta' oppure senza argomenti, ritorna un I<hashref>.

=cut
sub property {
	my $self = shift;
	my @keys = @_;
	my $property = $self->_property(KEYS => \@keys);
	
	my $property_final = {};
	# eseguo il loop per gestire i dati tecnici di tipo file
	for my $p (keys %{$property}){
		if (ref $property->{$p} eq 'ARRAY'){
			$property_final->{$p} = [] unless defined $property_final->{$p};
			for my $ar (@{$property->{$p}}){
				if (ref ($ar) && $ar->isa('API::ART::System::DT::File')){
					push @{$property_final->{$p}}, $ar->get_filename();
				} else {
					push @{$property_final->{$p}}, $ar;
				}
			}
		} else {
			if (ref ($property->{$p}) && $property->{$p}->isa('API::ART::System::DT::File')){
				$property_final->{$p} = $property->get_filename();
			} else {
				$property_final->{$p} = $property->{$p};
			}
		}
	}
	
	if ( scalar(@keys) == 1 ) {
		return $property_final->{$keys[0]};
	} else {
		return $property_final;
	}
}

=head2 I<object>->B<property_ext>( [ I<property1> [ , I<property2> [ , I<propertyN> ] ] ] )

Il comportamento è identico a quello del metodo property, ma in caso di dt di tipo FILE
viene restituito l'oggetto API::ART::System::DT::File 

=cut

sub property_ext {
	my $self = shift;
	my @keys = @_;
	my $property = $self->_property(KEYS => \@keys, EXTENDED_OUTPUT => 1);
	
	if ( scalar(@keys) == 1 ) {
		return $property->{$keys[0]};
	} else {
		return $property;
	}
}

#=head2 I<object>->B<property_history>( [ I<property1> [ , I<property2> [ , I<propertyN> ] ] ] )
#
#Ritorna un I<hashref> della storia delle proprieta' del sistema con eventuale filtro sui nomi delle proprieta' passate come argomento.
#
#=cut
#sub property_history {
#	my $self = shift;
#	my @keys = @_;
#	my $art = $self->art();
#	my $db = $art->_dbh();
#	my $history;
#	
#	my $sql = "
#		with h as (
#			select * from DATI_TECNICI_HISTORY
#			union
#			select * from DATI_TECNICI
#		)
#		select * From (
#		select
#			H.ID_SISTEMA
#			,o.login_operatore
#			,to_char(H.DATA_ULTIMA_VAR, ?) DATA_ULTIMA_VAR
#			,T.NOME TIPO_DATO_TECNICO
#			,H.DESCRIZIONE NEW_VALUE
#			,LEAD (H.DESCRIZIONE) over (partition by H.ID_SISTEMA, H.ID_TIPO_DATO_TECNICO order by H.id_dato_tecnico desc) as OLD_VALUE
#			,decode(t.multiplex_value,'Y',1,0) MULTIPLEX
#		from H
#		join OPERATORI o on (H.OPER_ULTIMA_VAR = o.ID_OPERATORE)
#		join TIPI_DATI_TECNICI t on (T.ID_TIPO_DATO_TECNICO = H.ID_TIPO_DATO_TECNICO)
#		where H.ID_SISTEMA = ?
#		and T.MULTIPLEX_VALUE = 'N'
#		union
#		select distinct
#				H.ID_SISTEMA
#				,o.login_operatore
#				,to_char(H.DATA_ULTIMA_VAR, ?) DATA_ULTIMA_VAR
#				,T.NOME TIPO_DATO_TECNICO
#				,F_SP_HISTORY_MULTIPLEX(
#	        id_sistema_p => h.id_sistema
#	        ,DATA_ULTIMA_VAR_p => H.DATA_ULTIMA_VAR
#	        ,ID_TIPO_DATO_TECNICO_p => h.id_tipo_dato_tecnico
#	      ) NEW_VALUE
#				,F_SP_HISTORY_MULTIPLEX(
#	        id_sistema_p => h.id_sistema
#	        ,DATA_ULTIMA_VAR_p => ( select max(h2.data_ultima_var) From h h2 where h2.id_sistema = h.id_sistema and h2.id_tipo_dato_tecnico = h.id_tipo_dato_tecnico and h2.data_ultima_var < h.data_ultima_var )
#	        ,ID_TIPO_DATO_TECNICO_p => h.id_tipo_dato_tecnico
#	      ) OLD_VALUE
#	      ,decode(t.multiplex_value,'Y',1,0) MULTIPLEX
#			from  H
#			join OPERATORI o on (H.OPER_ULTIMA_VAR = o.ID_OPERATORE)
#			join TIPI_DATI_TECNICI t on (T.ID_TIPO_DATO_TECNICO = H.ID_TIPO_DATO_TECNICO)
#			where H.ID_SISTEMA = ?
#	    and T.MULTIPLEX_VALUE = 'Y'
#	    )
#	    order by data_ultima_var desc
#	";
#
#	$art->last_error("Error fetching system property history!\n" . $@)
#		&& return undef
#			unless eval { $history = $art->_create_prepare(__PACKAGE__.'_HISTORY_PREPARE', $sql)->fetchall_hashref($self->art()->info('DEFAULT_DATE_FORMAT'),$self->id,$self->art()->info('DEFAULT_DATE_FORMAT'),$self->id) };
#
#	map { if($_->{MULTIPLEX }) { $_->{NEW_VALUE} = from_json $_->{NEW_VALUE}; $_->{OLD_VALUE} = from_json $_->{OLD_VALUE}; } delete $_->{MULTIPLEX}; } @{$history};
#
#	if ( scalar(@keys) == 0 ) {
#		return $history;
#	} else {
#		my %findkeys = map { $_ => 1 } @keys;
#		my @filter = grep { exists $findkeys{$_->{TIPO_DATO_TECNICO}} } @$history;
#		return \@filter;
#	}
#}

=head2 I<object>->B<is_active>()

Ritorna 1 se il sistema e' attivo oppure 0.

=cut
sub is_active { $_[0]->info('ACTIVE') }


=head2 I<object>->B<is_disabled>()

Ritorna 1 se il sistema e' sospeso oppure 0.

=cut
sub is_disabled { $_[0]->info('DISABLED') }


=head2 I<object>->B<is_ended>()

Ritorna 1 se il sistema e' stato dismesso oppure 0.

=cut
sub is_ended { $_[0]->info('ENDED') }


=head2 I<object>->B<disable>()

Disabilita il sistema.

Di seguito i parametri del metodo B<disable()>:

=over 4

=item B<DATE>

Data di creazione del sistema; se omesso verra' utilizzato I<sysdate>

=item B<DATE_FORMAT>

Formato da utilizzare per l'interpretazione di B<DATE>; se omesso verra' utilizzato API::ART::Info('DEFAULT_DATE_FORMAT')

=back

=cut
sub disable {
	my $self = shift;
	my %params = @_;
	my $art = $self->art();
	my $db = $art->_dbh();
	local $@;

	$art->clear_last_error();

	return 1
		if $self->is_disabled();

	# DATE_FORMAT
	$params{DATE_FORMAT} = $art->info('DEFAULT_DATE_FORMAT') unless defined $params{DATE_FORMAT};
	# DATE
	$params{DATE} = $art->_dbh()->get_sysdate($params{DATE_FORMAT}) unless defined $params{DATE};
	my $test_date = $art->_dbh()->test_date($params{DATE}, $params{DATE_FORMAT});
	$art->last_error("Invalid DATE '$params{DATE}': '$test_date'!")
		&& return 0
			if $test_date;
	$params{DATE} = $art->format_date($params{DATE}, $params{DATE_FORMAT});

	$db->do("savepoint AASdisable");

	my $sql = qq/
		UPDATE	sistemi
		SET		data_sospensione = TO_DATE('$params{DATE}', '${ \$art->info('DEFAULT_DATE_FORMAT') }')
		WHERE	id_sistema = ${ \$self->id() }
	/;
	$art->last_error("Error disabling system!\n" . $@)
		&& return undef
			unless eval { $db->do($sql) };

	# gestisco i comportamenti
	unless (defined $self->_manage_behaviours(EVENT => 'on_change')){
		# rollback savepoint
		$db->do("rollback to savepoint AASdisable");
		return 0;
	};
}


=head2 I<object>->B<enable>()

Riabilita il sistema se precedentemente disabilitato col metodo I<disable()>.

=cut
sub enable {
	my $self = shift;
	my %params = @_;
	my $art = $self->art();
	my $db = $art->_dbh();
	local $@;

	$art->clear_last_error();

	return 1
		unless $self->is_disabled();

	$db->do("savepoint AASenable");

	my $sql = qq/
		UPDATE	sistemi
		SET		data_sospensione = NULL
		WHERE	id_sistema = ${ \$self->id() }
	/;
	$art->last_error("Error enabling system!\n" . $@)
		&& return undef
			unless eval { $db->do($sql) };
	
	# gestisco i comportamenti
	unless (defined $self->_manage_behaviours(EVENT => 'on_change')){
		# rollback savepoint
		$db->do("rollback to savepoint AASenable");
		return 0;
	};
}


=head2 I<object>->B<end>()

Dismette il sistema.

Di seguito i parametri del metodo B<end()>:

=over 4

=item B<DATE>

Data di creazione del sistema; se omesso verra' utilizzato I<sysdate>

=item B<DATE_FORMAT>

Formato da utilizzare per l'interpretazione di B<DATE>; se omesso verra' utilizzato API::ART::Info('DEFAULT_DATE_FORMAT')

=back

=cut
sub end {
	my $self = shift;
	my %params = @_;
	my $art = $self->art();
	my $db = $art->_dbh();
	local $@;

	$art->clear_last_error();

	return 1
		if $self->is_ended();

	return undef
		unless $self->can_end();

	# DATE_FORMAT
	$params{DATE_FORMAT} = $art->info('DEFAULT_DATE_FORMAT') unless defined $params{DATE_FORMAT};
	# DATE
	$params{DATE} = $art->_dbh()->get_sysdate($params{DATE_FORMAT}) unless defined $params{DATE};
	my $test_date = $art->_dbh()->test_date($params{DATE}, $params{DATE_FORMAT});
	$art->last_error("Invalid DATE '$params{DATE}': '$test_date'!")
		&& return 0
			if $test_date;
	$params{DATE} = $art->format_date($params{DATE}, $params{DATE_FORMAT});

	$db->do("savepoint AASend");

	my $sql = qq/
		UPDATE	sistemi
		SET		data_dismissione = TO_DATE('$params{DATE}', '${ \$art->info('DEFAULT_DATE_FORMAT') }')
		WHERE	id_sistema = ${ \$self->id() }
	/;
	$art->last_error("Error ending system!\n" . $@)
		&& return 0
			unless eval { $db->do($sql) };
	
	# gestisco i comportamenti
	unless (defined $self->_manage_behaviours(EVENT => 'on_change')){
		# rollback savepoint
		$db->do("rollback to savepoint AASend");
		return 0;
	};
	return 1;
}

=head2 I<object>->B<can_end>()

Verifica se è possibile dismettere il sistema.

=cut

sub can_end {
        my $self = shift;
	my $art = $self->art();

        my $ids = $self->get_activities_id(ACTIVE       => 1);
        return undef
                unless defined $ids;
        $art->last_error("Pending activities found!")
                && return undef
                        if scalar(@$ids) > 0;
	return 1;
}


=head2 I<object>->B<set_property>( PROPERTIES => { I<name> => I<value> [, ... ] }, DATE => I<SCALAR>, DATE_FORMAT => I<SCALAR> )

Aggiorna le property del sistema, ritornando 1 in caso di successo oppure 0 in caso di fallimento (settando I<last_error()>).

Di seguito i parametri del metodo B<set_property()>:

=over 4

=item B<DATE>

Data di aggiornamento delle proprieta' del sistema; se omesso verra' utilizzato I<sysdate>

=item B<DATE_FORMAT>

Formato da utilizzare per l'interpretazione di B<DATE>; se omesso verra' utilizzato API::ART::Info('DEFAULT_DATE_FORMAT')

=item B<PROPERTIES> I<hashref>

Un I<hashref> delle proprieta' da modificare nel sistema

=back

=cut
sub _set_property {
	my $self = shift;
	my %params = @_;
	my $art = $self->art();
	my $db = $art->_dbh();
	my $property_types = $art->enum_system_property(SYSTEM_TYPE_ID => $self->cached_info('SYSTEM_TYPE_ID'), EXTENDED_OUTPUT => 1);
	my $sql = '';
	local $@;

	$art->clear_last_error();

	# DATE_FORMAT
	$params{DATE_FORMAT} = $art->info('DEFAULT_DATE_FORMAT') unless defined $params{DATE_FORMAT};
	# DATE
	$params{DATE} = $art->_dbh()->get_sysdate($params{DATE_FORMAT}) unless defined $params{DATE};
	my $test_date = $art->_dbh()->test_date($params{DATE}, $params{DATE_FORMAT});
	$art->last_error("Invalid DATE '$params{DATE}': '$test_date'!")
		&& return 0
			if $test_date;

	# PROPERTY
	$params{PROPERTIES} = {} unless defined $params{PROPERTIES};
	$art->last_error("PROPERTIES must be an hash-ref or undefined!") &&
		return 0
			unless ref($params{PROPERTIES}) eq 'HASH';

	my $sth = $art->_create_prepare(__PACKAGE__.'_INSERT_DT', "
		INSERT INTO dati_tecnici (
			 id_sistema
			,id_sottosistema
			,id_tipo_dato_tecnico
			,descrizione
			,id_dato_tecnico
			,oper_ultima_var
			,data_ultima_var
			,id_storico_import
		)
		VALUES (
			 ?
			,null
			,?
			,?
			,seq_dati_tecnici.NEXTVAL
			,?
			,TO_DATE(?, ?)
			,NULL
		)
	");
	return undef
		unless $sth;

	$db->do("SAVEPOINT set_system_property");
	
	my $system_id = $self->id();
	my $properties = $self->property();
	my $sth_sel_val = $art->_create_prepare(__PACKAGE__.'_SELECT_VAL_DT', "
		select id_tipo_dato_Tecnico
		from dati_Tecnici
		where id_sistema = ?
	");
	return undef
		unless $sth_sel_val;
	
	my @property_valorizzate = map {$_->[0]} @{$sth_sel_val->fetchall_arrayref($system_id)};
	foreach my $prop ( keys %{$params{PROPERTIES}} ) {
		my $orig_prop = $prop;
		$prop = $self->info('PROPERTIES_ALIAS_REVERSE')->{$prop} if $self->info('PROPERTIES_ALIAS_REVERSE') && exists $self->info('PROPERTIES_ALIAS_REVERSE')->{$prop};
		$params{PROPERTIES}->{$prop} = $params{PROPERTIES}->{$orig_prop};
		my $id_tdt = $art->get_system_property_id($prop);
		unless (defined $property_types->{$prop}) {
			$db->do("ROLLBACK TO SAVEPOINT set_system_property");
			$art->last_error("Bad PROPERTY '$prop' for SYSTEM_TYPE_NAME='${ \$self->type_name() }'!");
			return 0;	
		}

		if(ref ($params{PROPERTIES}->{$prop}) eq 'ARRAY'){
			if ($property_types->{$prop}->{MULTIPLEX}) {
				# se il nuovo valore e' uguale al valore attuale, skippo silente...
				my $array_are_different = 0;
				if (scalar @{$params{PROPERTIES}->{$prop}} != scalar @{$properties->{$prop}}){
					$array_are_different = 1;
				} else {# verifico se hanno gli stessi elementi
					for (my $i=0;$i<scalar @{$params{PROPERTIES}->{$prop}}; $i++){
						if ($params{PROPERTIES}->{$prop}->[$i] ne $properties->{$prop}->[$i]){
							$array_are_different = 1;
							last; # se c'è anche un solo elemento diverso allora esco
						}
					}
				}
				if ($array_are_different){
					if (grep {$_ eq $id_tdt} @property_valorizzate){
						unless ($self->delete_property( $prop )) {
							$db->do("ROLLBACK TO SAVEPOINT set_system_property");
							# last error e' gia' stato settato dalla delete_property
							return 0;	
						}
					}
					my $lookup = $art->lookup_get();
					for my $val (@{$params{PROPERTIES}->{$prop}}) {
						# verifico che sia un valore valido
						return 0 
							unless defined $art->check_value(
								TYPE => $property_types->{$prop}->{TYPE},
								KEY => $prop,
								VALUE => $val,
								VALUE_MIN => $property_types->{$prop}->{VALUE_MIN},
								VALUE_MAX => $property_types->{$prop}->{VALUE_MAX},
								FRACTIONS => $property_types->{$prop}->{FRACTIONS}
							);
						
						my @bind_params = (
							$system_id
							,$id_tdt
							,$val
							,$art->user()->id()
							,$params{DATE}
							,$params{DATE_FORMAT}
						);
						unless ($sth->execute(@bind_params)) {
							$db->do("ROLLBACK TO SAVEPOINT set_system_property");
							$self->art()->last_error("Error updating system property '$prop': " . $art->_dbh()->get_errormessage());
							return 0;	
						}
					}
				}
			} else {
				$db->do("ROLLBACK TO SAVEPOINT set_system_property");
				$art->last_error("As PROPERTY '$prop' is not MULTIPLEX you can't provide an arrayref!");
				return 0;
			}
		} else {
			if ($property_types->{$prop}->{MULTIPLEX}) {
				$db->do("ROLLBACK TO SAVEPOINT set_system_property");
				$art->last_error("As PROPERTY '$prop' is MULTIPLEX you must provide an arrayref!");
				return 0;
			}

			# gestisce solo valori di tipo API::ART::System::DT::Measure o scalare
			my $value = ref($params{PROPERTIES}->{$prop}) eq 'API::ART::System::DT::Measure' ? $params{PROPERTIES}->{$prop}->get_value() : $params{PROPERTIES}->{$prop};

			my $lookup = $art->lookup_get();
			# verifico che sia un valore valido
			return 0 
				unless defined $art->check_value(
					TYPE => $property_types->{$prop}->{TYPE},
					KEY => $prop,
					VALUE => $params{PROPERTIES}->{$prop},
					VALUE_MIN => $property_types->{$prop}->{VALUE_MIN},
					VALUE_MAX => $property_types->{$prop}->{VALUE_MAX},
					FRACTIONS => $property_types->{$prop}->{FRACTIONS}
				);
			
			if (ref $params{PROPERTIES}->{$prop} eq 'ARRAY') {
				$db->do("ROLLBACK TO SAVEPOINT set_system_property");
				$art->last_error("As PROPERTY '$prop' is not MULTIPLEX you must provide a scalar!");
				return 0;	
			}

			# gestione BLOB
			if ($property_types->{$prop}->{TYPE} eq 'BLOB') {
				# FIXME: valutare file size limit
				# genero filename: propertyName . '_' . systemId . '_' . '_YYYYMMDDHH24MISS_' . UUID
				my $blobname = join('_', $prop, $system_id, $params{DATE}, uuid4());
				# salvo il blob Base64 su filesystem
				$value =~ s/[\n\r\s]//g;
				$value =~ s/=*$//;
				my $mod = length($value) % 4;
				# paddo solo con uno o due = in accordo con la specifica Base64
				$value .= '=' x (4 - $mod)
					if $mod >= 2;

				my $bz = bzopen($self->art()->system_blobs_repository() . $blobname, 'w');
				unless ($bz) {
					$db->do("ROLLBACK TO SAVEPOINT set_system_property");
					$self->art()->last_error("Error saving BLOB for system property '$prop': " . $!);
					return 0;
				}
				my $written = $bz->bzwrite($value);
				$bz->bzclose();
				unless ($written == length($value)) {
					$db->do("ROLLBACK TO SAVEPOINT set_system_property");
					$self->art()->last_error("Error writing BLOB for system property '$prop': " . $bz->bzerror);
					return 0;
				}

				#my $mime = 'application/octet-stream';
				my $mime_string = "bzip2 -d -c ".$self->art()->system_blobs_repository() . $blobname." | base64 -d | file -bi - | cut -d ';' -f 1";
				my $mime = `$mime_string`;
				chomp($mime);

				my $mime_extensions = MIME::Types::by_mediatype($mime);

				# sostituisco al valore della property Base64  il filename
				$value = to_json(
					{
						'f' => $blobname,																		# nome del file all'interno del repository sul filesystem 
						'o' => 'bzip2',																			# formato di compressione
						'h' => sha256_hex($value),																# hash del blob Base64
						'd' => $params{DATE},																	# data salvataggio (a scopo di futura suddivisione del repository in anno, mese e giorno)
						'm' => $mime,																			# mime-type del file a valle della decodifica da Base64,
						'n' => $blobname.(defined $mime_extensions->[0][0] ? '.'.$mime_extensions->[0][0] : '')	# filename comprensivo di estensione a scopo download
					}
				);

			}

			# se il nuovo valore e' uguale al valore attuale, skippo silente...
			unless (
				(!defined $value && !defined $properties->{$prop})
				||
				(defined $value && defined $properties->{$prop} && $value eq $properties->{$prop})
			){
				if (grep {$_ eq $id_tdt} @property_valorizzate){
					unless ($self->delete_property( $prop )) {
						$db->do("ROLLBACK TO SAVEPOINT set_system_property");
						# last error e' gia' stato settato dalla delete_property
						return 0;	
					}
				}
				# lo formatto correttamente
				if ($property_types->{$prop}->{TYPE} eq 'CURRENCY'){
					$value = sprintf("%.".$property_types->{$prop}->{FRACTIONS}."f", $value);
				}

				my @bind_params = (
					$system_id
					,$id_tdt
					,$value
					,$art->user()->id()
					,$params{DATE}
					,$params{DATE_FORMAT}
				);
				unless ($sth->execute(@bind_params)) {
					$db->do("ROLLBACK TO SAVEPOINT set_system_property");
					$self->art()->last_error("Error updating system property '$prop': " . $art->_dbh()->get_errormessage());
					return 0;	
				}
			}

			# se è un dt di campionamento:
			# - scrivo nella tabella delle misure
			# - faccio l'upsert del dt
			# - non eseguo la delete (con la insert nella history perchè generebbe moltissimi record non significativi)
			if($property_types->{$prop}->{IS_MEASURE}) {
				if (ref($params{PROPERTIES}->{$prop}) ne 'API::ART::System::DT::Measure'){
					$db->do("ROLLBACK TO SAVEPOINT set_system_property");
					$art->last_error("As PROPERTY '$prop' is IS_MEASURE you must provide a reference to API::ART::System::DT::Measure!");
					return 0;	
				}

				if ($property_types->{$prop}->{TYPE} eq 'BLOB') {
					# imposto il valore campionato con il costrutto blob
					return 0
						unless $params{PROPERTIES}->{$prop}->set_value(VALUE => $value);
				} else {
					# imposto il valore campionato
					return 0
						unless $params{PROPERTIES}->{$prop}->set_value();
				}

				
			}
		}
	}

	# gestisco i comportamenti
	unless (defined $self->_manage_behaviours(EVENT => 'on_change')){
		# rollback savepoint
		$db->do("rollback to savepoint set_system_property");
		return 0;
	};

	return 1;
}

sub set_property {
	my $self = shift;
	my %params = @_;
	my $art = $self->art();

	my $property_types = $art->enum_system_property(SYSTEM_TYPE_ID => $self->cached_info('SYSTEM_TYPE_ID'), EXTENDED_OUTPUT => 1);
	foreach my $prop ( keys %{$params{PROPERTIES}} ) {
		$prop = $self->info('PROPERTIES_ALIAS_REVERSE')->{$prop} if $self->info('PROPERTIES_ALIAS_REVERSE') && exists $self->info('PROPERTIES_ALIAS_REVERSE')->{$prop};
		if($property_types->{$prop}->{IS_COUNTER}) {
			$art->last_error("You can not use set_property to update '$prop' of type counter: please use method inc_property");
			return 0;	
		}
	}

	return $self->_set_property(%params);
}


=head2 I<object>->B<property_measurements>( PROPERTIES => [], FROM => I<SCALAR>, TO => I<SCALAR> [, AGGREGATE_BY => I<SCALAR>] )

Se vengono passati piu' nomi di proprieta' oppure senza argomenti, ritorna un I<hashref>.

I valori accettati per AGGREGATE_BY sono 'hour', 'day', 'month'

=cut
sub property_measurements {
	my $self = shift;
	my $errmsg;
	my %params = @_;
	
	$self->art()->last_error($errmsg)
		&& return undef
		unless	$self->check_named_params(
			 ERRMSG 				=> \$errmsg
			,IGNORE_EXTRA_PARAMS 	=> 0
			,PARAMS 				=> \%params
			,MANDATORY => {
				PROPERTIES	=> { isa => 'ARRAY' },
				FROM		=> { isa => 'SCALAR' },
				TO			=> { isa => 'SCALAR' }
			}
			,OPTIONAL => {
				AGGREGATE_BY	=> { isa => 'SCALAR', list => ['hour', 'day', 'month'] }
			}
		);

	my $art = $self->art();
	$art->clear_last_error();

	# valido le date
	for ('FROM', 'TO'){
		return undef
			unless $art->format_iso_date($params{$_});
	}
	
	my $db = $art->_dbh();

	my $property_types = $art->enum_system_property(SYSTEM_TYPE_ID => $self->cached_info('SYSTEM_TYPE_ID'), EXTENDED_OUTPUT => 1);

	# i numeri arrivano tutti con il '.' come separatore decimale
	my $old_nls_lang = $art->_dbh()->get_session_parameters('NLS_NUMERIC_CHARACTERS');
	$art->_dbh()->set_session_parameters('NLS_NUMERIC_CHARACTERS' => '.,');

	my $ret = {};

	foreach my $prop (@{$params{PROPERTIES}}) {
		# gestisco solo le property di tipo MEASURE;
		next unless($property_types->{$prop}->{IS_MEASURE});

		my $id_tdt = $art->get_system_property_id($prop);
		my $field_meas_sql;
		my $field_ts_sql;
		my $group_sql;
		my $prepare_name;
		if ($params{AGGREGATE_BY}){
			my $format;
			if ($params{AGGREGATE_BY} eq 'hour'){
				$format = 'yyyy-mm-dd hh24';
				$prepare_name = __PACKAGE__.'_SEL_MEAS_TABLE_ah'.$id_tdt;
				$field_ts_sql = qq{to_char(cast(ts as date),'$format')||':00:00' TS};
			} elsif ($params{AGGREGATE_BY} eq 'day'){
				$format = 'yyyy-mm-dd';
				$prepare_name = __PACKAGE__.'_SEL_MEAS_TABLE_ad'.$id_tdt;
				$field_ts_sql = qq{to_char(cast(ts as date),'$format') TS};
			} elsif ($params{AGGREGATE_BY} eq 'month'){
				$format = 'yyyy-mm';
				$prepare_name = __PACKAGE__.'_SEL_MEAS_TABLE_am'.$id_tdt;
				$field_ts_sql = qq{to_char(cast(ts as date),'$format') TS};
			}
			$field_meas_sql = q{avg(misura) misura};
			$group_sql = qq{group by to_char(cast(ts as date),'$format')};
			
		} else {
			$field_meas_sql = 'misura';
			$field_ts_sql = 'ts';
			$group_sql = '';
			$prepare_name = __PACKAGE__.'_SEL_MEAS_TABLE_'.$id_tdt;
		}
		
		# definisco la prepare all'interno del loop perchè viene eseguita solo quando non presente
		my $sth_measure = $art->_create_prepare($prepare_name, "
			select ".$field_meas_sql.",
				".$field_ts_sql."
			from DTM_".$id_tdt." e
			where id_sistema = ?
				and ts between to_timestamp_tz(?,?) and to_timestamp_tz(?,?)
				".$group_sql."
			order by 2
		");
		return undef
			unless $sth_measure;
		
		my @bind_params = (
			$self->id(),
			$params{FROM},
			,$art->get_default_iso_date_format_oracle(),
			$params{TO},
			,$art->get_default_iso_date_format_oracle()
		);
		my $tmp_res = $sth_measure->fetchall_hashref(@bind_params);
		unless ($tmp_res) {
			$self->art()->last_error("Error selecting property_measurements for '$prop': " . $art->_dbh()->get_errormessage());
			return 0;	
		}
		
		my @t;
		if ($property_types->{$prop}->{TYPE} =~/^(INTEGER|FLOAT)$/){
			@t = map {[$_->{TS}, $_->{MISURA}*1]} @$tmp_res;
		} else {
			@t = map {[$_->{TS}, $_->{MISURA}]} @$tmp_res;
		}

		$ret->{$prop} = \@t;
	}

	$art->_dbh()->set_session_parameters('NLS_NUMERIC_CHARACTERS' => $old_nls_lang->{NLS_NUMERIC_CHARACTERS});

	return $ret;
}

=head2 I<object>->B<find_property_measurements>( REPONSE => {KEYS => I<ARRAY>}, [FROM => I<SCALAR>, TO => I<SCALAR>, F_FROM => I<SCALAR>, F_TO => I<SCALAR>] )

Effettua una ricerca sulle misure correlate restituendo i campi richiesti.

=over 4

=item B<RESPONSE.KEYS>

Elenco dei campi di cui si richiede l'estrazione. Chiave speciali: @TS per recuperare il timestamp della misura, @TS_INSERT per recuperare il timestamp della ricezione della misura

=item B<FROM>

Data di inizio delle misure

=item B<TO>

Data di fine delle misure

=item B<F_FROM>

Campo da utilizzare per le misure per la data di inizio delle misure

=item B<F_TO>

Campo da utilizzare per le misure per la data di fine delle misure

=item B<PROPERTIES> I<hashref>

Un I<hashref> delle proprieta' da modificare nel sistema

=item B<SORT_TS_INSERT>

Se impostato a '1' eseguo l'ordinamento su TS_INSERT invece che TS

=item B<SEARCH_TS_INSERT>

Se impostato a '1' eseguo la ricerca su TS_INSERT invece che TS

=item B<LIMIT>

Indica il numero massimo di record da estrarre

=back

=cut
sub find_property_measurements {
	my $self = shift;
	my $errmsg;
	my %params = @_;
	
	$self->art()->last_error($errmsg)
		&& return undef
		unless	$self->check_named_params(
			 ERRMSG 				=> \$errmsg
			,IGNORE_EXTRA_PARAMS 	=> 0
			,PARAMS 				=> \%params
			,MANDATORY => {
				RESPONSE	=> { isa => 'HASH' },
			}
			,OPTIONAL => {
				PROPERTIES_EQUAL	=> { isa => 'HASH' },
				FROM				=> { isa => 'SCALAR' },
				TO					=> { isa => 'SCALAR' },
				F_FROM				=> { isa => 'SCALAR' },
				F_TO				=> { isa => 'SCALAR' },
				SORT_TS_INSERT		=> { isa => 'SCALAR', list => [1]},
				SEARCH_TS_INSERT		=> { isa => 'SCALAR', list => [1]},
				LIMIT				=> { isa => 'SCALAR', pattern => qr/^\d+$/}
			}
		);

	$self->art()->last_error($errmsg)
		&& return undef
		unless	$self->check_named_params(
			 ERRMSG 				=> \$errmsg
			,IGNORE_EXTRA_PARAMS 	=> 0
			,PARAMS 				=> \%{$params{RESPONSE}}
			,MANDATORY => {
				KEYS	=> { isa => 'ARRAY' },
			}
		);

	unless (scalar @{$params{RESPONSE}->{KEYS}}){
		$self->art()->last_error("Param RESPONSE.KEYS must contain at least one key");
		return undef;
	}

	if (exists $params{PROPERTIES_EQUAL}){
		# la chiave PROPERTIES_EQUAL deve contenere almeno un elemento
		unless (scalar %{$params{PROPERTIES_EQUAL}}){
			$self->art()->last_error("Param PROPERTIES_EQUAL must contain at least one key");
			return undef;
		}
	}

	my $art = $self->art();
	my $db = $art->_dbh();

	$art->clear_last_error();

	my $property_types = $art->enum_system_property(SYSTEM_TYPE_ID => $self->cached_info('SYSTEM_TYPE_ID'), EXTENDED_OUTPUT => 1);

	my @join_tables;
	my $index = 0;
	my $sql = 'select 1 DUMMY';
	if (grep {$_ eq '@TS'} @{$params{RESPONSE}->{KEYS}}){
		$sql.=', dtm0.TS';
		@{$params{RESPONSE}->{KEYS}} = map {$_ if $_ ne '@TS'} @{$params{RESPONSE}->{KEYS}};
	}
	if (grep {$_ eq '@TS_INSERT'} @{$params{RESPONSE}->{KEYS}}){
		$sql.=', dtm0.TS_INSERT';
		@{$params{RESPONSE}->{KEYS}} = map {$_ if $_ ne '@TS_INSERT'} @{$params{RESPONSE}->{KEYS}};
	}

	# rimuove le chiavi vuote
	@{$params{RESPONSE}->{KEYS}} = grep {$_ ne ''} @{$params{RESPONSE}->{KEYS}};

	# verifico che sia una chiave richiedibile
	for my $prop (@{$params{RESPONSE}->{KEYS}}){
		# gestisco solo le property di tipo MEASURE e non BLOB
		unless ($property_types->{$prop}->{IS_MEASURE}){
			$self->art()->last_error("Key ".$prop." is not a measure", p => $prop);
			return undef;
		}
		# per ora non le getiamo poi vedremo
		if ($property_types->{$prop}->{TYPE} eq 'BLOB') {
			$self->art()->last_error("Key ".$prop." is a BLOB: cannot use this type of key");
			return undef;
		}

		my $id_tdt = $art->get_system_property_id($prop);

		unless (grep {$_->{ID_TDT} eq $id_tdt} @join_tables){
			push @join_tables, {
				TDT		=> $prop,
				ID_TDT	=> $id_tdt,
				INDEX	=> $index,
				KEY		=> 1
			};
			$index++;
		}
	}

	# verifico che sia una properties filtrabile
	for my $prop (keys %{$params{PROPERTIES_EQUAL}}){
		# gestisco solo le property di tipo MEASURE e non BLOB
		unless ($property_types->{$prop}->{IS_MEASURE}){
			$self->art()->last_error("Key ".$prop." is not a measure");
			return undef;
		}
		# per ora non le getiamo poi vedremo
		if ($property_types->{$prop}->{TYPE} eq 'BLOB') {
			$self->art()->last_error("Key ".$prop." is a BLOB: cannot use this type of key");
			return undef;
		}
		my $id_tdt = $art->get_system_property_id($prop);

		my @x = grep {$_->{ID_TDT} eq $id_tdt} @join_tables;
		if (scalar @x){
			$x[0]->{VALUE} = $params{PROPERTIES_EQUAL}->{$prop};
		} else {
			push @join_tables, {
				TDT		=> $prop,
				ID_TDT	=> $id_tdt,
				INDEX	=> $index,
				VALUE	=> $params{PROPERTIES_EQUAL}->{$prop}
			};
			$index++;
		}
	}

	my ($key_ts_from, $key_ts_to);
	$key_ts_from = $key_ts_to = 'dtm0.ts';

	for ('FROM', 'TO'){
		next unless defined $params{'F_'.$_};
		# gestisco solo le property di tipo MEASURE e non BLOB
		unless ($property_types->{$params{'F_'.$_}}->{IS_MEASURE}){
			$self->art()->last_error("Key ".$params{'F_'.$_}." is not a measure");
			return undef;
		}
		# per ora non le getiamo poi vedremo
		if ($property_types->{$params{'F_'.$_}}->{TYPE} ne 'TIMESTAMP') {
			$self->art()->last_error($params{'F_'.$_}." is not a TIMESTAMP: can not use for time search");
			return undef;
		}
		my $id_tdt = $art->get_system_property_id($params{'F_'.$_});
		my $index_found;

		my @x = grep {$_->{ID_TDT} eq $id_tdt} @join_tables;
		if (scalar @x){
			$x[0]->{'F_'.$_} = $params{'F_'.$_};
			$index_found = $x[0]->{INDEX};
		} else {
			push @join_tables, {
				TDT		=> $params{'F_'.$_},
				ID_TDT	=> $id_tdt,
				INDEX	=> $index,
				'F_'.$_	=> $params{'F_'.$_}
			};
			$index_found = $index;
			$index++;
		}

		if ($_ eq 'FROM'){
			$key_ts_from = 'dtm'.$index_found.'.ts';
		} elsif ($_ eq 'TO'){
			$key_ts_to = 'dtm'.$index_found.'.ts';
		}
		
	}
	
	# ciclo per costruire campi da estrarre
	for my $t (@join_tables){
		# verifico che sia una chiave da gestire
		next unless $t->{KEY};
		$sql.= ', dtm'.$t->{INDEX}.'.misura "'.$t->{TDT}.'"'
	}
	$sql.= '
		from sistemi s
	';

	# ciclo per costruire tabelle join
	my $first_join = 1;
	for my $t (@join_tables){
		if ($first_join){
			$sql.= ' left join dtm_'.$t->{ID_TDT}.' dtm'.$t->{INDEX}.' on dtm'.$t->{INDEX}.'.id_sistema = s.id_sistema';
			$first_join = 0;
		} else {
			$sql.= ' left join dtm_'.$t->{ID_TDT}.' dtm'.$t->{INDEX}.' on dtm'.$t->{INDEX}.'.id_sistema = s.id_sistema and dtm0.ts = dtm'.$t->{INDEX}.'.ts';
		}
	}
	$sql.= '
		where 1=1
		and s.id_sistema = '.$self->id()
	;

	# aggiungo le where condition
	for my $t (@join_tables){
		if (exists $t->{VALUE}){
			$sql.= ' and dtm'.$t->{INDEX}.'.misura = '.$art->_dbh()->quote($t->{VALUE});
		}
	}

	my $key_ts_from_sort = $key_ts_from.($params{SORT_TS_INSERT} ? '_insert' : '');
	my $key_ts_to_sort = $key_ts_to.($params{SORT_TS_INSERT} ? '_insert' : '');
	$key_ts_from .= '_insert' if defined $key_ts_from && $params{SEARCH_TS_INSERT};
	$key_ts_to .= '_insert' if defined $key_ts_to && $params{SEARCH_TS_INSERT};

	if (defined $params{FROM} && defined $params{TO}){
		$sql.= ' and '.$key_ts_from.' >= to_timestamp_tz('.$art->_dbh()->quote($params{FROM}).','.$art->_dbh()->quote($art->get_default_iso_date_format_oracle()).')';
		$sql.= ' and '.$key_ts_to.' <= to_timestamp_tz('.$art->_dbh()->quote($params{TO}).','.$art->_dbh()->quote($art->get_default_iso_date_format_oracle()).')';
	} elsif (defined $params{FROM}){
		$sql.= ' and '.$key_ts_from.' >= to_timestamp_tz('.$art->_dbh()->quote($params{FROM}).','.$art->_dbh()->quote($art->get_default_iso_date_format_oracle()).')';
	} elsif (defined $params{TO}){
		$sql.= ' and '.$key_ts_to.' <= to_timestamp_tz('.$art->_dbh()->quote($params{TO}).','.$art->_dbh()->quote($art->get_default_iso_date_format_oracle()).')';
	}
	
	#$sql.= ' order by '.$key_ts_from.($params{SORT_TS_INSERT} ? '_insert' : '').', '.$key_ts_to.($params{SORT_TS_INSERT} ? '_insert' : '');
	$sql.= ' order by '.$key_ts_from_sort.', '.$key_ts_to_sort;
	$sql.= ' OFFSET 0 ROWS FETCH NEXT '.$params{LIMIT}.' ROWS ONLY' if defined $params{LIMIT};

	# i numeri arrivano tutti con il '.' come separatore decimale
	my $old_nls_lang = $art->_dbh()->get_session_parameters('NLS_NUMERIC_CHARACTERS');
	$art->_dbh()->set_session_parameters('NLS_NUMERIC_CHARACTERS' => '.,');

	#print STDERR $sql."\n";

	my $tmp_res = $art->_dbh()->fetchall_hashref($sql);
	unless ($tmp_res) {
		$self->art()->last_error("Error selecting find_property_measurements: " . $art->_dbh()->get_errormessage());
		return undef;	
	}
	
	my @t;
	for my $r (@{$tmp_res}){
		for my $k (keys %$r){
			if ($k eq 'DUMMY'){
				delete $r->{$k};
				next;
			} elsif ($k eq 'TS'){
				next;
			}
			if (exists $property_types->{$k} && $property_types->{$k}->{TYPE} =~/^(INTEGER|FLOAT)$/){
				$r->{$k} = $r->{$k}*1 if defined $r->{$k};
			}
		}
		push @t, $r;
	}

	$art->_dbh()->set_session_parameters('NLS_NUMERIC_CHARACTERS' => $old_nls_lang->{NLS_NUMERIC_CHARACTERS});

	return \@t;}


=head2 I<object>->B<delete_property>( [ I<property1> [ , I<property2> [ , I<propertyN> ] ] ] )

Elimina le property specificate ritornando 1 in caso di successo oppure 0 in caso di fallimento (settando B<last_error()>).

=cut
sub delete_property {
	my $self = shift;
	my @properties = @_;
	my $art = $self->art();
	my $db = $art->_dbh();
	my $props = $self->cached_info('PROPERTIES');
	my $sql = '';
	local $@;

	$art->clear_last_error();

	return 1
		if scalar(@properties) == 0;

	$db->do("SAVEPOINT delete_system_property");
	
	my $system_id = $self->id();
	foreach my $prop ( @properties ) {
		$prop = $self->info('PROPERTIES_ALIAS_REVERSE')->{$prop} if $self->info('PROPERTIES_ALIAS_REVERSE') && exists $self->info('PROPERTIES_ALIAS_REVERSE')->{$prop};
		my $id_tdt = $art->get_system_property_id($prop);
		unless ($id_tdt) {
			$db->do("ROLLBACK TO SAVEPOINT delete_system_property");
			# il last_error e' gia' settato dal metodo
			return 0;
		}
		unless (grep(/^$prop$/, @$props)) {
			$db->do("ROLLBACK TO SAVEPOINT delete_system_property");
			$art->last_error("Bad PROPERTY '$prop' for SYSTEM_TYPE_NAME='${ \$self->type_name() }'!");
			return 0;
		}
		$sql = qq/
			INSERT INTO dati_tecnici_history
			SELECT	*
			FROM	dati_tecnici
			WHERE	id_sistema = $system_id
					AND id_tipo_dato_tecnico = $id_tdt
		/;
		unless (eval { $db->do($sql) } ) {
			$db->do("ROLLBACK TO SAVEPOINT delete_system_property");
			$art->last_error("Error saving system property '$prop' history!\n" . $@);
			return 0;
		}
		$sql = qq/
			DELETE	dati_tecnici
			WHERE	id_sistema = $system_id
					AND id_tipo_dato_tecnico = $id_tdt
		/;
		unless (eval { $db->do($sql) } ) {
			$db->do("ROLLBACK TO SAVEPOINT delete_system_property");
			$art->last_error("Error deleting system property '$prop'!\n" . $@);
			return 0;
		}
	}

	# gestisco i comportamenti
	unless (defined $self->_manage_behaviours(EVENT => 'on_change')){
		# rollback savepoint
		$db->do("rollback to savepoint delete_system_property");
		return 0;
	};

	return 1;
}

=head2 I<object>->B<inc_property>( PROPERTIES => { I<name> => I<value> [, ... ] } )

Incrementa/decrementa le property contatore, ritornando 1 in caso di successo oppure 0 in caso di fallimento (settando I<last_error()>).

Di seguito i parametri del metodo B<inc_property()>:

=item B<PROPERTIES> I<hashref>

Un I<hashref> delle proprieta' da modificare nel sistema

=back

=cut
sub inc_property {
	my $self = shift;
	my %params = @_;
	my $art = $self->art();

	$self->art()->last_error('System support not available!')
		&& return undef
			unless $self->art()->system_support();

	my $db = $art->_dbh();
	my $property_types = $art->enum_system_property(SYSTEM_TYPE_ID => $self->cached_info('SYSTEM_TYPE_ID'), EXTENDED_OUTPUT => 1);
	my $sql = '';
	local $@;

	$art->clear_last_error();

	# PROPERTY
	$params{PROPERTIES} = {} unless defined $params{PROPERTIES};
	$art->last_error("PROPERTIES must be an hash-ref or undefined!") &&
		return 0
			unless ref($params{PROPERTIES}) eq 'HASH';

	my $sth = $art->_create_prepare(__PACKAGE__.'_INSERT_DTC', "
		INSERT INTO DATI_TECNICI_CONTATORI (
			 id_sistema
			,id_tipo_dato_tecnico
			,incremento
		)
		VALUES (
			 ?
			,?
			,?
		)
	");
	return undef
		unless $sth;

	$db->do("SAVEPOINT inc_property");

	my $new_counter_properties;

	my $date = $db->get_sysdate();

	my $orig_NLS_NUMERIC_CHARACTERS = $db->get_session_parameters('NLS_NUMERIC_CHARACTERS');
	$db->set_session_parameters('NLS_NUMERIC_CHARACTERS' => '.,');
	foreach my $prop ( keys %{$params{PROPERTIES}} ) {
		my $id_tdt = $art->get_system_property_id($prop);
		unless (defined $property_types->{$prop}) {
			$db->set_session_parameters('NLS_NUMERIC_CHARACTERS' => $orig_NLS_NUMERIC_CHARACTERS);
			$db->do("ROLLBACK TO SAVEPOINT inc_property");
			$art->last_error("Bad PROPERTY '$prop' for SYSTEM_TYPE_NAME='${ \$self->type_name() }'!");
			return 0;
		}
		unless($property_types->{$prop}->{IS_COUNTER}) {
			$db->set_session_parameters('NLS_NUMERIC_CHARACTERS' => $orig_NLS_NUMERIC_CHARACTERS);
			$db->do("ROLLBACK TO SAVEPOINT inc_property");
			$art->last_error("PROPERTY '$prop' is not a counter!");
			return 0;
		}
		if (
			$params{PROPERTIES}->{$prop} !~ /^-?\d+$/
			&&
			$params{PROPERTIES}->{$prop} !~ /^-?\d+$/
			&&
			$params{PROPERTIES}->{$prop} !~ /^-?\d+(\.\d+)*$/
		){
			$db->set_session_parameters('NLS_NUMERIC_CHARACTERS' => $orig_NLS_NUMERIC_CHARACTERS);
			$db->do("ROLLBACK TO SAVEPOINT inc_property");
			$art->last_error("PROPERTY '$prop' must be a valid number!");
			return 0;
		}

		my @bind_params = (
			$self->id()
			,$id_tdt
			,$params{PROPERTIES}->{$prop}
		);
		unless ($sth->execute(@bind_params)) {
			$db->set_session_parameters('NLS_NUMERIC_CHARACTERS' => $orig_NLS_NUMERIC_CHARACTERS);
			$db->do("ROLLBACK TO SAVEPOINT inc_property");
			$art->last_error("Error incrementing system property '$prop': " . $art->_dbh()->get_errormessage());
			return 0;	
		}
		# il bignum serve per gestire numeri long in fase di somma
		{
			use bignum;
			$new_counter_properties->{$prop} = ($self->property($prop)||0)*1+$params{PROPERTIES}->{$prop}*1;
		}
		# invio ra per contatore
		my $rc = $art->get_system_sender()->insert(
			EVENT => 'PROPERTY_COUNTER',
			SOURCE_REF => $self->id().'-'.$art->get_system_property_id($prop),
			DATA => {
				ID_SISTEMA => $self->id(),
				ID_OPERATORE => $art->user()->id(),
				DATA_ESECUZIONE => $date,
				ID_TIPO_DATO_TECNICO => $art->get_system_property_id($prop),
				CLASSE_SISTEMA => ref($self)
			}
		);
		$self->art()->last_error('Unable to send property_counter')
			&& return undef
				unless ($rc);
	}

	$db->set_session_parameters('NLS_NUMERIC_CHARACTERS' => $orig_NLS_NUMERIC_CHARACTERS);

	# eseguo la _set_property
	unless ($self->_set_property(PROPERTIES => $new_counter_properties, DATE => $date)){
		# rollback savepoint
		$db->do("rollback to savepoint inc_property");
		return 0;
	};

	return 1;
}

=head2 I<object>->B<type_id>(), I<object>->B<type_name>()

Ritornano rispettivamente l'ID ed il nome del tipo sistema.

=cut
sub type_id {
	# Uso la property invece del metodo ->info() in quanto tale informazione non potra' mai cambiare
	$_[0]->cached_info('SYSTEM_TYPE_ID');
}
sub type_name {
	$_[0]->art()->get_system_type_name( $_[0]->type_id() );
}

=head2 I<object>->B<set_groups>( [ I<groupName1> [ , I<groupName2> [ , I<groupNameN> ] ] ] )

Aggiuge la visibilita' sul sistema ai gruppi indicati, ritornando 1 in caso di successo oppure 0 in caso di fallimento (settando I<last_error()>).

=cut
sub set_groups {
	my $self = shift;
	my @groups = @_;
	my $art = $self->art();
	my $db = $art->_dbh();
	my @sql = ();
	local $@;

	$art->clear_last_error();

	my $info_groups = $self->info('GROUPS');
	
	my $system_groups = defined $info_groups->[0] ? $info_groups : [];
	
	for my $group (@groups){

		$art->last_error("ROOT group cannot be added!")
			&& return 0
				if $group eq 'ROOT';

		$art->last_error("Invalid GROUP '$group!'")
			&& return 0
				unless $art->test_group_name($group);
				
		next if (grep { $_ eq $group  } @{$system_groups});
		
		push @sql, ' select '.$self->id().', '.$art->get_group_id($group).' from dual ';
	}
	
	return 1 unless scalar(@sql);
	
	$db->do("savepoint AASset_groups");

	my $sql = 'insert into permission_Sistemi (id_sistema, id_gruppo_abilitato) '.join ' union all ', @sql;
	
	$art->last_error("Error adding groups to system!\n" . $@)
			&& return 0
				unless eval { $db->do($sql) };

	# gestisco i comportamenti
	unless (defined $self->_manage_behaviours(EVENT => 'on_change')){
		# rollback savepoint
		$db->do("rollback to savepoint AASset_groups");
		return 0;
	};

	return 1;
}

=head2 I<object>->B<delete_groups>( [ I<groupName1> [ , I<groupName2> [ , I<groupNameN> ] ] ] )

Rimuove la visibilita' sul sistema ai gruppi indicati, ritornando 1 in caso di successo oppure 0 in caso di fallimento (settando I<last_error()>).

=cut
sub delete_groups {
	my $self = shift;
	my @groups = @_;
	my $art = $self->art();
	my $db = $art->_dbh();
	my @sql = ();
	local $@;

	$art->clear_last_error();
	
	my $info_groups = $self->info('GROUPS');
	
	my $system_groups = defined $info_groups->[0] ? $info_groups : [];

	for my $group (@groups){

		$art->last_error("ROOT group cannot be removed!")
			&& return 0
				if $group eq 'ROOT';

		$art->last_error("Invalid GROUP '$group!'")
			&& return 0
				unless $art->test_group_name($group);
				
		next unless (grep { $_ eq $group  } @{$system_groups});
		
		push @sql, $art->get_group_id($group);
	}
	
	return 1 unless scalar(@sql);
	
	$db->do("savepoint AASdelete_groups");

	my $sql = 'delete permission_Sistemi where id_sistema = '.$self->id().' and id_gruppo_abilitato in ('.(join ' , ', @sql).')';
	
	$art->last_error("Error deleting groups from system!\n" . $@)
			&& return 0
				unless eval { $db->do($sql) };

	# gestisco i comportamenti
	unless (defined $self->_manage_behaviours(EVENT => 'on_change')){
		# rollback savepoint
		$db->do("rollback to savepoint AASdelete_groups");
		return 0;
	};

	return 1;
}

=head2 I<object>->B<adopt_children>( CHILDREN => I<arrayref> )

Rende i sistemi specificati dall'argomento B<CHILDREN> "figli" del sistema corrente.

L'argomento B<CHILDREN> e' un I<arrayref> di oggetti B<API::ART::System>.

=cut
sub adopt_children {
	my $self = shift;
	my %params = @_;
	local $@;
	
	$self->art()->clear_last_error();
	# CHILDREN
	$self->art()->last_error("Missing CHILDREN!")
		&& return undef
			unless defined $params{CHILDREN};
	$self->art()->last_error("CHILDREN must be an array-ref!")
		&& return undef
			unless ref($params{CHILDREN}) eq 'ARRAY';
	$self->art()->last_error("No CHILD to adopt!")
		&& return undef
			unless scalar(@{$params{CHILDREN}});
	
	my @ids;
	# verifico che i CHILDREN non abbiano già un padre
	for my $ch (@{$params{CHILDREN}}){
		$self->art()->last_error("CHILD ".$ch->id()." already adopted!")
			&& return undef
				if defined $ch->parent_id();
		push @ids, $ch->id();
	}
	
	my $id = join ',', @ids;
	
	eval {
		$self->art()->_dbh()->do(qq(
			insert	into SISTEMI_RELAZIONE (A, B, TIPO, ID_OPERATORE)
			select	${\$self->id()}, id_sistema, 0, ${\$self->art()->user()->id()}
			from	sistemi
			where	id_sistema in ($id)
		));
	};
	$self->art()->last_error("Error adopting system!\n$@")
		&& return undef
			if $@;
	return scalar(@{$params{CHILDREN}});
}

=head2 I<object>->B<disinherit_children>( CHILDREN => I<arrayref> )

Disadotta i sistemi specificati dall'argomento B<CHILDREN> del sistema corrente.

L'argomento B<CHILDREN> e' un I<arrayref> di oggetti B<API::ART::System>.

=cut
sub disinherit_children {
	my $self = shift;
	my %params = @_;
	local $@;
	
	$self->art()->clear_last_error();
	# CHILDREN
	$self->art()->last_error("Missing CHILDREN!")
		&& return undef
			unless defined $params{CHILDREN};
	$self->art()->last_error("CHILDREN must be an array-ref!")
		&& return undef
			unless ref($params{CHILDREN}) eq 'ARRAY';
	$self->art()->last_error("No CHILD to disinherit!")
		&& return undef
			unless scalar(@{$params{CHILDREN}});
	
	# verifico che non mi abbiano passato figli duplicati
	my @children_to_disinherit;
	for my $ch (@{$params{CHILDREN}}){
		if (grep {$_ eq $ch->id} @children_to_disinherit){
			$self->art()->last_error("duplicate CHILD found");
			return undef;
		} else {
			push @children_to_disinherit, $ch->id;
		}
	}
	
	# verifico che ci siano tutti i figli
	my $id = join ',', @children_to_disinherit;
	my $count_children = $self->art()->_dbh()->fetch_minimalized("
		select count(1)
		from sistemi_relazione sr
		where sr.a = ".$self->id()."
		and b in ($id)
		and tipo = 0
		and data_fine is null
	");
	
	$self->art()->last_error("Not all children in the array are children of the system!")
		&& return undef
			if $count_children != scalar(@{$params{CHILDREN}});
	
	eval {
		$self->art()->_dbh()->do(qq(
			update SISTEMI_RELAZIONE
			set data_fine = sysdate
			where a = ${\$self->id()}
			and b in ($id)
			and tipo = 0
			and data_fine is null
		));
	};
	$self->art()->last_error("Error disinherit system!\n$@")
		&& return undef
			if $@;
	return scalar(@{$params{CHILDREN}});
}

=head2 I<object>->B<get_children>( [SYSTEM_TYPE_NAME => I<arrayref>, SHOW_ONLY_WITH_VISIBILITY => 1,0] )

Ritorna un I<arrayref> di oggetti di tipo API::ART::System rappresentante i
sistemi "figli" del sistema corrente

L'argomento B<SYSTEM_TYPE_NAME> e' un I<arrayref> di TIPI_SISTEMA.

L'argomento B<SHOW_ONLY_WITH_VISIBILITY> restituisce solo i sistemi figli su cui si ha visibilità se valorizzato a 1

=cut

sub _get_children {
	my $self = shift;
	my %params = @_;
	my $id = $self->id();
	
	my @where = ();
	my $join = '';
	
	$self->art()->last_error("SHOW_ONLY_WITH_VISIBILITY must be 0 or 1!") 
		&& (return undef)
			if defined $params{SHOW_ONLY_WITH_VISIBILITY} && !grep /^$params{SHOW_ONLY_WITH_VISIBILITY}$/, qw(0 1);
	
	if (
		(defined $params{SYSTEM_TYPE_NAME}  &&  scalar(@{$params{SYSTEM_TYPE_NAME}}) > 0 )
		||
		($params{SHOW_ONLY_WITH_VISIBILITY})
	){
		$join.=' join sistemi s on s.id_sistema = sr.b ';
		$join.=' join tipi_sistema ts on ts.id_tipo_sistema = s.id_tipo_sistema ';
		if (defined $params{SYSTEM_TYPE_NAME}  &&  scalar(@{$params{SYSTEM_TYPE_NAME}}) > 0 ){
			for my $sys (@{$params{SYSTEM_TYPE_NAME}}){
				$self->art()->last_error("Bad SYSTEM_TYPE_NAME ".$sys)
					&& return undef
						unless $self->art()->test_system_type_name($sys);
			}
			push @where, 'ts.nome_tipo_sistema IN (' . join(",", map { $self->art()->_dbh()->quote($_) } @{$params{SYSTEM_TYPE_NAME}}) . ')';
		}
		if ($params{SHOW_ONLY_WITH_VISIBILITY}){
			$join.= ' join permission_sistemi ps on ps.id_sistema = s.id_sistema and ps.id_gruppo_abilitato IN ( ' . join( ',', @{ $self->art()->user()->su()->activity()->groups() }) . ' )';
		}
	}
	
	my $where = '';
	$where = 'and ' . join("\n\t\tAND ", @where) if scalar(@where);
	
	my $children = $self->art()->_dbh()->fetchall_arrayref(qq(
		select		distinct sr.b
		from		sistemi_relazione sr
					$join
		where	sr.tipo = 0
			and sr.data_fine is null
			and sr.a = $id
			$where
		order by 1
	));
	
	return [ ] unless defined $children;
	return [ map { $_->[0] } @$children ];
}


sub get_children {
	my $self = shift;
	my %params = @_;
	local $@;
	
	if (defined $params{'SYSTEM_TYPE_NAME'} && ref ($params{'SYSTEM_TYPE_NAME'}) ne 'ARRAY'){
		$self->art()->last_error('Il parametro SYSTEM_TYPE_NAME deve essere un ARRAY');
		return undef;
	}
	
	# Trovo l'elenco dei "figli"
	my $children = $self->_get_children(%params);
	return undef unless defined $children;
	
	# Determino a quale classe fare riferimento per il nome della classe Collection da usare per la
	# creazione dell'istanza del 'figlio'
	my $class = eval '$' . ref($self) . '::DEFAULT_CHILDREN_CLASS';
	unless (defined $class){
		$class = $DEFAULT_CHILDREN_CLASS;
	}
	CLASS: {
		local $@;
		eval "require $class;";
		if ($@){
			$self->art()->last_error("Unable to require DEFAULT_CHILDREN_CLASS ($class): " .$@);
			return undef;
		}
	}

	# Ritorno un arrayref con le istanze dei "figli"
	my $ret = [
		map {
			$class->new( ART => $self->art() , ID => $_ )
		} @$children
	];
	
	# svuotiamo last_error() in quanto il map precedente lo lascia settato se non riesce ad istanziare uno o piu' sistemi (es: mancanza di permessi sul sistema figlio')
	$self->art()->clear_last_error();
	
	return $ret;
}

=head2 I<object>->B<add_child>( I<arguments> )

Crea un nuovo sistema figlio del sistema corrente.

B<NOTA>: per gli argomenti da passare al metodo fare riferimento al metodo B<create()> della classe
B<API::ART::Collection::System>.

=cut
sub add_child {
	my $self = shift;
	my %params = @_;
	local $@;

	$self->art()->clear_last_error();

	my $collection_class = 'API::ART::Collection::System';
	
	my $child = eval {
		$collection_class->new(
			ART => $self->art()
		)->create( %params );
	};
	if ($@){
		$self->art()->last_error("Unable to create child: " .$@);
		return undef;
	}
	return undef unless $child;
	return undef unless $self->adopt_children(
		CHILDREN => [ $child ],
	);
	return $child;
}

=head2 I<object>->B<get_activities_id>()

=head2 I<object>->B<get_activities_object>()

I due metodi sono simili con l'unica differenza che B<get_activities_id()> ritorna 
un I<arrayref> di id attività, mentre B<get_activities_object()> ritorna un I<arrayref> di
riferimenti ad oggetti attività secondo le specifiche dei metodi find_id e find_object della class API::ART::Collection::Activity

I parametri consentiti sono descritti dalle documentazioni dei relativi metodi della classe API::ART::Collection::Activity a meno dei seguenti

=over 4

=item B<SYSTEM_*>

=back

=back

=cut

sub _filter_get_activities{
	my $self = shift;
	my %params = @_;
	
	#tutti i parametri della find che iniziano per SYSTEM non devono possono essere utilizzati
	for my $k (keys %params){
		if ($k =~/^(SYSTEM)/){
			$self->art()->last_error("Param $k not permitted!");
			return undef;
		}
	}
	
	return 1;
}

sub _get_coll_activity {
	my $self = shift;
	unless (exists $self->{COLLECTION_ACTIVITY}){
		$self->{COLLECTION_ACTIVITY} = eval{API::ART::Collection::Activity->new(ART => $self->art())};
		return undef 
			if $@;
	}
	return $self->{COLLECTION_ACTIVITY};
}

sub get_activities_id {
	my $self = shift;
	my %params = @_;
	
	return undef unless $self->_filter_get_activities(%params);
	my $coll_activity = $self->_get_coll_activity();
	return undef
		unless defined $coll_activity;
	return $coll_activity->find_id(SYSTEM_ID => [$self->id()], %params);
}
sub get_activities_object {
	my $self = shift;
	my %params = @_;
	
	return undef unless $self->_filter_get_activities(%params);
	my $coll_activity = $self->_get_coll_activity();
	return undef
		unless defined $coll_activity;
	return $coll_activity->find_object(SYSTEM_ID => [$self->id()], %params);
}

=head2 I<object>->B<parent_id>()

Ritorna l'ID del sistema "padre" o I<undef> se il sistema corrente non dipende da altri sistemi.

=cut
sub parent_id {
	$_[0]->info('PARENT_SYSTEM_ID');
}

=head2 I<object>->B<parent>()

Ritorna un'istanza del sistema padre.

=cut
sub parent {
	my $self = shift;
	my $pid = $self->parent_id();
	return undef unless defined $pid;
	# Determino a quale classe fare riferimento per il nome della classe Collection da usare per la
	# creazione dell'istanza del 'padre'
	my $class = eval '$' . ref($self) . '::DEFAULT_PARENT_CLASS';
	CLASS: {
		local $@;
		eval "require $class;";
		if ($@){
			$self->art()->last_error("Unable to require DEFAULT_PARENT_CLASS ($class): " .$@);
			return undef;
		}
	}
	
	return $class->new(
		ART => $self->art() ,
		ID => $self->parent_id()
	);
}

=head2 I<object>->B<parent_type_id>(), I<object>->B<parent_type_name>()

Ritornano rispettivamente l'ID ed il nome del tipo sistema del sistema padre.

=cut
sub parent_type_id {
	my $self = shift;
	my $p = $self->parent();
	return undef unless defined $p;
	return $p->cached_info('SYSTEM_TYPE_ID');
}
sub parent_type_name {
	my $self = shift;
	my $p = $self->parent();
	return undef unless defined $p;
	return $p->cached_info('SYSTEM_TYPE_NAME');
}


=head2 I<object>->B<parent_property>( [ I<property1> [ , I<property2> [ , I<propertyN> ] ] ] )

Metodo "shortcut" che restituisce le proprieta' del padre del sistema corrente:

	...
	my $pp = $s->parent_property(); # equivale a $s->parent()->property()
	...

Se viene passato il nome di una sola proprieta' ritorna uno I<scalar>.

Se vengono passati piu' nomi di proprieta' oppure senza argomenti, ritorna un I<hashref>.

Nel caso in cui il sistema non abbia un padre restituisce B<undef>.

=cut
sub parent_property {
	my $self = shift;
	my $p = $self->parent();
	return undef
		unless $p;
	return $p->property(@_);
}

=head2 I<object>->B<ancestor_id>()

Ritorna l'ID del sistema "progenitore" o I<undef> se il sistema non ha progenitori.

=cut
sub ancestor_id {
	my $self = shift;
	my $ancestor = $self->_get_ancestor($self);
	return defined $ancestor ? $ancestor->id() : undef;
}

=head2 I<object>->B<ancestor>()

Ritorna un'istanza del sistema progenitore.

=cut
sub ancestor {
	my $self = shift;
	return $self->_get_ancestor($self);
}

=head2 I<object>->B<ancestor_type_id>(), I<object>->B<ancestor_type_name>()

Ritornano rispettivamente l'ID ed il nome del tipo sistema del sistema progenitore.

=cut
sub ancestor_type_id {
	my $self = shift;
	my $p = $self->ancestor();
	return undef unless defined $p;
	return $p->cached_info('SYSTEM_TYPE_ID');
}
sub ancestor_type_name {
	my $self = shift;
	my $p = $self->ancestor();
	return undef unless defined $p;
	return $p->cached_info('SYSTEM_TYPE_NAME');
}


=head2 I<object>->B<ancestor_property>( [ I<property1> [ , I<property2> [ , I<propertyN> ] ] ] )

Metodo "shortcut" che restituisce le proprieta' del progenitore del sistema corrente:

	...
	my $pp = $s->ancestor_property(); # equivale a $s->ancestor()->property()
	...

Se viene passato il nome di una sola proprieta' ritorna uno I<scalar>.

Se vengono passati piu' nomi di proprieta' oppure senza argomenti, ritorna un I<hashref>.

Nel caso in cui il sistema non abbia un progenitore restituisce B<undef>.

=cut
sub ancestor_property {
	my $self = shift;
	my $p = $self->ancestor();
	return undef
		unless $p;
	return $p->property(@_);
}

=head2 I<object>->B<dump>( %params )

Restituisce un hash che rappresenta il sistema come l'omologo formato B<JSON> restituito dall'B<API::ART::REST>
C<GET /systems/:ID> (vedi L<http://dvmas003.ict.sirti.net:10199/doc/ArtRestApi/index.html#api-System-getSystem>).

Le chiavi di primo livello sono le seguenti:

 {
   "id" => ...
   "info" => ...
   "properties" => ...
 }

=over

=item B<I<%params>>

=item * B<C<EXCLUDE_INFO>> <C<0|1>>

Esclude la chiave C<info>, default 0

=item * B<C<EXCLUDE_PROPERTIES>> <C<0|1>>

Esclude la chiave C<properties>, default 0

=item * B<C<SPLIT_PROPERTIES>> <C<0|1>>

Se impostato a 1 nella chiave C<properties> non ci saranno le properties di tipo MULTIPLEX (array),
che verranno invece inserite in una chiave dedicata chiamata C<propertiesArray>, default 0

=item * B<C<INCLUDE_ACTIVITIES>> <C<0|1>>

Include la chiave C<activities>, default 0

=back

=cut
sub dump() {
	my $self = shift;
	my $errmsg;
	my %params = @_;
	
	$self->art()->last_error($errmsg)
		&& return undef
		unless	$self->check_named_params(
			 ERRMSG 				=> \$errmsg
			,IGNORE_EXTRA_PARAMS 	=> 0
			,PARAMS 				=> \%params
			,MANDATORY => {}
			,OPTIONAL => {
				 EXCLUDE_INFO => { isa => 'SCALAR', list => [0,1] }
				,EXCLUDE_PROPERTIES => { isa => 'SCALAR', list => [0,1] }
				,INCLUDE_ACTIVITIES => { isa => 'SCALAR', list => [0,1] }
				,SPLIT_PROPERTIES => { isa => 'SCALAR', list => [0,1] }
			}
		);
	
	for ('EXCLUDE_INFO', 'EXCLUDE_PROPERTIES', 'SPLIT_PROPERTIES'){
		$params{$_} = 0 unless defined $params{$_};
	}
	
	my $str = {
		 "id" => $self->id() * 1
	};
	
	my $info = $self->info();
	
	unless ($params{EXCLUDE_INFO}) {
		$str->{"info"} = {
			 "type"				=> $self->art()->get_system_type_name($info->{"SYSTEM_TYPE_ID"})
			,"description"		=> $info->{"SYSTEM_NAME"}
			,"active"			=> $info->{"ACTIVE"} ? $JSON::true : $JSON::false
			,"disabled"			=> $info->{"DISABLED"} ? $JSON::true : $JSON::false
			,"ended"			=> $info->{"ENDED"} ? $JSON::true : $JSON::false
			,"creationDate"		=> $self->art()->get_iso_date_from_date($info->{"CREATION_DATE"})
			,"disablingDate"	=> defined $info->{"DISABLING_DATE"} ? $self->art()->get_iso_date_from_date($info->{"DISABLING_DATE"}) : undef
			,"endingDate"		=> defined $info->{"ENDING_DATE"} ? $self->art()->get_iso_date_from_date($info->{"ENDING_DATE"}) : undef
			,"properties"		=> $info->{"PROPERTIES"}
			,"groups"			=> $info->{"GROUPS"}
		};
		
		# NB: nel caso in cui non ci siano properties associate alle azioni del flusso, la API::ART popolano la
		# chiave PROPERTIES con un array di un elemento undefined ( [ undef ] ).
		# in questo caso faccio ritornare un array vuoto ( [] )
		$str->{"info"}->{properties} = []
			if(!defined $str->{"info"}->{properties}->[0]);
	}
	
	unless($params{EXCLUDE_PROPERTIES}){
		$str->{"properties"} = $self->property();
		if ($params{SPLIT_PROPERTIES}){
			
			my $system_type_id = $info->{'SYSTEM_TYPE_ID'};
	
			my $property_types = $self->{ART}->enum_system_property(SYSTEM_TYPE_ID => $system_type_id, EXTENDED_OUTPUT => 1);

			$str->{"propertiesArray"} = {};

			for my $kk (keys %{$property_types}){
				if ($property_types->{$kk}->{MULTIPLEX}){
					$str->{"propertiesArray"}->{$kk} = $str->{"properties"}->{$kk};
					delete $str->{"properties"}->{$kk};
				}
			}

		}
	}
	
	if ($params{INCLUDE_ACTIVITIES}){
		my $activities = $self->get_activities_object();
		$str->{"activities"} = [];
		for my $act (@{$activities}){
			push @{$str->{"activities"}}, $act->dump();
		}
	}
	
	return $str;	
}

=head2 I<object>->B<set_description>( I<scalar>, [FORCE => I<scalar>] )

Provvede all'aggiornamento della descrizione del sistema se possibile farlo

Ritorna true in caso di successo, false altrimenti.

=over 4

=item B<FORCE>

Opzionale. Permette l'aggiornamento della descrizione anche se il sistema non è attivo

=back

=cut

sub set_description(){
	my $self = shift;
	my $description = shift;
	my %params = @_;
        $self->art()->clear_last_error();
        my $errmsg;

        croak $errmsg
                unless  $self->check_named_params(
                         ERRMSG                                 => \$errmsg
                        ,IGNORE_EXTRA_PARAMS    => 0
                        ,PARAMS                                 => \%params
                        ,MANDATORY => { }
                        ,OPTIONAL => {
                                FORCE   => { isa => 'SCALAR', list => [ 1 ] }
                        }
                );

        $self->art()->last_error("Missing description")
                && return undef
                        unless defined $description;

        unless ($params{FORCE}){
		$self->art()->last_error("Unable to set description for system not active")
			&& return undef
				unless ($self->is_active);
	}

	my $sql = qq(
		update sistemi
		set descrizione = ?
		WHERE id_sistema = ?
	);
	
	$self->{ART}->_create_prepare(__PACKAGE__.'__sdesc', $sql);

	# imposto il savepoint
	$self->art()->_dbh()->do("SAVEPOINT AASset_desc_".$self->id());
	
	my $update_desc = $self->{ART}->_create_prepare(__PACKAGE__.'__sdesc')->do($description,$self->id());

	unless (defined $update_desc){
		$self->art()->last_error("Unable to set description");
		$self->art()->_dbh()->do("rollback to SAVEPOINT AASset_desc_".$self->id());
		return undef;
	}

	# sovrascrivo per leggere valori dopo aggiornamento
	$self->info();

	return 1;
}

=head2 I<object>->B<system_property>( [ I<property1> [ , I<property2> [ , I<propertyN> ] ] ] )

Ritorna le SYSTEM PROPERTIES definite nel tipo sistema

Se viene passato il nome di una sola proprieta' ritorna uno I<scalar>.

Se vengono passati piu' nomi di proprieta' oppure senza argomenti, ritorna un I<hashref>.

=cut
sub system_property {
	my $self = shift;
	my @keys = @_;
	my $act_props_conf = $self->art()->get_system_properties_group(SYSTEM_TYPE_NAME => [$self->cached_info('SYSTEM_TYPE_NAME')]);
	my $act_props = [];
	for my $p (@{$act_props_conf}){
		for my $p_hash (@{$p->{PROPERTIES}}){
			push @{$act_props} , $p_hash->{NAME} if ! grep { $_ eq $p_hash->{NAME} } @{$act_props} ;
		}
	}
	
	my $err=undef;
	
	my @keys_filter = ();
	if ( scalar(@keys) == 0 ) {
		@keys_filter = @{$act_props};
	} else {
		for my $p (@keys){
			unless (grep {$_ eq $p} @{$act_props}){
				$err.="TDT ".$p." non presente nell'elenco delle SYSTEM PROPERTY!\n";
			} else {
				push @keys_filter, $p;
			}
		}
	}
	
	croak ($err) if defined $err;

	my $ret;	
	if(scalar @keys == 1) {
		# siccome nella richiesta hai esplicitamente richiesto una sola activity property ti ritorno uno scalar
		$ret = $self->property(@keys_filter);
	} else {
		my $props = $self->property(@keys_filter);
		if(scalar @keys_filter == 1) {
			$ret = { $keys_filter[0] => $props };
		} else {
			$ret = $props;
		}
	}
	return $ret;
}

=head2 I<object>->B<system_property_group>(  )

Ritorna le SYSTEM PROPERTIES con il loro raggruppamento

=over 4

=item La struttura risultante e' la seguente:

	<arrayref> => [
			{
				GROUP => <scalar>,
				ID => <scalar>,
				PROPERTIES => [
					{
						'NAME' => <scalar>,
						'VALUE' => <scalar>,
						'READ_ONLY' => <Y|N>,
						'EXPIRED' => <Y|N>,
						'NOT_YET_USED' => <Y|N>
					}
				]
			}, ...
	]

=back

=cut
sub system_property_group {
	my $self = shift;
	my $system_property = $self->system_property();
	
	my @ret = ();
	
	my $keys = $self->art()->enum_system_property(SYSTEM_TYPE_NAME => $self->cached_info('SYSTEM_TYPE_NAME'));
	
	return undef unless $keys;
	
	for my $key (keys %{$keys}){
		push @ret, $key unless grep {$_ eq $key} @ret;
	}

	
	my $sys_props_conf = $self->art()->get_system_properties_group(SYSTEM_TYPE_NAME => [$self->cached_info('SYSTEM_TYPE_NAME')]);
	
	my $sys_props = [];
	for my $p (@{$sys_props_conf}){
		my $tmp;
		
		for my $k (keys %{$p}){
			if ($k ne 'PROPERTIES'){
				$tmp->{$k} = $p->{$k};
			} else {
				my $tmp_props = [];
				for my $p_hash (@{$p->{$k}}){
					next if (!exists $system_property->{$p_hash->{NAME}});
					$p_hash->{VALUE} = $system_property->{$p_hash->{NAME}};
					$p_hash->{NOT_YET_USED} = (grep {$_ eq $p_hash->{NAME}} @ret) ? 'N' : 'Y';
					push @{$tmp_props}, $p_hash;
				}
				$tmp->{$k} = $tmp_props;
			}
		}
		push @{$sys_props}, $tmp;
	}
	
	return $sys_props;
	
}

1;

