package API::ART::System::Behaviours::RefreshAllSystemActivities;
# public 
use strict;
use warnings;
use Carp;
use Data::Dumper;

=head1 NAME

B<API::ART::System::Behaviours::RefreshAllSystemActivities> - Comportamento per osservare i cambiamenti sul sistema e aggiornare le attività agganciate

=head1 SYNOPSIS

	# Uses needed packages
	use API::ART;
	use API::ART::System;

	# Create API::ART instance...
	my $art = API::ART->new(.......);
	# Create API::ART::System instance...
	my $a = API::ART::System->new(ART => $art, ID => 349522);

	my $ret = API::ART::System::Behaviours::RefreshAllSystemActivities::do($a,'RefreshAllSystemActivities');
	return undef unless (defined $ret);

=head1 DESCRIPTION

Questo package statico consente di osservare i cambiamenti sul sistema e aggiornare le attività agganciate

=head1 METHODS

Di seguito i metodi esposti dalla classe API::ART::System::Behaviours::RefreshAllSystemActivities.

=cut

=head2 I<object>->B<do>(<API::ART::System>, <BEHAVIOR_EVENT>)

Effettua gli aggiornamenti delle attività in funzione dei cambiamenti del sistema

=cut

sub do { # static method 
	my $system = shift;
	my $behaviour_event = shift;
	
	# recupero le attività associate
	my $activities = $system->get_activities_object();
	return undef unless defined $activities;

	for my $act (@{$activities}){
		return undef
			unless $act->refresh();
	}

	return 1;
}

1;

