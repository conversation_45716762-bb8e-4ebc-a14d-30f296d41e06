package API::ART::System::MQ::Consumer::PropertyCounter;

use strict;
use warnings;
use Carp;
use Data::Dumper;

use SIRTI::ART::RemoteActivity::Target;

use base 'SIRTI::Queue::EventConsumer';

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_event_types
{
	return ['PROPERTY_COUNTER'];
}

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_source_refs
{
	return [];
}

sub init
{
	my $self = shift;
	my $logger = $self->logger();
	my $art = $self->art();
	
	$self->{RA_QUEUE} = eval {
	
		$logger->debug("Creazione RA TARGET session per verifica pending");
		
		SIRTI::ART::RemoteActivity::Target->new(
			DB => $art->_dbh(),
			SOURCE_CONTEXT => $self->work_context('QUEUE_SOURCE_CONTEXT'),
			TARGET_CONTEXT => $self->work_context('QUEUE_TARGET_CONTEXT'),
			SESSION_DESCRIPTION => 'Inizializzazione coda per verifica pending'
		);
	};
	
	if ( $@ || !defined( $self->{RA_QUEUE} ) ) {
		my $msg = "Non posso inizializzare le Remote Activity TARGET: $@";
		$logger->fatal( "Non posso inizializzare le Remote Activity TARGET: $@" );
		croak $msg;
	}

	# viene modificata all'init perchè tanto servirà sempre così in questo ramq
	$art->_dbh()->set_session_parameters('NLS_NUMERIC_CHARACTERS' => '.,');

	$self->{PREPARE_SQL} = {
		"SUMMARY" => $art->_create_prepare(__PACKAGE__.'_S', '
			select sum(incremento) INCREMENTO, count(1) NUM_RECORD
			from DATI_TECNICI_CONTATORI
			where id_sistema = ?
				and id_tipo_dato_Tecnico = ?
		'),
		"DELETE" => $art->_create_prepare(__PACKAGE__.'_D', '
			delete DATI_TECNICI_CONTATORI
			where id_sistema = ?
				and id_tipo_dato_Tecnico = ?
		'),
		"INSERT" => $art->_create_prepare(__PACKAGE__.'_I', '
			insert into DATI_TECNICI_CONTATORI (ID_SISTEMA, ID_TIPO_DATO_TECNICO, INCREMENTO)
			values
			(?, ?, ?)
		'),
	}
}

#
# override
#
# params
# EVENT : oggetto di tipo SIRTI::Queue::Event
#
# return:
# oggetto di tipo SIRTI::Queue::Response o undef (e set last_error()) in caso di problemi fatali
#
sub consume_event()
{
	my $self	= shift;
	my %params	= @_;
	my $event	= $params{EVENT};
	my $ra_istance = $self->{RA_QUEUE} ;
	my $art		= $self->art();
	my $errmsg;
	
	$self->logger()->info( "Elaboro un evento..." );
	$self->logger()->info( "EVENT NAME: " . $event->get_event_name() );
	$self->logger()->info( "SOURCE_REF: " . $event->get_source_ref() );
	
	for ('ID_SISTEMA', 'ID_OPERATORE', 'ID_TIPO_DATO_TECNICO', 'DATA_ESECUZIONE'){
		unless (defined $event->get_data()->{$_}){
			my $msg = "Parametro ".$_." obbligatorio mancante!";
			$self->logger()->error( $msg );
			return $self->consume( STATUS => 1, TARGET_REF => 'KO', REASON => $msg);
		}
	}

	my $id_sistema = $event->get_data()->{ID_SISTEMA};
	my $id_tipo_dato_tecnico = $event->get_data()->{ID_TIPO_DATO_TECNICO};

	# verifico se ci sono altre RA pending sullo stesso SOURCE_REF
	my @id = $ra_istance->find_pending( 
			EVENT		=> $self->get_managed_event_types,
			SOURCE_REF	=> $event->get_source_ref()
		);

	# lo scalar non può mai essere minore di 1 in quanto c'è sempre almeno l'id dell'evento che si sta gestendo
	if (scalar @id > 1){
		# se ne trovo più di uno significa che esiste un evento di notifica successivo non ancora gestito
		# per il quale devo inviare la notifica e quindi segno come gia' lavorato con nulla di fatto quello corrente
		my $msg = "Evento da non inoltrare in quanto presente notifica successiva";
		$self->logger()->info( $msg );
		return $self->consume(
			REASON     => $msg,
			STATUS     => '0',
			TARGET_REF => $id_sistema
		);
	}

	my $class = $event->get_data()->{CLASS_SISTEMA}||'API::ART::System';
	eval "require $class;";
	if ($@){
		my $message = "Impossibile eseguire require $class: $@";
		$self->logger()->error( $message );
		return $self->skip(REASON => $message);
	}

	my $system = $class->new(ART => $art, ID => $id_sistema);
	
	if ($art->last_error()) {
		my $message = "Si e' verificato un errore istanziando l'oggetto attivita': ".$art->last_error();
		$self->logger()->error($message);
		return $self->skip(REASON => $message);
	}

	$self->logger()->info("Agganciato sistema con id $id_sistema");

	# recupero il totale e lo scontro con l'attuale valore
	my $res = $self->{PREPARE_SQL}->{"SUMMARY"}->fetchall_hashref($id_sistema, $id_tipo_dato_tecnico);

	my $aggiorna = ($res->[0]->{NUM_RECORD} > 1) ? 1 : 0;
	
	if ($res->[0]->{INCREMENTO} ne $system->property($art->get_system_property_name($id_tipo_dato_tecnico))){
		# se è diverso utilizzo la _set_property che mi permette di aggiornare il valore anche dei dt contatori
		unless ($system->_set_property(PROPERTIES => {$art->get_system_property_name($id_tipo_dato_tecnico) => $res->[0]->{INCREMENTO}}, DATE => $event->get_data()->{DATA_ESECUZIONE})) {
			my $message = "Errore in fase di _set_property: " . $art->last_error();
			$self->logger()->error( $message );
			return $self->skip(REASON => $message);
		}
		# eseguo l'update per allineare ELK
		my $activities = $system->get_activities_object();
		unless (defined $activities){
			my $message = "Errore in fase di get_activities_object: " . $art->last_error();
			$self->logger()->error( $message );
			return $self->skip(REASON => $message); 
		}
		for my $act (@$activities){
			unless ($act->refresh()){
				my $message = "Errore in fase di refresh: " . $art->last_error();
				$self->logger()->error( $message );
				return $self->skip(REASON => $message);
			}
		}
	}

	$art->_dbh->do("SAVEPOINT PropertyCounter");
	if ($aggiorna){
		unless ($self->{PREPARE_SQL}->{"DELETE"}->execute($id_sistema, $id_tipo_dato_tecnico)) {
			$art->_dbh->do("ROLLBACK TO SAVEPOINT PropertyCounter");
			my $message = "Si è verificato un errore in fase di DELETE: " . $art->_dbh()->get_errormessage();
			$self->logger()->error( $message );
			return $self->skip(REASON => $message);
		}
		unless ($self->{PREPARE_SQL}->{"INSERT"}->execute($id_sistema, $id_tipo_dato_tecnico, $res->[0]->{INCREMENTO})) {
			$art->_dbh->do("ROLLBACK TO SAVEPOINT PropertyCounter");
			my $message = "Si è verificato un errore in fase di INSERT: " . $art->_dbh()->get_errormessage();
			$self->logger()->error( $message );
			return $self->skip(REASON => $message);
		}
	}

	my $message = "Sistema con id $id_sistema aggiornato correttamente";
	$self->logger()->info($message);
	
	return $self->consume( STATUS => 0, TARGET_REF => 'OK', REASON => ($message));
}

sub finish
{

}
1;
