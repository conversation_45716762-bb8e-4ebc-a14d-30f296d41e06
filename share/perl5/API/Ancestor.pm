package API::<PERSON><PERSON><PERSON>;

use strict;
use warnings;
use Carp;

use base 'SIRTI::Base';

use API::Debug;


# Variabile di classe per la gestione dei messaggi di debug
our $Debugging = 0;

# Costruttore
sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $self  = {
					DEBUG => 0,
					DEBUG_HANDLER => API::Debug->new(), 
	};
	return bless( $self, $class );
}

# bridge
sub debug {
	my $self = shift;

	#confess "usage: thing->debug(level)" unless @_ == 1;
	my $level = shift;
	if ( ref($self) ) {

		# a livello di istanza
		# verifico se il subclassamento e' avvenuto
		# in modo "tradizionale" oppure con "closure"
		# testando la presenza della chiave dell'hash:
		# se si verificano errori significa che $self
		# non e' un hash ma una sub
		local $@;
		eval { exists $self->{"DEBUG"}; };
		if ($@) {

			# subclassamento con "closure"
			return $self->("DEBUG") unless defined $level;
			return $self->( "DEBUG", $level );
		} else {

			# subclassamento "tradizionale"
			return $self->{"DEBUG"} unless defined $level;
			return $self->{"DEBUG"} = $level;
		}
	} else {

		# a livello di classe
		return $Debugging unless defined $level;
		return $Debugging = $level;
	}
}

sub debugger {
	my $self = shift;
	local $@; 
	eval { exists $self->{"DEBUG_HANDLER"}; };
	if ($@) {
		# subclassamento con "closure"
		return $self->("DEBUG_HANDLER");
	} else {
		# subclassamento "tradizionale"
		return $self->{"DEBUG_HANDLER"};
	}
}

sub warning {
	my $self = shift;
	$self->debugger()->warning(@_) if $self->debug();
}

sub dump {
	my $self = shift;
	$self->debugger()->dump(@_) if $self->debug();
}

sub die {
	my $self = shift;
	$self->debugger()->die(@_) if $self->debug();
}

# Gestione distruttore istanza di classe
sub DESTROY {
	my $self = shift;
	if ( $Debugging || ($self && $self->debug()) ) {
		warn "Destroying " . ref($self) . "\n";
	}
}

# Gestione terminazione classe
sub END {
	if ($Debugging) {
		warn "Ending " . __PACKAGE__ . "\n";
	}
}

1;
