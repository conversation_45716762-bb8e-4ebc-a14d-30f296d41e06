package API::Debug;

use strict;
use warnings;
use Carp 'verbose';

use Data::Dumper;

sub new {
	my $this = shift;
	my $class = ref($this) || $this;
	my $self = {
		DEBUGID => time,
	};
	return bless $self, $class;
}

sub _get_msg {
	my $self = shift;
	return $self->debugid(), '|', ''.localtime(time), '|', join('|', @_[0 .. $#_]);
}

sub dump {
	my $self = shift;
	print STDERR $self->_get_msg(@_), Dumper(\@_);
}


sub warning {
	my $self = shift;
	print STDERR $self->_get_msg(@_), "\n";
} 

sub die {
	my $self = shift;
	die $self->get_msg(@_) . "\n"; 
}

sub debugid {
	my $self = shift;
	return $self->{DEBUGID}; 
}

1;