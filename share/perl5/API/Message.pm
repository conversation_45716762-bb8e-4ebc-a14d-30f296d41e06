package API::Message;

###################################################################################
# API::Message
###################################################################################

#TODO: documentazione POD

use strict;
use warnings;
use Carp qw/verbose croak/;
use base qw(API::Message::Transport);

###################################################################################

sub new{
	
	my ($this, $conn, $src) = @_;
	my $class = ref($this) || $this;
	
	croak("campo CONN: SIRTI::DB o API::ART non trovati")
		unless ((ref($conn) eq "SIRTI::DB")||(ref($conn) eq "API::ART"));
	
	croak("campo SRC: sorgente non configurata") unless $src;

	my $self={
			DB		=> ref($conn) eq 'SIRTI::DB' ? $conn : $conn->_dbh(), 
			SRC		=> $src,
		};

	$class->SUPER::new($self);
	
	return bless $self, $class;
}

##################################################################################

sub get($$){
	my ($self,$param) = @_;
	my $out;
	local $@;
	eval {
		$out=$self->SUPER::get($param);
	};
	return ($@, $out);
}

sub filter($$){
	my ($self, $param) = @_;
	local $@;
	eval {
		$self->SUPER::filter($param);
	};
	return $@;
}

sub put($$$$$){
	my ($self, $ns, $rmt_action, $rmt_action_spec, $msg, $caller) = @_;
	local $@;
	eval{
		$self->SUPER::put($ns, $rmt_action, $rmt_action_spec, $msg, $caller);
	};
	
	return $@;
}

##################################################################################

sub DESTROY{
    my $self = shift;
    return undef;
}

##################################################################################

if (__FILE__ eq $0) {
	
}

1;
