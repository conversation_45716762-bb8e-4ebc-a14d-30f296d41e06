package API::Message::Queue;

use strict;
use warnings;
use Carp 'verbose';

####################################################################################
# VARIABILI GLOBALI
####################################################################################

my $STATUS={
			ST_CREATE		=>1,		# RMT_QUEUE_OUT	: inserimento
			ST_SENT			=>2,		# RMT_QUEUE_IN	: inserimento
			ST_ACK			=>3,		# RMT_EVENTS	: inserimento
			ST_PROCESSED	=>4,		# RMT_EVENTS	: processamento
		};

my $TABLES={
			TABLE_PUT		=>'RMT_QUEUE_OUT',
			SEQ_TABLE_PUT	=>'SEQ_RMT_QUEUE_OUT',
			TABLE_GET		=>'RMT_EVENTS'
		};
		
###################################################################################

sub new{
	
	my ($this, $param) = @_;
	my $class = ref($this) || $this;
	
	my $self={
			DB	=>$param->{DB},
			SRC	=>$param->{SRC},
		};
	
	return bless $self, $class;
}

###################################################################################

sub get{
	
	my ($self, $param) = @_;
	
	my $db=$self->{DB};
	
	unless ($self->{CURSOR}){
		$param={} unless defined $param; 
		$self->filter($param);
	}
	
	my $res;
	
	$res=$self->{CURSOR}->fetchrow_hashref() unless $self->{END_OF_CURSOR};
	
	if ($res){
		unless ($self->{UPDATE_PREPARE}){
			$self->{UPDATE_PREPARE}=$db->create_prepare("
				update $TABLES->{TABLE_GET} 
					set id_status='$STATUS->{ST_PROCESSED}' 
					where id_msg=?");
		}
		$self->{UPDATE_PREPARE}->do($res->{ID_MSG});

	} else {
		$self->{CURSOR}->finish();
		$self->{END_OF_CURSOR}=1;
	}
	 
	if (defined $param){
		$self->{CURSOR}->finish();
		$self->{CURSOR} = undef;
	}
	
	return $res;
}

sub put($$$$$){

	my ($self, $ns,$rmt_action,$rmt_action_spec,$body, $caller)=@_;
	
	my $db				= $self->{DB};
	my $src				= $self->{SRC};
	my $stato_finale	= $STATUS->{ST_CREATE};

	unless ($self->{INSERT_PREPARE}){
		$self->{INSERT_PREPARE}=$db->create_prepare(
			qq(
				insert into $TABLES->{TABLE_PUT}
					(	
						id_msg,
						namespace,
						rmt_action,
						rmt_action_spec,
						msg,
						id_status,
						src,
						caller,
						d_create
					) 
				values 
					(
					$TABLES->{SEQ_TABLE_PUT}.nextval,
					?, -- namespace
					?, -- rmt_action
					?, -- rmt_action_spec
					?, -- body
					?, -- stato_finale
					?, -- src
					?, -- caller
					sysdate
					)
			)
		);
	}
			
	my $res=$self->{INSERT_PREPARE}->do(
		$ns,
		$rmt_action,
		$rmt_action_spec,
		$body,
		$stato_finale,
		$src,
		$caller
	);
	
	return !$res;
}

sub filter($$){
	
	my ($self, $param)= @_;
	
	my $db=$self->{DB};
	
	my @where;
	
	push (@where,'ID_MSG='			.$db->quote($param->{ID_MSG}))			if $param->{ID_MSG};
	push (@where,'RMT_ACTION='		.$db->quote($param->{RMT_ACTION}))		if $param->{RMT_ACTION};
	push (@where,'NAMESPACE'		.$db->quote($param->{NAMESPACE}))		if $param->{NAMESPACE};
	
	push (@where,"id_status=$STATUS->{ST_ACK}");
	
	my $where_conditions='';
	if (scalar @where){
		$where_conditions='where '.join(' AND ',@where);
	}
	
	my $sql="
				select 
					id_msg,
					namespace,
					rmt_action,
					rmt_action_spec,
					msg,
					caller 
				from $TABLES->{TABLE_GET} 
				$where_conditions
				for update";
	
	$self->{END_OF_CURSOR}	=undef;
	$self->{CURSOR}			=$db->create_cursor($sql);
	
	return $self->{CURSOR};
}

###################################################################################

sub DESTROY{
    my $self = shift;
    return undef;
}

1;
