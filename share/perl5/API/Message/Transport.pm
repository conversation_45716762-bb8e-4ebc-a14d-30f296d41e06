package API::Message::Transport;

use strict;
use warnings;
use Carp 'verbose';

use Data::Dumper;

use base qw(API::Message::Queue);

use API::Message::Transport::Serialize;

###################################################################################

sub new{
	
	my ($this, $param) = @_;
	my $class = ref($this) || $this;
	
	my $self={
			DB	=>$param->{DB},
			SRC	=>$param->{SRC},
		};
	
	$class->SUPER::new($self);
	
	return bless $self, $class;
}

###################################################################################

sub get($$){
	my ($self, $param) = @_;
	my $msg=$self->SUPER::get($param);
	if ($msg){
		$msg->{RMT_ACTION_SPEC}	= API::Message::Transport::Serialize::deserialize($msg->{RMT_ACTION_SPEC});
		$msg->{MSG}				= API::Message::Transport::Serialize::deserialize($msg->{MSG});
	}
	return $msg;
}

sub filter($$){
	my ($self,$param) = @_;
	return $self->SUPER::filter($param);
}

sub put($$$$$){
	my ($self, $ns, $rmt_action, $rmt_action_spec, $msg, $caller) = @_;
	return $self->SUPER::put(
		$ns, 
		$rmt_action, 
		API::Message::Transport::Serialize::serialize($rmt_action_spec), 
		API::Message::Transport::Serialize::serialize($msg),
		$caller
	);
}

sub DESTROY{
    my $self = shift;
    return undef;
}

1;
