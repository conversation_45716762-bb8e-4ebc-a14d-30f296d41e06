package API::Tools::XML::Validator;

use strict;
use warnings;

use vars qw(@ISA);
@ISA = qw(XML::Xerces::PerlErrorHandler);

# use blib;
use XML::Xerces;
use IO::Handle;

sub new {
	my $this = shift;
	my $class = ref($this) || $this;
	my $self = {
			COUNTER_W 	=> 0,
			COUNTER_E 	=> 0,
			ERROR_MSG 	=> '',
	};
	return bless $self, $class;
}

sub warning {
	my ($self,$super)=@_;
	my $line = $super->getLineNumber;
	my $column = $super->getColumnNumber;
	my $message = $super->getMessage;
  	$self->{ERROR_MSG}.=sprintf "%s:[%s]:%d:%d:%s:%s",
    	$main::PROGRAM,$main::FILE,$line, $column, 'W', $message;
    $self->{COUNTER_W}++;
}

sub error {
	my ($self,$super)=@_;
	my $line = $super->getLineNumber;
	my $column = $super->getColumnNumber;
	my $message = $super->getMessage;

	# WORKROUND NAMESPACE ##################################################################################
	unless($message=~ /has a different target namespace from the one specified in the instance document/){
  		$self->{ERROR_MSG}.=sprintf "%s:[%s]:%d:%d:%s:%s",
    		$main::PROGRAM,$main::FILE,$line, $column, 'E', $message;
		$self->{COUNTER_E}++;
	}
}

sub fatal_error {
	my ($self,$super)=@_;
	my $line = $super->getLineNumber;
	my $column = $super->getColumnNumber;
	my $message = $super->getMessage;
  	$self->{ERROR_MSG}.=sprintf "%s:[%s]:%d:%d:%s:%s",
    	$main::PROGRAM,$main::FILE,$line, $column, 'F', $message;
	$self->{COUNTER_E}++;
}

sub get_warning_count() {
	my $self=shift;
	return $self->{COUNTER_W};
}

sub get_error_count() {
	my $self=shift;
	return $self->{COUNTER_E};
}

sub get_error_msg() {
	my $self=shift;
	return $self->{ERROR_MSG};
}

sub validate{

	my ($self,$param) = @_;

	my $xmlFile   = $param->{xmlfile};  		# File XML da parserizzare
	my $xmlString = $param->{xmlstring};		# Stringa XML da parserizzare
	my $xsdFile   = $param->{xsdfile} || '';	# Schema da associare al file XML da parserizzare
	my  $full_schema = $param->{nocheckschema} ? 0 : 1;
	
	local $@;

	$self->{COUNTER_W} = 0;
	$self->{COUNTER_E} = 0;
	$self->{ERROR_MSG} = '';

	##### debug ####################################
	# print STDERR "*"x80,"\n";
	# print STDERR "xml: $xmlFile\nxsd: $xsdFile\n";
	# print STDERR "*"x80,"\n";
	##### debug ####################################

	die "specificare o il nome file o la stringa xml da validare" unless ( $xmlFile || $xmlString );

	if ( $xmlFile && ! -r $xmlFile )  {
		print STDERR "$xmlFile: non leggibile \n";
		return 2;
	}


	if ( $xsdFile && ! -r $xsdFile)  {
		print STDERR "$xsdFile: non leggibile \n";
		return 2;
	}

	my  $namespace = 1;
	my  $schema = 1;

	$main::FILE = $xmlFile || 'XMLDATA';
	$main::PROGRAM = $0;
	$main::PROGRAM =~ s|.*/(\w+)|$1|;

	my $parser = XML::Xerces::XMLReaderFactory::createXMLReader();
	my $myerrhandler = $self;
	$parser->setErrorHandler($myerrhandler);

	# print as we parse
	STDERR->autoflush();

	#   my $contentHandler = new XML::Xerces::PerlContentHandler() ;
	#   $parser->setContentHandler($contentHandler) ;

	# handle the optional features
	eval {
		$parser->setFeature("$XML::Xerces::XMLUni::fgSAX2CoreNameSpaces", $namespace);
		$parser->setFeature("$XML::Xerces::XMLUni::fgXercesSchema", $schema);
		$parser->setFeature("$XML::Xerces::XMLUni::fgXercesSchemaFullChecking", $full_schema);

		# Associa, se richiesto, uno schema XML (xsd) esterno
		if ( $xsdFile  &&  $schema  &&  $full_schema ) {
			$parser->setProperty("$XML::Xerces::XMLUni::fgXercesSchemaExternalNoNameSpaceSchemaLocation", $xsdFile);
		}
		  
	};
	XML::Xerces::error($@) if $@;

	# and the required features
	eval {
	  $parser->setFeature("$XML::Xerces::XMLUni::fgXercesContinueAfterFatalError", 1);
	  $parser->setFeature("$XML::Xerces::XMLUni::fgXercesValidationErrorAsFatal", 0);
	  $parser->setFeature("$XML::Xerces::XMLUni::fgSAX2CoreValidation", 1);
	  $parser->setFeature("$XML::Xerces::XMLUni::fgXercesDynamic", 1);
	};
	XML::Xerces::error($@) if $@;

	eval {
		my $is;
		if ( $xmlFile ) {
			$is = XML::Xerces::LocalFileInputSource->new($xmlFile);
		} else {
			$is = XML::Xerces::MemBufInputSource->new($xmlString);
		}
		$parser->parse($is) ;
	}; 
	XML::Xerces::error($@) if $@;

	return(1) if ($myerrhandler->get_error_count() > 0);
	return(1) if ($myerrhandler->get_warning_count() > 0);
	return(0);
}

1;
