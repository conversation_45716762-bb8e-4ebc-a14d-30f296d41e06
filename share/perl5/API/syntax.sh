#!/bin/bash

. $HOME/etc/profile.sh

for pkg in $( find . -type f -name '*.pm' )
do
	echo -ne "\n\nChecking $pkg syntax..."
	perl -c $pkg 
	rc="$?"
#	if [ "$rc" == "0" ]; then
#		mkdir -p docs
#		f=$(echo $pkg | sed s/:/_/g)
#		perldoc -u $pkg | pod2pdf --footer-text "Copyright 2008 SIRTI S.p.A." --title "ART Application Programming Interface - $pkg" > docs/${f}.pdf
#		rc="$?"
#		if [ "$rc" == "0" ]; then
#			echo "La documentazione PDF del package $pkg e' stata salvata in docs/${f}.pdf"
#		else
#			echo "ERRORE $rc estraendo la documentazione di $pkg"
#		fi
#	else
#		echo "ERRORE $rc testando la presenza del package $pkg"
#	fi
done
exit
