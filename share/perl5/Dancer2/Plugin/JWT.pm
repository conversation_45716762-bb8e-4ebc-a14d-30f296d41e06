use strict;
use warnings;
package Dancer2::Plugin::JWT;
# ABSTRACT: JSON Web Token made simple for Dancer2

use Dancer2::Plugin;
use Crypt::JWT qw(encode_jwt decode_jwt);
use URI;
use URI::QueryParam;

our $VERSION = '0.015-SIRTI';

register_hook qw(jwt_exception);

my $secret;
my $alg;
my $enc;
my $need_iat = undef;
my $need_nbf = undef;
my $need_exp = undef;
my $need_leeway = undef;

my $cookie_domain = undef;
my $cookie_name = '_jwt';
my $cookie_expires = '4 weeks';
my $cookie_path = '/';
my $cookie_http_only = 0;

my $dont_send_response_auth_header;
my $request_auth_header = 'Authorization';
my $request_auth_token;
my $dont_send_access_control_expose_headers;
my $dont_recognize_token_in_query_string;
my $query_string_token_param_name = '_jwt';
my $dont_recognize_token_in_cookie;
my $response_token_header_name = 'Authorization';
my $zip_payload;

register jwt => sub {
    my $dsl = shift;
    my @args = @_;

    if (@args) {
        $dsl->app->request->var(jwt => $args[0]);
        $dsl->app->request->var('jwt_status' => 'present');
    }
    else {
        if ($dsl->app->request->var('jwt_status') eq "missing") {
            $dsl->app->execute_hook('plugin.jwt.jwt_exception' => 'No JWT is present');
            return undef;
        }
    }
    return $dsl->app->request->var('jwt');
};

register jwt_raw_token => sub {
    my $dsl = shift;
    if ($dsl->app->request->var('jwt_status') eq "missing") {
        $dsl->app->execute_hook('plugin.jwt.jwt_exception' => 'No JWT is present');
        return undef;
    }
    my %params = (
          payload => $dsl->app->request->var('jwt')
        , key => $secret
        , alg => $alg
    );
    $params{zip} = 'deflate'
        if $zip_payload;
    return encode_jwt(%params);
};

register jwt_request_auth_header => sub {
    return $request_auth_header;
};

register jwt_token_header_name => sub {
    return $response_token_header_name;
};

on_plugin_import {
    my $dsl = shift;

    my $config = plugin_setting;
    die "JWT cannot be used without a secret!" unless (exists $config->{secret} && defined $config->{secret});
    # For RSA and ES algorithms - path to keyfile or JWK string, others algorithms - just secret string
    $secret = $config->{secret};

    $cookie_domain = $config->{cookie_domain};

    if ( exists $config->{cookie_name} && defined $config->{cookie_name} ) {
        $cookie_name = $config->{cookie_name};
    }

    if ( exists $config->{cookie_expires} && defined $config->{cookie_expires} ) {
        $cookie_expires = $config->{cookie_expires};
    }

    if ( exists $config->{cookie_path} && defined $config->{cookie_path} ) {
        $cookie_path = $config->{cookie_path};
    }

    if ( exists $config->{cookie_http_only} && defined $config->{cookie_http_only} ) {
        $cookie_http_only = $config->{cookie_http_only};
    }

    if ( exists $config->{dont_send_response_auth_header} && defined $config->{dont_send_response_auth_header} ) {
        $dont_send_response_auth_header = $config->{dont_send_response_auth_header};
    }

    if ( exists $config->{request_auth_header} && defined $config->{request_auth_header} ) {
        $request_auth_header = $config->{request_auth_header};
    }

    if ( exists $config->{request_auth_token} && defined $config->{request_auth_token} ) {
        $request_auth_token = $config->{request_auth_token};
    }

    if ( exists $config->{dont_send_access_control_expose_headers} && defined $config->{dont_send_access_control_expose_headers} ) {
        $dont_send_access_control_expose_headers = $config->{dont_send_access_control_expose_headers};
    }

    if ( exists $config->{dont_recognize_token_in_query_string} && defined $config->{dont_recognize_token_in_query_string} ) {
        $dont_recognize_token_in_query_string = $config->{dont_recognize_token_in_query_string};
    }

    if ( exists $config->{query_string_token_param_name} && defined $config->{query_string_token_param_name} ) {
        $query_string_token_param_name = $config->{query_string_token_param_name};
    }

    if ( exists $config->{dont_recognize_token_in_cookie} && defined $config->{dont_recognize_token_in_cookie} ) {
        $dont_recognize_token_in_cookie = $config->{dont_recognize_token_in_cookie};
    }

    if ( exists $config->{response_token_header_name} && defined $config->{response_token_header_name} ) {
        $response_token_header_name = $config->{response_token_header_name};
    }

    if ( exists $config->{zip_payload} && defined $config->{zip_payload} ) {
        $zip_payload = $config->{zip_payload};
    }

    $alg = 'HS256';

    if ( exists $config->{alg} && defined $config->{alg} ) {
        my $need_enc = undef;
        my $need_key = undef;

        if ( $config->{alg} =~ /^([EHPR])S(256|384|512)$/ ) {
            my $type = $1;

            if ( $type eq 'P' || $type eq 'R' ) {
                $need_key = 1;
            } elsif ( $type eq 'E' ) {
                $need_key = 2;
            }

            $alg = $config->{alg};
        } elsif ( $config->{alg} =~ /^A(128|192|256)(GCM)?KW$/ ) {
            my $len = $1;

            if ( ( length( unpack( "H*", $secret ) ) * 4 ) != $len ) {
                die "Secret key length must be equal " . $len / 8 . " bytes for selected algoritm";
            }

            $alg = $config->{alg};
            $need_enc = 1;
        } elsif ( $config->{alg} =~ /^PBES2-HS(256|384|512)\+A(128|192|256)KW$/ ) {
            my $hs = $1;
            my $a = $2;

            if ( ( ( $a * 2 ) - $hs ) != 0 ) { 
                die "Incompatible A and HS values";
            }

            $alg = $config->{alg};
            $need_enc = 1;
        } elsif ( $config->{alg} =~ /^RSA((-OAEP(-265)?)|1_5)$/ ) {
            $alg = $config->{alg};
            $need_enc = 1;
            $need_key = 1;
        } elsif ( $config->{alg} =~ /^ECDH-ES(\+A(128|192|256)KW)?$/ ) {
            $alg = $config->{alg};
            $need_enc = 1;
            $need_key = 2;
        } else {
            die "Unknown algoritm";
        }

        if ( $need_enc ) {
            unless ( exists $config->{enc} && defined $config->{enc} ) {
                die "JWE cannot be used with empty encryption method";
            }

            if ( $config->{enc} =~ /^A(128|192|256)GCM$/ ) {
                $enc = $config->{enc};
            } elsif ( $config->{enc} =~ /^A(128|192|256)CBC-HS(256|384|512)$/ ) {
                my $a = $1;
                my $hs = $2;

            if ( ( ( $a * 2 ) - $hs ) != 0 ) { 
                    die "Incompatible A and HS values";
                }

                $enc = $config->{enc};
            }
        }

        if ( defined $need_key ) {
            if ( $need_key eq 1 ) {
                # TODO: add code to handle RSA keys or parse JWK hash string:
                ##instance of Crypt::PK::RSA
                #my $data = decode_jwt(token=>$t, key=>Crypt::PK::RSA->new('keyfile.pem'));
                #
                ##instance of Crypt::X509 (public key only)
                #my $data = decode_jwt(token=>$t, key=>Crypt::X509->new(cert=>$cert));
                #
                ##instance of Crypt::OpenSSL::X509 (public key only)
                #my $data = decode_jwt(token=>$t, key=>Crypt::OpenSSL::X509->new_from_file('cert.pem'));
            } elsif ( $need_key eq 2 ) {
                # TODO: add code to handle ECC keys or parse JWK hash string:
                #instance of Crypt::PK::ECC
                #my $data = decode_jwt(token=>$t, key=>Crypt::PK::ECC->new('keyfile.pem'));
            }
        }
    }

    if ( exists $config->{need_iat} && defined $config->{need_iat} ) {
        $need_iat = $config->{need_iat};
    }

    if ( exists $config->{need_nbf} && defined $config->{need_nbf} ) { 
        $need_nbf = $config->{need_nbf};
    }

    if ( exists $config->{need_exp} && defined $config->{need_exp} ) { 
        $need_exp = $config->{need_exp};
    }

    if ( exists $config->{need_leeway} && defined $config->{need_leeway} ) { 
        $need_leeway = $config->{need_leeway};
    }

    $dsl->app->add_hook(
        Dancer2::Core::Hook->new(
            name => 'before_template_render',
            code => sub {
                my $tokens = shift;
                $tokens->{jwt} = $dsl->app->request->var('jwt');
            }
        )
    );

    $dsl->app->add_hook(
        Dancer2::Core::Hook->new(
            name => 'after',
            code => sub {
                my $response = shift;
                unless($dont_send_access_control_expose_headers) {
                    $response->push_header('Access-Control-Expose-Headers' => $response_token_header_name);
                }
            }
        )
    );
    
    $dsl->app->add_hook(
        Dancer2::Core::Hook->new(
            name => 'before',
            code => sub {
                my $app = shift;
                my $encoded = $app->request->header($request_auth_header);
                if($encoded) {
                    if($request_auth_token) {
                        if($encoded =~ /^$request_auth_token (.*)$/) {
                            $encoded = $1;
                        } else {
                            $encoded = undef;
                        }
                    }
                }

                unless($dont_recognize_token_in_cookie) {
                    if ($app->request->cookies->{$cookie_name}) {
                        $encoded = $app->request->cookies->{$cookie_name}->value;
                    }
                }
                unless($dont_recognize_token_in_query_string) {
                    if ($app->request->param($query_string_token_param_name)) {
                        $encoded = $app->request->param($query_string_token_param_name);
                    }
                }

                if ($encoded) {
                    my $decoded;
                    eval {
                        $decoded = decode_jwt(
                           token        => $encoded, 
                           key          => $secret, 
                           verify_iat   => $need_iat,
                           verify_nbf   => $need_nbf,
                           verify_exp   => defined $need_exp ? 1 : 0 ,
                           leeway       => $need_leeway, 
                           accepted_alg => $alg, 
                           accepted_enc => $enc
                        );
                    };
                    if ($@) {
                        $app->request->var('jwt_status' => 'missing');
                        $app->execute_hook('plugin.jwt.jwt_exception' => ($a = $@));
                        return;
                    };
                    $app->request->var('jwt', $decoded);
                    $app->request->var('jwt_status' => 'present');
                }
                else {
                    ## no token
                    $app->request->var('jwt_status' => 'missing');
                }
            }
        )
    );

    $dsl->app->add_hook(
        Dancer2::Core::Hook->new(
            name => 'after',
            code => sub {
                my $response = shift;
                my $decoded = $dsl->app->request->var('jwt');
                if (defined($decoded)) {
                    my %params = (
                        payload      => $decoded, 
                        key          => $secret, 
                        alg          => $alg,
                        enc          => $enc,
                        auto_iat     => $need_iat,
                        relative_exp => $need_exp,
                        relative_nbf => $need_nbf
                    );
                    $params{zip} = 'deflate' if $zip_payload;
                    my $encoded = encode_jwt(%params);

                    unless ($dont_send_response_auth_header) {
                        $response->header($response_token_header_name => $encoded);
                    }

                    unless ($dont_recognize_token_in_cookie) {
                        my %cookie = (
                            value => $encoded,
                            name => $cookie_name,
                            expires => $cookie_expires,
                            path => $cookie_path,
                            http_only => $cookie_http_only
                        );
                        $cookie{domain} = $cookie_domain if defined $cookie_domain;
                        $response->push_header('Set-Cookie' => Dancer2::Core::Cookie->new(%cookie)->to_header());
                    }

                    unless ($dont_recognize_token_in_query_string) {
                        if ($response->status =~ /^3/) {
                            my $u = URI->new( $response->header("Location") );
                            $u->query_param($query_string_token_param_name => $encoded);
                            $response->header(Location => $u);
                        }
                    }
                }
            }
        )
    );
};



register_plugin;

1;

# FIXME: documentare
#plugins:
#  JWT:
#    cookie_name: _jwt
#    cookie_expires: 4 weeks
#    cookie_path: /
#    cookie_http_only: 0
#    dont_send_response_auth_header: undef
#    dont_send_access_control_expose_headers: undef
#    request_auth_header: Authorization
#    request_auth_token: undef
#    dont_recognize_token_in_query_string: undef
#    query_string_token_param_name: _jwt
#    dont_recognize_token_in_cookie: undef
#    response_token_header_name: Authorization
#    zip_payload: undef

=encoding UTF-8

=head1 NAME

Dancer2::Plugin::JWT - JSON Web Token made simple for Dancer2

=head1 SYNOPSIS

     use Dancer2;
     use Dancer2::Plugin::JWT;

     post '/login' => sub {
         if (is_valid(param("username"), param("password"))) {
            jwt { username => param("username") };
            template 'index';
         }
         else {
             redirect '/';
         }
     };

     get '/private' => sub {
         my $data = jwt;
         redirect '/ unless exists $data->{username};

         ...
     };

     hook 'plugin.jwt.jwt_exception' => sub {
         my $error = shift;
         # do something
     };

=head1 DESCRIPTION

Registers the C<jwt> keyword that can be used to set or retrieve the payload
of a JSON Web Token.

To this to work it is required to have a secret defined in your config.yml file:

   plugins:
      JWT:
          secret: "string or path to private RSA\EC key"
          # default, or others supported by Crypt::JWT
          alg: HS256 
          # required only for JWE 
          enc: 
          # add issued at field
          need_iat: 1 
          # check not before field
          need_nbf: 1 
          # in seconds
          need_exp: 600 
          # timeshift for expiration
          need_leeway: 30 
          # JWT cookie domain, in case you need to override it
          cookie_domain: my_domain.com

B<NOTE:> A empty call (without arguments) to jwt will trigger the
exception hook if there is no jwt defined.

=head1 BUGS

I am sure a lot. Please use GitHub issue tracker 
L<here|https://github.com/ambs/Dancer2-Plugin-JWT/>.

=head1 ACKNOWLEDGEMENTS

To Lee Johnson for his talk "JWT JWT JWT" in YAPC::EU::2015.

To Nuno Carvalho for brainstorming and help with testing.

To user2014, thanks for making the module use Crypt::JWT.

=head1 COPYRIGHT AND LICENSE

Copyright 2015-2018 Alberto Simões, all rights reserved.

This module is free software and is published under the same terms as Perl itself.

=head1 AUTHOR

Alberto Simões C<< <<EMAIL>> >>

=cut
