package Dancer2::Serializer::RAW;

$Dancer2::Serializer::RAW::VERSION = '0.206000';
use Moo;
use Scalar::Util 'blessed';

with 'Dancer2::Core::Role::Serializer';

has '+content_type' => ( default => sub { '' } );

# class definition
sub serialize {
    my ( $self, $entity, $options ) = @_;

    return $entity;
}

sub deserialize {
    my ( $self, $entity, $options ) = @_;

    return { BODY => $entity };
}

1;

__END__

=pod

=encoding UTF-8

=head1 NAME

Dancer2::Serializer::RAW - Serializer raw

=head1 VERSION

version 0.206000

=head1 DESCRIPTION

This is the serializer raw.

=head1 ATTRIBUTES

=head2 content_type

Returns 'application/json'

=head1 METHODS

=head2 serialize($content)

Serializes a Perl data structure into a JSON string.

=head2 deserialize($content)

Deserializes a JSON string into a Perl data structure. If content to deserialize
is not a valid JSON string, return undef: routes waiting for application/json content
must check if params('body') is an hash, else input is not a valid JSON; routes waiting
for other content type can go on.

=head1 AUTHOR

SHTeam

=head1 COPYRIGHT AND LICENSE

This software is copyright (c) 2018 by SIRTI.

This is free software; you can redistribute it and/or modify it under
the same terms as the Perl 5 programming language system itself.

=head1 CREDITS

This module is based on L<Dancer2::Serializer::JSON|https://metacpan.org/pod/Dancer2::Serializer::JSON>.

=cut
