use 5.008001;
use strict;
use warnings;

package Dancer2::Session::MongoDB;
# ABSTRACT: Dancer 2 session storage with MongoDB
our $VERSION = '0.003-SIRTI'; # VERSION

use Moo;
use Carp;
use MongoDB;
#use MongoDB::MongoClient;
use MongoDB::OID;
use Dancer2::Core::Types;

use DateTime;

# db.dancer_sessions.createIndex( { "lastModifiedDate": 1 }, { expireAfterSeconds: 3600 } )
# db.dancer_sessions.createIndex( { "creationDate": 1 }, { expireAfterSeconds: 14400 } )

#--------------------------------------------------------------------------#
# Public attributes
#--------------------------------------------------------------------------#


has database_name => (
    is       => 'ro',
    isa      => Str,
    required => 1,
);


has collection_name => (
    is      => 'ro',
    isa     => Str,
    default => sub { "dancer_sessions" },
);


has client_options => (
    is      => 'ro',
    isa     => HashRef,
    default => sub { {} },
);


has serialization_engine => (
    is      => 'ro',
    isa     => Str,
    default => sub { "none" },
    trigger	=> sub {
    	my ($self, $engine) = @_;
    	croak "serialization_engine must be one of none, Storable, YAML, YAML::XS"
    		unless $engine =~ /^(none|Storable|YAML|YAML::XS)$/;
    	if( $engine eq "Storable" ) {
    		eval 'use Storable qw(freeze thaw); $Storable::Deparse = 1;';
    		croak $@
    			if $@;
		} elsif ( $engine eq "YAML" ) {
			eval 'use YAML;';
    		croak $@
    			if $@;
		} elsif ( $engine eq "YAML::XS" ) {
			eval 'use YAML::XS;';
    		croak $@
    			if $@;
		}
    }
);

#--------------------------------------------------------------------------#
# Private attributes
#--------------------------------------------------------------------------#

has _client => (
    is  => 'lazy',
    isa => InstanceOf ['MongoDB::MongoClient'],
);

sub _build__client {
    my ($self) = @_;
    return MongoDB::MongoClient->new( $self->client_options );
}

has _collection => (
    is  => 'lazy',
    isa => InstanceOf ['MongoDB::Collection'],
);

sub _build__collection {
    my ($self) = @_;
    my $db = $self->_client->get_database( $self->database_name );
    return $db->get_collection( $self->collection_name );
}

#--------------------------------------------------------------------------#
# Role composition
#--------------------------------------------------------------------------#

with 'Dancer2::Core::Role::SessionFactory';

# When saving/retrieving, we need to add/strip the _id parameter
# because the Dancer2::Core::Session object keeps them as separate
# attributes

sub _retrieve {
    my ( $self, $id ) = @_;
    my $doc = $self->_collection->find_one( { _id => $id } );
   	if( $self->serialization_engine eq "Storable" ) {
   		return thaw($doc->{data});
	} elsif ( $self->serialization_engine eq "YAML" || $self->serialization_engine eq "YAML::XS" ) {
		return Load($doc->{data});
	} else { # $self->serialization_engine eq "none"
		return $doc->{data};
	}
}

sub _flush {
    my ( $self, $id, $data ) = @_;
   	if( $self->serialization_engine eq "Storable" ) {
   		$data = freeze($data);
	} elsif ( $self->serialization_engine eq "YAML" || $self->serialization_engine eq "YAML::XS" ) {
		$data = Dump($data);
	}
    $self->_collection->update(
        { '_id' => $id },
        {
            '$set' => {
                data => $data,
                lastModifiedDate => DateTime->now()
            },
            '$setOnInsert' => {
                creationDate => DateTime->now()
            }
        },
        { upsert => 1, safe => 1 }
    );
}

sub _destroy {
    my ( $self, $id ) = @_;
    $self->_collection->remove( { _id => $id }, { safe => 1 } );
}

sub _sessions {
    my ($self) = @_;
    my $cursor = $self->_collection->query->fields( { _id => 1 } );
    return [ map { $_->{_id} } $cursor->all ];
}

1;


# vim: ts=4 sts=4 sw=4 et:

__END__

=pod

=head1 NAME

Dancer2::Session::MongoDB - Dancer 2 session storage with MongoDB

=head1 VERSION

version 0.003

=head1 SYNOPSIS

  # In Dancer 2 config.yml file

  session: MongoDB
  engines:
    session:
      MongoDB:
        database_name: myapp_db
        client_options:
          host: mongodb://localhost:27017

=head1 DESCRIPTION

This module implements a session factory for Dancer 2 that stores session
state within L<MongoDB>.

=head1 ATTRIBUTES

=head2 database_name (required)

Name of the database to hold the sessions collection.

=head2 collection_name

Collection name for storing session data. Defaults to 'dancer_sessions'.

=head2 client_options

Hash reference of configuration options to pass through to
L<MongoDB::MongoClient> constructor.  See that module for details on
configuring authentication, replication, etc.

=head2 serialization_engine

Serialization engine for storing session data. Can be one of 'none', 'YAML', 'YAML::XS', 'Storable'. Defaults to 'none'.
Note that if you don't use any serializaion engine, you won't be able to store in session perl objects.
For using 'YAML', 'YAML::XS' or 'Storable' engines you need to install CPAN modules.

=head1 SESSION DURATION

You can set max idle time and max session duration using mongodb TTL indexes L<http://docs.mongodb.org/manual/core/index-ttl>

  db.dancer_sessions.createIndex( { "lastModifiedDate": 1 }, { expireAfterSeconds: 3600 } )
  db.dancer_sessions.createIndex( { "creationDate": 1 }, { expireAfterSeconds: 14400 } )

=for Pod::Coverage method_names_here

=for :stopwords cpan testmatrix url annocpan anno bugtracker rt cpants kwalitee diff irc mailto metadata placeholders metacpan

=head1 SUPPORT

=head2 Bugs / Feature Requests

Please report any bugs or feature requests through the issue tracker
at L<https://github.com/dagolden/dancer2-session-mongodb/issues>.
You will be notified automatically of any progress on your issue.

=head2 Source Code

This is open source software.  The code repository is available for
public review and contribution under the terms of the license.

L<https://github.com/dagolden/dancer2-session-mongodb>

  git clone git://github.com/dagolden/dancer2-session-mongodb.git

=head1 AUTHOR

David Golden <<EMAIL>>

=head1 COPYRIGHT AND LICENSE

This software is Copyright (c) 2013 by David Golden.

This is free software, licensed under:

  The Apache License, Version 2.0, January 2004

=cut
