package SIRTI::ART::Events;

use strict;
use warnings;
use File::Basename;
use SIRTI::DB;
use SIRTI::Err;
use SIRTI::ART::Lookup::ART;
use JSON;

use Exporter;
our @ISA = qw(Exporter);
our @EXPORT_OK = qw( resolve_attachment legacy_attach_files);


#
# private
#



sub _save_attachments($$$@) {
	my ($self, $id_attivita, $action_date, @files) = @_;
	my $err=legacy_attach_files($self->{DB},$id_attivita,$action_date,\@files,REPOSITORY => $self->{ART_REPOSITORY});
	return defined ($err) ? 1 : 0;
}


sub _declare_constants($$$) {
	my ($self, $prefix, $sql) = @_;
	my $cur = $self->{DB}->create_cursor($sql) or return;
	while ( my $row = $cur->fetchrow_hashref() ) {
		$row->{KEY} = 'APERTURA' if $prefix eq 'ACTION_' && $row->{VALUE} eq 'A';
		$self->{$prefix.$row->{KEY}} = "$row->{VALUE}";
	}
}
sub _declare_activity_constants($) {
	my $self = shift;
	_declare_constants($self, 'TIPO_ATTIVITA_', '
													select ID_TIPO_ATTIVITA VALUE, NOME_TIPO_ATTIVITA KEY
													from tipi_attivita
	');
}
sub _declare_action_constants($) {
	my $self = shift;
	_declare_constants($self, 'ACTION_', '
											select ID_ACTION VALUE, NOME KEY
											from action
											where MORTO is null
	');
}
sub _declare_status_constants($) {
	my $self = shift;
	_declare_constants($self, 'STATO_', '
											select ID_STATO VALUE, NOME KEY
											from stati
											where MORTO is null
	');
}


sub _get_permission_action($) {
	my $self =shift;
	my $db=$self->{DB};
	# in questo primo pezzo della query vengono estratte le sole azioni che prevedono uno step di loop
	# sugli stati __ANY__STATUS__
	my $sql .= "
		select ID_STATO_INIZIALE
			,ID_ACTION_PERMESSA
			,ID_STATO_FINALE
		From (
			with action as (
			select distinct id_action_permessa
			    , id_tipo_attivita
				, id_stato_finale
				, case when id_stato_iniziale = id_stato_finale then 1 else 0 end LOOP
			from permission_action
			where id_tipo_attivita = $self->{ID_TIPO_ATTIVITA}
			    and id_gruppo_abilitato = $self->{ID_GRUPPO_ABILITATO}
			    and id_stato_iniziale = (select id_stato from stati where flag_stato_partenza = 'Y')
			    --and id_stato_finale = (select id_stato from stati where flag_stato_partenza = 'Y')
			),
			actionpos as (
			select *
			from permission_action
			where id_tipo_attivita = $self->{ID_TIPO_ATTIVITA}
			    and id_gruppo_abilitato = $self->{ID_GRUPPO_ABILITATO}
			    and id_stato_iniziale != (select id_stato from stati where flag_stato_partenza = 'Y')
			    and id_stato_finale != (select id_stato from stati where flag_stato_partenza = 'Y')
			)
			select a.id_tipo_attivita
					    ,  a.id_stato_iniziale
					    ,  a2.id_action_permessa ID_ACTION_PERMESSA
					    ,  case when a2.loop = 1 then a.id_Stato_iniziale else a2.id_stato_finale end ID_STATO_FINALE
					    ,  a.id_gruppo_abilitato
			from actionpos a
			    join action a2 on a.id_tipo_attivita = a2.id_tipo_attivita
		)
	";
	# qui aggiungo le azioni per l'azione virtuale di invalidazione che può essere eseguita anche da uno stato finale
	my $has_invalidate = $db->fetch_minimalized("select id_action from action where flag_action_partenza = 'I'");
	if ($has_invalidate) {
		$sql .= "
			union all
			select ID_STATO_INIZIALE
				,ID_ACTION_PERMESSA
				,ID_STATO_FINALE
			From (
				with actions as (
				select distinct id_action_permessa
					, id_tipo_attivita
					, id_stato_finale
					, case when id_stato_iniziale = id_stato_finale then 1 else 0 end LOOP
				from permission_action
				where id_tipo_attivita = $self->{ID_TIPO_ATTIVITA}
					and id_gruppo_abilitato = $self->{ID_GRUPPO_ABILITATO}
					and id_action_permessa = (select id_action from action where flag_action_partenza = 'I')
				),
				actionpos as (
				select *
				from permission_action
				where id_tipo_attivita = $self->{ID_TIPO_ATTIVITA}
					and id_gruppo_abilitato = $self->{ID_GRUPPO_ABILITATO}
					and id_stato_iniziale != (select id_stato from stati where flag_stato_partenza = 'Y')
					and id_stato_finale != (select id_stato from stati where flag_stato_partenza = 'Y')
				union
				select id_tipo_attivita,
					id_Stato_finale id_stato_iniziale,
					id_Action_permessa,
					id_Stato_finale,
					id_gruppo_Abilitato
				from permission_action
				where id_tipo_attivita = $self->{ID_TIPO_ATTIVITA}
					and id_gruppo_abilitato = $self->{ID_GRUPPO_ABILITATO}
					and id_stato_finale in (select id_stato from stati where flag_stato_partenza in ('C','Q'))
				)
				select distinct a.id_tipo_attivita
					,  a.id_stato_iniziale
					,  a2.id_action_permessa ID_ACTION_PERMESSA
					,  case when a2.loop = 1 then a.id_Stato_iniziale else a2.id_stato_finale end ID_STATO_FINALE
					,  a.id_gruppo_abilitato
				from actionpos a
					join actions a2 on a.id_tipo_attivita = a2.id_tipo_attivita
			)
		";
	}

	# qui aggiungo le azioni per l'azione virtuale di aggiunta documentazione che può essere eseguita anche da uno stato finale
	my $has_add_documentation = $db->fetch_minimalized("select id_action from action where flag_action_partenza = 'D'");
	if ($has_add_documentation) {
		$sql .= "
			union all
			select ID_STATO_INIZIALE
				,ID_ACTION_PERMESSA
				,ID_STATO_FINALE
			From (
				with actions as (
				select distinct id_action_permessa
					, id_tipo_attivita
					, id_stato_finale
					, case when id_stato_iniziale = id_stato_finale then 1 else 0 end LOOP
				from permission_action
				where id_tipo_attivita = $self->{ID_TIPO_ATTIVITA}
					and id_gruppo_abilitato = $self->{ID_GRUPPO_ABILITATO}
					and id_action_permessa = (select id_action from action where flag_action_partenza = 'D')
				),
				actionpos as (
				select *
				from permission_action
				where id_tipo_attivita = $self->{ID_TIPO_ATTIVITA}
					and id_gruppo_abilitato = $self->{ID_GRUPPO_ABILITATO}
					and id_stato_iniziale != (select id_stato from stati where flag_stato_partenza = 'Y')
					and id_stato_finale != (select id_stato from stati where flag_stato_partenza = 'Y')
				union
				select id_tipo_attivita,
					id_Stato_finale id_stato_iniziale,
					id_Action_permessa,
					id_Stato_finale,
					id_gruppo_Abilitato
				from permission_action
				where id_tipo_attivita = $self->{ID_TIPO_ATTIVITA}
					and id_gruppo_abilitato = $self->{ID_GRUPPO_ABILITATO}
					and id_stato_finale in (select id_stato from stati where flag_stato_partenza in ('C','Q'))
				)
				select distinct a.id_tipo_attivita
					,  a.id_stato_iniziale
					,  a2.id_action_permessa ID_ACTION_PERMESSA
					,  case when a2.loop = 1 then a.id_Stato_iniziale else a2.id_stato_finale end ID_STATO_FINALE
					,  a.id_gruppo_abilitato
				from actionpos a
					join actions a2 on a.id_tipo_attivita = a2.id_tipo_attivita
			)
		";
	}

	# qui aggiungo le azioni esplicitamente definite
	$sql .= "
		union all
		select
			ID_STATO_INIZIALE
			,ID_ACTION_PERMESSA
			,ID_STATO_FINALE
		from
			permission_action
		where
			ID_TIPO_ATTIVITA = $self->{ID_TIPO_ATTIVITA}
			and ID_GRUPPO_ABILITATO = $self->{ID_GRUPPO_ABILITATO}
	";
	
	
	my $cur = $db->create_cursor($sql) || SIRTI::Err::db_fatal_error($db);
	my $bs={};  # stati iniziali data una azione
	my $es={};  # stato finale data una azione ed uno stato iniziale
	while(1) {
		my $row = $cur->fetchrow_hashref();
		last if (!defined($row));
		$bs->{$row->{ID_ACTION_PERMESSA}}=[] if (!defined($bs->{$row->{ID_ACTION_PERMESSA}}));
		push @{$bs->{$row->{ID_ACTION_PERMESSA}}},$row->{ID_STATO_INIZIALE};
		$es->{$row->{ID_ACTION_PERMESSA}}->{$row->{ID_STATO_INIZIALE}}=$row->{ID_STATO_FINALE};
	}
	SIRTI::Err::db_fatal_error($db) if (SIRTI::DB::iserror());
	return {
		BS => $bs
		,ES => $es
	}
}


sub _get_tdta_action($) {
	my $self =shift;
	my $sql = "
		select
			td.id_action
			,td.id_tipo_dato_tecnico_attivita  id_tdta
			,td.obbligatorio
			,tdta.valore_default				valore_default
			,td.solo_lettura SOLO_LETTURA
			,td.nascosto NASCOSTO
			,td.prepopola PREPOPOLA
		from
			tipi_dati_tecnici_att_action td
			,tipi_dati_tecnici_attivita tdta
		where
			td.id_tipo_attivita = $self->{ID_TIPO_ATTIVITA}
			and td.id_tipo_dato_tecnico_attivita = tdta.id_tipo_dato_tecnico_attivita
	";
	my $db=$self->{DB};
	my $cur = $db->create_cursor($sql)|| SIRTI::Err::db_fatal_error($db);
	my $h={};
	while(1) {
		my $row = $cur->fetchrow_hashref();
		last if (!defined($row));
		$h->{$row->{ID_ACTION}}->{$row->{ID_TDTA}}={
														OBBLIGATORIO 		=> $row->{OBBLIGATORIO}
														,VALORE_DEFAULT		=> $row->{VALORE_DEFAULT}
														,SOLO_LETTURA		=> $row->{SOLO_LETTURA}
														,NASCOSTO			=> $row->{NASCOSTO}
														,PREPOPOLA			=> $row->{PREPOPOLA}
												   }
	}
	SIRTI::Err::db_fatal_error($db) if (SIRTI::DB::iserror());
	return $h;
}

sub _refresh_dta_tags {
	my $self = shift;
	my %params = @_;
	
	my $db = $self->get_db();
	
	my $tags = from_json((!defined $params{TDTA_VALUE} || $params{TDTA_VALUE} eq '') ? '[]' : $params{TDTA_VALUE});
	my $id_attivita = $params{ID_ATTIVITA};
	my $id_tdta = $params{TDTA_ID};
	my $last_var_date = $params{LAST_VAR_DATE};
	my $date_format = $params{DATE_FORMAT};	
	
	my $sql_add = '';
	my $sql_del_not_in = '';

	if (scalar @{$tags}){
		$sql_del_not_in = "and dtat.tag not in (
			".( join ',',  map { $db->quote($_)} @{$tags} )."
	    )";
	    
	    $sql_add = join '' , map { "SELECT null ID, ".$id_tdta." ID_TIPO_DATO_TECNICO_ATTIVITA, ".$id_attivita." ID_ATTIVITA, ".$db->quote($_)." tag, 'A' AZIONE FROM dual where ".$db->quote($_)." not in (select tag from dati_Tecnici_attivita_tags where id_attivita = ".$id_attivita." and id_tipo_Dato_Tecnico_attivita = ".$id_tdta." and disabled is null) union
	    "}  @{$tags} ;
	}
	
	my $sql = "
		MERGE INTO dati_tecnici_Attivita_tags e
		USING (
	      ".$sql_add."
	      select dtat.id, dtat.id_tipo_dato_Tecnico_Attivita, dtat.id_attivita, dtat.tag, 'D' AZIONE from dati_tecnici_Attivita_tags dtat
	      where dtat.id_tipo_dato_tecnico_attivita = ".$id_tdta."
	      and dtat.disabled is null
	      and dtat.id_Attivita = ".$id_attivita."
	      ".$sql_del_not_in."
	    ) h
	    ON (e.id_attivita = h.id_attivita and e.id_tipo_dato_tecnico_attivita = h.id_tipo_dato_tecnico_Attivita and e.tag = h.tag and h.azione = 'D')
	  WHEN MATCHED THEN
	   UPDATE SET e.disabled = to_date(".$db->quote($last_var_date).",".$db->quote($date_format).")
	   	where e.id = h.id
	  WHEN NOT MATCHED THEN
	    INSERT (id, id_tipo_dato_Tecnico_Attivita, id_attivita, tag)
	    VALUES (seq_dta_tags.nextval, h.id_tipo_dato_Tecnico_attivita, h.id_attivita, h.tag)
	";
	
	$db->do($sql) || return   SIRTI::DB::get_errormessage();

	return 1;
}	
	
sub _update_dta {


	my $self = shift;

	my %param=@_;
	my $db = $self->get_db();
	
	my $id_operatore = $param{"id_operatore"};
	my $id_attivita = $param{"id_attivita"};
	my $data_esecuzione  = $param{"data_esecuzione"};
	my $record = $param{"record"};

	# Caricamento DATI_TECNICI_ATTIVITA pre modifica
							
	my $result = $db->fetchall("
		select
			 d.ID_TIPO_DATO_TECNICO_ATTIVITA	id
			 ,d.descrizione 					valore
		from
			DATI_TECNICI_ATTIVITA d
		where
			d.id_attivita = $id_attivita
		order by
			d.id_attivita
			,d.data_esecuzione
			,d.ID_TIPO_DATO_TECNICO_ATTIVITA
			
		") || SIRTI::DB::get_errormessage();

	my %old_values=();
	for my $z (@$result) {
		$old_values{$z->[0]} = $z->[1];
	}
	
	my $lua = $self->{LOOKUP_ART};
	
	my @data_inserted=();
	for my $j(keys %$record) {   # $j contiene id_tipo_dato_tecnico_attivita
#		if (!defined($old_values{$j}) || !defined($record->{$j}) || $record->{$j} ne $old_values{$j}) {
#		if (defined($record->{$j}) && SIRTI::Err::nvl($old_values{$j}) ne $record->{$j}) {
		if ( $self->{SAVE_ALL_DTA} || SIRTI::Err::nvl($old_values{$j}) ne SIRTI::Err::nvl($record->{$j})) {
#			print STDERR "update_dta: Attivita $id_attivita Dato tecnico $j val '$record->{$j}' oldval '$old_values{$j}'\n";
			my $tipo_ui = $lua->tipo_ui_tdta_id($j);
			if (defined $record->{$j}){
				# # lo formatto correttamente
				if ($tipo_ui eq 'CURRENCY'){
					$record->{$j} = sprintf("%.".$lua->decimali_tdta_id($j)."f", $record->{$j});
				}
			}
			my $prepare = $db->create_prepare("
				insert into DATI_TECNICI_ATTIVITA(
						ID_TIPO_DATO_TECNICO_ATTIVITA,
						ID_ATTIVITA,
						DATA_ESECUZIONE,
						DESCRIZIONE,
						MORTO,
						OPER_ULTIMA_VAR,
						DATA_ULTIMA_VAR,
						ID_TRANSIZIONE
				)
				values (
						?
						,?
						,to_date(?,'YYYYMMDDHH24MISS')
						,?
						,NULL
						,?
						,sysdate
						 ,(select sa.id_transizione from storia_attivita sa where sa.id_attivita = ? and sa.data_esecuzione = to_date(?,'YYYYMMDDHH24MISS')) --ID_TRANSIZIONE
				)
			");

			my @bind_params = (
				$j,
				$id_attivita,
				$data_esecuzione,
				$record->{$j},
				$id_operatore,
				$id_attivita,
				$data_esecuzione
			);

			$prepare->do(@bind_params) || return SIRTI::DB::get_errormessage();
			
			# aggiorno i tags
			if ($tipo_ui eq 'TAGS'){
				$self->_refresh_dta_tags(ID_ATTIVITA => $id_attivita, TDTA_ID => $j, TDTA_VALUE => $record->{$j}, LAST_VAR_DATE => $data_esecuzione, DATE_FORMAT => 'YYYYMMDDHH24MISS' );
			}
			
			push @data_inserted,$j;
		}  # if
	}	# for keys record
	$self->{DATA_INSERTED}=\@data_inserted;
	return "";
} # _update_dta

sub _check_tipo_sistema($$) {
	my ($self,$id_sistema)=@_;
	SIRTI::Err::prec(defined($id_sistema),'parametro id_sistema non dichiarato');
	my $db=$self->get_db();
	my $row=$db->fetchall("select id_tipo_sistema from sistemi where id_sistema=$id_sistema") || SIRTI::Err::db_fatal_error($db);
	SIRTI::Err::prec(defined($row->[0]->[0]),"$id_sistema: id sistema non esistente in tabella sistemi");
	SIRTI::Err::prec($row->[0]->[0] == $self->{ID_TIPO_SISTEMA},"$id_sistema: sistema incongruente con il suo tipo (".$row->[0]->[0]."/".$self->{ID_TIPO_SISTEMA}.")");
}

sub _check_tipo_attivita($$) {
	my ($self,$id_attivita)=@_;
	SIRTI::Err::prec(defined($id_attivita),'parametro id_attivita non dichiarato');
	my $db=$self->get_db();
	my $row=$db->fetchall("select id_tipo_attivita from attivita where id_attivita=$id_attivita") || SIRTI::Err::db_fatal_error($db);
	SIRTI::Err::prec(defined($row->[0]->[0]),"$id_attivita: id attivita non esistente in tabella attivita");
	SIRTI::Err::prec($row->[0]->[0] == $self->{ID_TIPO_ATTIVITA},"$id_attivita: attivita incongruente con il suo tipo (".$row->[0]->[0]."/".$self->{ID_TIPO_ATTIVITA}.")");
	return 1; #### Evita che fallisca eval()
}


sub _get_action_name($$) {
	my ($self,$id)=@_;
	return "<nodef>" if !defined($id);
	return $id unless defined($self->{LOOKUP_ART});
	return $id unless $self->{LOOKUP_ART}->is_id_azione($id);
	return $self->{LOOKUP_ART}->nome_azione($id);
}

sub _get_tdta_name($$) {
	my ($self,$id)=@_;
	return "<nodef>" if !defined($id);
	return $id unless defined($self->{LOOKUP_ART});
	return $id unless $self->{LOOKUP_ART}->is_id_tdta($id);
	return $self->{LOOKUP_ART}->nome_tdta($id);
}


sub _resolve_attachment { #statico
	my ($dir,$name)=@_;
	$dir.='/' unless $dir=~/\/$/;
	my @dirs=$name=~/^\d+-(\d{4})(\d{2})(\d{2})/;
	return undef if scalar(@dirs) == 0;
	my $fullpath=$dir.join('/',@dirs).'/'.$name;

	return $fullpath if -r $fullpath;
	$fullpath=~s/\.gz$/.bz2/;
	return $fullpath if -r $fullpath;
	$fullpath=~s/\.bz2$/.gz/;
	return $fullpath if -r $fullpath;

	$fullpath=$dir.$name;
	return $fullpath if -r $fullpath;
	$fullpath=~s/\.gz$/.bz2/;
	return $fullpath if -r $fullpath;
	$fullpath=~s/\.bz2$/.gz/;
	return $fullpath if -r $fullpath;
	
	return undef;
}


sub _check_attachment {
	my ($fullpath,$taropt)=@_;
	local $?;
	$?=99; #imposta l' errore nel caso in cui venga dimenticata una estensione
	system("gzip -t '$fullpath'") if $taropt eq 'z';
	system("bzip2 -t '$fullpath'") if $taropt eq 'j';
	return $?;
}


sub _get_tar_opt {
	my $file=shift;
	my ($ext)=$file=~/\.([^.]+)$/;
	return undef unless defined $ext;
	return $ext eq 'gz'  ? 'z'
		:   $ext eq 'bz2' ? 'j'
		:   undef;
}


#
# public
#

sub new {
	my $this = shift;
	my $class = ref($this) || $this;
	
#		DB 						- istanza di SIRTI::DB
#		ID_TIPO_SISTEMA
#		ID_TIPO_ATTIVITA
#		ID_TIPO_SOTTOATTIVITA
#		ID_GRUPPO_ABILITATO
#		LOOKUP_ART				- istanza di DBU::LOOKUP_ART (opzionale)
#		SAVE_ALL_DTA			- 1 || [0] -> memorizza tutti i DTA anche se il valore non cambia
	my %self=@_;
	SIRTI::Err::prec(!defined($self{ID_TIPO_SOTTOATTIVITA}),SIRTI::Err::nvl($self{ID_TIPO_SOTTOATTIVITA})." il tipo sottoattivita non e\' gestito");
	SIRTI::Err::prec(defined($self{DB}),'parametro DB non dichiarato');
	SIRTI::Err::prec(defined($self{ID_TIPO_SISTEMA}),'parametro ID_TIPO_SISTEMA non dichiarato');
	SIRTI::Err::prec(defined($self{ID_TIPO_ATTIVITA}),'parametro ID_TIPO_ATTIVITA non dichiarato');
	SIRTI::Err::prec(defined($self{ID_GRUPPO_ABILITATO}),'parametro ID_GRUPPO_ABILITATO non dichiarato');
	
	$self{LOOKUP_ART} = new SIRTI::ART::Lookup::ART($self{DB}) if !defined($self{LOOKUP_ART});
	$self{SAVE_ALL_DTA} = 0 unless defined $self{SAVE_ALL_DTA};
	SIRTI::Err::prec($self{SAVE_ALL_DTA} == 1 || $self{SAVE_ALL_DTA} == 0, 'parametro SAVE_ALL_DTA non valido');

	###### test congruenza 	ID_TIPO_SISTEMA ID_TIPO_ATTIVITA ID_TIPO_SOTTOATTIVITA
	my $sql="select count(*)
				from
					attivita_per_tipi_sistema
				where
					ID_TIPO_ATTIVITA=$self{ID_TIPO_ATTIVITA}
					and ID_TIPO_SOTTOATTIVITA".(defined($self{ID_TIPO_SOTTOATTIVITA}) ? " = ".$self{DB}->quote($self{ID_TIPO_SOTTOATTIVITA}) : " is null")."
					and ID_TIPO_SISTEMA=$self{ID_TIPO_SISTEMA}
	";
	my $r = $self{DB}->fetchall($sql) || SIRTI::Err::db_fatal_error($self{DB});
	SIRTI::Err::prec($r->[0]->[0] > 0,$self{ID_TIPO_SISTEMA}.'/'.$self{ID_TIPO_ATTIVITA}.'/'.$self{DB}->quote($self{ID_TIPO_SOTTOATTIVITA}).": ID_TIPO_SISTEMA/ID_TIPO_ATTIVITA/ID_TIPO_SOTTOATTIVITA non congruenti\n$sql");
	###
	
	_declare_action_constants(\%self);
	_declare_status_constants(\%self);
	$self{PERMISSION_ACTION} = _get_permission_action(\%self);
#	return undef if (!defined($self{PERMISSION_ACTION}));
	SIRTI::Err::check(defined($self{PERMISSION_ACTION}),'$self{PERMISSION_ACTION} non definito');
	$self{TDTA_ACTION_WITH_HASH}=_get_tdta_action(\%self);

	SIRTI::Err::check(defined($self{TDTA_ACTION_WITH_HASH}),'$self{TDTA_ACTION_WITH_HASH} non definito');
	$self{TDTA_ACTION}=sub {
		my $hi=shift;
		my %ho=();
		for my $action(keys %$hi) {
			for my $id(keys %{$hi->{$action}}) {
				$ho{$action}->{$id}=$hi->{$action}->{$id}->{OBBLIGATORIO};
			}
		}
		return \%ho;
	}->($self{TDTA_ACTION_WITH_HASH});
#	return undef if (!defined($self{TDTA_ACTION}));
	SIRTI::Err::check(defined($self{TDTA_ACTION}),'$self{TDTA_ACTION} non definito');

	$self{ART_REPOSITORY}=$ENV{ART_REPOSITORY};
	unless (defined($self{ART_REPOSITORY})) {
	##### Path del repository di ART
		my $sql =	"
						select VALORE ART_REPOSITORY
						from CONFIG_ART
						where CHIAVE = 'ART.REPOSITORY'
					";
		my $r = $self{DB}->fetchall($sql) || SIRTI::Err::db_fatal_error($self{DB});
		$self{ART_REPOSITORY} = $r->[0]->[0] || undef;
		SIRTI::Err::prec(defined($self{ART_REPOSITORY}), "non trovato VALORE REPOSITORY in tab. CONFIG_ART per chiave 'ART.REPOSITORY'");
		$self{ART_REPOSITORY} .= '/' unless $self{ART_REPOSITORY} =~ /\/$/;
	}
	SIRTI::Err::prec(length($self{ART_REPOSITORY}) > 0,"ART_REPOSITORY ha lunghezza 0");
	SIRTI::Err::prec(-d $self{ART_REPOSITORY},"$self{ART_REPOSITORY}: non e' una directory");
	SIRTI::Err::prec(-w $self{ART_REPOSITORY},"$self{ART_REPOSITORY}: non e' scrivibile");
	###

    ##### Creazione hash $self{TDTA_NAME} del tipo DESCRIZIONE => ID_TDTA per i tdta associati all'attivita specificata
	$sql =	"
		select	tdta.DESCRIZIONE
			   ,tdta.ID_TIPO_DATO_TECNICO_ATTIVITA
		from	TIPI_DATI_TECNICI_ATTIVITA tdta
			   ,TIPI_DATI_TECNICI_ATT_ACTION tdtaa
		where  tdta.ID_TIPO_DATO_TECNICO_ATTIVITA =	tdtaa.ID_TIPO_DATO_TECNICO_ATTIVITA
			   AND tdtaa.ID_TIPO_ATTIVITA =	$self{ID_TIPO_ATTIVITA}
	";
	$r = $self{DB}->fetchall($sql) || SIRTI::Err::db_fatal_error($self{DB});
	foreach	my $tdta (@{$r}) {
		$self{TDTA_NAME}->{$tdta->[0]} =	$tdta->[1];
	}
	
	return bless \%self, $class;
}


sub get_db($) {
	my $self = shift;
	return $self->{DB};
}

sub get_id_tipo_sistema($) {
	my $self = shift;
	return $self->{ID_TIPO_SISTEMA};
}

sub get_id_tipo_attivita($) {
	my $self = shift;
	return $self->{ID_TIPO_ATTIVITA};
}

sub get_id_tipo_sottoattivita($) {
	my $self = shift;
	return $self->{ID_TIPO_SOTTOATTIVITA};
}

sub get_id_gruppo_abilitato($) {
	my $self = shift;
	return $self->{ID_GRUPPO_ABILITATO};
}

sub get_dta_inserted($) { return $_[0]->{DATA_INSERTED};}


#   NO_MAKE_OUTPUT_SUBDIR	- non viene creata la sottodirectory di memorizzazione per gli allegati
#   MAKE_OUTPUT_SUBDIR_PERMS - permessi per la creazione delle sottodirectory degli allegati  - default nessuno
#	TMPDIR	- directory per i files temporanei - se non specificata viene preso dalla tabella di configurazione globale oppure (come caso ultimo) /tmp

sub create_new_activity {   # se ok ritorna l' id della attivita creata
							# altrimenti ritorna un messaggio di errore
	my $self = shift;

	my %param=@_;
	
	my $db =$self->get_db();
	my $dbh=$db->get_dbh();
	
	SIRTI::Err::prec(!defined($param{"id_sottosistema"}),SIRTI::Err::nvl($param{"id_sottosistema"}).": i sottosistemi non sono implementati");
	my $id_operatore			= $param{"id_operatore"};
	my $id_operatore_corrente	= $param{"id_operatore_corrente"};
	my $id_sistema				= $param{"id_sistema"};
	my $id_sottosistema			= $param{"id_sottosistema"};
	my $id_tipo_attivita		= $self->{ID_TIPO_ATTIVITA};
	my $id_tipo_sottoattivita	= $self->{ID_TIPO_SOTTOATTIVITA};
	my $attivita_descrizione	= $param{"attivita_descrizione"};
	my $data_esecuzione			= $param{"data_esecuzione"};
	my $id_msg					= $param{"id_msg"};
	my $record					= $param{"record"};
	my $id_attivita_custom		= $param{"id_attivita_custom"};
	my @allegati				= ( defined($param{"allegati"}) ? @{$param{"allegati"}} : () );
	my $art						= $param{"art"};
	
	##### test se id_sistema e' congruente con id_tipo_sistema
	$self->_check_tipo_sistema($id_sistema);
	###
	
	my $err = $self->check_tdta($self->{ACTION_APERTURA},$record,$art);
	SIRTI::Err::prec($err eq '', "$err\nTIPI DATI TECNICI non verificati per l'azione ".$self->_get_action_name($self->{ACTION_APERTURA})." del tipo attivita ".($db->fetch_minimalized("select nome_tipo_Attivita from tipi_attivita where id_tipo_attivita = ".$id_tipo_attivita)));

	# utilizza nuova implementazione SOLO se
	# si richiede la forzatura dell'id_attivita'
	my $prep;
	unless (defined $id_attivita_custom){
	
		# id_attivita_custom non richiesta
		# l'id attivita' viene estratta tramite sequence
		$prep = "
		BEGIN
			:STATUS :=  TRAB.CREA_NUOVA_ATTIVITA(
				:V_ID_SISTEMA
				,:V_ID_SOTTOSISTEMA
				,:V_ID_TIPO_ATTIVITA
				,:V_ID_TIPO_SOTTOATTIVITA
				,:V_DESCRIZIONE
				,:V_ID_OP_ULT
				,:V_DATA
				,:V_ID_MAIL
			);
		END;
		";

	} else {
		
		# normalizzazione id_attivita_custom
		$id_attivita_custom = int($id_attivita_custom);
		
		# id_attivita_custom definita (si forza l'id_attivita)
		$prep = "
		BEGIN
			:STATUS :=  TRAB_FORCED.CREA_NUOVA_ATTIVITA_FORCED(
				:V_ID_SISTEMA
				,:V_ID_SOTTOSISTEMA
				,:V_ID_TIPO_ATTIVITA
				,:V_ID_TIPO_SOTTOATTIVITA
				,:V_DESCRIZIONE
				,:V_ID_OP_ULT
				,:V_DATA
				,:V_ID_MAIL
				,:V_ID_ATTIVITA_CUSTOM
			);
		END;
		";
	}

	my $sth = $dbh->prepare($prep) || SIRTI::Err::db_fatal_error($db);

	$sth->bind_param_inout(":V_ID_SISTEMA",\$id_sistema, 10);
	$sth->bind_param_inout(":V_ID_SOTTOSISTEMA",\$id_sottosistema, 10);
	$sth->bind_param_inout(":V_ID_TIPO_ATTIVITA",\$id_tipo_attivita, 4);
	$sth->bind_param_inout(":V_ID_TIPO_SOTTOATTIVITA",\$id_tipo_sottoattivita, 10);
	$sth->bind_param_inout(":V_DESCRIZIONE",\$attivita_descrizione, 2000);
	$sth->bind_param_inout(":V_ID_OP_ULT",\$id_operatore , 4);
	$sth->bind_param_inout(":V_DATA",\$data_esecuzione , 14);
	$sth->bind_param_inout(":V_ID_MAIL",\$id_msg, 8);
	
	if (defined $id_attivita_custom){
		$sth->bind_param_inout(":V_ID_ATTIVITA_CUSTOM",\$id_attivita_custom, 10);
	}
	
	my $status;
	$sth->bind_param_inout(":STATUS",\$status , 1000);
	$sth->execute || return SIRTI::DB::get_errormessage();
	$status = "" if ($status !~/ORA-\d+/);
	return $status if (length($status) > 0);

	my $id_attivita;
	unless (defined $id_attivita_custom){
		my $r = $db->fetchall("select seq_attivita.currval from dual") || SIRTI::Err::db_fatal_error($db);
		$id_attivita=$r->[0]->[0];
	} else {
		$id_attivita=$id_attivita_custom;
	}

	#####Work-around x id_operatore_corrente (assegnazione attivita ad un operatore differente)
	if ( defined($id_operatore_corrente) ) {
		$db->do( qq{
			update	storia_attivita sa
			set		sa.id_operatore_corrente = $id_operatore_corrente
			where	sa.id_attivita = $id_attivita
		}) || return SIRTI::DB::get_errormessage();
		$db->do( qq{
			update	attivita a
			set		a.id_operatore_corrente = $id_operatore_corrente
			where	a.id_attivita = $id_attivita
		}) || return SIRTI::DB::get_errormessage();
	}
	
	$param{"id_attivita"} = $id_attivita;
	my $update_dta = "";
	$update_dta =  $self->_update_dta(%param) if (defined($record));
	
	return $update_dta if $update_dta ne "";
	
#	for my $j (keys %$record) {   # $j contiene id_tipo_dato_tecnico_attivita
#		next unless defined $record->{$j}; # salta se non il valore e' null
#		my $valore = $db->quote($record->{$j});
#		$db->do ("
#			insert into DATI_TECNICI_ATTIVITA(
#					ID_TIPO_DATO_TECNICO_ATTIVITA,
#					ID_ATTIVITA,
#					DATA_ESECUZIONE,
#					DESCRIZIONE,
#					MORTO,
#					OPER_ULTIMA_VAR,
#					DATA_ULTIMA_VAR
#			)
#			values (
#					$j
#					,$id_attivita
#					,to_date('$data_esecuzione','YYYYMMDDHH24MISS')
#					,$valore
#					,NULL
#					,$id_operatore
#					,sysdate
#			)
#		") || return SIRTI::DB::get_errormessage();
#	}	# for keys record

### Salvo gli (eventuali) allegati

	if ( scalar(@allegati) > 0 ) {
		#my $rc = $self->_save_attachments($id_attivita, $data_esecuzione, @allegati);
		my $err=legacy_attach_files($db,$id_attivita,$data_esecuzione,\@allegati,REPOSITORY => $self->{ART_REPOSITORY},%param);
		return $err if defined $err;
#		return "Errore $rc salvando gli allegati!" if $rc != 0;
	}

	return $id_attivita;

}


sub step_activity(){
	my $self = shift;
	my $db = $self->get_db();
	my %param=@_;
	my $virtual_step			= $param{"virtual_step"};
	my $id_operatore			= $param{"id_operatore"};
	my $id_operatore_corrente	= $param{"id_operatore_corrente"};
	my $id_attivita				= $param{"id_attivita"};
	my $id_stato				= $param{"id_stato"};
	my $id_azione				= $db->quote($param{"id_azione"});
	my $descr_attivita			= $db->quote($param{"descr_attivita"});
	my $data_esecuzione			= $param{"data_esecuzione"};
	my $record					= $param{"record"} || {};
	my @allegati				= ( defined($param{"allegati"}) ? @{$param{"allegati"}} : () );
	my $art						= $param{"art"};

	my $lua = $self->{LOOKUP_ART};
	
	##### Controllo id operatori
	return "ID_OPERATORE $param{id_operatore} non valido!" unless $lua->is_id_operatore($param{id_operatore});
	return "ID_OPERATORE_CORRENTE $param{id_operatore_corrente} non valido!" if defined($param{"id_operatore_corrente"}) && !$lua->is_id_operatore($param{id_operatore_corrente});

	$self->_check_tipo_attivita($param{"id_attivita"});
	my $current_status = $self->get_current_status($param{"id_attivita"});

	my $err = undef;
	unless ( $virtual_step ) {
		##### Controllo se l'azione e' ammessa per lo stato corrente
		return "AZIONE '" . $lua->nome_azione($param{id_azione}) . "' non consentita per lo STATO CORRENTE '" . $lua->nome_stato($current_status) . "'" unless scalar(grep(/^$current_status$/, $self->get_begin_states($param{"id_azione"})));
	
		##### Controllo se lo stato finale richiesto e' ammesso per l'azione corrente (OBSOLETO!!!!)
		if ( defined $id_stato ) {
			return "STATO FINALE $param{id_stato} non valido per l'AZIONE $param{id_azione} richiesta" unless $param{id_stato} eq $self->get_end_states($param{"id_azione"}, $current_status);
		} else {
			$id_stato = $self->get_end_states($param{"id_azione"}, $current_status);
			unless ( defined $id_stato ) {
				return "AZIONE $param{id_azione} non consentita per lo STATO CORRENTE $current_status";
			}
		}
	
		##### Controllo se i dta specificati sono validi
		$err = $self->check_tdta($param{"id_azione"}, $record,$art);
		return "$err\nTIPI DATI TECNICI non verificati per l'azione '".$lua->nome_azione($param{"id_azione"})."'\n" unless $err eq '';
	}
	
	### gestisco assegnazione
	my ($keep_activity_assignee,$id_assigned,$id_tipo_attivita);
	my $action_type = $db->fetch_minimalized("select flag_action_partenza From action where flag_action_partenza in ('E','F','H') and id_action = ".$id_azione);
	
	if ($action_type){
		if ($action_type eq 'E'){
			$param{"id_operatore_corrente"} = $id_operatore
		} elsif ($action_type eq 'F') {
			undef $param{"id_operatore_corrente"};
		} else { #$action_type eq 'H'
			undef $param{"id_operatore_corrente"};
		}
	} else {
		
		# verifico se e' uno stato finale
		my $is_finalStatus = $db->fetch_minimalized("select 1 from stati where flag_stato_partenza in ('C','Q') and id_stato = ".$self->get_db()->quote($id_stato));
		
		# se e' uno stato finale sicuramente perde l'assegnazione
		if ($is_finalStatus){
			$keep_activity_assignee = 0;
			undef $param{"id_operatore_corrente"};
		} else {
			($id_assigned,$id_tipo_attivita) = $db->fetch_minimalized("select id_operatore_corrente, id_tipo_Attivita From attivita where id_attivita = ".$id_attivita);

			if (defined $id_assigned){
				if (!$db->fetch_minimalized("select 1 From action where flag_action_partenza = 'P' and id_action = ".$id_azione)){
					$keep_activity_assignee = 1;
				}
			} else {
				$keep_activity_assignee = 0;
			}
		}
	}
	
### Memorizzo nella tabella STORIA_ATTIVITA la transizione di STATO
#	print STDERR "storia attivita $id_attivita\n";

	my $sql="
	   insert into storia_attivita
			(
				ID_ATTIVITA,
				DATA_ESECUZIONE,
				ID_ACTION,
				DESCR_AZIONE,
				ID_OPERATORE,
				ID_STATO_RISULTANTE,
				ID_OPERATORE_CORRENTE
			)  values (
				$id_attivita
				,to_date('$data_esecuzione','YYYYMMDDHH24MISS')
				,$id_azione
				,$descr_attivita
				,$id_operatore
				,".$db->quote($id_stato)."
				," . (
					defined($param{"id_operatore_corrente"}) ? $param{"id_operatore_corrente"} :
					$keep_activity_assignee ? $id_assigned : 'null'
				) . "
			)
	";
#	print STDERR $sql."\n";
	
	my $rv=$db->do($sql) || return SIRTI::DB::get_errormessage();

### Aggiorno le informazioni di STATO_CORRENTE, OPERATORE e DATA VARIAZIONE DI STATO
###  del ticket nella tabella ATTIVITA


	$sql="
		update  ATTIVITA
		set
				DATA_ULT_VARSTAT=to_date('$data_esecuzione','YYYYMMDDHH24MISS')
				,ID_OP_ULT_VARSTAT=$id_operatore
				--,ID_OPERATORE_CORRENTE=$id_operatore
				". (defined $keep_activity_assignee && $keep_activity_assignee == 1 ? "" : ",ID_OPERATORE_CORRENTE=" . (defined($param{"id_operatore_corrente"}) ? $param{"id_operatore_corrente"} : 'null')) ."
				,STATO_CORRENTE=".$db->quote($id_stato)."
		where ID_ATTIVITA=$id_attivita
	";
	
	
#	print STDERR $sql."\n";
	$rv=$db->do($sql)  || return SIRTI::DB::get_errormessage();


### Salvo gli (eventuali) allegati

	if ( scalar(@allegati) > 0 ) {
#		my $rc = $self->_save_attachments($id_attivita, $data_esecuzione, @allegati);
		my $err=legacy_attach_files($db,$id_attivita,$data_esecuzione,\@allegati,REPOSITORY => $self->{ART_REPOSITORY});
		return $err if defined $err;
#		return "Errore $rc salvando gli allegati!" if $rc != 0;
	}

### Aggiorno i DATI TECNICI ATTIVITA

	return $self->_update_dta(%param) if (defined($record));
	
	return "";
}

sub get_current_status($$) {
	my ($self,$id_attivita)=@_;
	my $sql="
		select stato_corrente from attivita where ID_ATTIVITA=$id_attivita
	";
	my $db =$self->get_db();
	my $result = $db->fetchall($sql) || return undef;
	return '-1'  if (scalar(@$result) == 0);
	return $result->[0]->[0];
}

sub get_begin_states {
	my $self=shift;
	my @bs=();
	for my $action(@_) {
		SIRTI::Err::prec(defined($action), "Azione non definita!");
		SIRTI::Err::prec(exists($self->{PERMISSION_ACTION}->{BS}->{$action}), $self->{LOOKUP_ART}->nome_azione($action) . ": Azione senza stati iniziali o non esistente");
		push @bs,@{$self->{PERMISSION_ACTION}->{BS}->{$action}};
	}
	my %visto=();
	return grep(!$visto{$_}++, @bs);  # ritorna @bs eliminando i duplicati
}

sub get_end_states($$$) {
	my ($self,$action,$begin_state)=@_;
	return $self->{PERMISSION_ACTION}->{ES}->{$action}->{$begin_state};
}

sub get_tdta_action($$) {
	my ($self,$action)=@_;
	SIRTI::Err::prec(defined($action));
	return $self->{TDTA_ACTION}->{$action}; # ritorna un puntatore ad un hash con chiavi id_tdta
}


sub check_tdta($$$$) {
	my ($self,$action,$dta,$art) = @_;
	SIRTI::Err::prec(defined($action),'action non definita');
	SIRTI::Err::prec(defined($dta),'dta non definita');
	my ($key, $value,$err)=(undef,undef,'');
	
	my $h=$self->{TDTA_ACTION_WITH_HASH}->{$action};
#	my $h = $self->get_tdta_action($action);
	my %tdta_action = defined($h) ? %$h : ();
	
	##### Converte le chiavi dell'hash se sono state specificate come DESCRIZIONE e non come ID_TDTA
	my @keys = keys %$dta;
	foreach $key (@keys) {
		if ( $key !~ /^\d+$/ ) {
			if ( exists($self->{TDTA_NAME}->{$key}) ) {
				$dta->{$self->{TDTA_NAME}->{$key}} = $dta->{$key};
				#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#
				#                                                           #
				# DECIDERE SE AGGIUNGERE IL CONTROLLO X EVITARE CHE PER UN  #
				# DTA OBBLIGATORIO VENGA MEMORIZZATO undef O ''             #
				#                                                           #
				#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#-#
			} else {
				$err .= "TDTA ".$self->_get_tdta_name($key)." non ammesso per l'azione ".$self->_get_action_name($action)."!\n";
			}
			delete $dta->{$key};
		}
	}
	
	### aggiunge i valori di default alle chiave non specificate
	for my $id(keys %tdta_action) {
		$dta->{$id}=$tdta_action{$id}->{VALORE_DEFAULT} if !exists $dta->{$id} && defined($tdta_action{$id}->{VALORE_DEFAULT});
	}
	
	##### Controlla che i DTA specificati siano ammessi per l'azione $action e che non siano di tipo SOLO_LETTURA
	while ( ($key => $value) = each(%$dta) ) {
		if ( exists($tdta_action{$key}) ) {
			delete $tdta_action{$key};
		} else {
			$err .= "TDTA ".$self->_get_tdta_name($key)." non ammesso per l'azione ".$self->_get_action_name($action)."!\n";
		}
	}
	
	##### Controlla se non sono stati specificati DTA abbligatori (x $action)
	while ( ($key => $value) = each(%tdta_action) ) {
		if ( $value->{OBBLIGATORIO} eq 'Y' ) {
			$err .= "TDTA ".$self->_get_tdta_name($key)." obbligatorio!\n";
		}
	}
	
	my $lua = $self->{LOOKUP_ART};

	my $db = $self->get_db();

	my $prepare_iso_date = $db->create_prepare("select to_timestamp_tz(?,'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm') at time zone 'Europe/Rome' from dual");
	
	foreach my $prop ( keys %{ $dta } ) {
		
		my $tipo_ui = $lua->tipo_ui_tdta_nome($self->_get_tdta_name($prop));
		
		if ($tipo_ui eq 'EMAIL' && defined $dta->{$prop} && $dta->{$prop} ne ''){
			
			unless ($dta->{$prop} =~ /^[a-z0-9!#$%&'*+\/=?^_`{|}~.-]+@[a-z0-9]([a-z0-9-]*[a-z0-9])?(\.[a-z0-9]([a-z0-9-]*[a-z0-9])?)*$/i){
				$err .= "PROPERTY ".$self->_get_tdta_name($prop)." must be a valid EMAIL address!";
				next;
			}
		
		} elsif ($tipo_ui eq 'ISODAT' && defined $dta->{$prop} && $dta->{$prop} ne ''){
			### FIXME
			my $format_iso_date = eval{$art->format_iso_date($dta->{$prop})};
			unless ($format_iso_date){
				$err .= "PROPERTY ".$self->_get_tdta_name($prop)." must be a valid ISO date string representation!";
				next;
			}
			eval {$prepare_iso_date->fetch_minimalized($dta->{$prop})};
			if (SIRTI::DB::get_errormessage()){
				#return SIRTI::DB::get_errormessage();
				$err .= "Invalid ISODate for ".$self->_get_tdta_name($prop);
				next;
			}
			$dta->{$prop} = $format_iso_date;
		} elsif ($tipo_ui =~/^(TAGS|MOOKUP)$/){
			if (defined $dta->{$prop} && $dta->{$prop} ne ''){
				my $array = eval{ from_json($dta->{$prop} )};
				
				if ($@) {
					$err .= "Unable to parse JSON ".$self->_get_tdta_name($prop)." ".$dta->{$prop}.": " . $@;
					next;
				};
				
				unless ((ref $array) eq 'ARRAY') {
					$err .= "JSON ".$dta->{$prop}." must be an array ";
					next;
				}
				
				unless (scalar(@{$array})) {
					$dta->{$prop} = '';
					next;
				}
				
				my @array_unique = ();
				
				for my $tag_to_check (@{$array}){
					
					if (ref $tag_to_check){
						$err .= "The elements of ".$self->_get_tdta_name($prop)." ".$dta->{$prop}." must be STRINGS";
						next;
					}
					
					push @array_unique, $tag_to_check unless grep {$_ eq $tag_to_check} @array_unique;
				}
				
				$dta->{$prop} = to_json(\@array_unique);
			}
			
		} elsif ($tipo_ui eq 'CURRENCY' && defined $dta->{$prop} && $dta->{$prop} ne ''){
			my $decimali = $lua->decimali_tdta_nome($self->_get_tdta_name($prop));
			my $reg = qr/^\d+(\.\d{${decimali}})?$/;
			unless ($dta->{$prop} =~ /$reg/){
				$err .= "PROPERTY ".$self->_get_tdta_name($prop)." must be a valid CURRENCY!";
				next;
			}
		} elsif ($tipo_ui eq 'BOOL' && defined $dta->{$prop} && $dta->{$prop} ne ''){
			if  ($dta->{$prop} !~ /^(0|1)$/){
				$err .= "Invalid boolean for ".$self->_get_tdta_name($prop);
				next;
			}
		} elsif ($tipo_ui eq 'DAY' && defined $dta->{$prop} && $dta->{$prop} ne ''){
			if ($dta->{$prop} !~ /^\d{4}-\d{2}-\d{2}$/){
				$err .= "Invalid day for ".$self->_get_tdta_name($prop);
				next;
			}
			if ($db->test_date($dta->{$prop},'YYYY-MM-DD') ne ''){
				$err .= "Invalid day for ".$self->_get_tdta_name($prop);
				next;
			}
		} elsif ($tipo_ui eq 'JSON') {
			my $array = eval{ from_json($dta->{$prop})};
		
			if ($@) {
				$err .= "Unable to parse JSON ".$self->_get_tdta_name($prop);
				next;
			};
		} elsif ($tipo_ui eq 'LATLNG' && defined $dta->{$prop} && $dta->{$prop} ne ''){
			if ($dta->{$prop} !~ /^([+-])?([0-8]?[0-9](\.\d+)?|90(\.0+)?),\s*([+-])?(180(\.0+)?|((1[0-7][0-9]|[0-9][0-9]|[0-9])(\.\d+)?))$/){
				$err .= "Invalid coordinates for ".$self->_get_tdta_name($prop);
				next;
			}
		} elsif ($tipo_ui eq 'NUMBER' && defined $dta->{$prop} && $dta->{$prop} ne ''){
			if ($dta->{$prop} !~ /^-?\d+(\.\d+)*$/){
				$err .= "Invalid coordinates for ".$self->_get_tdta_name($prop);
				next;
			}
		} elsif ($tipo_ui eq 'ORA' && defined $dta->{$prop} && $dta->{$prop} ne ''){
			if ($dta->{$prop} !~ /^\d{4}:\d{2}$/){
				$err .= "Invalid hour for ".$self->_get_tdta_name($prop);
				next;
			}
		} elsif ($tipo_ui eq 'POPUP' && defined $dta->{$prop} && $dta->{$prop} ne ''){
			my $valori = $lua->valori_tdta_nome($self->_get_tdta_name($prop));
			my @elenco_valori = split(',', $valori);
			unless (grep {$dta->{$prop} eq $_} @elenco_valori){
				$err .= "Value '".$dta->{$prop}."' not available for ".$self->_get_tdta_name($prop);
				next;
			}
		}
	}
	
	return $err;
}


use enum  (												# valori nell' hash di  ritorno
				'PERM_TDTA_MATCH'							# ok - la chiave esiste
				,'PERM_TDTA_NON_AMMESSO_PER_AZIONE'		# la chiave non e' ammessa per l' azione immessa
				,'PERM_TDTA_OBBLIGATORIO'					# la chiave non e' stata specificata ma e' obbligatoria
			);
			
sub get_tdta_permission  {
	# ACTION - id azione
	# DTA    - puntatore ad un hash di chiavi/valori di dati tecnici attivita
	my $self=shift;
	my %param=@_;
	SIRTI::Err::prec(defined($param{ACTION}),'parametro ACTION non definito');
	SIRTI::Err::prec(defined($param{DTA}),'parametro DTA non definito');
	
#	my ($key, $value,$err)=(undef,undef,'');
	my $h = $self->get_tdta_action($param{ACTION});
	my %tdta_action = defined($h) ? %$h : ();
	
	##### Converte le chiavi dell'hash se sono state specificate come DESCRIZIONE e non come ID_TDTA
	my $dta=$param{DTA};
	for my $key(keys %$dta) {
		if ( $key =~ /^[a-z]+/i ) {
			my $key_id=$self->{TDTA_NAME}->{$key};
			if (defined($key_id) ) {
				$dta->{$key_id} = $dta->{$key};
			}
			delete $dta->{$key};
		}
	}
		
	
	##### Controlla che i DTA specificati siano ammessi per l'azione $action
	my %hret=();
	for my $key(keys %$dta) {
		if ( exists($tdta_action{$key}) ) {
			delete $tdta_action{$key};
			$hret{$key}  = PERM_TDTA_MATCH
		} else {
			$hret{$key} = PERM_TDTA_NON_AMMESSO_PER_AZIONE;
		}
	}
	##### Controlla se non sono stati specificati DTA abbligatori (x $action)
	while ( (my $key ,my  $value) = each(%tdta_action) ) {
		if ( $value eq 'Y' ) {
			$hret{$key}=PERM_TDTA_OBBLIGATORIO;
		}
	}
	return \%hret;
}


#questa funzione statica risolve il nome dell' allegato (se riesce)
#e' qui solo per compatibilita con vecchi programmi che non usano questa classe ma leggono l' allegato in modo diretto dal db
# EMIT_INPUT_FD  - invece di ritornare un nome ritorna un file descriptor del file aperto in lettura
sub resolve_attachment {
	my $repo=shift;
	my $file_name=shift;
	my %params=@_;
	my $name=_resolve_attachment($repo,$file_name);
	return $name unless defined $name;
	return $name unless $params{EMIT_INPUT_FD};
	my $taropt=_get_tar_opt($name);
	return  undef unless defined $taropt;
	my $r=_check_attachment($name,$taropt);
	return undef if $r != 0;
	my $fd;
	open($fd,"tar -Ox".$taropt."f '$name' |") or return undef;
	return  $fd;
}

#
#NO_CHECK  - non effettua il check di integrita dell' allegato
#          - attenzione, nel caso di lettura dell' allegato e con questa opzione impostata
#            l'eventuale errore non viene rilevato
#FILENAME  - se specificato, prendera' in considerazione solo l'allegato con lo stesso NOME_FILE_SERVIZIO
#		   - perche' piu allegati con lo stesso nome avranno un nome di file servizio unico
#EMIT_INPUT_FD	- non vengono creati i file di output ed in $files vengono emessi gli handle di lettura degli attachments
#SIZE_ONLY	- restituisce solo un array di hash con le dimensioni degli allegati
#
sub detach_attachments {
	my $self = shift;
	my $id_attivita = shift || return 'ID_ATTIVITA non specificato';
	my $action_date = shift || return 'ACTION_DATE non specificata';
	my $files = shift || return 'Array ref non specificato';
	eval { $self->_check_tipo_attivita($id_attivita); } || return "$id_attivita: ha un tipo attivita non congruente";
	my $path = shift || '.';
	$path =~ s/\/*$//;
	$path .= '/';
	my %params=@_;
#	my $i = 0;
	my $sql =	"
					select NOME_FILE_CLIENT, NOME_FILE_SERVIZIO, DIMENSIONE
					from   ALLEGATI_AZIONE
					where  ID_ATTIVITA = $id_attivita
						   and ID_ACTION = to_date('$action_date', 'YYYYMMDDHH24MISS')
						   and data_cancellazione is null
				";
	my $rows = $self->{DB}->fetchall_hashref($sql) || return SIRTI::DB::get_errormessage();
	return "Nessun attachment per l'attivita' $id_attivita\n" if scalar(@$rows) == 0;
	$|=1;
	
	my $rc = '';
	my @size_only = ();
	foreach my $row ( @$rows ) {
		next if (defined $params{FILENAME} && $params{FILENAME} ne $row->{NOME_FILE_SERVIZIO});
		
		if ($params{SIZE_ONLY}){
			push @size_only, {
				NOME_FILE_CLIENT => $row->{NOME_FILE_CLIENT}
				,SIZE => $row->{DIMENSIONE}
			};
			next;
		} else {
			my $fullpath=_resolve_attachment($self->{ART_REPOSITORY},$row->{NOME_FILE_SERVIZIO});
			return "Attachment non risolto per (id_attivita,data_elaborazione)=($id_attivita,$action_date)\n" unless defined $fullpath;
			$row->{NOME_FILE_CLIENT} =~ s/\\/\//g; ## Gestione nomi Windows !!!!!
			my $nome_file_client = basename($row->{NOME_FILE_CLIENT});
			my $taropt=_get_tar_opt($fullpath);
			return "$fullpath: estensione attachment non valida (valide sono gz|bz2)\n" unless defined $taropt;
			unless ($params{NO_CHECK}) {
				my $r=_check_attachment($fullpath,$taropt);
				return "$fullpath: il file non ha superato il test di validita\n" if $r;
			}
			
			return "Directory $path non trovata!" unless -d $path;
			return "Directory $path non scrivibile!" unless -w $path;
			
			my $fullclient=$path.$nome_file_client;
			if  (-e $fullclient && !$params{EMIT_INPUT_FD}) {
				$rc .= "File di destinazione $fullclient gia' presente.\n";
			}
			else {
				my ($fi,$fo);
				if (open $fi, "tar -Ox".$taropt."f '$fullpath' |") {
					if ($params{EMIT_INPUT_FD}) {
						push @$files,$fi;
						sleep 1;  #ritarda per problemi di sincronizzazione
					}
					elsif (open($fo, ">",$fullclient)) {
						while (<$fi>) {
							unless (print $fo $_) {
								close $fo;
								close $fi;
								return "$fullclient: errore di scrittura\n$!\n";
							}
						}
						close $fi;
						close $fo;
						push @$files, $nome_file_client;
					}
					else {
						close $fi;
						return "Impossibile creare $fullclient\n$!\n";
					}
				}
				else {
					return "errore apertura tar in pipe\n$!\n";
				}
			}
		}
	}
	return 
		$rc ne '' ? $rc : 
		$params{SIZE_ONLY} ? \@size_only
		: $rc;
}

sub get_sysdate  {
	my $self=shift;
	# FORMAT  => formato della data (default YYYYMMDDHH24MISS)
	# ADD	  => numero secondi da aggiungere (default 0)
	my %param=@_;
	$param{FORMAT} = "YYYYMMDDHH24MISS" if (!defined($param{FORMAT}));
	my $db = $self->get_db();
	my $sql="select to_char(
		sysdate " . (defined($param{ADD}) ? "+ $param{ADD} / 86400  " : "") . "
		,'$param{FORMAT}')
		from dual";
	my $r = $db->fetchall($sql) or  SIRTI::Err::db_fatal_error($db);
	return $r->[0]->[0];
}

sub date_add  {
	my $self=shift;
	# DATE    => la data da elaborare
	# FORMAT  => formato della data (default YYYYMMDDHH24MISS)
	# ADD	  => numero secondi da aggiungere o sottrarre (default 0)
	my %param=@_;
	$param{FORMAT} = "YYYYMMDDHH24MISS" if (!defined($param{FORMAT}));
	my $db = $self->get_db();
	my $sql = "select to_char(" . (defined($param{DATE}) ? "to_date('$param{DATE}', '$param{FORMAT}')" : "sysdate") . " " . (defined($param{ADD}) ? "+ ($param{ADD} / 86400)  " : "") .
			  ",'$param{FORMAT}') from dual";
	my $r = $db->fetchall($sql) or  SIRTI::Err::db_fatal_error($db);
	return $r->[0]->[0];
}


###############################################################################################
#
# DESCRIPTION
#   Metodo x automatizzare l'estrazione delle attivita e dei relativi TDTA in forma tabulare
#   tenendo conto della storicita' dei valori dei TDTA; infatti, per ogni DTA viene estratto
#   il valore piu' recente in riferimento alla ACTION_DATE specificata nel formato 'YYYYMMDDHH24MISS'.
#   In altre parole viene estratta la situazione immediatamente antecedente la data specificata.
#
# RETURN VALUE
#   Ritorna il puntatore ad un cursore ritornato dal metodo create_cursor() del package SIRTI::DB
#   I record ritornati dal cursore conterranno, nell'ordine, i valori dei TDTA specificati e tutte
#   le colonne della tabella ATTIVITA.
#
# METODI DI SELEZIONE
#   E' possibile filtrare i record in base a:
#     - uno o piu' ATTIVITA.ID_ATTIVITA mediante parametro (array_ref) ATTIVITA
#     - uno o piu' ATTIVITA.STATO_CORRENTE mediante parametro (array_ref) STATI
#     - uno o piu' specifici DTA mediante parametro TDTA_FILTER
#
# ESEMPIO
#   Il seguente esempio ritorna tutti i ticket con STATO_CORRENTE 'A' o '10' che hanno
#   associato un TDTA con ID_TIPO_DATO_TECNICO=2 il cui valore inizia per 'S'
#   ed il TDTA 'NOME' il cui valore inizia per 'A'
#     my $cursor = $event->activity_selection  (
#                                                 ,ATTIVITA       =>  [] # si puo' omettere!!
#                                                 ,STATI          =>  [ 'A' , '10' ]
#                                                 ,TDTA           =>  [ {NAME=>'COGNOME',ID=>2}
#                                                                       ,{NAME=>'NOME',ID=>3} ]
#                                                 ,TDTA_FILTER    =>  { 2=>" like 'S%'",NOME => "like 'A%'" }
#                                                      oppure
#                                                 ,TDTA_FILTER    =>  { COGNOME =>" like 'S%'" }

#                                                 ,ACTION_DATE    =>  '20061231000000'
#                                               );
#   Per stampare COGNOME, NOME e ID_ATTIVITA :
#     while ( my $row = $cursor->fetchrow_hashref() ) {
#       print $rows->{COGNOME}, $rows->{NOME}, $rows->{ID_ATTIVITA}, "\n";
#     }
#
###############################################################################################
sub activity_selection {
	my $self = shift;
	# TYPE			=> tipo di oggetto da ritornare in ('CURSOR','ARRAYREF','HASHREF','QUERY'); default='CURSOR'
	# ATTIVITA		=> array_ref con gli id_attivita da ricercare
	# ACTIONS		=> array_ref con gli id delle ACTION da filtrare in STORIA_ATTIVITA
	# STATI			=> array_ref con gli id degli stati da filtrare
	# TDTA			=> array_ref di hash_ref con  { NAME => nome_tdta , ID => id_tdta } da estrarre
	# TDTA_FILTER	=> hash_ref che ha come chiavi gli id_tdta e come valori i relativi filtri
	# ACTION_DATE	=> scalar_ref contenente la data di riferimento in base alla quale estrarre i DTA
	# ACTION_DATE_OP=> operatore da utilizzare per il match sulla data_esecuzione
	# SQL_DIRECTIVE	=> direttive sql - Es per oracle +RULE
	my %param = @_;
	$param{TYPE}					= 'CURSOR' unless defined($param{TYPE});
	$param{ATTIVITA}				= [] unless defined($param{ATTIVITA});
	$param{ACTIONS}					= [] unless defined($param{ACTIONS});
	$param{STATI}					= [] unless defined($param{STATI});
	$param{TDTA}					= [] unless defined($param{TDTA});
	$param{TDTA_FILTER}				= {} unless defined($param{TDTA_FILTER});
	$param{ACTION_DATE}				= '' unless defined($param{ACTION_DATE});
	$param{ACTION_DATE_OP}			= '<=' unless defined($param{ACTION_DATE_OP});
	$param{SQL_DIRECTIVE}			= ' ' unless defined($param{SQL_DIRECTIVE});
	$param{FORMATO_DATA_ESECUZIONE}	= 'YYYY-MM-DD HH24:MI:SS' unless defined($param{FORMATO_DATA_ESECUZIONE});
	
	$param{ACTION_DATE}	= ($param{ACTION_DATE} eq '' ? 'a.data_ult_varstat+2/86400' : "to_date('$param{ACTION_DATE}', 'YYYYMMDDHH24MISS')");
	unless ( $param{TYPE} =~ /(CURSOR)|(ARRAYREF)|(HASHREF)|(QUERY)/i ) {
		die "Parametro TYPE='$param{TYPE}' non valido.\nValori consentiti: 'CURSOR' 'ARRAYREF' 'HASHREF' 'QUERY'\n";
	}
	unless ( $param{ACTION_DATE_OP} =~ /(<)|(<=)|(=)|(>=)|(>)/i ) {
		die "Parametro ACTION_DATE_OP='$param{ACTION_DATE_OP}' non valido.\nValori consentiti: '<' '<=' '=' '>=' '>'\n";
	}
	
	my @fields = ();
	my @fields_dta = ();
	my @from = ();
	my @where = ();
	my $i = 0;
	my $sql = '';

	#
	# SELECT
	#
	for ($i=0; $i<scalar(@{$param{TDTA}}); ++$i) {
		push @fields_dta, '(select es.descrizione from es where es.id_Attivita = es_attivita.id_Attivita and es.DESC_TDTA = '.$self->get_db()->quote($param{TDTA}->[$i]->{NAME}).') "'.$param{TDTA}->[$i]->{NAME}.'"';
	}
	push @fields, 'a.*';
	push @fields, "to_char(sa.DATA_ESECUZIONE, '$param{FORMATO_DATA_ESECUZIONE}') DATA_ESECUZIONE";
	$sql .= 'with es_attivita as (select' . "\t" . '/*'.$param{SQL_DIRECTIVE}.'*/ ' . "\n\t\t " . join("\n\t\t,", @fields) . "\n";

	#
	# FROM
	#
	my $att_type = ref($param{ATTIVITA});
	push @from, 'ATTIVITA a';
	push @from, 'STORIA_ATTIVITA sa';
	my $dta_id_attivita = '';
	if ( $att_type eq 'ARRAY' ) {
		if ( scalar(@{$param{ATTIVITA}}) > 0 ) {
			$dta_id_attivita = "and dta.ID_ATTIVITA in (" . join(",", @{$param{ATTIVITA}}) . ")";
		}
	} elsif ( $att_type eq 'SCALAR'  ||  $att_type eq '' ) {
			$dta_id_attivita = "and dta.ID_ATTIVITA = $param{ATTIVITA}";
	}
=pod
	for ($i=0; $i<scalar(@{$param{TDTA}}); ++$i) {
#		push @from, "(
#                        select *
#                        from   dati_tecnici_attivita dta
#                        where  dta.ID_TIPO_DATO_TECNICO_ATTIVITA = $param{TDTA}->[$i]->{ID} -- $param{TDTA}->[$i]->{NAME}
#                               $dta_id_attivita
#                               and dta.DATA_ESECUZIONE < $param{ACTION_DATE}
#                     ) dta$i
#        ";
	}
=cut
	$sql .= 'from' . "\t " . join("\n\t\t,", @from) . "\n";

	#
	# WHERE
	#
	##### ATTIVITA
	push @where, "${\$self->get_id_tipo_attivita()} = a.ID_TIPO_ATTIVITA";
	if ( $att_type eq 'ARRAY' ) {
		if ( scalar(@{$param{ATTIVITA}}) > 0 ) {
			push @where, "a.ID_ATTIVITA in (" . join(",", @{$param{ATTIVITA}}) . ")";
		}
	} elsif ( $att_type eq 'SCALAR'  ||  $att_type eq '' ) {
			push @where, "a.ID_ATTIVITA = $param{ATTIVITA}";
	}
	##### STATI
	if ( scalar(@{$param{STATI}}) > 0 ) {
		push @where, "a.STATO_CORRENTE in ('" . join("','", @{$param{STATI}}) . "')";
	}
	##### STORIA_ATTIVITA
	#$param{ACTION_DATE}	= ($param{ACTION_DATE} eq '' ? 'sysdate' : "to_date('$param{ACTION_DATE}', 'YYYYMMDDHH24MISS')");
	my $action_filter = '';
	$action_filter =  "and ID_ACTION in ('" . join("','", @{$param{ACTIONS}}) . "')" if scalar(@{$param{ACTIONS}}) > 0;
	push @where,	"sa.ID_ATTIVITA = a.ID_ATTIVITA
                     and sa.DATA_ESECUZIONE = (
                                                 select max(DATA_ESECUZIONE)
                                                 from   storia_attivita
                                                 where  ID_ATTIVITA = a.ID_ATTIVITA
                                                        and DATA_ESECUZIONE < $param{ACTION_DATE}
                                                        $action_filter
                                              )
                    ";

	$sql .= 'where' . "\t" . join("\n\t\tand ", @where) . "\n";
	$sql.= "),es as (select dta2.id_attivita, tdta.descrizione desc_tdta, dta2.descrizione
	 	from dati_Tecnici_Attivita dta2
	 		join tipi_dati_Tecnici_attivita tdta on tdta.id_tipo_Dato_Tecnico_attivita = dta2.id_tipo_dato_Tecnico_Attivita
			 join es_attivita on es_attivita.id_attivita = dta2.id_Attivita
	 	where (dta2.id_Attivita, dta2.id_tipo_dato_Tecnico_attivita, dta2.data_esecuzione) in (
	 		select id_attivita, id_tipo_Dato_Tecnico_attivita, max(data_esecuzione)
	 		from dati_Tecnici_attivita dta
	 		where 1=1
	 			and dta.id_Attivita = es_Attivita.id_Attivita
	 			and DATA_ESECUZIONE $param{ACTION_DATE_OP} to_date(es_attivita.data_Esecuzione,'YYYY-MM-DD HH24:MI:SS')
	 		group by id_attivita, id_Tipo_dato_Tecnico_attivita
	 	)) select ".(scalar @fields_dta ? join("\n\t\t,", @fields_dta)."," : '')." es_attivita.* from es_attivita";
	
	if (keys(%{$param{TDTA_FILTER}})) {
		$sql="select * from ($sql) where 1=1 ";
		for my $k(keys(%{$param{TDTA_FILTER}})) {
			$sql.=" and ";
			$sql.= $k=~/^\d+$/
					? $self->{LOOKUP_ART}->nome_tdta($k)
					: $k;
			$sql.=' ';
			$sql.=$param{TDTA_FILTER}->{$k};
		}
	}
	
#	print STDERR $sql, "\n";
	if ( $param{TYPE} eq 'CURSOR' ) {
		return $self->get_db()->create_cursor($sql);
	}
	elsif ( $param{TYPE} eq 'ARRAYREF' ) {
		return $self->get_db()->fetchall($sql);
	}
	elsif ( $param{TYPE} eq 'HASHREF' ) {
		return $self->get_db()->fetchall_hashref($sql);
	}
	elsif ( $param{TYPE} eq 'QUERY' ) {
		return $sql;
	}
	else {
		SIRTI::Err::internal_error($param{TYPE}.": valore invalido ");
	}
	return undef;

}

##### Imposta una nuova istanza di DB
sub set_db($$) {
	my $self = shift;
	my $db_ref = shift;
	if ( ref($db_ref) eq 'SIRTI::DB' ) {
		$self->{DB} = $db_ref;
	} else {
		$self->{DB}= new SIRTI::DB($db_ref);
	}
}
##### Rimuove l'istanza di DB
sub unset_db($) {
	my $self = shift;
	$self->{DB} = undef;
}

#   Esegue l' attach di allegati
#   parametri posizionali:
#		db 				- istanze del db (puo essere SIRTI::DB oppure DBI oppure una classe legacy)
#		id_activity		- id attivita
#   	action_date     - nel formato YYYYMMHH24MISS
#   	files 			- array di nome di file oppure di filehandle convertibili in stringa
#   parametri nominali:
#  		REPOSITORY	- directory del repository - se non specificata viene ricavata dalla tabella di configurazione globale
#   	NO_MAKE_OUTPUT_SUBDIR	- non viene creata la sottodirectory di memorizzazione
#   	MAKE_OUTPUT_SUBDIR_PERMS - permessi per la creazione delle sottodirectory - default nessuno
#		TMPDIR	- directory per i files temporanei - se non specificata viene preso dalla tabella di configurazione globale oppure (come caso ultimo) /tmp
#   la routine ritorna una stringa di messaggi separati da newline in caso di errori altrimenti undef
#

sub legacy_attach_files { #metodo statico
	my $db=shift || return 'DB non  specificato';
	my $id_attivita = shift || return 'ID_ATTIVITA non specificato';
	my $action_date = shift || return 'ACTION_DATE non specificata';
	my $files = shift || return 'Array ref di files non specificato';
	my %params=@_;
	$db=SIRTI::DB->new($db) if ref($db) ne 'SIRTI::DB';
	my $repos=$params{REPOSITORY};
	unless (defined $repos) {
		$repos=$db->fetch_minimalized("select valore from config_art where upper(chiave)='ART.REPOSITORY' and rownum=1 ");
		return SIRTI::DB::get_errormessage() if SIRTI::DB::iserror();
	}
	return "REPOSITORY non trovato" unless defined $repos;
	return "$repos: non e' una directory" unless -d $repos;
	my @dirs=$action_date=~/^(\d{4})(\d{2})(\d{2})/;
	return "$action_date: non  e' stato possibile ricavare il nome delle sottodirectory (yyyy/mm/dd)" if scalar(@dirs) == 0;
	my $outdir=$repos.'/'.join("/",@dirs);
	if ($params{NO_MAKE_OUTPUT_SUBDIR}) {
		return "$outdir: non e' una directory" unless -d $outdir;
	}
	else {
		my $perm=$params{MAKE_OUTPUT_SUBDIR_PERMS};
		$perm=defined $perm ? "-m '$perm'" : '';
		my $cmd="mkdir $perm -p '$outdir'";
		local $?;
		system($cmd);
		return "$cmd: $?" if $?;
	}
	my @errors=();
	my @elab=();
	
	my $insert_prep=$db->create_prepare("
				insert into ALLEGATI_AZIONE (
					 ID_ATTIVITA
					,ID_ACTION
					,NOME_FILE_SERVIZIO
					,NOME_FILE_CLIENT
					,DIMENSIONE
					,TITOLO
					,DESCRIZIONE
					,REVISIONE
					,DATA_RIFERIMENTO
					,ID_TIPO_ALLEGATO_TIPO_DOC
					,ID_ALLEGATO
					,ID_TRANSIZIONE
				)
				values	(
					$id_attivita --ID_ATTIVITA
					,to_date('$action_date', 'YYYYMMDDHH24MISS') --ID_ACTION
					,? --NOME_FILE_SERVIZIO
					,? --NOME_FILE_CLIENT
					,? --DIMENSIONE
					,? --TITOLO
					,? --DESCRIZIONE
					,? --REVISIONE
					,to_date(?, 'YYYY-MM-DD') --DATA_RIFERIMENTO
					,(
						select tata.id
						from tipi_attivita_tipi_allegati tata
							join attivita att on att.id_tipo_attivita = tata.id_tipo_attivita
						where tata.id_tipo_documento = ?
							and att.id_attivita = $id_attivita
						) --ID_TIPO_ALLEGATO_TIPO_DOC
					,? --ID_ALLEGATO
					,(select sa.id_transizione from storia_attivita sa where sa.id_attivita = $id_attivita and sa.data_esecuzione = to_date('$action_date', 'YYYYMMDDHH24MISS')) --ID_TRANSIZIONE
				)
		");
	return SIRTI::DB::get_errormessage() if SIRTI::DB::iserror();
	
	my $insert_meta_prep=$db->create_prepare("
				insert into ALLEGATI_META (
												 ID_ALLEGATO
												,KEY
												,VALUE
											)
				values	(?,?,?)
		");
	return SIRTI::DB::get_errormessage() if SIRTI::DB::iserror();

	my $delete_prep=$db->create_prepare("
				delete ALLEGATI_AZIONE
				where id_attivita=$id_attivita and ID_ACTION=to_date('$action_date', 'YYYYMMDDHH24MISS')
	");
	return SIRTI::DB::get_errormessage() if SIRTI::DB::iserror();
	
	### Inizializzo il progressivo verificando che non ci siano gia' altri file allegati nello stesso minuto
	my $progressivo = $db->fetch_minimalized ("
		select nvl2(to_number(substr(nome_file_servizio,instr(nome_file_servizio,'-',1,2)+1,4)),to_number(substr(nome_file_servizio,instr(nome_file_servizio,'-',1,2)+1,4))+1,0) PROGR
		from (
		  select max(nome_file_servizio) Nome_file_servizio From allegati_azione
		  where id_attivita = $id_attivita
		    and nome_file_servizio like '".$id_attivita.'-'.substr($action_date, 0, 12).'-%'."'
		)
	");
	return SIRTI::DB::get_errormessage() if SIRTI::DB::iserror();
	
	my $tata = $db->fetchall_hashref("
		select tata.id
			, tata.id_tipo_documento
			, tata.descrizione
		from tipi_attivita_tipi_allegati tata
			join tipi_attivita ta on ta.id_tipo_attivita = tata.id_tipo_attivita
			join attivita att on att.id_tipo_attivita = ta.id_tipo_attivita
		where att.id_attivita = ".$id_attivita."
		order by tata.ordine nulls last, tata.id_tipo_documento
	") || SIRTI::Err::db_fatal_error($db);
	
	my $tmpdir=undef;
	
	for my $f(@$files) {
		next unless defined $f;
		# se è un HASH ho dell info aggiuntive da aggiungere
		my @extraInfoCols = ('TITLE', 'DESCRIPTION', 'REVISION', 'REF_DATE', 'DOC_TYPE');
		my $attachInfo = {};
		if (ref ($f) eq 'HASH'){
			unless (defined $f->{FILENAME}){
				push @errors,"per gli allegati con extraInfo è obbligatoria la chiave filename";
				last;
			}
			
			if (scalar @$tata){
				if (!exists $f->{DOC_TYPE} || ! defined $f->{DOC_TYPE}){
					push @errors,"DOC_TYPE obbligatorio per il tipo attivita";
					last;
				}
				
				unless (grep {$_->{ID_TIPO_DOCUMENTO} eq $f->{DOC_TYPE}} @$tata){
					push @errors,"tipo documento ".$f->{DOC_TYPE}." non supportato per il tipo attivita";
					last;
				}
				
			} else {
				if (exists $f->{DOC_TYPE} && defined $f->{DOC_TYPE}){
					push @errors,"tipi documenti non definiti per il tipo attivita" unless scalar @$tata;
					last;
				}
			}
			
			for (@extraInfoCols){
				$attachInfo->{$_} = defined $f->{$_} ? $f->{$_} : undef;
			}
			# verifico l'esistenza della chiave META
			if (defined $f->{META}){
				if (ref($f->{META}) ne 'HASH'){
					push @errors,"la chiave META deve essere un HASH";
					last;
				}
				# verifico che il valore delle chiavi siano tutte stringe
				for my $key (keys %{$f->{META}}){
					if (ref($f->{META}->{$key})){
						push @errors,"la chiave META.".$key." deve essere una stringa";
						last;
					}
				}
				$attachInfo->{META} = $f->{META};
			}
			$f = $f->{FILENAME};
		} else {
			if (scalar @$tata){
				push @errors,"DOC_TYPE obbligatorio per il tipo attivita";
				last;
			} else {
				for (@extraInfoCols){
					$attachInfo->{$_} = undef;
				}
			}
		}
		
		my $path=$f;
		if (length(ref($f)) != 0)  { #$f e' un oggetto - viene copiato in un file temporaneo
			unless (defined($tmpdir)) {
				$tmpdir=$params{TMP};
				if (defined $tmpdir && ! -d $tmpdir) {
					push  @errors,"$tmpdir: non e' una directory";
					$tmpdir=undef;
					last;
				}
				unless (defined $tmpdir) {
					$tmpdir=$db->fetch_minimalized("select valore from config_art where upper(chiave)='ART.TMPDIR' and rownum=1");
					if (SIRTI::DB::iserror()) {
						push @errors,SIRTI::DB::get_errormessage();
						$tmpdir=undef;
						last;
					}
					$tmpdir=undef if defined $tmpdir && ! -d $tmpdir;
				}
				unless (defined $tmpdir) {
					for my $t ( $ENV{TMP},$ENV{TMPDIR},'/tmp') {
						$tmpdir=$t;
						$tmpdir=undef if defined $tmpdir && ! -d $tmpdir;
						last if defined $tmpdir;
					}
				}
				unless (defined $tmpdir) {
					push @errors,"non si e' riuscito a trovare una directory per i file temporanei";
					last;
				}
				
				$tmpdir.='/'.$id_attivita.'_'.$action_date;
				unless (mkdir $tmpdir) {
					push @errors,"$tmpdir: errore creazione directory " unless mkdir $tmpdir;
					$tmpdir=undef;
					last;
				}
			}
			#my $fh=undef;
			my $t = basename("$f"); #converte "$f" in nome file
			$t=~s/^.*\\//g; #fa il  basename di un eventuale pathname windows
			$path=$tmpdir.'/'.$t;
			print STDERR "----> e' un handle: $path\n";
			if (open(my $fh, '>',$path)) {
				while(<$f>) {
					unless (print $fh $_) {
						push @errors,"$path: $!";
						close $fh;
						last;
					}
				}
				close $fh;
				$fh=undef;
			}
			else {
				push @errors,"$path: $!";
				last;
			}
		}
		unless (-r "$path") {
			push @errors,"$path: non leggibile";
			next;
		}
		my ($dir,$base)=(dirname($path),basename($path));
		my $out_file_name =	  $id_attivita
							. '-'
							. substr($action_date, 0, 12)
							. '-'
							. sprintf("%04d", $progressivo++)
							. '.tar.bz2';
		my $full_outfile=$outdir.'/'.$out_file_name;
		my $cmd = "tar -cp  -C '$dir' -- \"$base\" | bzip2 -9 > '$full_outfile'";
		$cmd.=';let "e=${PIPESTATUS[0]} + ${PIPESTATUS[1]}"; [ $e -eq 0 ] && exit 0 || exit 1';
		system($cmd);
		if ($?) {
			push @errors,"$full_outfile: errore creazione";
			last;
		}
		$attachInfo->{OUTFILE} = $out_file_name;
		$attachInfo->{INFILE} = $base;
		$attachInfo->{DIMENSIONE} = -s $path;
		push @elab,$attachInfo;
	}
	
	if (scalar(@errors) > 0) {
		system("/bin/rm -rf '$tmpdir'") if defined $tmpdir;
		my %uniq=();
		map {  $uniq{$_}=1;  } @errors;
		return join("\n",keys(%uniq));
	}
	
	for my $h(@elab) {
		# stacco la sequence nonostante il default perchè mi è necessario per l'eventuale gestione delle meta info
		my $id_allegato = $db->fetch_minimalized("select seq_allegati_azione.nextval from dual");
		push @errors,SIRTI::DB::get_errormessage() if SIRTI::DB::iserror();
		
		$insert_prep->do($h->{OUTFILE},$h->{INFILE},$h->{DIMENSIONE},$h->{TITLE},$h->{DESCRIPTION},$h->{REVISION},$h->{REF_DATE},$h->{DOC_TYPE},$id_allegato);
		push @errors,SIRTI::DB::get_errormessage() if SIRTI::DB::iserror();
		
		# gestisco gli eventuali meta
		if (exists $h->{META}){
			for my $m (keys %{$h->{META}}){
				$insert_meta_prep->do($id_allegato, $m, $h->{META}->{$m});
				push @errors,SIRTI::DB::get_errormessage() if SIRTI::DB::iserror();
			}
		}
	}

	$delete_prep->do() if scalar(@errors) > 0;  #per chi usa l' autocommit
	
	return scalar(@errors) > 0 ? join("\n",@errors) : undef;
}


#parametri:
#	DATE_FORMAT  	=> imposta il formato data - default YYYYMMDDHH24MISS
#	REVERSE_ORDER 	=> gli eventi sono ordinati temporalmente discendente(default ascendente)
#	NO_DTA			=> non carica i dati tecnici
#	il metodo ritorna uno scalare in caso di errore altrimenti un puntatore ad array
#

sub get_history {
	my ($self,$id_attivita,%params)=@_;
	return '1^ argomento ID_ATTIVITA non specificato' unless defined $id_attivita;
	return 'connessione al database non impostata' unless $self->{DB};
	my $date_format=SIRTI::Err::nvl($params{DATE_FORMAT},'YYYYMMDDHH24MISS');
	my $sql="
		select
			--st.id_attivita
			to_char(st.data_esecuzione,'$date_format') data_esecuzione
			,st.id_operatore
			,op.login_operatore login_operatore
			,st.id_action  id_azione
			,ac.nome   nome_azione
			,st.id_stato_risultante
			,s.nome nome_stato_risultante
			,st.descr_azione	descrizione
		from
			storia_attivita st
			left join operatori op on st.id_operatore=op.id_operatore
			left join action   ac on st.id_action = ac.id_action
			left join stati    s  on st.id_stato_risultante = s.id_stato
		where
			id_attivita=:ID_ATTIVITA
		order by
	";

	$sql.= $params{REVERSE_ORDER}
		? " id_attivita desc ,data_esecuzione desc "
		: " id_attivita,data_esecuzione"
	;

	my $p=$self->{DB}->create_prepare($sql);
	$p->get_sth->bind_param(':ID_ATTIVITA',$id_attivita);
	$p->get_sth->execute;
	my $cur=$p->create_cursor;
	my @history=();
	while(my $r=$cur->fetchrow_hashref) { push @history,$r; }
	$cur->finish;
	$p->finish;
	if ($params{REVERSE_ORDER}) {
		for my $i(0..scalar(@history) - 2) {
			$history[$i]->{ID_STATO_INIZIALE}=$history[$i + 1]->{ID_STATO_RISULTANTE};
			$history[$i]->{NOME_STATO_INIZIALE}=$history[$i + 1]->{NOME_STATO_RISULTANTE};
		}
	}
	else {
		for my $i(0..scalar(@history) - 2) {
			$history[$i + 1]->{ID_STATO_INIZIALE}=$history[$i]->{ID_STATO_RISULTANTE};
			$history[$i + 1]->{NOME_STATO_INIZIALE}=$history[$i]->{NOME_STATO_RISULTANTE};
		}
	}
	return \@history if $params{NO_DTA};
	
	$sql="
		select
			x.n   n
			--,d.id_tipo_dato_tecnico_attivita  id_tdta
			,t.descrizione                  nome_tdta
			--,d.oper_ultima_var              id_operatore
			--,op.login_operatore             login_operatore
			--,to_char(d.data_ultima_var,'YYYYMMDDHH24MISS')     data_ultimo_agg
			,d.descrizione					valore
		from storia_attivita st
			left join dati_tecnici_attivita d on st.id_attivita = d.id_attivita and st.data_esecuzione=d.data_esecuzione
			left join tipi_dati_tecnici_attivita t on d.id_tipo_dato_tecnico_attivita=t.id_tipo_dato_tecnico_attivita
			left join operatori op on d.oper_ultima_var=op.id_operatore
			join (
					select
						rownum - 1  n
						,id_attivita
						,data_esecuzione
					from
						storia_attivita st
					where
						st.id_attivita=:ID_ATTIVITA
					order by "
					.(	 $params{REVERSE_ORDER}
							? "st.id_attivita desc,st.data_esecuzione desc"
							: "st.id_attivita,st.data_esecuzione "
					)."
			) x on st.id_attivita=x.id_attivita and st.data_esecuzione = x.data_esecuzione

		where
			st.id_attivita=:ID_ATTIVITA
			order by
		".(
			$params{REVERSE_ORDER}
				?  "st.id_attivita desc ,st.data_esecuzione desc,d.id_tipo_dato_tecnico_attivita desc"
				:  "st.id_attivita,st.data_esecuzione,d.id_tipo_dato_tecnico_attivita"
		);
	$p=$self->{DB}->create_prepare($sql);
	$p->get_sth->bind_param(':ID_ATTIVITA',$id_attivita);
	$p->get_sth->execute;
	$cur=$p->create_cursor;
	while(my $r=$cur->fetchrow_hashref) {
		next unless defined $r->{NOME_TDTA};
		$history[$r->{N}]->{DTA}->{$r->{NOME_TDTA}}=$r->{VALORE};
	}
	$cur->finish;
	$p->finish;
	return \@history;
}

1;
