################################################################################
#
# Modulo di lookup per la trascodifica NOME <--> ID degli elementi di A.R.T.
#
################################################################################

	package SIRTI::ART::Lock::Activity;

	use strict;
	use warnings;
	use Carp 'verbose';
	use SIRTI::Err;
	use SIRTI::ART::Lookup::ART;


################################################################################
#  M E T O D I   P R I V A T I
################################################################################

	sub _get_activity_data {
		my $self = shift;
		print STDERR "-- SIRTI::ART::Activity::_get_activity_data()", "\n" if $self->{DEBUG};
		$self->{LOCK_DATA} = undef;
		my $r = $self->{DB}->fetchall_hashref(	"
            select   to_char(a.DATA_DI_LOCK, 'YYYY-MM-DD HH24:MI:SS')
                    ,case 
                        when ((sysdate - a.DATA_DI_LOCK) * 1440) <= $self->{TIMEOUT} then
                            1
                        else
                            0
                     end LOCKED
                    ,a.OPERATORE_DI_LOCK
                    ,o.NOME_OPERATORE || ' ' || o.COGNOME_OPERATORE NOME_OPERATORE_DI_LOCK
                    ,a.SESSIONE_DI_LOCK
            from     attivita a
                    ,operatori o
            where   a.ID_ATTIVITA = $self->{ACTIVITY_ID}
                    and o.ID_OPERATORE = a.OPERATORE_DI_LOCK
        ") || SIRTI::Err::db_fatal_error($self->{DB}, "Attivita' $self->{ACTIVITY_ID} non trovata!");
		$self->{LOCK_DATA} = $r->[0];
	}


################################################################################
#  M E T O D I   P U B L I C I
################################################################################

	#
	# Costruttore
	#
	sub new($$) {
		my $this = shift;
		my $class = ref($this) || $this;
		warn "classe deprecata - usa ART::Lock::Activities";
		my %self = @_;
		SIRTI::Err::prec(  exists($self{DB}) && exists($self{ACTIVITY_ID}) && exists($self{USER_ID}) && exists($self{SESSION_ID}) 
						,"USAGE: $class->new( 
                                                DB          => SIRTI::DB->new(CS), 
                                                ACTIVITY_ID => id_attivita,
                                                USER_ID     => id_operatore,
                                                SESSION_ID  => apache_session_id,
                                                [LOOKUP_ART => minuti]
                                                [TIMEOUT    => minuti]
                                                [DEBUG      => 1|0]
                                            )
                        ");
		SIRTI::Err::prec(  ref($self{DB}) eq 'SIRTI::DB', "L'oggetto DB deve essere un'istanza di SIRTI::DB\n");
		$self{LOOKUP_ART} = SIRTI::ART::Lookup::ART->new($self{DB}) unless defined $self{LOOKUP_ART};
		$self{USER_NAME} = $self{LOOKUP_ART}->nome_operatore($self{USER_ID});
		unless ( exists($self{TIMEOUT}) ) {
			$self{TIMEOUT} = $self{DB}->fetch_minimalized("select valore from config_art where chiave = 'ART.ATTIVITA_TIMEOUT_LOCK'") || SIRTI::Err::db_fatal_error($self{DB}, "Errore leggendo ART_CONFIG.");
		}
		return bless \%self, $class;
	}

	#
	# Ritorna un riferimento all'istanza db in uso
	#
	sub get_db($) {
		my $self=shift;
		print STDERR "-- SIRTI::ART::Activity::get_db()", "\n" if $self->{DEBUG};
		return $self->{DB};
	}

	sub is_locked {
		my $self = shift;
		print STDERR "-- SIRTI::ART::Activity::is_locked()", "\n" if $self->{DEBUG};
		$self->_get_activity_data();
		return	$self->{LOCK_DATA}->{LOCKED};
	}

	sub is_locked_by_myself {
		my $self = shift;
		print STDERR "-- SIRTI::ART::Activity::is_locked_by_myself()", "\n" if $self->{DEBUG};
		return	$self->is_locked()
				&& ($self->{LOCK_DATA}->{OPERATORE_DI_LOCK} eq $self->{USER_ID}) 
				&& ($self->{LOCK_DATA}->{SESSIONE_DI_LOCK} eq $self->{SESSION_ID});
	}

	sub get_lock_data {
		my $self = shift;
		print STDERR "-- SIRTI::ART::Activity::get_lock_data()", "\n" if $self->{DEBUG};
		$self->_get_activity_data();
		return $self->{LOCK_DATA};
	}

	sub can_lock {
		my $self = shift;
		print STDERR "-- SIRTI::ART::Activity::can_lock()", "\n" if $self->{DEBUG};
		return	!$self->is_locked() || $self->is_locked_by_myself();
	}
	
	sub lock {
		my $self = shift;
		print STDERR "-- SIRTI::ART::Activity::lock()", "\n" if $self->{DEBUG};
		if ( $self->can_lock() ) {
			$self->{DB}->do("
                                update  attivita
                                set      data_di_lock = sysdate
                                        ,operatore_di_lock = $self->{USER_ID}
                                        ,sessione_di_lock = ${\$self->{DB}->quote($self->{SESSION_ID})}
                                where   id_attivita = $self->{ACTIVITY_ID}
                            ") || SIRTI::Err::db_fatal_error($self->{DB}, "Errore impostando il lock.");
			return 1;
		} else {
			return 0;
		}
	}

	sub can_unlock {
		my $self = shift;
		print STDERR "-- SIRTI::ART::Activity::can_unlock()", "\n" if $self->{DEBUG};
		return	$self->is_locked_by_myself();
	}
	
	sub unlock {
		my $self = shift;
		print STDERR "-- SIRTI::ART::Activity::unlock()", "\n" if $self->{DEBUG};
		if ( $self->can_lock() ) {
			$self->{DB}->do("
                                update  attivita 
                                set      data_di_lock = null
                                        ,operatore_di_lock = null
                                        ,sessione_di_lock = null
                                where   id_attivita = $self->{ACTIVITY_ID}
                                        and sessione_di_lock = ${\$self->{DB}->quote($self->{SESSION_ID})}
                            ") || SIRTI::Err::db_fatal_error($self->{DB}, "Errore rimuovendo il lock.");
			return 1;
		} else {
			return 0;
		}
	}

###################################
#
# Testing the module...
#
###################################
if (__FILE__ eq $0) {

	####  test ###  
	package main;

	use strict;
	use warnings;
	use Carp 'verbose';
	use Getopt::Std;
	use Data::Dumper;


	use SIRTI::DB;
	use SIRTI::Err;

	my %Opt=();
	getopts ('c:v',\%Opt) or die "specifica l' opzione -c <oracleuser> ";

	my $db  = new SIRTI::DB($Opt{c}, {DEBUG =>0}) ||  SIRTI::Err::db_fatal_error();
	my $lua = new SIRTI::ART::Lookup::ART($db);

	#my $user = 'ROOT'; 
	my $user = 'rpulito';
	my $lock = SIRTI::ART::Lock::Activity->new( DB=>$db , USER_ID=>$lua->id_operatore($user), ACTIVITY_ID=>292303 , SESSION_ID=>'03fdb07685aa6a966b970b015765eabf' );
	print "L'attività " . ($lock->is_locked() ? '' : 'NON ') . "è lockata", "\n";
	print "L'attività " . ($lock->is_locked_by_myself() ? '' : 'NON ') . "è lockata da me stesso.", "\n";
	print "L'attività " . ($lock->can_lock() ? '' : 'NON ') . "può essere lockata.", "\n";

	print "L'attività " . ($lock->can_lock() && $lock->lock() ? '' : 'NON ') . "è stata lockata.", "\n";
	print "L'attività " . ($lock->can_unlock() && $lock->unlock() ? '' : 'NON ') . "è stata unlockata.", "\n";
	
	#print "Lock data: ", Dumper($lock->get_lock_data());
	#print STDOUT Dumper($lock);

	$db->commit();

	exit 0;

} else {

	1;

}
