################################################################################
#
# Modulo di lookup per la trascodifica NOME <--> ID degli elementi di A.R.T.
#
################################################################################

	package SIRTI::ART::Lookup::ART;

	use strict;
	use warnings;
	use List::Util qw(first);
	
	use SIRTI::Err;


################################################################################
#  M E T O D I   P R I V A T I
################################################################################

	sub _get_id($$$) {
		my ($self, $section, $key) = @_;
		return $self->{$section.'_NAME'}->{$key};
	}

	sub _get_name($$$) {
		my ($self, $section, $key) = @_;
		return $self->{$section.'_ID'}->{$key};
	}
	
	sub _get_description($$$) {
		my ($self, $section, $key) = @_;
		return $self->{$section.'_DESCRIPTION'}->{$key};
	}

	sub _get_activity_properties_group($$$) {
		my ($self, $key) = @_;
		return $self->{ACTIVITY_PROPERTIES_GROUP}->{$key};
	}

	sub _get_activity_property_groups() {
		my ($self) = @_;
		return $self->{ACTIVITY_PROPERTY_GROUPS};
	}

	sub _get_system_properties_group($$$) {
		my ($self, $key) = @_;
		return $self->{SYSTEM_PROPERTIES_GROUP}->{$key};
	}

	sub _get_system_property_groups() {
		my ($self) = @_;
		return $self->{SYSTEM_PROPERTY_GROUPS};
	}

################################################################################
#  M E T O D I   P U B L I C I
################################################################################

	#
	# Costruttore
	#
	sub new($$) {
		my $this = shift;
		my $class = ref($this) || $this;
		my %self = ();
		#$self{DB} = shift;
		#SIRTI::Err::prec( defined($self{DB}) , "USAGE: $class->new(\$db)\n");
		my $db = shift;
		SIRTI::Err::prec( defined($db) , "USAGE: $class->new(\$db)\n");

		my ($sql, $rows, $row);
		
		##### Creazione hash di lookup $self{TS} del tipo NAME => ID per i TIPI SISTEMA
		$sql = "select upper(ts.nome_tipo_sistema) name, ts.id_tipo_sistema id from tipi_sistema ts";
		$rows = $db->fetchall($sql) || SIRTI::Err::db_fatal_error($db);
		$self{TS_NAME} = {};
		foreach $row (@{$rows}) {
			$self{TS_NAME}->{$row->[0]} = $row->[1];
		}
		$self{TS_ID} = { reverse %{$self{TS_NAME}} };

		##### Creazione hash di lookup $self{CS} del tipo NAME => ID per le CATEGORIE SISTEMA
		$sql = "select upper(cs.nome_categoria) name, cs.id_categoria_sistema id from categorie_sistema cs";
		$rows = $db->fetchall($sql) || SIRTI::Err::db_fatal_error($db);
		$self{CS_NAME} = {};
		foreach $row (@{$rows}) {
			$self{CS_NAME}->{$row->[0]} = $row->[1];
		}
		$self{CS_ID} = { reverse %{$self{CS_NAME}} };

		##### Creazione hash di lookup $self{KS} del tipo NAME => ID per le CLASSI SISTEMA
		$sql = "select upper(ks.nome_classe) name, ks.id_classe_sistema id from classi_sistema ks";
		$rows = $db->fetchall($sql) || SIRTI::Err::db_fatal_error($db);
		$self{KS_NAME} = {};
		foreach $row (@{$rows}) {
			$self{KS_NAME}->{$row->[0]} = $row->[1];
		}
		$self{KS_ID} = { reverse %{$self{KS_NAME}} };

		##### Creazione hash di lookup $self{TO} del tipo NAME => ID per TIPI OGGETTO
		$sql = "select upper(too.descrizione) name, too.id_tipo_oggetto id from tipi_oggetto too";
		$rows = $db->fetchall($sql) || SIRTI::Err::db_fatal_error($db);
		$self{TO_NAME} = {};
		foreach $row (@{$rows}) {
			$self{TO_NAME}->{$row->[0]} = $row->[1];
		}
		$self{TO_ID} = { reverse %{$self{TO_NAME}} };

		##### Creazione hash di lookup $self{TDTS} del tipo NAME => ID per i TDTS associati al tipo sistema specificata
		$sql = "select tdts.nome, tdts.id_tipo_dato_tecnico, tdts.multiplex_value, case when tdts.tipo = 'CUREUR' then 'CURRENCY' else tdts.tipo end tipo  from tipi_dati_tecnici tdts where tdts.morto is null";
		$rows = $db->fetchall($sql) || SIRTI::Err::db_fatal_error($db);
		$self{TDTS_NAME} = {};
		$self{TDTS_MULTIPLEX_FOR_NAME} = {};
		$self{TDTS_MULTIPLEX_FOR_ID} = {};
		$self{TDTS_TIPO_FOR_NAME} = {};
		$self{TDTS_TIPO_FOR_ID} = {};
		foreach $row (@{$rows}) {
			$self{TDTS_NAME}->{$row->[0]} = $row->[1];
			$self{TDTS_MULTIPLEX_FOR_ID}->{$row->[1]} = $self{TDTS_MULTIPLEX_FOR_NAME}->{$row->[0]} = $row->[2] eq 'Y' ? 1 : 0;
			$self{TDTA_TIPO_FOR_ID}->{$row->[1]} = $self{TDTS_TIPO_FOR_NAME}->{$row->[0]} = $row->[3];
		}
		$self{TDTS_ID} = { reverse %{$self{TDTS_NAME}} };

		##### Creazione hash di lookup $self{GRUPPI} del tipo NAME => ID per i GRUPPI
        $sql = "select upper(g.nome) name, g.id_gruppo id from gruppi g where g.morto is null";
		$rows = $db->fetchall($sql) || SIRTI::Err::db_fatal_error($db);
		$self{GRUPPI_NAME} = {};
		foreach $row (@{$rows}) {
			$self{GRUPPI_NAME}->{$row->[0]} = $row->[1];
		}
		$self{GRUPPI_ID} = { reverse %{$self{GRUPPI_NAME}} };
		
		##### Creazione hash di lookup $self{RUOLI} del tipo NAME => ID per i RUOLI
		$sql = "select nvl((select count (1) From user_tables where table_name in ('RUOLI')),0) from dual";
		$rows = $db->fetch_minimalized($sql);
		
		SIRTI::Err::db_fatal_error($db) unless defined $rows;
		
		if ($rows == 1){
	        $sql = "select upper(r.nome) name, r.id_ruolo id from ruoli r where r.data_disabilitazione is null";
			$rows = $db->fetchall($sql) || SIRTI::Err::db_fatal_error($db);
			$self{RUOLI_NAME} = {};
			foreach $row (@{$rows}) {
				$self{RUOLI_NAME}->{$row->[0]} = $row->[1];
			}
			$self{RUOLI_ID} = { reverse %{$self{RUOLI_NAME}} };
		}

		##### Creazione hash di lookup $self{OPERATORI} del tipo NAME => ID per gli OPERATORI
		$sql = "select upper(o.login_operatore) name, o.id_operatore id from operatori o";
		$rows = $db->fetchall($sql) || SIRTI::Err::db_fatal_error($db);
		$self{OPERATORI_NAME} = {};
		foreach $row (@{$rows}) {
			$self{OPERATORI_NAME}->{$row->[0]} = $row->[1];
		}
		$self{OPERATORI_ID} = { reverse %{$self{OPERATORI_NAME}} };

		##### Creazione hash di lookup $self{TA} del tipo NAME => ID per i TIPI_ATTIVITA
		$sql = "
			select tata.id
				, tata.id_tipo_attivita
				, tata.id_tipo_documento
				, tata.descrizione
				, tata.id_tipo_dato_tecnico_attivita id_tipo_dato_tecnico_att
				, tdta.descrizione nome_tipo_dato_tecnico_att
			from tipi_attivita_tipi_allegati tata
				left join tipi_dati_tecnici_attivita tdta on tdta.id_tipo_Dato_Tecnico_attivita = tata.id_tipo_dato_tecnico_Attivita
			order by tata.id_tipo_attivita, tata.ordine nulls last, tdta.descrizione
		";
		my $res_activity_type_document_type = $db->fetchall_hashref($sql) || SIRTI::Err::db_fatal_error($db);
		my $activity_type_document_type = {};
		for my $r (@{$res_activity_type_document_type}){
			$activity_type_document_type->{$r->{ID_TIPO_ATTIVITA}} = [] unless exists $activity_type_document_type->{$r->{ID_TIPO_ATTIVITA}};
			push @{$activity_type_document_type->{$r->{ID_TIPO_ATTIVITA}}}, $r;
			delete $r->{ID_TIPO_ATTIVITA};
		}
		$sql = "
			select upper(ta.nome_tipo_attivita) name
				, ta.id_tipo_attivita
				, ta.descrizione
				, case when (select sum(1) from user_objects v where object_name in ('V_XT_'||ta.id_tipo_attivita,'INTESTAZIONI_DASHBOARDS')) = 2 then 1 else 0 end HAS_DASHBOARD
				, ui_route
			from tipi_attivita ta
		";
		$rows = $db->fetchall($sql) || SIRTI::Err::db_fatal_error($db);
		
		$self{TA_NAME} = {};
		$self{TA_DESCRIPTION} = {};
		$self{TA_HAS_DASHBOARD} = {};
		$self{TA_HAS_REPORT} = {};
		$self{TA_UI_ROUTE} = {};
		$self{TA_DOCUMENT_TYPES} = {};
		foreach $row (@{$rows}) {
			$self{TA_NAME}->{$row->[0]} = $row->[1];
			$self{TA_DESCRIPTION}->{$row->[1]} = $row->[2];
			# se TA_HAS_DASHBOARD è true significa che ho le XT e quindi ho anche il report standar per il tipo attivita
			$self{TA_HAS_DASHBOARD}->{$row->[1]} = $self{TA_HAS_REPORT}->{$row->[1]} = $row->[3];
			$self{TA_UI_ROUTE}->{$row->[1]} = $row->[4];
			$self{TA_DOCUMENT_TYPES}->{$row->[1]} = $activity_type_document_type->{$row->[1]} if exists $activity_type_document_type->{$row->[1]};
		}
		$self{TA_ID} = { reverse %{$self{TA_NAME}} };

		##### Creazione hash di lookup per permessi su update ACTIVITY_PROPERTY dato tipo_attivita
		$sql = "
			select id_tipo_attivita
				, id_gruppo
			from TIPI_ATTIVITA_AP_GRUPPI_PERM
		";
		$rows = $db->fetchall_hashref($sql) || SIRTI::Err::db_fatal_error($db);
		$self{TA_AP_GROUPS_PERM} = {};
		foreach $row (@{$rows}) {
			if (exists $self{TA_AP_GROUPS_PERM}->{$row->{ID_TIPO_ATTIVITA}}){
				push @{$self{TA_AP_GROUPS_PERM}->{$row->{ID_TIPO_ATTIVITA}}}, $row->{ID_GRUPPO}
			} else {
				$self{TA_AP_GROUPS_PERM}->{$row->{ID_TIPO_ATTIVITA}} = [$row->{ID_GRUPPO}];
			}
		}

		##### Creazione hash di lookup $self{STATI_NAME} del tipo NAME => ID per gli STATI
		$sql = "select upper(s.nome) name, s.id_stato id from stati s where s.morto is null";
		$rows = $db->fetchall($sql) || SIRTI::Err::db_fatal_error($db);
		$self{STATI_NAME} = {};
		foreach $row (@{$rows}) {
			$self{STATI_NAME}->{$row->[0]} = $row->[1];
		}
		$self{STATI_ID} = { reverse %{$self{STATI_NAME}} };
		
		##### Lookup STATI
		$sql = "select upper(s.nome) name, s.id_stato id, s.descrizione description, s.flag_stato_partenza STARTING_FLAG, s.interfaccia ui from stati s where s.morto is null";
		$rows = $db->fetchall_hashref($sql) || SIRTI::Err::db_fatal_error($db);
		$self{STATI} = {};
		foreach $row (@{$rows}) {
			$self{STATI}->{$row->{ID}} = $row;
			# elimino in quanto chiave principale
			delete $self{STATI}->{$row->{ID}}->{ID};
		}
		
		#verifico se nella tabella action è presente la colonna interfaccia
		my $interfaccia = $db->fetch_minimalized("
			select nvl2((
				select 1 from USER_TAB_COLS
				where table_name = 'ACTION'
				and column_name = 'INTERFACCIA'),'interfaccia','null') field
			from dual
		");
		
		##### Creazione hash di lookup $self{ACTION} del tipo NAME => ID per le ACTION associate all'attivita specificata
		$sql = "select upper(a.nome) name, a.id_action, a.flag_action_partenza STARTING_FLAG, descrizione description, ".$interfaccia." ui from action a where a.morto is null";
		$rows = $db->fetchall_hashref($sql) || SIRTI::Err::db_fatal_error($db);
		$self{ACTION} = {};
		$self{ACTION_NAME} = {};
		$self{ACTION_ASSIGNABLE_FOR_NAME} = {};
		$self{ACTION_ASSIGNABLE_FOR_ID} = {};
		foreach $row (@{$rows}) {
			$self{ACTION_NAME}->{$row->{NAME}} = $row->{ID_ACTION};
			$self{ACTION_ASSIGNABLE_FOR_ID}->{$row->{ID_ACTION}} = $self{ACTION_ASSIGNABLE_FOR_NAME}->{$row->{NAME}} = defined $row->{STARTING_FLAG} && $row->{STARTING_FLAG} =~/^(P|G)$/ ? 1 : 0;
			$self{ACTION_DESCRIPTION}->{$row->{NAME}} = $row->{DESCRIPTION};
			$self{ACTION}->{$row->{ID_ACTION}} = $row;
			# elimino in quanto chiave principale
			delete $self{ACTION}->{$row->{ID_ACTION}}->{ID_ACTION};
		}
		$self{ACTION_ID} = { reverse %{$self{ACTION_NAME}} };

		##### Creazione hash di lookup $self{TDTA} del tipo NAME => ID per i TDTA associati all'attivita specificata
		$sql = "
			select tdta.descrizione
				, tdta.id_tipo_dato_tecnico_attivita
				,tdta.valore_default
				, case when rtrim(tdta.tipo_ui) = 'CUREUR' then 'CURRENCY' else rtrim(tdta.tipo_ui) end tipo_ui
				,tdta.valori
				,nvl(tdta.etichetta, tdta.descrizione) etichetta
				,tdta.suggerimento
				,tdta.valore_predefinito
				,case when rtrim(tdta.tipo_ui) = 'CUREUR' then 'EUR' else null end valuta
				,case when rtrim(tdta.tipo_ui) = 'CUREUR' then 2 else null end decimali
			from tipi_dati_tecnici_attivita tdta where tdta.morto is null";
		# FIXME prevedere separatore migliaia per number
		$rows = $db->fetchall($sql) || SIRTI::Err::db_fatal_error($db);
		$self{TDTA_NAME} = {};
		$self{TDTA_PREDEFINED_VALUE_VALUES_FOR_NAME} = {};
		$self{TDTA_PREDEFINED_VALUE_VALUES_FOR_ID} = {};
		$self{TDTA_DEFAULT_VALUES_FOR_NAME} = {};
		$self{TDTA_DEFAULT_VALUES_FOR_ID} = {};
		$self{TDTA_TIPO_UI_FOR_NAME} = {};
		$self{TDTA_TIPO_UI_FOR_ID} = {};
		$self{TDTA_VALORI_FOR_NAME} = {};
		$self{TDTA_VALORI_FOR_ID} = {};
		foreach $row (@{$rows}) {
			$self{TDTA_NAME}->{$row->[0]} = $row->[1];
			$self{TDTA_DEFAULT_VALUES_FOR_ID}->{$row->[1]} = $self{TDTA_DEFAULT_VALUES_FOR_NAME}->{$row->[0]} = $row->[2];
			$self{TDTA_TIPO_UI_FOR_ID}->{$row->[1]} = $self{TDTA_TIPO_UI_FOR_NAME}->{$row->[0]} = $row->[3];
			$self{TDTA_VALORI_FOR_ID}->{$row->[1]} = $self{TDTA_VALORI_FOR_NAME}->{$row->[0]} = $row->[4];
			$self{TDTA_ETICHETTA_FOR_ID}->{$row->[1]} = $self{TDTA_ETICHETTA_FOR_NAME}->{$row->[0]} = $row->[5];
			$self{TDTA_SUGGERIMENTO_FOR_ID}->{$row->[1]} = $self{TDTA_SUGGERIMENTO_FOR_NAME}->{$row->[0]} = $row->[6];
			$self{TDTA_PREDEFINED_VALUE_VALUES_FOR_ID}->{$row->[1]} = $self{TDTA_PREDEFINED_VALUE_VALUES_FOR_NAME}->{$row->[0]} = $row->[7];
			$self{TDTA_VALUTA_FOR_ID}->{$row->[1]} = $self{TDTA_VALUTA_FOR_NAME}->{$row->[0]} = $row->[8];
			$self{TDTA_DECIMALI_FOR_ID}->{$row->[1]} = $self{TDTA_DECIMALI_FOR_NAME}->{$row->[0]} = $row->[9];
		}
		$self{TDTA_ID} = { reverse %{$self{TDTA_NAME}} };
		
		### Recupero activity_properties_group
		
		$sql = "select nvl((select count (1) From user_tables where table_name in ('AP','AP_GROUPS','AT_AP_GROUPS')),0) from dual";
		$rows = $db->fetch_minimalized($sql);
		
		SIRTI::Err::db_fatal_error($db) unless defined $rows;
		
		$self{ACTIVITY_PROPERTIES_GROUP} = {};
		
		if ($rows == 3){
			$sql = "
				select ta.nome_Tipo_attivita ACTIVITY_TYPE_NAME
				  , ag.descrizione GROUP_NAME
				  , AG.ID GROUP_ID
				  , TDTA.DESCRIZIONE PROPERTY_NAME
				  , nvl(AP.SOLO_LETTURA,'N') READ_ONLY
				  , case
				      when exists (select 1 from TIPI_DATI_TECNICI_ATT_ACTION tdtaa where tdtaa.id_tipo_dato_tecnico_attivita = ap.id_tipo_dato_tecnico_attivita and tdtaa.id_tipo_attivita = ap.id_tipo_Attivita and rownum<2)
				      then 'N'
				    else 'Y'
				    end EXPIRED
				  , nvl(AP.ANNULLABILE,'N') NULLABLE
				From ap ap
				  join tipi_attivita ta on ta.id_tipo_attivita = ap.id_tipo_attivita
				  join tipi_dati_tecnici_attivita tdta on tdta.id_tipo_dato_Tecnico_attivita = ap.id_tipo_dato_tecnico_attivita
				  left join ap_groups ag on ag.id = AP.ID_RAGGRUPPAMENTO
				  left join at_ap_groups aag on AAG.ID_RAGGRUPPAMENTO = ag.id and AAG.ID_TIPO_ATTIVITA = TA.ID_TIPO_ATTIVITA
				where AP.DISABILITATO is null
				order by ta.nome_Tipo_attivita,AAG.ORDINE asc nulls last, AP.ORDINE_RAGGRUPPAMENTO asc nulls last, AP.ORDINE asc
			";
			$rows = $db->fetchall_hashref($sql) || SIRTI::Err::db_fatal_error($db);
			
			my $tmp = undef;
			for my $row (@{$rows}){
				
				$tmp->{$row->{ACTIVITY_TYPE_NAME}} = exists $tmp->{$row->{ACTIVITY_TYPE_NAME}} ? $tmp->{$row->{ACTIVITY_TYPE_NAME}} : [];
				
				my @nums = @{$tmp->{$row->{ACTIVITY_TYPE_NAME}}};			
				
				my $index;
				
				if (defined $row->{GROUP_NAME}){
					$index = first { $nums[$_]->{GROUP} eq $row->{GROUP_NAME} } 0..$#nums;
				} else {
					$index = first { ! exists $nums[$_]->{GROUP}} 0..$#nums;
				}
				
				my $props = {
					NAME => $row->{PROPERTY_NAME}
					, EXPIRED => $row->{EXPIRED}
					, READ_ONLY => $row->{READ_ONLY}
					, NULLABLE => $row->{NULLABLE}
				};
				
				unless (defined $index){
					my $tmp_group = {
						PROPERTIES => [$props]
					};
					
					if ($row->{GROUP_NAME}){
						$tmp_group->{GROUP} = $row->{GROUP_NAME};
						$tmp_group->{ID} = $row->{GROUP_ID};
					}
					push @{$tmp->{$row->{ACTIVITY_TYPE_NAME}}}, $tmp_group
				} else {
					push @{$tmp->{$row->{ACTIVITY_TYPE_NAME}}->[$index]->{PROPERTIES}}, $props;
				}
			}
			
			$self{ACTIVITY_PROPERTIES_GROUP} = $tmp;

		}
		
		### Recupero activity_property_groups
		
		$sql = "select nvl((select count (1) From user_tables where table_name in ('AP_GROUPS')),0) from dual";
		$rows = $db->fetch_minimalized($sql);
		
		SIRTI::Err::db_fatal_error($db) unless defined $rows;
		
		$self{ACTIVITY_PROPERTY_GROUPS} = [];
		
		if ($rows == 1){
			$sql = "
				select ag.id, ag.descrizione GROUP_NAME
				From ap_groups ag
				where ag.DISABILITATO is null
				order by ag.descrizione asc
			";
			$rows = $db->fetchall_arrayref($sql) || SIRTI::Err::db_fatal_error($db);
			use Data::Dumper;
			$self{ACTIVITY_PROPERTY_GROUPS} = [ map { { ID => $_->[0], NAME => $_->[1] } } @{$rows} ];
		}

		### Recupero system_properties_group
		
		$sql = "select nvl((select count (1) From user_tables where table_name in ('SP','SP_GROUPS','ST_SP_GROUPS')),0) from dual";
		$rows = $db->fetch_minimalized($sql);
		
		SIRTI::Err::db_fatal_error($db) unless defined $rows;
		
		$self{SYSTEM_PROPERTIES_GROUP} = {};
		
		if ($rows == 3){
			$sql = "
				select ts.nome_Tipo_sistema SYSTEM_TYPE_NAME
				  , sg.descrizione GROUP_NAME
				  , sG.ID GROUP_ID
				  , TDT.nome PROPERTY_NAME
				  , nvl(sP.SOLO_LETTURA,'N') READ_ONLY
				  , 'N' EXPIRED -- per ora non ha senso la scadenza
				  , nvl(SP.ANNULLABILE,'N') NULLABLE
				From sp sp
				  join tipi_sistema ts on ts.id_tipo_sistema = sp.id_tipo_sistema
				  join tipi_dati_tecnici tdt on tdt.id_tipo_dato_Tecnico = sp.id_tipo_dato_tecnico
				  left join sp_groups sg on sg.id = SP.ID_RAGGRUPPAMENTO
				  left join st_sp_groups ssg on ssG.ID_RAGGRUPPAMENTO = sg.id and ssG.ID_TIPO_SISTEMA = TS.ID_TIPO_SISTEMA
				where SP.DISABILITATO is null
				order by ts.nome_Tipo_sistema,SSG.ORDINE asc nulls last, SP.ORDINE_RAGGRUPPAMENTO asc nulls last, SP.ORDINE asc
			";
			$rows = $db->fetchall_hashref($sql) || SIRTI::Err::db_fatal_error($db);
			
			my $tmp = undef;
			for my $row (@{$rows}){
				
				$tmp->{$row->{SYSTEM_TYPE_NAME}} = exists $tmp->{$row->{SYSTEM_TYPE_NAME}} ? $tmp->{$row->{SYSTEM_TYPE_NAME}} : [];
				
				my @nums = @{$tmp->{$row->{SYSTEM_TYPE_NAME}}};			
				
				my $index;
				
				if (defined $row->{GROUP_NAME}){
					$index = first { $nums[$_]->{GROUP} eq $row->{GROUP_NAME} } 0..$#nums;
				} else {
					$index = first { ! exists $nums[$_]->{GROUP}} 0..$#nums;
				}
				
				my $props = {
					NAME => $row->{PROPERTY_NAME}
					, EXPIRED => $row->{EXPIRED}
					, READ_ONLY => $row->{READ_ONLY}
					, NULLABLE => $row->{NULLABLE}
				};
				
				unless (defined $index){
					my $tmp_group = {
						PROPERTIES => [$props]
					};
					
					if ($row->{GROUP_NAME}){
						$tmp_group->{GROUP} = $row->{GROUP_NAME};
						$tmp_group->{ID} = $row->{GROUP_ID};
					}
					push @{$tmp->{$row->{SYSTEM_TYPE_NAME}}}, $tmp_group
				} else {
					push @{$tmp->{$row->{SYSTEM_TYPE_NAME}}->[$index]->{PROPERTIES}}, $props;
				}
			}
			
			$self{SYSTEM_PROPERTIES_GROUP} = $tmp

		}

		### Recupero system_property_groups
		
		$sql = "select nvl((select count (1) From user_tables where table_name in ('SP_GROUPS')),0) from dual";
		$rows = $db->fetch_minimalized($sql);
		
		SIRTI::Err::db_fatal_error($db) unless defined $rows;
		
		$self{SYSTEM_PROPERTY_GROUPS} = [];
		
		if ($rows == 1){
			$sql = "
				select sg.id, sg.descrizione GROUP_NAME
				From sp_groups sg
				where sg.DISABILITATO is null
				order by sg.descrizione asc
			";
			$rows = $db->fetchall_arrayref($sql) || SIRTI::Err::db_fatal_error($db);
			use Data::Dumper;
			$self{SYSTEM_PROPERTY_GROUPS} = [ map { { ID => $_->[0], NAME => $_->[1] } } @{$rows} ];
		}
		
		##### Hash per il recupero dei TDTA per TA/ACTION (che verra' usato come cache)
		$self{TDTA4ACTION} = {};

		return bless \%self, $class;
	}

	#
	# Ritorna un riferimento all'istanza db in uso
	#
	sub get_db($) {
		my $self=shift;
		#return $self->{DB};
		warn "Method SIRTI::ART::Lookup::ART::get_db() deprecated!\n";
		return undef;
	}

	#
	# TIPI_SISTEMA
	#
	sub is_id_tipo_sistema($$) {
		my ($self, $id) = @_;
		return defined $self->_get_name('TS', $id);
	}
	sub is_nome_tipo_sistema($$) {
		my $self = shift;
		my $name = uc(shift);
		return defined $self->_get_id('TS', $name);
	}
	sub id_tipo_sistema($$) {
		my $self = shift;
		my $name = uc(shift);
		SIRTI::Err::prec($self->is_nome_tipo_sistema($name), "Tipo sistema $name non valido!");
		return $self->_get_id('TS', $name);
	}
	sub nome_tipo_sistema($$) {
		my ($self, $id) = @_;
		SIRTI::Err::prec($self->is_id_tipo_sistema($id), "ID tipo sistema $id non valido!");
		return $self->_get_name('TS', $id);
	}

	#
	# CLASSI_SISTEMA
	#
	sub is_id_classe_sistema($$) {
		my ($self, $id) = @_;
		return defined $self->_get_name('KS', $id);
	}
	sub is_nome_classe_sistema($$) {
		my $self = shift;
		my $name = uc(shift);
		return defined $self->_get_id('KS', $name);
	}
	sub id_classe_sistema($$) {
		my $self = shift;
		my $name = uc(shift);
		SIRTI::Err::prec($self->is_nome_classe_sistema($name), "Classe sistema $name non valida!");
		return $self->_get_id('KS', $name);
	}
	sub nome_classe_sistema($$) {
		my ($self, $id) = @_;
		SIRTI::Err::prec($self->is_id_classe_sistema($id), "ID classe sistema $id non valido!");
		return $self->_get_name('KS', $id);
	}

	#
	# CATEGORIE_SISTEMA
	#
	sub is_id_categoria_sistema($$) {
		my ($self, $id) = @_;
		return defined $self->_get_name('CS', $id);
	}
	sub is_nome_categoria_sistema($$) {
		my $self = shift;
		my $name = uc(shift);
		return defined $self->_get_id('CS', $name);
	}
	sub id_categoria_sistema($$) {
		my $self = shift;
		my $name = uc(shift);
		SIRTI::Err::prec($self->is_nome_categoria_sistema($name), "Categoria sistema $name non valida!");
		return $self->_get_id('CS', $name);
	}
	sub nome_categoria_sistema($$) {
		my ($self, $id) = @_;
		SIRTI::Err::prec($self->is_id_categoria_sistema($id), "ID categoria sistema $id non valido!");
		return $self->_get_name('CS', $id);
	}

	#
	# TIPI_OGGETTO
	#
	sub is_id_tipo_oggetto($$) {
		my ($self, $id) = @_;
		return defined $self->_get_name('TO', $id);
	}
	sub is_nome_tipo_oggetto($$) {
		my $self = shift;
		my $name = uc(shift);
		return defined $self->_get_id('TO', $name);
	}
	sub id_tipo_oggetto($$) {
		my $self = shift;
		my $name = uc(shift);
		SIRTI::Err::prec($self->is_nome_tipo_oggetto($name), "Tipo oggetto $name non valido!");
		return $self->_get_id('TO', $name);
	}
	sub nome_tipo_oggetto($$) {
		my ($self, $id) = @_;
		SIRTI::Err::prec($self->is_id_tipo_oggetto($id), "ID tipo oggetto $id non valido!");
		return $self->_get_name('TO', $id);
	}

	#
	# TDTS
	#
	sub is_id_tdts($$) {
		my ($self, $id) = @_;
		return defined $self->_get_name('TDTS', $id);
	}
	sub is_nome_tdts($$) {
		my $self = shift;
		my $name = shift;
		return defined $self->_get_id('TDTS', $name);
	}
	sub id_tdts($$) {
		my $self = shift;
		my $name = shift;
		SIRTI::Err::prec($self->is_nome_tdts($name), "TDTS $name non valido!");
		return $self->_get_id('TDTS', $name);
	}
	sub nome_tdts($$) {
		my ($self, $id) = @_;
		SIRTI::Err::prec($self->is_id_tdts($id), "ID TDTS $id non valido!");
		return $self->_get_name('TDTS', $id);
	}
	sub multiplex_tdts_id($$) {
		my ($self, $id) = @_;
		SIRTI::Err::prec($self->is_id_tdts($id), "ID TDTS $id non valido!");
		return $self->{TDTS_MULTIPLEX_FOR_ID}->{$id};
	}
	sub multiplex_tdts_nome($$) {
		my $self = shift;
		my $name = shift;
		SIRTI::Err::prec($self->is_nome_tdts($name), "TDTS $name non valido!");
		return $self->{TDTS_MULTIPLEX_FOR_NAME}->{$name};
	}
	
	sub tipo_tdts_nome($$) {
		my $self = shift;
		my $name = shift;
		SIRTI::Err::prec($self->is_nome_tdts($name), "TDTS $name non valido!");
		return $self->{TDTS_TIPO_FOR_NAME}->{$name};
	}

	sub tipo_tdts_id($$) {
		my $self = shift;
		my $id = shift;
		SIRTI::Err::prec($self->is_id_tdts($id), "TDTS $id non valido!");
		return $self->{TDTS_TIPO_FOR_ID}->{$id};
	}

	#
	# ACTION
	#
	sub assegnabile_azione_id($$) {
		my ($self, $id) = @_;
		SIRTI::Err::prec($self->is_id_azione($id), "ID AZIONE $id non valido!");
		return $self->{ACTION_ASSIGNABLE_FOR_ID}->{$id};
	}
	sub assegnabile_azione_nome($$) {
		my $self = shift;
		my $name = uc(shift);
		SIRTI::Err::prec($self->is_nome_azione($name), "NOME AZIONE $name non valido!");
		return $self->{ACTION_ASSIGNABLE_FOR_NAME}->{$name};
	}

	#
	# GRUPPI
	#
	sub is_id_gruppo($$) {
		my ($self, $id) = @_;
		return defined $self->_get_name('GRUPPI', $id);
	}
	sub is_nome_gruppo($$) {
		my $self = shift;
		my $name = uc(shift);
		return defined $self->_get_id('GRUPPI', $name);
	}
	sub id_gruppo($$) {
		my $self = shift;
		my $name = uc(shift);
		SIRTI::Err::prec($self->is_nome_gruppo($name), "Gruppo $name non valido!");
		return $self->_get_id('GRUPPI', $name);
	}
	sub nome_gruppo($$) {
		my ($self, $id) = @_;
		SIRTI::Err::prec($self->is_id_gruppo($id), "ID gruppo $id non valido!");
		return $self->_get_name('GRUPPI', $id);
	}

	#
	# RUOLI
	#
	sub is_id_ruolo($$) {
		my ($self, $id) = @_;
		return defined $self->_get_name('RUOLI', $id);
	}
	sub is_nome_ruolo($$) {
		my $self = shift;
		my $name = uc(shift);
		return defined $self->_get_id('RUOLI', $name);
	}
	sub id_ruolo($$) {
		my $self = shift;
		my $name = uc(shift);
		SIRTI::Err::prec($self->is_nome_ruolo($name), "Ruolo $name non valido!");
		return $self->_get_id('RUOLI', $name);
	}
	sub nome_ruolo($$) {
		my ($self, $id) = @_;
		SIRTI::Err::prec($self->is_id_ruolo($id), "ID ruolo $id non valido!");
		return $self->_get_name('RUOLI', $id);
	}

	#
	# OPERATORI
	#
	sub is_id_operatore($$) {
		my ($self, $id) = @_;
		return defined $self->_get_name('OPERATORI', $id);
	}
	sub is_nome_operatore($$) {
		my $self = shift;
		my $name = uc(shift);
		return defined $self->_get_id('OPERATORI', $name);
	}
	sub id_operatore($$) {
		my $self = shift;
		my $name = uc(shift);
		SIRTI::Err::prec($self->is_nome_operatore($name), "Operatore $name non valido!");
		return $self->_get_id('OPERATORI', $name);
	}
	sub nome_operatore($$) {
		my ($self, $id) = @_;
		SIRTI::Err::prec($self->is_id_operatore($id), "ID operatore $id non valido!");
		return $self->_get_name('OPERATORI', $id);
	}

	#
	# TIPI_ATTIVITA
	#
	sub is_id_tipo_attivita($$) {
		my ($self, $id) = @_;
		return defined $self->_get_name('TA', $id);
	}
	sub is_nome_tipo_attivita($$) {
		my $self = shift;
		my $name = uc(shift);
		return defined $self->_get_id('TA', $name);
	}
	sub id_tipo_attivita($$) {
		my $self = shift;
		my $name = uc(shift);
		SIRTI::Err::prec($self->is_nome_tipo_attivita($name), "Tipo attivita' $name non valido!");
		return $self->_get_id('TA', $name);
	}
	sub nome_tipo_attivita($$) {
		my ($self, $id) = @_;
		SIRTI::Err::prec($self->is_id_tipo_attivita($id), "ID tipo attivita' $id non valido!");
		return $self->_get_name('TA', $id);
	}
	sub descrizione_tipo_attivita($$) {
		my ($self, $id) = @_;
		SIRTI::Err::prec($self->is_id_tipo_attivita($id), "ID tipo attivita' $id non valido!");
		return $self->_get_description('TA', $id);
	}
	
	#
	# STATI
	#
	sub is_id_stato($$) {
		my ($self, $id) = @_;
		return defined $self->_get_name('STATI', $id);
	}
	sub is_nome_stato($$) {
		my $self = shift;
		my $name = uc(shift);
		return defined $self->_get_id('STATI', $name);
	}
	sub id_stato  {
		my $self = shift;
		if (scalar(@_) == 1) {
			my $name = uc(shift);
			SIRTI::Err::prec($self->is_nome_stato($name), "Stato $name non valido!");
			return $self->_get_id('STATI', $name);
		}
		else {
			return map {
				my $name=uc($_);
				SIRTI::Err::prec($self->is_nome_stato($name), "Stato $name non valida!");
				$self->_get_id('STATI', $name);
			} @_;						
		}
	}
	sub nome_stato($$) {
		my ($self, $id) = @_;
		SIRTI::Err::prec($self->is_id_stato($id), "ID stato $id non valido!");
		return $self->_get_name('STATI', $id);
	}
	
	sub is_stato_finale($$) {
		my ($self, $id) = @_;
		return 1 if $self->{STATI}->{$id}->{FLAG_STATO_PARTENZA} =~/^(C|Q)$/;
	}

	#
	# ACTION
	#
	sub is_id_azione($$) {
		my ($self, $id) = @_;
		return defined $self->_get_name('ACTION', $id);
	}
	sub is_nome_azione($$) {
		my $self = shift;
		my $name = uc(shift);
		return defined $self->_get_id('ACTION', $name);
	}
	sub id_azione  {
		my $self = shift;
		if (scalar(@_) == 1) {
			my $name = uc(shift);
			SIRTI::Err::prec($self->is_nome_azione($name), "Azione $name non valida!");
			return $self->_get_id('ACTION', $name);
		}
		else {
			return map {
				my $name=uc($_);
				SIRTI::Err::prec($self->is_nome_azione($name), "Azione $name non valida!");
				$self->_get_id('ACTION', $name);
			} @_;				
		}
	}
	sub nome_azione($$) {
		my ($self, $id) = @_;
		SIRTI::Err::prec($self->is_id_azione($id), "ID azione $id non valido!");
		return $self->_get_name('ACTION', $id);
	}

	sub descrizione_azione  {
		my $self = shift;
		my $name = uc(shift);
		SIRTI::Err::prec($self->is_nome_azione($name), "Azione $name non valida!");
		return $self->_get_description('ACTION', $name);
	}

	#
	# TDTA
	#
	sub is_id_tdta($$) {
		my ($self, $id) = @_;
		return defined $self->_get_name('TDTA', $id);
	}
	sub is_nome_tdta($$) {
		my $self = shift;
		my $name = shift;
		return defined $self->_get_id('TDTA', $name);
	}
	sub id_tdta($$) {
		my $self = shift;
		my $name = shift;
		SIRTI::Err::prec($self->is_nome_tdta($name), "TDTA $name non valido!");
		return $self->_get_id('TDTA', $name);
	}
	sub nome_tdta($$) {
		my ($self, $id) = @_;
		SIRTI::Err::prec($self->is_id_tdta($id), "ID TDTA $id non valido!");
		return $self->_get_name('TDTA', $id);
	}

	sub default_tdta_nome($$) {
		my $self = shift;
		my $name = shift;
		SIRTI::Err::prec($self->is_nome_tdta($name), "TDTA $name non valido!");
		return $self->{TDTA_DEFAULT_VALUES_FOR_NAME}->{$name};
	}

	sub default_tdta_id($$) {
		my $self = shift;
		my $id = shift;
		SIRTI::Err::prec($self->is_id_tdta($id), "TDTA $id non valido!");
		return $self->{TDTA_DEFAULT_VALUES_FOR_ID}->{$id};
	}
	
	sub predefinedValue_tdta_nome($$) {
		my $self = shift;
		my $name = shift;
		SIRTI::Err::prec($self->is_nome_tdta($name), "TDTA $name non valido!");
		return $self->{TDTA_PREDEFINED_VALUE_VALUES_FOR_NAME}->{$name};
	}

	sub predefinedValue_tdta_id($$) {
		my $self = shift;
		my $id = shift;
		SIRTI::Err::prec($self->is_id_tdta($id), "TDTA $id non valido!");
		return $self->{TDTA_PREDEFINED_VALUE_VALUES_FOR_ID}->{$id};
	}
	
	sub tipo_ui_tdta_nome($$) {
		my $self = shift;
		my $name = shift;
		SIRTI::Err::prec($self->is_nome_tdta($name), "TDTA $name non valido!");
		return $self->{TDTA_TIPO_UI_FOR_NAME}->{$name};
	}

	sub tipo_ui_tdta_id($$) {
		my $self = shift;
		my $id = shift;
		SIRTI::Err::prec($self->is_id_tdta($id), "TDTA $id non valido!");
		return $self->{TDTA_TIPO_UI_FOR_ID}->{$id};
	}

	sub valori_tdta_nome($$) {
		my $self = shift;
		my $name = shift;
		SIRTI::Err::prec($self->is_nome_tdta($name), "TDTA $name non valido!");
		return $self->{TDTA_VALORI_FOR_NAME}->{$name};
	}

	sub valori_tdta_id($$) {
		my $self = shift;
		my $id = shift;
		SIRTI::Err::prec($self->is_id_tdta($id), "TDTA $id non valido!");
		return $self->{TDTA_VALORI_FOR_ID}->{$id};
	}

	sub etichetta_tdta_nome($$) {
		my $self = shift;
		my $name = shift;
		SIRTI::Err::prec($self->is_nome_tdta($name), "TDTA $name non valido!");
		return $self->{TDTA_ETICHETTA_FOR_NAME}->{$name};
	}
	
	sub etichetta_tdta_id($$) {
		my $self = shift;
		my $id = shift;
		SIRTI::Err::prec($self->is_id_tdta($id), "TDTA $id non valido!");
		return $self->{TDTA_ETICHETTA_FOR_ID}->{$id};
	}
	
	sub suggerimento_tdta_nome($$) {
		my $self = shift;
		my $name = shift;
		SIRTI::Err::prec($self->is_nome_tdta($name), "TDTA $name non valido!");
		return $self->{TDTA_SUGGERIMENTO_FOR_NAME}->{$name};
	}
	
	sub suggerimento_tdta_id($$) {
		my $self = shift;
		my $id = shift;
		SIRTI::Err::prec($self->is_id_tdta($id), "TDTA $id non valido!");
		return $self->{TDTA_SUGGERIMENTO_FOR_ID}->{$id};
	}

	sub valuta_tdta_nome($$) {
		my $self = shift;
		my $name = shift;
		SIRTI::Err::prec($self->is_nome_tdta($name), "TDTA $name non valido!");
		return $self->{TDTA_VALUTA_FOR_NAME}->{$name};
	}
	
	sub valuta_tdta_id($$) {
		my $self = shift;
		my $id = shift;
		SIRTI::Err::prec($self->is_id_tdta($id), "TDTA $id non valido!");
		return $self->{TDTA_VALUTA_FOR_ID}->{$id};
	}

	sub decimali_tdta_nome($$) {
		my $self = shift;
		my $name = shift;
		SIRTI::Err::prec($self->is_nome_tdta($name), "TDTA $name non valido!");
		return $self->{TDTA_DECIMALI_FOR_NAME}->{$name};
	}
	
	sub decimali_tdta_id($$) {
		my $self = shift;
		my $id = shift;
		SIRTI::Err::prec($self->is_id_tdta($id), "TDTA $id non valido!");
		return $self->{TDTA_DECIMALI_FOR_ID}->{$id};
	}
	
	sub is_activity_properties_group($$) {
		my ($self, $activity_type_name) = @_;
		return exists $self->{ACTIVITY_PROPERTIES_GROUP}->{$activity_type_name};
	}

	sub is_system_properties_group($$) {
		my ($self, $system_type_name) = @_;
		return exists $self->{SYSTEM_PROPERTIES_GROUP}->{$system_type_name};
	}
	
	#
	# Ritorna un riferimento ad un array di hash che rappresenta i TDTA associati
	# al TIPO_ATTIVITA ed all'ACTION specificate
	#
	sub tdta_for_action() {
		my $self = shift;
		# SIRTI::DB
		# NOME_TA || ID_TA
		# NOME_AZIONE || ID_AZIONE
		my $db = shift;
		my %param = @_;
		SIRTI::Err::prec(ref($db) eq 'SIRTI::DB', "Oggetto DB non specificato un'istanza di SIRTI::DB\n");
		SIRTI::Err::prec(exists($param{ID_TA}) || exists($param{NOME_TA}), "TIPO_ATTIVITA non specificato! (usare ID_TA o NOME_TA)");
		SIRTI::Err::prec($self->is_nome_tipo_attivita($param{NOME_TA}), "Tipo attivita' $param{NOME_TA} non valido!") if exists($param{NOME_TA});
		SIRTI::Err::prec($self->is_id_tipo_attivita($param{ID_TA}), "ID tipo attivita' $param{ID_TA} non valido!") if exists($param{ID_TA});
		SIRTI::Err::prec(exists($param{ID_AZIONE}) || exists($param{NOME_AZIONE}), "AZIONE non specificata! (usare ID_AZIONE o NOME_AZIONE)");
		SIRTI::Err::prec($self->is_nome_azione($param{NOME_AZIONE}), "Azione $param{NOME_AZIONE} non valida!") if exists($param{NOME_AZIONE});
		SIRTI::Err::prec($self->is_id_azione($param{ID_AZIONE}), "ID azione $param{ID_AZIONE} non valido!") if exists($param{ID_AZIONE});
		$param{ID_TA} = $self->id_tipo_attivita($param{NOME_TA}) if exists($param{NOME_TA});
		$param{ID_AZIONE} = $self->id_azione($param{NOME_AZIONE}) if exists($param{NOME_AZIONE});
		my $key = "$param{ID_TA}_$param{ID_AZIONE}";
		unless (scalar (keys %{$self->{TDTA4ACTION}})){
			my $sql = "
				select tdtaa.ID_TIPO_DATO_TECNICO_ATTIVITA ID_TDTA
					, tdta.DESCRIZIONE NOME_TDTA
					, tdtaa.POSIZIONE
					, tdtaa.OBBLIGATORIO
					, tdtaa.SOLO_LETTURA
					, tdtaa.NASCOSTO
                    , tdtaa.PREPOPOLA
                    , tdtaa.ID_TIPO_ATTIVITA||'_'||tdtaa.ID_ACTION key
				from    tipi_dati_tecnici_att_action tdtaa
					   ,tipi_dati_tecnici_attivita tdta
				where tdta.ID_TIPO_DATO_TECNICO_ATTIVITA = tdtaa.ID_TIPO_DATO_TECNICO_ATTIVITA    
				order by 3, 4, 2
			";
			
			my $res = $db->fetchall_hashref($sql) || SIRTI::Err::db_fatal_error($db);
			
			for my $r (@{$res}){
				my $key_cal = $r->{KEY};
				delete $r->{KEY};
				if (exists $self->{TDTA4ACTION}->{$key_cal}){
					push @{$self->{TDTA4ACTION}->{$key_cal}}, $r;
				} else {
					$self->{TDTA4ACTION}->{$key_cal} = [$r];
				}
			}
		}
		
		return $self->{TDTA4ACTION}->{$key}||[];
	}
	
	sub activity_properties_group($$) {
		my $self = shift;
		my $name = shift;
		SIRTI::Err::prec($self->is_nome_tipo_attivita($name), "Tipo attivita $name non valido!");
		return [] unless $self->is_activity_properties_group($name);
		return $self->_get_activity_properties_group($name);
	}
	
	sub activity_property_groups() {
		my $self = shift;
		return $self->_get_activity_property_groups();
	}

	sub system_properties_group($$) {
		my $self = shift;
		my $name = shift;
		SIRTI::Err::prec($self->is_nome_tipo_sistema($name), "Tipo sistema $name non valido!");
		return [] unless $self->is_system_properties_group($name);
		return $self->_get_system_properties_group($name);
	}

	sub system_property_groups() {
		my $self = shift;
		return $self->_get_system_property_groups();
	}
	
	1;

