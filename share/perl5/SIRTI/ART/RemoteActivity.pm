################################################################################
#
#
# SIRTI::ART::RemoteActivity
#
#
################################################################################


################################################################################
#
#
# Classe "padre" per la gestione delle REMOTE_ACTIVITY
#
#
################################################################################
package SIRTI::ART::RemoteActivity;

use strict;
use warnings;
use Carp; # 'croak';
use File::Basename;
use SIRTI::DB;
use SIRTI::PLSQL::RM_ACTIVITY; 
use SIRTI::ART::RemoteActivity::Event;


################################################################################
#  P R O P R I E T A '   P U B B L I C H E
################################################################################

our $VERSION = '0.04';
our $LOCALHOST_DEFAULT = '___LOCALHOST___';

#
# Elenco parametri mandatori ed opzionali per l'instanziazione della classe SIRTI::ART::RemoteActivity
#
our @mandatory_args	= qw(DB SESSION_DESCRIPTION SOURCE_CONTEXT);
our @optional_args	= qw(USER_ID USER_NAME DATE_FORMAT DEBUG TARGET_SERVICE TARGET_CONTEXT TARGET SOURCE_SERVICE RA_DBLINK DBMS_OUTPUT DBMS_OUTPUT_BUFFER);


################################################################################
#  M E T O D I   P R I V A T I
################################################################################

#
# Handle della connessione al database
# 
sub _dbh { &{ $_[0] }("DB") }

#
# Metodo da sovrascrivere nelle classi subclassanti Source e Target
#
sub _init { croak "Private method _init() must be overwritten!\n"; }


sub _check_session_exists {
	my $self = shift;
	return $self->get_plsql->check_session_exists(
		SESSION_TOKEN => $self->get_session_token()
		,SESSION_ID => $self->get_session_id()
	); 
}

sub _uniq {
	my $self = shift;
    my %seen = ();
    my @r = ();
    foreach my $a (@_) {
        unless ($seen{$a}) {
            push @r, $a;
            $seen{$a} = 1;
        }
    }
    return @r;
}

sub _set_date_format {
	my $self = shift;
	$self->_set_previous_date_format($self->_dbh()->get_session_parameters('NLS_DATE_FORMAT')->{NLS_DATE_FORMAT});
	$self->_dbh()->set_session_parameters(NLS_DATE_FORMAT => $self->get_date_format());
	return 1;
}

sub _unset_date_format {
	my $self = shift;
	$self->_dbh()->set_session_parameters(NLS_DATE_FORMAT => $self->_get_previous_date_format());
	$self->_unset_previous_date_format();
	return 1;
}

################################################################################
#  M E T O D I   P U B L I C I
################################################################################

#
# Costruttore della classe SIRTI::ART::RemoteActivity
#
# Parametri mandatori:
#
#  - DB						- Oggetto di tipo: SIRTI::DB, ART::C_db, ART::C_db_prod_art
#  - SESSION_DESCRIPTION 	- Descrizione della sessione da aprire
#  - SOURCE_SERVICE 		\
#  - SOURCE_CONTEXT 		|
#  - TARGET_SERVICE 		|--- Coordinate sorgente/destinazione degli eventi da inserire
#  - TARGET_CONTEXT			|
#  - TARGET					/
#
# Parametri opzionali:
#
# - USER_ID				- ID operatore ART
# - USER_NAME			- LOGIN operatore ART (chiave univoca in Single Sign On)
# - DATE_FORMAT			- Formato delle data da utilizzare (default="YYYYMMDDHH24MISS")
# - DEBUG				- Stampa informazioni utili per il debug (default=0)
# - DBMS_OUTPUT			- Abilita output dbms
# - DBMS_OUTPUT_BUFFER	- Abilita output dbms in modalita' buffer
#
sub new($$) {
	my $this = shift;
	my $class = ref($this) || $this;
	my %params = @_;
	my %self = ();
	
	# Verifica parametri obbligatori
	for my $mandatory (@mandatory_args) {
		croak "Missing mandatory argument: $mandatory\n"
			unless exists $params{$mandatory};
		croak "Uninitialized mandatory argument: $mandatory\n"
			unless defined $params{$mandatory};
		$self{$mandatory} = $params{$mandatory};
		delete $params{$mandatory};
	}
	# Verifica parametri obbligatori
	for my $optional (@optional_args) {
		$self{$optional} = $params{$optional};
		delete $params{$optional};
	}
	# Argomenti non ammessi
	croak "Unknown arguments: " . join(',', keys(%params)) . "\n"
		if scalar(keys(%params)) > 0;
	
	croak "TARGET invalid for Target Mode\n"
		if ($self{TARGET} && $class !~/Source/);
	
	# Controllo struttura chiave TARGET
	if ($self{TARGET}) {
		croak "TARGET must be an HASH\n"
			if (ref($self{TARGET}) ne 'HASH');
		for my $key ( keys %{$self{TARGET}} ) {
			my $isa = (
                ref($self{TARGET}->{$key}) && ref($self{TARGET}->{$key}) ne 'REF'
                ? ref($self{TARGET}->{$key})
                : ref(\$self{TARGET}->{$key})
            );
            croak "SOURCE_SERVICE missing"
            	unless ($self{SOURCE_SERVICE});
            croak "TARGET keys must be SCALAR or ARRAY"
            	if ($isa !~/^(SCALAR|ARRAY)$/);
            croak "if TARGET defined, SERVICE cannot be $LOCALHOST_DEFAULT"
				if ($key =~/^$LOCALHOST_DEFAULT$/);
			croak "if TARGET defined, CONTEXT cannot be $LOCALHOST_DEFAULT"
				if ($self{TARGET}->{$key} =~/^$LOCALHOST_DEFAULT$/);
			# Preparo hash vuoto TARGET_TOKEN
			if ($isa eq 'SCALAR'){
				$self{TARGET_TOKEN}->{$key}->{$self{TARGET}->{$key}}= undef;
			} else {
				for my $value (@{$self{TARGET}->{$key}} ){
					$self{TARGET_TOKEN}->{$key}->{$value}= undef;
				}
			}
		}
	}

	#Check for LOCALHOST
	if (!$self{TARGET_SERVICE} && !$self{TARGET} && !$self{SOURCE_SERVICE}){
		croak "Missing TARGET_CONTEXT\n"
			if !$self{TARGET_CONTEXT};
		croak "RA_DBLINK not permitted in LOCALHOST MODE\n"
			if $self{RA_DBLINK};
		$self{SOURCE_SERVICE} = $LOCALHOST_DEFAULT;
		$self{TARGET_SERVICE} = $LOCALHOST_DEFAULT;
	} else {
		#Check per parametri TARGETs
		croak "TARGET or TARGET_SERVICE/TARGET_CONTEXT mandatory\n"
			if (!$self{TARGET_SERVICE} && !$self{TARGET_CONTEXT} && !$self{TARGET});
		
		croak "TARGET_SERVICE/TARGET_CONTEXT not compatible with TARGET"
			if (defined $self{TARGET} && ($self{TARGET_SERVICE} || $self{TARGET_CONTEXT}) );
		
		croak "If not TARGET define TARGET_SERVICE and TARGET_CONTEXT"
			if (!$self{TARGET} && (!$self{TARGET_SERVICE} || !$self{TARGET_CONTEXT}) );
#		if ($self{TARGET}){
#			$self{TARGET_SERVICE} = $MULTICASTING_DEFAULT;
#			$self{TARGET_CONTEXT} = $MULTICASTING_DEFAULT;
#		}
	}
	$self{DATE_FORMAT} = 'YYYYMMDDHH24MISS' unless defined $self{DATE_FORMAT};
	
	# Creazione oggetto temporaneao
	my $self = bless(\%self, $class);
	
	# Costruisce l'oggetto SIRTI::DB se DB contiene un oggetto di tipo differente
	$self->{DB} = SIRTI::DB->new( $self->{DB} ) unless ref($self->{DB}) =~/^SIRTI::DB:{0,2}/;

	# 2021-09-17: commentato in quanto la query è spesso lenta in modo anomalo e ingiusticato	
	# # Verifico presenza package RM_ACTIVITY
	# croak 'Missing package RM_ACTIVITY!'
	#  	unless $self->{DB}->fetch_minimalized(qq{
	# 		SELECT 
	# 		  CASE 
	# 		  	-- logged-in as REMOTE_ACTIVITY
	# 		    WHEN (SELECT USER FROM DUAL) = 'REMOTE_ACTIVITY' 
	# 		        AND EXISTS (
	# 		                      SELECT 1
	# 		                      FROM  ALL_OBJECTS O
	# 		                      WHERE O.OBJECT_NAME = 'RM_ACTIVITY'
	# 		                        AND O.OBJECT_TYPE = 'PACKAGE'
	# 		                        AND O.OWNER = 'REMOTE_ACTIVITY'
	# 		        )
	# 		        THEN 1
	# 		    WHEN 
	# 			  	-- logged-in NOT as REMOTE_ACTIVITY
	# 		        EXISTS (
	# 		                      SELECT 1
	# 		                      FROM  ALL_OBJECTS O
	# 		                      WHERE O.OBJECT_NAME = 'RM_ACTIVITY'
	# 		                        AND O.OBJECT_TYPE = 'PACKAGE'
	# 		                        AND O.OWNER = 'REMOTE_ACTIVITY'
	# 		        )
	# 		        AND EXISTS (
	# 		                  SELECT 1
	# 		                  FROM  USER_TAB_PRIVS
	# 		                  WHERE GRANTOR = 'REMOTE_ACTIVITY'
	# 		                    AND OWNER = 'REMOTE_ACTIVITY'
	# 		                    AND TABLE_NAME = 'RM_ACTIVITY'
	# 		                    AND PRIVILEGE = 'EXECUTE'
	# 		        ) 
	# 		        AND 
	# 		        EXISTS (
	# 		                  SELECT 1
	# 		                  FROM  USER_SYNONYMS
	# 		                  WHERE SYNONYM_NAME = 'RM_ACTIVITY'
	# 		                    AND TABLE_OWNER = 'REMOTE_ACTIVITY'
	# 		                    AND TABLE_NAME = 'RM_ACTIVITY'
	# 		        )
	# 		                THEN 1
	# 		    ELSE 0
	# 		  END RA_SUPPORT
	# 		FROM DUAL
	# 	});
	 
	# Sub per inizializzazione subclassi
	$self->_init();
		
	my $RA_DBLINK = $self->{RA_DBLINK};

	# NB: qui non uso la _set_date_format perchè si utilizzano la closure
	$self->{PREVIOUS_DATE_FORMAT} = [$self->{DB}->get_session_parameters('NLS_DATE_FORMAT')->{NLS_DATE_FORMAT}];
	$self->{DB}->set_session_parameters(NLS_DATE_FORMAT => $self->{DATE_FORMAT});

	$self->{SESSION_READY}		= 0; # 0: Indica che l'insert nella RA_SESSION non è ancora avvenuto
	my $cmdline	= basename($0) . ' ' . join(" ", map { /\s/ ? "'$_'" : $_ } @ARGV);
    if ( length $cmdline > 3768 ) {
        $cmdline = substr( $cmdline, 0, 3768 ) . '...';
    }
	$self->{SESSION_CMDLINE}	= $cmdline;

	$self->{PLSQL}=SIRTI::PLSQL::RM_ACTIVITY->new(
		DB 				=> $self->{DB}
		,DBLINK 		=> $self{RA_DBLINK}
	);

	my %p=map { ($_,$self->{$_})  } qw( 
							SESSION_DESCRIPTION 
							SOURCE_SERVICE 
							SOURCE_CONTEXT 	
							USER_ID
							USER_NAME
							SESSION_CMDLINE
							RA_DBLINK
						);
	
	$p{SESSION_TYPE} = ($class !~/Source/) ? 'T' : 'S'; 
	

	$self->{SESSION_TOKEN}=$self->{PLSQL}->create_session( DATA => \%p);
	
	$self->{SESSION_ID}=$self->{PLSQL}->get_session_info( SESSION_TOKEN => $self->{SESSION_TOKEN})->{ID};
	
	my $target_token;
	if ($self{TARGET}) {
		for my $key ( keys %{$self{TARGET_TOKEN}} ) {
			for my $value ( keys %{$self{TARGET_TOKEN}->{$key}} ) {
				if (scalar (keys %{$self{TARGET_TOKEN}}) == 1 && scalar (keys %{$self{TARGET_TOKEN}->{$key}}) == 1) {
					$self{TARGET_SERVICE} = $key;
					$self{TARGET_CONTEXT} = $self{TARGET}->{$key};
				}
				$self{TARGET_TOKEN}->{$key}->{$value}=$self->{PLSQL}->create_target_session( 
					SESSION_TOKEN => $self->{SESSION_TOKEN}
					,TARGET_SERVICE => $key
					,TARGET_CONTEXT => $value
				);
				$self->{PLSQL}->store_session( 
					SESSION_TOKEN => $self{TARGET_TOKEN}->{$key}->{$value}
				);
			}
			
		}
	} else {
		$self{TARGET_TOKEN}->{$self{TARGET_SERVICE}}->{$self{TARGET_CONTEXT}}=$self->{PLSQL}->create_target_session( 
			SESSION_TOKEN => $self->{SESSION_TOKEN}
			,TARGET_SERVICE => $self->{TARGET_SERVICE}
			,TARGET_CONTEXT => $self->{TARGET_CONTEXT}
		);
		$self->{PLSQL}->store_session( 
			SESSION_TOKEN => $self{TARGET_TOKEN}->{$self{TARGET_SERVICE}}->{$self{TARGET_CONTEXT}}
		);
	}
		
	#
	# La tecnica della 'closure' consente di "nascondere" le proprieta'
	# constringendo il chiamante ad invocare solo i metodi esposti.
	# Per una riassegnazione il nuovo valore deve essere passato come 
	# parametro aggiuntivo.  
	#
	my $closure = sub {
		my $field = shift;
		if (@_) { $self->{$field} = shift }
		return $self->{$field};
	};
	$closure = bless( $closure, $class );
	
	$closure->_unset_date_format();

	return $closure;
}


=pod

=head2 find_pending( SOURCE_REF => I<SCALAR>, EVENT => I<SCALAR> )

Restituisce (in contesto di lista) l'elenco di ID degli eventi oppure (in contesto scalare) il numero di eventi 'pending'

=cut



#
# Metodo per la ricerca di eventi pending (non ancora lavorati)
#
# Parametri mandatori: nessuno
#
# Parametri opzionali:
#
# - EVENT		- Label/tipologia dell'evento  oppure array di Label/tipologia dell'evento 
# - SOURCE_REF	- Riferimento sorgente	oppure array di  Riferimento sorgente	
#
# In contesto scalare ritorna il numero degli eventi trovati.
# In contesto di lista ritorna un array di ID (degli eventi trovati). 
#
sub find_pending {
	my ($self,%args) = @_;
	my %params = ();
	# Verifica parametri obbligatori
	for my $mandatory (qw//) {
		croak "Missing mandatory argument: $mandatory\n"
			unless exists $args{$mandatory};
		croak "Uninitialized mandatory argument: $mandatory\n"
			unless defined $args{$mandatory};
		$params{$mandatory} = $args{$mandatory};
		delete $args{$mandatory};
	}
	# Verifica parametri NON obbligatori
	for my $optional (qw/EVENT SOURCE_REF/) {
		$params{$optional} = delete $args{$optional};
	}
	# Argomenti non ammessi
	croak "Unknown arguments: " . join(',', keys(%args)) . "\n"
		if scalar(keys(%args)) > 0;
	
	# Gestione EVENT e/o SOURCE_REF multipli
	my @events = ();
	push @events, $self->_uniq(@{ $params{EVENT} }) if ref($params{EVENT}) eq 'ARRAY'; 
	push @events, $params{EVENT} if defined($params{EVENT}) && ref($params{EVENT}) eq '';

	my @source_refs = ();
	push @source_refs, $self->_uniq(@{ $params{SOURCE_REF} }) if ref($params{SOURCE_REF}) eq 'ARRAY';
	push @source_refs, $params{SOURCE_REF} if defined($params{SOURCE_REF}) && ref($params{SOURCE_REF}) eq '';
	
	$self->_set_date_format();

	# Ricerca utilizzando la prepare in base alla presenza/asenza parametri in input
	my $tmp = [];
	SWITCH: {
		if ( scalar(@source_refs) && scalar(@events) ) {
			for my $e (@events) {
				for my $r (@source_refs) {
                    my $query = $self->get_plsql->find_events_pending(SESSION_TOKEN => $self->get_session_token,EVENT => $e,SOURCE_REF => $r);
                    my $row=$self->_dbh()->fetchall_arrayref($query);
                    foreach my $r ( @$row ) {
                        push @$tmp, $r->[0];
                    }
				}
			}
			last SWITCH;
		}
		if ( scalar(@source_refs) ) {
			for my $r (@source_refs) {
				my $query = $self->get_plsql->find_events_pending(SESSION_TOKEN => $self->get_session_token,SOURCE_REF => $r);
				my $row=$self->_dbh()->fetchall_arrayref($query);
				foreach my $r ( @$row ) {
					push @$tmp, $r->[0];
				}
			}
			last SWITCH;
		}
		if ( scalar(@events) ) {
			for my $e (@events) {
				my $query = $self->get_plsql->find_events_pending(SESSION_TOKEN => $self->get_session_token,EVENT => $e);
				my $row=$self->_dbh()->fetchall_arrayref($query);
				foreach my $r ( @$row ) {
					push @$tmp, $r->[0];
				}
			}
			last SWITCH;
		}
		my $query = $self->get_plsql->find_events_pending(SESSION_TOKEN => $self->get_session_token);
		my $row=$self->_dbh()->fetchall_arrayref($query);
		foreach my $r ( @$row ) {
			push @$tmp, $r->[0];
		}
	}

	$self->_unset_date_format();

	# Riconduce i risultati ad un array in quanto SIRTI::DB::fetchall_arrayref() ritorna un ARRAYREF di ARRAYREF
	my @pending = ();
	push @pending, @$tmp;
	# Verifica se ritornare un array o il numero di elementi dell'array
	return ( wantarray ? sort({ $a <=> $b } @pending) : scalar(@pending) );
}


=pod

=head2 find_first_pending( SOURCE_REF => I<SCALAR>, EVENT => I<SCALAR> )

Restituisce l'ID del primo  evento pending (non ancora gestito)

=cut

#
# Metodo per la ricerca del primo evento pending (non ancora lavorato)
#
# Parametri mandatori: nessuno
#
# Parametri opzionali:
#
# - EVENT		- Label/tipologia dell'evento 
# - SOURCE_REF	- Riferimento sorgente
#
#  ritorna l'ID dell'evento trovato. 
#



sub find_first_pending {
	my ($self,%args) = @_;
	my @res = $self->find_pending(%args);
	return $res[0];
}


=pod

=head2 find_last_pending( SOURCE_REF => I<SCALAR>, EVENT => I<SCALAR> )

Restituisce l'ID dell'ultimo evento pending (non ancora gestito)

=cut

#
# Metodo per la ricerca dell'ultimo evento pending (non ancora lavorato)
#
# Parametri obbligatori: nessuno
#
# Parametri opzionali:
#
# - EVENT		- Label/tipologia dell'evento 
# - SOURCE_REF	- Riferimento sorgente
#
#  ritorna un l'ID (dell'evento trovato). 
#
sub find_last_pending {
	my ($self,%args) = @_;
	my @res = $self->find_pending(%args);
	return $res[$#res];
}

#
# Metodi per l'accesso alle proprieta' dell'oggetto
#
sub debug					{ &{ $_[0] }("DEBUG") }
sub get_dblink				{ &{ $_[0] }("RA_DBLINK") }
sub get_date_format			{ &{ $_[0] }("DATE_FORMAT") }
sub _get_previous_date_format			{ 
	my $self = shift;
	my $previous = &{ $self }("PREVIOUS_DATE_FORMAT") ;
	return $previous->[(scalar @{$previous}-1)];
}
sub _set_previous_date_format			{
	my $self = shift;
	my $newValue = shift;
	my $previous = &{ $self }("PREVIOUS_DATE_FORMAT");
	push @$previous, $newValue;
	&{ $self }("PREVIOUS_DATE_FORMAT", $previous);
	return;
}
sub _unset_previous_date_format			{
	my $self = shift;
	my $previous = &{ $self }("PREVIOUS_DATE_FORMAT");
	pop @$previous;
	&{ $self }("PREVIOUS_DATE_FORMAT", $previous);
	return;
}
sub get_service_list		{ &{ $_[0] }("SERVICE") }
sub get_context_list		{ &{ $_[0] }("CONTEXT") }
sub get_source_service		{ &{ $_[0] }("SOURCE_SERVICE") }
sub get_source_service_info	{ &{ $_[0] }("SOURCE_SERVICE_INFO") }
sub get_source_context		{ &{ $_[0] }("SOURCE_CONTEXT") }
sub get_target_service		{ &{ $_[0] }("TARGET_SERVICE") }
sub get_target_service_info { &{ $_[0] }("TARGET_SERVICE_INFO") }
sub get_target_context		{ &{ $_[0] }("TARGET_CONTEXT") }
sub get_target				{ &{ $_[0] }("TARGET") }
sub get_target_token		{ &{ $_[0] }("TARGET_TOKEN") }
sub get_session_id			{ &{ $_[0] }("SESSION_ID") }
sub get_session_description	{ &{ $_[0] }("SESSION_DESCRIPTION") }
sub get_session_cmdline		{ &{ $_[0] }("SESSION_CMDLINE") }
sub get_session_token		{ &{ $_[0] }("SESSION_TOKEN") }
sub get_user_id				{ &{ $_[0] }("USER_ID") }
sub get_user_name			{ &{ $_[0] }("USER_NAME") }
sub get_ip					{ &{ $_[0] }("IP") }
sub get_os_user				{ &{ $_[0] }("OS_USER") }
sub get_host				{ &{ $_[0] }("HOST") }
sub is_session_ready		{ 
	# se la sessione non e' ancora stata inizializzata restituisco 0
	return &{ $_[0] }("SESSION_READY")
		unless &{ $_[0] }("SESSION_READY");
	# la sessione e' gia' stata inizializzata ma verifico che un rollback non abbia cancellato
	# il record in ra_session, eventualmente lo reinserisco, ritorno 1
	return $_[0]->_check_session_exists();  
}
sub set_session_ready		{ &{ $_[0] }("SESSION_READY", 1) }
sub get_plsql				{ 
	return undef unless defined $_[0];
	$_[0]->('PLSQL');
}

=pod

=head2 get_parent_id( ID => I<SCALAR>)

Elabora un evento 'pending'

=cut
sub get_parent_id {
	my $self = shift;
	my ($id) = @_;
	$self->_set_date_format();
	# Istanzia un nuovo oggetto 'evento' per effettuare la CANCELlazione
	my $e = SIRTI::ART::RemoteActivity::Event->new(
		 RA => $self
		,ID => $id
		,PLSQL => $self->get_plsql
	);
	$self->_unset_date_format();
	croak "Event $id not found|\n"
		unless defined($e);
	return $e->get_parent_id();
}

#
# Restituisce un oggetto di tipo SIRTI::ART::RemoteActivity::Events
#
# Parametri mandatori:
# 
# - ID	- ID dell'evento
#

sub get_event {
	my ($self,%args) = @_;
	my %params = ();
	# Verifica parametri obbligatori
	for my $mandatory (qw/ID/) {
		croak "Missing mandatory argument: $mandatory\n"
			unless exists $args{$mandatory};
		croak "Uninitialized mandatory argument: $mandatory\n"
			unless defined $args{$mandatory};
		$params{$mandatory} = $args{$mandatory};
		delete $args{$mandatory};
	}
	# Verifica parametri NON obbligatori
	for my $optional (qw//) {
		$params{$optional} = $args{$optional};
		delete $args{$optional};
	}
	# Argomenti non ammessi
	croak "Unknown arguments: " . join(',', keys(%args)) . "\n"
		if scalar(keys(%args)) > 0;

	$self->_set_date_format();

	my $event = SIRTI::ART::RemoteActivity::Event->new( 
		RA => $self 
		,ID => $params{ID}
		,PLSQL => $self->get_plsql
	);

	$self->_unset_date_format();

	return $event;
}


sub init_session {
	my $self = shift;
	return 1 if $self->is_session_ready();
	$self->_set_date_format();
	for my $key ( keys %{$self->get_target_token} ) {
		for my $value ( keys %{$self->get_target_token->{$key}} ) {
			$self->get_plsql->store_session( 
				SESSION_TOKEN => $self->get_target_token->{$key}->{$value}
			);
		}
	}
	#$self->get_plsql->store_session;
	$self->set_session_ready(); 
	$self->_unset_date_format();
	return $self->is_session_ready();
}

sub finish {  
	my $self=shift;
	my $p=$self->get_plsql;
	$p->finish if defined $p;
	return $self;
}

sub DESTROY {
	my $self=shift;
	local $@;
	eval { $self->finish; } 
}

if ($0 eq __FILE__) {
	my $DEBUG = 1;
	my $rm=SIRTI::ART::RemoteActivity->new(
			 DB             => SIRTI::DB->new($ENV{SQLID}, DEBUG => 0) #ART::C_db_prod_art->new() #($ENV{SQLID}) #SIRTI::DB->new($ENV{SQLID})
			,SESSION_DESCRIPTION => 'Hello world!'
			,SOURCE_SERVICE    => 'XTL2_PROVISIONING'
			,SOURCE_CONTEXT => 'MINIFASE2'
			,TARGET_SERVICE    => 'TL2_OLO2OLO'
			,TARGET_CONTEXT => 'MINIFASE2'
			,DATE_FORMAT => 'dd-mm-yyyy'
			,USER_ID	=> 999999
			,USER_NAME	=> 'ALVARO'
			,DEBUG 		=> $DEBUG
	);
}

1;


__END__

