################################################################################
#
# SIRTI::ART::RemoteActivity::Event
#
################################################################################

=head1 NAME

B<SIRTI::ART::RemoteActivity::Event> - Astrazione oggetto evento/messaggio

=head1 SYNOPSIS

  my $e = SIRTI::ART::RemoteActivity::Event->new( 
      RA => SIRTI::ART::RemoteActivity::Source->new(...)
     ,ID => 1000
  );

=head1 DESCRIPTION

La classe B<SIRTI::ART::RemoteActivity::Event> consente di mappare un 
messaggio/evento.

=cut

################################################################################
#
# Classe per il mappamento di un singolo evento
#
################################################################################



package SIRTI::ART::RemoteActivity::Event;

use strict;
use warnings;
use Carp; # 'croak';

use SIRTI::PLSQL::RM_ACTIVITY qw(:op);

#
# Popolamento evento 
#
sub _init {
	my $self = shift;
	my $ra = $self->get_ra();
	$ra->_set_date_format();
	$ra->_get_previous_date_format();
	# Carica le INFOrmazioni generali dell'evento (tabella RA): termina se l'ID non esiste  
	my $cur=$self->{PLSQL}->event_operation(OP_QUERY,ID => $self->{ID}, SESSION_TOKEN => $self->{SESSION_TOKEN});
	$self->{INFO}=$cur->fetchrow_hashref;
	$cur->finish;
	$cur=undef;
	$ra->_unset_date_format()
		&& return undef
			unless defined $self->{INFO};
	$ra->_get_previous_date_format();
	# Carica le informazioni di SESSION
	my $r=$self->{PLSQL}->load_session_from_event(EVENT_ID => $self->{ID});
	my $sess_info=$self->{PLSQL}->get_session_info(SESSION_TOKEN => $r->{SESSION_TOKEN});
	$self->{SESSION} = $sess_info;

	# Carica le informazioni addizionali (DATA) gestendo (eventualmente) le liste di valori
	$self->{DATA} = $self->{PLSQL}->data_event_operation(OP_QUERY, $self->{ID});
	
	# Carica le informazioni di LOOKUP

	$cur=$self->{PLSQL}->find_event_ignored(EVENT_ID => $self->{ID});
	$self->{LOOKUP}=[];
	while(my $r=$cur->fetchrow_hashref) {
		  push @{$self->{LOOKUP}},$r;
	}
	$cur->finish;

	$cur=$self->{PLSQL}->load_ack(EVENT_ID => $self->{ID}, SESSION_TOKEN => $self->{SESSION_TOKEN});
	my @tmp_ack=();
	while(my $r=$cur->fetchrow_hashref) {
		push @tmp_ack,$r;
		last;
	}
	$cur->finish;

	$self->{ACK} = ( scalar(@tmp_ack) == 0 ? undef : $tmp_ack[0] );
	$ra->_get_previous_date_format();
	$self->{DATA_ACK} = $self->{PLSQL}->data_ack_event_operation(OP_QUERY, $self->{ID});
	$ra->_get_previous_date_format();
	$ra->_unset_date_format();

	return 1;
}

=pod

=head1 METHODS

=head2 new( RA => I<SIRTI::ART::RemoteActivity::Source>|I<SIRTI::ART::RemoteActivity::Target>, ID => I<SCALAR> )

=over 4

=item * B<RA> : oggetto di tipo I<SIRTI::ART::RemoteActivity::Source> oppure I<SIRTI::ART::RemoteActivity::Target>

=item * B<ID> : IDentificativo dell'evento/messaggio da istanziare

=back

=cut
sub _new {
	my $this = shift;
	my $class = ref($this) || $this;
	my %params = @_;
	my %self = ();
	

	# Verifica parametri obbligatori
	for my $mandatory ( qw/RA ID/ ) {
		croak "Missing mandatory argument: $mandatory\n"
			unless exists $params{$mandatory};
		croak "Uninitialized mandatory argument: $mandatory\n"
			unless defined $params{$mandatory};
		$self{$mandatory} = delete $params{$mandatory};
	}
	# Verifica parametri opzionali
	for my $optional (qw/PLSQL/) {
		$self{$optional} = delete $params{$optional};
	}
	# Argomenti non ammessi
	
	croak "Unknown arguments: " . join(',', keys(%params)) . "\n"
		if scalar(keys(%params)) > 0;
	# Test tipo oggetto RA
	croak "Unexpected object-type RA!\n"
		unless ref($self{RA}) =~ '^SIRTI::ART::RemoteActivity';

	$self{PLSQL}=$self{RA}->get_plsql unless defined  $self{PLSQL};
	$self{SESSION_TOKEN} = $self{RA}->get_session_token;
	my $self = bless(\%self, $class);
	
	# Popola le informazioni dell'evento
	$self->_init();
	
	# Se la chiave INFO non e' valorizzata il caricamento non e' andato a buon fine
	return undef unless defined $self->{INFO};
	
	# Ritorna il riferimento all'oggetto 
	return $self;
}

sub new { return _new(@_); }


# 
# Metodi per l'interrogazione dell'evento: ritornano le rispettive chiavi
#
=pod

=head2 get_id()

Restituiscono l'id dell'evento

=cut 
sub get_id { $_[0]->{ID} }

=pod

=head2 get_ra

Restituisce l'oggetto Remote Activity

=cut 

sub get_ra { $_[0]->{RA} }

=pod

=head2 get_session()

Restituisce le informazioni sulla sessione

=cut 

sub get_session { $_[0]->{SESSION} }

=pod

=head2 get_info()

Restituisce le informazioni dell'evento

=cut 

sub get_info { $_[0]->{INFO} }

=pod

=head2 get_data()

Restituisce i dati dell'evento

=cut 

sub get_data { $_[0]->{DATA} }

=pod

=head2 get_lookup()

Restituisce le informazioni sull'eventuale LOOKUP

=cut

sub get_lookup { $_[0]->{LOOKUP} }

=pod

=head2 get_parent_id()

Restituisce le informazioni sull'eventuale ID padre dell'evento

=cut

sub get_parent_id {
	my $self = shift;
	my $ra = $self->get_ra();
	# Esegue l'elaborazione attraverso il metodo della classe SIRTI::ART::RemoteActivity::Event
	my $n=$ra->get_plsql->get_parent_id(
		EVENT_ID => $self->get_id
		, SESSION_TOKEN => $self->{SESSION_TOKEN}
	);
	return $n;
}

=pod

=head2 get_targets()

Restituisce gli oggetti degli eventuali eventi figli

=cut

sub get_targets {
	my $self = shift;
	my $ra = $self->get_ra();
	$ra->_set_date_format();
	# Esegue l'elaborazione attraverso il metodo della classe SIRTI::ART::RemoteActivity::Event
	my $n=$ra->get_plsql->get_children_id(
		EVENT_ID => $self->get_id
		, SESSION_TOKEN => $self->{SESSION_TOKEN}
	);
	my @children;
	foreach (@{$n}) {
		my $class = SIRTI::ART::RemoteActivity::Event->new(
			RA => $self->{RA}
     		,ID => $_
		);
		push (@children, $class);
	}
	$ra->_unset_date_format();
	return @children;
}

=pod

=head2 get_event_name()

Restituisce il nome dell'evento

=cut

sub get_event_name {$_[0]->{INFO}->{EVENT}}

=pod

=head2 is_pending()

Restituisce true se l'evento e' pending

=cut

sub is_pending {
	my $self = shift;
	$self->_init();
	return ( !defined($self->get_info()->{STATUS}) );
}

=pod

=head2 cancel()

Metodo per l'annullamento (CANCELling) di un evento

=cut

sub cancel {
	my ($self,$reason,%params) = @_;
	my $ra = $self->get_ra();
	# Testa se l'oggetto REMOTE_ACTIVITY (RA) consente di gestire l'annullamento (solo Source)
	croak "Unable to cancel in " . ref($ra) . " mode!\n"
		unless $ra->can('_cancel');
	# Inizializza la sessione (se non gia' inizializzata)
	croak "Unable to initialize SESSION!\n"
		unless $ra->init_session();
	# Verifica se l'evento e' ancora pending 
	return 0
		unless $self->is_pending;

	$ra->_set_date_format();
	# Esegue l'annullamento attraverso il metodo della classe SIRTI::ART::RemoteActivity::Event
	if ( $ra->_cancel( ID => $self->get_id() , REASON => $reason ) ) {
		$self->_init();
		$ra->_unset_date_format();
		return 1;
	} else {
		return 0;
	}
}

=pod

=head2 fetch()

Metodo per l'elaborazione (FETCHing) di un evento

=cut
sub fetch {
	my $self = shift;
	my ($target_ref, $status, $reason,$data) = @_;
	# STATUS deve essere 0:OK o 1:KO
	croak "Bad value for STATUS, must be between 0 and 99999!\,"
		unless $status =~ /^[0-9]{1,5}$/;
	my $ra = $self->get_ra();
	# Testa se l'oggetto REMOTE_ACTIVITY (RA) consente di gestire l'elaborazione (solo Target)
	croak "Unable to fetch in " . ref($ra) . " mode!\n"
		unless $ra->can('_fetch');
	# Inizializza la sessione (se non gia' inizializzata)
	croak "Unable to initialize SESSION!\n"
		unless $ra->init_session();
	# Verifica se l'evento e' ancora pending 
	return 0
		unless $self->is_pending;
	
	$ra->_set_date_format();

	# Esegue l'elaborazione attraverso il metodo della classe SIRTI::ART::RemoteActivity::Event
	my $n=$ra->get_plsql->event_fetch(
		EVENT_ID => $self->get_id()
		, TARGET_REF => $target_ref
		, STATUS => $status
		, REASON => $reason
		, SESSION_TOKEN => $self->{SESSION_TOKEN}
	);
	my $count=$ra->get_plsql->data_ack_event_operation(OP_INSERT,$self->get_id(),DATA => $data) if $data;
	$self->_init() if ($n);
	$ra->_unset_date_format();
	return $n;
}

=pod

=head2 lookup()

Metodo per l'analisi senza lavorazione (LOOKUP) di un evento

=cut

sub lookup {
	my $self = shift;
	my ($reason) = @_;
	my $ra = $self->get_ra();
	# Testa se l'oggetto REMOTE_ACTIVITY (RA) consente di gestire l'analisi senza elaborazione (solo Target)
	croak "Unable to lookup in " . ref($ra) . " mode!\n"
		unless $ra->can('_lookup');
	# Inizializza la sessione (se non gia' inizializzata)
	croak "Unable to initialize SESSION!\n"
		unless $ra->init_session();
	# Verifica se l'evento e' ancora pending 
	croak "Event " . $self->get_id() . " is not pending!\n"
		unless $self->is_pending;
	# Esegue l'analisi senza elaborazione attraverso il metodo della classe SIRTI::ART::RemoteActivity::Event

	$ra->_set_date_format();
	$ra->get_plsql->ignore_event_pending(
		EVENT_ID => $self->get_id()
		,REASON => $reason
		,SESSION_TOKEN => $self->{SESSION_TOKEN} 
	);
	$self->_init();
	$ra->_unset_date_format();
	return 1;
}


=pod

=head2 get_source_ref( ), get_target_ref( )

Restituiscono i riferimenti del mittente e del destinatario dell'evento/messaggio

=cut 

sub get_source_ref { $_[0]->{INFO}->{SOURCE_REF} }

sub get_target_ref { $_[0]->{INFO}->{TARGET_REF} }

=pod

=head2 get_status( ) 

Restituisce il valore dello stato dell'evento/messaggio

=cut 

sub get_status { $_[0]->{INFO}->{STATUS} }

=pod

=head2 need_ack( ) 

Restituisce I<VERO> se l'evento/messaggio richiede un acknowledge di lettura

=cut 



sub need_ack { $_[0]->get_info()->{NEED_ACK} }

=pod

=head2 get_ack( ) 

Restituisce le informazioni relative all'acknowledge dell'evento/messaggio

=cut 

sub get_ack { $_[0]->{ACK} }

=pod

=head2 get_data_ack( ) 

Restituisce i dati relativi all'acknowledge dell'evento/messaggio

=cut 

sub get_data_ack { $_[0]->{DATA_ACK} }

=pod

=head2 is_alive( ) 

Restituisce I<VERO> se l'evento/messaggio è ancora valido

=cut 

sub is_alive { $_[0]->get_info()->{ALIVE} }

=pod

=head2 is_pending_ack( ) 

Restituisce I<VERO> se l'acknowledge della lettura dell'evento e' 'pending'

=cut 


sub is_pending_ack {
	my $self = shift;
	$self->_init();
	return ( $self->need_ack() && !defined($self->get_ack()) );
}


=pod

=head1 NOTES

=over 4

=item Nessuna nota.

=back

=cut


=pod

=head1 BUGS

Eventuali bug riscontrati dovranno essere segnalati su B<ART - Global Services>: L<https://www.artnet.sirtisistemi.net/ARTIT/art> aprendo un apposito DEV-TICKET.

=head1 HISTORY

=over

=item Ver. 0.04

Prima release del modulo

=back

=head1 AUTHOR

Alvaro Livraghi <<EMAIL>>

=cut

1;


__END__
