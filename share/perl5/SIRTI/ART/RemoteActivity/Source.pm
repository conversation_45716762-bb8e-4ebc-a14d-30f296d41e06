################################################################################
#
# SIRTI::ART::RemoteActivity::Source
#
################################################################################

=head1 NAME

B<SIRTI::ART::RemoteActivity::Source> - Gestione evento/messaggio

=head1 SYNOPSIS

  Modalita' Destinatario unico

  my $ra = SIRTI::ART::RemoteActivity::Source->new( 
	 DB                  => SIRTI::DB->new('user/pwd@host')
	,SESSION_DESCRIPTION => 'Big-bang test session!!!!'
	,SOURCE_SERVICE      => 'source_service'
	,SOURCE_CONTEXT      => 'source_context'
	,TARGET_SERVICE      => 'target_service'
	,TARGET_CONTEXT      => 'target_context'
  );

  Modalita' Destinatario Multiplo

  my $ra = SIRTI::ART::RemoteActivity::Source->new( 
	 DB                  => SIRTI::DB->new('user/pwd@host')
	,SESSION_DESCRIPTION => 'Big-bang test session!!!!'
	,SOURCE_SERVICE      => 'source_service'
	,SOURCE_CONTEXT      => 'source_context'
	,TARGET				 => {
		'target_service' => ['target_context', 'target_context']
		,'target_service' => 'target_context'
		,'target_service' => 'target_context'
	}
  );

  Modalita' Destinatario locale

  my $ra = SIRTI::ART::RemoteActivity::Source->new( 
     DB                  => SIRTI::DB->new('user/pwd@host')
    ,SESSION_DESCRIPTION => 'Big-bang test session!!!!'
	,SOURCE_CONTEXT      => 'source_context'
	,TARGET_CONTEXT      => 'target_context'
  );
  
=head1 DESCRIPTION

La classe B<SIRTI::ART::RemoteActivity::Source> consente di inviare ad un sistema 
remoto un messaggio/evento in modo completamente asincrono e garantendo, allo 
stesso tempo, il disaccoppiamento del sistema mittente (I<source>) da quelli di 
destinazione (I<target>).

=cut

package SIRTI::ART::RemoteActivity::Source;

use base qw(SIRTI::ART::RemoteActivity);

use strict;
use warnings;
use Carp; # 'croak';

use SIRTI::PLSQL::RM_ACTIVITY qw(:op);

################################################################################
#  P R O P R I E T A '   P U B B L I C H E
################################################################################

our $VERSION = '0.04';



################################################################################
#  P R O P R I E T A '   P R I V A T E
################################################################################



################################################################################
#  M E T O D I   P R I V A T I
################################################################################

# Script di inizializzazione del package
sub _init {
	$_[0]->{RA_DBLINK} = '';
	1; 
}  


sub _cancel {
	my $self = shift;
	my %params = @_; 
	# Inizializza la sessione (se non gia' inizializzata)
	croak "Unable to initialize SESSION!\n"
		unless $self->init_session();
	# Annulla l'evento utilizzando la prepare
	my $plsql=$self->get_plsql;
	$self->_set_date_format();
	my $n=$plsql->cancel_pending_event(EVENT_ID => $params{ID},REASON => $params{REASON});
	$self->_unset_date_format();
	return $n;
}


sub _fetch_ack {
	my ($self,%params)=@_;
	# Inizializza la sessione (se non gia' inizializzata)
	croak "Unable to initialize SESSION!\n"
			unless $self->init_session();
	# Marca come "letto" l'acknowledge utilizzando la prepare
	$self->_set_date_format();
	my $fetch = $self->get_plsql->fetch_ack(
		EVENT_ID			=> $params{ID}
		,NOTE				=> $params{NOTE}
		,SESSION_TOKEN		=> $self->get_session_token
	);
	$self->_unset_date_format();

	return $fetch;
}

sub _fetch_target_ack {
	my ($self,%params)=@_;
	# Inizializza la sessione (se non gia' inizializzata)
	croak "Unable to initialize SESSION!\n"
			unless $self->init_session();
	# Marca come "letto" l'acknowledge utilizzando la prepare
	$self->_set_date_format();
	my $fetch = $self->get_plsql->fetch_target_ack(
		EVENT_ID			=> $params{ID}
		,NOTE				=> $params{NOTE}
	);
	$self->_unset_date_format();
	return $fetch;
}

################################################################################
#  M E T O D I   P U B L I C I
################################################################################

=pod

=head1 METHODS

=cut


=pod

=head2 new( DB => I<SIRTI::DB>, SESSION_DESCRIPTION => I<SCALAR>, SOURCE_SERVICE => I<SCALAR>, SOURCE_CONTEXT => I<SCALAR>, TARGET_SERVICE => I<SCALAR>, TARGET_CONTEXT => I<SCALAR> [ , USER_ID => I<SCALAR> ], TARGET =>I<HASHREF> )

=over 4

=item * DB

Oggetto/connect-string per l'accesso al DB. Puo' essere uno fra:

=item * SESSION_DESCRIPTION

Descrizione della sessione corrente

=item * SOURCE_SERVICE

IDentificativo del sistema sorgente

=item * SOURCE_CONTEXT

IDentificativo del sistema sorgente. Da valorizzare in caso di destinatario unico o destinatario
multiplo, da non valorizzare in caso di destinatario locale

=item * TARGET_SERVICE

IDentificativo del sistema destinatario. Da valorizzare in caso di destinatario unico, da non
valorizzare in caso di destinatrio multiplo (vedi parametro TARGET) o di destinatario locale

=item * TARGET_CONTEXT

IDentificativo del contesto destinatario. Da valorizzare in caso di destinatario unico o
destinatario locale, da non valorizzare in caso di destinatrio multiplo (vedi parametro TARGET)

=item * TARGET

Array associativo che ha come chiavi i servizi target e come valori stringhe (o array) che
identificano i contesti target. Da valorizzare in caso di destinatario multiplo, da non
valorizzare in caso di destinatario singolo o destinatario locale

=item * RA_DBLINK 

Indica il nome del DBLINK da utilizzare per accedere agli eventi/messaggi; se omesso, valorizzato a undef o a stringa vuota ('') verra' utilizzato il database locale

=item * USER_ID

 IDentificativo dell'operatore che ha richiesto l'inserimento dell'evento

=item * USER_NAME

 LOGIN_OPERATORE della tabella OPERATORI di ART

=item * DATE_FORMAT

Formato da utilizzare per la manipolazione delle date (default: "YYYYMMDDHH24MISS")

=back

=cut


=head2 get_session_description(), get_source_service(), get_source_context(), get_target_service(), get_target_context(), get_target(), get_dblink(), debug(), get_date_format(), get_user_id(), get_user_name()

Restituiscono le informazioni fornite al metodo B<new()>


=head2 get_service_list(), get_context_list()

Restituiscono la lista dei servizi e dei contesti configurati sul sistemna "locale"

=head2 get_source_service_info(), get_target_service_info()

Restituiscono informazioni addizionali sui servizi e sui contesti configurati sul sistemna "locale"

=head2 get_session_id()

Restituisce l'ID della sessione corrente

=head2 get_ip(), get_host()

Restituisce l'indirizzo I<IP> e l'I<host-name> della macchina su cui e' in esecuzione l'applicazione

=head2 get_os_user()

Restituisce il nome dell'utente unix che ha eseguito l'applicazione corrente

=head2 get_session_cmdline()

Restituisce la I<command line> dell'applicazione in esecuzione

=head2 is_session_ready()

Verifica se la sessione corrente e' "pronta" per recepire eventi/messaggi

=head2 set_session_ready()

Abilita la sessione corrente a recepire eventi/messaggi.

=head2 init_session()

Abilita la sessione corrente a recepire eventi/messaggi (se non lo era).


=head2 get_event( ID => I<SCALAR> )

Restituisce l'oggetto B<SIRTI::ART::RemoteActivity::Event> che rappresenta l'evento/messaggio identificato da I<ID>.

=cut

sub get_source_service { &{ $_[0] }("SOURCE_SERVICE") }
sub get_session_description { &{ $_[0] }("SESSION_DESCRIPTION") }
sub get_source_context { &{ $_[0] }("SOURCE_CONTEXT") }
sub get_target_service { &{ $_[0] }("TARGET_SERVICE") }
sub get_target_context { &{ $_[0] }("TARGET_CONTEXT") }
sub get_target { &{ $_[0] }("TARGET") }


=pod

=head2 insert( SOURCE_REF => I<SCALAR>, EVENT => I<SCALAR>, DATA => I<HASHREF> [, SCHEDULE_DATE => I<SCALAR>, EXPIRY_DATE => I<SCALAR>, NEED_ACK => I<SCALAR> ] )

Inserisce un nuovo evento in cui:

=over 4

=item * B<SOURCE_REF>

Deve contenere un riferimento (univoco o meno) utile al destinatario del messaggio per identificare l'oggetto del messaggio

=item * B<EVENT>

Deve contenere un nome (o tipo) di evento riconoscibile dal destinatario del messaggio

=item * B<DATA> 

Deve contenere un elenco di coppie chiave/valore associati al messaggio

=item * B<SCHEDULE_DATE>

Se specificato, rappresenta la data (nel formato I<DATE_FORMAT> specificato nel costruttore di classe) oltre la quale il messaggio potra' essere "consumato" dal destinatario

=item * B<EXPIRY_DATE>

Se specificato, rappresenta la data (nel formato I<DATE_FORMAT> specificato nel costruttore di classe) entro la quale il messaggio potra' essere "consumato" dal destinatario

=item * B<NEED_ACK>

Se specificato, fa in modo che, nel momento in cui viene "consumato" il messaggio, venga generato un acknowledge verso il mittente del messaggio stesso; l'unico valore ammesso e' "1"

=back

Ritorna uno scalare che rappresenta un IDentificativo numerico univoco se l'evento/messaggio e' stato correttamente inserito.

Ritorna 0 se si e' verificata un'anomalia.

Esempio:

  my $rc = $ra->insert(
     EVENT          => 'KILL',
     SOURCE_REF     => 'Bill',
     DATA           => {
         weapon => 'katana',
         when   => '5 a.m.'
     },
     SCHEDULE_DATE  => '20120117000000',
     EXPIRY_DATE    => '20120118000000',
     NEED_ACK       => 1
  );
  if ( $rc > 0 ) {
      print "Message queued with ID $rc", "\n";
  } else {
      print "Message not queued!", "\n";
  }

=cut

sub insert {
	my $self = shift;
	my %args = @_;
	my %params = ();
	# Verifica parametri obbligatori
	for my $mandatory (qw/SOURCE_REF EVENT DATA/) {
		croak "Missing mandatory argument: $mandatory\n"
			unless exists $args{$mandatory};
		croak "Uninitialized mandatory argument: $mandatory\n"
			unless defined $args{$mandatory};
		$params{$mandatory} = delete $args{$mandatory};
	}
	# Verifica parametri NON obbligatori
	for my $optional (qw/SCHEDULE_DATE EXPIRY_DATE NEED_ACK/) {
		$params{$optional} = delete $args{$optional};
	}
	# Argomenti non ammessi
	croak "Unknown arguments: " . join(',', keys(%args)) . "\n"
		if scalar(keys(%args)) > 0;
	
	# Argomenti non validi
	croak "Bad NEED_ACK value: " . $params{NEED_ACK} . "\n"
		if defined($params{NEED_ACK}) && $params{NEED_ACK} != 1;
	
	# Inizializza la sessione (se non gia' inizializzata)
	croak "Unable to initialize SESSION!\n"
		unless $self->init_session();
	
	my $plsql=$self->get_plsql;
	
	$self->_set_date_format();
	my $ra_id = $plsql->event_operation(
		OP_INSERT
		,SESSION_TOKEN => $self->get_session_token
		,SOURCE_REF => $params{SOURCE_REF}
		,EVENT => $params{EVENT}
	    ,SCHEDULE_DATE => $params{SCHEDULE_DATE}    
	    ,EXPIRY_DATE => $params{EXPIRY_DATE}
	    ,NEED_ACK => $params{NEED_ACK}
	);
	
	my $count=$plsql->data_event_operation(OP_INSERT,$ra_id,DATA => $params{DATA});

	$self->_unset_date_format();
	return $ra_id;
}

=pod

=head2 find_pending( [ SOURCE_REF => I<SCALAR>|I<ARRAYREF> , EVENT => I<SCALAR>|I<ARRAYREF> ] )

Restituisce (in contesto di lista) l'elenco degli ID degli eventi che rispettano i requisiti 
specificati oppure (in contesto scalare) semplicemente il numero di eventi 'pending'.

=over 4

=item * B<SOURCE_REF> rappresenta il riferimento (se I<SCALAR>) o i riferimenti (se I<ARRAYREF>) espresso/i dal mittente

=item * B<EVENT> rappresenta il tipo di evento (se I<SCALAR>) o i tipi di evento (se I<ARRAYREF>) espresso/i dal mittente

=back


=head2 find_first_pending( [ SOURCE_REF => I<SCALAR>|I<ARRAYREF> , EVENT => I<SCALAR>|I<ARRAYREF> ] )

Analogamente al metodo B<find_pending()>, restituisce l'ID dell'ultimo evento pending (non ancora gestito) che rispetta i reauisiti specificati.


=head2 find_last_pending( [ SOURCE_REF => I<SCALAR>|I<ARRAYREF> , EVENT => I<SCALAR>|I<ARRAYREF> ] )

Analogamente al metodo B<find_pending()>, restituisce l'ID del primo evento pending (non ancora gestito) che rispetta i reauisiti specificati.


=cut

=pod

=head2 cancel( ID => I<SCALAR>, REASON => I<SCALAR>  )

Annulla un evento 'pending'

=cut

sub cancel {
	my ($self,%args) = @_;
	my %params = ();
	# Verifica parametri obbligatori
	for my $mandatory (qw/ID REASON/) {
		croak "Missing mandatory argument: $mandatory\n"
			unless exists $args{$mandatory};
		croak "Uninitialized mandatory argument: $mandatory\n"
			unless defined $args{$mandatory};
		$params{$mandatory} = $args{$mandatory};
		delete $args{$mandatory};
	}
	# Verifica parametri NON obbligatori
	for my $optional (qw//) {
		$params{$optional} = $args{$optional};
		delete $args{$optional};
	}
	# Argomenti non ammessi
	croak "Unknown arguments: " . join(',', keys(%args)) . "\n"
		if scalar(keys(%args)) > 0;
	
	$self->_set_date_format();
	# Istanzia un nuovo oggetto 'evento' per effettuare la cancellazione
	my $e = SIRTI::ART::RemoteActivity::Event->new(
		 RA				=> $self
		,ID				=> $params{ID}
		,PLSQL			=> $self->get_plsql
	);
	croak "Event $params{ID} not found|\n"
		unless defined($e);
	my $cancel = $e->cancel($params{REASON});
	$self->_unset_date_format();
	return $cancel;
}
		

=pod

=head2 fetch_ack( ID => I<SCALAR> [ , NOTE => I<SCALAR> ] )

Marca come "letto" l'acknowledge di un evento "consumato"

=cut
sub fetch_ack {
	my $self = shift;
	my %args = @_;
	my %params = ();
	# Verifica parametri obbligatori
	for my $mandatory (qw/ID/) {
		croak "Missing mandatory argument: $mandatory\n"
			unless exists $args{$mandatory};
		croak "Uninitialized mandatory argument: $mandatory\n"
			unless defined $args{$mandatory};
		$params{$mandatory} = $args{$mandatory};
		delete $args{$mandatory};
	}
	# Verifica parametri NON obbligatori
	for my $optional (qw/NOTE/) {
		$params{$optional} = $args{$optional};
		delete $args{$optional};
	}
	# Argomenti non ammessi
	croak "Unknown arguments: " . join(',', keys(%args)) . "\n"
		if scalar(keys(%args)) > 0;
	
	$self->_set_date_format();
	my $fetch = $self->_fetch_ack( 
		ID => $params{ID}
		,NOTE => $params{NOTE} 
	);
	$self->_unset_date_format();

	return $fetch;
}


=pod

=head2 find_pending_ack( [ SOURCE_REF => I<SCALAR>|I<ARRAYREF> , EVENT => I<SCALAR>|I<ARRAYREF> ] )

In contesto di lista, restituisce l'elenco degli ID degli eventi, per i quali, rispettando i requisiti 
specificati, per cui l'acknowledge non e' ancora stato gestito'.

In contesto scalare restituisce semplicemente il numero degli eventi impattati.

=over 4

=item * B<SOURCE_REF> rappresenta il riferimento (se I<SCALAR>) o i riferimenti (se I<ARRAYREF>) espresso/i dal mittente

=item * B<EVENT> rappresenta il tipo di evento (se I<SCALAR>) o i tipi di evento (se I<ARRAYREF>) espresso/i dal mittente

=back


=head2 find_first_pending_ack( [ SOURCE_REF => I<SCALAR>|I<ARRAYREF> , EVENT => I<SCALAR>|I<ARRAYREF> ] )

Analogamente al metodo B<find_pending_ack()>, restituisce l'ID del primo evento il cui acknowledge non e' ancora stato gestito


=head2 find_last_pending_ack( [ SOURCE_REF => I<SCALAR>|I<ARRAYREF> , EVENT => I<SCALAR>|I<ARRAYREF> ] )

Analogamente al metodo B<find_pending_ack()>, restituisce l'ID dell'ultimo evento il cui acknowledge non e' ancora stato gestito

=cut

sub _find_pending_ack {
	my ($self,%params)=@_;
	$self->_set_date_format();
	my $cur=$self->get_plsql->find_pending_ack(%params,SESSION_TOKEN => $self->get_session_token);
	my @rows=();
	while(my $row=$cur->fetchrow_arrayref) {
		push @rows,$row->[0];
	};
	$cur->finish;
	$self->_unset_date_format();
	return \@rows;
}

sub find_pending_ack {
	my ($self,%args)=@_;
	my %params = ();
	# Verifica parametri obbligatori
	for my $mandatory (qw//) {
		croak "Missing mandatory argument: $mandatory\n"
			unless exists $args{$mandatory};
		croak "Uninitialized mandatory argument: $mandatory\n"
			unless defined $args{$mandatory};
		$params{$mandatory} = delete $args{$mandatory};
	}
	# Verifica parametri NON obbligatori
	for my $optional (qw/EVENT SOURCE_REF/) {
		# trick per evitare il bind con casting sbagliato nelle dbi....
		$args{$optional} = substr( qq/$args{$optional} /, 0, -1 )
			if defined $args{$optional} && !ref $args{$optional};
		$params{$optional} = delete $args{$optional};
	}
	# Argomenti non ammessi
	croak "Unknown arguments: " . join(',', keys(%args)) . "\n"
		if scalar(keys(%args)) > 0;
	
	my @events = ();
	push @events, $self->_uniq(@{ $params{EVENT} }) if ref($params{EVENT}) eq 'ARRAY';
	push @events, $params{EVENT} if defined($params{EVENT}) && ref($params{EVENT}) eq '';

	my @source_refs = ();
	push @source_refs, $self->_uniq(@{ $params{SOURCE_REF} }) if ref($params{SOURCE_REF}) eq 'ARRAY';
	push @source_refs, $params{SOURCE_REF} if defined($params{SOURCE_REF}) && ref($params{SOURCE_REF}) eq '';

	# Evita (o dovrebbe evitare) il problema della visibilita' degli eventi 
#	$self->_get_dummy_prepare()->fetchall_arrayref();

	$self->_set_date_format();
	# Ricerca utilizzando la prepare in base alla presenza/assenza parametri in input
	my $tmp = [];
	SWITCH: {
		if ( scalar(@source_refs) && scalar(@events) ) {
			for my $e (@events) {
				for my $r (@source_refs) {
					push @$tmp, @{ $self->_find_pending_ack(SOURCE_REF => $r,EVENT => $e) };
				}
			}
			last SWITCH;
		}
		if ( scalar(@source_refs) ) {
			for my $r (@source_refs) {
				push @$tmp,@{ $self->_find_pending_ack(SOURCE_REF => $r) };
			}
			last SWITCH;
		}
		if ( scalar(@events) ) {
			for my $e (@events) {
				push @$tmp, @{ $self->_find_pending_ack(EVENT => $e) }
			}
			last SWITCH;
		}
		push @$tmp, @{ $self->_find_pending_ack };
	}
	$self->_unset_date_format();
#	# Ricerca utilizzando la prepare in base alla presenza/asenza parametri in input
#	my $tmp = $self->_get_find_pending_ack_prepare()->fetchall_arrayref();
	# Riconduce i risultati ad un array in quanto SIRTI::DB::fetchall_arrayref() ritorn un ARRAYREF di ARRAYREF
	# Verifica se ritornare un array o il numero di elementi dell'array
	return ( wantarray ? sort({ $a <=> $b } @$tmp) : scalar(@$tmp) );
}


sub find_first_pending_ack {
	my $self = shift;
	$self->_set_date_format();
	my @res = $self->find_pending_ack(@_);
	$self->_unset_date_format();
	return $res[0];
}
sub find_last_pending_ack {
	my $self = shift;
	$self->_set_date_format();
	my @res = $self->find_pending_ack(@_);
	$self->_unset_date_format();
	return $res[-1];
}


=pod

=head2 find_pending_target_ack( [ SOURCE_REF => I<SCALAR>|I<ARRAYREF> , EVENT => I<SCALAR>|I<ARRAYREF> ] )

In contesto di lista, restituisce l'elenco degli ID degli eventi, per i quali, rispettando i requisiti 
specificati, per cui l'acknowledge dei singoli target non e' ancora stato gestito'.

In contesto scalare restituisce semplicemente il numero degli eventi impattati.

=over 4

=item * B<SOURCE_REF> rappresenta il riferimento (se I<SCALAR>) o i riferimenti (se I<ARRAYREF>) espresso/i dal mittente

=item * B<EVENT> rappresenta il tipo di evento (se I<SCALAR>) o i tipi di evento (se I<ARRAYREF>) espresso/i dal mittente

=back


=head2 find_first_pending_target_ack( [ SOURCE_REF => I<SCALAR>|I<ARRAYREF> , EVENT => I<SCALAR>|I<ARRAYREF> ] )

Analogamente al metodo B<find_pending_ack()>, restituisce l'ID del primo evento il cui acknowledge del singolo target non e' ancora stato gestito


=head2 find_last_pending_target_ack( [ SOURCE_REF => I<SCALAR>|I<ARRAYREF> , EVENT => I<SCALAR>|I<ARRAYREF> ] )

Analogamente al metodo B<find_pending_ack()>, restituisce l'ID dell'ultimo evento il cui acknowledge del singolo target non e' ancora stato gestito

=cut

sub _find_pending_target_ack {
	my ($self,%params)=@_;
	$self->_set_date_format();
	my $cur=$self->get_plsql->find_pending_target_ack(%params,SESSION_TOKEN => $self->get_session_token);
	my @rows=();
	while(my $row=$cur->fetchrow_arrayref) {
		push @rows,$row->[0];
	};
	$cur->finish;
	$self->_unset_date_format();
	return \@rows;
}

sub find_pending_target_ack {
	my ($self,%args)=@_;
	my %params = ();
	# Verifica parametri obbligatori
	for my $mandatory (qw//) {
		croak "Missing mandatory argument: $mandatory\n"
			unless exists $args{$mandatory};
		croak "Uninitialized mandatory argument: $mandatory\n"
			unless defined $args{$mandatory};
		$params{$mandatory} = delete $args{$mandatory};
	}
	# Verifica parametri NON obbligatori
	for my $optional (qw/EVENT SOURCE_REF/) {
		# trick per evitare il bind con casting sbagliato nelle dbi....
		$args{$optional} = substr( qq/$args{$optional} /, 0, -1 )
			if defined $args{$optional} && !ref $args{$optional};
		$params{$optional} = delete $args{$optional};
	}
	# Argomenti non ammessi
	croak "Unknown arguments: " . join(',', keys(%args)) . "\n"
		if scalar(keys(%args)) > 0;
	
	my @events = ();
	push @events, $self->_uniq(@{ $params{EVENT} }) if ref($params{EVENT}) eq 'ARRAY';
	push @events, $params{EVENT} if defined($params{EVENT}) && ref($params{EVENT}) eq '';

	my @source_refs = ();
	push @source_refs, $self->_uniq(@{ $params{SOURCE_REF} }) if ref($params{SOURCE_REF}) eq 'ARRAY';
	push @source_refs, $params{SOURCE_REF} if defined($params{SOURCE_REF}) && ref($params{SOURCE_REF}) eq '';

	# Evita (o dovrebbe evitare) il problema della visibilita' degli eventi 
#	$self->_get_dummy_prepare()->fetchall_arrayref();

	$self->_set_date_format();
	# Ricerca utilizzando la prepare in base alla presenza/assenza parametri in input
	my $tmp = [];
	SWITCH: {
		if ( scalar(@source_refs) && scalar(@events) ) {
			for my $e (@events) {
				for my $r (@source_refs) {
					push @$tmp, @{ $self->_find_pending_target_ack(SOURCE_REF => $r,EVENT => $e) };
				}
			}
			last SWITCH;
		}
		if ( scalar(@source_refs) ) {
			for my $r (@source_refs) {
				push @$tmp,@{ $self->_find_pending_target_ack(SOURCE_REF => $r) };
			}
			last SWITCH;
		}
		if ( scalar(@events) ) {
			for my $e (@events) {
				push @$tmp, @{ $self->_find_pending_target_ack(EVENT => $e) }
			}
			last SWITCH;
		}
		push @$tmp, @{ $self->_find_pending_target_ack };
	}
	$self->_unset_date_format();
#	# Ricerca utilizzando la prepare in base alla presenza/asenza parametri in input
#	my $tmp = $self->_get_find_pending_ack_prepare()->fetchall_arrayref();
	# Riconduce i risultati ad un array in quanto SIRTI::DB::fetchall_arrayref() ritorn un ARRAYREF di ARRAYREF
	# Verifica se ritornare un array o il numero di elementi dell'array
	return ( wantarray ? sort({ $a <=> $b } @$tmp) : scalar(@$tmp) );
}


sub find_first_pending_target_ack {
	my $self = shift;
	$self->_set_date_format();
	my @res = $self->find_pending_target_ack(@_);
	$self->_unset_date_format();
	return $res[0];
}
sub find_last_pending_target_ack {
	my $self = shift;
	$self->_set_date_format();
	my @res = $self->find_pending_target_ack(@_);
	$self->_unset_date_format();
	return $res[-1];
}


=pod

=head1 NOTES

=over 4

=item Nessuna nota.

=back

=cut


=pod

=head1 BUGS

Eventuali bug riscontrati dovranno essere segnalati su B<ART - Global Services>: L<https://www.artnet.sirtisistemi.net/ARTIT/art> aprendo un apposito DEV-TICKET.

=head1 HISTORY

=over

=item Ver. 0.04

Prima release del modulo

=back

=head1 AUTHOR

Alvaro Livraghi <<EMAIL>>

=cut


###################################
#
# Testing the module...
#
###################################
if (__FILE__ eq $0) {

	####  test ###  
	package main;

	use strict;
	use warnings;
	use Carp; 



	eval('
			use SIRTI::DB::Oracle;
			use Data::Dumper;
	');

	if ($@) {
		print STDERR $@;
		exit 1;
	}

	my $DEBUG = 1;

	my $sqlid=$ENV{SQLID_DRAULL};
	$sqlid=$ENV{SQLID} unless defined $sqlid;

	my $db=SIRTI::DB::Oracle->new($sqlid, DEBUG => 0,DBMS_OUTPUT => 1);

	my $w = eval {
		SIRTI::ART::RemoteActivity::Source->new( 
			DB => $db
			,SESSION_DESCRIPTION => 'Hello world!'
			,SOURCE_SERVICE    => 'LOCALHOST'
			,SOURCE_CONTEXT => 'GRO'
			,TARGET_SERVICE    => 'LOCALHOST'
			,TARGET_CONTEXT => 'GRO'
			,DATE_FORMAT => 'dd-mm-yyyy'
			,USER_ID	=> 999999
			,USER_NAME	=> 'ALVARO'
			,DEBUG => $DEBUG
		);
	};

	croak $@ if $@;

print STDERR "costruttore new effettuata\n";

	print "W: ", Dumper($w);
	print "DBH: ", Dumper($w->_dbh());
	print "SOURCE_SERVICE: ", Dumper $w->get_source_service();
	print "GET_SOURCE_SERVICE_INFO: ", Dumper $w->get_source_service_info();
	print "GET_SOURCE_CONTEXT: ", Dumper $w->get_source_context();
	print "GET_TARGET_SERVICE: ", Dumper $w->get_target_service();
	print "GET_TARGET_SERVICE_INFO: ", Dumper $w->get_target_service_info();
	print "GET_TARGET_CONTEXT: ", Dumper $w->get_target_context();
#	print "_GET_RA_PREPARE: ", Dumper $w->_get_ra_prepare();
	print "USER_ID: ", Dumper $w->get_user_id();
	print "USER_NAME: ", Dumper $w->get_user_name();

	my ($ref, $event) = ('100', 'OPEN');
	
	for $ref (1..10) {
		print "Insert for REF/EVENT=$ref/$event : ";
		print STDOUT ( $w->insert(
			 SOURCE_REF	=> $ref 
			,EVENT		=> $event
			,SCHEDULE_DATE => '23-02-2010'
			,EXPIRY_DATE => '25-02-2010'
			,DATA		=> {
				pippo => 0,
				pluto => 1,
				paperino => [ 2,3,4 ]
			}
		) ? 'OK' : 'KO');
		print "\n";
	}
	
	print "ALL EVENTS: ", Dumper( [ $w->find_pending() ] ), "\n";
	print "ALL EVENTS=$event: ", Dumper( [ $w->find_pending( EVENT => $event ) ] ), "\n";
	#$w->_dbh()->commit();
	#exit;
	
	#sleep 1;
	for $ref (1..10) {
		#print "Pending event(s) for REF/EVENT=$ref/$event ... ";
		print "Pending event(s) for EVENT=$event ... ";
		my @pending = $w->find_pending(
			# SOURCE_REF => $ref
			#,
			EVENT		=> $event
		);
		print Dumper \@pending;
		for my $id (@pending) {
			my $e = $w->get_event(ID => $id);
			print "EVENT($id) INFO: ", Dumper($e->get_info());
			print "EVENT($id) SESSION: ", Dumper($e->get_session());
			print "EVENT($id) DATA: ", Dumper($e->get_data());
			print "EVENT($id) LOOKUP: ", Dumper($e->get_lookup());
			print "Cancelling with EVENT... ", $e->cancel('Che menefutt\'amme\'!'), "\n";
			#print "Cancelling with RA... ", $w->cancel(ID=>$id,REASON=>'Che menefutt\'amme\'!'), "\n";
			print "EVENT($id) INFO: ", Dumper($e->get_info());
			print "EVENT($id) SESSION: ", Dumper($e->get_session());
			print "EVENT($id) DATA: ", Dumper($e->get_data());
			print "EVENT($id) LOOKUP: ", Dumper($e->get_lookup());
		}
#		for my $id (@pending) {
#			print "Cancelling event $id ...";
#			print $w->cancel(
#				ID => $id,
#				REASON => "Cancelling event $id"
#			);
#			print "\n";
#		}
		my $count = $w->find_pending(
			 SOURCE_REF => $ref
			,EVENT		=> $event
		);
		print "COUNT: $count\n";
	}

	$w->finish;
	print "ROLLBACK\n" and $w->_dbh()->rollback();
	#print "COMMIT\n" and $w->_dbh()->commit();
	$w=undef;
	exit 0;

}


1;



__END__

