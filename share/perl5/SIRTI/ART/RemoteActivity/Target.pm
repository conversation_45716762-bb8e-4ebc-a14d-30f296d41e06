################################################################################
#
# SIRTI::ART::RemoteActivity::Target
#
################################################################################

=head1 NAME

B<SIRTI::ART::RemoteActivity::Target> - Gestione evento/messaggio

=head1 SYNOPSIS

  Modalita' Source remoto

  my $w = SIRTI::ART::RemoteActivity::Target->new( 
	 DB                  => SIRTI::DB->new('user/pwd@host')
	,SESSION_DESCRIPTION => 'Big-bang test session!!!!'
	,SOURCE_SERVICE      => 'source_service'
	,SOURCE_CONTEXT      => 'source_context'
	,TARGET_SERVICE      => 'target_service'
	,TARGET_CONTEXT      => 'target_context'
  );
  
  Modalita' Source locale

  my $w = SIRTI::ART::RemoteActivity::Target->new( 
	 DB                  => SIRTI::DB->new('user/pwd@host')
	,SESSION_DESCRIPTION => 'Big-bang test session!!!!'
	,SOURCE_CONTEXT      => 'source_context'
	,TARGET_CONTEXT      => 'target_context'
  );

=head1 DESCRIPTION

La classe B<SIRTI::ART::RemoteActivity::Target> consente di recepire un 
messaggio/evento inviato da un sistema remoto in modo completamente asincrono e 
garantendo, allo stesso tempo, il disaccoppiamento del sistema di setinazione 
(I<target>) da quello mittente (I<source>).

=cut

package SIRTI::ART::RemoteActivity::Target;

use strict;
use warnings;
use Carp 'croak';
use base qw(SIRTI::ART::RemoteActivity);



################################################################################
#  P R O P R I E T A '   P U B B L I C H E
################################################################################

our $VERSION = '0.04';


################################################################################
#  M E T O D I   P R I V A T I
################################################################################


sub _init {
	my $self = shift;
	if ($self->{SOURCE_SERVICE} eq $SIRTI::ART::RemoteActivity::LOCALHOST_DEFAULT) {
		delete $self->{RA_DBLINK};
	} else {
	$self->{RA_DBLINK} = $self->{SOURCE_SERVICE}
		unless ( defined $self->{RA_DBLINK});
	}
} 

################################################################################
#  M E T O D I   P U B L I C I
################################################################################

=pod

=head1 METHODS

=cut


=pod

=head2 new( DB => I<SIRTI::DB>, SESSION_DESCRIPTION => I<SCALAR>, SOURCE_SERVICE => I<SCALAR>, SOURCE_CONTEXT => I<SCALAR>, TARGET_SERVICE => I<SCALAR>, TARGET_CONTEXT => I<SCALAR> [ RA_DBLINK => I<SCALAR> , USER_ID => I<SCALAR> , USER_NAME => I<SCALAR> , DATE_FORMAT => I<SCALAR> , DEBUG => I<SCALAR> ] )

=over 4

=item * B<DB> : oggetto di tipo I<SIRTI::DB>, I<ART::C_db*> oppure connect-string

=item * B<SESSION_DESCRIPTION> : descrizione della sessione corrente

=item * B<SOURCE_SERVICE> : IDentificativo del sistema sorgente

=item * B<SOURCE_CONTEXT> : IDentificativo del contesto sorgente

=item * B<TARGET_SERVICE> : IDentificativo del sistema destinatario

=item * B<TARGET_CONTEXT> : IDentificativo del contesto destinatario

=item * B<RA_DBLINK> : indica il nome del DBLINK da utilizzare per accedere agli eventi/messaggi; se omesso o valorizzato a I<undef> verra' utilizzato il valore indicato in B<SOURCE_SERVICE>; se impostato a stringa vuota ('') gli eventi/messaggi verranno "letti" dallo stesso DB. Da non utilizzare in caso di modalita' localhost.

=item * B<USER_ID> : IDentificativo dell'operatore che ha richiesto l'inserimento dell'evento

=item * B<USER_NAME> : LOGIN_OPERATORE della tabella OPERATORI di ART

=item * B<DATE_FORMAT> : formato da utilizzare per la manipolazione delle date (default: "YYYYMMDDHH24MISS")

=item * B<DEBUG> : se impostato ad "1" stampa su STDERR messaggi di debug

=back

=head2 get_session_description( ), get_source_service( ), get_source_context( ), get_target_service( ), get_target_context( ), get_dblink( ), debug( ), get_date_format( ), get_user_id( ), get_user_name( )

Restituiscono le informazioni fornite al metodo B<new()>


=head2 get_service_list( ), get_context_list( )

Restituiscono la lista dei servizi e dei contesti configurati sul sistemna "locale"

=head2 get_source_service_info( ), get_target_service_info( )

Restituiscono informazioni addizionali sui servizi e sui contesti configurati sul sistemna "locale"

=head2 get_session_id( )

Restituisce l'ID della sessione corrente

=head2 get_ip( ), get_host( )

Restituisce l'indirizzo I<IP> e l'I<host-name> della macchina su cui e' in esecuzione l'applicazione

=head2 get_os_user( )

Restituisce il nome dell'utente unix che ha eseguito l'applicazione corrente

=head2 get_session_cmdline( )

Restituisce la I<command line> dell'applicazione in esecuzione

=head2 is_session_ready( )

Verifica se la sessione corrente e' "pronta" per recepire eventi/messaggi

=head2 set_session_ready( )

Abilita la sessione corrente a recepire eventi/messaggi.

=head2 init_session( )

Abilita la sessione corrente a recepire eventi/messaggi (se non lo era).


=head2 get_event( ID => I<SCALAR> )

Restituisce l'oggetto B<SIRTI::ART::RemoteActivity::Event> che rappresenta l'evento/messaggio identificato da I<ID>.


=head2 find_pending( SOURCE_REF => I<SCALAR>, EVENT => I<SCALAR> )

Restituisce (in contesto di lista) l'elenco di ID degli eventi oppure (in contesto scalare) il numero di eventi 'pending'


=head2 find_first_pending( SOURCE_REF => I<SCALAR>, EVENT => I<SCALAR> )

Restituisce l'ID del primo evento pending (non ancora gestito)


=head2 find_last_pending( SOURCE_REF => I<SCALAR>, EVENT => I<SCALAR> )

Restituisce l'ID dell'ultimo evento pending (non ancora gestito)

=cut

sub get_source_service { &{ $_[0] }("SOURCE_SERVICE") }
sub get_session_description { &{ $_[0] }("SESSION_DESCRIPTION") }
sub get_source_context { &{ $_[0] }("SOURCE_CONTEXT") }
sub get_target_service { &{ $_[0] }("TARGET_SERVICE") }
sub get_target_context { &{ $_[0] }("TARGET_CONTEXT") }

=pod

=head2 fetch( ID => I<SCALAR>, TARGET_REF => I<SCALAR>, STATUS => I<SCALAR>, REASON => I<SCALAR>, DATA => I<HASHREF> )

Marca come "letto" ("consumato") un evento/messaggio:

=over 4

=item * B<ID> : IDentificativo dell'evento/messaggio

=item * B<TARGET_REF> : consente di associare all'evento/messaggio un valore "chiave" nel contesto target

=item * B<STATUS> : indica l'esito della lavorazione; per convenzione, B<0> indica che l'event/messaggio e' stato correttamente processato.

=item * B<REASON> : consente di specificare un commento della lavorazione

=item * B<DATA>	  : Hahsref di dati comunicati dal Target al mittente

=back

=cut
sub fetch {
	my $self = shift;
	my %args = @_;
	my %params = ();
	# Verifica parametri obbligatori
	for my $mandatory (qw/ID TARGET_REF STATUS REASON/) {
		croak "Missing mandatory argument: $mandatory\n"
			unless exists $args{$mandatory};
		croak "Uninitialized mandatory argument: $mandatory\n"
			unless defined $args{$mandatory};
		$params{$mandatory} = $args{$mandatory};
		delete $args{$mandatory};
	}
	# Verifica parametri NON obbligatori
	for my $optional (qw/DATA/) {
		$params{$optional} = $args{$optional};
		delete $args{$optional};
	}
	# Argomenti non ammessi
	croak "Unknown arguments: " . join(',', keys(%args)) . "\n"
		if scalar(keys(%args)) > 0;
	
	# Controllo struttura chiave DATA
	if ($params{DATA}) {
		croak "DATA must be an HASH\n"
			if (ref($params{DATA}) ne 'HASH');
		for my $key ( keys %{$params{DATA}} ) {
			my $isa = (
                ref($params{DATA}->{$key}) && ref($params{DATA}->{$key}) ne 'REF'
                ? ref($params{DATA}->{$key})
                : ref(\$params{DATA}->{$key})
            );
            croak "DATA keys must be SCALAR or ARRAY"
            	if ($isa !~/^(SCALAR|ARRAY)$/);
		}
	} else {
		$params{DATA} = {};
	}
	
	$self->_set_date_format();
	# Istanzia un nuovo oggetto 'evento' per effettuare la CANCELlazione
	my $e = SIRTI::ART::RemoteActivity::Event->new(
		 RA => $self
		,ID => $params{ID}
		,PLSQL => $self->get_plsql
	);
	$self->_unset_date_format()
		&& croak "Event $params{ID} not found|\n"
			unless defined($e);
	my $fetch = $e->fetch(
		 $params{TARGET_REF}
		,$params{STATUS}
		,$params{REASON}
		,$params{DATA}
	);
	$self->_set_date_format();
	return $fetch;
}


sub _fetch { croak "dummy method"; }

=pod

=head2 lookup( ID => I<SCALAR>, REASON => I<SCALAR> )

Consente di tracciare l'impossibilita' contingente nel "leggere" ("consumare") un evento/messaggio:

=over 4

=item * B<ID> : IDentificativo dell'evento/messaggio

=item * B<REASON> : consente di specificare la motivazione della mancata lavorazione

=back

=cut

sub lookup {
	my $self = shift;
	my %args = @_;
	my %params = ();
	# Verifica parametri obbligatori
	for my $mandatory (qw/ID REASON/) {
		croak "Missing mandatory argument: $mandatory\n"
			unless exists $args{$mandatory};
		croak "Uninitialized mandatory argument: $mandatory\n"
			unless defined $args{$mandatory};
		$params{$mandatory} = $args{$mandatory};
		delete $args{$mandatory};
	}
	# Verifica parametri NON obbligatori
	for my $optional (qw//) {
		$params{$optional} = $args{$optional};
		delete $args{$optional};
	}
	# Argomenti non ammessi
	croak "Unknown arguments: " . join(',', keys(%args)) . "\n"
		if scalar(keys(%args)) > 0;
	$self->_set_date_format();
	# Istanzia un nuovo oggetto 'evento' per effettuare la CANCELlazione
	my $e = SIRTI::ART::RemoteActivity::Event->new(
		 RA => $self
		,ID => $params{ID}
		,PLSQL =>  $self->get_plsql
	);
	$self->_unset_date_format()
		&& croak "Event $params{ID} not found|\n"
			unless defined($e);
	my $r=$e->lookup($params{REASON});
	$self->_unset_date_format();
	return $r;
}


sub _lookup { croak "dummy method"; }


=pod

=head1 NOTES

=over 4

=item Nessuna nota.

=back

=cut


=pod

=head1 BUGS

Eventuali bug riscontrati dovranno essere segnalati su B<ART - Global Services>: L<https://www.artnet.sirtisistemi.net/ARTIT/art> aprendo un apposito DEV-TICKET.

=head1 HISTORY

=over

=item Ver. 0.04

Prima release del modulo

=back

=head1 AUTHOR

Alvaro Livraghi <<EMAIL>>

=cut


###################################
#
# Testing the module...
#
###################################
if (__FILE__ eq $0) {

	####  test ###  
	package main;

	use strict;
	use warnings;
	use Carp 'verbose';
	eval('use SIRTI::DB::Oracle;
		use Data::Dumper;
	');

	if ($@) {
		print STDERR $@;
		exit 1;
	}

	my $sqlid=$ENV{SQLID_DRAO2O};
	$sqlid=$ENV{SQLID} unless defined $sqlid;


	#$ENV{ART_DB_AUTOCOMMIT} = 1;
	my $w = SIRTI::ART::RemoteActivity::Target->new( 
		 DB             => SIRTI::DB::Oracle->new(
		 	 $sqlid
		 	,DEBUG => 0
			,DBMS_OUTPUT => 1
		 )
		#,RA_DBLINK => 'DULLT2'
		,SESSION_DESCRIPTION => 'Hello world!'
		,SOURCE_SERVICE    => 'TL2_PROVISIONING'
		,SOURCE_CONTEXT => 'MINIFASE2'
		,TARGET_SERVICE    => 'TL2_OLO2OLO'
		,TARGET_CONTEXT => 'MINIFASE2'
		,DATE_FORMAT => 'dd-mm-yyyy'
		,USER_ID	=> undef
	);

	print Dumper($w);
	print Dumper($w->_dbh());
	print Dumper $w->get_source_service();
	print Dumper $w->get_source_service_info();
	print Dumper $w->get_source_context();
	print Dumper $w->get_target_service();
	print Dumper $w->get_target_service_info();
	print Dumper $w->get_target_context();

	my ($ref, $event) = ('100', 'OPEN');
	
	print "ALL EVENTS: ", Dumper( [ $w->find_pending() ] ), "\n";
	print "ALL EVENTS=$event: ", Dumper( [ $w->find_pending() ] ), "\n";

	my @pending = $w->find_pending();
	print Dumper \@pending;
	my $e = undef;
	for my $id (@pending) {
		$e = $w->get_event(ID => $id);
		warn "Undefined event: $id\n" and next 
			unless defined $e;
		print "EVENT($id) INFO: ", Dumper($e->get_info());
		print "EVENT($id) DATA: ", Dumper($e->get_data());
		print "EVENT($id) LOOKUP: ", Dumper($e->get_lookup());
		print "Lookup with RA... ", $w->lookup(ID => $id, REASON => 'RA L-O-O-K-U-P'), "\n";
		print "Lookup with EVENT... ", $e->lookup('EVENT L-O-O-K-U-P #1'), "\n";
		print "Lookup with EVENT... ", $e->lookup('EVENT L-O-O-K-U-P #2'), "\n";
		print "Fetching with EVENT... ", $e->fetch(999, 0, 'Che menefutt\'amme\'!'), "\n";
		#print "Fetching with RA... ", $w->fetch(ID => $id, TARGET_REF => 999, STATUS => 0, REASON => 'Che menefutt\'amme\'!'), "\n";
		print "EVENT($id) INFO: ", Dumper($e->get_info());
		print "EVENT($id) DATA: ", Dumper($e->get_data());
		print "EVENT($id) LOOKUP: ", Dumper($e->get_lookup());
	}

	print "ROLLBACK\n" and $w->_dbh()->rollback();
	#print "COMMIT\n" and $w->_dbh()->commit();

	exit 0;

} 

1;


__END__

