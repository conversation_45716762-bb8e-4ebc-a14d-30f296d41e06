package SIRTI::AuthRadius;

use strict;
use warnings;
use Authen::Radius;
use Carp qw( verbose croak );
use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

use base 'SIRTI::Base';

# deve essere passato un numero pari di server (server principale e backup) nel seguente ordine:
# RADIUS_SERVER             # internal, mandatory
# RADIUS_SERVER_EXT         # external, optional
# RADIUS_SERVER_OTHER       # other, optional 
# RADIUS_SERVER_...         # ..., optional 
# etc...
# RADIUS_SERVER_BKP         # internal bkp, mandatory
# RADIUS_SERVER_EXT_BKP     # external bkp, optional
# RADIUS_SERVER_OTHER_BKP   # other, optional 
# RADIUS_SERVER_..._BKP     # ..., optional 
# etc...
#
# ogni tipo di server puo' avere opzionalmente un dominio associato:
# RADIUS_DOMAIN             # internal, optional
# RADIUS_DOMAIN_EXT         # external, optional
# RADIUS_DOMAIN_OTHER       # other, optional 
# RADIUS_DOMAIN_...         # ..., optional 
# etc...

sub new {
	my $this = shift;
	my $class = ref($this) || $this;
	my $self = {};
	bless $self, $class;

	my %params = @_;
	my $errmsg;
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'COMMON::LIB::' . __PACKAGE__ );
	
	croak( $errmsg )
		unless $self->check_named_params(
			 ERRMSG     => \$errmsg
			,PARAMS     => \%params
			,MANDATORY  => {
				 RADIUS  => { isa => 'ARRAY' }
			}
		);
	
	croak "You can define only one server or a even number of them"
		unless (scalar(@{$params{RADIUS}}) == 1 || scalar(@{$params{RADIUS}})%2 == 0);
	
	$self->{META_NODI} = scalar(@{$params{RADIUS}})/2;
	$self->{NUM_NODI}  = scalar(@{$params{RADIUS}});
		
	$self->{RADIUS} = [];
	
	for my $rad (@{$params{RADIUS}}){
		croak( $errmsg )
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> $rad
				,MANDATORY	=> {
					 SERVER	=> { isa => 'SCALAR' }
					,SECRET	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					 DOMAIN	=> { isa => 'SCALAR', pattern => qr{^@.+} }
				}
			);
		
		my $auth_radius =  Authen::Radius->new( Host => $rad->{SERVER}, Secret => $rad->{SECRET} );
		
		croak Authen::Radius::strerror($Authen::Radius::get_error)
			unless defined $auth_radius;
		
		my $tmp = { CONNECTION => $auth_radius, SERVER => $rad->{SERVER}, SECRET => $rad->{SECRET} };
		$tmp->{DOMAIN} = $rad->{DOMAIN} if defined $rad->{DOMAIN};
		push @{$self->{RADIUS}}, $tmp;
	
	}
	
	$self->{LOGGER}->trace( 'RADIUS SERVERS: '.Dumper($self->{RADIUS}) );
	
	return $self;
}

sub get_auth {

    my $self = shift;
    my %params = @_;

	my $errmsg;
	$self->last_error( $errmsg )
		&& return undef
			unless $self->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 USERNAME	=> { isa => 'SCALAR' }
					,PASSWORD	=> { isa => 'SCALAR' }
				}
			);
	
	my $num_server_not_available = 0;
	my $num_ko = 0;
	for(my $i=1; $i<=$self->{NUM_NODI}; $i++) {
		
		my $rad = $self->{RADIUS}->[$i-1];
		
		$self->{LOGGER}->debug( 'Checking credential on RADIUS_SERVER('.($i-1).') '.$rad->{SERVER} );
		
		my $username = $params{USERNAME} . (defined $rad->{DOMAIN} ? $rad->{DOMAIN} : '');
		my $password = $params{PASSWORD};
		
		$self->{LOGGER}->debug( 'Credentials: username "'.$username.'", password "******"' );
		
		my $ret = $rad->{CONNECTION}->check_pwd( $username, $password );
		
		if($rad->{CONNECTION}->get_error ne 'ENONE') {
			# errore nell'ottenere la risposta dal server
			$num_server_not_available++;
			$self->{LOGGER}->warn( 'Unable to check_pwd on RADIUS_SERVER('.($i-1).') '.$rad->{SERVER}.': '.$rad->{CONNECTION}->strerror($rad->{CONNECTION}->get_error));
		} else {
			# autenticazione andata a buon fine
			if($ret) {
				$self->{LOGGER}->debug( 'check_pwd OK on RADIUS_SERVER('.($i-1).') '.$rad->{SERVER} );
				return $ret
			} else {
				# username o password sbagliate
				$self->{LOGGER}->debug( 'check_pwd KO on RADIUS_SERVER('.($i-1).') '.$rad->{SERVER} );
				$num_ko++;
			}
		}
		
		if($i == $self->{META_NODI} && $num_ko == $self->{META_NODI}) {
			last;
		}
		
	}
	
	$self->{LOGGER}->warn( 'None of the RADIUS_SERVERs is available')
		if $num_server_not_available == $self->{NUM_NODI};
	
	return 0;

}

#---------------------------------------------------------------------
# UNIT TEST
#---------------------------------------------------------------------
if ( __FILE__ eq $0 ) {

	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $RADIUS = [];
	
	my $radius_intranet = {
		SERVER => $ENV{RADIUS_SERVER},
		SECRET => $ENV{RADIUS_SECRET}
	};
	$radius_intranet->{DOMAIN} = $ENV{RADIUS_DOMAIN} if defined $ENV{RADIUS_DOMAIN} && $ENV{RADIUS_DOMAIN} ne '';
	push @{$RADIUS}, $radius_intranet;
	
	if (defined $ENV{RADIUS_SERVER_EXT} && $ENV{RADIUS_SERVER_EXT} ne '') {
		my $radius_extranet = {
			SERVER => $ENV{RADIUS_SERVER_EXT},
			SECRET => $ENV{RADIUS_SECRET_EXT}
		};
		$radius_extranet->{DOMAIN} = $ENV{RADIUS_DOMAIN_EXT} if defined $ENV{RADIUS_DOMAIN_EXT} && $ENV{RADIUS_DOMAIN_EXT} ne '';
		push @{$RADIUS}, $radius_extranet;
	};
	
	my $radius_intranet_bkp = {
		SERVER => $ENV{RADIUS_SERVER_BKP},
		SECRET => $ENV{RADIUS_SECRET_BKP}
	};
	$radius_intranet_bkp->{DOMAIN} = $ENV{RADIUS_DOMAIN} if defined $ENV{RADIUS_DOMAIN} && $ENV{RADIUS_DOMAIN} ne '';
	push @{$RADIUS}, $radius_intranet_bkp;
	
	if (defined $ENV{RADIUS_SERVER_EXT} && $ENV{RADIUS_SERVER_EXT} ne '') {
		my $radius_extranet_bkp = {
			SERVER => $ENV{RADIUS_SERVER_EXT_BKP},
			SECRET => $ENV{RADIUS_SECRET_EXT_BKP}
		};
		$radius_extranet_bkp->{DOMAIN} = $ENV{RADIUS_DOMAIN_EXT} if defined $ENV{RADIUS_DOMAIN_EXT} && $ENV{RADIUS_DOMAIN_EXT} ne '';
		push @{$RADIUS}, $radius_extranet_bkp;
	};

	my $auth = SIRTI::AuthRadius->new(
		RADIUS => $RADIUS
	);

	Log::Log4perl->get_logger( 'crash::test' )->info("Configurazione: " . Dumper $RADIUS);	
	Log::Log4perl->get_logger( 'crash::test' )->info("Esito: " . Dumper( $auth->get_auth( USERNAME => 'longhix', PASSWORD => 'xxx' ) ) );

}

1;

