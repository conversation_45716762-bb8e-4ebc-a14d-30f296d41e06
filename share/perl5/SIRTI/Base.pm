################################################################################
#
# SIRTI::Base
#
################################################################################

=head1 SIRTI::Base

B<SIRTI::Base> - Classe di base contenente metodi statici general-purpose

=head1 SYNOPSIS

  use SIRTI::Base;
  
  my $w = SIRTI::Base->new();

  # This output: "pippo  \t pluto   paperino"
  print $w->trim("   pippo  \t pluto   paperino   ");

  # This output: "pippo pluto paperino"
  print $w->trimmer("   pippo  \t pluto   paperino   ");

  # This output: "pippo  \t pluto   paperino"
  print $w->trim_trails("  ----.    pippo  \t pluto   paperino () ");

  # This output: "ÀÈÌÒÙAEIOU"
  print $w->upper("àèìòùaeiou");

  # This output: "àèìòùaeiou"
  print $w->lower("ÀÈÌÒÙAEIOU");

=head1 DESCRIPTION

Drescrizione dettagliata...

=cut

	package SIRTI::Base;

	use strict;
	use warnings;
	use Carp qw/verbose croak/;
	#use Encode qw/encode_utf8 is_utf8/;
	use SIRTI::ART::CGI::Charset;
	
	use vars qw( @ISA @EXPORT @EXPORT_OK $VERSION );
	
	require Exporter;
	
	@ISA = qw(Exporter);
	
	@EXPORT = qw(
	    &trim
	    &trimmer
	    &trim_trails
	    &normalize
	    &normalize_apici
	    &upper
	    &lower
	);
	
	@EXPORT_OK = qw(
	    &trim
	    &trimmer
	    &trim_trails
	    &normalize
	    &normalize_apici
	    &upper
	    &lower
	);


################################################################################
#  P R O P R I E T A '   P U B B L I C H E
################################################################################

	our $VERSION = '0.01';


################################################################################
#  M E T O D I
################################################################################

=pod

=head1 METHODS

=over 4

=cut


=pod

=item new()

Ritorna il riferimento ad un'istanza della classe

=cut
	sub new($$) {
		my $this = shift;
		my $class = ref($this) || $this;
		return bless {}, $class;
	}


=pod

=item trim($scalar)

Rimuove spazi iniziali e finali

=cut
	sub trim {
		my $self = shift;
		my $s = shift;
		return undef if !defined($s);
		$s =~ s/^\s*(.*?)\s*$/$1/;	# spazi iniziali a finali
		return $s;
	}


=pod

=item trimmer($scalar)

Rimuove spazi iniziali, finali ed anche spazi ripetuti all'interno della stringa

=cut
	sub trimmer {
		my $self = shift;
		my $s = shift;
		return undef if !defined($s);
		$s =~ s/\s+/ /g;	# spazi multipli
		$s = $self->trim($s);		# spazi iniziali a finali
		return $s;
	}


=pod

=item trim_trails($scalar)

Rimuove caratteri non alfanumerici iniziali e finali

=cut
	sub trim_trails {
		my $self = shift;
		my $s = shift;
		# elenco caratteri da rimuovere \|!"£$%&/()='?^[{]}+*@°#§<>,;.:-_;
		my $chars = '\\\|\!\"\£\$\%\&\/\(\)\=\'\?\^\[\{\]\}\+\*\@\°\#\§\<\>\,\;\.\:\-\_';
		$s =~ s/^[\s$chars]*(.*?)[\s$chars]*$/$1/g;
		return $s;
	}



{
	my %CHARS = (
		 LOWER		=> [ qw(à á ç è é ì í ò ó ù ú) ]
		,LOWER_BASE	=> [ qw(a a c e e i i o o u u) ]
		,UPPER		=> [ qw(À Á Ç È É Ì Í Ò Ó Ù Ú) ]
		,UPPER_BASE	=> [ qw(A A C E E I I O O U U) ]
	);

=pod

=item normalize($scalar)

Converte i caratteri accentati nel relativo carattere non accentato

=cut
	sub normalize {
		my $self = shift;
		my $s = shift;
		return undef if !defined($s);
		for (my $i=0; $i<scalar(@{$CHARS{LOWER}}); ++$i) {
			my $from	= $CHARS{LOWER}->[$i];
			my $to		= $CHARS{LOWER_BASE}->[$i];
			$s =~ s/$from/$to/g;
		}
		for (my $i=0; $i<scalar(@{$CHARS{UPPER}}); ++$i) {
			my $from	= $CHARS{UPPER}->[$i];
			my $to		= $CHARS{UPPER_BASE}->[$i];
			$s =~ s/$from/$to/g;
		}
		return $s;
	}

=pod

=item normalize_apici($scalar)

Converte i caratteri accentati nel relativo carattere non accentato seguito da un apice

=cut
	sub normalize_apici {
		my $self = shift;
		my $s = shift;
		return undef if !defined($s);
		$s = _to_utf8($s);
		for (my $i=0; $i<scalar(@{$CHARS{LOWER}}); ++$i) {
			my $from	= $CHARS{LOWER}->[$i];
			my $to		= $CHARS{LOWER_BASE}->[$i];
			$s =~ s/$from/${to}'/g;
		}
		for (my $i=0; $i<scalar(@{$CHARS{UPPER}}); ++$i) {
			my $from	= $CHARS{UPPER}->[$i];
			my $to		= $CHARS{UPPER_BASE}->[$i];
			$s =~ s/$from/${to}'/g;
		}
		return $s;
	}
	

=pod

=item upper($scalar)

Converte in MAIUSCOLO tenendo conto dei caratteri estesi

=cut
	sub upper {
		my $self = shift;
		my $s = shift;
		return undef if !defined($s);
		for (my $i=0; $i<scalar(@{$CHARS{LOWER}}); ++$i) {
			my $from	= $CHARS{LOWER}->[$i];
			my $to		= $CHARS{UPPER}->[$i];
			$s =~ s/$from/$to/g;
		}
		$s = uc($s);
		return $s;
	}
	
=pod

=item lower($scalar)

Converte in minuscolo tenendo conto dei caratteri estesi

=cut
	sub lower {
		my $self = shift;
		my $s = shift;
		return undef if !defined($s);
		$s = lc($s);
		for (my $i=0; $i<scalar(@{$CHARS{UPPER}}); ++$i) {
			my $from	= $CHARS{UPPER}->[$i];
			my $to		= $CHARS{LOWER}->[$i];
			$s =~ s/$from/$to/g;
		}
		return $s;
	}

}


=pod

=item last_error($msg)

Se viene specificato $msg viene settato il messaggio di errore.

Ritorna sempre l'ultimo messaggio di errore settato.

=cut
sub last_error {
	my $self = shift;
	$self->{__ERROR__} = join(' ', @_) if scalar(@_) > 0;
	return $self->{__ERROR__};
}

=pod

=item clear_error($msg)

Setta a '' (stringa vuota) il messaggio di errore.

=cut
sub clear_error { $_[0]->last_error('') }
sub clear_last_error { $_[0]->clear_error('') }



=pod

=item check_named_params(ERRMSG => I<SCALARREF>, PARAMS => I<HASHREF>, MANDATORY => I<ARRAYREF> [, OPTIONAL => I<ARRAYREF>, IGNORE_EXTRA_PARAMS => I<SCALAR>] )

Pattern per la verifica di parametri passati in input ad un generico metodo.

Ritorna 1 se tutti i B<PARAMS> rispettano i I<vincoli> impostati in B<MANDATORY> e B<OPTIONAL>.

  $self->check_named_params(
     ERRMSG => \$errmsg # ref per memorizzare il messaggio di errore
    ,IGNORE_EXTRA_PARAMS => 1 # ignora eventuali parametri non previsti in MANDATORY e OPTIONAL
    ,PARAMS => \%input_params # elenco dei parametri da verificare
    ,MANDATORY => { # elenco paratmetri obbligatori
        PIPPO => { isa => 'SCALAR' , pattern => qr{^.+$} }
       ,PLUTO => { isa => 'ARRAY' , min => 1 , max => 10 }
       ,PAPERINO => { isa => 'HASH' }
       ,ORAZIO => { isa => 'SCALAR' , list => [ 1 , 2 , 3 , 4 ] }
    }
    ,OPTIONAL => { # elenco paratmetri opzionali
        DUFFY => { isa => 'ARRAY' }
       ,ACTIVITY => { isa => undef, inherits => 'API::ART::Activity' }
  )

B<ERRMSG> conterra' il messaggio di errore nel caso in cui il check fallisca.

B<MANDATORY> e B<OPTIONAL> sono I<HASHREF> in cui le I<keys> rappresentano i nomi dei parametri mentre i relativi I<values>
rappresentano i vocoli da rispettare:

=over 4

=item * B<isa>: (obbligatorio) tipo di valore del parametro nel rispetto della funzione Perl B<ref()>, cioe' uno fra SCALAR, ARRAY, HASH, CODE, REF, GLOB, LVALUE, FORMAT, IO, VSTRING e Regexp. se non definito, il tipo di valore del parametro puo' essere qualunque

=item * B<inherits>: (opzionale) tipo di valore del parametro nel rispetto della funzione Perl UNIVERSAL B<< CLASS->isa() >>, cioe' appartenere ad una classe o sottoclasse del valore del parametro

=item * B<pattern>: (opzionale) regexp che il valore del parametro deve rispettare (valido solo con isa=SCALAR)

=item * B<min> e B<max>: (opzionale) numero minimo e massimo di elementi attesi nel caso di isa=ARRAY

=item * B<list>: (opzionale) elenco (I<ARRAYREF>) di valori validi (valido solo con isa=SCALAR)

=item * B<custom_check>: (opzionale) subref che controlla il valore del parametro rispetto alla funzione utente passata, che deve ritornare undef se tutto ok, altrimenti deve restituire l'errore riscontrato in fase di check. Parametro da usare consapevolmente!

=back

B<check_named_params()>, di default, ritorna errore anche nel caso vengano passati parametri non previsti
in I<MANDATORY> e I<OPTIONAL>. Questo comportamento puo' essere modificato settando a 1 l'opzione I<IGNORE_EXTRA_PARAMS>.

=cut
	sub check_named_params {
		my $self = shift;
		my %params = @_;

		# Verifica parametri
		croak "Missing mandatory parameter 'ERRMSG'"
			unless exists $params{ERRMSG};
		croak "Bad type for parameter 'ERRMSG' (must be SCALAR ref)"
			unless ref($params{ERRMSG}) eq 'SCALAR';
		croak "Missing mandatory parameter 'PARAMS'"
			unless exists $params{PARAMS};
		croak "Bad type for parameter 'PARAMS' (must be HASH ref)"
			unless ref($params{PARAMS}) eq 'HASH';
		croak "Missing mandatory parameter 'MANDATORY'"
			unless exists $params{MANDATORY};
		croak "Bad type for parameter 'MANDATORY' (must be HASH ref)"
			unless ref($params{MANDATORY}) eq 'HASH';
		$params{OPTIONAL} = {}
			unless defined $params{OPTIONAL};
		croak "Bad type for parameter 'OPTIONAL' (must be HASH ref)"
			unless ref($params{OPTIONAL}) eq 'HASH';
		$params{IGNORE_EXTRA_PARAMS} = 0
			unless exists $params{IGNORE_EXTRA_PARAMS};
		croak "Bad type for parameter 'IGNORE_EXTRA_PARAMS' (must be SCALAR ref)"
			unless ref(\$params{IGNORE_EXTRA_PARAMS}) eq 'SCALAR';
		croak "Bad value for parameter 'IGNORE_EXTRA_PARAMS' (must be 0 or 1)"
			unless grep(/^$params{IGNORE_EXTRA_PARAMS}$/, qw/0 1/);
		
		# Faccio una copia per consentire la cancellazione delle chiavi senza provocare "danni" al chiamante
		my %parameters = %{$params{PARAMS}};
		
		# Argomenti obbligatori
		foreach my $p ( keys %{$params{MANDATORY}} ) {
			(${$params{ERRMSG}} = "Missing mandatory parameter '$p'")
				and return 0
					unless exists $parameters{$p};
			(${$params{ERRMSG}} = "Uninitialized mandatory parameter '$p'")
				&& return 0
					unless defined $parameters{$p};
			(${$params{ERRMSG}} = "Missing isa for parameter '$p'")
				&& return 0
					unless exists $params{MANDATORY}->{$p}->{isa};
			(${$params{ERRMSG}} = "Wrong inherits for parameter '$p', (must be ARRAY ref)")
				&& return 0
					if (exists $params{MANDATORY}->{$p}->{inherits} && ref($params{MANDATORY}->{$p}->{inherits}) ne 'ARRAY' ) ;
			my $isa = (
				ref($parameters{$p}) && ref($parameters{$p}) ne 'REF'
				? ref($parameters{$p})
				: ref(\$parameters{$p})
			);
			(${$params{ERRMSG}} = "Bad ISA type for parameter '$p' (expected '$params{MANDATORY}->{$p}->{isa}' but provided '$isa')")
				&& return 0
					unless (!defined $params{MANDATORY}->{$p}->{isa} || $isa eq $params{MANDATORY}->{$p}->{isa});
			
			# Il parametro inherits non e' compatibile con il parametro isa che deve essere undef, ma non solleviamo una eccezione in quanto
			# se e' una sottoclasse si blocca in Bad ISA e non riesce ad arrivare fin qui, se non si blocca il controllo seguente e' inutile.
			if ( exists $params{MANDATORY}->{$p}->{inherits} && !$params{MANDATORY}->{$p}->{isa} ) {
				for (@{$params{MANDATORY}->{$p}->{inherits}}) {
					my $rc = eval { $parameters{$p}->isa($_) };
					(${$params{ERRMSG}} = "Bad parameter '$p' (expected OBJECT but provided '$isa')")
						&& return 0
							if $@;
					(${$params{ERRMSG}} = "Bad parameter '$p' (expected any of ".join (', ',@{$params{MANDATORY}->{$p}->{inherits}})." class or subclass but provided $isa)")
						&& return 0
							unless $rc;
				}
			}
			if ( exists $params{MANDATORY}->{$p}->{pattern} ) {
				(${$params{ERRMSG}} = "Bad pattern for parameter '$p'")
					&& return 0
						unless uc(ref($params{MANDATORY}->{$p}->{pattern})) eq 'REGEXP';
				(${$params{ERRMSG}} = "Pattern mismatch for parameter '$p' ('$params{MANDATORY}->{$p}->{pattern}')")
					&& return 0
						unless $parameters{$p} =~ $params{MANDATORY}->{$p}->{pattern};
			}
			if ( exists $params{MANDATORY}->{$p}->{list} ) {
				(${$params{ERRMSG}} = "Bad list for parameter '$p'")
					&& return 0
						unless ref($params{MANDATORY}->{$p}->{list}) eq 'ARRAY';
				(${$params{ERRMSG}} = "Bad value for parameter '$p'")
					&& return 0
						#unless grep(/^$parameters{$p}$/, @{ $params{MANDATORY}->{$p}->{list} });
						unless grep { $_ eq $parameters{$p} } @{ $params{MANDATORY}->{$p}->{list} };
			}
			
			if ( exists $params{MANDATORY}->{$p}->{custom_check} ) {
				my $error = $params{MANDATORY}->{$p}->{custom_check}->($parameters{$p});
				(${$params{ERRMSG}} = "Bad value for parameter '$p' => '$parameters{$p}': $error")
					&& return 0
						if $error;
			}

			
			SWITCH: {
				if ( $isa eq 'ARRAY' ) {
					if ( exists $params{MANDATORY}->{$p}->{min} ) {
						(${$params{ERRMSG}} = "Bad min-filter for parameter '$p'")
							&& return 0
								unless $params{MANDATORY}->{$p}->{min} =~ /^\d+$/;
						(${$params{ERRMSG}} = "At least $params{MANDATORY}->{$p}->{min} elements expected for parameter '$p'")
							&& return 0
								unless scalar(@{ $parameters{$p} }) >= $params{MANDATORY}->{$p}->{min};
					if ( exists $params{MANDATORY}->{$p}->{max} ) {
						(${$params{ERRMSG}} = "Bad max-filter for parameter '$p'")
							&& return 0
								unless $params{MANDATORY}->{$p}->{max} =~ /^\d+$/;
						(${$params{ERRMSG}} = "At most $params{MANDATORY}->{$p}->{max} elements expected for parameter '$p'")
							&& return 0
								unless scalar(@{ $parameters{$p} }) <= $params{MANDATORY}->{$p}->{max};
					}
					}
					last SWITCH;
				}
			}
			delete $parameters{$p};
		}
		
		# Argomenti opzionali
		foreach my $p ( keys %{$params{OPTIONAL}} ) {
			next
				unless exists $parameters{$p};
				
			(${$params{ERRMSG}} = "Wrong inherits for parameter '$p', (must be ARRAY ref)")
				&& return 0
					if (exists $params{OPTIONAL}->{$p}->{inherits} && ref($params{OPTIONAL}->{$p}->{inherits}) ne 'ARRAY' ) ;
				
			my $isa = (
				ref($parameters{$p}) && ref($parameters{$p}) ne 'REF'
				? ref($parameters{$p})
				: ref(\$parameters{$p})
			);
			(${$params{ERRMSG}} = "Bad ISA type for parameter '$p' (expected '$params{OPTIONAL}->{$p}->{isa}' but provided '$isa')")
				&& return 0
					unless (!defined $params{OPTIONAL}->{$p}->{isa} || $isa eq $params{OPTIONAL}->{$p}->{isa});

			# Il parametro inherits non e' compatibile con il parametro isa che deve essere undef
			if ( exists $params{OPTIONAL}->{$p}->{inherits} && !$params{OPTIONAL}->{$p}->{isa} ) {
				for (@{$params{OPTIONAL}->{$p}->{inherits}}) {
					my $rc = eval { $parameters{$p}->isa($_) };
					(${$params{ERRMSG}} = "Bad parameter '$p' (expected OBJECT but provided '$isa')")
						&& return 0
							if $@;
					(${$params{ERRMSG}} = "Bad parameter '$p'  any of ".join (', ',@{$params{OPTIONAL}->{$p}->{inherits}})." class or subclass but provided $isa)")
						&& return 0
							unless $rc;
				}
			}

			if ( defined $parameters{$p} ) {
				if ( exists $params{OPTIONAL}->{$p}->{pattern} ) {
					(${$params{ERRMSG}} = "Bad pattern for parameter '$p'")
						&& return 0
							unless uc(ref($params{OPTIONAL}->{$p}->{pattern})) eq 'REGEXP';
					(${$params{ERRMSG}} = "Pattern mismatch for parameter '$p' ('$params{OPTIONAL}->{$p}->{pattern}')")
						&& return 0
							unless $parameters{$p} =~ $params{OPTIONAL}->{$p}->{pattern};
				}
				if ( exists $params{OPTIONAL}->{$p}->{list} ) {
					(${$params{ERRMSG}} = "Bad list for parameter '$p'")
						&& return 0
							unless ref($params{OPTIONAL}->{$p}->{list}) eq 'ARRAY';
					(${$params{ERRMSG}} = "Bad value for parameter '$p'")
						&& return 0
							#unless grep(/^$parameters{$p}$/, @{ $params{OPTIONAL}->{$p}->{list} });
							unless grep { $_ eq $parameters{$p} } @{ $params{OPTIONAL}->{$p}->{list} };
				}


				if ( exists $params{OPTIONAL}->{$p}->{custom_check} ) {
					my $error = $params{OPTIONAL}->{$p}->{custom_check}->($parameters{$p});
					(${$params{ERRMSG}} = "Bad value for parameter '$p' => '$parameters{$p}': $error")
						&& return 0
							if $error;
				}

				SWITCH: {
					if ( $isa eq 'ARRAY' ) {
						if ( exists $params{OPTIONAL}->{$p}->{min} ) {
							(${$params{ERRMSG}} = "Bad min-filter for parameter '$p'")
								&& return 0
									unless $params{OPTIONAL}->{$p}->{min} =~ /^\d+$/;
							(${$params{ERRMSG}} = "At least $params{OPTIONAL}->{$p}->{min} elements expected for parameter '$p'")
								&& return 0
									unless scalar(@{ $parameters{$p} }) >= $params{OPTIONAL}->{$p}->{min};
						}
						if ( exists $params{OPTIONAL}->{$p}->{max} ) {
							(${$params{ERRMSG}} = "Bad max-filter for parameter '$p'")
								&& return 0
									unless $params{OPTIONAL}->{$p}->{max} =~ /^\d+$/;
							(${$params{ERRMSG}} = "At most $params{OPTIONAL}->{$p}->{max} elements expected for parameter '$p'")
								&& return 0
									unless scalar(@{ $parameters{$p} }) <= $params{OPTIONAL}->{$p}->{max};
						}
						last SWITCH;
					}
				}
			}
			delete $parameters{$p};
		}
		
		unless ( $params{IGNORE_EXTRA_PARAMS} ) {
			# Verifica parametri non ammessi
			(${$params{ERRMSG}} = "Unknown parameters: " . join(',', keys(%parameters)))
				&& return 0
					if scalar(keys(%parameters)) > 0;
		}
		return 1;
	}


=pod

=back

=head1 CARATTERI ESTESI

I caratteri estesi gestiti attualmente sono i seguenti:

  à á ç è é ì í ò ó ù ú
  À Á Ç È É Ì Í Ò Ó Ù Ú

=cut


sub _to_utf8 {
	my $s = shift;
	return undef unless defined $s;
	return $s if SIRTI::ART::CGI::Charset::isutf8($s);
	return SIRTI::ART::CGI::Charset::encode_utf8($s);
}


###################################
#
# Testing the module...
#
###################################
if (__FILE__ eq $0) {

	####  test ###
	package main;

	use strict;
	use warnings;
	use Carp 'verbose';

	exit 0;

} else {

	1;

}


__END__

=pod

=head1 BUGS

Eventuali bug riscontrati dovranno essere segnalati su B<ART - Global Services>: L<https://www.artnet.sirtisistemi.net/ARTIT/art> aprendo un apposito DEV-TICKET.

=head1 HISTORY

=over

=item Ver. 0.01

Prima release del modulo

=back

=head1 AUTHOR

Alvaro Livraghi <<EMAIL>>

=cut


1;
