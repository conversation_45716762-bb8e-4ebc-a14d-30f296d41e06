package SIRTI::Base::Singleton;

use strict;
use warnings;
use Carp;

=head1 SIRTI::Base::Singleton

Classe base per singleton con le funzionalita' della SIRTI::Base (last_error, check_named_params, ...)

Come prerequisito e' necessaria la presenza della classe Class::Singleton

L'oggetto espone tutti i metodi della SIRTI::Base e della Class::Singleton

Per istanziare l'oggetto e' necessario effettuare l'override del metodo _new_instance

Esempio:

sub _new_instance {

	my $class = shift;

	my $self  = bless { }, $class;

	my %params = @_;

	# codice
	
	return $self;
}

=cut

use base qw/SIRTI::Base Class::Singleton/;

sub new {
	croak "utilizzare metodo instance per istanziare la classe";
}

#override metodi Class::Singleton
sub instance{
	shift->Class::Singleton::instance(@_);
}

sub has_instance{
	shift->Class::Singleton::has_instance(@_);
}
#fine override metodi Class::Singleton

if ($0 eq __FILE__) {
	my $s = SIRTI::Base::Singleton->new();
}

1;