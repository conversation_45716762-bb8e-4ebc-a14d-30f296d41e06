package SIRTI::CSV::_iterator;
use strict;
use warnings;
use integer;
use English '-no_match_vars';
use Carp;

sub _add_error {
	my ($self,$code,$msg,$pos)=@_;
	if (!defined $self->{group_by_errors} || $self->{group_by_errors}->{$code} < $self->{group_by_errors}->{MAX}) {
		my $err={
			code 	=> $code
			,msg	=> $msg
			,line	=> $self->{line_counter}
			,pos	=> $pos
		};
		push @{$self->{errors}},$err;
		if (defined $self->{group_by_errors}) {
			$self->{group_by_errors}->{$code}=0 unless defined $self->{group_by_errors}->{$code};
			++$self->{group_by_errors}->{$code}; 
		}
	}
	return undef;
}

sub _new {
	my ($class,%params)=@_;
	return bless \%params,$class;	
}

sub _get_line {
	my ($self,%params)=@_;
	return undef unless defined $self->{streamer}; #EOF
	my $line=$self->{streamer}->get_line;
	unless ($line) {  #EOF
		my $fd=(delete $self->{streamer})->{INPUT_STREAM};
		close $fd if defined $self->{path};	 #chiude se e' uno streamer di file
		delete $self->{line_counter};
		return undef;		
	}
	else {
		++$self->{line_counter}
	}
	return $line;	
}

sub _check_values {
	my $self=shift;
	my $err=0;
	for my $i(0..scalar(@_) - 1) {
		next unless defined $self->{defined_header}->[$i];
		my $check=$self->{defined_header}->[$i]->{check_value};
		next unless defined $check;
		my $r=$check->($_[$i]);
		unless ($r) {
			$self->_add_error(2099,'EIF - Check value failed',$i + 1); #valore non valido per check
			$err=1;
		}
	}
	return !$err;
}

sub get_header	{  return $_[0]->{header} }

sub fetchrow_hashref {
	my ($self,%params)=@_;
	my $r=$self->fetchrow_arrayref(%params);
	return undef unless defined $r;  #EOF
	return {} unless scalar(@$r);  #riga errata 
	my %h=();
	for my $i(1..scalar(@$r)) {
		my $label=$self->{header}->[$i - 1];
		$label="col_$i" unless defined $label;
		$h{$label}=$r->[$i - 1];
	}
	return \%h;
}

sub fetchrow_arrayref {
	my ($self,%params)=@_;
	my $line=$self->_get_line(%params);
	return undef unless defined $line;

	my $fields=SIRTI::CSV::Parser::_process_line($self,$line,%$self);
	return [] unless defined $fields;
	my $err=0;
	if (defined $self->{defined_header} && scalar(@{$self->{defined_header}}) != scalar(@$fields)) {
		$self->_add_error('9999','EHR - Wrong columns number',0);
		++$err;
	}
	++$err  if $self->{check_body} && !$self->_check_values(@$fields);	
	return $err ? [] : $fields;	
}


sub get_errors {
	my ($self,%params)=@_;
	return wantarray ? @{$self->{errors}} : $self->{errors};	
}

sub finish {
	my ($self,%params)=@_;
	return undef unless defined $self->{streamer};
	my $fd=(delete $self->{streamer})->{INPUT_STREAMER};
	close $fd if defined $self->{path};
	delete $self->{line_counter};
	return $self;
}

package SIRTI::CSV::Parser;

use strict;
use warnings;
use integer;
use English '-no_match_vars';
use Carp;
use Storable qw(dclone);

use constant {
	PARSER_MIN_VERSION 		=> 0.30
	,VERSION				=> 0.01
};

sub _fusion {
	my ($self,%params)=@_;
	my %p=%$self;
	for my $k(keys %params) {
		$p{$k}=$params{$k};
	}
	return wantarray ? %p : \%p;
}



sub _translate_into_path {
	my ($self,%params)=@_;
	return $params{path} if defined $params{path};
	my $obj=$params{obj};
	return undef unless defined $obj;
	my $r=ref($obj);
	if ($r eq 'CODE') {
		my $ret=$obj->(%params);
		return $self->_translate_into_path($ret,%params);
	}
	require File::Temp;
	my $fh = File::Temp->new(UNLINK => 0);
	croak "errore creazione file temporaneo: $!" unless defined $fh;
	if ($r eq '') {  #stringa
		print $fh $obj || croak "errore scrittura: $!";
	}
	elsif ($r eq 'SCALAR') { #puntatore ad una stringa
		print $fh $$obj ||  croak "errore scrittura: $!";
	}
	elsif ($r eq 'ARRAY') {
		for  my $e(@$obj) {
			print $fh $e,"\n" ||  croak "errore scrittura: $!"; 
		}
	}
	elsif ($r eq 'HASH') {
		for  my $e(values %$obj) {
			print $fh $e,"\n" ||  croak "errore scrittura: $!"; 
		}
	}
	elsif ($obj eq *STDIN || $r eq 'GLOB') {
		while(<$obj>) { 
			print $fh $_  || croak "errore scrittura: $!";
		}
	}
	else {
		croak "$r: tipo non implementato"
	}
	my $filename=$fh->filename;
	close $fh;
	return $filename;
}

sub _resolve_header {
	my ($self,%params)=@_;
	return wantarray ? () : undef unless defined $params{header};
	my $r=ref(my $h=$params{header});
	croak "$r: parametro header non e' un array"  unless $r  eq 'ARRAY';
	my @h=map {
		my $e=$h->[$_];
		my $r=ref($e);
		if ($r eq '') {
			$e={
					name => $e
				};
		}
		elsif ($r eq 'HASH') {
			croak "check_value in header[$_] non e' una sub " if defined $e->{check_value} && ref($e->{check_value}) ne 'CODE'; 
		}
		else {
			croak "$r: parametro header invalido";
		}
		$e;
	} (0..scalar(@$h) - 1);
	return wantarray ? @h : \@h;
}

sub new {
	my ($class,%params)=@_;
	return  bless \%params,$class;
}


sub analize_object {
	my ($self,%params)=@_;
	my %p=$self->_fusion(%params);
	my $path=$self->_translate_into_path(%p);
	croak "parametro path oppure obj non definiti" unless defined $path;
	require SIRTI::InputStreamer;
	my $fd=SIRTI::InputStreamer::resolve_path_name($path);
	return undef unless defined $fd;
	my $streamer=SIRTI::InputStreamer->new(INPUT_STREAM => $fd);
	my $get_sep_char=sub {
		my ($line,%p)=@_;
		if ($p{quote_char}) {
			my $q=$p{quote_char};
			if ($line=~/^$q([^$q]+)$q(.*)$/) {
				my ($field,$resto)=($1,$2);
				return "," if length($resto) == 0;  # se la line ha una sola colonna si puo impostare il separatore a piacere 
				my ($sep)=$resto=~/^([;,\|\t:])/; # i caratteri cercati come separatore sono , ; : | \t
				return $sep if defined $sep;
			}
		}
		else {  #se quote_char non e' definito oppure e' nullo  si suppone che il carattere con maggior frequenza sia il separatore
			my %seps=();
			for my $i(0..length($line) - 1) {
				my $c=substr($line,$i,1);
				next unless $c=~/^[;,\|\t:]/;
				$seps{$c}=0 unless defined $seps{$c};
				++$seps{$c};
			}
			return undef if scalar(keys %seps) == 0;  	#nessun separatore trovato
			my ($c,$count)=(' ',0);
			for my $k(keys %seps) {
				if($seps{$k} > $count) {
					($c,$count)=($k,$seps{$k});
				}
			}
			return $c if $count > 0;
		}
		return undef;
	};


	my $get_quote_char=sub {
		my ($line,%p)=@_;
		return "'" 	if $line=~/^'/;  #'
		return '"' if $line=~/^"/; #"
		return undef;
	};
	local $_;
	while($_=$streamer->get_line) {
		chomp;
		if (/^\s*$/) {
			$p{ignore_blank_lines}=1 unless exists $p{ignore_blank_lines};
			next;
		}
		$p{rtrim_regexp}='\s+' if !exists $p{rtrim_regexp} && /\s+$/;
		my $r=defined $p{rtrim_regexp} ? $p{rtrim_regexp} : '';
		$p{trim_shell_char}="'" if !exists $p{trim_shell_char} && /^'[^']+'$r$/;  #'
		$p{trim_shell_char}='"' if !exists $p{trim_shell_char} 	&& /^"[^"]+"$r$/; #"  
		$p{quote_char}=''  if !exists $p{quote_char} && $p{trim_shell_char};  #se esiste un carattere di shell allora si suppone che non esista il carattere di chiusura colonne

		my $line=$_;
		if ($p{trim_shell_char}) {
			my $t=$p{trim_shell_char};
			$line=~/^$t([^$t]+)$t$r$/;
			$line=$1 if defined $1;
		}
		next if $line=~/^\s*$/;
		$p{quote_char}=$get_quote_char->($line,%p) unless defined  $p{quote_char}; #trova il quote_char
		$p{sep_char}=$get_sep_char->($line,%p) unless defined $p{sep_char};  #trova il separatore 

		$p{quote_char}='' if defined $p{sep_char} && ! defined $p{quote_char}; #assume una linea senza quote
		
		last if defined $p{sep_char} && defined $p{quote_char};
	}
	close ($fd);
	return wantarray ? %p : \%p;
}


sub get_parser_valid_params {
	my %p=(	 #nome => default
			 quote_char          => '"',
			 escape_char         => '"',
			 sep_char            => ',',
			 eol                 => $\,
			 always_quote        => 0,
			 binary              => 0,
			 keep_meta_info      => 0,
			 allow_loose_quotes  => 0,
			 allow_loose_escapes => 0,
			 allow_whitespace    => 0,
			 verbatim            => 0
	);
	return wantarray ? %p : \%p;
}

sub _extract_parser_params {
	my %p=@_;
	my $v=get_parser_valid_params;
	my %r=map { exists $v->{$_} ? ($_,defined $p{$_} ? $p{$_} : $v->{$_}) : (); }  keys %p;
	return wantarray ? %r : \%r;
}


sub _check_header_positional {
	my ($self,$d)=@_;
	my $count=0;
	my $h=$self->{header};
	for my $i(0..scalar(@$h) - 1) {
		unless (defined $d->[$i]) {
			$self->_add_error('9999','HEAD - Too many columns ',$i + 1);
			++$count;
			next;
		}
		if ($d->[$i]->{name} ne $h->[$i]) {
			$self->_add_error('9999','HEAD - Bad column name',$i + 1);
			++$count;
		}
	}
	return $count == 0 ? 1 : 0
}


sub _check_header {
	my ($self,$d)=@_;
	my $count=0;
	my $h=$self->{header};
	my %names=map {   ($_->{name},undef) } @$d;
	for my $i(0..scalar(@$h) - 1) {
		unless (exists $names{$h->[$i]}) {
			$self->_add_error('9999','HEAD - Bad column name',$i + 1);
			++$count;
		}
	}
	return $count == 0 ? 1 : 0
}

sub _add_error {
 	return SIRTI::CSV::_iterator::_add_error(@_); 
}


sub _process_line {
	my ($self,$line,%params)=@_;
	chomp($line);
	if ($params{rtrim_regexp}) {
		my $r=$params{rtrim_regexp};
		$line=~s/$r$//;
	}
	if ($params{trim_shell_char}) {
		my $c=$params{trim_shell_char};
		$line=$1 if $line=~/^$c(.*)$/;
		$line=$1 if $line=~/^(.*)$c$/;
	}
	my $parser=$params{parser};
	unless ($parser->parse($line)) {
		my ($cde, $str, $pos)=$parser->error_diag;
		$self->_add_error($cde,$str,$pos);
		return wantarray ? () : undef;
	}
	my @fields=$parser->fields;
	return wantarray ? @fields : \@fields;
}

sub parse  {
	my ($self,%params)=@_;
	my %p=$self->_fusion(%params);
	croak "parametro check_header o check_header_positional sono specificati ma non e' specificato il parametro  first_line_is_header"
			if !$p{first_line_is_header} && ($p{check_header} || $p{check_header_positional});
	croak "parametro header non e' stato specificato e sono stati specificati i parametri check_header e/o check_header_positional "
		if !$p{header} && ($p{check_header} || $p{check_header_positional});

	croak "parametro header no e' stato specificato ma sono stati specificati i parametri check_body "
		if !$p{header} && $p{check_body};

	if (defined $p{group_by_errors}) {
		croak "parametro group_by_error non e' numerico" if scalar($p{group_by_errors} =~ /^(\d+)$/) == 0; 
		$self->{group_by_errors}={ MAX => $p{group_by_errors}};
	}
	my $defined_header=$self->_resolve_header(%p);
	require SIRTI::InputStreamer;
	require Text::CSV_XS;
	croak "e richiesta almeno la versione ".PARSER_MIN_VERSION." di Text::CSV_XS "
		if PARSER_MIN_VERSION > $Text::CSV_XS::VERSION;
	my $parser_params=_extract_parser_params(%p);
	my $parser=Text::CSV_XS->new($parser_params);
	confess "parametri invalidi per  Text::CSV_XS" unless defined $parser; 
	$self->{errors}=[];

	my $path=$self->_translate_into_path(%p);
	croak "parametro path oppure obj non definiti" unless defined $path;
	require SIRTI::InputStreamer;
	my $fd=SIRTI::InputStreamer::resolve_path_name($path);
	return undef unless defined $fd;
	my $s=SIRTI::InputStreamer->new(INPUT_STREAM => $fd);
	$self->{header}=sub {
		my %p=@_;
		return undef unless $p{first_line_is_header};
		my $line=$p{streamer}->get_line;
		unless (defined $line) {
			$self->_add_error('2012','EOF - No such header',0);
			return undef;
		}
		my $fields=$self->_process_line($line,%p);
		return $fields;
	}->(%p,parser => $parser,streamer => $s);
	
	return undef if scalar(@{$self->{errors}});

	if (defined $self->{header} && ($p{check_header} || $p{check_header_positional})) {
		my $r=$p{check_header_positional} 
			? $self->_check_header_positional($defined_header)
			: $self->_check_header($defined_header);

		return undef unless $r;
	}

	return undef if scalar(@{$self->{errors}});

	return SIRTI::CSV::_iterator->_new(
		parent				=> $self
		,path				=>  $p{path}
		,streamer			=>	$s
		,defined_header		=>	$defined_header
		,header				=>	defined $self->{header} ? dclone($self->{header}) : undef
		,parser				=>	$parser
		,group_by_errors	=>	$p{group_by_errors} ? { MAX => $p{group_by_errors} } : undef
		,errors				=>  []		 
		,check_body			=>  sub {
			return undef unless $p{check_body};
			return undef unless defined $defined_header;
			for my $h(@$defined_header) {
				return  1 if defined $h->{check_value};
			}
			return 0;
		}->()
		,line_counter		=> $self->{header} ? 1 : 0
		,trim_shell_char	=> $p{trim_shell_char}
		,rtrim_regexp		=> $p{rtrim_regexp}
	);


}



sub get_errors { my $e=$_[0]->{errors}; $e=[] unless defined $e; return wantarray ? @$e : $e; }


sub get_header	{ my $h=$_[0]->{header}; $h=[] unless defined $h; return wantarray ? @$h : $h; }


1;


__END__



=head1  NAME

SIRTI::CSV::Parser -  parser per csv files 

=cut

=head1 SYNOPSIS

use SIRTI::CSV::Parser

=cut


=head1 DESCRIPTION


questo package e' una classe - istanziare con il metodo new


=head1 FUNCTIONS

questo modulo definisce le seguenti funzioni

new - costruttore 

	parametri:
			 quote_char          => '"',
			 escape_char         => '"',
			 sep_char            => ',',
			 eol                 => $\,
			 always_quote        => 0,
			 binary              => 0,
			 keep_meta_info      => 0,
			 allow_loose_quotes  => 0,
			 allow_loose_escapes => 0,
			 allow_whitespace    => 0,
			 verbatim            => 0
			 	i parametri ui sopra compreso il loro default sono usati dal package Text::CSV_XS
			 	leggere la documentazione di questo package
			 	
			 ignore_blank_lines	 - se vero ignora le linee vuote o completamente a spazi
			 trim_shell_char     - carattere che racchiude tutta una linea (Es: "AAA;HHH" con separatore ;)
			                       questo carattere viene eliminato dal parsing
			 obj - oggetto su cui eseguire il parsing 
			       puo' essere un stringa, un array , un hash, un file descriptor e codice
			 path - se definito ha la priorita' su 'obj' ed indica il path_name di un file
			 first_line_is_header - se vero indica che la prima riga di obj e' una intestazione
			 header - imposta un  header di validita per check_header e/o check_body 
			 			header deve essere un array di nomi Es: [ qw(col1 col2)]
			 			oppure un array di hash Es: [ { name => 'col1',check_value => sub {..}},... ]
						se definito check_value deve essere un handle ad una sub per testare il valore della corrispondente colonna 
			 check_header - se vero l' intestazione di obj viene confrontata con header per verificarne la correttezza - la posizione delle colonne non e' importante
			 check_header_positional - se vero l' intestazione di obj viene confrontata con header per verificarne la correttezza - la posizione delle colonne e' importante

			 group_by_errors - se definito vengono riportati solo i primi errori dello stesso tipo specificati nel parametro 
			 				Es:  group_by_errors => 10
			 check_body	 - se vero ogni linea letta viene confrontata con header per verificarne la correttezza


resolve_parser_params - valuta i parametri di input (gli stessi del costruttore), leggendo obj analizza i dati 
						e ritorna i parametri per parse


parse - inizializza gli oggetti interni ed effettua l' eventauale parsing dell' intestazione 
		i parametri sono gli stessi del costruttore e a parita di chiave hanno la precedenza su di esso
		alla fine della fusione dei parametri l' unico parametro obbligatorio e' 'obj' oppure 'path'
		il metodo ritorna undef in caso di errori oppure un oggetto di tipo SIRTI::CSV::_iterator su cui iterare
		

get_parser_valid_params - ritorna un hash di parametro => valore_default usati dal parser Text::CSV_XS


get_errors - ritorna una array di errori trovati
      un errore e' un  hash avente come chiavi:
      	code 	=> codice errore (numero di 4 cifre)
      	msg		=> messaggio di errore compatibile con 	Text::CSV_XS
      	pos		=> numero di colonna interessata all' errore - la prima colonna ha indice 1
      				in caso di errore non collegabile ad una colonna il valore e' 0
      	linea	=> numero di linea compresa l' eventuale intestazione interessata - la prima riga ha numero 1
      	
get_header - il metodo ritorna un array di nomi rappresentante l' intestazione di obj dopo l' uso di parse

 		
=head1  NAME

SIRTI::CSV::_iterator -  iteratore su linee di un file csv 

=cut

=head1 SYNOPSIS

nessuno - e' istanziato da SIRTI::CSV::Parser

=cut

=head1 DESCRIPTION

questo package e' una classe - un oggetto di questa classe e' ritornato  dal metodo SIRTI::CSV::Parser::parse


=head1 FUNCTIONS

questo modulo definisce le seguenti funzioni

		
get_errors - ritorna una array di errori trovati
			vedi SIRTI::CSV::Parser::get_errors


get_header - ritorna lo stesso risultato di  SIRTI::CSV::Parser::get_header


fetchrow_arrayref - ritorna un ref ad un array  di valori di colonne
                    in caso di EOF il metodo ritorna undef
                    in caso di errore il metodo ritorna un ref ad un array vuoto


fetchrow_hashref - ritorna un ref ad un hash di coppie colonna => valore
                    in caso di EOF il metodo ritorna undef
                    in caso di errore il metodo ritorna un ref ad un hash vuoto

finish - libera le risorse usate (Es: file handle) - da usare nel caso si voglia interrompere il parsing prima di EOF
		 dopo questa chiamata una chiamata a fetchrow_ ritorna sempre undef (EOF) 



		 				
