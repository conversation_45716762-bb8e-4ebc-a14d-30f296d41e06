package SIRTI::Cache;

use strict;
use warnings;
use Carp;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{COM}/share/locale" );

=head1 NAME

B<SIRTI::Cache>

=head1 SYNOPSIS

	use SIRTI::Cache;

	my $cache = SIRTI::Cache->new(
		DRIVER => "SIRTI::Cache::Driver::Memcached",
		SERVER => "127.0.0.1:11211"
	);
	
	$cache->set(
		"48iojhd78h8dfo98890",
		{ pippo => "pluto" },
		{ expire =>60*60*24 }
	);
	$cache->get("48iojhd78h8dfo98890");
	$cache->delete("48iojhd78h8dfo98890");

=head1 DESCRIPTION

Questo package consente di interagire con un cache server attraverso uno specifico driver

=head1 METHODS

=cut

=head2 B<new>( DRIVER => I<SCALAR> [ , %PARAMS ] )

Costruttore della classe

=over 4

=item B<DRIVER>: I<SCALAR>

Package che implementa il driver.

Il driver deve implementare i seguenti metodi che devono croakare in caso di errore:

=over 5

=item B<new>( %PARAMS )

=item B<set>( I<$key> , I<$value> [ , I<$opts> ] )

=item B<get>( I<$key> )

=item B<delete>( I<$key> )

=item B<exposed_methods>()

=back

=item B<%PARAMS>

Parametri specifici del driver. Verranno passati al costruttore del driver

=back

=cut
sub new {
	my $class = shift;
	my $self  = bless { }, $class;
	my %params = @_;
	
	croak __x("Missing {paramname} param", paramname => "DRIVER")
		unless defined $params{DRIVER};

	eval "require $params{DRIVER};";
	croak __x("Unable to load Driver {driver}: {reason}", driver => $params{DRIVER}, reason => $@)
		if $@;

	$self->{DRIVER} = $params{DRIVER}->new(%params);
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'COMMON::LIB::' . __PACKAGE__ );

	return bless( $self, $class );
}

sub _logger { shift->{LOGGER} }

sub _driver { shift->{DRIVER} }

=head2 B<set>( I<$key> , I<$value> [ , I<$opts> ] )

Inserisce un oggetto nel cache server

=over 4

=item B<$key>: I<SCALAR>

Stringa usata come chiave per l'oggetto da memorizzare

=item B<$value>: I<MIXED>

Oggetto da memorizzare

=item B<$opts>: I<HASHREF>

Opzionale, parametri specific del driver

=back

=cut
sub set {
	my $self = shift;
	my ($key, $value, $opts) = @_;
	return $self->_driver()->set($key, $value, $opts);
}

=head2 B<get>( I<$key> )

Recupera un oggetto nel cache server

=over 4

=item B<$key>: I<SCALAR>

Stringa usata come chiave per l'oggetto da recuperare

=back

=cut
sub get {
	my $self = shift;
	my ($key) = @_;
	return $self->_driver()->get($key);
}

=head2 B<delete>( I<$key> )

Elimina un oggetto nel cache server

=over 4

=item B<$key>: I<SCALAR>

Stringa usata come chiave per l'oggetto da eliminare

=back

=cut
sub delete {
	my $self = shift;
	my ($key) = @_;
	return $self->_driver()->delete($key);
}

=head2 B<driver_exposed_methods>()

Restituisce un array con la lista dei metodi specifici che possono essere invocati nel driver:
il driver deve restituire tali metodi cattraverso il metodo B<exposed_methods>() che restuisce un array di scalari

=cut
sub driver_exposed_methods {
	my $self = shift;

	return $self->_driver()->exposed_methods();
}

=head2 B<call_driver_method>( I<$method> [ , @params ] )

Invoca un metodo specifico del driver

=over 4

=item B<$method>: I<SCALAR>

Nome del metodo da invocare

=item B<@params>: I<LIST>

Elenco opzionale di argomenti che verranno passati al metodo del driver

=back

=cut
sub call_driver_method {
	my $self = shift;
	my $method = shift;
	my @params = @_;

	my @exposed_methods = $self->_driver()->exposed_methods();
	croak __x("Method {method} not exposed by driver; valid methods are: {methods}", method => $method, methods => join(",", @exposed_methods))
		unless grep /^$method/, @exposed_methods;
	
	return $self->_driver()->$method(@params);
}

if (__FILE__ eq $0) {

	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $cache;
	eval {
		$cache = SIRTI::Cache->new(DRIVER => "SIRTI::Cache::Driver::Memcached", SERVER => "127.0.0.1:11211");
	};
	if ($@) {
		die "SIRTI::Cache: $@";
	}
	
	my @exposed_methods = $cache->driver_exposed_methods();
	print 'Exposed methods: ' . Dumper(\@exposed_methods);
	$cache->set('pippo' => { a => 1, b => "ciao" });
	print Dumper $cache->get('pippo');
	$cache->call_driver_method('disconnect_all');
	$cache->delete('pippo');
	print Dumper $cache->get('pippo');
	$cache->delete('pippo');

}

1;
