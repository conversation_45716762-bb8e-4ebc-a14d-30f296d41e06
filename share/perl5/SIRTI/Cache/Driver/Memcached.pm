package SIRTI::Cache::Driver::Memcached;

use strict;
use warnings;
use Carp;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);
use Cache::Memcached;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{COM}/share/locale" );

=head1 NAME

B<SIRTI::Cache::Driver::Memcached>

=head1 SYNOPSIS

	use SIRTI::Cache;

	my $cache = SIRTI::Cache->new(
		DRIVER => "SIRTI::Cache::Driver::Memcached",
		SERVER => "127.0.0.1:11211"
	);
	
	$cache->set(
		"48iojhd78h8dfo98890",
		{ pippo => "pluto" },
		{ expire =>60*60*24 }
	);
	$cache->get("48iojhd78h8dfo98890");
	$cache->delete("48iojhd78h8dfo98890");

=head1 DESCRIPTION

Memcached driver per B<SIRTI::Cache>

=head1 METHODS

=cut

=head2 B<new>( SERVER => I<SCALAR> )

Costruttore della classe

=over 4

=item B<SERVER>: I<SCALAR>

Indirizzo del/i server memcached in formato I<host:port[,host:port[,...]]>

=back

=cut
sub new {
	my $class = shift;
	my $self  = bless { }, $class;
	my %params = @_;
	
	croak __x("Missing {paramname} param", paramname => "SERVER")
		unless defined $params{SERVER};

	$self->{LOGGER} = Log::Log4perl->get_logger( 'COMMON::LIB::' . __PACKAGE__ );

	my @servers = split ",", $params{SERVER};
	$self->{MEMCACHED} = new Cache::Memcached {
		 servers => \@servers
		,debug => 0
		,compress_threshold => 10_000
	};

	return bless( $self, $class );
}

sub _logger { shift->{LOGGER} }

sub _mem { shift->{MEMCACHED} }

=head2 B<set>( I<$key> , I<$value> [ , I<$opts> ] )

Inserisce un oggetto nel memcached server

=over 4

=item B<$key>: I<SCALAR>

Stringa usata come chiave per l'oggetto da memorizzare

=item B<$value>: I<MIXED>

Oggetto da memorizzare

=item B<$opts>: I<HASHREF>

Opzionale, HASHREF che può contenere le seguenti chiavi:

=over 5

=item B<expire>: I<SCALAR>

numero di secondi dopo il quale la chiave espirerà dal cache server

=back

=back

=cut
sub set {
	my $self = shift;
	my ($key, $value, $opts) = @_;
	$opts = {} unless defined $opts;
	my $ret;
	if($opts->{expire}) {
		$ret = $self->_mem()->set($key, $value, $opts->{expire});
	} else {
		$ret = $self->_mem()->set($key, $value);
	}
	croak __x("Unable to set key {key}", key => $key)
		unless $ret;
	return $ret;
}

=head2 B<get>( I<$key> )

Recupera un oggetto nel memcached server

=over 4

=item B<$key>: I<SCALAR>

Stringa usata come chiave per l'oggetto da recuperare

=back

=cut
sub get {
	my $self = shift;
	my ($key) = @_;
	return $self->_mem()->get($key);
}

=head2 B<delete>( I<$key> )

Elimina un oggetto nel memcached server

=over 4

=item B<$key>: I<SCALAR>

Stringa usata come chiave per l'oggetto da eliminare

=back

=cut
sub delete {
	my $self = shift;
	my ($key) = @_;
	my $ret = $self->_mem()->delete($key);
	croak __x("Unable to delete key {key}", key => $key)
		unless $ret;
	return $ret;
}

=head2 B<exposed_methods>()

Restituisce la lista dei metodi specifici che possono essere invocati nel driver

=cut
sub exposed_methods {
	my $self = shift;

	return ('disconnect_all');
}

=head1 EXPOSED METHODS

=head2 B<disconnect_all>()

Chiude tutte le connessioni cachate ai memcached server.

=cut
sub disconnect_all {
	my $self = shift;

	return $self->_mem()->disconnect_all();
}

if (__FILE__ eq $0) {

	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $cache;
	eval {
		$cache = SIRTI::Cache::Driver::Memcached->new(SERVER => "127.0.0.1:11211");
	};
	if ($@) {
		die "SIRTI::Cache::Driver::Memcached: $@";
	}
	
	eval {$cache->set('pippo' => { a => 1, b => "ciao" })};
	print $@ if $@;
	print Dumper $cache->get('pippo');
	eval {$cache->delete('pippo')};
	print $@ if $@;
	print Dumper $cache->get('pippo');
	eval {$cache->delete('pippo')};
	print $@ if $@;

}

1;
