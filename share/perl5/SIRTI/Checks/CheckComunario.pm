package SIRTI::Checks::CheckComunario;

use strict;
use warnings;
use Storable;

use SIRTI::Err;
use SIRTI::Nice;

use base qw(SIRTI::Checks::GenericCheck);
use Data::Dumper;
use DBI qw(:sql_types);
use DBD::Oracle qw(:ora_types);
use SIRTI::PLSQL::CSF;


sub _format_msg($@) {
	my $self=shift;
	my $msg=shift;
	my @comunario=@_;
	
	my $opt = scalar(@comunario) > 0 && !exists($comunario[0]->{ID}) ? shift @comunario : {};

	my $data=$self->get_data();
	
	if ($opt->{NOERR}) {
		SIRTI::Err::check(scalar(@comunario) == 1);
		return defined $data ? ([],$data->{COMUNE},$data->{FRAZIONE}) : ([],undef,undef) ;
	}
	
	my $getcaps_comuni=sub { #ricava i cap ordinati e univoci
		my %uniq=();
		return sort grep { defined($_) && $uniq{$_}++ == 0 }  map { @{$_->{CAPS}} }  @_; 
	};

	my $getprovincie =  sub { #ricava le provincie ordinate e univoche
								my %uniq=();
								return sort grep { defined($_) && $uniq{$_}++ == 0 }  map {  $_->{TARGA}   }  @_; 
						};

	my $getcomuni = sub { #ricava i comuni ordinati e univoci trattando il caso di errore
		my $row=shift;
		my %uniq=();
#		return sort grep { defined($_) && $uniq{$_}++ == 0 }  map {  ($_->{DENOMSTD},$_->{DENOMABBREV})  }  @_; 
		my @c=sort grep { defined($_) && $uniq{$_}++ == 0 }  map {  $_->{DENOMSTD} }  @_;
		return  @c if scalar(@c) > 1;
		my @filter=sort grep { defined($_) && $row eq $_ } map {  $_->{DENOMSTD},$_->{DENOMABBREV},$_->{DENOMSTDQ} } @_;
		return scalar(@filter) == 0 ? @c : (); 
	};

	my $getcaps_fr=sub { #ricava i cap delle frazioni ordinati e univoci
		my %uniq=();
		return sort grep { defined($_) && $uniq{$_}++ == 0 }  map {   @{$_->{FR_CAPS}}  }  @_;  
	};

	my $getcaps=sub {  #ricava i cap genericamente 
		my $f = defined($self->{ROW}->{FRAZIONE}) ? $getcaps_fr : $getcaps_comuni;
		return $f->(@_);
	};


	my $get_frazioni=sub { #ricava le frazioni valide 
		my %uniq=();
#		return sort grep { defined($_) && $uniq{$_}++ == 0 }  map {  ($_->{FR_DENOMSTD},$_->{FR_DENOMABBREV})  }  @_; 
		return sort grep { defined($_) && $uniq{$_}++ == 0 }  map {  $_->{FR_DENOMSTD}   }  @_; 
	};
																
	my %res=();
	for my $k(keys %{$self->{ROW}}) {
		$res{$k}=$self->{ROW}->{$k} if defined $self->{ROW}->{$k};
	}
	if (scalar(@comunario) > 0) {
		if (defined($self->{ROW}->{COMUNE})) {
			my @comuni=$getcomuni->($self->{ROW}->{COMUNE},@comunario);
			$res{COMUNI_CORRETTI}=join(',',@comuni) if scalar(@comuni) > 0; # ||  !grep($_ eq $self->{ROW}->{COMUNE},@comuni); 
		}
		if (defined($self->{ROW}->{PROVINCIA}) && !$opt->{NOPR}) {
			my @pr=$getprovincie->(@comunario);
			$res{PROVINCIE_CORRETTE}=join(',',@pr) unless grep($_ eq $self->{ROW}->{PROVINCIA},@pr);
		}
		if (defined($self->{ROW}->{CAP}) && !$opt->{NOCAP}) {
			my @caps=$getcaps->(@comunario);
			$res{CAP_CORRETTI}=join(',',@caps) unless  grep($_ eq $self->{ROW}->{CAP},@caps); 
			delete $res{CAP_CORRETTI} if length(SIRTI::Err::nvl($res{CAP_CORRETTI})) == 0;
		}
		if (defined($self->{ROW}->{FRAZIONE}) && !$opt->{NOFR}) {
			my @fr=$get_frazioni->(@comunario);
			$res{FRAZIONI_CORRETTE}=join(',',@fr) if scalar(@fr) > 1 ||  !grep($_ eq $self->{ROW}->{FRAZIONE},@fr);
			delete $res{FRAZIONI_CORRETTE} if length(SIRTI::Err::nvl($res{FRAZIONI_CORRETTE})) == 0;
		}
	}
	
	$res{MSG}=$msg;
	return defined $data ?  ([\%res],$data->{COMUNE},$data->{FRAZIONE}) : ([\%res],undef,undef);
}




sub _check_comune_low_new  {
	my $self=shift;
	my $db=$self->{DB};
	my $r=$self->{CSF}->do_check_comune_frazione(
			%{$self->{ROW}}
			,ATTEMPT_SEARCH_RESOLUTION => $self->{ATTEMPT_SEARCH_RESOLUTION} ? 1 : 0
	) ||  SIRTI::Err::db_fatal_error($db);		

	my ($err,$comunario)=@$r;

	$self->{__COMUNE__} = scalar(@$comunario) == 1  ? @$comunario[0] : undef;

	if (scalar(@$err) > 0)  {
		my $errmsg=$self->_get_errmsg() || SIRTI::Err::db_fatal_error($db);	
		return $self->_format_msg($errmsg->[$err->[0]],@$comunario)
	}

	return  $self->_format_msg(undef,{ NOERR => 1 },@$comunario);
}


sub _get_errmsg {
	my $self=shift;
	return $self->{ERRMSG}  if defined $self->{ERRMSG};
	$self->{ERRMSG}=$self->{CSF}->get_errmsg(LEGACY_ERRMSG => $self->{LEGACY_ERRMSG});
	return  $self->{ERRMSG};
}

sub _do_check($) {
	my $self=shift;
	$self->{__COMUNE__} = undef;
	my ($msgs,$comunario,$frazionario)=$self->_check_comune_low_new();
	return $msgs;
}

sub _get_dump {
	return Dumper(@_);
}

### public

sub new {
	my $class=shift;
	my %param = @_;
	my $self = \%param;
		#DB							=>	istanza di SIRTI::DB
		#NO_CONV_ACCENTED_CHARS    	=> non cambia le lettere accentate nelle corrispondenti non accentate 
		#NO_TRANSLATE_SPECIAL_CHARS => non converte i caratteri speciali (es: ' " #) in spazio prima del check
		#CACHE_DATA					=> se vero scambia tempo di elaborazione con spazio in memoria
		#PARAMS_LENGTH				=> hash di parametri per valorizzare restrizioni sulle lunghezze
		#ATTEMPT_SEARCH_RESOLUTION  => se vero e non riesce a risolvere effettua altri tentativi per ricercare le informazioni immesse
		#								 ATTENZIONE - la ricerca puo essere lenta
		#                                questa opzione e' utile in un sw interattivo con un utente umano
		#LEGACY_ERRMSG				=> se vero cerca di emettere i messaggi in modalita compatibile con la versione non csf
		#CSF						=> istanza di SIRTI::PLSQL::CSF
		#
	$self->{DB}=SIRTI::DB->new() unless defined $self->{DB};
	$self->{__COMUNE__}		= undef;
	delete $self->{CACHE};
	$self->{CACHE}={} if $self->{CACHE_DATA};
	$self->{PARAMS_LENGTH}={
								COMUNE			=>    {  
															MIN  	=>	2
															,FILTER	=>  sub {  $_[0]=~s/\W//g; $_[0]; }  
													  }
								,PROVINCIA		=>    {  
															MIN  	=>	2  
															,FILTER	=>  sub {  $_[0]=~s/\W//g; $_[0]; }  
													  }
								,CAP			=>    {  
															MIN  	=>	5 
															,MAX 	=>  5 
															,FILTER	=>  sub {  $_[0]=~s/\D//g; $_[0]; } 
													  }
								,FRAZIONE		=>    {  
															MIN  	=>	2 
															,FILTER	=>  sub {  $_[0]=~s/\W//g; $_[0]; }  
													  }
							} unless defined $self->{PARAMS_LENGTH};
	$self->{CSF}=SIRTI::PLSQL::CSF->new(DB => $self->{DB}) unless defined $self->{CSF};
	return bless $self,$class;
}


sub get_mandatory_attrs($) { return []; } # elenco attributi obbligatori per check 

sub get_optional_attrs($) { return [ 'COMUNE','PROVINCIA','CAP','FRAZIONE']; }


sub get_params { 
	my $h=SIRTI::Checks::GenericCheck::get_params();
	my %parms = (
			DB							=>	'istanza di SIRTI::DB'
			,NO_CONV_ACCENTED_CHARS    	=> 'non cambia le lettere accentate nelle corrispondenti non accentate' 
			,NO_TRANSLATE_SPECIAL_CHARS	=> 'non converte i caratteri speciali (es: \' " #) in spazio prima del check'
			,CACHE_DATA					=> 'scambia tempo di elaborazione con spazio in memoria'
			,PARAMS_LENGTH				=> 'hash di parametri per valorizzare restrizioni sulle lunghezze'
			,ATTEMPT_SEARCH_RESOLUTION	=> 'se non riesce a risolvere effettua altri tentativi per ricercare le informazioni immesse (opzione utile per un  sw interattivo con un utente umano)'
			,LEGACY_ERRMSG				=> 'cerca di emettere i messaggi in modalita compatibile con la versione non csf'
	);
	
	for my $k(keys %parms) {
		$h->{$k}=$parms{$k}
	}
	return $h;
}

sub get_chektype($) {  return 'COMUNARIO'; }

sub get_id_comune {
	my $self = shift;
	return  undef unless defined $self->{__COMUNE__};
	return defined  $self->{__COMUNE__}->{IDCOMUNE}
			? $self->{__COMUNE__}->{IDCOMUNE}
			: $self->{__COMUNE__}->{ID};
			
}


sub get_id_frazione {
	my $self = shift;
	return  undef unless defined $self->{__COMUNE__};
	return defined  $self->{__COMUNE__}->{IDCOMUNE} 
		? $self->{__COMUNE__}->{ID}
		: undef;
}



sub get_data {
	my $self = shift;
	my $data=$self->{__COMUNE__};	
	return undef unless defined $data; 
	my $key=_get_dump($self->{ROW});
	return $self->{CACHE}->{GET_DATA}->{$key} if defined $self->{CACHE}->{GET_DATA}->{$key};


	my $db = $self->{DB};

	unless (defined $self->{PREPARE}->{PR}) {
		$self->{PREPARE}->{PR}=$db->create_prepare("select * from table(csf.provincia(?))");
	}
	my $pr=$self->{PREPARE}->{PR}->fetchall_hashref($data->{IDPROVINCIA});
	$pr=$pr->[0];
	
	unless (defined $self->{PREPARE}->{RG}) {
		$self->{PREPARE}->{RG}=$db->create_prepare("select * from table(csf.regione(?))");
	}
	
	my $rg=$self->{PREPARE}->{RG}->fetchall_hashref($pr->{IDREGIONE});
	$rg=$rg->[0];
	
	my %out=();
	$out{REGIONE}=$rg;
	$out{PROVINCIA}=$pr;
	$out{ROW}=$self->{ROW};
	if (defined $data->{IDCOMUNE}) {  # e' stato fatto un  match per frazione		                        		
		$out{MATCH}={};
		for my $k (qw( C_MATCHDENOMINAZIONE F_MATCHDENOMINAZIONE C_MATCHSTD F_MATCHSTD MATCHDENOMINAZIONE MATCHSTD )) {
			$out{MATCH}->{$k} = delete $data->{$k} if exists $data->{$k}; 
		}
		
		my %comune=map {  
			my $k=$_; 
			$k=~s/^C_//;
			$k='DENOMINAZIONE_STD' if $k eq 'DENOMINAZIONESTD';
			$k='DENOMINAZIONE' if $k eq 'DENOMINAZIONEAPICISTD';
			$k='DENOMINAZIONE_ABBREVIATA' if $k eq 'DENOMABBREVSTD';								  
			($k,$data->{$_}) 
		} grep(/^C_/,keys %$data);
		$comune{CAP}=$data->{CAPS};
		$comune{ID}=$data->{IDCOMUNE};		
		delete $comune{DENOMSTD};
		my %frazione=map { 
			my $k=$_; 
			$k=~s/^F_//; 
			($k,$data->{$_}) 
		} grep(/^F_/,keys %$data);	
		$frazione{ID}=$data->{ID};
		$frazione{IDCOMUNE}=$data->{IDCOMUNE};
		$frazione{CAP}=$data->{FR_CAPS};			
		$out{COMUNE}=\%comune;
		$out{FRAZIONE}=\%frazione;		
	}
	else {
		my %comune=map {
			my $k=$_; 
			$k='DENOMINAZIONE_STD' if $k eq 'DENOMINAZIONESTD';
			$k='DENOMINAZIONE' if $k eq 'DENOMINAZIONEAPICISTD';
			$k='DENOMINAZIONE_ABBREVIATA' if $k eq 'DENOMABBREVSTD';								  			
			($k,$data->{$_});
		} keys %$data;
		$comune{CAP}=delete  $comune{CAPS};
		delete $comune{DENOMSTDQ};
		delete $comune{DENOMSTD};
		delete $comune{DENOMABBREV};
		$out{MATCH}={
			MATCHDENOMINAZIONE  => delete    $comune{MATCHDENOMINAZIONE}
			,MATCHSTD			=> delete	 $comune{MATCHSTD}
		};
		$out{COMUNE}=\%comune;
	}
	$self->{CACHE}->{GET_DATA}->{$key}=\%out if $self->{CACHE};
	return \%out;
}

	
sub free {
	my $self=shift;
	$self->{CSF}->free();
	if (ref($self->{PREPARE}) eq 'HASH') {      #chiude le prepare
		for my $v(values %{$self->{PREPARE}}) {
			if (ref($v)=~/^SIRTI::DB/) {
				$v->finish();
				$v=undef;
			}
			else {
			
			# qualcuno ha barato 
			}
		}
		$self->{PREPARE}={};
	}
}


sub DESTROY {
	my $self=shift;
	local $@;
	eval { $self->free(); }
}


1;

