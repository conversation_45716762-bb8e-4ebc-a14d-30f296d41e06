package SIRTI::Checks::CheckNumtel;

use strict;
use warnings;

use SIRTI::Err;
use SIRTI::Checks::CheckComunario;
use SIRTI::PLSQL::CSF;
use base qw(SIRTI::Checks::GenericCheck);




sub _split_numtel($$) {
	my $self=shift;
	my $n=shift;
	$n=$self->{ROW}->{NUMTELCOMPL} if !defined($n);
	return $self->{CSF}->split_numtelcompl($n);
}


sub _do_check($) {
	my $self=shift;


	my $numtelcompl=$self->{ROW}->{NUMTELCOMPL};
	
	return [] if !defined($numtelcompl); 
		
	my ($pref,$numtel) = $self->_split_numtel();



	return [ { 
				MSG 			=>  "numero telefonico non corretto"
				,NUMTELCOMPL    =>  $numtelcompl 				
			} ] if !defined($numtel); 



	return [ { 
				MSG 			=>  "prefisso telefonico non ricavabile dal numero telefonico"
				,NUMTELCOMPL    =>  $numtelcompl 				
			} ] if !defined($pref); 
			

	return [] unless defined($self->{ROW}->{COMUNE});
	

	my $db=$self->{DB};
	my $comune		= $db->quote($self->_translate_sql($self->{ROW}->{COMUNE}));
	my $provincia	= $db->quote($self->_translate_sql($self->{ROW}->{PROVINCIA}));
	
	my $msgs = $self->{CHECK_COMUNARIO}->do_check(
														{ 
																		COMUNE	=> $self->{ROW}->{COMUNE}
																		,PROVINCIA	=> $self->{ROW}->{PROVINCIA}
														}
												);
	return $msgs if scalar(grep(
									defined($_->{MSG}) 
										&&  SIRTI::Err::nvl($self->{CHECK_COMUNARIO}->{EMIT_MSG_ON_OK}) ne $_->{MSG} 
									,@$msgs)) > 0;
									
	my $data = $self->{CHECK_COMUNARIO}->get_data();
	
	my @prefissi=$self->{CSF}->get_comune_prefissi($data->{COMUNE}->{ID});

		
	
	return [ {
		MSG => "Prefissi telefonici per il comune indicato non trovati"
		,COMUNE			=> $self->{ROW}->{COMUNE}
		,PROVINCIA		=> $self->{ROW}->{PROVINCIA}
		,NUMTELCOMPL	=> $numtelcompl
	} ] unless scalar(@prefissi) > 0;
		
		
	return [ {
		MSG => "Prefisso non corretto per il comune specificato"
		,NUMTELCOMPL		=> $numtelcompl
		,PREFISSI_CORRETTI	=> join(', ',@prefissi)
		,PREFISSO			=> $pref
		,COMUNE				=> $self->{ROW}->{COMUNE}
		,PROVINCIA			=> $self->{ROW}->{PROVINCIA}
	} ] unless grep(/^$pref$/,@prefissi);
		
	
	return [];
}



### public

sub new($$) {
	my ($class,%params)=@_;
				#DB							=> istanza di SIRTI::DB (obbligatoria)
				#CSF						=> istanza di SIRTI::PLSQL::CSF
				#CHECK_COMUNARIO			=> istanza di SIRTI::Checks::CheckComunario

	SIRTI::Err::prec(defined($params{DB}),"connessione non definita (imposta il parametro DB come prima alternativa)"); 

	$params{CSF}=SIRTI::PLSQL::CSF->new(DB => $params{DB}) unless defined $params{CSF};

	$params{CHECK_COMUNARIO} = new SIRTI::Checks::CheckComunario(%params) unless defined $params{CHECK_COMUNARIO}; 

	return bless \%params,$class;
}

sub split_numtel($$) {
	my $self = shift;
	my $n = shift;
	return $self->_split_numtel($n);
}

sub get_mandatory_attrs() { return ['NUMTELCOMPL']; } # elenco attributi obbligatori per check 

sub get_optional_attrs() { return ['COMUNE','PROVINCIA']; } # elenco attributi opzionali

sub get_chektype() {  return 'NUMTEL'; }

sub get_data() { return undef; }

if (__FILE__ eq $0) {

	####  test ###  
	package main;


	use strict;
	use warnings;
	use Getopt::Std;
	
	use SIRTI::DB;
	

	my %Opt=();
	getopts ('c:v',\%Opt) or die "specifica l' opzione -c <oracleuser> ";



	my $db  = new SIRTI::DB($Opt{c}) ||  SIRTI::Err::db_fatal_error();

	my $sql= "
		select 
			r.id   						ID
			,r.numtelcompl				NUMTELCOMPL
			,r.comune					COMUNE
			,r.cap						CAP                     
			,r.provincia				PROVINCIA              
		from 
			rubrica r
		where 
	";


	$sql.=scalar(@ARGV) > 0 
			? 	(
					$ARGV[0] eq 'ALL' 
						?	" 1 = 1" 
						:   "r.id in (".join(',',@ARGV ).")"
				)
			:	
				"  r.codiceoperatoretlc='TD' and r.datacessazione is null";

	$sql="$sql  union select  0,null,'alessandria','15100','al' from dual";
	print $sql,"\n";

	my $cur = $db->create_cursor($sql) || SIRTI::Err::db_fatal_error($db);

	my $ck=new SIRTI::Checks::CheckNumtel(DB => $db);

	print "inizio check \n" if defined($Opt{v});

	$| = 1; # ==> $OUTPUT_AUTOFLUSH = 1

	my $row;
	my $checks;
	while(1) {
		$row = $cur->fetchrow_hashref() || last;
		print "check id ",$row->{ID},"\n" if defined($Opt{v});
		$checks=$ck->do_check($row);
		$ck->print_messages();
	}

	SIRTI::Err::db_fatal_error($db) if (SIRTI::DB::iserror());

	$cur->finish();
	$db->rollback();


	exit 0;
}
else {1;}







