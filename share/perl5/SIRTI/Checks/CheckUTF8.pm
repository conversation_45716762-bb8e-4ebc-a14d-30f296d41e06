package SIRTI::Checks::CheckUTF8;

use strict;
use warnings;

use SIRTI::Err;
use base qw(SIRTI::Checks::GenericCheck);
use Encode 'from_to';



sub _do_check($) {
	my $self=shift;
	my %h=();
	for my $k(keys %{$self->{ROW}}) {
		my $v=$self->{ROW}->{$k};
		next if !defined($v);
		$h{$k}=$v if isutf8($v);
	}
	return [] if scalar(keys(%h)) == 0;
	$h{MSG}="elenco attributi supposti in codifica utf8";
	return [\%h];
}


### public


sub new($$) {
	my $class=shift;
	my %param = @_;
	my $self = \%param;
	$self->{CHECK_ALL_DATA}=1;
	return bless $self,$class;
}



sub get_mandatory_attrs() { return [  ]; } # elenco attributi obbligatori per check 

sub get_optional_attrs() { return []; } # elenco attributi opzionali

sub get_chektype() {  return 'UTF8'; }

sub isutf8($)  { # check utf8 - statico 
	my $v=shift;
	my $l=length($v);
	my $nl=from_to($v,"utf-8","iso-8859-1");
	return $nl != $l ? 1 : 0;
}


if (__FILE__ eq $0) {

	####  test ###  
	package main;


	use strict;
	use warnings;
	use Getopt::Std;
}
else {1;}







