package SIRTI::Checks::CheckVersion;

use strict;
use warnings;
use Carp qw/croak/;

use SIRTI::Err;

use base qw(SIRTI::Checks::GenericCheck);

# public 
use constant ERR_OK				=> 0;       # il formato e' corretto
use constant ERR_EMPTY			=> 1;		# errore lunghezza  a 0   
use constant ERR_FORMAT			=> 2;       # formato non valido 


#private

sub _check_Version($) { # static method
	my $version = shift;
    return ERR_OK if !defined($version);
    return ERR_EMPTY if( $version eq "" );
    my @c = split /\./, $version;
    for my $c ( @c ) {
    	return ERR_FORMAT unless( $c =~ /^\d+$/ );
    }
    return ERR_OK;
}

sub _do_check($) {
	my $version=$_[0]->{ROW}->{VERSION};
	my $rc = _check_Version($version);
	return $rc == ERR_OK
		? 	[]
		:	[{
				MSG 			=>	"formato non valido (codice $rc)"
				,VERSION		=>  $version
			}];
}

=pod
$ck->compare_versions ( $v1, $v2 )
funziona solo con notazione dotted decimale
ritorna 1 se $v1 > $v2
ritorna -1 se $v1 > $v2
ritorna 0 se $v1 = $v2
croak se $v1 o $v2 non rispettano il formato
es. $ck->compare_versions ( '1.9.0', '1.8.1' )
=cut
sub compare_versions {
	my $self = shift;
	my $v1 = shift;
	my $v2 = shift;
	for my $v ( $v1, $v2 ) {
		my $msgs = $self->do_check({ VERSION => $v });
		croak join( "\n", @{$self->get_string_messages()})
			if ( @$msgs );
	}
    my @v1 = split( /\./, $v1 );
    my @v2 = split( /\./, $v2 );
    my $max = ( @v1 > @v2 ? @v1 : @v2 );
    for ( my $i = 0; $i < $max; $i++ ) {
    	$v1[$i] = 0 unless defined ( $v1[$i] );
    	$v2[$i] = 0 unless defined ( $v2[$i] );
        return 1 if ( $v1[$i] > $v2[$i] );
        return -1 if ( $v1[$i] < $v2[$i] );
    }
    return 0;
}


### public


sub get_mandatory_attrs() { return ['VERSION']; } # elenco attributi obbligatori per check 

sub get_optional_attrs($) { return []; }

sub get_chektype() {  return 'VERSION'; }



if (__FILE__ eq $0) {


	####  test ###  
	package main;
	my $ck = SIRTI::Checks::CheckVersion->new();


	if (scalar(@ARGV) == 0) {
		my %cf=(	
					'1.8.0'		=> 0
					,'1.9.0.4'	=> 0
					,'1.0.0RC3'	=> 2
					,'1.0.0RC4'	=> 2
					,'1'		=> 0
				);

		for my $c(keys %cf) {
			my $msgs=$ck->do_check({ VERSION => $c });
			my $codice=sub {
				my $msg=$msgs->[0]->{MSG};
				return '0' unless defined $msg;
				$msg=~/codice\s+(\d+)/;
				return '' unless defined $1;
				return $1;
			}->();
			sub {
				print STDERR $c,": codici diversi - ";
				print STDERR "restituito ",$codice," invece di  ",$cf{$c},"\n";
				print STDERR "\n";
			}->() if $codice ne $cf{$c};
		}
			
	}
	else {
		for my $cod(@ARGV) {
			$ck->do_check({ 
					CODFISCALE => $cod
					,CHECK_CHECKSUM => 1
				});
			$ck->print_messages();
		}
	}
	
	print "compare_versions ( '1.9.0', '1.8.1' ) = " . $ck->compare_versions ( '1.9.0', '1.8.1' ) ."\n";
	print "compare_versions ( '1.9.0', '1.18.1' ) = " . $ck->compare_versions ( '1.9.0', '1.18.1' ) ."\n";
	print "compare_versions ( '1.9.0', '1.8.1.0' ) = " . $ck->compare_versions ( '1.9.0', '1.8.1.0' ) ."\n";
	print "compare_versions ( '1.9.0', '1.9.0.1' ) = " . $ck->compare_versions ( '1.9.0', '1.9.0.1' ) ."\n";
	print "compare_versions ( '1.9.0.1', '1.9.0' ) = " . $ck->compare_versions ( '1.9.0.1', '1.9.0' ) ."\n";
	print "compare_versions ( '1.9.0', '1.9.0' ) = " . $ck->compare_versions ( '1.9.0', '1.9.0' ) ."\n";
	print "compare_versions ( '1.19', '1' ) = " . $ck->compare_versions ( '1.19', '1' ) ."\n";
	print "compare_versions ( '1.19', '2' ) = " . $ck->compare_versions ( '1.19', '2' ) ."\n";
	
	my @versions = ( '1', '0.9.2', '1.9.1', '1.8.1', '2.0.0', '1.9.1.1' );
	print "ORIGINALE: " . Dumper( \@versions );
	print "ORDINATO:  " . Dumper( [ sort { $ck->compare_versions( $a, $b ) } @versions ] ); 
	
}
else {1;}
