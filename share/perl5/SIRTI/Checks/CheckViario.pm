package SIRTI::Checks::CheckViario;

use strict;
use warnings;
#use Benchmark;

use SIRTI::Err qw(nvl);
use SIRTI::Checks::CheckComunario;
use base qw(SIRTI::Checks::GenericCheck);  #forse e' piu logico ereditare da comunario
use Storable;					


use DBI qw(:sql_types);
use DBD::Oracle qw(:ora_types);

							   		
sub _check_comunario  {
	my $self=shift;
	my %params=@_;
	my %v=%{$self->{ROW}};
	delete $v{CAP} unless $params{CAP_CHECK};
	$self->{CHECK_COMUNARIO}->{ROW}=\%v;
	my ($m,$comunario,$frazionario)=$self->{CHECK_COMUNARIO}->_check_comune_low_new();	
	
	if (defined($comunario) && $comunario->{FLAGSTRADARIO} eq 'S') {  # se e' un comune con strade
		# il test e' fatto senza il cap perche' viene testata la strada 
	}
	else { # va rifatto il test con il cap, per un comune non multicap oppure una frazione di un comune non multicap
			# o comune non trovato
		$self->{CHECK_COMUNARIO}->{ROW}=$self->{ROW};
		($m,$comunario,$frazionario)=$self->{CHECK_COMUNARIO}->_check_comune_low_new();	
	}


	for my $k(@$m) {
		$k->{CHECKTYPE}=$self->{CHECK_COMUNARIO}->get_chektype() if !defined($k->{CHECKTYPE});
		$k->{ID}=$self->{ROW}->{ID} if !defined($k->{ID});
	}
	return ($m,$comunario,$frazionario);
}	


{
	my %parita= (
		K	=> 'KM'
		,D	=> 'DISPARI'
		,P	=> 'PARI'
	);

	my %colore=(
		R	=> 'ROSSO'
		,N	=> 'NERO'
	);

	sub _get_civici {
		my ($self,$idstrada,$idsestiere)=@_;
		my $db=$self->{DB};
		my $rows=$self->{CSF}->get_strada_ncivs($idstrada,$idsestiere) || SIRTI::Err::db_fatal_error($db);
		return  join(',',map {
								my $r='';
								if (defined($_->{PARITA})) {
									my $p=$_->{PARITA};
									$p=$parita{$p} if defined $parita{$p};
									$r.=$p.' ';
								}
								my $dal = $_->{NDAL}.(defined($_->{ESPONENTEDAL}) ? '/'.$_->{ESPONENTEDAL} : '');
								my $al = $_->{NAL}.(defined($_->{ESPONENTEAL}) ? '/'.$_->{ESPONENTEAL} : '');
								$r.=$dal eq $al ? $dal : $dal.'-'.$al;
								if (defined($_->{COLORE})) {
									my $c= $_->{COLORE};
									$c=$colore{$c} if defined $colore{$c};
									$r.=' '.$c;
								}
								$r;
							} @$rows
					);
		
	}
}


sub _get_dugstd {
	my $self=shift;
	my $db=$self->{DB};
	my $x=$self->{CSF}->get_dugstd() || SIRTI::Err::db_fatal_error($db);
	return $x;
}	


{
	my %colore=(
		'R'	=> 'ROSSO'
		,'N' => 'NERO'
	);
	sub _get_complet_addr {
		my ($self,$ind)=@_;
		my $r = $ind->{DENOMAPICISTD};
		$r .= ', '.SIRTI::Err::nvl($self->{ROW}->{NUMCIVICO},'SNC');
		$r .= '/'.$self->{ROW}->{ESPONENTE}  if defined $self->{ROW}->{ESPONENTE};
		$r .= ' '.SIRTI::Err::nvl($colore{$self->{ROW}->{COLORE}},$self->{ROW}->{COLORE}) if defined $self->{ROW}->{COLORE};
		$r .= ' QUARTIERE '.$ind->{QUARTIEREAPICI} if defined $ind->{QUARTIEREAPICI};
		$r .= ' '.$self->{ROW}->{NOTE} if length(SIRTI::Err::nvl($self->{ROW}->{NOTE})) > 0;
		return $r;
	}
}


sub _get_parttopon {
	my ($self,$idstrada,$idsestiere)=@_;
	my $db=$self->{DB};
	my $r=$self->{CSF}->get_strada_parttopons($idstrada,$idsestiere,MATCH_TIPO_DENOMINAZIONE => [ 'S','Q'] ) ||  SIRTI::Err::db_fatal_error($db);
	return @$r;
}
	

sub _get_errmsg {
	my $self=shift;
	return SIRTI::Checks::CheckComunario::_get_errmsg($self);
}


sub _do_check_strada {
	my $self=shift;
	my $idcomune=shift;
	my %params=@_;
	my $db = $self->{DB};
	my ($part,$descind,$cap)=defined $params{ROW} 
		? ($params{ROW}->{PARTTOPON},$params{ROW}->{DESCINDIRIZZO},$params{ROW}->{CAP})
		: ($self->{ROW}->{PARTTOPON},$self->{ROW}->{DESCINDIRIZZO},$self->{ROW}->{CAP})
	;

	$part=' ' if defined $part and $part=~/^\s+$/;

	
	my %p=(
		ID_COMUNE		=> $idcomune
		,PARTTOPON		=> $part
		,DENOMINAZIONE	=> $descind
		,CAP			=> $cap
		,ATTEMPT_SEARCH_RESOLUTION	=> $self->{ATTEMPT_SEARCH_RESOLUTION} ? 1 : 0
		,PARTTOPON_RESTRICTED_CHECK	=> $self->{PARTTOPON_RESTRICTED_CHECK} ? 1 : 0
	);		
	my $res=$self->{CSF}->do_check_strada(%p) || SIRTI::Err::db_fatal_error($db);
	return @$res;
}


sub _do_check_nciv {
	my $self=shift;
	my $id_strada=shift;
	my $id_sestiere=shift;
	my %params=@_;
	my ($nciv,$parita,$exp,$colore,$cap)=defined $params{ROW}
		? ($params{ROW}->{NUMCIVICO},$params{ROW}->{PARITA},$params{ROW}->{ESPONENTE},$params{ROW}->{COLORE},$params{ROW}->{CAP})
		: ($self->{ROW}->{NUMCIVICO},$self->{ROW}->{PARITA},$self->{ROW}->{ESPONENTE},$self->{ROW}->{COLORE},$self->{ROW}->{CAP})
	;

	my %p=(
			ID_STRADA 		=> $id_strada
			,ID_SESTIERE	=> $id_sestiere
			,NCIV			=> $nciv
			,PARITA			=> $parita
			,EXP			=> $exp
			,COLORE			=> $colore
			,CAP			=> $cap
			,ATTEMPT_SEARCH_RESOLUTION	=> $self->{ATTEMPT_SEARCH_RESOLUTION} ? 1 : 0
	);
	my $db=$self->{DB};
	my $res=$self->{CSF}->do_check_nciv(%p) || SIRTI::Err::db_fatal_error($db);
	return @$res;
}

sub _do_check($) {
	my $self=shift;
	$self->{__STRADA__}=undef;
	$self->{__NCIV__}=undef;
	

	my @msgs=();
	delete $self->{ROW}->{NUMCIVICO} if length(SIRTI::Err::nvl($self->{ROW}->{NUMCIVICO})) == 0;
	# test  attibuti topo indipendenti dal comune
	if ( defined $self->{ROW}->{NUMCIVICO} ) {
		$self->{ROW}->{NUMCIVICO_INPUT} = $self->{ROW}->{NUMCIVICO};
		if ( $self->{ROW}->{NUMCIVICO} =~ m/^KM / ) {
			$self->{ROW}->{PARITA} = 'K';			# Imposto flag di parita' x numeri civico espressi in kilometri
			$self->{ROW}->{NUMCIVICO} =~ s/^KM //;	# Rimuovo l'unita' di misura KM
			$self->{ROW}->{NUMCIVICO} =~ s/\,/./;	# Converto in notazione anglosassone
			$self->{ROW}->{NUMCIVICO} = (0 + $self->{ROW}->{NUMCIVICO}) * 1000; # Converto in metri
		} elsif ( $self->{ROW}->{NUMCIVICO} =~ m/^(\d+)[\-\/\s]?(.*?)$/ ) {
			my $civico = (0 + $1) if defined($1);
			#$self->{ROW}->{PARITA} = (($civico/2) == int($civico/2) ? 'P' : 'D' );
			$self->{ROW}->{NUMCIVICO} = $civico;
			$self->{ROW}->{ESPONENTE} = $2 if defined($2) && $2 ne ''; 
		} else {
			
		}
	}

	$self->{ROW}->{PARITA} = undef if SIRTI::Err::nvl($self->{ROW}->{PARITA}) eq ''; 
	SIRTI::Err::check(!defined($self->{ROW}->{PARITA}) || $self->{ROW}->{PARITA} eq 'K');  

	if ( defined($self->{ROW}->{COLORE}) ) {
		$self->{ROW}->{COLORE} = substr($self->{ROW}->{COLORE}, 0, 1);  
		$self->{ROW}->{COLORE} = undef if $self->{ROW}->{COLORE} eq '-';
	}
	
	push @msgs,{
		MSG			=> 'numero civico non numerico oppure non SNC o stringa nulla'
		,NUMCIVICO		=> $self->{ROW}->{NUMCIVICO}
	} if defined($self->{ROW}->{NUMCIVICO}) && uc($self->{ROW}->{NUMCIVICO}) ne 'SNC' && $self->{ROW}->{NUMCIVICO} !~ /^\d*$/;
	
	
	push @msgs,{
		MSG			=> 'parte toponomastica non definita oppure a lunghezza 0'
	} if SIRTI::Err::nvl($self->{ROW}->{PARTTOPON}) eq '';
	
	push @msgs,{
		MSG			=> 'comune non definito'
	} if !defined($self->{ROW}->{COMUNE});

 
 	if (defined($self->{ROW}->{PARTTOPON}) && $self->{ROW}->{PARTTOPON} !~/^\s*$/) { # a blank e' valido 
		push @msgs,{
			MSG			=> 'parte toponomastica non conosciuta'
			,PARTTOPON		=> $self->{ROW}->{PARTTOPON}
		} if defined($self->{ROW}->{PARTTOPON}) && length($self->{ROW}->{PARTTOPON}) != 0 && scalar(grep /^$self->{ROW}->{PARTTOPON}$/,@{$self->{DUGSTD}}) == 0;
	}

	if (defined($self->{ROW}->{NOTE})) {
		$self->{ROW}->{DESCINDIRIZZO}.=' '.$1 if $self->{ROW}->{NOTE}=~s/\bQUARTIERE\s+(.*)$//;
		$self->{ROW}->{NOTE}=~s/^\s+//;
		$self->{ROW}->{NOTE}=~s/\s+$//;
		$self->{ROW}->{NOTE}=undef if $self->{ROW}->{NOTE} =~/^\s*$/;
	}

	return \@msgs if !defined($self->{ROW}->{COMUNE});
	
	my ($m,$comunario,$frazionario)=_check_comunario($self);
	push @msgs,@$m;
#	$self->{COMUNEHASVIARIO}->{$comunario->{ID}}= $comunario->{FLAGSTRADARIO} eq 'S' ? 1 : 0;
	
	
	
	#_comunehasviario($self->{DB},$comunario->{ID},$self) if defined($comunario->{ID}) && !exists $self->{COMUNEHASVIARIO}->{$comunario->{ID}};
	

	if (
		!defined($comunario->{ID})
		|| (defined($self->{ROW}->{FRAZIONE}) && $frazionario->{FLAGSTRADARIO} ne 'S')
#		|| $self->{COMUNEHASVIARIO}->{$comunario->{ID}} == 0 # il comune non ha un viario associato 
		|| (defined $self->{ROW}->{COMUNE} && $comunario->{FLAGSTRADARIO} ne 'S')
	)	{  # completa i test mancanti
=pod
			push @msgs,{
				MSG			=> 'numero civico non definito '
			} if !defined($self->{ROW}->{NUMCIVICO});
=cut			
			return \@msgs; # ed esce 
		}


	my ($errcode,$indirizzi)=$self->_do_check_strada($comunario->{ID});

	$self->{__STRADA__}=$indirizzi->[0] if scalar(@$indirizzi) == 1; 


	if (scalar(@$errcode) > 0) {
		my  $msg = {
			PARTTOPON		=>	$self->{ROW}->{PARTTOPON}
			,DESCINDIRIZZO	=>  $self->{ROW}->{DESCINDIRIZZO}
			,FRAZIONE		=>	$self->{ROW}->{FRAZIONE}
			,COMUNE			=>  $self->{ROW}->{COMUNE}
			,PROVINCIA		=>  $self->{ROW}->{PROVINCIA}
			,CAP			=> 	$self->{ROW}->{CAP}
		};
		
		
		if (scalar(@$indirizzi) == 0 && defined $self->{ROW}->{CAP}) {  #la strada non e' stata trovata quindi rifa' il test 
																		#sul comune includendo il cap
			my ($m,$comunario,$frazionario)=_check_comunario($self,CAP_CHECK => 1);
			@msgs=@$m;
		}														
		
		if (defined $self->{__STRADA__}) {
			if (defined  $self->{ROW}->{NUMCIVICO} 
				&& $self->{NUMCIV_RESTRICTED_CHECK} 
				&& defined $self->{ROW}->{CAP} 
				&& $self->{__STRADA__}->{MULTICAP} eq 'S' 
				) {
				#il test sul cap viene fatto subito dopo sul numero civico 
				#quindi per evitare l' errore di cap non valido a livello di strada si rifa il test 
				#sulla strada senza usare il cap
				my %row=%{$self->{ROW}};
				delete $row{CAP};
				($errcode,$indirizzi)=$self->_do_check_strada($comunario->{ID},ROW => \%row);
			}

			for my $e(@$errcode) {
				my %m=%$msg;
				my $errd = $self->_get_errmsg()->[$e];
				if ($e==$self->{CSF}->ERR_INCONGR_CAP_STRADA) {
					my $a=$self->{__STRADA__}->{CAP};
					my $k=scalar(@$a) == 1 ? 'CAP_CORRETTO' : 'CAP_CORRETTI';
					$m{$k}=join(',',@$a);
					$errd=~s/ambito\s+della\s+via/ambito del numero civico/ if defined  $self->{ROW}->{NUMCIVICO} && $self->{NUMCIV_RESTRICTED_CHECK};
				}
				elsif ($e==$self->{CSF}->ERR_INCONGR_PT_STRADA) {
					my @pt=$self->_get_parttopon($self->{__STRADA__}->{ID_STRADA},$self->{__STRADA__}->{ID_SESTIERE});
					my $k=scalar(@pt) == 1 ? 'PARTTOPON_CORRETTA' : 'PARTTOPON_CORRETTE';
					$m{$k}=join(',',@pt);
					$m{SUGGERIMENTO}=$self->{COMPLET_SUGGEST}   
								? $self->_get_complet_addr( $self->{__STRADA__})
								:  $self->{__STRADA__}->{DENOMABBREVAPICISTD};


				}
				elsif ($e == $self->{CSF}->ERR_STRADA_DIZIONE_ERRATA) {
					$m{SUGGERIMENTO}=$self->{COMPLET_SUGGEST}   
								? $self->_get_complet_addr( $self->{__STRADA__})
								:  $self->{__STRADA__}->{DENOMABBREVAPICISTD};
				}
				$m{MSG}=$errd;
				push @msgs,\%m;
			}
			
		}
		else {
			for my $e(@$errcode) {
				my %m=%$msg;
				my $errd = $self->_get_errmsg()->[$e];
				if (scalar(@$indirizzi) > 1 && grep($e == $_,($self->{CSF}->ERR_STRADA_DIZIONE_ERRATA,$self->{CSF}->ERR_STRADA_NON_UNIVOCA))) {
					my $i = 0;
					foreach my $ind(@$indirizzi) {
						++$i;
						$m{"SUGGERIMENTO_${i}"} = $self->{COMPLET_SUGGEST}   
							? $self->_get_complet_addr($ind)
							: $ind->{DENOMABBREVAPICISTD};
					}	
				}
				$m{MSG}=$errd;
				push @msgs,\%m;
			}
		}
	}
	
	
	if (defined  $self->{ROW}->{NUMCIVICO} && $self->{NUMCIV_RESTRICTED_CHECK} && defined $self->{__STRADA__} && $self->{__STRADA__}->{MULTICAP} eq 'S') {  
		my ($errcode,$nciv)=$self->_do_check_nciv($self->{__STRADA__}->{ID_STRADA},$self->{__STRADA__}->{ID_SESTIERE});

		$self->{__NCIV__}= $nciv->[0] if scalar(@$nciv) == 1; 
		if (scalar(@$errcode) > 0) {				
			my $msg={
				MSG				=>  $self->_get_errmsg()->[$errcode->[0]] 
				,EXTRA_CIVICO	=>	$self->{ROW}->{EXTRA_CIVICO}
				,NUMCIVICO		=>  $self->{ROW}->{NUMCIVICO}
				,PARTTOPON		=>	$self->{ROW}->{PARTTOPON}
				,FRAZIONE		=>	$self->{ROW}->{FRAZIONE}
				,DESCINDIRIZZO	=>  $self->{ROW}->{DESCINDIRIZZO}
				,COMUNE			=>  $self->{ROW}->{COMUNE}
				,PROVINCIA		=>  $self->{ROW}->{PROVINCIA}
				,CAP			=> 	$self->{ROW}->{CAP}
			};	
			$msg->{NUMERI_CORRETTI} = $self->_get_civici($self->{__STRADA__}->{ID_STRADA},$self->{__STRADA__}->{ID_SESTIERE}) if scalar(@$nciv) == 0 || $msg->{MSG}=~/colore/i;
			$msg->{CAP_CORRETTI}=join(',',@{$self->{__STRADA__}->{CAP}}) if $msg->{MSG}=~/cap/i && scalar(@$nciv) == 0;
			$msg->{CAP_CORRETTO}=$self->{__NCIV__}->{CAP} if $msg->{MSG}=~/cap/i && scalar(@$nciv) ==1;
			push @msgs,$msg;
		}
		
		if (defined $self->{__NCIV__}->{CAP}) {
			$self->{__STRADA__}->{CAP}=$self->{__NCIV__}->{CAP};
		}
		
	}
	return \@msgs;
}

# public 
sub new($$) {
	my $class=shift;
	my %params = @_;
	my $self = \%params;
		#DB							=> istanza di SIRTI::DB
		#NO_CONV_ACCENTED_CHARS    	=> se vero non cambia le lettere accentate nelle corrispondenti non accentate 
		#NO_TRANSLATE_SPECIAL_CHARS => se vero non converte i caratteri speciali (es: ' " #) in spazio prima del check
		#CACHE_DATA					=> se vero scambia tempo di elaborazione con spazio in memoria
		#PARAMS_LENGTH				=> hash di parametri per valorizzare restrizioni sulle lunghezze
		#NUMCIV_RESTRICTED_CHECK	=> se vero  esegue un check di esistenza su numero civico e sulla coppia numcivico/cap
		#							   se  falso esegue solo check di validita solo su cap/indirizzo  						   
		#PARTTOPON_RESTRICTED_CHECK => se vero esegue  un controllo restrittivo sulla parte toponomastica
		#							   se falso un controllo piu rilassato 
		#ATTEMPT_SEARCH_RESOLUTION  => se vero e non riesce a risolvere effettua altri tentativi per ricercare le informazioni immesse
		#								 ATTENZIONE - la ricerca puo essere lenta
		#                                questa opzione e' utile in un sw interattivo con un utente umano
		#COMPLET_SUGGEST			=>  se vero emette come suggerimenti la stringa completa dell' indirizzo
		#
		#LEGACY_ERRMSG				=> se vero cerca di emettere i messaggi in modalita compatibile con la versione non csf
		#
		#CSF						=> istanza di SIRTI::PLSQL::CSF
		#CHECK_COMUNARIO			=> istanza di SIRTI::Checks::CheckComunario
		
	SIRTI::Err::prec(defined($self->{DB}),"connessione non definita (imposta il parametro DB come prima alternativa)"); 

	$self->{CHECK_COMUNARIO} = SIRTI::Checks::CheckComunario->new(%params) unless defined $self->{CHECK_COMUNARIO};
	delete $self->{__STRADA__};
	delete $self->{CACHE};
	$self->{CACHE}={} if $self->{CACHE_DATA};
	
	$self->{PARAMS_LENGTH}=$self->{CHECK_COMUNARIO}->{PARAMS_LENGTH} unless defined $self->{PARAMS_LENGTH};
	$self->{CSF}=$self->{CHECK_COMUNARIO}->{CSF} unless defined  $self->{CSF};
	$self->{DB}=$self->{CHECK_COMUNARIO}->{DB} unless defined $self->{DB};
	bless $self,$class;
	$self->{DUGSTD}=$self->_get_dugstd();
	return $self;

}



sub get_mandatory_attrs($) { 
	my $self=shift;
	return $self->{CACHE}->{MANDATORY_ATTRS} if defined($self->{CACHE}->{MANDATORY_ATTRS});
	my @attr=('PARTTOPON','DESCINDIRIZZO','NUMCIVICO');
	push @attr,@{$self->{CHECK_COMUNARIO}->get_mandatory_attrs()};
	$self->{CACHE}->{MANDATORY_ATTRS}=\@attr;
	return \@attr;
}


sub get_optional_attrs($) {
	my $self=shift;
	return $self->{CACHE}->{OPTIONAL_ATTRS} if defined($self->{CACHE}->{OPTIONAL_ATTRS});
	my @attr=('ESPONENTE','NOTE','COLORE','PARITA');
	push @attr,@{$self->{CHECK_COMUNARIO}->get_optional_attrs()};
	$self->{CACHE}->{OPTIONAL_ATTRS}=\@attr;
	return \@attr;
}

sub get_params { 
	my $h=SIRTI::Checks::CheckComunario::get_params();
	my %parms = (
			NUMCIV_RESTRICTED_CHECK		=> 'esegue un check di esistenza su numero civico e sulla coppia numcivico/cap (per default esegue check di validita solo su cap/indirizzo)'  						   
			,PARTTOPON_RESTRICTED_CHECK => 'esegue  un controllo restrittivo sulla parte toponomastica altrimento un controllo piu rilassato'
			,COMPLET_SUGGEST					=>  'emette come suggerimenti la stringa completa dell\' indirizzo'
			,LEGACY_ERRMSG				=>  'cerca di emettere i messaggi in modalita compatibile con la versione non csf (precedente versione)'
	);
	
	for my $k(keys %parms) {
		$h->{$k}=$parms{$k}
	}
	return $h;
}

sub get_chektype($) {  return 'VIARIO'; }

sub get_id_comune {
	my $self = shift;
	return undef unless defined $self->{CHECK_COMUNARIO};
	return $self->{CHECK_COMUNARIO}->get_id_comune();
}

sub get_id_frazione {
	my $self = shift;
	return undef unless defined $self->{CHECK_COMUNARIO};
	return $self->{CHECK_COMUNARIO}->get_id_frazione();
}

sub get_id_strada { $_[0]->{__STRADA__}->{ID_STRADA}; }

sub get_id_sestiere { $_[0]->{__STRADA__}->{ID_SESTIERE}; }


{
	my %tr=(
			DENOMSTD	=> 'DENOMINAZIONE_STD'
			,DENOMABBREVAPICISTD	=> undef
			,DENOMABBREVSTD	=> 'DENOMINAZIONE_ABBREVIATA_STD'
			,DENOMAPICISTD	=> undef
			,DUGAPICISTD	=> undef
			,DUGCOMPLAPICISTD => undef
			,DUGCOMPLSTD => undef
			,DUGSTD => undef
	);
	sub get_data {
		my $self = shift;
		my $db = $self->{DB};

		my $data = $self->{CHECK_COMUNARIO}->get_data();

		my $rawdata = $self->{ROW};


		$data->{STRADA}=defined $self->{__STRADA__} ? Storable::dclone($self->{__STRADA__}) : {};
		$data->{STRADA}->{NUMCIVICO} = $rawdata->{NUMCIVICO};
		$data->{STRADA}->{ESPONENTE} = $rawdata->{ESPONENTE};
		$data->{STRADA}->{NOTE} = $rawdata->{NOTE};
		$data->{STRADA}->{PARTTOPON}=$data->{STRADA}->{DUGSTD} if defined $data->{STRADA}->{DUGSTD};
		$data->{STRADA}->{ID}=$data->{STRADA}->{ID_STRADA} if defined $data->{STRADA}->{ID_STRADA};
		$data->{STRADA}->{DENOMINAZIONE}=$self->trimmer(nvl($data->{STRADA}->{DUGCOMPLSTD}).' '.nvl($data->{STRADA}->{NOMESTD}));


		for my $k(keys %{$data->{STRADA}}) {
			next unless exists $tr{$k};
			my $v=$tr{$k};
			if  (defined $v) {
				$data->{STRADA}->{$v} = delete $data->{STRADA}->{$k};
			}
			else {
				delete $data->{STRADA}->{$k};
			}
		}

		$data->{STRADA}->{DENOMINAZIONE_STD}=$data->{STRADA}->{NOMESTD}; #per compatibilita' con le vecchie lib
		$data->{STRADA}->{DENOMINAZIONE_STD}.=' '.$data->{STRADA}->{QUARTIERE} if defined $data->{STRADA}->{QUARTIERE};
		$data->{STRADA}->{DENOMINAZIONE_ABBREVIATA} = $data->{STRADA}->{DENOMINAZIONE_ABBREVIATA_STD};


		$data->{STRADA}->{INDIRIZZO_POSTALE} = $self->trimmer(
				(defined($data->{STRADA}->{PARTTOPON})  ? "$data->{STRADA}->{PARTTOPON} "  : '')
				. SIRTI::Err::nvl($data->{STRADA}->{DENOMINAZIONE})
				.(defined($data->{STRADA}->{NUMCIVICO}) ? ', '.$data->{STRADA}->{NUMCIVICO} : '')
				.(defined($data->{STRADA}->{NUMCIVICO}) && defined($data->{STRADA}->{ESPONENTE})
							? '/' . $data->{STRADA}->{ESPONENTE}
							: ''
				 )
				.(defined($data->{STRADA}->{NUMCIVICO}) && defined($data->{STRADA}->{COLORE})
							? ' '.$data->{STRADA}->{COLORE}
							: ''
				)
				.SIRTI::Err::nvl	(	$data->{STRADA}->{QUARTIERE}, 
										' QUARTIERE ' . SIRTI::Err::nvl($data->{STRADA}->{QUARTIERE}) . ' ', 
										''
									)
		);

		$data->{STRADA}->{NOTE} = $self->trimmer(
			SIRTI::Err::nvl	(	$data->{STRADA}->{QUARTIERE}, 
								'QUARTIERE ' . SIRTI::Err::nvl($data->{STRADA}->{QUARTIERE}) . ' ', 
								''
							) . 
			SIRTI::Err::nvl($data->{STRADA}->{NOTE})
		) unless $self->{COMPLET_SUGGEST};

		$data->{STRADA}->{NOTE}='' if SIRTI::Err::nvl($data->{STRADA}->{NOTE}) =~/^\s*$/;



		$data->{RAW} = Storable::dclone($rawdata);


		delete $data->{STRADA} 	unless defined $self->{__STRADA__};
		return $data;	
	}
}

sub free() {
	my $self=shift;
	if (ref($self->{PREPARE}) eq 'HASH') {      #chiude le prepare
		for my $v(values %{$self->{PREPARE}}) {
			$v->finish() if ref($v)=~/^SIRTI::DB/;
		}
		$self->{PREPARE}={};
	}
	$self->{CHECK_COMUNARIO}->free();
	$self->{CSF}->free();
}

sub DESTROY {
	my $self=shift;
	eval { $self->free(); }
}

1;




