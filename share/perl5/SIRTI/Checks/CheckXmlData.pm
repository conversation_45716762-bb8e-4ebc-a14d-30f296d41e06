package SIRTI::Checks::CheckXmlData::<PERSON>rrorHandler;

use XML::Xerces;
use IO::Handle;

use base  qw(XML::Xerces::PerlErrorHandler);

sub _new {
	my $this = shift;
	my $class = ref($this) || $this;
	my $self = {
		MSGS	=> []
	};
	return bless $self, $class;
}



sub _get_msg($$) {
	my ($super,$type)=@_;
	return {
		TYPE	=> $type
 		,LINE	=> $super->getLineNumber
 		,COL	=> $super->getColumnNumber
 		,MSG	=> $super->getMessage
 	};
}

sub warning {
	my ($self,$super)=@_; 		
 	push @{$self->{MSGS}},_get_msg($super,'W');
}

sub error {
	my ($self,$super)=@_;
	push @{$self->{MSGS}},_get_msg($super,'E');
}

sub fatal_error {
	my ($self,$super)=@_;
	push @{$self->{MSGS}},_get_msg($super,'F');
}

package SIRTI::Checks::CheckXmlData;

use strict;
use warnings;

use SIRTI::Err;

use base qw(SIRTI::Checks::GenericCheck);


#private

	

sub _do_check($) {
	my $self=shift;
	my $cv=$self->get_xmlconverter();
	my $t=$cv->do_convert($self->{ROW});
	my $msgerr = $cv->get_errmsg();

	return [
				{
					MSG	=> 'dati non validi per schema xml'
					,MSGS	=> $msgerr
					,BUFF	=> $t
				}
			] if defined($msgerr);
  	my $is = XML::Xerces::MemBufInputSource->new($t);
  	$self->{ERRHANDLER}->{MSGS}=[];
  	$self->{PARSER}->parse($is);
	my $msgs=$self->{ERRHANDLER}->{MSGS};
	return scalar(@$msgs) == 0
		? []
		: [
			{
				MSG		=> 'dati non validi per schema xml'
				,MSGS	=> $msgs
				,BUFF	=>  $t
			}
		];				
}


# public 

sub get_mandatory_attrs($) { # elenco attributi obbligatori per check 
	return $_[0]->get_xmlconverter()->get_mandatory_attrs();
} 

sub get_optional_attrs($) { # elenco attributi opzionali per check 
	return $_[0]->get_xmlconverter()->get_optional_attrs();
}

sub get_chektype() {  return 'XMLDATA'; }

sub get_params { 
	my $h=SIRTI::Checks::GenericCheck::get_params();
	my %parms = (
		XMLCONVERTER			=>  'istanza di una classe convertitrice figlia di DBU::Conversion::GenericConversion + DBU::Conversion::XmlMethods'
		,PARSER						=>  'istanza parser Xerces (opzionale)'
	);
	for my $k(keys %parms) {
		$h->{$k}=$parms{$k}
	}
	return $h;
}

sub get_xmlconverter($) { return $_[0]->{XMLCONVERTER}; }

sub new($$) {
	my $class=shift;
	my %param = @_;
	my $self = \%param;
				#XMLCONVERTER			=>  istanza di una classe convertitrice figlia di DBU::Conversion::GenericConversion + DBU::Conversion::XmlMethods
				#PARSER					=>  istanza parser Xerces (opzionale)
	SIRTI::Err::prec(defined($self->{XMLCONVERTER}),"parametro XMLCONVERTER non definito");
	if (!defined($self->{PARSER})) {
		$self->{PARSER} = XML::Xerces::XMLReaderFactory::createXMLReader();
		$self->{ERRHANDLER}=_new SIRTI::Checks::CheckXmlData::ErrorHandler;
		$self->{PARSER}->setErrorHandler($self->{ERRHANDLER});
		$self->{PARSER}->setFeature("$XML::Xerces::XMLUni::fgSAX2CoreNameSpaces", 1);
		$self->{PARSER}->setFeature("$XML::Xerces::XMLUni::fgXercesSchema", 1);
		$self->{PARSER}->setFeature("$XML::Xerces::XMLUni::fgXercesSchemaFullChecking", 1);
		$self->{PARSER}->setFeature("$XML::Xerces::XMLUni::fgXercesContinueAfterFatalError", 1);
		$self->{PARSER}->setFeature("$XML::Xerces::XMLUni::fgXercesValidationErrorAsFatal", 0);
		$self->{PARSER}->setFeature("$XML::Xerces::XMLUni::fgSAX2CoreValidation", 1);
		$self->{PARSER}->setFeature("$XML::Xerces::XMLUni::fgXercesDynamic", 1);
	}
	bless $self,$class;
	# Associa, se richiesto, uno schema XML (xsd) esterno
	if ( defined($self->get_xmlconverter()->get_xmlschema())) {
		$self->{PARSER}->setProperty("$XML::Xerces::XMLUni::fgXercesSchemaExternalNoNameSpaceSchemaLocation", $self->get_xmlconverter()->get_xmlschema());
	}

	return $self;
}


sub get_string_messages($) {
	my $self=shift;
	my @a=();	
	map {
		push @a,$_->{BUFF};
		map {
			push @a,"(".$_->{TYPE}.") Line: ".$_->{LINE}." Col: ".$_->{COL}." msg: ".$_->{MSG};
		} @{$_->{MSGS}};
		push @a,"";
	} @{$self->{MSGS}};
	return \@a;
}

if (0) {
	if (__FILE__ eq $0) {
	
	
		####  test ###  
		
		package main;
	
		use strict;
		use warnings;
		use Getopt::Std;
		use Data::Dumper;
		eval("use DBU::Conversion::Conversion_Dta_Xml");
		croak $@ if $@;
		eval("SIRTI::DB");
		croak $@ if $@;
		
		my %Opt=();
		getopts ('v',\%Opt);
	
		my $db  = new SIRTI::DB() ||  SIRTI::Err::db_fatal_error();
	
		use constant {
						XSD_PATH	=> $ENV{HOME}.'/etc/DataExchange.xsd'
		};
		my $xml = new DBU::XML(XSD_PATH => XSD_PATH);
		my $cv=new DBU::Conversion::Conversion_Dta_Xml(
									DB	=> $db
									,DBUXML	=> $xml
							);
		
	
		my $ck=new SIRTI::Checks::CheckXmlData(
			 	XMLCONVERTER	=> $cv
		);
	
	
		my $sql= "
			select 
				r.*
				,to_char(r.datalavoro,'YYYY-MM-DD HH24:MI:SS') sdatalavoro
			from 
				rubrica r
			where 
		";
	
	
		$sql.=scalar(@ARGV) > 0 
				? 	(
						$ARGV[0] eq 'ALL' 
							?	" 1 = 1" 
							:   "r.id in (".join(',',@ARGV ).")"
					)
				:	
					"  r.codiceoperatoretlc='TD' and r.datacessazione is null";
	
	
	
		my $cur = $db->create_cursor($sql) || SIRTI::Err::db_fatal_error($db);
	
		print "inizio check \n" if defined($Opt{v});
	
		while(1) {
			my $row = $cur->fetchrow_hashref() || last;
			print "check id ",$row->{ID},"\n" if defined($Opt{v});
			$row->{DATALAVORO}=$row->{SDATALAVORO};
			delete $row->{SDATALAVORO};
			for my $k(keys %$row) {
				delete $row->{$k} if !defined($row->{$k}) && scalar(grep/^$k$/,@{$ck->get_mandatory_attrs()}) == 0;
			}
			my $checks=$ck->do_check($row);
			print Dumper($checks),"\n";
		}
	
		SIRTI::Err::db_fatal_error($db) if (SIRTI::DB::iserror());
	
		$cur->finish();
		$db->rollback();
	
	}
}

1;



