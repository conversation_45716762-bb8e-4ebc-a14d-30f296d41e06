package SIRTI::Checks::GenericCheck;

use strict;
use warnings;

use SIRTI::Err;

use base 'SIRTI::Base';
use Data::Dumper;

# private


sub _translate_sql($$) {
	my ($self,$s)=@_;
	return '' unless defined($s);
	$s=~s/[^\w]/ /g;
	return  uc( $self->trimmer($self->normalize($s)));
}

 
sub _check_attrs($) {
	my $self=shift;
	return 0 if $self->{ATTR_VALUE_UNDEF};
	my $nomiattr=$self->get_mandatory_attrs();
	for my $x(@$nomiattr) {
		SIRTI::Err::check(exists $self->{ROW}->{$x},$x.": chiave non esistente in hash dati per classe ".ref($self));
	}
	return 1;
}

sub _check_defined_attrs($) {
	my $self=shift;
	return [] if !defined($self->{ATTR_VALUE_DEFINED});
	my @e=();
	my $values=$self->{ROW};
	for my $a(@{$self->{ATTR_VALUE_DEFINED}}) {
#		SIRTI::Err::prec(scalar(grep /^$a$/,@{$self->get_all_attrs()}) > 0,"$a: l'attributo non e' definito per la classe ".ref($self));
		next scalar(grep /^$a$/,@{$self->get_all_attrs()}) == 0;
		push @e,$a if !defined($values->{$a});
	}
	return scalar(@e) > 0 
		? [
			 {
				MSG						=> 'attributi non valorizzati'
				,ATTR_NON_VALORIZZATI	=> join(',',@e)
			 }
		  ]
		: [];		
}


sub _check_params_length {
	my $self=shift;
	my $h = $self->get_param('PARAMS_LENGTH');
	return () unless defined $h;
	my @msgs=();
	my $values=$self->{ROW};
	return () unless defined $values;
	for my $k(keys %$values) {
		my ($v,$f) = ($values->{$k},$h->{$k});
		next unless defined $v;
		next unless defined $f;
		my $vf=defined($f->{FILTER}) ?  $f->{FILTER}->($v) : $v;
		next if length($vf) == 0;
		my ($min,$max)=(defined($f->{MIN}) ? $f->{MIN}  : 0,defined($f->{MAX}) ? $f->{MAX} : 4092);
		push @msgs, { 
			MSG						=>	'lunghezza inferiore alla lunghezza minima'
			,ATTRIBUTO				=>	$k
			,LUNGHEZZA_NORMALIZZATA =>  length($vf)
			,VALORE_NORMALIZZATO	=>  $vf
			,LUNGH_MINIMA			=>  $min
		} if length($vf) < $min;
		
		push @msgs, { 
			MSG						=>	'lunghezza superiore alla lunghezza massima'
			,ATTRIBUTO				=>	$k
			,LUNGHEZZA_NORMALIZZATA =>  length($vf)
			,VALORE_NORMALIZZATO	=>  $vf
			,LUNGH_MASSIMA			=>  $max
		} if length($vf) > $max;
	}
	return @msgs;
}


sub _filter_msg($$) {
	my ($self,$h)=@_;
	for my $k(keys %$h) {
		next if $k =~/MSG|CORRETT|TABELLA|ATTR_NON_VALORIZZATI|SUGGERIMENT/;
		delete $h->{$k};
	}
	return $h;
}

sub _do_check($) {
	SIRTI::Err::check(0,'metodo astratto');
	return [];
}

# public 


sub new($) {
	my $class=shift;
	my %params=@_;
	$params{MSGS}=[];
	my $self=\%params;
	return bless $self,$class;
}


sub get_mandatory_attrs($) { # elenco attributi obbligatori 
	SIRTI::Err::check(0,'metodo astratto');
	return [];
}

sub get_optional_attrs($) { # elenco attributi opzionali 
	SIRTI::Err::check(0,'metodo astratto');
	return []; 
} 

sub get_params { 
		return {  
			NO_INPUT_FIELDS_INTO_MSG   => 'nei messaggi di errori non emette i campi di input'
			,EMIT_MSG_ON_OK			  		=> 'aggiunge il messaggio in caso di ok'
			,ATTR_VALUE_DEFINED		  		=> "elenco attributi i cui valori devono essere definiti - Es: [ 'ATTR1','ATTR2' ]"
			,ATTR_VALUE_UNDEF			  	=> "assume undef per i valori degli attributi obbligatori non presenti (per default e' un errore)"
		};
}

sub get_all_attrs($) {
	my $self=shift;
	my @t=@{$self->get_mandatory_attrs()};
	push @t,@{$self->get_optional_attrs()};
	return \@t;
}

sub get_attr_value_defined($) {
	my $self=shift;
	return defined($self->{ATTR_VALUE_DEFINED}) 
			?  $self->{ATTR_VALUE_DEFINED}
			: [];
}

sub get_chektype() {
	SIRTI::Err::check(0,'metodo astratto');
	return '';
}



sub set_param($$$) {
	my ($self,$param,$value)=@_;
	my $old=$self->{$param};
	$self->{$param}=$value;
	return $old;
}


sub get_param($$) {
	my ($self,$param)=@_;
	return $self->{$param};
}

sub do_check($$@) { 
	my $self=shift;
	my $values=shift;
	my %params=@_;
	foreach my $key (keys %$values) {
		$values->{$key} = ($self->normalize_apici($values->{$key})) if defined $values->{$key};
	}
	$self->{ROW}=$values;
	$self->{PARAMS}=\%params;
	$self->_check_attrs();
	my $msgs=$self->_check_defined_attrs();
	push @$msgs,$self->_check_params_length();
	if (scalar(@$msgs) > 0) {
		for my $k(@$msgs) {
			$self->_filter_msg($k) if $self->{NO_INPUT_FIELDS_INTO_MSG};
			$k->{CHECKTYPE}=$self->get_chektype() if !defined($k->{CHECKTYPE});
			$k->{ID}=$values->{ID} if !defined($k->{ID}) && defined($values->{ID});
		}
		$self->{MSGS}=$msgs;
		return $msgs;
	}
	
	push @$msgs,@{$self->_do_check()};
	for my $k(@$msgs) {
		$self->_filter_msg($k) if $self->{NO_INPUT_FIELDS_INTO_MSG};
		$k->{CHECKTYPE}=$self->get_chektype() if !defined($k->{CHECKTYPE});
		$k->{ID}=$values->{ID} if !defined($k->{ID}) && defined($values->{ID});
	}
	if ($self->{EMIT_MSG_ON_OK}  && scalar(@$msgs) == 0) {
		my $k={};
		$k->{CHECKTYPE}=$self->get_chektype();
		$k->{ID}=$values->{ID} if defined($values->{ID});
		$k->{MSG}=$self->{EMIT_MSG_ON_OK};
		push @$msgs,$k;
	}
	$self->{MSGS}=$msgs;
	return $msgs;
}

sub get_messages($) { return $_[0]->{MSGS};}

sub get_string_messages($)  {
	my $self=shift;
	my @a=();
	map {
		my $c=$_;
		my $s='';
		$s.=$c->{ID}.':' if defined($c->{ID}); 
		$s.=$c->{CHECKTYPE}.':' if defined($c->{CHECKTYPE}); 
		$s.=SIRTI::Err::nvl($c->{MSG},$c->{MSG},'<NOMSG>');
		my ($fl,$s1)=(0,' (');
		for my $k(sort keys %$c) {
			next if grep /^$k$/,('CHECKTYPE','MSG','ID');
			$s1.="$k => " . sub {
				return '<UNDEF>' unless defined($c->{$k});
				return "'".$c->{$k}."'" if $k ne 'POSSIBILI_VALORI';
				return Dumper($c->{$k});
			}->() . ' ';			
			$fl=1;
		}
		$s1.=")";
		$s.=$s1 if $fl;
		push @a,$s;
	} @{$self->{MSGS}};
	return \@a;
}

sub print_messages($$$) {
	my ($self,$file,$subst)=@_;
	$file=*STDOUT if !defined($file);
	if (ref(SIRTI::Err::nvl($subst)) eq 'HASH') {
		for my $x(@{$self->get_messages()}) {
			for my $k(keys (%$x)) {
				$x->{$k} = $subst->{$k} if exists $subst->{$k};
			}
		}
	}	
	my $msgs=$self->get_string_messages();
	if (scalar(@$msgs) > 0) {
		my @m=ref($subst) eq 'CODE' ? map { $subst->($_) } @$msgs : @$msgs;
#		print $file join("\n",@$msgs),"\n";
		print $file join("\n",@m),"\n";
	}
}


sub reset_messages($) { $_[0]->{MSGS}=[];}


##### Imposta una nuova istanza di DB
sub set_db($$) {
	my ($self,$db_ref) = @_;
	if ( ref($db_ref) eq 'SIRTI::DB' ) {
		$self->{DB} = $db_ref;
	} else {
		$self->{DB}= new SIRTI::DB($db_ref) || SIRTI::Err::db_fatal_error();
	}
}

##### Rimuove l'istanza di DB
sub unset_db($) {
	my $self = shift;
	$self->{DB} = undef;
}


sub free() {}

1;


