package SIRTI::Client::IMAP::ExampleConsumer;

use strict;
use warnings;
use Carp;

use base 'SIRTI::Client::IMAP::MessageConsumer';

#
# override
#
sub consume {
	my $self = shift;
	my %params = @_;
	my $message = $params{MESSAGE};
	
	$self->logger()->info( "Elaboro un messaggio..." );

	$self->logger()->info( "Message-Id: " . $message->get_message_id() );
	$self->logger()->info( "Subject: " . $message->get_subject() );

	# croak "STOP!!!" per fermare l'elaborazione

	return "MARK_AS_SEEN"
	
}

1;
