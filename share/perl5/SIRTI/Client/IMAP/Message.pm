################################################################################
#
# SIRTI::Client::IMAP::Message
#
################################################################################
=pod

=head1 NAME

B<SIRTI::Client::IMAP::Message> - Oggetto che rappresenta un messaggio IMAP che richiede elaborazione
  
=head1 DESCRIPTION

L'oggetto SIRTI::Client::IMAP::Message viene creato dal processo B<client_imap> e inviato ad un generico consumer
SIRTI::Client::IMAP::MessageConsumer tramite il metodo B<consume( )>

=cut

package SIRTI::Client::IMAP::Message;

use strict;
use warnings;
use Carp;
use Log::Log4perl qw(get_logger :levels :nowarn);
use Encode qw{ decode encode_utf8 };
use Email::MIME::ContentType;
use Date::Language;
use DateTime;

use base 'SIRTI::Base';

sub new {

	my $this = shift;
	my $class = ref($this) || $this;
	my %params = @_;
	my $self = bless( {}, $class );
	my $errmsg = '';

	$self->clear_error();

	croak $errmsg unless $self->check_named_params(
		 ERRMSG	 => \$errmsg
		,PARAMS	 => \%params
		,MANDATORY  => {
			 IMAP					 => { isa => 'Mail::IMAPClient' }
			,MESSAGE_UID			  => { isa => 'SCALAR' }
		}
		,OPTIONAL   => {
			 TIME_ZONE				=> { isa => 'SCALAR' }
			,LOCALE				   => { isa => 'SCALAR' }
		}
	);

	$self->{__LOGGER__} = Log::Log4perl->get_logger( 'COMMON::LIB::' . __PACKAGE__ );
	$self->{__LOGGER__}->trace( __PACKAGE__ . '->new' );

	$self->{_IMAP} = $params{IMAP};
	$self->{_MESSAGE_UID} = $params{MESSAGE_UID};
	
	$self->{_TIME_ZONE} = exists $params{TIME_ZONE} ? $params{TIME_ZONE} : 'Europe/Rome';
	$self->{_LOCALE} = exists $params{LOCALE} ? $params{LOCALE} : 'it_IT';
	
	$self->{_DATE_LANGUAGE} = Date::Language->new('English');

	return $self;
}

=head1 METHODS

=head2 get_message_uid( )

Restituisce l'identificativo univoco del messaggio.

=cut

sub get_message_uid { shift->{_MESSAGE_UID}; }

=head2 get_subject( )

Restituisce l'oggetto del messaggio.

=cut

sub get_subject { shift->_get_header("Subject") }

=pod

=head2 get_message_id( )

Restituisce l'header Message-Id del messaggio.

=cut

sub get_message_id { shift->_get_header("Message-Id") }

=pod

=head2 get_date( )

Restituisce un oggetto di tipo C<DateTime> che descrive la data del messaggio.

=cut

sub get_date {
	my $self = shift;
	my $seconds = $self->_date_language()->str2time($self->get_header_date());
	return DateTime->from_epoch(epoch => $seconds, time_zone => $self->_time_zone(), locale => $self->_locale());
}

=pod

=head2 get_header_content_type( )

Restituisce l'header C<Content-Type> del messaggio.

=cut

sub get_header_content_type { shift->_get_header("Content-Type") }

=pod

=head2 get_header_date( )

Restituisce l'header C<Date> del messaggio.

=cut

sub get_header_date { shift->_get_header("Date") }

=pod

=head2 get_header_from( )

Restituisce l'header C<From> del messaggio.

=cut

sub get_header_from { shift->_get_header("From") }

=pod

=head2 get_header_to( )

Restituisce l'header C<To> del messaggio.

=cut

sub get_header_to { shift->_get_header("To") }

=pod

=head2 body_string( )

Restituisce una stringa con il contenuto del corpo del messaggio.

=cut

sub body_string { my $self = shift; $self->_imap()->body_string($self->get_message_uid() ) }

=pod

=head2 get_bodystructure( )

Restituisce un oggetto di tipo C<Mail::IMAPClient::BodyStructure> che descrive il messaggio.

=cut

sub get_bodystructure { my $self = shift; $self->_imap()->get_bodystructure($self->get_message_uid()) }

=pod

=head2 get_envelope( )

Restituisce un oggetto di tipo C<Mail::IMAPClient::BodyStructure::Envelope> che descrive il messaggio.

=cut

sub get_envelope { my $self = shift; $self->_imap()->get_envelope($self->get_message_uid() ) }

=pod

=head2 get_content_type( )

Restituisce un hashref con le chiavi C<type>, C<subtype>, e un hashref C<attributes> (RFC 2045 e RFC 2231).
Se l'header C<Content-Type> non è presente viene restituito il default (text/plain, encoding us-ascii).

=cut

sub get_content_type {
	my $self = shift;
	my $ret = parse_content_type($self->get_header_content_type);
	delete $ret->{discrete};
	delete $ret->{composite};
	return $ret;
}

=pod

=head2 get_message_string( )

Restituisce una stringa con il contenuto completo della mail.

=cut

sub get_message_string {
	my $self = shift;
	return $self->_imap()->message_string($self->get_message_uid());
}

=pod

=head2 message_to_file($file)

Accetta in input un filename o file handle come argomenti e mette il messaggio (inclusi gli header RFC822) nel file passato (o scrive nel file handle, se è passato un file handle).
Ritorna true in caso di successo o undef in caso di errore impostando last_error della classe.

=cut

sub message_to_file {
	my $self = shift;
	my $file = shift;
	$self->last_error("Impossibile salvare il messagio sul file: ".$self->_imap_last_error())
		&& return undef
			unless $self->_imap()->message_to_file($file, $self->get_message_uid());
	return 1;
}

=pod

=head2 logger( )

Questo metodo restituisce un oggetto logger che può essere utilizzato per effettuare il logging del consumer
con i metodi C<trace()>, C<debug()>, C<info()>, C<warn()>, C<error()> e C<fatal()>.
Nota: il logger restituito è di tipo Log::Log4perl::Logger. Se nome del package è ad esempio PIPPO::Pluto::Paperino
la categoria del logger instanziato sarà PIPPO::LIB::PIPPO::Pluto::Paperino (vedi http://search.cpan.org/~mschilli/Log-Log4perl/)

=cut
sub logger { shift->{__LOGGER__}; }

### Metodi privati

sub _imap { shift->{_IMAP}; }

sub _imap_last_error { shift->_imap()->LastError }

sub _get_header {
	my $self = shift;
	my $header = lc(shift);
	
	return $self->{"_HEADER_".$header} if exists $self->{"_HEADER_".$header};
	
	$self->{"_HEADER_".$header} = $self->_imap()->get_header( $self->get_message_uid(), $header );
	croak "Impossibile recuperare l'header $header del messaggio: " . $self->_imap_last_error()
		unless defined $self->{"_HEADER_".$header};
	
	return $self->{"_HEADER_".$header} = encode_utf8(decode('MIME-Header', $self->{"_HEADER_".$header}));
}

sub _locale { shift->{_LOCALE}; }

sub _time_zone { shift->{_TIME_ZONE}; }

sub _date_language { shift->{_DATE_LANGUAGE}; }

=pod

=head1 SEE ALSO

Script perl B<client_imap>, package B<SIRTI::Client::IMAP::MessageConsumer>


=head1 AUTHOR

SHTeam :)

=cut

############################################################################
# UNIT TEST
############################################################################

if ( __FILE__ eq $0 ) {
}

1;
