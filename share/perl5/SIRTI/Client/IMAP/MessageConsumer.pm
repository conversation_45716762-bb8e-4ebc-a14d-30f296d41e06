################################################################################
#
# SIRTI::Client::IMAP::MessageConsumer
#
################################################################################
=pod

=head1 NAME

B<SIRTI::Client::IMAP::MessageConsumer> - Interfaccia da implementare per la gestione di messaggi con C<client_imap>

=head1 SYNOPSIS

  package MyConsumer;
  
  use strict;
  use warnings;
  use Carp;
  
  use base 'SIRTI::Client::IMAP::MessageConsumer';
  
  #
  # override
  #
  sub consume {
    my $self = shift;
    my %params = @_;
    my $message = $params{MESSAGE};
    
    $self->logger()->info( "Elaboro un messaggio..." );
  
    $self->logger()->info( "Message-Id: " . $message->get_message_id() );
    $self->logger()->info( "Subject: " . $message->get_subject() );
  
    # croak "STOP!!!" per fermare l'elaborazione
  
    return "MARK_AS_SEEN"
    
  }
  
  1;

=head1 DESCRIPTION

La classe B<SIRTI::Client::IMAP::MessageConsumer> descrive l'interfaccia che un consumer di messaggi deve implementare per
poter essere utlizzato con C<client_imap>.

Il nome della classe che implementa il consumer deve essere indicato, insieme ad altri parametri, in un file di
configurazione da passare come argomento a C<client_imap> (vedi documentazione di C<client_imap>). 

Segue lo schema di funzionamento di C<client_imap> (tra parentesi il metodo del consumer che viene invocato nei vari step):

=over 12

=item 1. lettura e validazione del file di configurazione

=item 2. istanziazione dell'oggetto consumer di classe B<C<SIRTI::Client::IMAP::MessageConsumer>>

=item 3. inizializzazione (B<C<$consumer-E<gt>init()>>)

=item 4. recupero dei messaggi (B<C<$consumer-E<gt>before_consuming_messages()>>)

=item 5. invocazione della procedura di elaborazione dei messaggi (B<C<$consumer-E<gt>consume()>>)

=item 6. esecuzione dei comandi IMAP sul messaggio 

=item 7. invocazione dei metodi B<C<$consumer-E<gt>done()>> o
B<C<$consumer-E<gt>not_done()>> in caso di esecuzione andata a buon fine
o meno dei comandi imap

=item 8. ritorno al punto 5 finchè non sono stati lavorati tutti i messaggi recuperati

=item 9. uscita dal ciclo di elaborazione dei messaggi (B<C<$consumer-E<gt>after_consuming_messages()>>)

=item 10. ritorno al punto 4 se lanciato come demone

=item 13. esecuzione delle procedure di conclusione del processo di elaborazione dei messaggi (B<C<$consumer-E<gt>finish()>>)

=back
 
=cut

package SIRTI::Client::IMAP::MessageConsumer;

use strict;
use warnings;
use Carp;
use Scalar::Util qw(blessed);
use Log::Log4perl qw(get_logger :levels :nowarn);

use API::ART;

use base 'SIRTI::Base';

our $VERSION = '0.1';

=pod

=head1 METHODS

=cut

#
# should not be overriden
#
# new( DRY_RUN => <BOOLEAN>, WORK_CONTEXT => <HASHREF> )
#
# params:
# * DRY_RUN : indica se verranno o meno effettuate le azioni imap indicate nella risposta del consumer
# * WORK_CONTEXT : hashref di variabili in formato chiave valore che sono presenti nel file di
#     configurazione di client_imap tramite la chiave WORK_CONTEXT
#

sub new {
    my $this = shift;
    my $class = ref($this) || $this;
    my %params = @_;
    my $self = bless( {}, $class );
    my $errmsg = '';

	$self->clear_error();

	croak $errmsg unless $self->check_named_params(
         ERRMSG              => \$errmsg
		,IGNORE_EXTRA_PARAMS => 1
        ,PARAMS     => \%params
        ,MANDATORY  => {
             DRY_RUN                => { isa => 'SCALAR', list => [ 0, 1 ] }
            ,WORK_CONTEXT			=> { isa => 'HASH' }
        }
		,OPTIONAL => {
		}
    );

	my @parts = split( '::', $class );
	my $logger_name = $parts[0] . '::LIB::' . join( '::', @parts );

	$self->{__LOGGER__} = Log::Log4perl->get_logger( $logger_name );
	$self->{__LOGGER__}->trace( __PACKAGE__ . '->new' );

	$self->{__DRY_RUN} = $params{DRY_RUN};
	$self->{__WORK_CONTEXT} = $params{WORK_CONTEXT};

	return $self;
}

=pod

=head2 init( )

Questo metodo viene invocato da C<client_imap> subito dopo aver istanziato l'oggetto consumer. B<Può> essere sovrascritto
per effettuare delle routine di inizializzazione del consumer.

Da notare che questo è il punto giusto dove creare dei singleton che potranno essere mantenuti nella sessione tramite
la definizione di una chiave dell'oggetto. Attenzione: al fine di evitare la sovrascrittura accidentale delle chiavi
utilizzate dalla classe base C<SIRTI::Client::IMAP::MessageConsumer>, non utilizzare chiavi che iniziano per doppio underscore
(C<__>).

=cut

#
# optionally override
#
sub init {
	return;
}

=pod

=head2 before_consuming_messages( NUM_MESSAGES => C<SCALAR> )

=over

=item * B<NUM_MESSAGES> : numero di messaggi trovati che verranno inviati uno per volta alla funzione
C<consume()>

=back


Questo metodo viene invocato dopo aver recuperato i messaggi e prima di iniziare a inviarli uno alla volta al
consumer per implementare delle logiche di apertura del blocco delle lavorazioni.

Questo metodo B<può> essere sovrascritto.

=cut

#
# optionally override
#
sub before_consuming_messages {
	return;
}

=pod

=head2 consume( MESSAGE => C<SIRTI::Client::IMAP::Message> )

=over

=item * B<MESSAGE> : oggetto di tipo C<SIRTI::Client::IMAP::Message>

=back


Questo metodo, che B<DEVE> essere sovrascritto, viene invocato una volta per ciascun messaggio che viene recuperato.

Deve ritornare una delle azioni configurate che indicano a C<client_imap> quali operazione eseguire sulla mailbox.
Può inoltre lanciare una eccezione in caso di problemi fatali per
comunicare a C<client_imap> di interrompere le elaborazioni.

=cut

#
# override
#
sub consume { croak "This method must be overridden!\n"; }

=pod

=head2 done( )

Questo metodo viene invocato una volta per ciascun messaggio che viene recuperato
dalla coda dopo l'elaborazione del metodo C<consume> nel caso in cui l'esecuzione
delle azioni richieste sia andata a buon fine

Può lanciare una eccezione in caso di problemi fatali per
comunicare al C<client_imap> di interrompere le elaborazioni.

=cut

#
# optionally override
#
sub done { return; }

=pod

=head2 not_done( )

Questo metodo viene invocato una volta per ciascun messaggio che viene recuperato
dalla coda dopo l'elaborazione del metodo C<consume> nel caso in cui l'esecuzione
delle azioni richieste non sia andata a buon fine

Può lanciare una eccezione in caso di problemi fatali per
comunicare al C<client_imap> di interrompere le elaborazioni.

=cut

#
# optionally override
#
sub not_done { return; }

=pod

=head2 after_consuming_messages( )

Questo metodo viene invocato dopo aver elaborato tutti i messaggi recuperati e prima di effettuare una nuova ricerca.

Questo metodo B<può> essere sovrascritto per implementare delle logiche di chiusura su un blocco di messaggi elaborati.

Può lanciare una eccezione in caso di problemi fatali per comunicare a C<client_imap> di interrompere le elaborazioni.

=cut

#
# optionally override
#
sub after_consuming_messages {
	return;
}

=pod

=head2 finish( )

Questo metodo viene invocato da C<client_imap> prima di terminare. B<Può> essere sovrascritto per
effettuare delle routine di conclusione del processo di elaborazione dei messaggi.

Può lanciare una eccezione in caso di problemi fatali per comunicare C<client_imap> di interrompere le elaborazioni.

=cut

#
# optionally override
#
sub finish {
    return;
}

=pod

=head1 UTILITY METHODS

In questa sezione vengono elencate dei metodi di utilità che possono essere utlizzati dal consumer.

=head2 is_dry_run( )

Questo metodo indica se l'elaborazione è di tipo dry-run.

=cut

sub is_dry_run { shift->{__DRY_RUN} }


=pod

=head2 logger( )

Questo metodo restituisce un oggetto logger che può essere utilizzato per effettuare il logging del consumer
con i metodi C<trace()>, C<debug()>, C<info()>, C<warn()>, C<error()> e C<fatal()>.
Nota: il logger restituito è di tipo Log::Log4perl::Logger. Se nome del package è ad esempio PIPPO::Pluto::Paperino
la categoria del logger instanziato sarà PIPPO::LIB::PIPPO::Pluto::Paperino (vedi http://search.cpan.org/~mschilli/Log-Log4perl/)

=cut

sub logger { shift->{__LOGGER__} }

=pod

=head2 work_context( $name )

=over

=item * B<$name> : C<SCALAR>, nome della properties che deve essere restituita, opzionale

=back


Questo metodo permette di recuperare le properties che sono state caricate dal file di configurazione di C<client_imap>
tramite la chiave C<WORK_CONTEXT>. Se viene invocata senza parametro viene restuito un hashref con tutte le properties
definite.

=cut

sub work_context {
	my $self = shift;
	my $name = shift;
	if ( defined $name ) {
	    return 
		   ( not exists $self->{__WORK_CONTEXT}->{$name} ) ? undef :
		       $self->{__WORK_CONTEXT}->{$name};
	}		    
	
	return $self->{__WORK_CONTEXT};
}

=pod

=head2 last_error( $msg )

=over

=item * B<$msg> : C<SCALAR>, messaggio di errore, opzionale

=back


Se viene invocata con il parametro $msg viene settato il messaggio di errore.
Ritorna sempre l'ultimo messaggio di errore settato. 

=cut
 
=pod

=head1 SEE ALSO

Script perl B<client_imap>, package B<SIRTI::Client::IMAP::Message>


=head1 AUTHOR

SHTeam :)

=cut

1;
