
package SIRTI::DB::Cursor;

use strict;
use warnings;
use Carp;

sub _new($$$$@)  {
	my ($classname,$dbh,$query,$parent,$params) = @_;
	my $sth=$dbh->prepare($query,$params) or  return undef;
    $sth->{HandleError} = sub{ carp(shift) };
	$sth->execute or return undef;
	my $self = {
		QUERY		=> $query
		,STH		=> $sth
		,DBH		=> $dbh
		,PARENT		=> $parent
	};
	return bless $self,$classname;
}

sub _new1($$@)  {
	my $classname=shift;
	my $parent=shift;
	$parent->{STH}->execute(@_) or return undef;
	my $self = {
		QUERY		=> $parent->{QUERY}
		,STH		=> $parent->{STH}
		,DBH		=> $parent->{DBH}
		,PARENT		=> $parent
	};
	return bless $self,$classname;
}

sub fetchrow_arrayref($) {
	my $self=shift;
	return $self->{STH}->fetchrow_arrayref();
}


sub fetchrow_hashref($) {
	my $self=shift;
	return $self->{STH}->fetchrow_hashref();
}

sub finish($) {
	my $self=shift;
	print STDERR "-- SIRTI::DB::Cursor::finish\n" if $self->{PARENT}->{PARAM}->{DEBUG};
	$self->{STH}->finish() if $self->{STH};
	for my $k(keys %$self) { $self->{$k}=undef;}
	return 1;
}

sub get_query($) { return $_[0]->{QUERY};}

sub get_dbh($) {return $_[0]->{DBH};}

sub get_sth($) { return $_[0]->{STH};}

sub DESTROY {
	my $self = shift;
	print STDERR "-- SIRTI::DB::Cursor::DESTROY\n" if $self->{PARENT}->{PARAM}->{DEBUG};
	local $@;
	eval {$self->finish(); };
	return undef;
}


package SIRTI::DB::Prepare;
use strict;
use warnings;
use Carp;

sub _new(@)  {
	my ($classname,$dbh,$query,$parent,$params) = @_;
	my $sth=$dbh->prepare($query,$params) or  return undef;
    $sth->{HandleError} = sub{ carp(shift) };
	my $self = {
		QUERY		=> $query
		,STH		=> $sth
		,DBH		=> $dbh
		,PARENT		=> $parent
	};
	return bless $self,$classname;
}

sub _new_cached(@)  {
	my ($classname,$dbh,$query,$parent,$params) = @_;
	my $sth=$dbh->prepare_cached($query,$params) or return undef;
    $sth->{HandleError} = sub{ carp(shift) };
	my $self = {
		QUERY		=> $query
		,STH		=> $sth
		,DBH		=> $dbh
		,PARENT		=> $parent
	};
	return bless $self,$classname;
}

sub bind_param  {
	my $self=shift;
	my $name=shift;
	my $value=shift;
	print STDERR "bind_param: ",$name," => ",defined $value  ? "'".$value."'" : 'null',"\n" if $self->{PARENT}->{PARAM}->{DEBUG};
	my $sth=$self->{STH};
	return $sth->bind_param($name,$value,@_);

}

sub bind_param_inout {
	my $self=shift;
	my $name=shift;
	my $pvalue=shift;
	my $size=shift;
	print STDERR "bind_param_inout:  $name: size => $size\n" if $self->{PARENT}->{PARAM}->{DEBUG};
	my $sth=$self->{STH};
	return $sth->bind_param_inout($name,$pvalue,$size,@_);
}


sub execute {
	my $self=shift;
	print STDERR "execute " if $self->{PARENT}->{PARAM}->{DEBUG};
	return $self->{STH}->execute(@_);
}

sub fetchall($@) {			# return reference to array of array of scalars
	my $self=shift;
	print STDERR "--  SIRTI::DB::Prepare::fetchall() --->\n" .$self->get_query_with_values(@_). ";\n" if $self->{PARENT}->{PARAM}->{DEBUG};

	my $sth=$self->{STH};
	$sth->execute(@_) or return undef;
	return $sth->fetchall_arrayref();
}

sub fetchall_hashref($@) { # return reference to array of hash
	my $self=shift;
	print STDERR "-- SIRTI::DB::Prepare::fetchall_hashref() --->\n" .$self->get_query_with_values(@_). ";\n" if $self->{PARENT}->{PARAM}->{DEBUG};

	my $cur = $self->create_cursor(@_) or return undef;
	my @r=();
	while(my $row = $cur->fetchrow_hashref()) {
		push @r,$row;
	}
	return undef if SIRTI::DB::iserror();
	$cur->finish() || return undef;
	return  \@r;
}

sub fetchall_arrayref($@) { # return reference to array of array of scalars
	my $self=shift;
	return $self->fetchall(@_);
}

sub fetch_minimalized($@) {
	my $self=shift;
	my $rows=$self->fetchall(@_) || return undef;
	return undef if scalar(@$rows) == 0;
	if (scalar(@{$rows->[0]}) == 1) { # se c'e' una sola colonna
		my @a=();
		for my $v(@$rows) {
			push @a,$v->[0];
		}
		return scalar(@a) == 1 ? $a[0] : @a;
	}
	return @$rows if scalar(@$rows) > 1;
	my $r=$rows->[0];
	return @$r if scalar(@$r) > 1;
	return $r->[0];
}

sub create_cursor($@) {
	my $self=shift;
	if ($self->{PARENT}->{PARAM}->{DEBUG}) {
		print STDERR "-- SIRTI::DB::Prepare::create_cursor(",join(',',map { $self->{DBH}->quote($_) } @_).")\n" ;
		print STDERR $self->get_query_with_values(@_),"\n";
	}
	return  SIRTI::DB::Cursor->_new1($self,@_);
}

sub fetchscalar($@) {
	my $self=shift;
	my $r=$self->fetchall_arrayref(@_) || return undef;
	return $r->[0]->[0];
}

sub do($@) {
	my $self=shift;
	print STDERR "-- SIRTI::DB::Prepare::do() --->\n" . $self->get_query_with_values(@_). ";\n" if $self->{PARENT}->{PARAM}->{DEBUG};
	my $sth=$self->{STH};
    $sth->execute(@_) || return undef;
    return $sth->rows == 0 ? '0E0' : $sth->rows;
}

sub finish($) {
	my $self=shift;
	print STDERR "SIRTI::DB::Prepare::finish()\n" if $self->{PARENT}->{PARAM}->{DEBUG};
	if (defined($self->{STH})){
		$self->{STH}->finish() || return undef;
	}
	for my $k(keys %$self) { $self->{$k}=undef;}
	return 1;
}


sub get_query($) { return $_[0]->{QUERY};}

sub get_dbh($) {return $_[0]->{DBH};}

sub get_sth($) { return $_[0]->{STH};}

sub get_query_with_values($@) {
	my $self=shift;
	my $q=$self->{QUERY};
	for my $v(@_) {
		if  (!defined($v)) {
			$q=~s/\?/NULL/;
		}
		elsif ($v=~/^\d+$/) {
			$q=~s/\?/$v/;
		}
		else {
			my $x=$self->{PARENT}->quote($v);
			$q=~s/\?/$x/;
		}
	}
	return $q;
}

sub DESTROY {
	my $self = shift;
	print STDERR "-- SIRTI::DB::Prepare::DESTROY\n" if $self->{PARENT}->{PARAM}->{DEBUG};
	local $@;
	eval { 	$self->finish(); };
	return undef;
}

package SIRTI::DB::_Params;  #lega l' hash dei parametri a questa classe
	use SIRTI::Tie;
	use base "SIRTI::Tie::Hash";
	
	sub STORE($$$) {
		my ($self,$key,$value)=@_;
		$self->{BIND}->{$key}=$value;
		if (ref($self->{PARAMS}->[0]) eq 'DBI::db') {
			if ($key eq 'LOB_READMAXLEN') {
				$self->{PARAMS}->[0]->{LongReadLen}=$value ;
			}
			elsif ($key eq 'LOB_READTRUNK') {
				$self->{PARAMS}->[0]->{LongTruncOk}=$value;
			}
			else {
				#vuoto
			}
		}
	}

	
package SIRTI::DB;

use strict;
use warnings;
use DBI qw(:sql_types);
use Carp;
use SIRTI::Err;
use SIRTI::DB::ConnStr;

my %_PAR_NAMES = (	# nome parametro => nome variabile d' ambiente
	RAISEERROR			=> 'ART_DB_RAISEERROR'
	,PRINTERROR			=> 'ART_DB_PRINTERROR'
	,AUTOCOMMIT			=> 'ART_DB_AUTOCOMMIT'
	,NLS_LANG			=> 'NLS_LANG'
	,ORACLE_HOME		=> 'ORACLE_HOME'
	,DEBUG				=> 'ART_DB_DEBUG'
	,LOB_READMAXLEN		=> 'ART_LOB_READMAXLEN'
	,LOB_READTRUNK  	=> 'ART_LOB_READTRUNK'
	,DBI_DRIVER			=> 'ART_DBI_DRIVER'
	,NO_DB_PWD			=> 'ART_NO_DB_PWD'
	,SUIDEXEC			=> 'ART_DB_PWD_SUIDEXEC'
	,APPLICATION_NAME 	=> 'ART_DB_PWD_APPLICATION_NAME'
	,IPC_SIZE			=> 'ART_DB_PWD_IPC_SIZE'
	,IPC_PERM			=> 'ART_DB_PWD_IPC_PERM'
	,SIGNAL_TRAP		=> 'ART_DB_PWD_SIGNAL_TRAP'
	,CLIENT_TAG			=> 'ART_DB_CLIENT_TAG'
);


my %_PAR_DEFVALUES = (	# nome parametro => valore di default
	RAISEERROR		=> 1
	,PRINTERROR		=> 0
	,AUTOCOMMIT		=> 0
	,NLS_LANG		=> undef
	,ORACLE_HOME	=> undef
	,DEBUG			=> 0
	,LOB_READMAXLEN	=> 0   # lunghezza massima in bytes di un lob in lettura
	,LOB_READTRUNK	=> 0   # se vero la lettura di un lob viene troncata a LOB_READMAXLEN altrimenti e' un errore
	,DBI_DRIVER		=> 'dbi:Oracle:'
	,NO_DB_PWD		=> 0
	,SIGNAL_TRAP	=> 'TERM HUP'
	,CLIENT_TAG		=> undef
);

sub _connect($$) {   # static method
	my ($cs,$param)=@_;
	my $orig_cs=$cs;
	unless ($param->{NO_DB_PWD}) {
		my $connstr=SIRTI::DB::ConnStr::get_connect_string($cs,%$param);
		if (defined $connstr && length($connstr) > 0) {
			$cs=$connstr;
		}
		else {
			$param->{NO_DB_PWD}=1;
		}
	}

	my $conn = eval {
		DBI->connect(
					$param->{DBI_DRIVER}
					,$cs
					,''
					,{
						RaiseError	=> $param->{RAISEERROR}
						,PrintError => $param->{PRINTERROR}
						,AutoCommit => $param->{AUTOCOMMIT}
					}
		);
	};
	if ($@) {
		if ($param->{NO_DB_PWD}) {
			croak $@;
		}
		else {
			 die "'$orig_cs': errore connessione - la stringa di connessione reperita non e' corretta o il database non e' disponibile\n";
		}
	}
	return $conn;

}

sub new  {
	my $classname = shift;
	my $typ=ref($_[0]);
	my $connectstring	=
		scalar(@_) > 0 && ( length($typ) == 0  ||  grep(/^$typ/,qw(DBI::db SIRTI::DB))  ||  $typ =~ m/^ART::C_db/ )
			? shift
			: defined($ENV{ART_DB_CONNECT_STRING})
				? $ENV{ART_DB_CONNECT_STRING}
				: defined($ENV{SQLID})
					? $ENV{SQLID}
					: SIRTI::Err::check(0,'nessuna stringa di connessione');
	$connectstring='' if (!defined($connectstring));
		
	my $param = scalar(@_) == 0
		? {}
		: ref($_[0]) eq 'HASH'
			? shift
			: {};

	my %t=@_;
	for my $k(keys(%t)) {
		$param->{$k}=$t{$k};
	}
	
	for my $k(keys %_PAR_NAMES) {
		if (!defined($param->{$k})) {
			$param->{$k}=defined($ENV{$_PAR_NAMES{$k}})
				? $ENV{$_PAR_NAMES{$k}}
				: $_PAR_DEFVALUES{$k};
		}
	}


	for my $k ( qw(NLS_LANG ORACLE_HOME) ) {
		my $envname=$_PAR_NAMES{$k};
		if (defined($param->{$k})) {
			$ENV{$envname}=$param->{$k}
		}
		else {
			print STDERR "(W) variabile d' ambiente $envname non definita\n" if $k eq 'NLS_LANG' && !$param->{ART_AUTONLS_LANG} || $k ne  'NLS_LANG';
		}
	}
		
	my $self = {
		 CONNECTSTRING	=> $connectstring
		,PARAM			=> undef
	};
	
	my $ref=ref($connectstring);
	
	if (length($ref) == 0) {
		$self->{DBH} = _connect($connectstring,$param) || return undef;
	}
	elsif ($ref eq 'DBI::db') {
		$self->{DBH}=$connectstring;
		$self->{NOT_DESTROY}=1;
	}
	elsif ($ref =~ m/^ART::C_db/) {
		$self->{DBH}=$connectstring->{dbh};
		$self->{PARAM}->{RAISEERROR}=$connectstring->{raiseerror};
		$self->{NOT_DESTROY}=1;
	}
	else {
		SIRTI::Err::internal_error("$ref: oggetto non conosciuto");
	}
	
	for my $k(keys %$self) {
		$self->{lc($k)}=$self->{$k};
	}

	my %tp=();
	tie %tp,'SIRTI::DB::_Params',$self->{DBH};
	%tp=%$param;
	$self->{PARAM}=\%tp;
	return bless $self,$classname;
}


sub get_dbh($) { return $_[0]->{DBH};}

sub iserror  { # static method
	return defined($DBI::err);
}

sub get_errorcode  {  # static method
	return $DBI::err
}


sub get_errormessage { # static method
	return $DBI::errstr;
}

sub do($$) {
	my ($self,$statement) = @_;
	croak "statement non  specificato" unless defined $statement;
	print STDERR "-- SIRTI::DB::do() --->\n" . $statement . ";\n" if $self->{PARAM}->{DEBUG};
    return $self->get_dbh()->do($statement);
}


sub fetchall($$) {			# return reference to array of array of scalars
	my ($self,$query,%params)=@_;
	croak "query non  specificata" unless defined $query;
	print STDERR "-- SIRTI::DB::fetchall() --->\n" . $query . ";\n" if $self->{PARAM}->{DEBUG};
	my $dbh=$self->get_dbh();
	my $sth=$dbh->prepare($query) or return undef;
	$sth->execute or return undef;
	return $sth->fetchall_arrayref();
}

sub fetchall_hashref($$) { # return reference to array of hash
	my ($self,$query)=@_;
	#print STDERR "-- fetchall_hashref() --->\n" . $query . ";\n" if $self->{PARAM}->{DEBUG};
	my $cur = $self->create_cursor($query) or return undef;
	my @r=();
	while(my $row = $cur->fetchrow_hashref()) {
		push @r,$row;
	}
	return undef if SIRTI::DB::iserror();
	$cur->finish() || return undef;
	$cur=undef;
	return  \@r;
}

sub fetchall_arrayref($$) { # return reference to array of array of scalars
	return fetchall($_[0],$_[1]);
}

sub fetchscalar($$) {
	my ($self,$query)=@_;
	my $r=$self->fetchall_arrayref($query) || return undef;
	return $r->[0]->[0];
}

sub query($$) {				# only for old compatibility - use fetchall
	my ($self,$query) = @_;
	my $r = $self->fetchall($query);
	return @$r;
}


sub fetch_minimalized($$) {
	my ($self,$query)=@_;
	my $rows=$self->fetchall($query) || return undef;
	return undef if scalar(@$rows) == 0;
	if (scalar(@{$rows->[0]}) == 1) { # se c'e' una sola colonna
		my @a=();
		for my $v(@$rows) {
			push @a,$v->[0];
		}
		return scalar(@a) == 1 ? $a[0] : @a;
	}
	return @$rows if scalar(@$rows) > 1;
	my $r=$rows->[0];
	return @$r if scalar(@$r) > 1;
	return $r->[0];
}

#####################################################################
# RICHIAMA STORED PROCEDURE
#####################################################################
#
# proc : nome della procedura
# param: nel formato
#   [
#       {param=>'valore1',in_value=>10},
#       {param=>'valore2',in_value=>1,out_value=>\$var},
#       {param=>'valore2',sql_type=>$SQL_TYPE,out_value=>\$var},
#       { },
#       {param=>'data', cmd=>'to_char(:data,\'dd-mm-yyyy\')'}
#   ]
#
# dove:
#
# param     -> nome parametro (se non esiste il campo e' un null)
# in_value  -> valore in ingresso
# out_value -> valore in uscita (riferimento ad una variabile)
# cmd       -> funzione su parametro
# sql_type  -> tipo di dato SQL. Deve essere gestito dal driver.
#             es: 'LONG RAW','RAW','LONG','CHAR','DECIMAL',
#             'DOUBLE PRECISION','DATE','VARCHAR2','BLOB','BFILE','CLOB'
#            La lista completa ed aggiornata e' ottenibile richiamando
#            il metodo $self->sql_type_list()
#####################################################################
sub call_stored_procedure($$$){

    my ($self, $proc, $param) = @_;

    my $dbh = $self -> {DBH};

    my @param_list = map( defined $_->{param} ? ( defined $_->{cmd} ? $_->{cmd} : ':' . $_->{param} ) : 'null' , @$param );

    my $cmd = "BEGIN $proc(".join(',',@param_list)."); END;";

    my $func = $dbh->prepare($cmd);

    for (@$param){

        SP_BINDING_SWITCH:{

            last SP_BINDING_SWITCH unless defined $_->{param};

            if (defined $_->{in_value}) {
                $func->bind_param( ":$_->{param}", $_->{in_value} );
                last SP_BINDING_SWITCH;
            }

            if (defined $_->{out_value}) {
				$func->bind_param_inout( ":$_->{param}", $_->{out_value} , $_->{in_value} ? $_->{in_value} :
																$_->{sql_type} ? { TYPE => $_->{sql_type} } : ${$_->{out_value}} );
                last SP_BINDING_SWITCH;
            }

        } # end SP_BINDING_SWITCH

    } # end binding

    return $func->execute();
}

sub create_cursor($$;$) {
	my ($self,$query,$params)=@_;
	print STDERR "-- SIRTI::DB::create_cursor() --->\n" . $query . ";\n" if $self->{PARAM}->{DEBUG};
	return _new SIRTI::DB::Cursor($self->get_dbh(),$query,$self,$params);
}

sub create_prepare($$;$) {
	my ($self,$query,$params)=@_;
	print STDERR "-- SIRTI::DB::create_prepare() --->\n" . $query . ";\n" if $self->{PARAM}->{DEBUG};
	return _new SIRTI::DB::Prepare($self->get_dbh(),$query,$self,$params);
}

sub create_prepare_cached($$;$) {
	my ($self,$query,$params)=@_;
	return _new_cached SIRTI::DB::Prepare($self->get_dbh(),$query,$self,$params);
}

sub rollback($) {
	my $self=shift;
	print STDERR "-- SIRTI::DB::rollback() --->\n" if $self->{PARAM}->{DEBUG};
	return $self->{DBH}->{AutoCommit} ? 1: $self->{DBH}->rollback();
}

sub commit($) {
	my $self=shift;
	print STDERR "-- SIRTI::DB::commit() --->\n" if $self->{PARAM}->{DEBUG};
	return $self->{DBH}->commit();
}


sub disconnect($) {
	my $self=shift;
	print STDERR "-- SIRTI::DB::disconnect() --->\n" if $self->{PARAM}->{DEBUG};
	$self->rollback();
	if (ref($self->{PARAM}->{BEFORE_DISCONNECT}) eq 'CODE') {
		my $f=$self->{PARAM}->{BEFORE_DISCONNECT};
		$self->{PARAM}->{BEFORE_DISCONNECT}=undef;
		local $@;
		eval { $f->($self);  };
	}
	my $r=$self->{DBH}->disconnect();
	$self->{DBH}=undef;
	delete $self->{DATABASE_PARAMETERS};
	untie %{$self->{PARAM}};
	return $r;
}


sub quote($$) {
	my ($self,$string)=@_;
	return $self->{DBH}->quote($string);
}

sub quote_identifier($$) {
	my ($self,$string)=@_;
	return $self->{DBH}->quote_identifier($string);
}


sub unquote($$) {
	my ($self,$string)=@_;
	$string=~s/(^'|'$)//g; #
	$string=~s/''/'/g; #'
	return $string;
}

sub unquote_identifier($$) {
	my ($self,$string)=@_;
	$string=~s/(^"|"$)//g; #
	$string=~s/""/"/g; #"
	return $string;
}

sub set_printerror($$) {
	my ($self,$flag)=@_;
	my $old=$self->{DBH}->{PrintError};
	$self->{DBH}->{PrintError} = $flag ? 1 : 0;
	return $old;
}

sub get_printerror($) { return $_[0]->{DBH}->{PrintError};}

sub set_raiseerror($$) {
	my ($self,$flag)=@_;
	my $old=$self->{DBH}->{RaiseError};
	$self->{DBH}->{RaiseError} = $flag ? 1 : 0;
	return $old;
}

sub get_raiseerror($) { return $_[0]->{DBH}->{RaiseError};}


sub set_autocommit($$) {
	my ($self,$flag)=@_;
	my $old=$self->{DBH}->{AutoCommit};
	$self->{DBH}->{AutoCommit} =  $flag ? 1 : 0;
	return $old;
}

sub get_autocommit($) { return $_[0]->{DBH}->{AutoCommit};}


sub get_params($)  { return $_[0]->{PARAM};}


sub set_params($@) {
	my $self=shift;
	my %params=@_;
	my %oldparams=();
	for my $k(keys %params) {
		$oldparams{$k}=$self->{PARAM}->{$k};
		$self->{PARAM}->{$k}=$params{$k};
	}
	return \%oldparams;
}


sub get_database_parameters($) {
	my $self=shift;
	return $self->{DATABASE_PARAMETERS} if defined($self->{DATABASE_PARAMETERS});
	my $rows=$self->fetchall_arrayref("select * from NLS_DATABASE_PARAMETERS") || return undef;
	my %h=();
	for my $r(@$rows) {
		$h{$r->[0]}=$r->[1];
	}
	$self->{DATABASE_PARAMETERS}=\%h;
	return \%h;
}


sub get_session_parameters {
	my $self=shift;
	my $sql="select parameter,value from NLS_SESSION_PARAMETERS ";
	$sql.=" where parameter in (".join(",",map { $self->quote($_) } @_).")";
	my $rows=$self->fetchall_arrayref($sql) || return undef;
	my %h=();
	for my $r(@$rows) {
		$h{$r->[0]}=$r->[1];
	}
	return \%h;
}

sub set_session_parameters {
	my $self=shift;
	my %params=@_;
	my @k=keys %params;
	my $old=$self->get_session_parameters(@k);
	for my $k(@k) {
		$self->do("alter session set ".$k." = ".$self->quote($params{$k})) || return undef;
	}
	return scalar(keys %params);
}

sub get_sysdate($@) {
	my $self=shift;
	my $sql="select ".join(",",map {
										"to_char(sysdate,".$self->quote($_).")"
								   } scalar(@_) == 0 ? ('YYYYMMDDHH24MISS') : @_
					      )." from dual";
	return $self->fetch_minimalized($sql);
}


#
# Consente di testare la validita' di una data
# Ritorna:
#   - stringa vuota		==> data corretta
#   - stringa di errore	==> data non valida
#
sub test_date($$$) {
	my $self = shift;
	my ($date, $format) = @_;
	return 'Expecting string with date data' unless defined $date && $date ne '';
	$format = 'YYYYMMDDHH24MISS' unless defined $format;
	$date = $self->quote($date);
	$format = $self->quote($format);
	local $@;
	my $rows=eval {
		$self->fetchall_arrayref(qq(
			select to_date($date, $format) from dual
		));
	};
	return get_errormessage if $@;
	return get_errormessage unless defined $rows;
	return '';
}


#
# Ritorna un array contenente i tipi sql supportati dal DBI
# ed utilizzabili nella chiamata al metodo call_stored_procedure
#
sub sql_type_list { return map { $_->{TYPE_NAME} } $_[0]->get_dbh()->type_info() }

sub connection {
    return undef unless $_[0]; # !!! evita un warning nel caso di riutilizzo di una connessione preesistente !!!
	return $_[0]->{CONNECTSTRING};
}

sub DESTROY {
	my $self = shift;
	return undef unless $self; # !!! evita un warning nel caso di riutilizzo di una connessione preesistente !!!
	local $@;
	eval {
		print STDERR "-- SIRTI::DB::DESTROY() --->\n" if $self->{PARAM} && $self->{PARAM}->{DEBUG};
		return if $self->{NOT_DESTROY};
		$self->disconnect();
	};
	return undef;
}



if (__FILE__ eq $0) {
	package main;
	use strict;
	use warnings;
	my $before_disconnect=undef;
	my $sqlid=$ARGV[0];
	$sqlid=$ENV{SQLID_DBU} unless defined $sqlid;
	my $db=SIRTI::DB->new($sqlid,DEBUG => 0,BEFORE_DISCONNECT => sub { $before_disconnect="before disconnect\n"; } );
#	print join(' ',$db->get_sysdate('YYYYMMDDHH24MISS','YYYY')),"\n";
	my $db1=new SIRTI::DB($db->get_dbh());
	my $prepare=$db->create_prepare("select * from user_tables where table_name like ?");
	use Data::Dumper;
#	print Dumper($prepare->fetchall_arrayref("LOOKUP%")),"\n";
#	print Dumper($prepare->fetchall_hashref("LOOKUP%")),"\n";
	$prepare->finish();
	$db->do("create table dummy(a char(1))");
	$prepare=$db->create_prepare("insert into dummy values(?)");
	my $n=$prepare->do("1");
	$prepare->finish();
	$db->do("drop table dummy");
	die "test fallito: $n " if $n ne 1;
	my $cursor=$db->create_cursor("select * from cat where rownum < 2");
	while(my $r=$cursor->fetchrow_hashref()) { }
	$cursor->finish();
	my $testdate=$db->test_date("21/12/2012","DD/MM/YYYY");
	die "test non passato: $testdate " if length($testdate) > 0;
	$testdate=$db->test_date("32/12/2012","DD/MM/YYYY");
	die "test non passato " if length($testdate) == 0;
	$prepare=$db->create_prepare("
		begin
			:OUTPAR := :INPAR * 2;
		end;
	");
	$prepare->bind_param(':INPAR',7);
	my $result=undef;
	$prepare->bind_param_inout(':OUTPAR',\$result,10);
	$prepare->execute();
	die "test non passato " if $result ne 14;
	$prepare->finish();
	$db->rollback();
	$db->disconnect();
	die "test non passato" unless defined  $before_disconnect;

}


1;

