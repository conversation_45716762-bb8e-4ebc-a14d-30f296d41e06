package SIRTI::DB::ConnStr;

use strict;
use warnings;
use Carp;

use IPC::SysV qw(IPC_PRIVATE IPC_RMID S_IRUSR S_IRGRP S_IROTH S_IWUSR S_IWGRP S_IWOTH);


use constant {
	DEFAULT_SUIDEXEC			=>  'suidexec' 
	,DEFAULT_APPLICATION_NAME	=>	'connstr'
	,DEFAULT_IPC_SIZE			=>  255
	,DEFAULT_IPC_PERM			=> 	S_IRUSR|S_IRGRP|S_IROTH|S_IWUSR|S_IWGRP|S_IWOTH
	,DEFAULT_SIGNAL_TRAP		=>  'TERM HUP'
	,DEFAULT_DEBUG				=>  0
	,DEFAULT_CLIENT_TAG			=>  '<undef>'
};



sub _exit_after_exec {
	print STDERR "exec fallita ";
	if (defined $ENV{MOD_PERL}) {
		eval { CORE::exit; };
	}
	else {
		eval {exit 1;};
	}
}


sub get_connect_string {  #metodo statico
	my $dbname=shift;
	return undef unless defined $dbname;
	my %params=@_;
	$params{SUIDEXEC}=DEFAULT_SUIDEXEC unless defined  $params{SUIDEXEC};
	$params{APPLICATION_NAME}=DEFAULT_APPLICATION_NAME unless defined $params{APPLICATION_NAME};
	$params{IPC_SIZE}=DEFAULT_IPC_SIZE unless defined $params{IPC_SIZE};
	$params{IPC_PERM}=DEFAULT_IPC_PERM unless defined $params{IPC_PERM};
	$params{SIGNAL_TRAP}=DEFAULT_SIGNAL_TRAP   unless defined  $params{SIGNAL_TRAP};
	$params{SIGNAL_TRAP}=[ split(/\s+/,$params{SIGNAL_TRAP}) ];
	$params{DEBUG}=DEFAULT_DEBUG unless defined $params{DEBUG};
	$params{CLIENT_TAG}=DEFAULT_CLIENT_TAG	unless defined  $params{CLIENT_TAG};

	my %suidparams=(
		IPCSIZE			=>  $params{IPC_SIZE}
		,IPCKEY			=> 	undef
		,DBNAME			=>  $dbname
		,CLIENT_TAG		=>  $params{CLIENT_TAG}
	);

	my $id=undef;
	my %savetrap=();
	my @signals=();
	for my $k(@{$params{SIGNAL_TRAP}}) { 
		$savetrap{$k}=$SIG{$k}; $SIG{$k}=sub { push  @signals,$k unless grep($_ eq $k,@signals); print STDERR "intercettato segnale $k"; }
	}
	local $@;
	my $str=eval {
	 	$id=shmget(IPC_PRIVATE,$params{IPC_SIZE},$params{IPC_PERM}) ||  croak "$!";
		print STDERR "generata chiave IPC  '$id'\n" if $params{DEBUG};
		$suidparams{IPCKEY}=$id;
		my @cmd=($params{SUIDEXEC},$params{APPLICATION_NAME},map {  $_.'="'.$suidparams{$_}.'"' } grep (defined  $suidparams{$_},keys  %suidparams));
		print STDERR "chiamata di '",join(' ',@cmd),"'\n" if $params{DEBUG};
		my $pid=fork;
		croak "fork fallita" unless defined $pid;
		if ($pid == 0) { #figlio
			for my $k(keys %savetrap) {
				my $h=$savetrap{$k};
				$SIG{$k}=defined $h ? $h : 'DEFAULT'; 
			}
			{exec @cmd};
			_exit_after_exec;
		}
		print STDERR "fork con figlio $pid\n"  if $params{DEBUG};
		my $pid_wait=wait;
		croak "nessun figlio esistente " if $pid_wait == -1;
		my $rc=$?;
		croak "figlio terminato in errore: $rc" if $rc != 0;
		my $buff='';
		if (defined $id) {
			shmread($id, $buff,0,$params{IPC_SIZE}) || croak "$!";
		}
		$buff;
	};
	if (defined $id) {
		shmctl($id, IPC_RMID, 0) || croak "$!";
	}
	for my $k(keys %savetrap) {
		my $h=$savetrap{$k};
		$SIG{$k}=defined $h ? $h : 'DEFAULT'; 
	}
	for my $s(@signals) {		 # mi rimando i segnali intercettati ed ignorati
		kill($s,$$);
	}
	if ($@) {
		print STDERR $@,"\n";
		return undef;
	}
	my $begin=index($str, "\0");
	$str=substr($str,0,$begin) if $begin >= 0;
	return undef if length($str) == 0;  #stringa non trovata
	my ($r)=$str=~/^STRCONN=(\S*)/;
	unless (defined $r) {
		print STDERR $str,"\n";
		return undef;
	}
	$r=undef if  $r=~/^\s*$/;
	return $r;
}



if ($0 eq __FILE__) {
	my $db=defined $ARGV[0] ? $ARGV[0] : "db1"; 
	my $strconn=SIRTI::DB::ConnStr::get_connect_string($db,DEBUG => 1);
	if (length($strconn) == 0) {
		print "stringa connessione non recepita\n";
	}
	else {
		print "$strconn\n";
	}
}



1;
