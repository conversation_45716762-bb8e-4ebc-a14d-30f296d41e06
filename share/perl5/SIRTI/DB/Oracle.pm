
package SIRTI::DB::Oracle;
#
#  implementa particolari metodi di Oracle
#

#use SIRTI::DB;
use strict;
use warnings;
use base  qw(SIRTI::DB);


use constant {
	DEFAULT_DBMS_OUTPUT_BUFFER	=>  100000
};


sub new  {
	my $class=shift;
	#oltre a quelli di SIRTI::DB
	#DBMS_OUTPUT					=> abilita dbmsoutput
	#DBMS_OUTPUT_BUFFER				=>  size del buffer per dbmsoutput

	my $self=$class->SUPER::new(@_);
	my $ob=defined $self->{PARAM}->{DBMS_OUTPUT_BUFFER} ? $self->{PARAM}->{DBMS_OUTPUT_BUFFER} :  DEFAULT_DBMS_OUTPUT_BUFFER;
	$self->set_dbms_output(ENABLE => $self->{PARAM}->{DBMS_OUTPUT},DBMS_OUTPUT_BUFFER => $ob);
	return $self;
}


sub create_prepare($$;$) {
	my ($self,$query,$params)=@_;
	print STDERR "-- Oracle::create_prepare() --->\n" . $query . ";\n" if $self->{PARAM}->{DEBUG};
	return _new SIRTI::DB::Oracle::Prepare($self->get_dbh(),$query,$self,$params);
}


sub create_cursor($$;$) {
	my ($self,$query,$params)=@_;
	print STDERR "-- Oracle::create_cursor() --->\n" . $query . ";\n" if $self->{PARAM}->{DEBUG};
	return _new SIRTI::DB::Oracle::Cursor($self->get_dbh(),$query,$self,$params);
}


sub set_dbms_output {
	my $self=shift;
	my %params=@_;
	$self->{PARAM}->{DBMS_OUTPUT_BUFFER}=$params{DBMS_OUTPUT_BUFFER} if defined $params{DBMS_OUTPUT_BUFFER};
	my $old=$self->{PARAM}->{DBMS_OUTPUT};
	$self->{PARAM}->{DBMS_OUTPUT}=$params{ENABLE};
	$self->get_dbh()->func($self->{PARAM}->{DBMS_OUTPUT_BUFFER}, 'dbms_output_enable') if $params{ENABLE};	    #disable non esiste nel DBI quindi viene ignorato
	return $old ? 1 : 0;
}

sub get_dbms_output {
	my $self=shift;
	my $fd=shift;
	return undef unless  $self->{PARAM}->{DBMS_OUTPUT};
	$fd=*STDERR unless defined $fd;
	my @lines=$self->get_dbh()->func('dbms_output_get');
	for my $line(@lines) {
		next unless defined $line;
		print $fd $line,"\n";
	}
	return $self;
}

package SIRTI::DB::Oracle::Prepare;
use base "SIRTI::DB::Prepare";

sub create_cursor($@) {
	my  $self=shift;
	print STDERR "-- Oracle::create_cursor() --->\n" .join(',',map { $self->{DBH}->quote($_) } @_).";\n" if $self->{PARENT}->{PARAM}->{DEBUG};
	return  SIRTI::DB::Oracle::Cursor->_new1($self,@_);
}

package SIRTI::DB::Oracle::Cursor;
use base "SIRTI::DB::Cursor";

sub lob_read($$$$) {
	my ($self,$locator,$offset,$size)=@_;
	print STDERR "-- lob_read() --->\n" if $self->{PARENT}->{PARAM}->{DEBUG};
	return $self->get_dbh()->ora_lob_read( $locator, $offset, $size)
}


sub lob_write($$$$) { 
	my ($self,$locator,$offset,$buffer)=@_;
	print STDERR "-- lob_write() --->\n" if $self->{PARENT}->{PARAM}->{DEBUG};
	return $self->get_dbh()->ora_lob_write($locator,$offset,$buffer);
}	

sub lob_append($$$) {
	my ($self,$locator,$buffer)=@_;
	print STDERR "-- lob_append() --->\n" if $self->{PARENT}->{PARAM}->{DEBUG};
	return $self->get_dbh()->ora_lob_append($locator,$buffer);
}


sub lob_trim($$$) {
	my ($self,$locator,$length)=@_;
	print STDERR "-- lob_trim() --->\n" if $self->{PARENT}->{PARAM}->{DEBUG};
	return $self->get_dbh()->ora_lob_trim($locator,$length);
}

sub lob_length($$) {
	my ($self,$locator)=@_;
	print STDERR "-- lob_length() --->\n" if $self->{PARENT}->{PARAM}->{DEBUG};
	return $self->get_dbh()->ora_lob_length($locator);
}


if (__FILE__ eq $0) {  #esempio di uso dei lobs
	package main;
	use strict;
	use warnings;
	my $db=new SIRTI::DB::Oracle($ENV{SQLID_DBU},DEBUG => 1);
	use constant tb => 'prova_lob';
	my ($old_pe,$old_re)=($db->set_printerror(1),$db->set_raiseerror(0));
	$db->do("drop table ".tb);
	$db->set_printerror($old_pe);$db->set_raiseerror($old_re);
	$db->do("create table ".tb."(id number(20),cl clob,bl blob)");
	for my $i(0..3) {
		$db->do("insert into ".tb." values($i,EMPTY_CLOB(),EMPTY_BLOB())");
	}
	my ($bclob,$bblob)=('scrittura clob','scrittura blob');
	{ 
		#scrivi
		my $sth=$db->create_prepare("select * from ".tb." for update",{ ora_auto_lob => 0 });
		my $cur=$sth->create_cursor();
		while(my $row = $cur->fetchrow_hashref()) { 
			$cur->lob_write($row->{CL},1,$bclob.$row->{ID});
			$cur->lob_write($row->{BL},1,$bblob.$row->{ID});
		}
		$sth->finish();
	}
	{ 
		#leggi
		my $sth=$db->create_prepare("select * from ".tb." ",{ ora_auto_lob => 0 });
		my $cur=$sth->create_cursor();
		while(my $row = $cur->fetchrow_hashref()) { 		
			my $clob=$cur->lob_read($row->{CL},1,length($bclob)+length($row->{ID}));
			my $blob=$cur->lob_read($row->{BL},1,length($bblob)+length($row->{ID}));
			SIRTI::Err::check($clob eq $bclob.$row->{ID});
			SIRTI::Err::check($blob eq $bblob.$row->{ID});
		}
		$sth->finish();
	}
	
	
	for my $i(4..6) {
		$db->do("insert into ".tb." values($i,EMPTY_CLOB(),EMPTY_BLOB())");
	}

	{ 
		#scrivi
		my $cur=$db->create_cursor("select * from ".tb." for update",{ ora_auto_lob => 0 });
		while(my $row = $cur->fetchrow_hashref()) { 
			$cur->lob_write($row->{CL},1,$bclob.$row->{ID});
			$cur->lob_write($row->{BL},1,$bblob.$row->{ID});
		}
	}
	{ 
		#leggi
		my $cur=$db->create_cursor("select * from ".tb." ",{ ora_auto_lob => 0 });
		while(my $row = $cur->fetchrow_hashref()) { 		
			my $clob=$cur->lob_read($row->{CL},1,length($bclob)+length($row->{ID}));
			my $blob=$cur->lob_read($row->{BL},1,length($bblob)+length($row->{ID}));
			SIRTI::Err::check($clob eq $bclob.$row->{ID});
			SIRTI::Err::check($blob eq $bblob.$row->{ID});
		}
	}
	
	$db->commit();

}

1;

		
