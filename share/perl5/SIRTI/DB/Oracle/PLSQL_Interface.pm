package SIRTI::DB::Oracle::PLSQL_Interface;


#
# interfaccia perl verso plsql 
#


use strict;
use warnings;
use Digest::MD5 qw/md5_base64/;
use DBI qw/:sql_types/;
use DBD::Oracle qw/:ora_types/;
use utf8;
use Carp;



sub _cs_call {
	my ($self,$name,$values,%params)=@_;
	my @filters=defined $params{FILTERS} ? @{$params{FILTERS}} : ();
	my $s='';
	my $idx=0;
	if ($params{FUNCTION_CALL}) {
		$s.='? := ';
		++$idx;
	}
	return 	"$s $name(".join(',',map {
											my $f=$filters[$_];
											my $out=defined $f ? $f.'(?)' : '?';
											$out;
										}  ($idx..scalar(@$values) - 1)
									  ).")";
}

sub _cs_exceptions {
	my ($self,%params)=@_;
	my @exceptions=$params{EXCEPTIONS} ? @{$params{EXCEPTIONS}} : @{$self->{EXCEPTIONS}};
	return  scalar(@exceptions) 
		?
			"
				exception
			".join("\n",map {  
								"when ".$exceptions[$_ - 1]." then rc := $_;"  
					}  (1..scalar(@exceptions)))
		: 
			'';
}

sub get_exception_block {
	my ($self,%params)=@_;
	return $self->_cs_exceptions(%params);
}

sub _create_prepare {
	my ($self,$q,%params)=@_;
	print STDERR ref($self),"::_create_prepare su '$q'\n" if $self->{DEBUG};
	my $name=md5_base64($q);
	$self->{PREPARE}->{$name}=$self->{DB}->prepare($q) unless defined $self->{PREPARE}->{$name};
	return $name;
}

sub _bind_params {
	my ($self,$prepare_name,$values,%params)=@_;
	my $sth=$self->{PREPARE}->{$prepare_name};
	my @bind_inout=defined $params{BIND_INOUT} ? @{$params{BIND_INOUT}}   : ();
	for my $i(0..scalar(@$values) - 1) {
		my $h=$bind_inout[$i];
		if ($h) {
			print STDERR ref($self),"::_bind_params in/out su parametro $i'\n" if $self->{DEBUG};
			$sth->bind_param_inout($i + 1,$h->{PVALUE},$h->{SIZE},(defined $h->{OTHER_PARAMS} ? @{$h->{OTHER_PARAMS}} : ()))  || return undef;
		}			
		else {
			print STDERR ref($self),"::_bind_params input  su parametro $i'\n" if $self->{DEBUG};
			$sth->bind_param($i + 1,$values->[$i],(defined $params{BIND} ? @{$params{BIND}->[$i]} : ()))  || return undef;
		}
	}
	return $sth;
}

sub call_scalar_func {
	my ($self,$name,$values,%params)=@_;
	my $q="
			declare
				rc number(2) := 0;
			begin
				begin
					"
					.$self->_cs_call($name,$values,%params,FUNCTION_CALL => 1).';'
					.$self->_cs_exceptions."
				end;
				? := rc;
			end;
		";


	my $prepare_name=$self->_create_prepare($q) || return undef;
	push @$values,undef;   # per l' RC
	my @bind_inout=defined $params{BIND_INOUT} ? @{$params{BIND_INOUT}}   : ();
	$bind_inout[scalar(@$values) - 1]={ PVALUE => \my $rc,SIZE => 2 };
	my $sth=$self->_bind_params($prepare_name,$values,%params,BIND_INOUT => \@bind_inout) || return undef;
	$sth->execute	 || return undef;
	return $rc;
}


sub call_pipe_func {
	my ($self,$name,$values,%params)=@_;
	my $q="select *  from table(".$self->_cs_call($name,$values,%params).(defined $params{ORDER_BY} ? $params{ORDER_BY} : '').")";
	my $prepare_name=$self->_create_prepare($q) || return undef;
	my $sth=$self->_bind_params($prepare_name,$values,%params) || return undef;
	$sth->execute || return undef;
	my @rows=();
	while(my $r=$sth->fetchrow_hashref()) {
		push @rows,$r;
	}				
	return undef if $DBI::err;
	return wantarray ? @rows : \@rows;
}

sub call_proc {
	my ($self,$name,$values,%params)=@_;
	my $q="
			declare
				rc number(2) := 0;				
			begin
				begin
					".$self->_cs_call($name,$values,%params).';'
					.$self->_cs_exceptions."
				end;
				? := rc;
			end;
	";
	my $prepare_name=$self->_create_prepare($q) || return undef;	
	push @$values,undef;   # per l' RC
	my @bind_inout=defined $params{BIND_INOUT} ? @{$params{BIND_INOUT}}   : ();
	$bind_inout[scalar(@$values) - 1]={ PVALUE => \my $rc,SIZE => 2 };
	my $sth=$self->_bind_params($prepare_name,$values,%params,BIND_INOUT => \@bind_inout) || return undef;
	$sth->execute	 || return undef;
	return $rc;
}

sub call_do_block {
	my ($self,$sql,$values,%params)=@_;
	my $q="
			begin
				".$sql.";
				? := sql%rowcount;
			end;
	";
	my $prepare_name=$self->_create_prepare($q) || return undef;	
	push @$values,undef;   # per l' RC
	my @bind_inout=defined $params{BIND_INOUT} ? @{$params{BIND_INOUT}}   : ();
	$bind_inout[scalar(@$values) - 1]={ PVALUE => \my $count,SIZE => 18 };
	my $sth=$self->_bind_params($prepare_name,$values,%params,BIND_INOUT => \@bind_inout) || return undef;
	$sth->execute	 || return undef;
	return $count == 0 ? '0E0' : $count;
}


sub call_anonymous_block {
	my ($self,$q,$values,%params)=@_;
	my $prepare_name=$self->_create_prepare($q) || return undef;	
	my $sth=$self->_bind_params($prepare_name,$values,%params) || return undef;
	$sth->execute	 || return undef;
	return $self;
}

sub get_errmsg {
	my ($self,$rc,%params)=@_;
	my $exceptions_msg=defined $params{EXCEPTIONS_MSG} ? $params{EXCEPTIONS_MSG} : $self->{EXCEPTIONS_MSG};
	return 	$exceptions_msg->[$rc] if defined $rc;
	my @m=@$exceptions_msg;
	return wantarray ? @m : \@m;
}


#
# parametri conosciuti: 
#		DB - connessione ad un database 
#		EXCEPTIONS 		- array di nomi di eccezioni
#		EXCEPTIONS_MSG 	- array di messaggi relativi ai nomi di eccezioni 
#		DEBUG			- se vero assume debug - se non specificato prende il valore di ART_DB_DEBUG 

sub new {
	my ($class,%params)=@_;
	croak "parametro DB non impostato\n" unless defined $params{DB};
	my $db=sub {
		my $db=shift;
		my $r=ref $db;
		return $db->get_dbh if $r eq 'SIRTI::DB';
		return $db if $r eq 'DBI::db';
		return $db->{dbh} if $r eq 'ART::C_db';
		return undef;
	}->($params{DB});
	croak ref($params{DB}).": tipo parametro DB non conosciuto\n" unless defined $db;
	$params{DB}=$db;
	delete $params{PREPARE};
	for my $e (qw/EXCEPTIONS EXCEPTIONS_MSG/) {
		$params{$e}=[] unless defined $params{$e};
	}
	$params{DEBUG}=$ENV{ART_DB_DEBUG} unless defined $params{DEBUG};
	return bless \%params,$class;
}


sub free {
	my $self=shift;
	if (ref($self->{PREPARE}) eq 'HASH') {      #chiude le prepare
		for my $v(values %{$self->{PREPARE}}) {
			$v->finish();
		}
		delete $self->{PREPARE};
	}
}


sub DESTROY {
	my $self=shift;
	local $@;
	eval { $self->free(); }
}

1;
