package SIRTI::Date::Calendar;

use strict;
use warnings;
use Carp;

use Date::Calendar;
use Date::Calendar::Profiles qw( $Profiles );

use base 'Date::Calendar';

sub new {
	
	my $this  = shift;
	my $class = ref($this) || $this;
	my %param = @_;
	
	##############################################################################
	# il parametro WEEKEND accettato SOLO a partire dalla versione 6.3
	# 1=Monday, 2=Tuesday, 3=Wednesday, 4=Thursday, 5=Friday, 6=Saturday, 7=Sunday
	##############################################################################
	my @weekend = defined $param{WEEKEND} ? @{$param{WEEKEND}} : undef;

	# patch festivita'
	$Profiles->{'IT'}->{"Fondazione della Repubblica 1946"} = sub   {
                                                                    my($year,$label) = @_;
                                                                    return($year,6,2) if (($year >= 1947 && $year < 1977) || $year >= 2001);
                                                                    return(); # didn't exist before 1947 or between 1977 and 2000
                                                              };

	$Profiles->{'IT'}->{"Centocinquantenario dell'Unità d'Italia"} = sub   {
                                                                    my($year,$label) = @_;
                                                                    return($year,3,17) if ($year == 2011);
                                                                    return(); # only in 2011
                                                              };
	my $self  =
		defined $param{WEEKEND} ?		
			$class->SUPER::new($Profiles->{'IT'},0,@weekend)
		:
			$class->SUPER::new($Profiles->{'IT'});
	
	return undef unless $self;
	
	bless( $self, $class );
	
}

#######################################################################
# UNIT TEST
#######################################################################

if ( __FILE__ eq $0 ) {

	unless ($ARGV[0]){
		print "calcolo del prossimi 7 giorni lavorativi\n";
    	print "parametri: gg-mm-yyyy [sabato_lavorativo]\n";
    	exit 1;
	}

	my  ($g, $m, $a) = split('-', $ARGV[0]);
	
	printf "data iniziale: %04d%02d%02d\n",$a, $m, $g;
	
	my $cal =
		defined $ARGV[1] ? 
		SIRTI::Date::Calendar->new(WEEKEND=>[7])
		:
		SIRTI::Date::Calendar->new();
	
	print "considero sabati lavorativi\n" if $ARGV[1]; 
	
	# prossimi 7 giorni lavorativi
    printf(
    	"d: %3d w: %d\n",$_,
    		$cal->add_delta_workdays($a,$m,$g,$_),
    	) for (1..7);
}

1;
