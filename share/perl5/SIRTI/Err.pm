package SIRTI::Err;
# public 
use strict;
use warnings;
use Carp;

use base qw(Exporter);

# use the option -MCarp=verbose in cmd line for display a stack trace 
use constant EXIT_OK => 0;  
use constant EXIT_DATAERROR => 1;   
use constant EXIT_INPUTERROR => 2;
use constant EXIT_DBFAILURE => 3;
use constant EXIT_SYSTEMERROR => 4;
use constant EXIT_INTERNALERROR => 5;

my %t=( 
					'errcode' => [ qw ( 
										EXIT_OK 
										EXIT_DATAERROR     
										EXIT_INPUTERROR
										EXIT_DBFAILURE 
										EXIT_SYSTEMERROR 
										EXIT_INTERNALERROR 
									) ] 
					,'ut'	=> [ qw (
										nvl
										check_array_value_options
									)
								]
					,'throw' => [
									qw (
											db_fatal_error
											system_error
											internal_error
									)
								]
					,'contract' =>  [
										qw (
												prec
												post
												check
										)
									]
		);

our %EXPORT_TAGS=( all => [ map { @{$t{$_}} } keys %t ],%t); 
our @EXPORT_OK=( @{$EXPORT_TAGS{all}} );
our @EXPORT=();



sub db_fatal_error { # static method 
	my $db = shift;
	require SIRTI::DB;
	my $code = SIRTI::DB::get_errorcode();
	my $descr = SIRTI::DB::get_errormessage();
	my $msg = '';
	$msg = "DB FATAL ERROR - code: $code \n" if (defined($code));
	$msg .= "$descr\n"					if (defined($descr));
	if (scalar(@_) > 0) {
		$msg .= join ("\n", @_)."\n";
	}
	print STDERR $msg;
	$db->rollback() if (defined($db));
	Carp::carp;
	$!= EXIT_DBFAILURE;
	die $msg;
}

sub system_error { # static method 
	my $msg = join ("\n", @_)."\n";
	$msg = 'SYSTEM ERROR '.$msg;
	print STDERR $msg;
	Carp::carp;
	$!= EXIT_SYSTEMERROR;
	die $msg;

}

sub internal_error { # static method 
	my $msg = join ("\n", @_)."\n";
	$msg = 'PANIC: '.$msg;
	print STDERR $msg;
	Carp::carp;
	$!= EXIT_INTERNALERROR;
	die $msg;
}

sub prec {# static method
	my $cond=shift;
	return if ($cond);
	my $msg = join ("\n", @_)."\n";
	$msg = 'PRECONDITION FAILED: '.$msg;
	print STDERR $msg;
	Carp::carp;
	$!= EXIT_INTERNALERROR;
	die $msg;
}

sub post {# static method
	my $cond=shift;
	return if($cond);
	my $msg = join ("\n", @_)."\n";
	$msg = 'POSTCONDITION FAILED: '.$msg;
	print STDERR $msg;
	Carp::carp;
	$!= EXIT_INTERNALERROR;
	die $msg;
}

sub check {# static method
	my $cond=shift;
	return if($cond);
	my $msg = join ("\n", @_)."\n";
	$msg = 'CHECK FAILED: '.$msg;
	print STDERR $msg;
	Carp::carp;
	$!= EXIT_INTERNALERROR;
	die $msg;
}


sub nvl { # static method
	return defined($_[0]) ? $_[0] : '' if scalar(@_) == 1;
	return defined($_[0]) ? $_[0] : $_[1] if scalar(@_) == 2;
	return defined($_[0]) ? $_[1] : $_[2] if scalar(@_) == 3;
	check(0,"SIRTI::Err::nvl: ".scalar(@_).": numero parametri non corretto (1..3) ");
}

sub nvl1($$) { # static method
	my ($s,$c)=@_;
	Carp::carp "SIRTI::Err::nvl1: funzione deprecata - usa SIRTI::Err::nvl";
	return defined($s) ? $s : $c;
}

sub nvl2($$$) { # static method
	my ($s,$t,$f)=@_;
	Carp::carp "SIRTI::Err::nvl2: funzione deprecata - usa SIRTI::Err::nvl";
	return defined($s) ? $t : $f;
}


sub check_warn { # static method
	my $cond=shift;
	return  if($cond);
	print STDERR "warn check failed: ";
	print STDERR @_;
	print STDERR "\n";
	Carp::carp;
}		



sub check_array_value_options { #  esegue un check di validita per una opzione con valori da un elenco separato da :
							   #  ritorna un puntatore ad un array selezionati e ok 
							   #  altrimenti undef
							   #  viene effettuato un log su STDERR degli errori 
	my $value=shift;
	# @_ contiene l' elenco dei valori validi 
	prec(defined($value),'valore non definito');
	my $f=0;
	my @t=split /[:,]/,$value;
	for my $v(@t) {
		my $x=$v;
		$x=~s/^(\w+=).*$/$1/;
		if (scalar(grep /^$x$/,@_) == 0) {
			print STDERR "$x: tipo check non conosciuto - valori validi '".join(',',@_)."'\n";
			$f++;
		}
	}
	return $f ? undef : \@t; 
}

1;

