package SIRTI::Geo::Coder::Google;

=pod

=head1 NAME

SIRTI::Geo::Coder::Google - Recupero informazioni georeferenziali mediante Google Maps API

=head1 SYNOPSIS

  use SIRTI::Geo::Coder::Google;
  
  $geo = SIRTI::Geo::Coder::Google->new(
      ua => LWP::UserAgent->new( # uses a custom UA due to <PERSON><PERSON>'s proxy issues!
          proxy => [ 'https', 'connect://*************:8080' ],
          timeout => 10
      ),
      client => 'gme-sirtispa',
      key => '****************************',
      language => 'us',
      gl => 'us'
  );
  my $location1 = $geo->query_by_address(q/via stamira d'ancona 9 milano (mi)/);
  my $location2 = $geo->query_by_latlng(q/45.4994073,9.2287115/);

=head1 DESCRIPTION

Lo scopo principale della libreria C<SIRTI::Geo::Coder::Google> e' quello di semplificare l'accesso
alla Google Maps API per recuoperare le informazioni georeferenziali attraverso un indirizzo oppuer
attraverso le coordinate georeferenziali.

=cut

use strict;
use warnings;
use Carp;

use LWP::UserAgent;
use Geo::Coder::Google;

use base 'SIRTI::Base';

our $VERSION = '0.01';

=head2 CONSTRUCTOR

=head3 C<new>

C<new()> accetta in input i seguenti parametri:

=over 3

=item C<client =E<gt> STRING>

(B<obbligatorio>) L'identificativo 'client' della sottoscrizione ai servizi Google.

=item C<key =E<gt> STRING>

(B<obbligatorio>) La chiave privata utilizata per la firma della richiesta ai servizi Google.

=item C<ua =E<gt> L<LWP::Useragent|LWP::UserAgent>>

E' possibile fornire un oggetto L<LWP::Useragent|LWP::UserAgent> per gestire la presenza di 
un proxy, ad esempio, nel caso degli ambienti Sirti:

  LWP::UserAgent->new(
    proxy => [ 'https', 'connect://*************:8080' ],
    timeout => 10
  )

Se non specificato, la connessione utilizzale impostazioni del proxy dell'environment.

=item C<language =E<gt> STRING> (default: C<it>)

E' possibile specificare un linguaggio differente specificando il relativo codice di due caratteri.

=item C<gl =E<gt> STRING> (default: C<it>)

E' possibile specificare un paese di riferimento differente specificando il relativo codice di due caratteri.

=back

=cut
sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my %params = @_;
	my $self = {};

	my %geo_opts = (
		apiver => 3,
		language => 'it',
		gl => 'it'
	);
	if (exists($params{ua}) && ref($params{ua}) eq 'LWP::UserAgent') {
		$geo_opts{ua} = $params{ua};
	} else {
		# Se non specificato, passo un oggetto LWP::UserAgent che eredita dall'ENV le impostazioni del proxy
		$geo_opts{ua} = LWP::UserAgent->new();
		$geo_opts{ua}->env_proxy;
	}
	croak "Missing 'client' parameter\n"
		unless exists($params{client});
	$geo_opts{client} = $params{client};
	croak "Missing 'key' parameter\n"
		unless exists($params{key});
	$geo_opts{key} = $params{key};
	
	if (exists($params{language})) {
		if (!ref($params{language}) && length($params{language}) == 2) {
			$geo_opts{language} = $params{language};
		} else {
			warn "Ignoring 'language' parameters due to unexpected type/value\n";
		}
	}
	if (exists($params{gl})) {
		if (!ref($params{gl}) && length($params{gl}) == 2) {
			$geo_opts{gl} = $params{gl};
		} else {
			warn "Ignoring 'gl' parameters due to unexpected type\n";
		}
	}
	
	$self->{__GEO} = Geo::Coder::Google->new( %geo_opts );
	
	return bless($self, $class);
}

=head2 METHODS

=cut


=head3 C<query_by_address>

C<query_by_address()> accetta in input l'indirizzo da ricercare come I<SCALAR>.

=cut
sub query_by_address {
	my $self = shift;
	my $address = shift;
	$self->last_error("Missing address")
		&& return undef
			unless defined $address;
	my $location = eval {
		$self->{__GEO}->geocode( location => $address )
	};
	$self->last_error("Service unavailable")
		&& return undef
			if $@;
	
	$self->last_error("Unknown address")
		&& return undef
			unless defined $location;
	
	return $location;
}


=head3 C<query_by_latlng>

C<query_by_latlng()> accetta in input le coordinate georeferenziali da ricercare come I<SCALAR> nel 
formato I<latitudine,longitudine>, ad esempio: "45.4994073,9.2287115".

=cut
sub query_by_latlng {
	my $self = shift;
	my $latlng = shift;
	$self->last_error("Missing coordinates")
		&& return undef
			unless defined $latlng;
	
	my $reverse_location = eval {
		$self->{__GEO}->reverse_geocode( latlng => $latlng )
	};
	
	$self->last_error("Service unavailable")
		&& return undef
			if $@;
	
	$self->last_error("Unknown coordinates")
		&& return undef
			unless defined $reverse_location;
	
	return $reverse_location;
}

=head3 C<get_geo_location(LOCATION => <LOCATION>[, FORMAT => <FORMAT>])>

C<get_geo_location()> accetta in input l'oggetto location (restituito dal metodo query_by_address) e
restituisce la locazione nel formato richiesto. Ad esempio il formato STRING restituisce "45.4994073,9.2287115".

=cut
sub get_geo_location {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->last_error($errmsg)
		and return undef
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					LOCATION	=> { isa => 'HASH' }
				}
				,OPTIONAL	=> {
					FORMAT		=> { isa => 'SCALAR', list => ['STRING'] },
				}
	);
	
	if ($params{FORMAT} eq 'STRING'){
		$self->last_error($errmsg)
			and return undef
				unless $self->check_named_params(
					 ERRMSG		=> \$errmsg
					,PARAMS		=> $params{LOCATION}
					,MANDATORY	=> {
						geometry	=> { isa => 'HASH' }
					}
					,IGNORE_EXTRA_PARAMS => 1
		);
		
		$self->last_error($errmsg)
			and return undef
				unless $self->check_named_params(
					 ERRMSG		=> \$errmsg
					,PARAMS		=> $params{LOCATION}->{geometry}
					,MANDATORY	=> {
						location	=> { isa => 'HASH' }
					}
					,IGNORE_EXTRA_PARAMS => 1
		);
		
		$self->last_error($errmsg)
			and return undef
				unless $self->check_named_params(
					 ERRMSG		=> \$errmsg
					,PARAMS		=> $params{LOCATION}->{geometry}->{location}
					,MANDATORY	=> {
						lat	=> { isa => 'SCALAR' },
						lng	=> { isa => 'SCALAR' },
					}
					,IGNORE_EXTRA_PARAMS => 1
		);
		
		return $params{LOCATION}->{geometry}->{location}->{lat} . ',' . $params{LOCATION}->{geometry}->{location}->{lng};
	} else {
		$self->last_error("Unknown format");
		return undef;
	}
}

=head1 AUTHOR

Alvaro Livraghi, E<lt><EMAIL><gt>

=head1 CONTRIBUTE

...

=head1 BUGS

Please use GitHub project link above to report problems or contact authors.

=head1 COPYRIGHT AND LICENSE

Copyright 2018 by Sirti S.p.A.

=cut


1;