package SIRTI::Getopt::Long;

use strict;
use warnings;

use Exporter;
use vars qw(@ISA @EXPORT @EXPORT_OK);
@ISA = qw(Exporter);

use Getopt::Long qw();
Getopt::Long::Configure(qw(bundling no_ignore_case no_auto_abbrev));

BEGIN {
    # Init immediately so their contents can be used in the 'use vars' below.
    @EXPORT    = qw(&GetOptions);
    @EXPORT_OK = qw();
}

our @TMP = @ARGV;

sub GetOptions {
	return Getopt::Long::GetOptionsFromArray ( \@TMP, @_ );
}

1;

