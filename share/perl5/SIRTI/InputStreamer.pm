package SIRTI::InputStreamer;
use strict;
use warnings;
use Carp;
use base qw(Exporter);

use overload "<>" => \&get_line;
# attenzione - esiste un baco nel package overload per cui chiamando my @y=<$x> 
# esegue sempre la lettura di una sola riga (in pratica wantarray e' sempre falso)
#

my %t=( 
	'overload' 		=> [ ( "<>" ) ] 
	,'resolve_path_name' => [ qw ( resolve_path_name_zip resolve_path_name_text resolve_path_name_unknow resolve_path_name ) ] 	
);
our %EXPORT_TAGS=( all => [ map { @{$t{$_}} } keys %t ],%t); 
our @EXPORT_OK=( @{$EXPORT_TAGS{all}} );
our @EXPORT=();
our $VERSION=0.0.3;

=pod
	iteratore su  un qualunque oggetto simile ad un flusso (file array stringa procedura)
=cut

sub _init_input_stream {
	my $self=shift;
	return $self unless defined $self->{INPUT_STREAM}; 
	my $r=ref($self->{INPUT_STREAM});
	if ($r eq '') { #string
		$self->{I}->{P}=0;  #current position
	}
	elsif ($r eq 'ARRAY') {
		$self->{I}={ R => 0,P => 0};  #current index + current position
	}
	elsif ($r eq 'SCALAR') { #reference to scalar
		$self->{I}->{P}=0;  #current index
	}
	return $self;
}



=pod 
	costruttore
		parametri - INPUT_STREAM - imposta uno stream di input 
					e\'  possibile associare una stringa, un array, una procedura o un descrittore di file aperto	in lettura
					il default e\' nessuno
=cut

sub new {
	my $class=shift;
	my %params=@_;
	my $s=bless(\%params,$class);
	return $s->_init_input_stream;
}


=pod
	imposta il descrittore di input
	e\' possibile associare una stringa, un array, una procedura o un descrittore di file aperto	in lettura
=cut

sub set_input_descriptor {
	my ($self,$fd,%params)=@_;
	$self->{INPUT_STREAM}=$fd;
	return $self->_init_input_stream;
}



=pod 
	metodi statici - resolve_path_name risolve un file in base al contenuto, effettua una apertura in lettura e ritorna un descrittore di file 
	questo metodo usa i metodi statici resolve_path_name_XXX  dove XXX e\' il tipo di file
	in caso di errore il metodo ritorna undef - occorre consultare la variabile $! per il tipo di errore
=cut


sub resolve_path_name_zip {
	my $path=shift;
	unless (defined $path) {
		$!=2; # No such file or directory
		return undef;
	}
	open(my $fd,"unzip -p '$path'|");
	return $fd;
}

sub resolve_path_name_text {
	my $path=shift;
	unless (defined $path) {
		$!=2; # No such file or directory
		return undef;
	}
	open(my $fd,'<',$path);
	return $fd;
}


sub resolve_path_name_unknow {
	my $path=shift;
	unless (defined $path) {
		$!=2; # No such file or directory
		return undef;
	}
	open(my $fd,'<',$path);
	return undef unless defined $fd;
	binmode($fd,':raw');
	return $fd;
}


sub resolve_path_name {
	my $path=shift;
	unless (defined $path) {
		$!=2; # No such file or directory
		return undef;
	}
	if (-d $path) {
		$!=21;     # Is a directory
		return undef;
	}

	unless (-f $path)  {
		$!=2;  # No such file or directory
		return undef;
	}

	unless (-r $path) {
		$!=13; 	#Permission denied
		return 	undef;
	}

	require File::Type;
	my $file_type=File::Type->mime_type($path);
	return resolve_path_name_zip($path) if $file_type eq 'application/zip';
	return resolve_path_name_text($path) if $file_type eq 'application/octet-stream';
	return resolve_path_name_unknow($path);

}



=pod
	legge n caratteri e li riporta in uscita 
	ritorna undef o stringa nulla se si e\' effettuata la lettura oltre la fine
=cut

sub get_chars {  #get n chars - default 1
	my ($self,$n,%params)=@_;
	$n=1 unless defined $n;
	my $stream=$self->{INPUT_STREAM};
	croak "INPUT_STREAM non definita" unless defined $stream;
	return '' if $n <= 0;
	my $r=ref($stream);
	if ($stream eq *STDIN || ref($stream) eq 'GLOB') {
		my $s=undef;
		my $r=read $stream,$s,$n;
		croak "$!" unless defined $r;
		return '' if $r == 0;
		return $s;
	}
	elsif ($r eq '') { #string
		return  '' if  $self->{I}->{P} >= length($stream);
		my $s=substr($stream,$self->{I}->{P},$n);
		$self->{I}->{P} += $n;
		return $s;
	}
	elsif ($r eq 'ARRAY') {
		return '' if $self->{I}->{R} >= scalar(@{$self->{INPUT_STREAM}});
		my $s='';
		while($self->{I}->{R} < scalar(@$stream)  && length($s) < $n) {
			my $e=$self->{INPUT_STREAM}->[$self->{I}->{R}];
			$e='' unless defined $e;
			$s .= "\n" if $self->{I}->{P} == 0 && $self->{I}->{R} > 0;
			my $m=$n - length($s);
			$s .= substr($e,$self->{I}->{P},$m);	
			$self->{I}->{P} += $m;
			if ($self->{I}->{P}  >= length($e)) {
				$self->{I}->{P} = 0;
				++$self->{I}->{R};
			}
		}
		return $s;
	}
	elsif ($r eq 'CODE') {
		return $stream->($self,@_);
	}
	elsif ($r eq 'SCALAR') {
		return  '' if  $self->{I}->{P} >= length($$stream);
		my $s=substr($$stream,$self->{I}->{P},$n);
		$self->{I}->{P} += $n;
		return $s;
	}
	else {
		croak $r.': type non implemented';
	}
	return undef;	
}


=pod
	legge a righe
	ritorna undef o stringa nulla se si e\' effettuata la lettura oltre la fine
	altrimenti ritorna una riga  terminante con new_line (per compatibilita con l\' operatore <>) 
=cut

sub get_line {
	my $self=shift;
	return <$self> if ref($self) ne  'SIRTI::InputStreamer';

	my $stream=$self->{INPUT_STREAM};
	croak "INPUT_STREAM non definita" unless defined $stream;
	my $r=ref($stream);
	if (wantarray) {
		return <$stream> if $stream eq *STDIN || $r eq 'GLOB'; 	#usa la versione ottimizzata per i files
		my @s=();
		while(my $s=$self->get_line) {
			push @s,$s;
		}
		return @s;
	}

	if ($stream eq *STDIN || $r eq 'GLOB') {
		my $s=<$stream>;
		return $s;
	}
	elsif ($r eq '') { #string
		return undef if $self->{I}->{P} >= length($stream);
		my $s='';
		while($self->{I}->{P} < length($stream)) {
			my $c=substr($stream,$self->{I}->{P}++,1);
			$s .= $c;
			last if $c eq "\n";
		}			
		return $s;
	}
	elsif ($r eq 'ARRAY') {
		confess "internal error" if wantarray;	#impedisce un loop infinito
		return undef if $self->{I}->{R} >= scalar(@$stream);
		return 	$stream->[$self->{I}->{R}++]."\n";
	}
	elsif ($r eq 'CODE') {
		return $stream->($self,@_);
	}
	elsif ($r eq 'SCALAR') {
		return undef if $self->{I}->{P} >= length($$stream);
		my $s='';
		while($self->{I}->{P} < length($$stream)) {
			my $c=substr($$stream,$self->{I}->{P}++,1);
			$s .= $c;
			last if $c eq "\n";
		}			
		return $s;
	}
	else {
		croak $r.': type non implemented';
	}
	return undef;	
}


1;
