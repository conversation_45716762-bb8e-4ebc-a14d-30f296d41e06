package SIRTI::Lock::Fcntln;

use strict;
use warnings;
use integer;
use English '-no_match_vars';
use Carp;
use Fcntl qw(:DEFAULT :flock);
use File::Basename;


sub _open {
	my ($self,$filename,%params)=@_;
	sysopen($self->{FH},$filename,O_RDONLY | O_CREAT) || return undef;
	return $self;
}

sub _get_default_filename {
	my $dir=$ENV{HOME}.'/tmp';
	$dir='/tmp' unless -d $dir;
	return $dir.'/'.basename($0).'.lk';
}

sub _ignore_errors {
	my ($self,%params)=@_;
	return  defined $params{IGNORE_ERRORS} 
		? $params{IGNORE_ERRORS} 
		: $self->{IGNORE_ERRORS};

}

# costruttore 
# parametri 
# FILENAME  - nome di file per lock - se non specificato viene ricavato un default dal nome del programma 
# IGNORE_ERRORS - i metodi (compreso il costruttore) in caso di fallimento ritornano undef altrimenti viene sollevata una eccezione
#

sub new {
	my ($class,%params)=@_;
	my $self=bless \%params,$class;
	my $filename=$params{FILENAME};
	$filename=_get_default_filename unless defined $filename;
	unless ($self->_open($filename)) {
		return undef if $params{IGNORE_ERRORS};
		croak "$filename: open:  $!";
	}
	$self->{FILENAME}=$filename;
	return $self;
}

sub get_filename { return $_[0]->{FILENAME}; }

#  attiva un lock 
#  parametri:
#  NOWAIT  : se il lock non e' possibile impostarlo il metodo esce con undef
#            se questo parametro non e' specificato il metodo e' bloccante
#  IGNORE_ERRORS - in caso di fallimento ritorna undef altrimenti vale l' impostazione nel costruttore

sub lock {
	my ($self,%params)=@_;
	my $flag=LOCK_EX;
	$flag |= LOCK_NB if $params{NOWAIT};
	unless (flock($self->{FH},$flag)) {
		return undef if $params{NOWAIT} || $self->_ignore_errors;
		croak $self->{FILENAME}.": lock: $!";
	}
	return $self;
}

#
# rilascia un lock 
# parametri:
# IGNORE_ERRORS - in caso di fallimento ritorna undef altrimenti vale l' impostazione nel costruttore

sub unlock {
	my ($self,%params)=@_;	
	unless (flock($self->{FH},LOCK_UN)) {
		return undef if  $self->_ignore_errors;
		croak $self->{FILENAME}.": unlock: $!";
	}
	return $self;
}



sub finish {
	my ($self,%params)=@_;
	return $self unless $self->{FH};
	unless (close $self->{FH}) {
		delete $self->{FH};
		return undef if  $self->_ignore_errors;
		croak $self->{FILENAME}.": close: $!";
	}
	delete $self->{FH};
	return $self;
}

sub DESTROY {
	$_[0]->finish;
}

if (__FILE__ eq $0) {
	my $lock=SIRTI::Lock::Fcntln->new;
	print STDERR "uso il file ",$lock->get_filename,"\n";
	$lock->lock;
	print STDERR "entrato nella sez. critica\n";
	my $loop=20;
	while($loop--) {
		print STDERR "$$";
		sleep 1;
	}
	print STDERR "\n";
	$lock->unlock;
	print STDERR "uscito dalla sez. critica\n";
}



1;
