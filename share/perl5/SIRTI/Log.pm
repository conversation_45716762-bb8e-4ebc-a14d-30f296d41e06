package SIRTI::Log;

use strict;
use warnings;

use File::Basename;
use Benchmark;
use Carp;

our @argv;

sub BEGIN{
	@SIRTI::Log::argv=@ARGV;
}

sub new {
	# HANDLE - file-handle su cui scrivere
	# DESCRIPTION - descrizione del blocco da scrivere (ie: il nome dello script)
	# TIME - data di inizializzazione
	# THREADSAFE - se impostato a TRUE implementa un workaround nel metodo DESTROY 
    my $this = shift;
	my %params = @_;
    my $class = ref($this) || $this;
    my %self = ();
	$self{HANDLE} = $params{HANDLE} || *STDERR;
	$self{DESCRIPTION} = $params{DESCRIPTION} || '';
	$self{TIME} = $params{TIME} || time;
	$self{__INIT__} = 0;
	$self{THREADSAFE} = ($params{THREADSAFE} ? 1 : 0);
    return bless \%self, $class;
}

sub getdate {
	my $self = shift;
	my $time = shift || time;
	my ($sec,$min,$hour,$mday,$mon,$year,$wday,$yday,$isdst) = localtime($time);
	return sprintf("%02d-%02d-%04d %02d:%02d:%02d", $mday, ($mon+1), ($year+1900), $hour, $min, $sec);
}

sub init {
	my $self = shift;
	$self->{__INIT__} = 1;
	$self->{TIME0} = Benchmark->new();
	$self->out_scalar('===== BEGIN ====================================================================');
	$self->out_hash('COMMAND' => basename($0).' '.join(" ", map { /\s/ ? "'$_'" : $_ } @argv));
	$self->out_hash('DESCRIPTION' => $self->{DESCRIPTION});
	$self->out_hash('DATE' => $self->getdate($self->{TIME}));
}

sub out_scalar {
	my $self = shift;
	my $s = shift;
	$s = '' unless defined($s);
	my $norc = shift || 0;
	print {$self->{HANDLE}} $s;
	print {$self->{HANDLE}} "\n" unless $norc;
	return 1;
}

sub out_hash {
	my $self = shift;
	my %h = @_;
	while ( my ($key => $value) = each(%h) ) {
		$self->out_scalar($key, 1);
		$self->out_scalar(' : ', 1);
		$self->out_scalar($value);
	}
	return 1;
}

sub out_array {
	my $self = shift;
	my %h = @_ || ();
	while ( my ($key => $value) = each(%h) ) {
		$self->out_scalar($key, 1);
		$self->out_scalar(' : ', 1);
		$self->out_scalar($value);
	}
	return 1;
}

#---------------------------------------------------------------
# OUT_RECORD
# metodi:
# - out_record_columns 
# - out_record_header
# - out_record
#---------------------------------------------------------------

sub out_record_columns{
	
	my ($self, $columns) = @_; 
	
	$self->{OUT_RECORD}->{COLUMNS} = $columns;
	
}

sub out_record_header{
	
	my $self = shift;
	
	foreach (keys %{$self->{OUT_RECORD}->{COLUMNS}} ){
		$self->out_scalar(
			sprintf("%-*.*s"
				,$self->{OUT_RECORD}{COLUMNS}{$_}{MIN} || $self->{OUT_RECORD}{COLUMNS}{$_}{MAX},
				$self->{OUT_RECORD}{COLUMNS}{$_}{MAX} || 25,
				$_)
		,1);
	}
	
	$self->out_scalar();
	
}

sub out_record{
	
	my $self = shift;
	my %h = @_;
	
	foreach (keys %{$self->{OUT_RECORD}->{COLUMNS}}){

		croak "colonna $_ non definita nell'intestazione di out_record"
			unless defined $self->{OUT_RECORD}{COLUMNS}{$_};
	
		$self->out_scalar(	
			sprintf("%-*.*s"
				,$self->{OUT_RECORD}{COLUMNS}{$_}{MIN} || $self->{OUT_RECORD}{COLUMNS}{$_}{MAX},
				$self->{OUT_RECORD}{COLUMNS}{$_}{MAX} || 25,
				defined $h{$_} ? $h{$_} : '')
			,1);	
	}
	
	$self->out_scalar();
		
}


#---------------------------------------------------------------
# out_table_hash
#
# richiede:
# - HEADER 		=> intestazione
# - TABLE 		=> hash chiave/valore
# - PRINT_TOTAL => stampa totale
#---------------------------------------------------------------

sub out_table_hash() {

	my $self = shift;
	my %p = @_;
	
	my $totale = 0;
	
	my ($intestazione, $h, $print_total) = ($p{HEADER}||'', $p{TABLE}, $p{PRINT_TOTAL}||0);
	
	croak ("OUT_STAT: richiesto parametro intestazione di tipo HASHREF")
		unless defined $h && ref $h eq 'HASH';

    if ( keys %$h ) {
        $self->out_scalar( sprintf "+%30s+%30s+%9s+",
            '-' x 30, '-' x 30, '-' x 9 );
        $self->out_scalar( sprintf "|%30s|%30s|%9s|", $intestazione, '', '' );
        $self->out_scalar( sprintf "+%30s+%30s+%9s+",
            '-' x 30, '-' x 30, '-' x 9 );
    }

    for ( sort keys %$h ) {

        $self->out_scalar( sprintf "|%30s|%30s|%9d|", '', $_, $h->{$_}, );
        
        $totale += $h->{$_};

        $self->out_scalar( sprintf "+%30s+%30s+%9s+",
            '-' x 30, '-' x 30, '-' x 9 );
    }
    
    if ( $print_total ) {
        $self->out_scalar( sprintf "|%30s|%30s|%9s|", 'TOTALE', '', $totale );
        $self->out_scalar( sprintf "+%30s+%30s+%9s+",
            '-' x 30, '-' x 30, '-' x 9 );
    }

    $self->out_scalar();

}

sub out_line() {
	my $self = shift;
    $self->out_scalar('-'x80);
}

sub out_section($$) {
	my ($self, $section) = @_;
    $self->out_scalar();
    $self->out_line();
    $self->out_scalar($section);
    $self->out_line();
    $self->out_scalar();
}

sub out_benchmark {
	my $self = shift;
	if ( $self->{__INIT__} ) {
	    $self->out_hash("BENCHMARK" => timestr(timediff(Benchmark->new(), $self->{TIME0})));
	}
	return undef;
}

sub DESTROY {
	my $self = shift;
	if ( $self->{__INIT__}  &&  (!$self->{THREADSAFE} || defined $self->{TIME0}) ) {
		$self->out_scalar('===== END ======================================================================');
		$self->out_scalar();
	}
	return undef;
}

############################################################################
# UNIT TEST
############################################################################

if ( __FILE__ eq $0 ) {
	
	use Tie::IxHash;

	##### Inizializzazione LOG
	my $log = SIRTI::Log->new(
    	HANDLE      => *STDOUT,
    	DESCRIPTION => basename($0),
    	TIME        => time
	);
	
	$log->init();
	
	$log->out_section('OUT_SECTION');
	
	$log->out_scalar('TEST OUT SCALAR');
	
	$log->out_hash('OUT HASH'=> 'TEST');
	
	$log->out_line();
	
	$log->out_scalar();
	
	# out_record ----------------------------------------------------------
	tie my %record_cols, 'Tie::IxHash',
	(
		ID_ORDINE 	=> {MAX=> 20},
		STATO		=> {MAX=> 25},
		ESITO		=> {MAX=> 10},
	); 
	
	$log->out_section('OUT_RECORD');
	$log->out_record_columns(\%record_cols);
	
	$log->out_record_header();
	$log->out_record( ID_ORDINE=>4815, 	STATO=>'CHIUSA', ESITO=>'OK');
	$log->out_record( ID_ORDINE=>16, 	STATO=>'APERTA', ESITO=>'KO');
	$log->out_record( ID_ORDINE=>2342, 	STATO=>'RESPINTO', ESITO=>'OK');
	$log->out_scalar();
	# fine out_record -----------------------------------------------------
	
	tie my %stat, 'Tie::IxHash',(
		APERTA=>4815,
		RESPINTO=>16,
		CHIUSA=>2342,
	);
	
	$log->out_table_hash(
		HEADER=>'TEST OUT_TABLE_HASH',
		TABLE=>\%stat,
		PRINT_TOTAL=>1,
	);
	
	$log->out_scalar();
	
	$log->out_benchmark();

}

1;
