package SIRTI::Log::Log4perl::Logger;
use base qw(Log::Log4perl::Logger);

sub _adjmsg {
	my $self=shift;
	return split("\n",join('',@_));
}

sub debug {
	my $self=shift;
	for($self->_adjmsg(@_)) {
		$self->SUPER::debug($_);
	}
}

sub info {
	my $self=shift;
	for($self->_adjmsg(@_)) {
		$self->SUPER::info($_);
	}
}

sub warn {
	my $self=shift;
	for($self->_adjmsg(@_)) {
		$self->SUPER::warn($_);
	}
}

sub error {
	my $self=shift;
	for($self->_adjmsg(@_)) {
		$self->SUPER::error($_);
	}
}

sub fatal {
	my $self=shift;
	for($self->_adjmsg(@_)) {
		$self->SUPER::fatal($_);
	}
}

package SIRTI::Log::Log4perl;

#
#  wrapper su log4perl 
#
#  easy non e' implementato 
#

use Log::Log4perl;
use base qw(Exporter);

use constant {
	DEFAULT_ERROR_EXIT_CODE 	=> 200
	,DEFAULT_CONF_PATHNAME  	=> $ENV{HOME}.'/etc/log4perl.conf'
	,LOGGER_CLASS				=> 'SIRTI::Log::Log4perl::Logger'
};


my %t=( 
					'constant' => [ qw ( 
										DEFAULT_ERROR_EXIT_CODE 
										DEFAULT_CONF_PATHNAME     
									) ] 
					,'init'	=> [ qw (
										init
									)
								]
					,'get_logger' => [
									qw (
											get_logger
									)
								]
);

our %EXPORT_TAGS=( all => [ map { @{$t{$_}} } keys %t ],%t); 
our @EXPORT_OK=( @{$EXPORT_TAGS{all}} );
our @EXPORT=();

sub _error {
	my ($msg,%params)=@_;
	print STDERR "$msg";
	if ($params{NOT_EXIT_ON_ERROR}) {
		return defined $params{CATEGORY} ? undef : 0;
	}
	$params{ERROR_EXIT_CODE}=DEFAULT_ERROR_EXIT_CODE unless defined  $params{ERROR_EXIT_CODE};
	if (defined $ENV{MOD_PERL}) {
		eval { CORE::exit; };
	}
	else {
		eval {exit $params{ERROR_EXIT_CODE};};
	}
}

#parametri:
#	CONF_PATHNAME - ricerca il file di configurazione 
#                    tenta con: 
#									il valore di questo parametro se definito
#                    				il valore della variabile d' ambiente LOG4PERL_CONF
#                    				il valore di default {HOME}/etc/log4perl.conf'
#									in caso di nessun  file trovato si ha un errore
#	
#   WATCH_TIME  -  imposta l' allarme per la rilettura del file di configurazione 
#                  per default nessuno
#   NOT_EXIT_ON_ERROR - in caso di errore nonn esce ma ritorna 0 
#                       a meno che non sia impostato il parametro 	CATEGORY, in tal caso ritorna undef
#                       se il valore e' falso viene eseguita una exit per il valore del parametro ERROR_EXIT_CODE
#                       (che ha per default la costante DEFAULT_ERROR_EXIT_CODE) 
#
#   CATEGORY    - se impostato ritorna una oggetto logger impostato alla categoria CATEGORY
#                  in caso di errore e con vero NOT_EXIT_ON_ERROR viene ritornato undef
#   ERROR_EXIT_CODE - codice d' errore di uscita di init 
#					per default DEFAULT_ERROR_EXIT_CODE


sub init {
	my %params=@_;
	$params{CONF_PATHNAME}=$ENV{LOG4PERL_CONF} unless defined $params{CONF_PATHNAME};
	$params{CONF_PATHNAME}=DEFAULT_CONF_PATHNAME  unless defined $params{CONF_PATHNAME};
	return _error($params{CONF_PATHNAME}.": file di configurazione per log4perl non esistente o non leggibile\n",%params) 
		unless -r  $params{CONF_PATHNAME};
	
	if (defined $params{WATCH_TIME}) {
		return _error($params{WATCH_TIME}.": valore parametro WATCH_TIME non valido - deve essere un numero da 1 a 8 cifre\n",%params) 
			unless $params{WATCH_TIME}=~/^\d{1,8}$/;
	}

	local $@;
	eval {
		if (defined $params{WATCH_TIME}) {
			 Log::Log4perl::init_and_watch($params{CONF_PATHNAME},$params{WATCH_TIME});
		}
		else {
			 Log::Log4perl::init($params{CONF_PATHNAME});
		}
	};

	
	return 	_error("errore inizializzazione log4perl - $@\n",%params) if $@;

	Log::Log4perl->wrapper_register(LOGGER_CLASS);

	return get_logger($params{CATEGORY}) if defined $params{CATEGORY};

	return 1;
}

sub get_logger { return bless Log::Log4perl->get_logger(@_),LOGGER_CLASS; }



if ($0 eq __FILE__) {
	my $logger=init(
		CATEGORY	=> 'TLCCRMWS.APP.SCRIPT.testlogger'
	);
	print ref($logger),"\n";
	$logger->fatal("this is an fatal message\nhhh","jjj\n");



}


1;


