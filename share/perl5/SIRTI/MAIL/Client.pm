################################################################################
#
# SIRTI::MAIL::Client
#
################################################################################

=head1 NAME

B<SIRTI::MAIL::Client> - Package per l'invio centralizzato di MAIL

=head1 SYNOPSIS

  my $mail = SIRTI::MAIL::Client->new(
    SENDER_MAIL	=> '<EMAIL>', # Optional
  ) || die;
  my $status = $sms->send(
    SENDER_MAIL       => '<EMAIL>',     
    SUBJECT           => 'subject',         
    RECIPIENT_MAIL    => '<EMAIL>',
    RECIPIENT_MAIL_CC => 'cc1,cc2,cc3 ...',
    RECIPIENT_MAIL_BC => 'bc1,bc2,bc3 ...', 
    BODY              => 'This is an MAIL',
    ATTACH            => [
                            #attach text example (see MIME::Lite)
                            { 
                              Type     =>'TEXT',
                              Data     =>"Here's the GIF file you wanted"
                            },

                            #attach file example (see MIME::Lite)
                            { 
                              Type        => 'image/gif',
                              Path        => 'aaa000123.gif',
                              Filename    => 'logo.gif',
                              Disposition => 'attachment'
                            }
                         ]
  );
  if ( $status == ERR_OK ) {
	  print "MAIL sent!", "\n";
  } else {
	  print "MAIL not sent: ", $sms->last_error(), "\n";
  }

=head1 DESCRIPTION

Questo package rappresenta l'interfaccia I<client> verso il proxy SMS di Sirti.

=cut

package SIRTI::MAIL::Client;

use strict;
use warnings;
use Carp qw{verbose croak};
use Text::Iconv;
use JSON;
use MIME::Lite;

use constant {
	 ERR_OK				=> 1
	,ERR_KO				=> 0

	,ERR_BAD_PARAMETERS		=> -1001
	,ERR_POST			=> -1002
	,ERR_RESPONSE			=> -1003
};

use base 'SIRTI::Base';


################################################################################
#  P R O P R I E T A '   P U B B L I C H E
################################################################################

our $VERSION = '0.01';


################################################################################
#  M E T O D I   P R I V A T I
################################################################################

sub _test { $_[0]->{__TEST__} }

sub _debug { $_[0]->{__DEBUG__} }

sub _warn {
	my $self = shift;
	my $msgid = shift || '';
	my $msg = shift || '';
	print STDERR "$msgid $msg", "\n";
	return 1;
}

sub _is_utf8 {
	my ($self, $s) = @_;
	my $iconv = Text::Iconv->new('utf-8', 'utf-8');
	my $dummy = $iconv->convert($s);
	my $retval = $iconv->retval();
	undef $iconv;
	return undef unless defined($retval);
	return ( $retval == 0 ? 1 : 0 );
}

sub _to_utf8 {
	my ($self, $s) = @_;
	my $iconv = Text::Iconv->new('iso-8859-15', 'utf-8');
	return $iconv->convert($s);
}

sub _send {
    my $self = shift;
    my %params = @_;
    my $errmsg = '';

    #il controllo dei parametri viene fatto nella funzione chiamante (send())
       
    if( defined $params{BODY} ) {

         # Converto il corpo del messaggio in UTF-8
         unless ( $self->_is_utf8($params{BODY}) ) {
      	          $self->_warn( "INPUT....: '$params{BODY}' is not utf8..." );
		  $params{BODY} = $self->_to_utf8($params{BODY});
		  $self->_warn( "OUTPUT...:'$params{BODY}'" );
		  $self->_warn( "is utf8? ", $self->_is_utf8($params{BODY}) );
	 }
    }

    # Invio la richiesta al server se non sono in modalità TEST
    unless ($self->{__TEST__}) {
                  my %args = ( );
                  my %paname = (
                        SENDER_MAIL       => 'From',
                        SUBJECT           => 'Subject',
                        RECIPIENT_MAIL    => 'To',
                        RECIPIENT_MAIL_CC => 'Cc',
                        RECIPIENT_MAIL_BC => 'Bcc',
                        BODY              => 'Data'
                  );

                  for( keys %paname ) { 
                       $args{$paname{$_}} = $params{$_} if exists $params{$_};
                  }

                  
                  $args{SENDER_MAIL} = $self->get_sender_mail() 
                            if !defined $args{SENDER_MAIL} && defined $self->get_sender_mail();
                  

	  	  my $msg = MIME::Lite->new( %args );

                  if( ref($params{ATTACH}) eq 'ARRAY'  ) {
                      for( @{$params{ATTACH}} ) {
                         $msg->attach( %{$_} );
                      }
                }

		$msg->send();
	}

	return 1;
}




################################################################################
#  M E T O D I   P U B L I C I
################################################################################

=pod

=head1 METHODS

=over 4

=item new( I<SENDER_TEXT>, [I<SERVICENAME>, I<TEST>, I<DEBUG>] )

Costruttore di classe.

Parametri:

=over 8

=item * SENDER_TEXT : I<SCALAR> che rappresenta l'iIDentificativo che verra' usato come B<mittente> degli SMS

=item * TEST : I<SCALAR> che può valere 0|1 (default=0): se impostato ad 1 non invia l'SMS (non viene inserita
la riga in SMS_BUFFR)

=item * DEBUG : I<SCALAR> che può valere 0|1 (default=0): se impostato ad 1 emette informazioni di debug

=item * SERVICENAME : I<SCALAR> che rappresenta il nome del servizio (istanza ART o altro) abilitata all'invio
di SMS; se omesso sara' necessario specificare tale informazione in fase di invio dell'SMS

=back

=cut
sub new {
    my $this = shift;
    my $class = ref($this) || $this;
    my %params = @_;
    my $self = bless( {}, $class );
    my $errmsg = '';
	croak $errmsg
		unless $self->check_named_params(
			 ERRMSG	=> \$errmsg
			,PARAMS	=> \%params
			,MANDATORY	=> {
			
			}
			,OPTIONAL	=> {
				 TEST		    => { isa => 'SCALAR' , list => [0, 1] }
				,SENDER_MAIL	    => { isa =>	'SCALAR' }
				,DEBUG		    => { isa => 'SCALAR' , list => [0, 1] }
				,SUBJECT	    => { isa =>	'SCALAR' }
				,SENDER_MAIL	    => { isa =>	'SCALAR' }
			}
		);

	# Impostazione proprietà mediante metodi pubblici
	$self->set_sender_mail( $params{SENDER_MAIL} )
                  if defined $params{SENDER_MAIL};

	# Impostazione proprietà private
	$self->{__SUBJECT__}	= $params{SUBJECT} || '';
	$self->{__TEST__}		= ( defined($params{TEST}) ? $params{TEST} : 0 );
	$self->{__DEBUG__}		= ( defined($params{DEBUG}) ? $params{DEBUG} : 0 );
	$self->{__RESPONSE__} = undef;

	return $self;
}


=pod

=item response()

Ritorna una struttura dati che rappresenta la response JSON ricevuta dal proxy.

=cut
sub get_response { $_[0]->{__RESPONSE__} }


=pod

=item get_sender_text()

Ritorna uno I<SCALAR> con l'iIDentificativo attualmente impostato come B<mittente> degli SMS

=cut
sub get_sender_mail { $_[0]->{SENDER_MAIL} }

=pod

=item set_sender_text( I<SCALAR> )

Imposta (o reimposta) l'iIDentificativo che verra' usato come B<mittente> degli SMS

=cut
sub set_sender_mail {
   my $self = shift;
   my $sender_mail = shift;
   my $errmsg = '';
	croak $errmsg
		unless $self->check_named_params(
			 ERRMSG		=> \$errmsg
			,PARAMS		=> { SENDER_MAIL => $sender_mail }
			,MANDATORY	=> {
				SENDER_MAIL	=> { isa =>	'SCALAR' }
			}
			,OPTIONAL	=> {
			}
		);
	$self->{SENDER_MAIL} = $sender_mail;
}


=pod

=item send( I<BODY>, I<RECIPIENT_NUMBER>, [ I<SENDER_TEXT> ] )

Invia al PROXY una richiesta di invio di un SMS.

Parametri:

=over 8

=item * BODY : I<SCALAR> che rappresenta il testo del SMS da inviare

=item * RECIPIENT_NUMBER : I<SCALAR> che indica il numero del destinatario del messaggio

=item * SENDER_TEXT : I<SCALAR> rappresenta il mittente del SMS; se omesso verra'
utilizzato il SENDER_TEXT impostato nella creazione dell'oggetto

=item * SERVICENAME : I<SCALAR> che rappresenta il nome del servizio (istanza ART o altro) abilitata all'invio
di SMS; se omesso verra' utilizzato (se indicato) il valore specificato nella creazione dell'oggetto.
B<ATTENZIONE! Nel caso in cui non venisse specificato ne' nel metodo new() ne' nel metodo send(), l'invio
dell'SMS fallira' (nel metodo privato _send())>

=back

B<send()> ritorna I<1> se la richiesta e' stata presa in carico correttamente dal proxy. In caso di errore
viene ritornato I<0> e viene impostato B<last_error()>

=cut
sub send {
    my $self = shift;
    my %params = @_;
    my $errmsg = '';

	$self->clear_error();
	$self->{__RESPONSE__} = undef;
	$self->last_error(ERR_BAD_PARAMETERS, $errmsg)
		&& return ERR_BAD_PARAMETERS
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {

				}
				,OPTIONAL	=> {
			  	     SENDER_MAIL       => { isa => 'SCALAR' }
                                    ,BODY              => { isa => 'SCALAR' }
				    ,SUBJECT           => { isa => 'SCALAR' }
                                    ,RECIPIENT_MAIL    => { isa => 'SCALAR' }
                                    ,RECIPIENT_MAIL_CC => { isa => 'SCALAR' }
                                    ,RECIPIENT_MAIL_BC => { isa => 'SCALAR' }
                                    ,ATTACH            => { isa => 'ARRAY'  }
				}
			);

        if( !defined $params{RECIPIENT_MAIL}    &&
            !defined $params{RECIPIENT_MAIL_CC} &&
            !defined $params{RECIPIENT_MAIL_BC}     ) {

            $self->last_error( ERR_BAD_PARAMETERS, 'Manca un destinatario' );
            return ERR_KO;
        }

	# Invio mail
	return $self->_send( %params );
}


1;

