package SIRTI::MQ::Notify::DataHandler;



my $DEFAULT_NOTIFY_TYPE_KEY   = "__NOTIFY_MESSAGE__";


sub new {
    my $this = shift;
    my $class = ref($this) || $this;
    return bless {},$class;
}

# 
# nome della chiave che segnala all'interno di un messaggio (hash)
# la richiesta di notifica e il tipo di messaggio da notificare 
# (MAIL, SMS, ecc...)
#
# @Override
#
sub _notify_type_key   { return $DEFAULT_NOTIFY_TYPE_KEY  }



#
# argomenti: hashref 
# verifica se il messaggio contiene una richiesta di notifica
# (se e' presente la chiave $self->_notify_type_key )
#
sub has_notify_type_key {
    my( $self, $message ) = @_;

    return 0 if ref($message) ne 'HASH';
    return exists $message->{ $self->_notify_type_key };
}

#
# argomenti: -
# ritorna il nome della chiave utilizzata per segnalare
# una richiesta di notifica
#
sub get_notify_type_keyname {
    my( $self ) = shift;
    return $self->_notify_type_key;
}

#
# argomenti: hashref
# ritorna se presente il valore della chiave di notifica
# che esprime il tipo messaggio da notificare. (MAIL, SMS, ecc..)
# undef e' ritenuto un tipo di notifica valido.
#
sub get_notify_type {
    my( $self, $message) = @_;
    return undef if ref($message) ne 'HASH';
    return $message->{ $self->_notify_type_key };
}

#
# metodo utilizzato dal client per inviare al consumer
# una richiesta di invio messaggio allegando anche quella
# di invio notifica con relativi dati supplementari
#
# agomenti: $senderParams, $outDataMessage
#
#    $senderParams (hashref) 
#         rappresenta l'elenco dei normali parametri che 
#         caratterizzano il messaggio da spedire.
#
#    $outDataMessage (hashref)
#         rappresenta il messaggio da restituire al client, 
#         aggiornato con le chiavi di notifica
#
#
sub encode_notify_data {    
    my ($self, $sendArguments, $outDataMessage ) = @_;
    return;    
}


#
# metodo utilizzato dal consumer di messaggi, per formattare
# il messaggio di inoltro notifica da spedire al consumer notifiche.
#
# argomenti: $inData, $extraData, $outData
# 
#    $inData (hashref) 
#        rappresenta il messaggio ricevuto dal client
#
#    $extraData (hashref)
#        parametri opzionali da aggiungere alla notifica
#
#    $outData (hashref)
#        hash chiave/valore destinato a conterene 
#        la codifica del messaggio per il consumer di notifiche
#
# @Override
#
sub encode_notify_request {
    my ($self, $inData, $extraData, $outData ) = @_;
    return;
}


#
# metodo utilizzato dal consumer di notifiche per ottenere 
# i dati della richiesta ed eventuali dati extra
#
# argomenti: $inData, $extraData, $outData
# 
#    $inData (hashref) 
#        rappresenta il messaggio ricevuto dal consumer messaggio 
#
#    $extraData (hashref)
#        hash destinato a contenere 
#        parametri opzionali da aggiungere alla notifica
#
#    $outData (hashref)
#        hash chiave/valore destinato a conterene 
#        la codifica del messaggio per il consumer di notifiche
#
# @Override
#
sub decode_notify_request {
    my ($self, $inData, $extraData, $outData ) = @_;

    if( ref($extraData) eq 'HASH' ) {
        %$extraData = ( );
        for( keys %$inData ) {
             next if ! /^__/;
             my $val = $inData->{$_};
             s/^__//;
             $extraData->{$_} = $val; 
        }
    }

    if( ref($outData) eq 'HASH' ) {
        %$outData = ( );
        for( keys %$inData ) {
             next if /^__/;
             $outData->{$_} = $inData->{$_};
        }
    }
    return;
}

1;

