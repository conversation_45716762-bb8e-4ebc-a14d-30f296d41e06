package SIRTI::MQ::Notify::MailDataHandler;


use base qw(SIRTI::MQ::Notify::DataHandler);


sub new {
    my $this  = shift;
    my $class = ref($this) || $this;

    return bless { }, $class; 
}



# utilizzato dal client per inviare richieste di inoltro
# messaggio (EMAIL) con relativa notifica
# il metodo aggiorna il messaggio (hash) $outData.
# effettuando un merge con il contenuto di $notifyInfo (hash)
# aggiungendo la chiave $self->_notify_type_key per segnalare
# la richiesta di notifica.
#
# @Override
#
sub encode_notify_data {
    my ($self, $notifyInfo, $outData) = @_;

    return 0 if ref($notifyInfo) ne 'HASH';

    for( keys %{$notifyInfo} ) {
         $outData->{$_} = $notifyInfo->{$_};
    }

    $outData->{ $self->_notify_type_key } = 'MAIL';
    return 1;
}

#
# utilizzato dal consumer per inviare il messaggio di
# notifica (hash) al notifier. (Vedi classe base)
#
# @Override
#
sub encode_notify_request {
    my ($self, $inData, $extraData, $outData ) = @_;

    %$outData = ( );

    #TODO:FIXME completare

    #SENDER_MAIL
    #RECIPIENT_MAIL
    #SUBJECT
    #BODY

    ## dati mittente

    $outData->{NOTIFY_TYPE}     = 'MAIL'; 
    $outData->{NOTIFY_CODE}     = $inData->{NOTIFY_CODE};
    $outData->{NOTIFY_SERVICE}  = $inData->{NOTIFY_SERVICE};
    $outData->{ID_ANAGRAFICA}   = $inData->{ID_ANAGRAFICA};
    $outData->{ACTIVITY_TYPE}   = $inData->{ACTIVITY_TYPE};
    $outData->{ACTIVITY_ID}     = $inData->{ACTIVITY_ID};
    $outData->{ORDER_ID}        = $inData->{ID_ORDINE};
    $outData->{CUSTOMER_NUMBER} = $inData->{CUSTOMER_NUMBER};
    
    ## dati messaggio

    ##$outData->{TELEPHONE_NUMBER} = $inData->{RECIPIENT_NUMBER};
    $outData->{SEND_DATE}        = $inData->{SEND_DATE};
    ## $outData->{ADDRESS}       = $inData->{ADDRESS};
    $outData->{EMAIL_ADDRESS} = $inData->{RECIPIENT_MAIL};
 

    if( ref($extraData) eq 'HASH' ) {
        for( keys %$extraData ) {
             $outData->{"__" . $_ } = $extraData->{$_};
        }
    }

    return 1;
}

1;

