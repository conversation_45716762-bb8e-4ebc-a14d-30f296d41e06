################################################################################
#
# SIRTI::::MQ::Notify::RequestConsumer
#
################################################################################
=pod

=head1 NAME

B<SIRTI::MQ::Notify::Consumer> - Consumer che registra su DB le notifiche inviate al cliente

=head1 SYNOPSIS

ramq <consume_config_file> <options>

=head1 DESCRIPTION

 
=cut

package SIRTI::MQ::Notify::RequestConsumer;

use strict;
use warnings;
use Carp;
use Scalar::Util qw(blessed);
use Log::Log4perl qw(get_logger :levels :nowarn);

use API::ART;
use SIRTI::Queue::Response;

use SIRTI::ART::RemoteActivity::Source;
use SIRTI::MQ::Notify::RequestDataHandler;

use base 'SIRTI::Queue::EventConsumer';


our $VERSION = '0.1';

=pod

=head1 METHODS

=cut

sub _notify_data_handler {
    return shift->{_NOTIFY_DATA_HANDLER};
}

sub _notify_db_table_name {
    return shift->{_NOTIFY_DB_TABLE_NAME};
}

sub _notify_db_schema_name {
    return shift->{_NOTIFY_DB_SCHEMA_NAME};
}

sub _notify_db_table_columns {
    return shift->{_NOTIFY_DB_TABLE_COLUMNS};
}

sub _notify_db_sequence_name {
    return shift->{_NOTIFY_DB_SEQUENCE_NAME};
}

sub _ra_event_name {
    return shift->{_NOTIFY_RA_EVENT_NAME};
}

=pod

=head2 init( )

Questo metodo viene invocato da C<ramq> subito dopo aver istanziato l'oggetto consumer. B<Può> essere sovrascritto
per effettuare delle routine di inizializzazione del consumer.

Da notare che questo è il punto giusto dove creare dei singleton che potranno essere mantenuti nella sessione tramite
la definizione di una chiave dell'oggetto. Attenzione: al fine di evitare la sovrascrittura accidentale delle chiavi
utilizzate dalla classe base C<SIRTI::Queue::EventConsumer>, non utilizzare chiavi che iniziano per doppio underscore
(C<__>).

=cut

#
# optionally override
#
sub init {
    my $self = shift;
    my $workContext = $self->work_context();


    croak "Nome evento RA mancante (parametro WORK_CONTEXT->NOTIFY_RA_EVENT_NAME)"
              if !defined $workContext->{NOTIFY_RA_EVENT_NAME};

    croak "Nome tabella di notifica mancante (parametro WORK_CONTEXT->NOTIFY_DB_TABLE_NAME)"
              if !defined $workContext->{NOTIFY_DB_TABLE_NAME};


    $self->{_NOTIFY_DATA_HANDLER} = SIRTI::MQ::Notify::RequestDataHandler->new( );

    my $tabName = $workContext->{NOTIFY_DB_TABLE_NAME};
    my ($schema,$tabella) = $tabName =~ /^\s*([^\.]+)\.(.+)$/;

    do {{
          if( defined $schema && defined $tabella) {
              $schema  =~ s/\s+$//;
              $tabella =~ s/^\s+//;
              $tabella =~ s/\s+$//;
              last if $schema ne '' and $tabella ne '';
          }
          croak "Nome della tabella di notifica '$tabName' non valido (atteso formato  nome_schema.nome_tabella)";
    }};
        
    croak "Nome sequenza id notifica mancate (parametro WORK_CONTEXT->NOTIFY_DB_SEQUENCE_NAME)"
              if !defined $workContext->{NOTIFY_DB_SEQUENCE_NAME};

    $self->{_NOTIFY_RA_EVENT_NAME} = $workContext->{NOTIFY_RA_EVENT_NAME};

    $self->{_NOTIFY_DB_TABLE_NAME} = $tabella;

    $self->{_NOTIFY_DB_SCHEMA_NAME} = $schema;

    $self->{_NOTIFY_DB_SEQUENCE_NAME} = $workContext->{NOTIFY_DB_SEQUENCE_NAME}; 
       
    my $db = $self->db;
    my $notifyDbTable  = $self->_notify_db_table_name;
    my $notifyDbSchema = $self->_notify_db_schema_name;

    my $columns;
    my $stmt;

    eval { 
       $stmt = $db->create_prepare( 
                     qq{
                          SELECT column_name, data_type, data_length
                            FROM all_tab_columns
                           WHERE table_name = ? and owner = ?
                     }
       );

       $columns = $stmt->fetchall_hashref( $notifyDbTable, $notifyDbSchema );
    };
    my $except = $@;
    if( defined $stmt ) {
        eval { local $@; $stmt->finish ; $stmt = undef };
    }

    if( $except ) {
        die "Errore leggendo l'elenco delle colonne della tabella $notifyDbTable: $except"; 
    }

    if( !defined $columns || scalar(@$columns) < 1 ) {
        die "Impossibile ottenere la struttura della tabella $notifyDbTable: " 
            . ($db->get_errormessage()||'?');
    }

    my $tabCols = { 
                 map { 
                     $_->{COLUMN_NAME} => { 
                            DATA_TYPE   => uc($_->{DATA_TYPE}), 
                            DATA_LENGTH => $_->{DATA_LENGTH}
                     }  
                 } @$columns
    }; 

    $self->{_NOTIFY_DB_TABLE_COLUMNS_ORDER} = $columns;
    $self->{_NOTIFY_DB_TABLE_COLUMNS} = $tabCols;

    $self->logger->info( );    
    $self->logger->info("Colonne tabella di notifica '$notifyDbTable':");

    for( @$columns ) {
         $self->logger->info(
                   "   <> " 
                 . sprintf( '%-24s %-16s', $_->{COLUMN_NAME}, $_->{DATA_TYPE} )
         );
    }    
    $self->logger->info( );
    return;
}

=pod

=head2 get_managed_event_types( )

Questo metodo B<DEVE> essere sovrascritto e ritornare un arrayref con l'elenco dei tipi di eventi che devono essere passati
al consumer. Nel caso in cui non debba essere applicato alcun filtro sugli eventi della coda, deve ritornare una
reference a un array vuoto.

=cut

#
# override
#
sub get_managed_event_types { [ shift->_ra_event_name ] };

=pod

=head2 get_managed_source_refs( )

Questo metodo B<DEVE> essere sovrascritto e ritornare un arrayref con l'elenco dei SOURCE_REF che devono essere passati
al consumer. Nel caso in cui non debba essere applicato alcun filtro sui SOURCE_REF, deve ritornare una
reference a un array vuoto.

=cut

#
# override
#
sub get_managed_source_refs { [ ] }

=pod

=head2 before_consuming_events( NUM_EVENTI => C<SCALAR> )

=over

=item * B<NUM_EVENTI> : numero di eventi pending trovati che verranno inviati uno per volta alla funzione
C<consume_event()>

=back


Questo metodo viene invocato dopo aver recuperato gli eventi pending e prima di iniziare a inviarli uno alla volta al
consumer per implementare delle logiche di apertura del blocco delle lavorazioni.

Questo metodo B<può> essere sovrascritto.

=cut

#
# optionally override
#
sub before_consuming_events {
	return;
}

=pod

=head2 consume_event( EVENT => C<SIRTI::Queue::Event> )

=over

=item * B<EVENT> : oggetto di tipo C<SIRTI::Queue::Event>

=back


Questo metodo, che B<DEVE> essere sovrascritto, viene invocato una volta per ciascun evento che viene recuperato
dalla coda.

Deve ritornare un oggetto l'esito del metodo di utilità C<consume()> (es. C<return consume( ... )>)
per indicare a C<ramq> che la remote activity deve essere marcata come lavorata, oppure l'esito del metodo di utilità
C<skip()> (es. C<return skip( ... )>) per indicare a C<ramq> che la remote activity non deve essere marcata come
lavorata, e quindi riproposta al prossimo giro. Può inoltre lanciare una eccezione in caso di problemi fatali per
comunicare C<ramq> di interrompere le elaborazioni.

L'oggetto di tipo C<SIRTI::Queue::Event> espone i seguenti metodi:

=over

=item * get_event_name() : ritorna il tipo di evento che dobbiamo elaborare

=item * get_source_ref() : ritorna un riferimento (univoco o meno) utile al destinatario del 
messaggio per identificare l'oggetto del messaggio

=item * get_info() : ritorna un hashref con le info relative all'evento

=item * get_data() : ritorna un hashref con i dati associati all'evento
 
=back


=cut

#
# override
#
sub consume_event {
    my $self = shift;
    my %params = @_;
    my $event = $params{EVENT};


    $self->logger()->info( "Elaboro notifica: " . $event->get_event_name() );
    
    #
    # non utilizzato, e' sufficiente eseguire il 
    # consumer in ROLLBACK mode (ramq -t r)
    #
    my $RUN_TEST = $params{RUN_TEST};

    my $eventData = $event->get_data();

    if( !defined $eventData ) {
         $self->logger->fatal( "informazioni di notifica mancanti" );
         return $self->consume( 
                     REASON => 'Informazioni di notifica mancanti',
                     STATUS => 1,
                     TARGET_REF => 'KO' 
         );
    }
    
    $self->logger()->trace( "Event data:" );
    $self->logger()->trace( "----------------" );
    for( keys %$eventData ) {
         $self->logger()->trace( "'$_' = " . "\"" . format_string( $eventData->{$_}) . "\"" ); 
    }
    $self->logger()->trace( "----------------" );

    my $error = $self->store_notify( $eventData );

    if( defined $error ) {
        $self->logger()->error( "Errore registrando la notifica su DB: '" . $error . "'" );
    }
    else {
        $self->logger()->trace( "Notifica registrata correttamente su DB" );
    }

    return $self->consume(
                 defined $error ?
                   (
                      REASON     => $error,
                      STATUS     => 1,
                      TARGET_REF => 'KO' 
                   )
                 :
                   ( 
                      REASON     => 'Notifica registrata correttamente',
                      STATUS     => 0,
                      TARGET_REF => 'OK'
                   )
    );
}


sub store_notify {
    my ($self, $eventData) = @_;
    my ($extraData, $outData) = ( { }, { } );
   
    my $notifyDataHandler    = $self->_notify_data_handler( );
    my $notifyDbTable        = $self->_notify_db_table_name( );
    my $notifyDbSchema       = $self->_notify_db_schema_name( );
    my $notifyDbTableColumns = $self->_notify_db_table_columns( );
    my $notifyDbSequenceName = $self->_notify_db_sequence_name;


    $notifyDataHandler->decode_notify_request( $eventData, $extraData, $outData );

    my @columns = grep { 

         ( uc($_) ne 'NOTIFY_ID') && exists $notifyDbTableColumns->{uc($_)}

    } keys %$outData;


    if( !scalar(@columns) ) {
        if( scalar(keys %$outData) ) {
            return "nessuna corrispondenza tra nomi dei RA data ricevuti e quelli delle colonne della tabella di notifica...";        }
        else {
            return "informazioni di notifica (RA data) mancanti";
        }
    }


    my $qtext = "insert into ${notifyDbSchema}.${notifyDbTable}" 
               . "("
               .     join( ',', ( "NOTIFY_ID" , @columns ) ) 
               . ")"
               . "values" 
               . "("
               .     "${notifyDbSequenceName}.nextval," 
               .     join( ',', map {
                                      if( defined $outData->{$_} && $notifyDbTableColumns->{uc $_}->{DATA_TYPE} eq 'DATE' ) {
                                          my ($val,$format) = ($outData->{$_}||'') =~ /^([^\#]+)\#(.+)$/;
                                          if( defined $format ) {
                                              qq{ to_date( ?, '$format' ) }
                                          }
                                          else {
                                             '?';
                                          }
                                      }
                                      else {
                                          '?';
                                      }
                                } @columns )
               . ")";


    $self->logger->trace( "Eseguo SQL insert: $qtext" );

    my $qstmt = undef;

    eval {
       $qstmt = $self->db()->create_prepare( $qtext );

       if( !defined $qstmt ) {
           die "prepare SQL insert non riuscito: " . ($self->db()->get_errormessage||'last_error undef ?');
       }

       my $res = $qstmt->do( 
                 map {
                        if( defined $outData->{$_} && $notifyDbTableColumns->{uc $_}->{DATA_TYPE} eq 'DATE' ) {
                            my ($val,$format) = $outData->{$_} =~ /^([^\#]+)\#(.+)$/;
                            $val;
                        }
                        else {
                            $outData->{$_};
                        }                          
                 } @columns 
       );
   
       die "SQL insert non riuscito: " . ($self->db()->get_errormessage)
          if $res ne '1';
    };
    my $except = $@;
    if( defined $qstmt ) {
        eval { local $@; $qstmt->finish; $qstmt = undef; }
    }
    if( $except ) {
        return $except;
    }
    return;
}

=pod

=head2 after_consuming_events( )

Questo metodo viene invocato dopo aver elaborato tutti gli eventi pending e prima di effettuare una nuova ricerca.

Questo metodo B<può> essere sovrascritto per implementare delle logiche di chiusura su un blocco eventi elaborati.

Puo' lanciare una eccezione in caso di problemi fatali per comunicare C<ramq> di interrompere le elaborazioni.

=cut

#
# optionally override
#
sub after_consuming_events {
	return;
}

=pod

=head2 finish( )

Questo metodo viene invocato da C<ramq> prima di terminare. B<Può> essere sovrascritto per
effettuare delle routine di conclusione del processo di elaborazione degli eventi.

Può lanciare una eccezione in caso di problemi fatali per comunicare C<ramq> di interrompere le elaborazioni.

=cut

#
# optionally override
#
sub finish {
}

=pod

=head1 UTILITY METHODS

In questa sezione vengono elencate dei metodi di utilità che possono essere utlizzati dal consumer.

=cut

sub format_string {
    my ($string) = shift;
    return "undef" if !defined $string;
    $string =~ s/\n|\r/ /gsm;
    return $string;
}

#
# utilities
#

=pod

=head2 consume( REASON => C<SCALAR>, STATUS => C<SCALAR>, TARGET_REF => C<SCALAR>, ACK_DATA => C<HASHREF> )

=over

=item * B<REASON> : un commento sulla lavorazione effettuata

=item * B<STATUS> : numero intero non negativo che indica l'esito della lavorazione (per convenzione 0 viene utilizzato
per indicare che la lavorazione ha avuto successo, un numero positivo per indicare che la lavorazione non è andata a
buon fine)

=item * B<TARGET_REF> : stringa che indica un riferimento nel contesto del servizio target

=item * B<ACK_DATA> : opzionale. hashref chiave valore che può essere utilizzato per comunicare informazioni al servizio sorgente
dell'evento

=back

Questo metodo viene invocato al termine dell'override del metodo C<consume_event()> (es. C<return consume( ... )>)
per indicare a C<ramq> che la remote activity deve essere marcata come lavorata.

=cut

sub consume { shift->_response( FETCH_RESULT => 1, @_ ) }
	
=pod

=head2 skip( REASON => C<SCALAR> )

=over

=item * B<REASON> : il motivo per cui non si è lavorato l'evento

=back


Questo metodo viene invocato al termine dell'override del metodo C<consume_event()> (es. C<return skip( ... )>)
per indicare a C<ramq> che la remote activity non deve essere marcata come lavorata, e quindi riproposta al prossimo
giro.

=cut

sub skip { shift->_response( FETCH_RESULT => 0, @_ ) }
	
#
# metodo privato
#
# params: vedi SIRTI::Queue::Response::set_fetch_result()
#
# restituisce un oggetto di tipo SIRTI::Queue::Response
#

sub _response {
	my $self = shift;
	my %params = @_;
	
	$self->clear_error();
	my $response = eval { SIRTI::Queue::Response->new( @_ ); };
	$self->last_error( $@ )
		&& return undef
			if $@;
	
	return $response;
}

=pod

=head2 db( )

Questo metodo restituisce un oggetto di tipo C<SIRTI::DB> con la connessione corrente al database.

=cut

sub db { shift->{_DB} }


=pod

=head2 art( )

Questo metodo restituisce un oggetto API::ART nel caso in cui C<ramq> sia stato
configurato per accedere al DB utilizzando ART. Diversamente viene restituito undef
 
=cut

sub art { shift->{_ART} }


=pod

=head2 logger( )

Questo metodo restituisce un oggetto logger che può essere utilizzato per effettuare il logging del consumer
con i metodi C<trace()>, C<debug()>, C<info()>, C<warn()>, C<error()> e C<fatal()>.
Nota: il logger restituito è di tipo Log::Log4perl::Logger. Se nome del package è ad esempio PIPPO::Pluto::Paperino
la categoria del logger instanziato sarà PIPPO::LIB::PIPPO::Pluto::Paperino (vedi http://search.cpan.org/~mschilli/Log-Log4perl/)

=cut

sub logger { shift->{__LOGGER__} }

=pod

=head2 work_context( $name )

=over

=item * B<$name> : C<SCALAR>, nome della properties che deve essere restituita, opzionale

=back


Questo metodo permette di recuperare le properties che sono state caricate dal file di configurazione di C<ramq>
tramite la chiave C<WORK_CONTEXT>. Se viene invocata senza parametro viene restuito un hashref con tutte le properties
definite.

=cut

sub work_context {
	my $self = shift;
	my $name = shift;
	if ( defined $name ) {
	    return 
		   ( not exists $self->{_WORK_CONTEXT}->{$name} ) ? undef :
		       $self->{_WORK_CONTEXT}->{$name};
	}		    
	
	return $self->{_WORK_CONTEXT};
}

=pod

=head2 last_error( $msg )

=over

=item * B<$msg> : C<SCALAR>, messaggio di errore, opzionale

=back


Se viene invocata con il parametro $msg viene settato il messaggio di errore.
Ritorna sempre l'ultimo messaggio di errore settato. 

=cut
 
=pod

=head1 SEE ALSO

Script perl B<ramq>, package B<SIRTI::Queue::Event>, B<SIRTI::Queue::EventAckConsumer>, B<SIRTI::Queue::EventAck>


=head1 BUGS

Eventuali bug riscontrati dovranno essere segnalati su B<ART - Global Services>: L<https://www.artnet.sirtisistemi.net/ARTIT/art> aprendo un apposito DEV-TICKET.

=head1 HISTORY

=over

=item Ver. 0.1

Prima release del modulo

=back


=head1 AUTHOR

Gruppo OLO :)

=cut

1;
