package SIRTI::MQ::Notify::SmsDataHandler;

use base qw(SIRTI::MQ::Notify::DataHandler);



sub new {
    my $this  = shift;
    my $class = ref($this) || $this;

    return bless { }, $class; 
}


#
# utilizzato dal client per inviare richieste di inoltro 
# messaggio (SMS) con relativa notifica
# il metodo aggiorna il messaggio (hash) $outData, 
# effettuando un merge con il contenuto di $notifyInfo (hash)
# aggiungendo la chiave $self->_notify_type_key per 
# segnalare la richiesta di notifica.
#
# @Override
#
sub encode_notify_data {
    my ($self, $notifyInfo, $outData) = @_;

    return 1 if ref($notifyInfo) ne 'HASH';

    for( keys %{$notifyInfo} ) {
         $outData->{$_} = $notifyInfo->{$_};
    }

    $outData->{ $self->_notify_type_key } = 'SMS';
    return 1;
}

#
# utilizzato dal consumer per inviare il messaggio
# di notifica (hash) al consumer notifier 
# (Vedi classe base)
#
# @Override
#
sub encode_notify_request {
    my ($self, $inData, $extraData, $outData) = @_;

    ## dati mittente

    $outData->{NOTIFY_TYPE}    = 'SMS';
    $outData->{NOTIFY_CODE}    = $inData->{NOTIFY_CODE};
    $outData->{NOTIFY_SERVICE} = $inData->{NOTIFY_SERVICE};
    $outData->{ID_ANAGRAFICA}  = $inData->{ID_ANAGRAFICA};
    $outData->{ACTIVITY_TYPE}  = $inData->{ACTIVITY_TYPE};
    $outData->{ACTIVITY_ID}    = $inData->{ACTIVITY_ID};
    $outData->{ORDER_ID}       = $inData->{ID_ORDINE};

    
    ## dati messaggio

    $outData->{TELEPHONE_NUMBER} = $inData->{RECIPIENT_NUMBER};
    $outData->{CUSTOMER_NUMBER}  = $inData->{CUSTOMER_NUMBER};
    $outData->{SEND_DATE}        = $inData->{SEND_DATE};
    ## $outData->{ADDRESS}       = $inData->{ADDRESS};
    ## $outData->{EMAIL_ADDRESS} = $inData->{EMAIL_ADDRESS};
 
    if( ref($extraData) eq 'HASH' ) {
        for( keys %$extraData ) {
             $outData->{ "__" . $_ } = $extraData->{$_};
        }
    }

    return 1;  
}


1;

