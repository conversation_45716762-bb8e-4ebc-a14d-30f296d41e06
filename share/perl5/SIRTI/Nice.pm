package SIRTI::Nice;


=pod
sub UTF8_to_nice {  # convert a utf8 string into a nice string 
	local $_=shift;
	join("",
		map { 
			$_ > 255 
				? sprintf("\\x{%04X}", $_) 
				: (chr($_)=~/[[:cntrl:]]/ ? sprintf("\\x{%02X}",$_) : chr($_))   
	 	} unpack("U*", $_)
 	);         
}



sub nice_to_UTF8($)  { # convert a nice string into utf8 string
	local $_=shift ;
	while (s/\\x{([0-9a-fA-F]{4})}/pack('U',hex($1))/ge) {} # convert \x{hhhh} into utf8 
	while (s/\\x{([0-9a-fA-F]{2})}/chr(hex($1))/ge) {}		# convert \x{hh}  into char
	return $_;
}

=cut


	use Encode qw(decode   encode);
	use constant DB_CHARCODE => 'iso-8859-15';

	sub UTF8_to_nice { # convert a utf8 string into a nice string 
		my @out=();
		for my $s(@_) {
			push @out,encode(DB_CHARCODE,$s)
		}
		return scalar(@out) == 0 ? undef : scalar(@out) == 1 ? shift @out : @out;
	}


	sub nice_to_UTF8 { # convert a nice string into utf8 string
		my @out=();
		for my $s(@_) {
			push @out,decode(DB_CHARCODE,$s);
		}
		return scalar(@out) == 0 ? undef : scalar(@out) == 1 ? shift @out : @out;
	}

1;


