package SIRTI::NsnExt;

use strict;
use Carp ('verbose');
use Data::Dumper;
use DBI qw(:sql_types);

use base 'SIRTI::Base';

sub new {
    my $this = shift;
    my $class = ref($this) || $this;
    my %self = @_;
	
	die "Missing mandatory parameter 'DB'.\n" unless $self{DB};
	die "Bad parameter type: 'DB' must be a SIRTI::DB instance.\n" unless ref($self{DB}) eq 'SIRTI::DB';
	
	die "DB MISSING: Mancante package CONTROLLO_OLO!" if (!$self{DB}->fetch_minimalized ("select 'x' from all_objects where object_name = 'CONTROLLO_OLO'"));
	
	$self{_PREPARE_PREFISSO} = $self{DB}->create_prepare(qq{select controllo_olo.get_prefisso(:NSN_EXT) PREFISSO from dual});
	$self{_PREPARE_ARCO} = $self{DB}->create_prepare(qq{select controllo_olo.get_arco(:NSN_EXT) ARCO from dual});
	$self{_PREPARE_DONOR} = $self{DB}->create_prepare(qq{select controllo_olo.get_donor(:NSN_EXT) DONOR from dual});
	$self{_PREPARE_DISTRETTO} = $self{DB}->create_prepare(qq{select controllo_olo.get_distretto(:NSN_EXT) DISTRETTO from dual});
	$self{_PREPARE_AREALOCALE} = $self{DB}->create_prepare(qq{select controllo_olo.get_arealocale(:NSN_EXT) AREA_LOCALE from dual});
	$self{_PREPARE_OPERATORE} = $self{DB}->create_prepare(qq{select controllo_olo.get_operatore(:NSN_EXT) OPERATORE from dual});
	#$self{_PREPARE_ARCHI_COW} = $self{DB}->create_prepare(qq{select ARCO from table (controllo_olo.get_archi_from_cow (:COW))});
	
    return bless \%self, $class;
}

sub get_prefisso {
	my $self = shift;
	my $nsn_ext = shift;
	
	my $sth = $self->_get_prepare_prefisso()->get_sth();
	$sth->bind_param(':NSN_EXT',$nsn_ext,{ sql_type => SQL_VARCHAR });
	$sth->execute();
	my $r = $sth->fetchrow_hashref();
	return undef unless $r;
	return $r->{PREFISSO};
	
}

sub get_arco {
	my $self = shift;
	my $nsn_ext = shift;
	
	my $sth = $self->_get_prepare_arco()->get_sth();
	$sth->bind_param(':NSN_EXT',$nsn_ext,{ sql_type => SQL_VARCHAR });
	$sth->execute();
	my $r = $sth->fetchrow_hashref();
	return undef unless $r;
	return $r->{ARCO};
	
}

sub get_donor {
	my $self = shift;
	my $nsn_ext = shift;
	
	my $sth = $self->_get_prepare_donor()->get_sth();
	$sth->bind_param(':NSN_EXT',$nsn_ext,{ sql_type => SQL_VARCHAR });
	$sth->execute();
	my $r = $sth->fetchrow_hashref();
	return undef unless $r;
	return $r->{DONOR};
	
}

sub get_distretto {
	my $self = shift;
	my $nsn_ext = shift;
	
	my $sth = $self->_get_prepare_distretto()->get_sth();
	$sth->bind_param(':NSN_EXT',$nsn_ext,{ sql_type => SQL_VARCHAR });
	$sth->execute();
	my $r = $sth->fetchrow_hashref();
	return undef unless $r;
	return $r->{DISTRETTO};
	
}

sub get_arealocale {
	my $self = shift;
	my $nsn_ext = shift;
	
	my $sth = $self->_get_prepare_arealocale()->get_sth();
	$sth->bind_param(':NSN_EXT',$nsn_ext,{ sql_type => SQL_VARCHAR });
	$sth->execute();
	my $r = $sth->fetchrow_hashref();
	return undef unless $r;
	return $r->{AREA_LOCALE};
	
}

sub get_operatore {
	my $self = shift;
	my $nsn_ext = shift;
	
	my $sth = $self->_get_prepare_operatore()->get_sth();
	$sth->bind_param(':NSN_EXT',$nsn_ext,{ sql_type => SQL_VARCHAR });
	$sth->execute();
	my $r = $sth->fetchrow_hashref();
	return undef unless $r;
	return $r->{OPERATORE};
	
}

#sub get_archi_cow {
#	my $self = shift;
#	my $cow = shift;
#	
#	my $sth = $self->_get_prepare_archi_cow()->get_sth();
#	$sth->bind_param(':COW',$cow,{ sql_type => SQL_VARCHAR });
#	$sth->execute();
#	my $r = $sth->fetchall_arrayref();
#	return undef unless scalar @$r;
#	return $r;
#	
#}

# Prepare
sub _get_prepare_prefisso { $_[0]->{_PREPARE_PREFISSO} }
sub _get_prepare_arco { $_[0]->{_PREPARE_ARCO} }
sub _get_prepare_donor { $_[0]->{_PREPARE_DONOR} }
sub _get_prepare_distretto { $_[0]->{_PREPARE_DISTRETTO} }
sub _get_prepare_arealocale { $_[0]->{_PREPARE_AREALOCALE} }
sub _get_prepare_operatore { $_[0]->{_PREPARE_OPERATORE} }
#sub _get_prepare_archi_cow { $_[0]->{_PREPARE_ARCHI_COW} }



if (__FILE__ eq $0) {

    package main;

    use SIRTI::DB;

    use Data::Dumper;

	my $db=new SIRTI::DB($ARGV[0], DEBUG=>$ENV{ART_DB_DEBUG}) or die $!;
	
    use strict;
    use warnings;
    use Carp ('verbose');
	use Data::Dumper;
	
	#die 'ARGV[1] deve essere COW|NSN_EXT' if $ARGV[1] !~/^(COW|NSN_EXT)$/;
	die 'ARGV[1] deve essere COW' if $ARGV[1] !~/^(COW)$/;
	
	my $archi	= SIRTI::NsnExt->new(DB => $db);
	unless ( $archi ) {
		print STDERR "Errore interno alla libreria Archi\n";
		exit 1;
	}
	
	my $result;
	
	if ($ARGV[1] eq 'NSN_EXT'){
		$result = $archi->get_prefisso($ARGV[2]);
		print STDERR "Prefisso: $result\n";
		$result = $archi->get_arco($ARGV[2]);
		print STDERR "Arco: $result\n";
		$result = $archi->get_donor($ARGV[2]);
		print STDERR "Donor: $result\n";
		$result = $archi->get_distretto($ARGV[2]);
		print STDERR "Distretto: $result\n";
		$result = $archi->get_arealocale($ARGV[2]);
		print STDERR "Area Locale: $result\n";
		$result = $archi->get_operatore($ARGV[2]);
		print STDERR "Operatore: $result\n";
	} 
#	elsif ($ARGV[1] eq 'COW'){
#		$result = $archi->get_archi_cow($ARGV[2]);
#		print STDERR "ARCHI_COW: ";
#		foreach (@$result) {
# 			print STDERR $_->[0],' ';
# 		} 
# 		print STDERR "\n";
#				
#	}
} else { 
	1; 
}
