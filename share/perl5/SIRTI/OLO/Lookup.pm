################################################################################
#
# SIRTI::OLO::Lookup
#
################################################################################

=head1 NAME

B<SIRTI::OLO::Lookup> - Package per la trascodifica dei codici OLO per i vari 
servizi.

=head1 SYNOPSIS

  my $w = SIRTI::OLO::Lookup->new(DB => $db);

=head1 DESCRIPTION

Consente di trascodificare in modo semplice i codici OLO relativ ai servizi:

=over 4

=item * NPTS

=item * OLO2OLO

=item * DBU

=back

=cut

	package SIRTI::OLO::Lookup;

	use strict;
	use warnings;
	use Carp 'verbose';
	use Memoize;
	Memoize::memoize('_get_from_npts',			INSTALL => 'get_from_npts');
	Memoize::memoize('_get_from_o2o',			INSTALL => 'get_from_o2o');
	Memoize::memoize('_get_from_dbu',			INSTALL => 'get_from_dbu');
	Memoize::memoize('_get_dbu_from_npts',		INSTALL => 'get_dbu_from_npts');
	Memoize::memoize('_get_dbu_from_o2o',		INSTALL => 'get_dbu_from_o2o');
	Memoize::memoize('_get_npts_from_dbu',		INSTALL => 'get_npts_from_dbu');
	Memoize::memoize('_get_npts_from_o2o',		INSTALL => 'get_npts_from_o2o');
	Memoize::memoize('_get_o2o_from_npts',		INSTALL => 'get_o2o_from_npts');
	Memoize::memoize('_get_o2o_from_dbu',		INSTALL => 'get_o2o_from_dbu');
	Memoize::memoize('_is_active_from_npts',	INSTALL => 'is_active_from_npts');
	Memoize::memoize('_is_active_from_o2o',		INSTALL => 'is_active_from_o2o');
	Memoize::memoize('_is_active_from_dbu',		INSTALL => 'is_active_from_dbu');
	
	use SIRTI::Err;



################################################################################
#  P R O P R I E T A '   P U B B L I C H E
################################################################################

	our $VERSION = '0.01';



################################################################################
#  M E T O D I   P R I V A T I
################################################################################

	sub _get_from_to {
		my $self = shift;
		my $from = shift or return '';
		my $to = shift or return '';
		my $cod = shift or return '';
		print STDERR "Querying ${to}-code from ${from}-code for OLO ${cod}", "\n" if $self->{DEBUG};
		if ($from eq 'O2O') {
			$cod = $self->{DB}->quote($cod);
			$cod = SIRTI::Err::nvl($self->{DB}->fetch_minimalized("
				select PKG_O2O.GET_COW_FROM_PIN ($cod) from dual
			") ) ;
		};
		$cod = $self->{DB}->quote($cod);
		return SIRTI::Err::nvl( $self->{DB}->fetch_minimalized(qq|
			select	cod_${to}
			from	$self->{__REF_TABLE__}
			where	cod_${from} = ${cod}
		|) );
	}


	sub _get {
		my $self = shift;
		my $from = shift or return '';
		my $cod = shift or return '';
		$cod = $self->{DB}->quote($cod);
		print STDERR "Querying from ${from}-code for OLO ${cod}", "\n" if $self->{DEBUG};
		my $tmp = $self->{DB}->fetchall_hashref(qq|
			select	 cod_npts
					,active
					,cod_o2o
					,cod_dbu
					,ragione_sociale
			from	$self->{__REF_TABLE__}
			where	cod_${from} = ${cod}
		|);
		return $tmp->[0] if defined $tmp;
		return undef;
	}
	
	
	sub _is_active {
		my $self = shift;
		my $data = $self->_get(@_);
		return 0 unless defined $data;
		return $data->{ACTIVE};
	}


################################################################################
#  M E T O D I   P U B L I C I
################################################################################

=pod

=head1 METHODS

=head2 new( I<properties> )

E' possibile creare un'istanza dell'oggetto inizializzando le seguenti proprieta':

=over 4

=item * DB => SIRTI::DB

Riferimento ad un oggetto SIRTI::DB

=item * DEBUG => I<boolean>

Se impostato a 1 (o un altro scalare valutabile "vero") stampa informazioni riguardo 
la momoization delle chiamate.

=back

=cut
	sub new($$) {
		my $this = shift;
		my $class = ref($this) || $this;
		my %params  = @_;
		my %self  = ();
		##### DEBUG
		$self{DEBUG} = $params{DEBUG};
		delete $params{DEBUG};
		##### DB
		SIRTI::Err::prec(defined($params{DB}), 'Missing DB argument!');
		SIRTI::Err::prec($params{DB}->can('fetch_minimalized'), 'DB must be a reference SIRTI::DB class (or subclass)!');
		$self{DB} = $params{DB};
		delete $params{DB};
		##### Verifica parametri non invalidi
		SIRTI::Err::prec(scalar(keys(%params)) == 0, "Bad argument(s): " . join(', ', keys(%params)));
		##### Verifica DB
		my $count = -1;
		$self{__REF_TABLE__} = 'LOOKUP_OLO';
		eval {
			$count = $self{DB}->fetch_minimalized(qq|select count(1) from $self{__REF_TABLE__}|); 
		};
		SIRTI::Err::prec($@ eq '', "Missing table $self{__REF_TABLE__}?\n\n" . $@);
		SIRTI::Err::prec($count > 0, "Table $self{__REF_TABLE__} is empty!");
		
		eval {
			$count = $self{DB}->fetch_minimalized(qq|select 1 from all_OBJECTS where OBJECT_NAME = 'PKG_O2O' and rownum<2|); 
		};
		SIRTI::Err::prec(defined $@, $@);
		SIRTI::Err::prec($count, "Missing OBJECT PKG_O2O");
		return bless \%self, $class;
	}


=pod

=head2 get_I<CODOUT>_from_I<CODIN>(B<COD>)

Ritorna il codice B<COD> trascodificato dal tipo I<CODIN> al tipo I<CODOUT>:

=over 4

=item get_dbu_from_npts

=item get_dbu_from_o2o

=item get_npts_from_dbu

=item get_npts_from_o2o

=item get_o2o_from_npts

=item get_o2o_from_dbu

=back

=cut
	sub _get_dbu_from_npts	{ $_[0]->_get_from_to('NPTS',	'DBU',	$_[1]) }
	sub _get_dbu_from_o2o	{ $_[0]->_get_from_to('O2O',	'DBU',	$_[1]) }
	sub _get_npts_from_dbu	{ $_[0]->_get_from_to('DBU',	'NPTS',	$_[1]) }
	sub _get_npts_from_o2o	{ $_[0]->_get_from_to('O2O',	'NPTS',	$_[1]) }
	sub _get_o2o_from_npts	{ $_[0]->_get_from_to('NPTS',	'O2O',	$_[1]) }
	sub _get_o2o_from_dbu	{ $_[0]->_get_from_to('DBU',	'O2O',	$_[1]) }

=pod

=head2 get_from_I<CODIN>

Ritorna un hash contenente le informazioni relative al codice B<COD> di tipo I<CODIN>:

=over 4

=item get_from_npts

=item get_from_o2o

=item get_from_dbu

=back

=cut
	sub _get_from_npts	{ $_[0]->_get('NPTS',	$_[1]) }
	sub _get_from_o2o	{ $_[0]->_get('O2O',	$_[1]) }
	sub _get_from_dbu	{ $_[0]->_get('DBU',	$_[1]) }


=pod

=head2 is_active_from_I<CODIN>

Ritorna 1 se il codice B<COD> di tipo I<CODIN> indica un OLO attivo oppure 0 se non lo e':

=over 4

=item is_active_from_npts

=item is_active_from_o2o

=item is_active_from_dbu

=back

=cut
	sub _is_active_from_npts	{
		my $data = $_[0]->_get('NPTS',	$_[1]);
		return 0 unless defined($data); 
		return $data->{ACTIVE};
	}
	sub _is_active_from_o2o		{ 
		my $data = $_[0]->_get('O2O',	$_[1]);
		return 0 unless defined($data); 
		return $data->{ACTIVE}; 
	}
	sub _is_active_from_dbu		{ 
		my $data = $_[0]->_get('DBU',	$_[1]);
		return 0 unless defined($data); 
		return $data->{ACTIVE}; 
	}



###################################
#
# Testing the module...
#
###################################
if (__FILE__ eq $0) {

	####  test ###  
	package main;

	use strict;
	use warnings;
	use Carp 'verbose';
	use SIRTI::DB;
	use Data::Dumper;
	
	my $lo = SIRTI::OLO::Lookup->new( DB => SIRTI::DB->new($ENV{SQLID_DBU}) , DEBUG => 1 );
	print STDERR $lo->get_dbu_from_npts('FAS'), "\n";
	print STDERR $lo->get_dbu_from_npts('FAS'), "\n";
	print STDERR $lo->get_dbu_from_npts('FAS'), "\n";
	print STDERR $lo->get_dbu_from_o2o('FAS'), "\n";
	print STDERR $lo->get_dbu_from_o2o('FAS'), "\n";
	print STDERR $lo->get_dbu_from_o2o('FAS'), "\n";
	print STDERR $lo->get_o2o_from_dbu('TD'), "\n";
	print STDERR $lo->get_o2o_from_dbu('TD'), "\n";
	print STDERR $lo->get_o2o_from_dbu('TD'), "\n";
	print STDERR $lo->get_o2o_from_npts('FAS'), "\n";
	print STDERR $lo->get_o2o_from_npts('FAS'), "\n";
	print STDERR $lo->get_o2o_from_npts('FAS'), "\n";
	print STDERR $lo->get_npts_from_o2o('FAS'), "\n";
	print STDERR $lo->get_npts_from_o2o('FAS'), "\n";
	print STDERR $lo->get_npts_from_o2o('FAS'), "\n";
	print STDERR $lo->get_npts_from_dbu('TD'), "\n";
	print STDERR $lo->get_npts_from_dbu('TD'), "\n";
	print STDERR $lo->get_npts_from_dbu('TD'), "\n";

	print STDERR Dumper $lo->get_from_dbu('TD'), "\n";
	print STDERR Dumper $lo->get_from_dbu('TD'), "\n";
	print STDERR Dumper $lo->get_from_o2o('TL2'), "\n";
	print STDERR Dumper $lo->get_from_o2o('TL2'), "\n";
	print STDERR Dumper $lo->get_from_npts('TL2'), "\n";
	print STDERR Dumper $lo->get_from_npts('TL2'), "\n";

	print STDERR "id_active: ", Dumper($lo->is_active_from_o2o('TL2')), "\n";
	print STDERR "id_active: ", Dumper($lo->is_active_from_npts('TL2')), "\n";
	print STDERR "id_active: ", Dumper($lo->is_active_from_dbu('TD')), "\n";

	exit 0;

} else {

	1;

}


__END__

=pod

=head1 BUGS

Eventuali bug riscontrati dovranno essere segnalati su B<ART - Global Services>: L<https://www.artnet.sirtisistemi.net/ARTIT/art> aprendo un apposito DEV-TICKET.

=head1 HISTORY

=over

=item Ver. 0.01

Prima release del modulo

=back

=head1 AUTHOR

Alvaro Livraghi <<EMAIL>>

=cut
