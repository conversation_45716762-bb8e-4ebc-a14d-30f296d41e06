package SIRTI::OSSOAuth;
use strict;

$SIRTI::OSSOAuth::VERSION = '0.12';

use LWP::UserAgent;
use Data::Dumper;

my ($error, $errno);

sub new {

	my $self = {};

	my $class = shift;
	my $config = shift;

	my %sso_config = %{$config->{-sso_config}};
		
	$self->{serviceUrl} = $sso_config{'sso.serviceUrl'};
	$self->{frontendUrl} = $sso_config{'sso.frontendUrl'};
	$self->{type} = $sso_config{'sso.type'};
	$self->{checkIDP} = $sso_config{'sso.checkIDP'};
	$self->{debug} = $sso_config{'debug.enabled'};
	$self->{adminUsername} = $sso_config{'sso.adminUsername'};
	$self->{adminPassword} = $sso_config{'sso.adminPassword'};
	$self->{application_name} = $sso_config{'sso.application_name'};

	$self->{testlb} = $sso_config{'sso.testlb'};
	$self->{fe_rewrite_prefix} = $sso_config{'sso.fe_rewrite_prefix'};

	# patch per cambio dominio TeleTu
	if ( $ENV{HTTP_X_FORWARDED_HOST} ) {
		$self->{frontendUrl}       =~ s/tele2services\.sirtisistemi\.net/$ENV{HTTP_X_FORWARDED_HOST}/;
		$self->{fe_rewrite_prefix} =~ s/tele2services\.sirtisistemi\.net/$ENV{HTTP_X_FORWARDED_HOST}/;
	}
	
	$self->{sync_etf} = $sso_config{'sso.sync_etf'};
	if(!$self->{sync_etf}) {
		$sso_config{'passwordRules.abilita_recupero'} = 0;
	}

	my %passwordRules = ();
	foreach my $k (keys %sso_config) {
		if($k =~ /^passwordRules\.(.*)$/) {
			$passwordRules{$1} = $sso_config{$k};
		}
	}
	$self->{passwordRules} = \%passwordRules;
	
	my %pushConfig = ();
	foreach my $k (keys %sso_config) {
		if($k =~ /^push_([^\.]*)\.(.*)$/) {
			$pushConfig{$1}{$2} = $sso_config{$k};
		}
	}
	$self->{pushConfig} = \%pushConfig;

	$self->{session} = $config->{-session};
	$self->{request} = $config->{-request};
	$self->{response} = $config->{-response};
	$self->{cgi} = $config->{-cgi};
	
	$self->{serviceUrl} =~ s/\/$//;
	if(!$self->{frontendUrl}) {
		$self->{frontendUrl} = $self->{serviceUrl};
	}
	$self->{frontendUrl} =~ s/\/$//;
	$self->{changePasswordUrl} = $self->{frontendUrl}."/tools/changepwd.html";
	$self->{lostPasswordUrl} = $self->{frontendUrl}."/tools/lostpwd.html";
	$self->{logoutUrl} = $self->{frontendUrl}."/tools/logout.html";
	$self->{portalUrl} = $self->{frontendUrl}."/tools/portale.html";
	$self->{loginUrl} = $self->{frontendUrl}."/UI/Login";
	
	# checkIDP - indica quando effettuare la verifica della validita' della sessione sull'IDP,
	# puo' assumere i seguenti valori:
	# 0 - sempre
	# -1 - solo per il login
	# n - verifica la validita' della sessione ogni n secondi
	$self->{checkIDP} = 0 if($self->{checkIDP} !~ /^[+-]?[0-9]+$/);

	SWITCH: {
		#per ora e' supportato solo il single domain sso (SDSSO)
		($self->{type} eq "CD") && do { last SWITCH };
		$self->{type} = "SD";
	}

	if($self->{fe_rewrite_prefix}) {
		$self->{requestUrl} = $self->{fe_rewrite_prefix}.$ENV{REQUEST_URI}.($ENV{QUERY_STRING} ? "?".$ENV{QUERY_STRING} : "");
	}
	else {
		$self->{requestUrl} = ($ENV{HTTPS} ? "https://" : "http://").$ENV{HTTP_HOST}.$ENV{REQUEST_URI}.($ENV{QUERY_STRING} ? "?".$ENV{QUERY_STRING} : "");
	}
	# my $hostname = ($ENV{HTTP_X_FORWARDED_HOST} ? $ENV{HTTP_X_FORWARDED_HOST} : $ENV{HTTP_HOST});
	# $self->{requestUrl} = ($ENV{HTTPS} ? "https://" : "http://").$hostname.$ENV{REQUEST_URI}.($ENV{QUERY_STRING} ? "?".$ENV{QUERY_STRING} : "");
	
	use URI::Escape;
	$self->{requestUrlEncoded} = uri_escape($self->{requestUrl});
	$self->{lostPasswordUrlEncoded} = uri_escape($self->{lostPasswordUrl});

	$self->{_error} = \$error;
	$self->{_errno} = \$errno;

	$self->{subjectId} = undef;
	$self->{username} = undef;
	$self->{attributes} = {};

	$self->{authBy} = "";

	# gestione password
  $self->{passwordRules}{orig_specials} = $self->{passwordRules}{specials};
	$self->{passwordRules}{specials} =~ s/\+/\\\+/g;
	$self->{passwordRules}{specials} =~ s/\-/\\\-/g;
	$self->{passwordRules}{specials} =~ s/\./\\\./g;
	$self->{passwordRules}{specials} =~ s/\*/\\\*/g;
	$self->{passwordRules}{specials} =~ s/\$/\\\$/g;
	$self->{passwordRules}{specials} =~ s/\?/\\\?/g;
	$self->{passwordRules}{specials} =~ s/\^/\\\^/g;
	$self->{passwordRules}{specials} =~ s/\&/\\\&/g;
	$self->{passwordRules}{specials} =~ s/\%/\\\%/g;
	$self->{passwordRules}{specials} =~ s/\@/\\\@/g;

	if($self->{passwordRules}{howmanyold} !~ /^[0-9]+$/) {
		$self->{passwordRules}{howmanyold} = 0;
	}

	if($self->{passwordRules}{warn_before_expire} !~ /^[0-9]+$/) {
		$self->{passwordRules}{warn_before_expire} = 0;
	}

	bless ($self, $class);
	$self->_appendToLog("requestUrl: ".$self->{requestUrl});
	return $self;

}

sub checkAuth {
	
	my $self = shift;

	my $session = $self->{session};

	my $session_username;
	if(ref($self->{cgi})) {
		$session_username = ${$session}->param('username');
	}
	else {
	 $session_username = ${$session}->{username};
	}
	if(defined($session_username)) {
	
		my $checkIDP;

		# checkIDP - indica quando effettuare la verifica della validita' della sessione sull'IDP,
		# puo' assumere i seguenti valori:
		# 0 - sempre
		# -1 - solo per il login
		# n - verifica la validita' della sessione ogni n secondi

		my $session_lastIDPcheck;
		if(ref($self->{cgi})) {
			$session_lastIDPcheck = ${$session}->param('lastIDPcheck');
		}
		else {
		 $session_lastIDPcheck = ${$session}->{lastIDPcheck};
		}
		SWITCH: {
			($self->{checkIDP} == 0) && do { $checkIDP = 1; last SWITCH};
			($self->{checkIDP} == -1) && do { $checkIDP = 0; last SWITCH};
			if((time - $session_lastIDPcheck) > $self->{checkIDP}) {
				$checkIDP = 1;				
			}
			else {
				$checkIDP = 0;
			}
		}

		my $session_sirtiforcepasswordchange;
		if(ref($self->{cgi})) {
			my $tmp = ${$session}->param('sso_attributes');
			$session_sirtiforcepasswordchange = $tmp->{sirtiforcepasswordchange}[0];
		}
		else {
		 $session_sirtiforcepasswordchange = ${$session}->{sso_attributes}{sirtiforcepasswordchange}[0];
		}
		if(
			$session_sirtiforcepasswordchange eq 'Y' ||
			$session_sirtiforcepasswordchange eq 'X' ||
			$session_sirtiforcepasswordchange eq 'U' ||
			$session_sirtiforcepasswordchange eq 'F'
		) {
			$checkIDP = 1;
		}
		
		if($checkIDP) {
			if($self->_isAuthenticated()) {
				$self->{subjectId} = $self->_getSubjectId();
				$self->_getAttributes();
				$self->{username} = $self->{attributes}{uid}[0];
				if(ref($self->{cgi})) {
					${$session}->param('username', $self->{attributes}{uid}[0]);
					${$session}->param('subjectId', $self->{subjectId});
					${$session}->param('sso_attributes', $self->{attributes});
					${$session}->param('lastIDPcheck', time);
				}
				else {
					${$session}->{username} = $self->{attributes}{uid}[0];
					${$session}->{subjectId} = $self->{subjectId};
					${$session}->{sso_attributes} = $self->{attributes};
					${$session}->{lastIDPcheck} = time;
				}
				$self->{authBy} = "IDP";
				$self->_appendToLog("AuthBy: ".$self->{authBy});

				# verifica password rules
				$self->_checkPasswordRules();
				
			}
			else {
				if(ref($self->{cgi})) {
					${$session}->clear('sso_attributes');
					${$session}->clear('subjectId');
					${$session}->clear('lastIDPcheck');
					${$session}->clear('username');
				}
				else {
					delete ${$session}->{sso_attributes};
					delete ${$session}->{subjectId};
					delete ${$session}->{lastIDPcheck};
					delete ${$session}->{username};
				}
				if(ref($self->{cgi})) {
					print ${$self->{cgi}}->redirect(
						$self->{loginUrl}."?goto=".$self->{requestUrlEncoded}.
						($self->getPasswordRules('abilita_recupero') ? "&rpwd=".$self->{lostPasswordUrlEncoded} : "")
					);
					exit;
				}
				else {
					${$self->{response}}->Redirect(
						$self->{loginUrl}."?goto=".$self->{requestUrlEncoded}.
						($self->getPasswordRules('abilita_recupero') ? "&rpwd=".$self->{lostPasswordUrlEncoded} : "")
					);
				}
			}
		}
		else {
			if(ref($self->{cgi})) {
				$self->{attributes} = ${$session}->param('sso_attributes');
				$self->{subjectId} = ${$session}->param('subjectId');
				$self->{username} = ${$session}->param('username');
			}
			else {
				$self->{attributes} = ${$session}->{sso_attributes};
				$self->{subjectId} = ${$session}->{subjectId};
				$self->{username} = ${$session}->{username};
			}
			$self->{authBy} = "session";
			$self->_appendToLog("AuthBy: ".$self->{authBy});
		}
	}
	else {
			if($self->_isAuthenticated()) {
				
				$self->{subjectId} = $self->_getSubjectId();
				$self->_getAttributes();
				$self->{username} = $self->{attributes}{uid}[0];
				if(ref($self->{cgi})) {
					${$session}->param('username', $self->{attributes}{uid}[0]);
					${$session}->param('subjectId', $self->{subjectId});
					${$session}->param('sso_attributes', $self->{attributes});
					${$session}->param('lastIDPcheck', time);
				}
				else {
					${$session}->{username} = $self->{attributes}{uid}[0];
					${$session}->{subjectId} = $self->{subjectId};
					${$session}->{sso_attributes} = $self->{attributes};
					${$session}->{lastIDPcheck} = time;
				}
				$self->{authBy} = "IDP";
				$self->_appendToLog("AuthBy: ".$self->{authBy});
				
				# verifica password rules
				$self->_checkPasswordRules();
				
			}
			else {
				if(ref($self->{cgi})) {
					${$session}->clear('sso_attributes');
					${$session}->clear('subjectId');
					${$session}->clear('lastIDPcheck');
					${$session}->clear('username');
				}
				else {
					delete ${$session}->{sso_attributes};
					delete ${$session}->{subjectId};
					delete ${$session}->{lastIDPcheck};
					delete ${$session}->{username};
				}
				if(ref($self->{cgi})) {
					print ${$self->{cgi}}->redirect(
						$self->{loginUrl}."?goto=".$self->{requestUrlEncoded}.
						($self->getPasswordRules('abilita_recupero') ? "&rpwd=".$self->{lostPasswordUrlEncoded} : "")
					);
					exit;
				}
				else {
					${$self->{response}}->Redirect(
						$self->{loginUrl}."?goto=".$self->{requestUrlEncoded}.
						($self->getPasswordRules('abilita_recupero') ? "&rpwd=".$self->{lostPasswordUrlEncoded} : "")
					);
				}
			}
	}

	return 1;

}

sub _checkPasswordRules {
	
	my $self = shift;

	my $tmpcpurl = quotemeta($self->{changePasswordUrl});
	my $tmplourl = quotemeta($self->{logoutUrl});
	if($self->{requestUrl} !~ /^$tmpcpurl/ && $self->{requestUrl} !~ /^$tmplourl/) {
		# $self->_appendToLog("sono in verifica password");
		my $expired = $self->_passwordIsExpired();
		if($expired && $self->{attributes}{sirtiforcepasswordchange}[0] ne 'X') {
			$self->updateUser({
				"username" => $self->{username},
				"sirtiforcepasswordchange" => "X"
			}, 1);
		}
		my $force = $self->{attributes}{sirtiforcepasswordchange}[0];
		my $glob_force = $self->{passwordRules}{force_attivo};
		if($force eq 'Y' && !($glob_force eq '' || $glob_force eq '0')) {
			# l'admin ha modificato la password dell'utente
			if(ref($self->{cgi})) {
				print ${$self->{cgi}}->redirect($self->{changePasswordUrl}."?goto=".$self->{requestUrlEncoded});
				exit;
			}
			else {
				${$self->{response}}->Redirect($self->{changePasswordUrl}."?goto=".$self->{requestUrlEncoded});
			}
		}
		if($force eq 'F' && !($glob_force eq '' || $glob_force eq '0')) {
			# primo login
			if(ref($self->{cgi})) {
				print ${$self->{cgi}}->redirect($self->{changePasswordUrl}."?goto=".$self->{requestUrlEncoded});
				exit;
			}
			else {
				${$self->{response}}->Redirect($self->{changePasswordUrl}."?goto=".$self->{requestUrlEncoded});
			}
		}
		if($force eq 'U' && !($glob_force eq '' || $glob_force eq '0')) {
			# confronto case insensitive, forzatura cambio password
			if(ref($self->{cgi})) {
				print ${$self->{cgi}}->redirect($self->{changePasswordUrl}."?goto=".$self->{requestUrlEncoded});
				exit;
			}
			else {
				${$self->{response}}->Redirect($self->{changePasswordUrl}."?goto=".$self->{requestUrlEncoded});
			}
		}
		if($force eq 'X' && !($glob_force eq '' || $glob_force eq '0')) {
			# password expired
			if(ref($self->{cgi})) {
				print ${$self->{cgi}}->redirect($self->{changePasswordUrl}."?goto=".$self->{requestUrlEncoded});
				exit;
			}
			else {
				${$self->{response}}->Redirect($self->{changePasswordUrl}."?goto=".$self->{requestUrlEncoded});
			}
		}
	}
}

sub getPasswordRules {
	
	my $self = shift;
	my $name = shift;
	
	if($name) {
		return $self->{passwordRules}{$name};
	}
	else {
		return %{$self->{passwordRules}};
	}
}

sub _validatePwd {

	my $self = shift;
	my $pw = shift;
	my ($up, $num, $spec, $max, $i);
	
	for ($i=0; $i < length($pw); $i++) {
		my $c = substr($pw, $i, 1);
		$up = 1 if $c =~ /[A-Z]/;
		$num = 1 if $c =~ /[0-9]/;
		$spec = 1 if $c =~ /[$self->{passwordRules}{specials}]/;
	}
	return 0 if $i < $self->{passwordRules}{minlen};
	return 0 if $up == 0 && $self->{passwordRules}{upchar} == 1;
	return 0 if $num == 0 && $self->{passwordRules}{numchar} == 1;
	return 0 if $spec == 0 && $self->{passwordRules}{specchar} == 1;
	return 1;

}

sub _validateLastPwds {

	my $self = shift;
	my $pw = shift;
	
	if(!$self->{passwordRules}{howmanyold}) {
		return 1;
	}
	
	my @tmp = ();
	if(defined($self->{attributes}{sirtiuserlastpasswords})) {
		@tmp = sort { $b cmp $a } @{$self->{attributes}{sirtiuserlastpasswords}};
	}

	use Digest::MD5;
	my $ctx = Digest::MD5->new;
	$ctx->add($pw);
	my $password = "{MD5}".$ctx->b64digest."==";

	my $i = 0;
	foreach my $oldpwd (@tmp) {
		$i++;
		if($i <= $self->{passwordRules}{howmanyold}) {
			if($password eq substr($oldpwd, 20)) {
				return 0;
			}
		}
	}
	
	return 1;

}

sub _passwordIsExpired {
	
	my $self = shift;

	my $expired =  0;
	
	if($self->{passwordRules}{expire} ne '' && $self->{passwordRules}{expire} ne '0') {
		use Date::Calc qw( Delta_Days Today );
		if($self->{attributes}{sirtiuserfirstlogin}[0] =~ /\d{4}-\d{2}-\d{2}/) {
			# entro solo se first_login e' valorizzato, quindi se lo lascio null non c'e' la forzatura della password
			my ($flyear, $flmonth, $flday) = split '-', $self->{attributes}{sirtiuserfirstlogin}[0];
			if (Delta_Days(($flyear, $flmonth, $flday), Today()) >= $self->{passwordRules}{expire}) {
				$expired = 1;
			}
		}
	}

	return $expired;

}

sub getAttributes {
	
	my $self = shift;
	my $name = shift;
	
	if($name) {
		if(ref($self->{attributes}{$name})) {
			return @{$self->{attributes}{$name}};
		}
		else {
			my @tmp = ();
			return @tmp;
		}
	}
	else {
		return %{$self->{attributes}};
	}

}

sub getChangePasswordUrl {
	
	my $self = shift;
	
	return $self->{changePasswordUrl}."?goto=".$self->{requestUrlEncoded};
}

sub getLostPasswordUrl {
	
	my $self = shift;
	
	return $self->{lostPasswordUrl};
}

sub getLoginUrl {
	
	my $self = shift;
	
	return $self->{loginUrl};
}

sub getLogoutUrl {
	
	my $self = shift;
	
	return $self->{logoutUrl};
}

sub getPortalUrl {
	
	my $self = shift;
	
	return $self->{portalUrl};
}

sub showPasswordRules {

    my $self = shift;

		my @ret = ();
    push(@ret, "almeno ".$self->{passwordRules}{minlen}." caratteri");
    push(@ret, "almeno un carattere maiuscolo") if $self->{passwordRules}{upchar};
    push(@ret, "almeno un carattere numerico") if $self->{passwordRules}{numchar};
    push(@ret, "almeno un carattere speciale tra: ".$self->{passwordRules}{orig_specials}) if $self->{passwordRules}{specchar};
    push(@ret, "non deve essere stata utilizzata nei ".$self->{passwordRules}{howmanyold}." precedenti cambi password") if $self->{passwordRules}{howmanyold};

    return @ret;
}

sub createPwd {
	
	my $self = shift;
	my $length = shift;
	
	$length = 8 if(!$length);

	my $password;
	my $possible = 'abcdefghijkmnpqrstuvwxyz23456789ABCDEFGHJKLMNPQRSTUVWXYZ';
	while (length($password) < $length) {
		$password .= substr($possible, (int(rand(length($possible)))), 1);
	}
	return $password

}

sub changePassword {
	
	my $self = shift;
	my $password = shift;
	
	if(!$self->_validatePwd($password)) {
		$self->{_errno} = 1001;
		$self->{_error} = "Password validation error";
		return 0;
	}
	
	if(!$self->_validateLastPwds($password)) {
		$self->{_errno} = 1002;
		$self->{_error} = "Password equal to one of previous";
		return 0;
	}
	
	my @toadd = ();
	if($self->{passwordRules}{howmanyold}) {
		use Date::Calc qw( Today Today_and_Now );
		use Digest::MD5;
		my $ctx = Digest::MD5->new;
		$ctx->add($password);
		my @tmp = ();
		if(defined($self->{attributes}{sirtiuserlastpasswords})) {
			@tmp = sort { $b cmp $a } @{$self->{attributes}{sirtiuserlastpasswords}};
		}
		unshift @tmp, sprintf("%04d-%02d-%02d %02d:%02d:%02d {MD5}%s==", Today_and_Now(), $ctx->b64digest());
		for(my $i=0; $i<@tmp; $i++) {
			if($i < $self->{passwordRules}{howmanyold}) {
				push @toadd, $tmp[$i];
			}
		}
	}
	
	# TODO modificare i permessi di LDAP in modo che possa modificare anche gli attributi
	# sirtiforcepasswordchange e sirtiforcepasswordchange. fatto questo togliere il secondo parametro
	return $self->updateUser({
		"username" => $self->{username},
		"userpassword" => $password,
		"passwordtype" => "plaintext",
		"sirtiforcepasswordchange" => "N",
		"sirtiuserfirstlogin" => sprintf("%04d-%02d-%02d", Today()),
		"sirtiuserlastpasswords" => \@toadd
	}, 1);

}

sub authenticate() {

	# TODO apre una sessione, fare in modo che non succeda, probabilmente facendo un logout subito dopo

	my $self = shift;
	my $username = shift;
	my $password = shift;
	
	my $url = $self->{serviceUrl}."/identity/authenticate";
	if($self->{testlb}) {
		$url = "http://testartnet.sirtisistemi.net/lbsso/identity/authenticate";
	}
	
  my $connection = LWP::UserAgent->new();
  $connection->agent("SIRTI::OSSOAuth/".$SIRTI::OSSOAuth::VERSION." ");
  my $req = HTTP::Request->new(POST => $url);
  $req->content_type('application/x-www-form-urlencoded');

	use URI::Escape;
	my $content;
	$content .= "username=".uri_escape(lc($username))."&";
	$content .= "password=".uri_escape($password)."&";
	$content = substr($content, 0, -1);
	
	$req->content($content);

	my $res = $connection->request($req);

	if($res->is_success()) {
		$res->content =~ /^token\.id=(.*)$/;
		$content = "subjectid=".uri_escape($1);
		$req->uri($self->{serviceUrl}."/identity/logout");
		$req->content($content);
		$res = $connection->request($req);
		return 1;
	}
	else {
		$res->status_line() =~ /^([\d]+) (.*)$/;
		$self->{_errno} = $1;
		$self->{_error} = $2;
		return 0;
	}
}

sub createUser {

	my $self = shift;
	my $userref = shift;
	my $use_admin = shift;
	
	my ($subjectid, $identity_name);
	my %user = %{$userref};
	if($use_admin) {
		# $self->_appendToLog('creating user by admin account');
		$subjectid = $self->_getAdminSubjectId();
		if(!$subjectid) {
			return 0;
		}
	}
	else {
		# $self->_appendToLog('creating user by user account');
		$subjectid = $self->{subjectId};
	}

	$identity_name = $user{username};
	delete $user{username};
	
	if($user{passwordtype} eq "plaintext") {
		use Digest::MD5 qw(md5_hex);
		my $ctx = Digest::MD5->new;
		$ctx->add($user{userpassword});
		$user{userpassword} = "{MD5}".$ctx->b64digest()."==";
	}
	delete $user{passwordtype};	

	#identity_name
	#identity_type
	#identity_attribute_names
	#identity_attribute_values_attributename
	#admin

	my $url = $self->{serviceUrl}."/identity/create";
	if($self->{testlb}) {
		$url = "http://testartnet.sirtisistemi.net/lbsso/identity/create";
	}
	
  my $connection = LWP::UserAgent->new();
  $connection->agent("SIRTI::OSSOAuth/".$SIRTI::OSSOAuth::VERSION." ");
  my $req = HTTP::Request->new(POST => $url);
  $req->content_type('application/x-www-form-urlencoded');

	use URI::Escape;
	use Date::Calc qw( Today );
	my $content = "admin=".uri_escape($subjectid)."&";
	$content .= "identity_name=".uri_escape(lc($identity_name))."&";
	$content .= "identity_type=user&";
	foreach my $k (keys %user) {
		$content .= "identity_attribute_names=".uri_escape($k)."&";
		$content .= "identity_attribute_values_".$k."=".uri_escape($user{$k})."&";
	}
	$content .= "identity_attribute_names=sirtiusercreated&";
	$content .= "identity_attribute_values_sirtiusercreated=".sprintf("%04d-%02d-%02d", Today())."&";
	$content = substr($content, 0, -1);
	
	$req->content($content);
	$self->_appendToLog('creating user REST message body: '.$content);

	my $res = $connection->request($req);

	if($use_admin) {
		$self->_releaseAdminSubjectId($subjectid);
	}

	if($res->is_success()) {
		return 1;
	}
	else {
		$res->status_line() =~ /^([\d]+) (.*)$/;
		$self->{_errno} = $1;
		$self->{_error} = $2;
		return 0;
	}

}

sub addApplication {
	
	my $self = shift;
	my $username = shift;
	my $application = shift;
	my $use_admin = shift;

	my %userData = $self->getUserData($username);
	
	my %user = ();
	$user{username} = $userData{'uid'}[0];
	$user{sirtiapplications} = $userData{'sirtiapplications'};
	use Date::Calc qw( Today );
	
	my @tmp = ();
	if(ref($user{sirtiapplications})) {
		@tmp = @{$user{sirtiapplications}};
	}
	@{$user{sirtiapplications}} = ();
	foreach my $app (@tmp) {
		if($app !~ /^[0-9]{4}-[0-9]{2}-[0-9]{2} $application$/) {
			push @{$user{sirtiapplications}}, $app;
		}
	}
	push @{$user{sirtiapplications}}, sprintf("%04d-%02d-%02d", Today())." ".$application;
	
	return $self->updateUser(\%user, $use_admin, 1);
	
}

sub setApplicationLastLogin {
	
	my $self = shift;
	my $username = shift;
	my $application = shift;
	my $use_admin = shift;

	my %userData = $self->getUserData($username);
	
	my %user = ();
	$user{username} = $userData{'uid'}[0];
	$user{sirtiapplications} = $userData{'sirtiapplications'};
	$user{sirtiapplicationlastlogin} = $userData{'sirtiapplicationlastlogin'};
	use Date::Calc qw( Today Today_and_Now );

	if(!ref($userData{'sirtiapplications'})) {
		return 0;
	}
	if(!grep(/^[0-9]{4}-[0-9]{2}-[0-9]{2} $application$/, @{$userData{'sirtiapplications'}})) {
		return 0;
	}

	my @tmp = ();
	if(ref($user{sirtiapplicationlastlogin})) {
		@tmp = @{$user{sirtiapplicationlastlogin}};
	}
	@{$user{sirtiapplicationlastlogin}} = ();
	foreach my $app (@tmp) {
		if($app !~ /^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2} $application$/) {
			push @{$user{sirtiapplicationlastlogin}}, $app;
		}
	}
	push @{$user{sirtiapplicationlastlogin}}, sprintf("%04d-%02d-%02d %02d:%02d:%02d", Today_and_Now())." ".$application;
	
	return $self->updateUser(\%user, $use_admin, 1);
	
}

sub removeApplication {
	
	my $self = shift;
	my $username = shift;
	my $application = shift;
	my $use_admin = shift;

	my %userData = $self->getUserData($username);
	
	my @tmp = ();
	foreach my $tmpapp (@{$userData{'sirtiapplications'}}) {
		if($tmpapp !~ /^[0-9]{4}-[0-9]{2}-[0-9]{2} $application$/) {
			push @tmp, $tmpapp;
		}
	}
	if(@tmp) {
		$userData{'sirtiapplications'} = \@tmp;
	}
	else {
		$userData{'sirtiapplications'} = "";
	}
	
	my @tmpLastLogin = ();
	foreach my $tmpapp (@{$userData{'sirtiapplicationlastlogin'}}) {
		if($tmpapp !~ /^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2} $application$/) {
			push @tmpLastLogin, $tmpapp;
		}
	}
	if(@tmpLastLogin) {
		$userData{'sirtiapplicationlastlogin'} = \@tmpLastLogin;
	}
	else {
		$userData{'sirtiapplicationlastlogin'} = "";
	}
	
	my %user = ();
	$user{username} = $userData{'uid'}[0];
	$user{sirtiapplications} = $userData{'sirtiapplications'};
	$user{sirtiapplicationlastlogin} = $userData{'sirtiapplicationlastlogin'};
	
	return $self->updateUser(\%user, $use_admin, 1);
	
}

sub updateUser {
	
	my $self = shift;
	my $userref = shift;
	my $use_admin = shift;
	my $dont_propagate = shift;
	
	my ($subjectid, $identity_name);
	my %user = %{$userref};
	if($use_admin) {
		# $self->_appendToLog('updating user by admin account');
		$subjectid = $self->_getAdminSubjectId();
		if(!$subjectid) {
			return 0;
		}
	}
	else {
		# $self->_appendToLog('updating user by user account');
		$subjectid = $self->{subjectId};
	}

	$identity_name = $user{username};
	delete $user{username};
	
	my $plaintextpassword;
	if($user{passwordtype} eq "plaintext") {
		$plaintextpassword = $user{userpassword};
		use Digest::MD5 qw(md5_hex);
		my $ctx = Digest::MD5->new;
		$ctx->add($user{userpassword});
		$user{userpassword} = "{MD5}".$ctx->b64digest()."==";
	}
	delete $user{passwordtype};	

	#identity_name
	#identity_attribute_names
	#identity_attribute_values_attributename
	#admin

	my $url = $self->{serviceUrl}."/identity/update";
	if($self->{testlb}) {
		$url = "http://testartnet.sirtisistemi.net/lbsso/identity/update";
	}
	
  my $connection = LWP::UserAgent->new();
  $connection->agent("SIRTI::OSSOAuth/".$SIRTI::OSSOAuth::VERSION." ");
  my $req = HTTP::Request->new(POST => $url);
  $req->content_type('application/x-www-form-urlencoded');

	use URI::Escape;
	my $content = "admin=".uri_escape($subjectid)."&";
	$content .= "identity_name=".uri_escape(lc($identity_name))."&";
	foreach my $k (keys %user) {
		if(ref($user{$k})) {
			foreach my $value (@{$user{$k}})	{
				$content .= "identity_attribute_names=".uri_escape($k)."&";
				$content .= "identity_attribute_values_".$k."=".uri_escape($value)."&";
			}
		}
		else {
			$content .= "identity_attribute_names=".uri_escape($k)."&";
			$content .= "identity_attribute_values_".$k."=".uri_escape($user{$k})."&";
		}
	}
	$content = substr($content, 0, -1);
	
	$req->content($content);
	$self->_appendToLog('updating user REST message body: '.$content);
	my $res = $connection->request($req);

	if($use_admin) {
		$self->_releaseAdminSubjectId($subjectid);
	}

	if($res->is_success()) {
		if($self->{username} eq uc($identity_name)) {
			$self->_getAttributes();
			if(ref($self->{cgi})) {
				${$self->{session}}->param('sso_attributes', $self->{attributes});
			}
			else {
				${$self->{session}}->{sso_attributes} = $self->{attributes};
			}
		}
		
		if(!$dont_propagate) {
			my %userdata = $self->getUserData($identity_name);
			$self->_pushUpdates(\%userdata, $plaintextpassword);
		}

		return 1;
	}
	else {
		$res->status_line() =~ /^([\d]+) (.*)$/;
		$self->{_errno} = $1;
		$self->{_error} = $2;
		$self->_appendToLog('Errore nella modifica dei dati utente: '.$self->{_errno}." - ".$self->{_error}, "error");
		return 0;
	}

}

sub _pushUpdates {
	
	my $self = shift;
	my $userref = shift;
	my $plaintextpassword = shift;
	
	my (%user, %tmpuser);
	%tmpuser = %{$userref};
	
	$user{username} = $tmpuser{uid}[0];
	$user{uid} = $tmpuser{uid}[0];
	$user{sn} = $tmpuser{sn}[0];
	$user{givenname} = $tmpuser{givenname}[0];
	$user{inetuserstatus} = $tmpuser{inetuserstatus}[0];
	$user{sirtiforcepasswordchange} = $tmpuser{sirtiforcepasswordchange}[0];
	$user{sirtiuserfirstlogin} = $tmpuser{sirtiuserfirstlogin}[0];
	$user{sirtiuserlastlogin} = $tmpuser{sirtiuserlastlogin}[0];
	$user{sirtiusercreated} = $tmpuser{sirtiusercreated}[0];
	$user{userpassword} = $tmpuser{userpassword}[0];
	$user{cn} = $tmpuser{cn}[0];
	$user{mail} = $tmpuser{mail}[0];
	$user{telephonenumber} = $tmpuser{telephonenumber}[0];
	$user{facsimiletelephonenumber} = $tmpuser{facsimiletelephonenumber}[0];
	if($user{userpassword} =~ /^\{MD5\}(.*)==$/) {
		$user{userpassword} =~ s/^\{MD5\}(.*)==$/$1/;
	}
	else {
		use Digest::MD5 qw(md5_hex);
		my $ctx = Digest::MD5->new;
		$ctx->add($user{userpassword});
		$user{userpassword} = $ctx->b64digest();
	}
	
	my %pushConfig = %{$self->{pushConfig}};
	foreach my $pc (keys %pushConfig) {
		if($pc ne $self->{application_name}) {
			SWITCH: {
				($pushConfig{$pc}{type} eq 'dbi') && do {
					$self->_appendToLog("push updates: working on ".$pushConfig{$pc}{db_connect_string});
					
					my ($push_error, $push_errno) = (0, "");
					
					use DBI qw(:sql_types);
					use Date::Calc qw( Today_and_Now check_date check_time );
					
					my $dbh = DBI->connect(
						$pushConfig{$pc}{db_connect_string},
						$pushConfig{$pc}{db_username},
						$pushConfig{$pc}{db_password}
					);
					if($dbh) {
						
						my $sth = $dbh->prepare($pushConfig{$pc}{prepared_statement});
						if($sth) {
							
							
							my $i = 0;
							foreach my $param (@{$pushConfig{$pc}{evaled_bind_params_values}}) {
								# $self->_appendToLog("parametro ".($i+1).": ".eval($param)." - type: ".$pushConfig{$pc}{evaled_bind_params_types}[$i]);
								$sth->bind_param(++$i, eval($param), { TYPE => $pushConfig{$pc}{evaled_bind_params_types}[$i-1] });
							}
							
							my $ret = $sth->execute();
							if($ret) {
								if(defined($plaintextpassword)) {
									$self->_appendToLog("la password e' stata modificata e mi e' stata passata in plaintext: eseguo l'update");
		
									my $statement = "UPDATE ".$pushConfig{$pc}{users_table}." SET ".$pushConfig{$pc}{password_field}."=? WHERE ".$pushConfig{$pc}{username_field}."=?";
									my $sth = $dbh->prepare($statement);
		
									if($sth) {
										
										my $password;
										
										SWITCH: {
											($pushConfig{$pc}{passwordtype} eq "md5") && do {
												use Digest::MD5;
												my $ctx = Digest::MD5->new;
												$ctx->add($plaintextpassword);
												$password = $ctx->digest;
												last SWITCH;
											};
											($pushConfig{$pc}{passwordtype} eq "hexmd5") && do {
												use Digest::MD5;
												my $ctx = Digest::MD5->new;
												$ctx->add($plaintextpassword);
												$password = $ctx->hexdigest;
												last SWITCH;
											};
											($pushConfig{$pc}{passwordtype} eq "b64md5") && do {
												use Digest::MD5;
												my $ctx = Digest::MD5->new;
												$ctx->add($plaintextpassword);
												$password = $ctx->b64digest;
												last SWITCH;
											};
											($pushConfig{$pc}{passwordtype} eq "ucplaintext") && do {
												$password = uc($plaintextpassword);
												last SWITCH;
											};
											($pushConfig{$pc}{passwordtype} eq "lcplaintext") && do {
												$password = lc($plaintextpassword);
												last SWITCH;
											};
											# $pushConfig{$pc}{users_table} eq "plaintext"
											$password = $plaintextpassword;
										}
										
										$sth->bind_param(1, $password);
										$sth->bind_param(2, $user{username});
										my $ret = $sth->execute();
										if($ret) {
											# update della password eseguito con successo
										}
										else {
											$push_errno = 1005;
											$push_error = "Impossibile eseguire l'update della password: ".$dbh->errstr; 
										}
									}
									else {
										$push_errno = 1004;
										$push_error = "Impossibile preparare l'update della password: ".$dbh->errstr; 
									}
								}
							}
							else {
								$push_errno = 1003;
								$push_error = "Impossibile eseguire l'update: ".$dbh->errstr; 
							}
						}
						else {
							$push_errno = 1002;
							$push_error = "Impossibile preparare l'update: ".$dbh->errstr; 
						}
					}
					else {
						$push_errno = 1001;
						$push_error = "Impossibile connettersi al database: ".$DBI::errstr; 
					}
					if(!$push_error) {
						# update eseguito con successo
						$self->_appendToLog("Propagazione delle modifiche sul sistema \"".$pc."\" avvenuta con successo.");
					}
					else {
						# inviare email di notifica errore
						use Data::Dumper;
						my $body;
						$body = "Errore nella propagazione delle modifiche sul sistema \"".$pc."\"\n";
						$body .= "Error Number: ".$push_errno."\n";
						$body .= "Error String: ".$push_error."\n";
						$body .= "Contenuto dell'hash table \%user = ".Dumper(\%user)."\n";
						if($pushConfig{$pc}{plaintextpassword} && defined($plaintextpassword)) {
							$body .= "Plaintextpassword: ".$plaintextpassword."\n";
						}
						
						use MIME::Lite;
						my $msg = MIME::Lite->new(
							To => $pushConfig{$pc}{notify_email},
							Subject => "[SSO] Errore nella propagazione delle modifiche sul sistema \"".$pc."\"",
							Data => $body
						);
						$msg->send();
		
						$self->_appendToLog("Errore nella propagazione delle modifiche sul sistema \"".$pc."\": ".$push_errno." - ".$push_error, "warning");
					}
					last SWITCH;
				};
				# unsupported type
				$self->_appendToLog("Impossibile propagare le modifiche sul sistema \"".$pc."\": type \"".$pushConfig{$pc}{type}."\" non supportato.", "warning");
			}
		}
	}
}

sub searchUsers {

	my $self = shift;
	my $filter = shift;
	
	my ($subjectid, $identity_name);
	# $self->_appendToLog('searching users by admin account');
	$subjectid = $self->_getAdminSubjectId();
	if(!$subjectid) {
		return 0;
	}

	my $url = $self->{serviceUrl}."/identity/search";
	if($self->{testlb}) {
		$url = "http://testartnet.sirtisistemi.net/lbsso/identity/search";
	}
	
  my $connection = LWP::UserAgent->new();
  $connection->agent("SIRTI::OSSOAuth/".$SIRTI::OSSOAuth::VERSION." ");
  my $req = HTTP::Request->new(POST => $url);
  $req->content_type('application/x-www-form-urlencoded');

	use URI::Escape;
	my $content = "admin=".uri_escape($subjectid)."&";
	$content .= "attributes_names=objectclass&attributes_values_objectclass=person&";
	$content .= "filter=".uri_escape($filter)."&";
	$content = substr($content, 0, -1);
	
	$req->content($content);
	$self->_appendToLog('search users REST message body: '.$content);

	my $res = $connection->request($req);

	$self->_releaseAdminSubjectId($subjectid);

	if($res->is_success()) {
		my @users;
		my @lines = split(/\n/, $res->content());
		for(my $i=0; $i<@lines; $i++) {
			if($lines[$i] =~ /^string=(.*)$/) {
				push @users, uc($1);
			}
		}
		return sort @users;
	}
	else {
		$res->status_line() =~ /^([\d]+) (.*)$/;
		$self->{_errno} = $1;
		$self->{_error} = $2;
		return 0;
	}
 	
}

sub getUserData {

	my $self = shift;
	my $uid = shift;
	
	my ($subjectid, $identity_name);
	# $self->_appendToLog('get user data by admin account');
	$subjectid = $self->_getAdminSubjectId();
	if(!$subjectid) {
		my %user = ();
		return %user;
	}

	my $url = $self->{serviceUrl}."/identity/read";
	if($self->{testlb}) {
		$url = "http://testartnet.sirtisistemi.net/lbsso/identity/read";
	}
	
  my $connection = LWP::UserAgent->new();
  $connection->agent("SIRTI::OSSOAuth/".$SIRTI::OSSOAuth::VERSION." ");
  my $req = HTTP::Request->new(POST => $url);
  $req->content_type('application/x-www-form-urlencoded');

	use URI::Escape;
	my $content = "admin=".uri_escape($subjectid)."&";
	$content .= "name=".uri_escape(lc($uid))."&";
	$content .= "attributes_names=uid&";
	$content .= "attributes_names=sn&";
	$content .= "attributes_names=givenname&";
	$content .= "attributes_names=inetuserstatus&";
	$content .= "attributes_names=sirtimemberof&";
	$content .= "attributes_names=sirtiforcepasswordchange&";
	$content .= "attributes_names=sirtiuserfirstlogin&";
	$content .= "attributes_names=sirtiuserlastlogin&";
	$content .= "attributes_names=sirtiusercreated&";
	$content .= "attributes_names=sirtiuserlastpasswords&";
	$content .= "attributes_names=sirtiapplications&";
	$content .= "attributes_names=sirtiapplicationlastlogin&";
	$content .= "attributes_names=userpassword&";
	$content .= "attributes_names=cn&";
	$content .= "attributes_names=mail&";
	$content .= "attributes_names=telephonenumber&";
	$content .= "attributes_names=facsimiletelephonenumber&";
	$content .= "attributes_names=objectclass&";
	$content = substr($content, 0, -1);
	
	$req->content($content);
	$self->_appendToLog('get user data REST message body: '.$content);

	my $res = $connection->request($req);

	$self->_releaseAdminSubjectId($subjectid);

	if($res->is_success()) {
		$self->_appendToLog('get user data REST answer: '.$res->content());
		# print ${$self->{cgi}}->pre($res->content);
		my %user;
		my @lines = split(/\n/, $res->content());
		for(my $i=0; $i<@lines; $i++) {
			if($lines[$i] =~ /^identitydetails.attribute.name=(.*)$/) {
				my $name = $1;
				my @value = ();
				while($lines[$i+1] =~ /^identitydetails.attribute.value=(.*)$/) {
					push @value, $1;
					$i++;
				}
				$user{$name} = \@value;
			}
		}
		$user{'uid'}[0] = uc($user{'uid'}[0]);
		return %user;
	}
	else {
		$res->status_line() =~ /^([\d]+) (.*)$/;
		$self->{_errno} = $1;
		$self->{_error} = $2;
		# TODO controllare il valore di ritorno
		
		my %user = ();
		return %user;
	}
 	
}

sub logout {

	my $self = shift;

	my $session = $self->{session};
	my $url = $self->{serviceUrl}."/identity/logout";
	if($self->{testlb}) {
		$url = "http://testartnet.sirtisistemi.net/lbsso/identity/logout";
	}
	
  my $connection = LWP::UserAgent->new();
  $connection->agent("SIRTI::OSSOAuth/".$SIRTI::OSSOAuth::VERSION." ");
  my $req = HTTP::Request->new(POST => $url);
  $req->content_type('application/x-www-form-urlencoded');

	my @cookieNamesToForward = $self->_getCookieNamesToForward();
	$self->_forwardCookies(\$req, \@cookieNamesToForward);

	my $content = "dummy";
	$req->content($content);
	
	my $res = $connection->request($req);

	$self->{subjectId} = undef;
	$self->{username} = undef;
	$self->{attributes} = {};

	if(ref($self->{cgi})) {
		${$session}->clear('sso_attributes');
		${$session}->clear('subjectId');
		${$session}->clear('lastIDPcheck');
		${$session}->clear('username');
	}
	else {
		delete ${$session}->{sso_attributes};
		delete ${$session}->{subjectId};
		delete ${$session}->{lastIDPcheck};
		delete ${$session}->{username};
	}

}

sub _isAuthenticated {

	my $self = shift;

	my $url = $self->{serviceUrl}."/identity/isTokenValid";
	if($self->{testlb}) {
		$url = "http://testartnet.sirtisistemi.net/lbsso/identity/isTokenValid";
	}

	my $authenticated = 0;
	
  my $connection = LWP::UserAgent->new();
  $connection->agent("SIRTI::OSSOAuth/".$SIRTI::OSSOAuth::VERSION." ");
  my $req = HTTP::Request->new(POST => $url);
  $req->content_type('application/x-www-form-urlencoded');

	my @cookieNamesToForward = $self->_getCookieNamesToForward();
	$self->_forwardCookies(\$req, \@cookieNamesToForward);

	my $content = "dummy";
	$req->content($content);
	
	my $res = $connection->request($req);
	if($res->is_success()) {
		if(index(lc($res->content), "boolean=true") != -1) {
			$authenticated = 1;
		}
	}

	$self->_appendToLog("_isAuthenticated: ".$authenticated);
	return $authenticated;

}

sub _forwardCookies {

	my $self = shift;
	my $req = shift;
	my $cookieNames = shift;
	
	my $sb;
	my %cookies = $self->_getCookies();
	
	foreach my $cookieName (@{$cookieNames}) {
		if(defined($cookies{$cookieName})) {
			$sb .= $cookieName."=".$cookies{$cookieName}.";";
		}
	}
	if($sb) {
		${$req}->header("Cookie" => $sb);
	}

}

sub _getSubjectId {

	my $self = shift;

	my $cookieName = $self->_getTokenCookieName();
  my %cookies = $self->_getCookies();
  
	return $cookies{$cookieName};

}

sub _getAttributes {
	
	my $self = shift;

	if(!defined($self->{attributes}{'uid'}[0])) {
		my $url = $self->{serviceUrl}."/identity/attributes";
		if($self->{testlb}) {
			$url = "http://testartnet.sirtisistemi.net/lbsso/identity/attributes";
		}
		
	  my $connection = LWP::UserAgent->new();
	  $connection->agent("SIRTI::OSSOAuth/".$SIRTI::OSSOAuth::VERSION." ");
	  my $req = HTTP::Request->new(POST => $url);
	  $req->content_type('application/x-www-form-urlencoded');
	
		my @cookieNamesToForward = $self->_getCookieNamesToForward();
		$self->_forwardCookies(\$req, \@cookieNamesToForward);
	
		my $content = "dummy";
		$req->content($content);
		
		my $res = $connection->request($req);
		# print $res->as_string();
		$self->_appendToLog("sto ricaricando gli attributi");
		
		if($res->is_success()) {
			$self->_appendToLog('attributes REST answer: '.$res->content());
			# print ${$self->{cgi}}->pre($res->content);
			# $self->_appendToLog($res->content());
			$self->{attributes} = {};
			my @lines = split(/\n/, $res->content());
			for(my $i=0; $i<@lines; $i++) {
				if($lines[$i] =~ /^userdetails.attribute.name=(.*)$/) {
					my $name = $1;
					my @value = ();
					while($lines[$i+1] =~ /^userdetails.attribute.value=(.*)$/) {
						push @value, $1;
						$i++;
					}
					$self->{attributes}{$name} = \@value;
				}
			}
			$self->{attributes}{'uid'}[0] = uc($self->{attributes}{'uid'}[0]);
		}
	}

	# print ${$self->{cgi}}->pre(Dumper($self->{attributes}));

	my %tmp = $self->getUserData($self->{attributes}{'uid'}[0]);
	$self->{attributes} = \%tmp;

	# print ${$self->{cgi}}->pre(Dumper(\%tmp));
	
	return 1;

}

sub _getCookieNamesToForward {

	my $self = shift;

	my $url = $self->{serviceUrl}."/identity/getCookieNamesToForward";
	if($self->{testlb}) {
		$url = "http://testartnet.sirtisistemi.net/lbsso/identity/getCookieNamesToForward";
	}
	
	my @nameSet = ();
	
	my $ua = LWP::UserAgent->new;
	$ua->agent("SIRTI::OSSOAuth/".$SIRTI::OSSOAuth::VERSION." ");
	my $req = HTTP::Request->new(POST => $url);
	my $res = $ua->request($req);
	if ($res->is_success) {
		$self->_appendToLog("_getCookieNamesToForward success: ".$res->content());
		my @parts = split(/\n/, $res->content());
		foreach my $part ( @parts ) {
			if($part =~ /^string=(.*)$/) {
				push @nameSet, $1;
			}
		}
	}
	else {
		$self->_appendToLog("_getCookieNamesToForward failed: ".$res->status_line());
	}
	
	return @nameSet;

}

sub _getTokenCookieName {
	
	my $self = shift;

	my $url = $self->{serviceUrl}."/identity/getCookieNameForToken";
	if($self->{testlb}) {
		$url = "http://testartnet.sirtisistemi.net/lbsso/identity/getCookieNameForToken";
	}
	
	my $cookieName = undef;
	
  my $ua = LWP::UserAgent->new;
  $ua->agent("SIRTI::OSSOAuth/".$SIRTI::OSSOAuth::VERSION." ");
  my $req = HTTP::Request->new(POST => $url);
  my $res = $ua->request($req);
  if ($res->is_success) {
		my @parts = split /\n/, $res->content;
		foreach my $part ( @parts ) {
			if($part =~ /^string=(.*)$/) {
				$cookieName = $1;
				last;
			}
		}
  }
  
  return $cookieName;
  
}

sub _getCookies {

	my $self = shift;

	my %cookies = ();

	if(ref($self->{cgi})) {
		my @cookieNames = ${$self->{cgi}}->cookie();
		foreach my $cookie (@cookieNames) {
			$cookies{$cookie} = ${$self->{cgi}}->cookie($cookie);
			$cookies{$cookie} =~ s/ /+/g;
		}
	}
	else {
		my $tmp = ${$self->{request}}->Cookies();
		foreach my $k (keys %{$tmp}) {
			#	print $k." -- ".ref($tmp->{$k})."\n";
			if(ref($tmp->{$k}) eq "HASH") {
				foreach my $kk (keys %{$tmp->{$k}}) {
					$cookies{$k} = $kk."=".${$tmp->{$k}}{$kk};
					$cookies{$k} =~ s/ /+/g;
				}
			}
			else {
				$cookies{$k} = $tmp->{$k};
			}
		}
	}
	
	return %cookies;

}

sub _releaseAdminSubjectId {
	
	my $self = shift;
	my $adminsubjectid = shift;
	
	my $url = $self->{serviceUrl}."/identity/logout";
	if($self->{testlb}) {
		$url = "http://testartnet.sirtisistemi.net/lbsso/identity/logout";
	}
	
	my $connection = LWP::UserAgent->new();
	$connection->agent("SIRTI::OSSOAuth/".$SIRTI::OSSOAuth::VERSION." ");
	my $req = HTTP::Request->new(POST => $url);
	$req->content_type('application/x-www-form-urlencoded');

	use URI::Escape;
	my $content;

	$content = "subjectid=".uri_escape($adminsubjectid);

	$req->content($content);

	my $res = $connection->request($req);

	if($res->is_success()) {
		return 1;
	}
	else {
		$res->status_line() =~ /^([\d]+) (.*)$/;
		$self->{_errno} = $1;
		$self->{_error} = $2;
		return 0;
	}

}

sub _getAdminSubjectId {
	
	my $self = shift;

	my $url = $self->{serviceUrl}."/identity/authenticate";
	if($self->{testlb}) {
		$url = "http://testartnet.sirtisistemi.net/lbsso/identity/authenticate";
	}
	
	my $connection = LWP::UserAgent->new();
	$connection->agent("SIRTI::OSSOAuth/".$SIRTI::OSSOAuth::VERSION." ");
	my $req = HTTP::Request->new(POST => $url);
	$req->content_type('application/x-www-form-urlencoded');

	use URI::Escape;
	my $content;
	$content .= "username=".uri_escape(lc($self->{adminUsername}))."&";
	$content .= "password=".uri_escape($self->{adminPassword})."&";
	$content = substr($content, 0, -1);
	
	$req->content($content);

	my $res = $connection->request($req);

	if($res->is_success()) {
		$res->content =~ /^token\.id=(.*)$/;
		return $1;
	}
	else {
		$res->status_line() =~ /^([\d]+) (.*)$/;
		$self->{_errno} = $1;
		$self->{_error} = $2;
		return 0;
	}
}

sub error {
	my $self = shift;
	if (ref $self) {
		return $self->{_error};
	}
	else {
		return $error;
	}
}

sub errno {
	my $self = shift;
	if (ref $self) {
		return $self->{_errno};
	}
	else {
		return $errno;
	}
}

sub _appendToLog {
	
	my $self = shift;
	my $message = shift;
	my $type = shift;
	
	if(!$type) {
		$type = "info";
	}
	
	if($type eq "error" || $type eq "warning" || $self->{debug}) {
		if(ref($self->{cgi})) {
			use Date::Calc qw( Today_and_Now );
			printf STDERR "[%04d-%02d-%02d %02d:%02d:%02d] [%s] [sso] %s\n", Today_and_Now(), $type, $message;
		}
		else {
			${$self->{response}}->AppendToLog("[".$type."] ".$message);
		}
	}
}

1;
