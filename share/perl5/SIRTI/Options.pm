package SIRTI::Options;


use strict;
use warnings;
use integer;
use English '-no_match_vars';
use Carp;
use Getopt::Long qw(GetOptionsFromArray GetOptionsFromString);
use Tie::IxHash;
use Storable;

use  constant _PARAM_NOT_OPTIONS => qw ( header_description footer_description default_env_association  default_env_prefix space_tabulator help_option_key margin_spaces);

my %_PARAM_DEFVAL=(
	space_tabulator		=> 4
	,help_option_key	=> 'help'
	,margin_spaces		=> 0
);

sub _get_options_from_obj {
	my $obj=shift;
	croak "obj non di tipo ".__PACKAGE__ unless ref($obj) eq __PACKAGE__;
	return $obj->_prepare;
}

sub _filter_null { 
	return grep(defined $_ && length($_) > 0,@_);
}

sub _dup_data {
	my $s=shift;
	return undef unless defined $s;
	my ($d,$e)=($Storable::Deparse,$Storable::Eval);
	$Storable::Deparse=$Storable::Eval=1;
	my $dup=Storable::dclone($s);	  #copia profonda compresi i CODE 
	$Storable::Deparse=$d;
	$Storable::Eval=$e;
	return $dup;
}

sub _norm {
	my ($v,%params)=@_;
	while (ref($v) eq 'SCALAR') {
		$v=$$v;
	}
	while (ref($v) eq 'CODE') {
		$v=$v->(%params);
	}
	$v=[ keys %$v ] if ref($v) eq 'HASH';
	return _filter_null(@$v) if ref($v) eq 'ARRAY';
	return  _filter_null(split("\n",$v)) if ref($v) eq '';
	return _norm($v,%params);
}

sub _prepare_env {
	my ($opt,%params)=@_;
	my $e=$opt->{env_association};
	if (defined $e) {
		my @e=_norm($e,%params);
		my @fe=map {  
			my $e=$_;
			$e=$params{default_env_prefix}.$e if defined $params{default_env_prefix};
			$e;
		} grep(defined $_ && length($_) > 0,@e);
		$e=\@fe;
	}
	elsif ($params{default_env_association}) {
	 	$e=defined $params{default_env_prefix} ? $params{default_env_prefix} : '';
		my $k=$opt->{key};
		$k=uc($k) if defined $opt->{default_env_prefix} && $opt->{default_env_prefix}=~/^[A-Z][A-Z0-9_]+$/;
		$e.=$k;
		$e=[ $e ];
	}
	else {
		$e=[];
	}
	return $e;
}


sub _prepare_opt {
	my $opt=_dup_data(shift @_);
	my %params=@_;
	$opt->{option_value}=sub {
		my $v=shift;
		$v=~s/_/-/g; #trasforma gli underscore in minus per std nome opzioni
		$v;
	}->($opt->{key}) unless defined $opt->{option_value};
	$opt->{type}='' unless defined $opt->{type};
	$opt->{type}='='.$opt->{type} if $opt->{type}=~/^\w/;
	$opt->{getoptlong_option}=$opt->{option_value}.$opt->{type};
	$opt->{option_value_description}=$opt->{getoptlong_option} unless defined $opt->{option_value_description};
	$opt->{option_value_description}=[ _norm($opt->{option_value_description},%params) ];
	$opt->{description}='' unless defined $opt->{description};
	$opt->{description}=[ _norm($opt->{description},%params) ];
	$opt->{env_association}=_prepare_env($opt,%params);
	if (defined $opt->{default_value}) {
		my $r=ref($opt->{default_value});
		my ($v,$n)=($opt->{default_value},undef);
		if ($opt->{is_list_values}) {
			$n=[ $v ] if $r eq '';
			$n=[ keys %$v ] if $r eq 'HASH';
			$n=[ $$v ] if $r eq 'SCALAR';
			$n=$v if $r eq 'ARRAY';
		}
		else {
			$n=$v if $r eq 'SCALAR';
			$n=\$v if $r eq '';
		}
		croak "$r:  default_value di '".$opt->{key}."' non valido" unless defined $n;
		$opt->{default_value}=$n;
	}

	for my $k ( qw( pre_check_handle check_handle  post_check_handle) ) {
		my $f=$opt->{$k};
		next unless defined $f;
		croak "$k non e' una sub" unless ref($f) eq 'CODE';
		$f->($opt,%params) if $k eq 'pre_check_handle';
	}

	return $opt;
}

sub _prepare {
	my $self=shift;
	return $self->{_prepared} if  $self->{_prepared};
	my $h=new Tie::IxHash;
	if (defined $self->{merge_before}) {	 #prepara i parametri e le opzioni prima 
		my $a=ref($self->{merge_before}) eq 'ARRAY' ? $self->{merge_before} : [ $self->{merge_before} ];
		for my $obj(@$a) {
			my $p=_get_options_from_obj($obj);
			for my $k(_PARAM_NOT_OPTIONS) {
				$self->{_prepared}->{params}->{$k}=$p->{params}->{$k} if defined $p->{params}->{$k};
			}
			for my $k($p->{options}->Keys) {
				$h->Push($k,$p->{options}->Values($p->{options}->Indices($k)));
			}
		}
	}

	for my $k(_PARAM_NOT_OPTIONS)  {  #prepara i parametri
		$self->{_prepared}->{params}->{$k}=$self->{$k} if defined $self->{$k};
	}

	my $a=ref($self->{options}) eq 'ARRAY' ? $self->{options} : [ $self->{options} ]; #prepara le opzioni 
	for my $i(0..scalar(@$a) - 1) {
		my $opt=$a->[$i];
		croak "l'elemento $i delle opzioni non e' un hash" unless ref($opt) eq 'HASH';
		my $key=$opt->{key};
		croak "l' elemento $i delle opzioni non ha un attributo key" unless defined $key;
		my $newopt=_prepare_opt($opt,%{$self->{_prepared}->{params}});
		$h->Push($key,$newopt);
	}

	if (defined $self->{merge_after}) {	   #prepara i parametri e le opzioni dopo
		my $a=ref($self->{merge_after}) eq 'ARRAY' ? $self->{merge_after} : [ $self->{merge_after} ];
		for my $obj(@$a) {
			my $p=_get_options_from_obj($obj);
			for my $k(_PARAM_NOT_OPTIONS) {
				$self->{_prepared}->{params}->{$k}=$p->{params} unless  defined $self->{_prepared}->{params}->{$k};
			}
			for my $k($p->{options}->Keys) {
				my $v=$h->Values($h->Indices($k));
				next if defined $v;
				$h->Push($k,$p->{options}->Values($p->{options}->Indices($k)));
			}
		}
	}

	for my $k(keys %_PARAM_DEFVAL) {		  #aggiuge i valori di default ai parametri
		$self->{_prepared}->{params}->{$k}=$_PARAM_DEFVAL{$k} 
			unless defined $self->{_prepared}->{params}->{$k};
	}

	for my $k ( qw( header_description footer_description ) ) {
		my $p=$self->{_prepared};
		my @a=_norm($p->{params}->{$k},%{$p->{params}});
		$p->{params}->{$k}=\@a;
	}

	$self->{_prepared}->{params}->{space_tabulator}=$_PARAM_DEFVAL{space_tabulator}
		unless $self->{_prepared}->{params}->{space_tabulator}=~/^\d+$/;

	$self->{_prepared}->{params}->{margin_spaces}=$_PARAM_DEFVAL{margin_spaces}
		unless $self->{_prepared}->{params}->{margin_spaces}=~/^\d+$/;

	delete $self->{_prepared}->{params}->{help_option_key} 	#cancella la chiave help_option_key se non  e' definita
		unless defined $h->Indices($self->{_prepared}->{params}->{help_option_key});

	$self->{_prepared}->{options}=$h;

	return $self->{_prepared};

}

sub _prepare_for_getopt {
	my ($self,%params)=@_;
	return $self->{_prepared}->{getopt} if $self->{_prepared}->{getopt};
	$self->_prepare(%params);
	my %opt=();	my %assoc=();
	my $undef=undef;
	for my $k($self->{_prepared}->{options}->Keys) {
		my $h=$self->{_prepared}->{options}->Values($self->{_prepared}->{options}->Indices($k));
		my $value=Storable::dclone($h->{is_list_values} ? [] : \$undef);
		my $k1=$h->{getoptlong_option};
#		my $k1=$h->{key};
		$opt{$k1}=$value;
		$assoc{$k1}=$h;
	}
	$self->{_prepared}->{getopt}={ opt => \%opt,assoc => \%assoc };
	return $self->{_prepared}->{getopt}->{opt};
}

sub _assoc_getopt_values {
	my ($self,$opt,%params)=@_;
	for my $k(keys %$opt) { #associa i valori delle variabili d' ambiente ed il default
		my $p=$opt->{$k};
		my $r=ref($p);
		croak "'$r'" unless $r=~/^(ARRAY|HASH|SCALAR)/;
		next if $r eq 'ARRAY' && scalar(@$p) > 0;
		next if $r eq 'HASH' && scalar(keys(%$p)) > 0;
		next if $r eq 'SCALAR' && defined $$p && length($$p) > 0;
		my $h=$self->{_prepared}->{getopt}->{assoc}->{$k};
		my @envs_keys=grep(defined $ENV{$_} && length($ENV{$_}) > 0,@{$h->{env_association}});
		my $value=undef;
		if (scalar(@envs_keys) > 0) {
			if ($r eq 'ARRAY') {
				my $value=[ scalar(@envs_keys) > 1 ? map {$ENV{$_}} @envs_keys : split(/\s+/,$ENV{$envs_keys[0]}) ];
			}
			elsif ($r eq 'HASH') {
				$value={ map { ($_,$ENV{$_}) } @envs_keys };
			}
			elsif ($r eq 'SCALAR') {
				$value=\$ENV{$envs_keys[0]};
			}
			else {
				croak "$r:  env_association di '".$opt->{key}."' non valido" unless defined $value;
			}
		}
		$value=$h->{default_value} unless defined $value; 
		$opt->{$k}=$value if defined $value;
	}
	return undef;
}		


sub _check_handles {
	my ($self,$opt,%params)=@_;
	my %errmsgs=();
	for my $k(keys %$opt) { 
		my $h=$self->{_prepared}->{getopt}->{assoc}->{$k};
		my ($f_check,$f_post)=($h->{check_handle},$h->{post_check_handle});
		if (defined $f_check) {
			my $r=$f_check->($opt->{$k},%params,opt => $k,user_def => $self->{_prepared}->{getopt}->{assoc});
			if ($r) {
				$r=[ $r ] if ref($r) eq '';
				$errmsgs{$k}=$r;
				next;
			}
		}
		$f_post->($opt->{$k},%params,opt => $k,user_def => $self->{_prepared}->{getopt}->{assoc})
			if $f_post;
	}		
	return scalar(keys(%errmsgs)) == 0 ? undef : \%errmsgs;
}

sub _check_mandatory_values {
	my ($self,$opt,%params)=@_;
	my %errmsgs=();
	for my $k(keys %$opt) { #associa i valori delle variabili d' ambiente
		my $h=$self->{_prepared}->{getopt}->{assoc}->{$k};
		next unless $h->{mandatory};
		my $p=$opt->{$k};
		my $r=ref($p);
		if ($r eq 'HASH' && scalar(keys %$p) == 0  || $r eq 'ARRAY' && scalar(@$p) == 0 || $r  eq 'SCALAR' && !defined $$p) {
			my @s=();
			push @s,"l' opzione ",join("\n",@{$h->{option_value_description}})," e' obbligatoria";
			push @s,"associarle un valore  oppure impostare una delle seguenti variabili d' ambiente: ",join(" ",map { "'".$_."'" } @{$h->{env_association}})
				if scalar @{$h->{env_association}} > 1;
			push @s,"associarle un valore oppure impostare la variabile d' ambiente: ",join("",map { "'".$_."'" } @{$h->{env_association}})
				if scalar @{$h->{env_association}} == 1;
			$errmsgs{$k}=\@s;
		}
	}
	return scalar(keys(%errmsgs)) == 0 ? undef : \%errmsgs;
}

sub _getopt {
	my ($self,%params)=@_;
	my $prep=$self->_prepare_for_getopt(%params);
	my $opt=Storable::dclone($prep);
	my $r=sub {
		my $p=shift;
		$p=\@ARGV unless defined $p;
		return GetOptionsFromArray($p,%$opt) if ref($p) eq 'ARRAY';
		return GetOptionsFromString($p,%$opt);
	}->($params{get_options_from});
	return ('errore getopt',$opt) unless $r;
	$r=$self->_assoc_getopt_values($opt,%params);
	return ($r,$opt) if $r;
	$r=$self->_check_handles($opt,%params);
	return ($r,$opt) if $r;
	$r=$self->_check_mandatory_values($opt,%params);
	return ($r,$opt) if $r;
	return ($r,$opt);

}

##parametri:
#    header_description                         => testo di intestazione per costruire l' usage
#    footer_description                         => testo di coda per costruire l' usage
#    default_env_association                    => se vero per default le opzioni sono associate ad una variabile d' ambiente
#                                                  per specificare una opzione non assegnata ad una variabile d'ambiente 
#                                                  impostarla a valore stringa nulla
#    default_env_prefix                         => prefisso nome variabili d' ambiente - per default nessuno
#                                                  se il prefisso ha tutte lettere maiuscole allora tutte le variabile d' ambiente 
#                                                  autocostruite avranno il nome tutto maiuscolo
#    merge_before                               => oggetto o array di altri oggetti SIRTI::Options da cui prendere le opzioni
#    options                                    =>  array di hash contenenti le opzioni desiderate
#          opzione                              => hash di valore:
#            key                                => obbligatorio: nome univoco dell' opzione 
#            option_value                       => valore opzione da dare in pasto a Getopt::Long - per default il valore di key 
#            option_value_description           => nome dell' opzione  per usage - per default il valore di option_value + type
#            description                        => descrizione dell' opzione - se non specificato nessuna descrizione viene associata
#                                                  la descrizione viene usata per generare l' usage (vedi metodo usage)
#            is_list_values                     => l' opzione e' una lista di valori
#            env_association                    => nome variabile d' ambiente associata all' opzione 
#                                                  se 'default_env_association' e' vero e 'env_association' e' undef 
#                                                  la variabile d' ambiente associata viene costruita dal valore di 'default_env_prefix' unito a key
#                                                  se 'env_association' e' stringa nulla nessuna variabile d' ambiente e' associata
#            type                               => tipo associato (vedi  Getopt::Long) 
#                                                  per default nessun tipo e' associato (l' opzione e' considerata un boolean)
#            default_value  <value>             => se l' opzione non e' valorizzata assume come valore <value>
#                                                  il default per value e' 'undef'
#                                                  default_value viene valutato prima di 'pre_check_handle' e dopo la 'env_association'
#            pre_check_handle                   => puntatore ad una sub per effettuare manipolazioni sull' opzione
#                                                  prima di effettuare un check - per default nessuna sub associata
#                                                  pre_check_handle viene eseguito dopo  'default_value'
#            check_handle                       => puntatore ad una sub che effettua un check di validita' sul valore dell' opzione
#                                                  la sub deve ritornare falso per check valido altrimenti un array di messaggi di errore (o un messaggio di errore)
#                                                  questa sub viene eseguita dopo pre_check_handle - per default nessuna sub associata 
#            post_check_handle                  => puntatore ad una sub per effettuare manipolazioni sulla opzione
#                                                  dopo avere effettuato un check con esito valido - per default nessuna sub associata
#                                                  post_check_handle viene eseguito dopo  check_handle              
#            mandatory                          => se vero l' opzione e' obbligatoria
#                                                  il valore dell' opzione viene valutato dopo post_check_handle
#                                                  e' una specie di check_handle specializzato
#   merge_after                                 => oggetto o array di altri oggetti SIRTI::Options da cui prendere le opzioni
#   help_option_key                             => <key>  - chiave per indicare l' opzione di help
#                                                  per default la chiave 'help' se definita nelle opzioni altrimenti nessun  aiuto 
#   space_tabulator                             => numero di spazi corrispondenti a un tab (default 4)    per usage
#   margin_spaces                               => numero di spazi margine (default 0)  per usage
# 
#
#Es:
#    my $opt=SIRTI::Options(
#        header_description    =>  'header'
#        ,footer_description    =>  'footer'
#        ,options    => [
#                    {
#                        key => 'verbose'
#                        ,description => 'abilita la modalita verbosa'
#                    }
#                    ,{
#                        key    => 'length'
#                        ,description => "specifica l' altezza del muro"
#                        ,env_association  => [ qw(MYAPPL_LENGTH  MYAPPL_HEIGHT)]
#                        ,option_value => 'length|height|l=f'
#                        ,option_value_description => 'length|height|l=<valore floating point>'
#                    }
#                ]
#

sub new {
	my ($class,%params)=@_;
	for my $k(keys %params) {
		delete $params{$k} if $k=~/^_/;	  #elimina le cache
	}
	return bless \%params,$class;
}

#
#restituisce in output una stringa da usage per l' usage
#
sub get_usage {
	my ($self,%params)=@_;
	$self->_prepare unless $self->{_prepared};
	return $self->{_prepared}->{usage} if defined $self->{_prepared}->{usage}; 
	my @lines=();
	my $p=$self->{_prepared};
	push @lines,@{$p->{params}->{header_description}};
	my $options=$p->{options};
	for my $k($options->Keys) {
		my $h=$options->Values($options->Indices($k));
		push @lines,map { "\t".$_ } @{$h->{option_value_description}};
		push @lines,map { "\t\t".$_ } @{$h->{description}};
		push @lines,"\t\tvariabile d' ambiente associata ".$h->{env_association}->[0] if scalar(@{$h->{env_association}}) == 1;
		push @lines,"\t\tvariabili d' ambiente associate: ".join(" ",@{$h->{env_association}})  if scalar(@{$h->{env_association}}) > 1;
		push @lines," ";
	}
	push @lines,@{$p->{params}->{footer_description}};
	my $tbspaces=' 'x$p->{params}->{space_tabulator};
	my $margin_spaces=' 'x$p->{params}->{margin_spaces};
	$self->{_prepared}->{usage}=join("\n",map { s/\t/$tbspaces/g; $margin_spaces.$_;} @lines);
	return $self->{_prepared}->{usage};
}

#
#esegue il parsing degli argomenti valutati dal parametro get_options_from se definito altrimenti da  @ARGV    
#parametri:
#      get_options_from    =>  puo essere una stringa od un array da cui effettuare il parsing
#                              per default prende @ARGV
#      exit_on_error       => <rc>  - in caso di errore esce con rc - per default il metodo ritorna una struttura di errori
#      auto_help_usage     => <rc>  - in caso l' utente immette l' opzione di aiuto il sistema stampa su STDOUT
#                             l' output del metodo get_usage ed esce con <rc>
#      print_msg_errors    => se vero stampa su stderr i messaggi di errore
#


sub parse_arguments {
	my ($self,%params)=@_;
	$self->_prepare unless $self->{_prepared};
	my ($errmsg,$opt)=$self->_getopt(%params);
	unless ($errmsg) {
		$self->{_prepared}->{getopt}->{opt}=$opt;
		if (defined $params{auto_help_usage}) {
			my $key=$self->{_prepared}->{params}->{help_option_key};
			if (defined $key && ${$opt->{$key}}) {
				my $rc=$params{auto_help_usage};
				$rc=255 unless $rc =~/^\d{1,3}$/;
				$rc=255 if $rc > 255;
				print STDOUT $self->get_usage,"\n";
				exit $rc;
			}
		}
		my $assoc=$self->{_prepared}->{getopt}->{assoc};
		my %user_opt=map { my $v=$opt->{$_}; ($assoc->{$_}->{key},ref($v)  eq 'SCALAR' ? $$v : $v) } keys %$opt;
		$self->{_prepared}->{user_getopt}=\%user_opt;
		return $errmsg;
	}
	if ($params{print_msg_errors}) {
		if (ref($errmsg) eq '') {
			print STDERR $errmsg,"\n";
		} 
		elsif (ref($errmsg) eq 'HASH') {
			for my $k(sort keys %$errmsg) {
				print STDERR "$k: ",join("\n",@{$errmsg->{$k}}),"\n";
			}
		}
		else {
			croak ref($errmsg).": errore interno";
		}
	}

	if (defined $params{exit_on_error}) {
		my $rc=$params{exit_on_error};
		$rc=255 unless $rc =~/^\d{1,3}$/;
		$rc=255 if $rc > 255;
		exit $rc;
	}

	delete $self->{_prepared}->{getopt}->{opt};
	return $errmsg;
}

sub getopt {
	my ($self,%params)=@_;
	my $opt=$self->{_prepared}->{getopt}->{opt};
	if (defined $opt) {
		return wantarray ? %$opt : $opt;
	}
	return wantarray ? () : undef;
}

sub user_getopt {
	my ($self,%params)=@_;
	my $opt=$self->{_prepared}->{user_getopt};
	if (defined $opt) {
		return wantarray ? %$opt : $opt;
	}
	return wantarray ? () : undef;
}

if (__FILE__ eq $0) {
	eval "use Data::Dumper";
	croak $@ if $@;

   	my $opt1=SIRTI::Options->new(
		header_description => "header1\nopzioni1:\n"
	    ,footer_description	=> 'footer1'
		,default_env_prefix => 'PROVA1_'
		,options =>  [
			{
				key	=> 'verbose'
				,description => 'abilita la modalita verbosa'
			}
			,
			{
				key	=> 'length'
				,description => "specifica l' altezza del muro"
				,env_association  => [ qw(MYAPPL_LENGTH  MYAPPL_HEIGHT)]
				,option_value => 'length|height|l=f'
				,option_value_description => 'length|height|l=<valore floating point>'
			}
		]
		,margin_spaces	=> 2
	);



	print STDERR $opt1->get_usage,"\n";


	my $opt2=SIRTI::Options->new(
		header_description => "header2\nopzioni2:\n"
	    ,footer_description	=> 'footer2'
		,default_env_prefix => 'PROVA2_'
		,options => [  
			{
					key							=> 'length'
					,description 				=> "specifica l' altezza del muro d'assalto"
					,env_association  			=> [ qw(MYAPPL_LENGTH_ASS  MYAPPL_HEIGHT_ASS)]
					,option_value 				=> 'length_ass|height_ass|l=f'
					,option_value_description 	=> 'length_ass|height_ass|l=<valore floating point>'
					,mandatory => 1
			}
			,
			{
					key							=> 't'
					,description 				=> "specifica la modalita di transazione"
					,env_association  			=> 'transaction_mode'
					,option_value_description 	=> 't=<c|r>'
					,default_value 				=> 'r'
					,type						=> 's'
					,pre_check_handle 			=> sub {
						my($opt,%params)=@_;
						print STDERR "pre check su ".$opt->{key},"\n";

					}
					,check_handle 				=> sub {
						my($pvalue,%params)=@_;
						return $$pvalue.": valore invalido - valori validi sono r|c" unless $$pvalue=~/^(r|c)$/;
						return undef;
					}
			}
			,
			{
					key => 'help'
			}

		]
		,margin_spaces	=> 0
		,merge_before => $opt1
 
	);


	#print STDERR $opt2->get_usage,"\n"; 


	my $errmsgs=$opt2->parse_arguments(auto_help_usage => 0);
	if (defined $errmsgs) {
		print STDERR "errore getopt\n";
		print STDERR Dumper($errmsgs);
	}
}


1;

