package SIRTI::PLSQL::CSF;
#
# interfaccia verso package pl/sql csf
#
use strict;
use warnings;
use Storable;
use SIRTI::Err;
use SIRTI::DB;
use Data::Dumper;
use DBI qw(:sql_types);
use DBD::Oracle qw(:ora_types);


sub _get_err {
	my ($self,$func,%params)=@_;
	my $db=$self->{DB};
	return $db->fetch_minimalized("select csf.$func from dual");
}


sub ERR_OK                                   { $_[0]->_get_err('ERR_OK'); }
sub ERR_COMUNE_NON_UNIVOCO                   { $_[0]->_get_err('ERR_COMUNE_NON_UNIVOCO'); }
sub ERR_INCONGR_CAP_COMUNE                   { $_[0]->_get_err('ERR_INCONGR_CAP_COMUNE'); }
sub ERR_INCONGR_PR_COMUNE                    { $_[0]->_get_err('ERR_INCONGR_PR_COMUNE'); }
sub ERR_INCONGR_CAP_PR_COMUNE                { $_[0]->_get_err('ERR_INCONGR_CAP_PR_COMUNE'); }
sub ERR_COMUNE_DIZIONE_ERRATA                { $_[0]->_get_err('ERR_COMUNE_DIZIONE_ERRATA'); }
sub ERR_COMUNE_NON_TROVATO                   { $_[0]->_get_err('ERR_COMUNE_NON_TROVATO'); }
sub ERR_FRAZIONE_NON_UNIVOCA                 { $_[0]->_get_err('ERR_FRAZIONE_NON_UNIVOCA'); }
sub ERR_INCONGR_CAP_FRAZIONE                 { $_[0]->_get_err('ERR_INCONGR_CAP_FRAZIONE'); }
sub ERR_INCONGR_PR_FRAZIONE                  { $_[0]->_get_err('ERR_INCONGR_PR_FRAZIONE'); }
sub ERR_INCONGR_CAP_PR_FRAZ                  { $_[0]->_get_err('ERR_INCONGR_CAP_PR_FRAZ'); }
sub ERR_FRAZIONE_DIZIONE_ERRATA              { $_[0]->_get_err('ERR_FRAZIONE_DIZIONE_ERRATA'); }
sub ERR_FRAZIONE_NON_TROVATA                 { $_[0]->_get_err('ERR_FRAZIONE_NON_TROVATA'); }
sub ERR_FRAZCOM_DIZIONE_ERRATA               { $_[0]->_get_err('ERR_FRAZCOM_DIZIONE_ERRATA'); }
sub ERR_FRAZCOM_NON_TROVATI                  { $_[0]->_get_err('ERR_FRAZCOM_NON_TROVATI'); }
sub ERR_INCONGR_CAP_STRADA                   { $_[0]->_get_err('ERR_INCONGR_CAP_STRADA'); }
sub ERR_INCONGR_PT_STRADA                    { $_[0]->_get_err('ERR_INCONGR_PT_STRADA'); }
sub ERR_STRADA_DIZIONE_ERRATA                { $_[0]->_get_err('ERR_STRADA_DIZIONE_ERRATA'); }
sub ERR_STRADA_NON_UNIVOCA                   { $_[0]->_get_err('ERR_STRADA_NON_UNIVOCA'); }
sub ERR_STRADA_NON_TROVATA                   { $_[0]->_get_err('ERR_STRADA_NON_TROVATA'); }
sub ERR_NCIV_NON_UNIVOCO                     { $_[0]->_get_err('ERR_NCIV_NON_UNIVOCO'); }
sub ERR_INCONGR_CAP_NCIV                     { $_[0]->_get_err('ERR_INCONGR_CAP_NCIV'); }
sub ERR_INCONGR_COLORE_NCIV                  { $_[0]->_get_err('ERR_INCONGR_COLORE_NCIV'); }
sub ERR_INCONGR_CAP_COLORE_NCIV              { $_[0]->_get_err('ERR_INCONGR_CAP_COLORE_NCIV'); }
sub ERR_NCIV_NON_TROVATO                     { $_[0]->_get_err('ERR_NCIV_NON_TROVATO'); }
sub ERR_NCIV_SNC                             { $_[0]->_get_err('ERR_NCIV_SNC'); }
sub ERR_ULIMIT								 { $_[0]->_get_err('ERR_ULIMIT'); }

sub new($$) {
	my ($class,%params)=@_;
		#DB							=> istanza di SIRTI::DB
		#CACHE_DATA					=> se vero scambia tempo di elaborazione con spazio in memoria (non implementato)		
	$params{DB}=new SIRTI::DB unless defined $params{DB};
	$params{CACHE}=$params{CACHE_DATA} ? {} : undef;
	return bless \%params,$class;
}


sub get_comune_caps {
	my ($self,$idcomune,%params)=@_;
	my $db=$self->{DB};
	unless (defined $self->{PREPARE}->{COMUNE_CAPS}) {
		$self->{PREPARE}->{COMUNE_CAPS}=$db->create_prepare("
			select cap from table(csf.comune_caps(?,null)) order by id,cap"
		) || return undef;
	}
	my $cur=$self->{PREPARE}->{COMUNE_CAPS}->create_cursor($idcomune) || return undef;

	my @caps=();
	while(my $r=$cur->fetchrow_hashref()) {
		push @caps,$r->{CAP};
	}				
	if (SIRTI::DB::iserror()) {
		$cur->finish();
		return undef;
	}	
	$cur->finish();
	return wantarray ? @caps : \@caps;
}


sub get_comune_prefissi {
	my ($self,$idcomune,%params)=@_;
	my $db=$self->{DB};
	unless (defined $self->{PREPARE}->{COMUNE_PREFISSI}) {
		$self->{PREPARE}->{COMUNE_PREFISSI}=$db->create_prepare("
			select prefisso from table(csf.comune_prefissi(?,null)) order by id,prefisso"
		) || return undef;
	}
	my $cur=$self->{PREPARE}->{COMUNE_PREFISSI}->create_cursor($idcomune) || return undef;

	my @e=();
	while(my $r=$cur->fetchrow_hashref()) {
		push @e,$r->{PREFISSO};
	}				
	if (SIRTI::DB::iserror()) {
		$cur->finish();
		return undef;
	}	
	$cur->finish();
	return wantarray ? @e : \@e;
}



sub get_frazione_prefissi {
	my ($self,$idfrazione,%params)=@_;
	my $db=$self->{DB};
	unless (defined $self->{PREPARE}->{FRAZIONE_PREFISSI}) {
		$self->{PREPARE}->{FRAZIONE_PREFISSI}=$db->create_prepare("
			select prefisso from table(csf.frazione_prefissi(?,null)) order by id,prefisso"
		) || return undef;
	}
	my $cur=$self->{PREPARE}->{FRAZIONE_PREFISSI}->create_cursor($idfrazione) || return undef;

	my @e=();
	while(my $r=$cur->fetchrow_hashref()) {
		push @e,$r->{PREFISSO};
	}				
	if (SIRTI::DB::iserror()) {
		$cur->finish();
		return undef;
	}	
	$cur->finish();
	return wantarray ? @e : \@e;
}


sub split_numtelcompl_old {
	my ($self,$numtelcompl,%params)=@_;
	my $db=$self->{DB};
	unless (defined $self->{PREPARE}->{SPLIT_NUMTELCOMPL}) {
		$self->{PREPARE}->{SPLIT_NUMTELCOMPL}=$db->create_prepare("
			declare
				cur csf.generic_cursor;
				r o_numtel_splitted;
			begin
				open cur for select * from table(csf.split_numtelcompl(:NC));
				fetch cur into r;
				:P := r.prefisso
				:N := r.numero;
			end;
			"
		) || return undef;
	}
	my $sth=$self->{PREPARE}->{SPLIT_NUMTELCOMPL}->get_sth;
	$sth->bind_param(':NC',$numtelcompl) || return undef;
	$sth->bind_param_inout( ":P", \my $prefisso, 10, { sql_type => SQL_VARCHAR } ) || return undef;
	$sth->bind_param_inout( ":N", \my $numero, 20, { sql_type => SQL_VARCHAR } ) || return undef;
	$sth->execute;
	my @a=($prefisso,$numero);
	return wantarray ? @a : \@a;
}


sub split_numtelcompl {
	my ($self,$numtelcompl,%params)=@_;
	my $db=$self->{DB};
	unless (defined $self->{PREPARE}->{SPLIT_NUMTELCOMPL}) {
		$self->{PREPARE}->{SPLIT_NUMTELCOMPL}=$db->create_prepare("
				select * from table(csf.split_numtelcompl(?))
			"
		) || return undef;
	}
	my $cur=$self->{PREPARE}->{SPLIT_NUMTELCOMPL}->create_cursor($numtelcompl);
	my $r=$cur->fetchrow_arrayref; 
	if (SIRTI::DB::iserror()) {
		$cur->finish();
		return undef;
	}
	my @a=@$r;
	$cur->finish();
	return wantarray ? @a : \@a;
}

sub do_check_comune_frazione {
	my ($self,%params)=@_;
	#COMUNE
	#FRAZIONE
	#PROVINCIA
	#CAP
	#ATTEMPT_SEARCH_RESOLUTION
	
	my ($db,$ok,$cur,$errcode)=($self->{DB},$self->ERR_OK,undef,undef);
	
	unless (defined $self->{PREPARE}->{CF_CHECK}) { 
		$self->{PREPARE}->{CF_CHECK}=$db->create_prepare("
			begin
				csf.comune_frazione_check(
					:cur
					,:errcode
					,:c_denom
					,:f_denom
					,:pr
					,:cap
					,:asr
				);
			end;
			"
		) || return undef;	
	}
	
	$db->get_dbms_output(*STDERR) if $db->can('get_dbms_output'); 
	my $sth=$self->{PREPARE}->{CF_CHECK}->get_sth() || return undef;
	$sth->bind_param_inout( ":cur", \$cur, 0, { ora_type => ORA_RSET } ) || return undef;
	$sth->bind_param_inout( ":errcode", \$errcode, 0,{ sql_type => SQL_NUMERIC } ) || return undef;
	$sth->bind_param(":c_denom", $params{COMUNE}, {sql_type => SQL_VARCHAR }) || return undef;
	$sth->bind_param(":f_denom", $params{FRAZIONE}, {sql_type => SQL_VARCHAR }) || return undef;
	$sth->bind_param(":pr",$params{PROVINCIA},{ sql_type => SQL_VARCHAR }) || return undef;;
	$sth->bind_param(":cap",$params{CAP},{ sql_type => SQL_VARCHAR }) || return undef;;
	$sth->bind_param(":asr",$params{ATTEMPT_SEARCH_RESOLUTION} ? 'S' : 'N',{ sql_type => SQL_CHAR }) || return undef;;
	$sth->execute() || return undef;
	
	my @comunario=();
	while (my $v = $cur->fetchrow_hashref()) {
		$v->{DENOMSTD}=$v->{DENOMINAZIONEAPICISTD} if exists $v->{DENOMINAZIONEAPICISTD}; 
		$v->{DENOMSTD}=$v->{C_DENOMINAZIONEAPICISTD} if exists $v->{C_DENOMINAZIONEAPICISTD}; 
		$v->{DENOMABBREV}=$v->{DENOMABBREVSTD} if exists $v->{DENOMABBREVSTD}; 
		$v->{DENOMABBREV}=$v->{C_DENOMABBREVSTD} if exists $v->{C_DENOMABBREVSTD}; 
		$v->{DENOMSTDQ}=$v->{DENOMINAZIONESTD} if exists $v->{DENOMINAZIONESTD};
		$v->{DENOMSTDQ}=$v->{C_DENOMINAZIONEAPICISTD} if exists $v->{C_DENOMINAZIONEAPICISTD};
		$v->{FR_DENOMSTD}=SIRTI::Err::nvl($v->{F_DENOMINAZIONEAPICISTD},$v->{F_DENOMINAZIONESTD}) if exists $v->{F_DENOMINAZIONESTD};
		$v->{TARGA}=delete $v->{PROVINCIA};
		$v->{CAPS}=$self->get_comune_caps(defined $v->{IDCOMUNE} ? $v->{IDCOMUNE} : $v->{ID}) || return undef;

		if (exists $v->{IDCOMUNE}) {  #e' una frazione 
			$v->{FR_CAPS}=$self->get_frazione_caps($v->{ID}) || return undef;		
		}
		push @comunario,$v; 

	}

	if (SIRTI::DB::iserror()) {
		$cur->finish();
		return undef;
	}	
	
	$cur->finish();
	my @err=(); 
	push @err,$errcode if $errcode != 	$ok;
	return wantarray ? (\@err,\@comunario) : [ \@err,\@comunario ];
}

sub get_frazione_caps {
	my ($self,$idfrazione,%params)=@_;
	my $db=$self->{DB};
	unless (defined $self->{PREPARE}->{FRAZIONE_CAPS}) {
		$self->{PREPARE}->{FRAZIONE_CAPS}=$db->create_prepare("
			select cap from table(csf.frazione_caps(?,null)) order by id,cap"
		) || return undef;
	}
	my $cur=$self->{PREPARE}->{FRAZIONE_CAPS}->create_cursor($idfrazione) || return undef;

	my @caps=();
	while(my $r=$cur->fetchrow_hashref()) {
		push @caps,$r->{CAP};
	}				
	if (SIRTI::DB::iserror()) {
		$cur->finish();
		return undef;
	}	
	$cur->finish();
	return wantarray ? @caps : \@caps;
}

sub get_strada_ncivs {
	my ($self,$idstrada,$idsestiere,%params)=@_;
	my $db=$self->{DB};
	my ($cur,$errcode);
	unless (defined  $self->{PREPARE}->{ARCHI}) {
		$self->{PREPARE}->{ARCHI}=$db->create_prepare("
			select * from table(csf.archi(?,?,null)) 
			order by 	id_strada
						,id_sestiere
						,ndal     
						,esponentedal nulls first
						,colore nulls first        
		") || return undef;
	}
	my $rows=$self->{PREPARE}->{ARCHI}->fetchall_hashref($idstrada,$idsestiere);
	return undef unless defined $rows;
	return wantarray ? @$rows : $rows;
}

sub _get_dug {
	my ($self,%params)=@_;
	my $db=$self->{DB};
	my $k=uc($params{NAME});
	unless (defined  $self->{PREPARE}->{$k}) {
		$self->{PREPARE}->{$k}=$db->create_prepare("
			select * from table(csf.".$params{NAME}."(?)) order by ".$params{ORDER_BY}."

		"
		) || return undef;
	}
	return $self->{PREPARE}->{$k}->fetchall_hashref($params{ID}); 
}	

sub get_abbreviazioni_dug {
	my ($self,%params)=@_;
	my $r=$self->_get_dug(%params,NAME =>  'abbreviazioni_dug',ORDER_BY => 'dugstd,dugabbreviatastd') || return undef;
	my @x=map { { DUGSTD => $_->{DUGSTD},DUGABBREVIATASTD => $_->{DUGABBREVIATASTD} }; } @$r;
	return wantarray ? @x : \@x;
}

sub get_dug_regexp {
	my ($self,%params)=@_;
	my $r=$self->_get_dug(%params,NAME =>  'dugs_regexp',ORDER_BY => 'nome,alias') || return undef;
	my @x=map { { NOME => $_->{NOME},ALIAS => $_->{ALIAS} }; } @$r;
	return wantarray ? @x : \@x;
}

sub get_dugstd {
	my ($self,%params)=@_;
	my $r=$self->_get_dug(%params,NAME =>  'dugs',ORDER_BY => 'dug') || return undef;
	my @x=map { $_->{DUG}; } @$r;
	return wantarray ? @x : \@x;
}

sub get_strada_parttopons {
	my ($self,$idstrada,$idsestiere,%params)=@_;
	#MATCH_TIPO_DENOMINAZIONE	=> [ 'A','S';'Q','B' ]  - A => alias,S => standard,Q => con apici,B breve
	my $db=$self->{DB};	
	unless (defined $self->{PREPARE}->{V_PT}) {
		$self->{PREPARE}->{V_PT}=$db->create_prepare("select parttopon pt,matchdenominazione md from table(csf.strada_topons(?,?))  order by parttopon") || return undef;
	}
	my $cur=$self->{PREPARE}->{V_PT}->create_cursor($idstrada,$idsestiere) || return undef;
	my @pt=();
	while(my $r=$cur->fetchrow_hashref()) {
		if (defined $params{MATCH_TIPO_DENOMINAZIONE}) {
			next if ref($params{MATCH_TIPO_DENOMINAZIONE}) eq 'ARRAY'  && scalar(grep($_ eq $r->{MD},@{$params{MATCH_TIPO_DENOMINAZIONE}})) == 0;
			next if ref($params{MATCH_TIPO_DENOMINAZIONE}) eq ''  && $r->{MD} ne $params{MATCH_TIPO_DENOMINAZIONE};
		}
		push @pt,$r->{PT};
	}
	if (SIRTI::DB::iserror()) {
		$cur->finish();
		return undef; 
	}
	$cur->finish();
	return wantarray ? @pt : \@pt;
}


sub get_strada_caps {
	my ($self,$idstrada,$idsestiere,%params)=@_;
	my $db=$self->{DB};
	unless (defined $self->{PREPARE}->{STRADA_CAPS}) {
		$self->{PREPARE}->{STRADA_CAPS}=$db->create_prepare("select cap from table(csf.strada_caps(?,?)) order by id_strada,id_sestiere,cap") || return undef;
	}
	my $cur=$self->{PREPARE}->{STRADA_CAPS}->create_cursor($idstrada,$idsestiere) || return undef;
	my @caps=();
	while(my $r=$cur->fetchrow_hashref()) {
		push @caps,$r->{CAP};
	}				
	if (SIRTI::DB::iserror()) {
		$cur->finish();
		return undef;
	}	
	$cur->finish();
	return wantarray ? @caps : \@caps;	
}

sub do_check_strada {
	my ($self,%params)=@_;
	#ID_COMUNE
	#PARTTOPON
	#DENOMINAZIONE
	#CAP
	#ATTEMPT_SEARCH_RESOLUTION 
	#PARTTOPON_RESTRICTED_CHECK
	
	my ($db,$ok)=($self->{DB},$self->ERR_OK);

	unless (defined $self->{PREPARE}->{CFV_CHECK}) {
		$self->{PREPARE}->{CFV_CHECK}=$db->create_prepare("
			begin
				open :cur for
				select * from table(csf.strada_check(
													 :id_comune
													,:parttopon
													,:denominazione
													,:cap
													,:asr
													,:prc
												)
								) order by id_comune  nulls first,id_strada desc,id_sestiere desc;
			end;
			"
		) || return undef;
	}
	
	my $sth=$self->{PREPARE}->{CFV_CHECK}->get_sth() || return undef;
	my $cur=undef;
	$sth->bind_param_inout( ":cur", \$cur, 0, { ora_type => ORA_RSET } ) || return undef;
	$sth->bind_param(":id_comune", $params{ID_COMUNE},{sql_type => SQL_NUMERIC } ) || return undef;
	$sth->bind_param(":parttopon", $params{PARTTOPON}, {sql_type => SQL_VARCHAR }) || return undef;
	$sth->bind_param(":denominazione",$params{DENOMINAZIONE},{ sql_type => SQL_VARCHAR }) || return undef;
	$sth->bind_param(":cap",$params{CAP},{ sql_type => SQL_VARCHAR }) || return undef;
	$sth->bind_param(":asr",$params{ATTEMPT_SEARCH_RESOLUTION} ? 'S' : 'N',{ sql_type => SQL_CHAR }) || return undef;
	$sth->bind_param(":prc",$params{PARTTOPON_RESTRICTED_CHECK} ? 'S' : 'N',{ sql_type => SQL_CHAR }) || return undef;
	$sth->execute() || return undef;	
	my @errcodes=();
	while (my $v = $cur->fetchrow_hashref()) {
		my $err =  $v->{ID_STRADA};
		last if $err == $ok;
		push @errcodes,$err;
	}
	
	if (SIRTI::DB::iserror()) {
		$cur->finish();
		$sth->finish();
		return undef;
	}	

	my @indirizzi=();
	while (my $v = $cur->fetchrow_hashref()) {
		$v->{CAP}=$self->get_strada_caps($v->{ID_STRADA},$v->{ID_SESTIERE}) || return undef;;
		push @indirizzi,$v; 
	}

	if (SIRTI::DB::iserror()) {
		$cur->finish();
		$sth->finish();
		return undef;
	}	

	$sth->finish();
	$cur->finish();
	return wantarray ? (\@errcodes,\@indirizzi) : [ \@errcodes,\@indirizzi ];
}




sub do_check_nciv {
	my ($self,%params)=@_;
	#ID_STRADA
	#ID_SESTIERE
	#NCIV
	#PARITA
	#EXP
	#COLORE
	#CAP
	#ATTEMPT_SEARCH_RESOLUTION
	
	my $db = $self->{DB};
	
	unless (defined $self->{PREPARE}->{NCIV_CHECK}) {
		$self->{PREPARE}->{NCIV_CHECK}=$db->create_prepare("
			begin
				csf.nciv_check(
					:cur
					,:errcode
					,:id_strada
					,:id_sestiere
					,:nciv
					,:parita
					,:exp
					,:colore
					,:cap
					,:asr
				);
			end;
		"
		) || return undef;	
	}

	my ($cur,$errcode);
	my $sth=$self->{PREPARE}->{NCIV_CHECK}->get_sth() || return undef;
	$sth->bind_param_inout( ":cur", \$cur, 0, { ora_type => ORA_RSET } ) || return undef;
	$sth->bind_param_inout( ":errcode", \$errcode, 0,{ sql_type => SQL_NUMERIC } ) || return undef;
	$sth->bind_param(":id_strada", $params{ID_STRADA},{ sql_type => SQL_NUMERIC} ) || return undef;
	$sth->bind_param(":id_sestiere", $params{ID_SESTIERE},{ sql_type => SQL_NUMERIC} ) || return undef;
	$sth->bind_param(":nciv",$params{NCIV},{ sql_type => SQL_VARCHAR }) || return undef;;
	$sth->bind_param(":parita",$params{PARITA},{ sql_type => SQL_VARCHAR }) || return undef;;
	$sth->bind_param(":exp",$params{EXP},{ sql_type => SQL_VARCHAR }) || return undef;;
	$sth->bind_param(":colore",$params{COLORE},{ sql_type => SQL_VARCHAR }) || return undef;;
	$sth->bind_param(":cap",$params{CAP},{ sql_type => SQL_VARCHAR }) || return undef;;
	$sth->bind_param(":asr",$params{ATTEMPT_SEARCH_RESOLUTION} ? 'S' : 'N',{ sql_type => SQL_CHAR }) || return undef;
	$sth->execute() || return undef;
	my @nciv=();
	while (my $v = $cur->fetchrow_hashref()) {
		push @nciv,$v; 
	}
	if (SIRTI::DB::iserror()) {
		$cur->finish();
		$sth->finish();
		return undef;
	}
	$cur->finish();
	$sth->finish();
	my @err=();
	push @err,$errcode if $errcode != $self->ERR_OK;
	return wantarray ? (\@err,\@nciv) : [ \@err,\@nciv ];
}


sub get_errmsg {
	my ($self,%params)=@_;
	#LEGACY_ERRMSG
	my @err=();
	my $db=$self->{DB};
	my $legacy = $params{LEGACY_ERRMSG} ? "'S'" : "'N'";
	my $cur = $db->create_cursor(" select * from table(csf.get_errcodes($legacy)) order by code")   || return undef;
	while (my $v = $cur->fetchrow_hashref()) {
		$err[$v->{CODE}]=$v->{DESCRIPTION};
	}
	if (SIRTI::DB::iserror()) {
		$cur->finish();
		return undef;
	}	
	$cur->finish();
	return  wantarray ? @err :  \@err;
}


sub free {
	my $self=shift;
	if (ref($self->{PREPARE}) eq 'HASH') {      #chiude le prepare
		for my $v(values %{$self->{PREPARE}}) {
			$v->finish();
		}
		$self->{PREPARE}={};
	}
}


sub DESTROY {
	my $self=shift;
	local $@;
	eval { $self->free(); }
}


if (__FILE__ eq $0) {  #piccolo test
	my $csf = new SIRTI::PLSQL::CSF(DB => new SIRTI::DB);
	my $res=$csf->do_check_comune_frazione(COMUNE => 'MILANO');
	print Dumper($res);

}

1;
