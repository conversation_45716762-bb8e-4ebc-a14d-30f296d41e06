################################################################################
#
# SIRTI::PLSQL::RM_ACTIVITY
#
################################################################################

=head1 NAME

B<SIRTI::PLSQL::RM_ACTIVITY> - Interfaccia PLSQL per RemoteActivity

=head1 SYNOPSIS

  my $ra = SIRTI::ART::RemoteActivity::Source->new( 
	 DB                  => SIRTI::DB->new('user/pwd@host')
	,DBLINK => 'Big-bang test session!!!!'
	,DBMS_OUTPUT_BUFFER      => 'source_service'
	,DBMS_OUTPUT      => 'source_context'
	,NO_RAISE_EXCEPTION      => 'target_service'
  );

=head1 DESCRIPTION

La classe B<SIRTI::PLSQL::RM_ACTIVITY> si occupa dell'interfacciamento delle RemoteActivity con il DB tramite un package PLSQL.

=cut

package SIRTI::PLSQL::RM_ACTIVITY;

use strict;
use warnings;
use SIRTI::DB;
use Carp;
use DBI qw(:sql_types);
use DBD::Oracle qw(:ora_types);
use IO::String;
#use File::Temp;

use base qw(Exporter);

use constant {
	 OP_INSERT						=> 0
	,OP_UPDATE						=> 1
	,OP_DELETE						=> 2
	,OP_QUERY						=> 3
	,EXCP_OK						=> 0   
	,EXCP_NO_SUCH_DBLINK			=> 1
	,EXCP_NO_SUCH_SCHEMA			=> 2
	,EXCP_MISSING_SCHEMA_OBJECTS	=> 3
	,EXCP_NO_SUCH_SOURCE_SERVICE	=> 4
	,EXCP_NO_SUCH_TARGET_SERVICE	=> 5
	,EXCP_NO_SUCH_SOURCE_CONTEXT	=> 6
	,EXCP_NO_SUCH_TARGET_CONTEXT	=> 7
	,EXCP_NO_SUCH_SESSION			=> 8
	,EXCP_SESSION_NOT_LOADED		=> 9
	,EXCP_SESSION_ALREADY_STORED	=> 10
	,EXCP_INVALID_SESSION_TYPE		=> 11
	,EXCP_INVALID_QUEUE				=> 12
	,EXCP_INVALID_EVENT_TYPE		=> 13
	,EXCP_INVALID_NEED_ACK			=> 14
};

use constant {
	EXCEPTIONS => "
		exception
			when rm_activity.no_such_dblink then
				:ERRMSG := 'no such dblink: ' || :DBLINK;
				:ERRCODE := ".EXCP_NO_SUCH_DBLINK.";
			when rm_activity.no_such_schema then
				:ERRMSG := 'no such schema';
				:ERRCODE := ".EXCP_NO_SUCH_SCHEMA.";
			when rm_activity.missing_schema_objects then
				:ERRMSG := 'missing schema objects';
				:ERRCODE := ".EXCP_MISSING_SCHEMA_OBJECTS.";
			when rm_activity.no_such_source_service then
				:ERRMSG := 'no such source service';
				:ERRCODE := ".EXCP_NO_SUCH_SOURCE_SERVICE.";
			when rm_activity.no_such_target_service then
				:ERRMSG := 'no such target service';
				:ERRCODE := ".EXCP_NO_SUCH_TARGET_SERVICE.";
			when rm_activity.no_such_source_context then
				:ERRMSG := 'no such source context';
				:ERRCODE := ".EXCP_NO_SUCH_SOURCE_CONTEXT.";
			when rm_activity.no_such_target_context then
				:ERRMSG := 'no such target context';
				:ERRCODE := ".EXCP_NO_SUCH_TARGET_CONTEXT.";
			when rm_activity.no_such_session then
				:ERRMSG := 'no such session';
				:ERRCODE := ".EXCP_NO_SUCH_SESSION.";
			when rm_activity.session_not_loaded  then
				:ERRMSG := 'session not loaded';
				:ERRCODE := ".EXCP_SESSION_NOT_LOADED.";
			when rm_activity.session_already_stored then
				:ERRMSG := 'session already stored';
				:ERRCODE := ".EXCP_SESSION_ALREADY_STORED.";
			when rm_activity.invalid_session_type then
				:ERRMSG := 'Invalid session type';
				:ERRCODE := ".EXCP_INVALID_SESSION_TYPE.";
			when rm_activity.invalid_queue then
				:ERRMSG := 'Invalid Queue';
				:ERRCODE := ".EXCP_INVALID_QUEUE.";
			when rm_activity.invalid_event_type then
				:ERRMSG := 'Invalid Event Type';
				:ERRCODE := ".EXCP_INVALID_EVENT_TYPE.";
			when rm_activity.invalid_need_ack then
				:ERRMSG := 'Invalid NEED_ACK';
				:ERRCODE := ".EXCP_INVALID_NEED_ACK.";
	"
};


my %t=( 
			'op' 	=> [ qw ( 	OP_INSERT OP_UPDATE OP_DELETE OP_QUERY )  ]
			,'excp'	=> [ qw (
									EXCP_OK
									EXCP_NO_SUCH_DBLINK
									EXCP_NO_SUCH_SCHEMA
									EXCP_MISSING_SCHEMA_OBJECTS
									EXCP_NO_SUCH_SOURCE_SERVICE
									EXCP_NO_SUCH_TARGET_SERVICE
									EXCP_NO_SUCH_SOURCE_CONTEXT
									EXCP_NO_SUCH_TARGET_CONTEXT
									EXCP_NO_SUCH_SESSION
									EXCP_SESSION_NOT_LOADED
									EXCP_SESSION_ALREADY_STORED
									EXCP_INVALID_QUEUE
									EXCP_INVALID_EVENT_TYPE
									EXCP_INVALID_NEED_ACK
							)
						]
		);

our %EXPORT_TAGS=( all => [ map { @{$t{$_}} } keys %t ],%t); 
our @EXPORT_OK=( @{$EXPORT_TAGS{all}} );
our @EXPORT=();


sub _bind {
	my ($self,$sth,%params)=@_;
	$params{DBLINK}=$self->{DBLINK}||'' unless defined $params{DBLINK};
	for my $k(keys %params) {
		$sth->bind_param(':'.$k,$params{$k});
	}
	return $self;
}


sub _execute {
	my ($self,$sth,%params)=@_;
	my ($errmsg,$errcode,$errfatal);
	$sth->bind_param_inout(':ERRMSG',\$errmsg,1024);
	$sth->bind_param_inout(':ERRCODE',\$errcode,5);
	{
		local $@;
		eval {
			$sth->execute;
		};
		$errfatal=$@;
		$self->{DB}->get_dbms_output if $self->{DB}->can('get_dbms_output');
	}
	croak $errfatal if $errfatal;
	my @ret=($self,EXCP_OK,'');
	$params{NO_RAISE_EXCEPTION}=$self->{NO_RAISE_EXCEPTION} unless defined 	 $params{NO_RAISE_EXCEPTION};
	if (defined $errmsg) {			
		croak "$errmsg\n"  if ref($params{NO_RAISE_EXCEPTION}) ne 'ARRAY' || !grep($errcode == $_,@{$params{NO_RAISE_EXCEPTION}});
 		@ret=($self,$errcode,$errmsg);
		return wantarray ? @ret : \@ret;
	}
	return @ret if wantarray;
	return $params{NO_RAISE_EXCEPTION} ? \@ret : $self;
}

sub _map_chunk($) {
	my @cur_array = shift;
	my $redo=0;
	my $chunk_value;
	my ($k_1,$v_1,$chunk_id_1);
	my %data=();
	my $count=0;
	foreach (@{$cur_array[0]}) {
		$count++;
		my ($k,$v,$chunk_id)=($_->[0],$_->[1],$_->[2]);
		SWITCH : {
			if (
				defined $chunk_value->{CHUNK_ID}
				&& (
					!$chunk_id
					||
					$chunk_id != $chunk_value->{CHUNK_ID}
				) 
			){
				$k_1 = $k;
				$k = $chunk_value->{KEY};
				$v_1 = $v;
				$v = $chunk_value->{VALORE};
				$chunk_id_1 = $chunk_id;
				$redo = 1;
			}
			if ($chunk_id && !$redo){
				$chunk_value->{VALORE}.=$v;
				$chunk_value->{KEY} = $k;
				$chunk_value->{CHUNK_ID} = $chunk_id;
				if ($count ne scalar(@{$cur_array[0]})){
					next;
				} else {
					$k = $chunk_value->{KEY};
					$v = $chunk_value->{VALORE};
					$chunk_id = $chunk_value->{CHUNK_ID};
				}
			}
			if (exists $data{$k} && ref($data{$k}) eq ''){
				$data{$k}=[$data{$k},$v];
			} elsif (ref($data{$k}) eq 'ARRAY'){
				push @{$data{$k}}, $v;
			} else {
				$data{$k}=$v;
			} 
			if ($redo){
				delete $chunk_value->{VALORE};
				delete $chunk_value->{KEY};
				delete $chunk_value->{CHUNK_ID};
				$k = $k_1;
				$v = $v_1;
				$chunk_id = $chunk_id_1;
				$redo = 0;
				redo SWITCH;
			}
		}
	}
	return %data;
};

my %LOOKUP = (
		SERVICE	=> {
						INSERT => sub {
							my ($self,%params)=@_;									
							$self->{PREPARE}->{SERVICE_INSERT}=$self->{DB}->create_prepare("
											declare
												ra ra_service%rowtype;
											begin
												ra.id := :ID;
												ra.description := :DESCRIPTION;
												ra.enabled := :ENABLED;
												rm_activity.create_service (
														:DBLINK
														,ra
												);
											".EXCEPTIONS."
											end;
							") unless defined $self->{PREPARE}->{SERVICE_INSERT};
							my $sth=$self->{PREPARE}->{SERVICE_INSERT}->get_sth;
							$self->_bind($sth,%{$params{DATA}});
							return $self->_execute($sth,%params);

						}
						,UPDATE => sub { confess "not implemented"; }
						,DELETE => sub { confess "not implemented"; }
						,QUERY  => sub { confess "not implemented"; }
					}
		,CONTEXT => {
						INSERT => sub {
							my ($self,%params)=@_;
							$self->{PREPARE}->{CONTEXT_INSERT}=$self->{DB}->create_prepare("
											declare
												ra ra_context%rowtype;
												id number;
											begin
												ra.id := :ID;
												ra.description := :DESCRIPTION;
												ra.enabled := :ENABLED;
												rm_activity.create_context (
														:DBLINK
														,ra
												);
											".EXCEPTIONS."
											end;

							") unless defined $self->{PREPARE}->{CONTEXT_INSERT};
							my $sth=$self->{PREPARE}->{CONTEXT_INSERT}->get_sth;
							$self->_bind($sth,%{$params{DATA}});
							return $self->_execute($sth,%params);
						}
						,UPDATE => sub {confess "not implemented"; }
						,DELETE => sub {confess "not implemented"; }
						,QUERY	=> sub {confess "not implemented"; }
		}
);

################################################################################
#  M E T O D I   P U B L I C I
################################################################################

=pod

=head1 METHODS

=cut


=pod

=head2 new( DB => I<SIRTI::DB>, SESSION_DESCRIPTION => I<SCALAR>, SOURCE_SERVICE => I<SCALAR>, SOURCE_CONTEXT => I<SCALAR>, TARGET_SERVICE => I<SCALAR>, TARGET_CONTEXT => I<SCALAR> [ , USER_ID => I<SCALAR> ], TARGET =>I<HASHREF> )

=over 4

=item * DB

Istanza di SIRTI::DB

=item * DBLINK 

DBLINK per l'accesso al DB

=item * NO_RAISE_EXCEPTION

Non lancia eccezioni in caso di errore execute

=back

=cut

sub new($$) {
	my ($class,%params)=@_;
	#DB								=> istanza di SIRTI::DB
	#DBLINK							=> dblink di connessione remota
	#NO_RAISE_EXCEPTION				=> non lancia eccezioni in caso di errore execute
	$params{DBLINK}=undef unless exists  $params{DBLINK};
	croak "DB is not SIRTI::DB or SIRTI::DB::Oracle" unless ref($params{DB}) =~/^SIRTI::DB:{0,2}/;
	my $self=bless \%params,$class;
	return $self;
}

=pod

=head2 lookup_operation( LOOKUP => I<SCALAR>, OPERATION => I<SCALAR>, DATA => I<HASHREF>)

	$rm->lookup_operation(
		LOOKUP		=> 	'CONTEXT'
		,OPERATION 	=>  'INSERT'
		,DATA		=>  {
							ID 				=> $context_id
							,DESCRIPTION	=> "description $context_id"
							,ENABLED			=> 1
						}
	)

=over 4

=item * LOOKUP

SERVICE | CONTEXT

=item * OPERATION 

INSERT

=item * DATA

Dati della configurazione

=back

=cut

sub lookup_operation {
	my ($self,%params)=@_;
	for my $k ( qw(LOOKUP OPERATION DATA) ) {
		croak "$k param not defined " unless defined   $params{$k};
	}
	my $op=$LOOKUP{$params{LOOKUP}}->{$params{OPERATION}};
	croak "unknow lookup operation" unless defined $op;
	$params{DATA}->{DBLINK}=$self->{DBLINK};
	return $op->($self,%params);
}
	
=pod

=head2 get_db_schema()

Recupera lo schema del DB su cui si sta lavorando

=cut

sub get_db_schema {
	my ($self,%params)=@_;
	return $self->{DB_SCHEMA} if defined $self->{DB_SCHEMA};
	my $sth=$self->{DB}->create_prepare("
		begin
			:SCHEMA := rm_activity.ra_schema;
		".EXCEPTIONS."
		end;
	")->get_sth;
	my $schema=undef;
	$sth->bind_param_inout(':SCHEMA',\$schema,34);
	my @ret=$self->_execute($sth,%params);
	$ret[0]=$self->{DB_SCHEMA}=$schema;
	return @ret if wantarray;
	return $params{NO_RAISE_EXCEPTION} ? \@ret : $schema;
}

=pod

=head2 create_session( SESSION_TYPE => I<SCALAR>, RA_DBLINK => I<SCALAR>, SESSION_DESCRIPTION => I<SCALAR>, SESSION_CMDLINE => I<SCALAR>, SOURCE_SERVICE => I<SCALAR>, SOURCE_CONTEXT => I<SCALAR>, USER_ID => I<SCALAR>, USER_NAME => I<SCALAR>)

Crea la sessione del SOURCE

=over 4

=item * SESSION_TYPE

T => TARGET | S => SERVICE

=item * RA_DBLINK 

Dblink da utilizzare

=item * SESSION_DESCRIPTION

Descrizione della sessione

=item * SESSION_CMDLINE

Command Line della sessione

=item * SOURCE_SERVICE

Source Service

=item * SOURCE_CONTEXT

Source Context

=item * USER_ID

ID dell'operatore

=item * USER_NAME

Operatore che ha effettuato la connession

=back

=cut

sub create_session {
	my ($self,%params)=@_;
	#$self->unload_session if defined $self->{SESSION_ID};
	$self->{PREPARE}->{CREATE_SESSION}=$self->{DB}->create_prepare("
		begin
			:SESSION_TOKEN := rm_activity.create_session (
					:SESSION_TYPE
					,:RA_DBLINK            
					,:SESSION_DESCRIPTION     
					,:SESSION_CMDLINE         
					,:SOURCE_SERVICE  
					,:SOURCE_CONTEXT  
					,:USER_ID         
					,:USER_NAME
			);
		".EXCEPTIONS."
		end;
	") unless defined $self->{PREPARE}->{CREATE_SESSION};
	my $token=undef;
	my $sth=$self->{PREPARE}->{CREATE_SESSION}->get_sth;
	$sth->bind_param_inout(':SESSION_TOKEN',\$token,255);
	$self->_bind($sth, %{$params{DATA}});
	my @ret=$self->_execute($sth,%params);
	$ret[0]=$self->{SESSION_TOKEN}=$token;
	return @ret if wantarray;
	return $params{NO_RAISE_EXCEPTION} ? \@ret : $token;
}

=pod

=head2 create_target_session( SESSION_TOKEN => I<SCALAR>, TARGET_SERVICE => I<SCALAR>, TARGET_CONTEXT => I<SCALAR>)

Crea la sessione del target

=over 4

=item * SESSION_TOKEN

Token di sessione restituito della create_session

=item * TARGET_SERVICE

Target Service

=item * TARGET_CONTEXT

Target Context

=back

=cut

sub create_target_session {
	my ($self,%params)=@_;
	$self->{PREPARE}->{CREATE_TARGET_SESSION}=$self->{DB}->create_prepare("
		begin
			:SESSION_TARGET_TOKEN := rm_activity.create_target_session (
					:SESSION_TOKEN            
					,:TARGET_SERVICE  
					,:TARGET_CONTEXT  
			);
		".EXCEPTIONS."
		end;
	") unless defined $self->{PREPARE}->{CREATE_TARGET_SESSION};
	my $token=undef;
	my $sth=$self->{PREPARE}->{CREATE_TARGET_SESSION}->get_sth;
	$sth->bind_param_inout(':SESSION_TARGET_TOKEN',\$token,255);
	$self->_bind($sth, %params);
	my @ret=$self->_execute($sth,%params);
#	$token=$ret[0];
#	$ret[0]=$self->{SESSION_TARGET_TOKEN}=$token;
	return @ret if wantarray;
	return $params{NO_RAISE_EXCEPTION} ? \@ret : $token;
}

=pod

=head2 store_session( SESSION_TOKEN => I<SCALAR>)

Memorizza la sessione sulla banca dati

=over 4

=item * SESSION_TOKEN

Token di sessione restituito della create_session

=back

=cut

sub store_session {
	my ($self,%params)=@_;
	croak "not session set " unless defined $params{SESSION_TOKEN};
	$self->{PREPARE}->{STORE_SESSION}=$self->{DB}->create_prepare("
		begin
			rm_activity.store_session (
				:SESSION_TOKEN
			);
		".EXCEPTIONS."
		end;
	") unless defined  $self->{PREPARE}->{STORE_SESSION};
	my $sth=$self->{PREPARE}->{STORE_SESSION}->get_sth;
	$self->_bind($sth,SESSION_TOKEN => $params{SESSION_TOKEN});
	return $self->_execute($sth);
}

=pod

=head2 load_session( ID_SESSION => I<SCALAR>, DBLINK => I<SCALAR>)

Ricava il SESSION_TOKEN a partire dall'id della sessione

=over 4

=item * ID_SESSION

ID della session

=item * DBLINK

Eventuale DBLINK

=back

=cut

sub load_session {
	my ($self,%params)=@_;
	croak "SESSION_ID param not defined " unless defined $params{SESSION_ID};
	$self->{PREPARE}->{LOAD_SESSION}=$self->{DB}->create_prepare("
		begin
			:SESSION_TOKEN := rm_activity.load_session (
				:DBLINK
				,:ID_SESSION
			);
		".EXCEPTIONS."
		end;
	") unless defined  $self->{PREPARE}->{LOAD_SESSION};
	my $sth=$self->{PREPARE}->{LOAD_SESSION}->get_sth;

	$self->_bind($sth,ID_SESSION => $params{SESSION_ID});
	my %record=();
	$sth->bind_param_inout(':SESSION_TOKEN',\$record{SESSION_TOKEN},255);
	my @ret=$self->_execute($sth,%params);
	$self->{SESSION_ID} = $params{SESSION_ID} if defined $record{SESSION_TOKEN};
	$self->{SESSION_TOKEN}= $record{SESSION_TOKEN} if defined $record{SESSION_TOKEN};
	$ret[0]=\%record;
	return @ret if wantarray;
	return $params{NO_RAISE_EXCEPTION} ? \@ret : $ret[0];
}

=pod

=head2 load_session_from_event( EVENT_ID => I<SCALAR>, DBLINK => I<SCALAR>)

Ricava il SESSION_TOKEN a partire dall'id dell'evento

=over 4

=item * EVENT_ID

ID dell'evento

=item * DBLINK

Eventuale DBLINK

=back

=cut

sub load_session_from_event {
	my ($self,%params)=@_;
	croak "EVENT_ID param not defined " unless defined $params{EVENT_ID};

	$self->{PREPARE}->{LOAD_SESSION_EVENT}=$self->{DB}->create_prepare("
		begin
			:SESSION_TOKEN := rm_activity.load_session_from_event (
				:DBLINK
				,:EVENT_ID
			);
		".EXCEPTIONS."
		end;
	") unless defined  $self->{PREPARE}->{LOAD_SESSION_EVENT};
	my $sth=$self->{PREPARE}->{LOAD_SESSION_EVENT}->get_sth;
	$self->_bind($sth, EVENT_ID => $params{EVENT_ID});
	my %record=();
	$sth->bind_param_inout(':SESSION_TOKEN',\$record{SESSION_TOKEN},255);
	my @ret=$self->_execute($sth,%params);
	$self->{SESSION_ID}=$record{ID};
	(delete $self->{PREPARE}->{LOAD_SESSION_EVENT})->finish;
	$ret[0]=\%record;
	return @ret if wantarray;
	return $params{NO_RAISE_EXCEPTION} ? \@ret : $ret[0];
}

=pod

=head2 get_session_info( SESSION_TOKEN => I<SCALAR>)

Ricava le informazioni dalla sessione a partire dal SESSION_TOKEN

=over 4

=item * SESSION_TOKEN

Identificativo della sessione

=back

=cut

sub get_session_info {
	my ($self,%params)=@_;
	croak "SESSION_TOKEN param not defined " unless defined $params{SESSION_TOKEN};

	$self->{PREPARE}->{LOAD_SESSION_EVENT}=$self->{DB}->create_prepare("
		begin
			rm_activity.get_session_info (
				:CUR
				,:SESSION_TOKEN
			);
		".EXCEPTIONS."
		end;
	") unless defined  $self->{PREPARE}->{LOAD_SESSION_EVENT};
	my $sth=$self->{PREPARE}->{LOAD_SESSION_EVENT}->get_sth;
	$self->_bind($sth,SESSION_TOKEN => $params{SESSION_TOKEN});
	$sth->bind_param_inout(':CUR',\my $cur,0, {ora_type => ORA_RSET });
	my @ret=$self->_execute($sth,%params);
	(delete $self->{PREPARE}->{LOAD_SESSION_EVENT})->finish;
	$ret[0]=$cur->fetchrow_hashref();
	return @ret if wantarray;
	return $params{NO_RAISE_EXCEPTION} ? \@ret : $ret[0];
}

=pod

=head2 get_parent_id( SESSION_TOKEN => I<SCALAR>, EVENT_ID => I<SCALAR>)

Ricava l'id dell'eventuale evento padre

=over 4

=item * SESSION_TOKEN

Identificativo della sessione

=item * EVENT_ID

Id dell'evento

=back

=cut

sub get_parent_id {
	my ($self,%params)=@_;
	croak "SESSION_TOKEN param not defined " unless defined $params{SESSION_TOKEN};
	croak "EVENT_ID param not defined " unless defined $params{EVENT_ID};

	$self->{PREPARE}->{GET_PARENT_ID}=$self->{DB}->create_prepare("
		begin
			:PARENT_ID := rm_activity.get_parent_id (
				 :SESSION_TOKEN
				,:EVENT_ID
			);
		".EXCEPTIONS."
		end;
	") unless defined  $self->{PREPARE}->{GET_PARENT_ID};
	my $sth=$self->{PREPARE}->{GET_PARENT_ID}->get_sth;
	$self->_bind($sth,SESSION_TOKEN => $params{SESSION_TOKEN},EVENT_ID => $params{EVENT_ID});
	my $parent_id=undef;
	$sth->bind_param_inout(':PARENT_ID',\$parent_id,255);
	my @ret=$self->_execute($sth,%params);
	$ret[0]=$self->{PARENT_ID}=$parent_id;
	return @ret if wantarray;
	return $params{NO_RAISE_EXCEPTION} ? \@ret : $ret[0];
}

=pod

=head2 event_operation( SESSION_TOKEN => I<SCALAR>, OP_INSERT => I<SCALAR> [, SOURCE_REF => I<SCALAR>] [, EVENT => I<SCALAR>] [, SCHEDULE_DATE => I<SCALAR>] [, EXPIRY_DATE => I<SCALAR>] [, NEED_ACK => I<SCALAR>] [, ID => I<SCALAR>])

Esegue le operazioni di inserimento e lettura di un evento

=over 4

=item * SESSION_TOKEN

Identificativo della sessione

=item * OP_INSERT

Tipo di operazione (INSERT => 0 | QUERY => 3)

=item * SOURCE_REF

Source REF (solo se operazione di INSERT)

=item * EVENT

Evento (solo se operazione di INSERT)

=item * SCHEDULE_DATE

Data di schedulazione (solo se operazione di INSERT)

=item * EXPIRY_DATE

Data di scadenza (solo se operazione di INSERT)

=item * NEED_ACK

Richiesta ACK (solo se operazione di INSERT)

=item * ID

ID dell'evento (solo se operazione di QUERY)

=back

=cut

sub event_operation {
	my ($self,$op,%params)=@_;
	croak "session token not loaded (session_token is undefined)" unless defined $params{SESSION_TOKEN};
	my @ret=();
	if ($op eq OP_INSERT) {
		$self->{PREPARE}->{DATA_HEADER_INSERT}=$self->{DB}->create_prepare("
			begin
				:RES_ID := rm_activity.event_insert(
				          :SESSION_TOKEN             
				         ,:SOURCE_REF       
				         ,:EVENT            
				         ,sysdate
				         ,:SCHEDULE_DATE    
				         ,:EXPIRY_DATE
				         ,:NEED_ACK
 				);
		   ".EXCEPTIONS."
		   end;
		") unless defined  $self->{PREPARE}->{DATA_HEADER_INSERT};
		my $sth=$self->{PREPARE}->{DATA_HEADER_INSERT}->get_sth;
		my %p=map {  ($_,$params{$_})      } qw (
									SOURCE_REF       
									EVENT                
									SCHEDULE_DATE    
									EXPIRY_DATE
									NEED_ACK   
								);
		$self->_bind($sth,%p,SESSION_TOKEN => $params{SESSION_TOKEN});
		$sth->bind_param_inout(':RES_ID',\my $res_id,38);
		@ret=$self->_execute($sth,%params);
		$ret[0]=$res_id;
	}
	elsif ($op eq OP_QUERY) {
		$self->{PREPARE}->{DATA_HEADER_QUERY}=$self->{DB}->create_prepare("
			begin
				rm_activity.event_query(
						:CUR
						,:SESSION_TOKEN
						,:ID
			);
		".EXCEPTIONS."
			end;
		") unless defined $self->{PREPARE}->{DATA_HEADER_QUERY};
		my $sth=$self->{PREPARE}->{DATA_HEADER_QUERY}->get_sth;
		$self->_bind($sth,SESSION_TOKEN => $params{SESSION_TOKEN},ID => $params{ID});
		$sth->bind_param_inout(':CUR',\my $cur,0, {ora_type => ORA_RSET });
		@ret=$self->_execute($sth,%params);
		(delete $self->{PREPARE}->{DATA_HEADER_QUERY})->finish;
		$ret[0]=$cur;
	}
	else {
		croak "not implemented";
	}
	return @ret if wantarray;
	return $params{NO_RAISE_EXCEPTION} ? \@ret : $ret[0];

}

=pod

=head2 data_event_operation( SESSION_TOKEN => I<SCALAR>, OP_INSERT => I<SCALAR>, EVENT_ID => I<SCALAR> [, DATA => I<HASHREF>])

Esegue le operazioni di inserimento e lettura dei dati di un evento

=over 4

=item * SESSION_TOKEN

Identificativo della sessione

=item * OP_INSERT

Tipo di operazione (INSERT => 0 | QUERY => 3)

=item * EVENT_ID

ID dell'evento

=item * DATA

Hash con dati da associare all'evento (solo se operazione di INSERT)

=back

=cut

sub data_event_operation {
	my ($self,$op,$event_id,%params)=@_;
	croak "session_token not loaded (session_token is undefined)" unless defined $self->{SESSION_TOKEN};
	if ($op eq OP_INSERT) {
		$self->{PREPARE}->{DATA_INSERT}=$self->{DB}->create_prepare("
			begin
				:RES_ID := rm_activity.event_data_insert(
				          :SESSION_TOKEN             
				         ,:EVENT_ID       
				         ,:NAME       
				         ,:VALUE
 				);
		   ".EXCEPTIONS."
		   end;
		") unless defined  $self->{PREPARE}->{DATA_INSERT};
		my $sth=$self->{PREPARE}->{DATA_INSERT}->get_sth;
		$self->_bind($sth,SESSION_TOKEN => $self->{SESSION_TOKEN},EVENT_ID => $event_id);
		$sth->bind_param_inout(':RES_ID',\my $res_id,38);
		$self->{PREPARE}->{DATA_INSERT_CHUNK}=$self->{DB}->create_prepare("
			begin
				:RES_ID := rm_activity.event_data_insert(
				          :SESSION_TOKEN             
				         ,:EVENT_ID       
				         ,:NAME       
				         ,:VALUE
				         ,:CHUNK_ID
 				);
		   ".EXCEPTIONS."
		   end;
		") unless defined  $self->{PREPARE}->{DATA_INSERT_CHUNK};
		my $sth_chunk=$self->{PREPARE}->{DATA_INSERT_CHUNK}->get_sth;
		$self->_bind($sth_chunk,SESSION_TOKEN => $self->{SESSION_TOKEN},EVENT_ID => $event_id);
		$sth_chunk->bind_param_inout(':RES_ID',\$res_id,38);
		my $count=0;
		my %p=();
		if (exists $params{DATA}) {
			%p=ref($params{DATA}) eq 'HASH' ? %{$params{DATA}} : ( DATA => $params{DATA} );
		}
		my @ret=();
		for my $name(keys %p) {
			my $data=delete $p{$name};
			my $r=ref($data);
			if ($r eq 'ARRAY') {
				DATA : {
					my $chunk_id = 0;
					for my $value(@$data) {
						if ($value && length($value)>2000){
							my $h = IO::String->new($value);
							#my $h = File::Temp->new($value);
							#$h->seek(0,0);
							while (read $h, my $chunk, 2000) {
								$self->_bind($sth_chunk,NAME => $name,VALUE => $chunk, CHUNK_ID=> $chunk_id);
								@ret=$self->_execute($sth_chunk,%params);
								$ret[0]=++$count;
								last DATA if $ret[1] != EXCP_OK;
							}
							$chunk_id++;
						} else {
							$self->_bind($sth,NAME => $name,VALUE => $value);
							@ret=$self->_execute($sth,%params);
							$ret[0]=++$count;
							last DATA if $ret[1] != EXCP_OK;
						}
					}
				}
			}
			elsif ($r eq '') {
				DATA : {
					my $chunk_id = 0;
					if ($data && length($data)>2000){
						my $h = IO::String->new($data);
						while (read $h, my $chunk, 2000) {
							$self->_bind($sth_chunk,NAME => $name,VALUE => $chunk, CHUNK_ID=> $chunk_id);
							@ret=$self->_execute($sth_chunk,%params);
							$ret[0]=++$count;
							last DATA if $ret[1] != EXCP_OK;
						}
					} else{
						$self->_bind($sth,NAME => $name,VALUE => $data);
						@ret=$self->_execute($sth,%params);
						$ret[0]=++$count;
						last DATA;
					}
				}
				last if $ret[1] != EXCP_OK;
			}
			elsif ($r eq 'SCALAR') {
				DATA : {
					my $chunk_id = 0;
					if ($$data && length($$data)>2000){
						my $h = IO::String->new($$data);
						while (read $h, my $chunk, 2000) {
							$self->_bind($sth_chunk,NAME => $name,VALUE => $chunk, CHUNK_ID=> $chunk_id);
							@ret=$self->_execute($sth_chunk,%params);
							$ret[0]=++$count;
							last DATA if $ret[1] != EXCP_OK;
						}
					} else {
						$self->_bind($sth,NAME => $name,VALUE => $$data);
						@ret=$self->_execute($sth,%params);
						$ret[0]=++$count;
						last DATA;
					}
				}
				last if $ret[1] != EXCP_OK;
			}
			else {
				croak "$r: unknow type binding";
			}
		}
		return @ret if wantarray;
		return $params{NO_RAISE_EXCEPTION} ? \@ret : $ret[0];
	}
	elsif ($op eq OP_QUERY) {
		$self->{PREPARE}->{DATA_QUERY}=$self->{DB}->create_prepare("
		   begin
			rm_activity.event_data_query(
				:CUR
				,:SESSION_TOKEN
				,:EVENT_ID
			);
		".EXCEPTIONS."
		end;
		") unless defined $self->{PREPARE}->{DATA_QUERY};
		my $sth=$self->{PREPARE}->{DATA_QUERY}->get_sth;
		$self->_bind($sth,SESSION_TOKEN => $self->{SESSION_TOKEN},EVENT_ID => $event_id);
		$sth->bind_param_inout(':CUR',\my $cur,0, {ora_type => ORA_RSET });
		my @ret=$self->_execute($sth,%params);
		(delete $self->{PREPARE}->{DATA_QUERY})->finish;
		my %data=_map_chunk($cur->fetchall_arrayref());
		$cur->finish;
		$ret[0]=\%data;
		return @ret if wantarray;
		return $params{NO_RAISE_EXCEPTION} ? \@ret : $ret[0];
	}
	else {
		confess "not implemented";
	}
}

=pod

=head2 data_ack_event_operation( SESSION_TOKEN => I<SCALAR>, OP_INSERT => I<SCALAR>, EVENT_ID => I<SCALAR> [, DATA => I<HASHREF>])

Esegue le operazioni di inserimento e lettura dei dati di un ack

=over 4

=item * SESSION_TOKEN

Identificativo della sessione

=item * OP_INSERT

Tipo di operazione (INSERT => 0 | QUERY => 3)

=item * EVENT_ID

ID dell'ack

=item * DATA

Hash con dati da associare all'evento (solo se operazione di INSERT)

=back

=cut

sub data_ack_event_operation {
	my ($self,$op,$event_id,%params)=@_;
	croak "session_token not loaded (session_token is undefined)" unless defined $self->{SESSION_TOKEN};
	if ($op eq OP_INSERT) {
		$self->{PREPARE}->{DATA_ACK_INSERT}=$self->{DB}->create_prepare("
			begin
				:RES_ID := rm_activity.event_data_ack_insert(
				          :SESSION_TOKEN             
				         ,:EVENT_ID       
				         ,:NAME       
				         ,:VALUE
 				);
		   ".EXCEPTIONS."
		   end;
		") unless defined  $self->{PREPARE}->{DATA_ACK_INSERT};
		my $sth=$self->{PREPARE}->{DATA_ACK_INSERT}->get_sth;
		$self->_bind($sth,SESSION_TOKEN => $self->{SESSION_TOKEN},EVENT_ID => $event_id);
		$sth->bind_param_inout(':RES_ID',\my $res_id,38);
		$self->{PREPARE}->{DATA_ACK_INSERT_CHUNK}=$self->{DB}->create_prepare("
			begin
				:RES_ID := rm_activity.event_data_ack_insert(
				          :SESSION_TOKEN             
				         ,:EVENT_ID       
				         ,:NAME       
				         ,:VALUE
				         ,:CHUNK_ID
 				);
		   ".EXCEPTIONS."
		   end;
		") unless defined  $self->{PREPARE}->{DATA_ACK_INSERT_CHUNK};
		my $sth_chunk=$self->{PREPARE}->{DATA_ACK_INSERT_CHUNK}->get_sth;
		$self->_bind($sth_chunk,SESSION_TOKEN => $self->{SESSION_TOKEN},EVENT_ID => $event_id);
		$sth_chunk->bind_param_inout(':RES_ID',\$res_id,38);
		my $count=0;
		my %p=();
		if (exists $params{DATA}) {
			%p=ref($params{DATA}) eq 'HASH' ? %{$params{DATA}} : ( DATA => $params{DATA} );
		}
		my @ret=();
		for my $name(keys %p) {
			my $data=delete $p{$name};
			my $r=ref($data);
			if ($r eq 'ARRAY') {
				DATA : {
					my $chunk_id = 0;
					for my $value(@$data) {
						if ($value && length($value)>2000){
							my $h = IO::String->new($value);
							while (read $h, my $chunk, 2000) {
								$self->_bind($sth_chunk,NAME => $name,VALUE => $chunk, CHUNK_ID=> $chunk_id);
								@ret=$self->_execute($sth_chunk,%params);
								$ret[0]=++$count;
								last DATA if $ret[1] != EXCP_OK;
							}
							$chunk_id++;
						} else {
							$self->_bind($sth,NAME => $name,VALUE => $value);
							@ret=$self->_execute($sth,%params);
							$ret[0]=++$count;
							last DATA if $ret[1] != EXCP_OK;
						}
					}
				}
			}
			elsif ($r eq '') {
				DATA : {
					my $chunk_id = 0;
					if ($data && length($data)>2000){
						my $h = IO::String->new($data);
						while (read $h, my $chunk, 2000) {
							$self->_bind($sth_chunk,NAME => $name,VALUE => $chunk, CHUNK_ID=> $chunk_id);
							@ret=$self->_execute($sth_chunk,%params);
							$ret[0]=++$count;
							last DATA if $ret[1] != EXCP_OK;
						}
					} else{
						$self->_bind($sth,NAME => $name,VALUE => $data);
						@ret=$self->_execute($sth,%params);
						$ret[0]=++$count;
						last DATA;
					}
				}
				last if $ret[1] != EXCP_OK;
			}
			elsif ($r eq 'SCALAR') {
				DATA : {
					my $chunk_id = 0;
					if ($$data && length($$data)>2000){
						my $h = IO::String->new($$data);
						while (read $h, my $chunk, 2000) {
							$self->_bind($sth_chunk,NAME => $name,VALUE => $chunk, CHUNK_ID=> $chunk_id);
							@ret=$self->_execute($sth_chunk,%params);
							$ret[0]=++$count;
							last DATA if $ret[1] != EXCP_OK;
						}
					} else {
						$self->_bind($sth,NAME => $name,VALUE => $$data);
						@ret=$self->_execute($sth,%params);
						$ret[0]=++$count;
						last DATA;
					}
				}
				last if $ret[1] != EXCP_OK;
			}
			else {
				croak "$r: unknow type binding";
			}
		}
		return @ret if wantarray;
		return $params{NO_RAISE_EXCEPTION} ? \@ret : $ret[0];
	}
	elsif ($op eq OP_QUERY) {
		$self->{PREPARE}->{DATA_ACK_QUERY}=$self->{DB}->create_prepare("
		   begin
			rm_activity.event_data_ack_query(
				:CUR
				,:SESSION_TOKEN
				,:EVENT_ID
			);
		".EXCEPTIONS."
		end;
		") unless defined $self->{PREPARE}->{DATA_ACK_QUERY};
		my $sth=$self->{PREPARE}->{DATA_ACK_QUERY}->get_sth;
		$self->_bind($sth,SESSION_TOKEN => $self->{SESSION_TOKEN},EVENT_ID => $event_id);
		$sth->bind_param_inout(':CUR',\my $cur,0, {ora_type => ORA_RSET });
		my @ret=$self->_execute($sth,%params);
		(delete $self->{PREPARE}->{DATA_ACK_QUERY})->finish;
		my %data=_map_chunk($cur->fetchall_arrayref());
		$cur->finish;
		$ret[0]=\%data;
		return @ret if wantarray;
		return $params{NO_RAISE_EXCEPTION} ? \@ret : $ret[0];
	}
	else {
		confess "not implemented";
	}
}

=pod

=head2 find_events_pending( SESSION_TOKEN => I<SCALAR> [, SOURCE_REF => I<SCALAR>] [, EVENT => I<SCALAR>])

Restituisce un array di id da lavorare

=over 4

=item * SESSION_TOKEN

Identificativo della sessione

=item * SOURCE_REF

Riferimento del Source relativo all'evento

=item * EVENT

Tipo di evento

=back

=cut

sub	find_events_pending	{
		my ($self,%params)=@_;
#		use	Data::Dumper;
#		use	Benchmark;
#		use	Time::HiRes	qw(gettimeofday);
#		my ($t0, $t1);
#		print STDERR	"%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%\n\n\n";
#		$t0	= gettimeofday();
#		my $i =	1;
#		while (	my $c =	caller($i++) ) {
#				print STDERR "$i: $c\n";
#		}
#		print STDERR	Dumper(\%params);
		$params{SESSION_TOKEN}=$self->{SESSION_TOKEN} unless defined	$params{SESSION_TOKEN};
		#$self->unload_session if defined $self->{SESSION_ID};
		$self->{PREPARE}->{QUERY_FIND_PENDING}=$self->{DB}->create_prepare("
				begin
						:QUERY := rm_activity.get_sql_find_pending (
										 :SESSION_TOKEN
										,:SOURCE_REF
										,:EVENT
						);
				".EXCEPTIONS."
				end;
		") unless defined $self->{PREPARE}->{QUERY_FIND_PENDING};
		my $query=undef;
		my %p=map {	 ($_,$params{$_})	   } qw(
																								SOURCE_REF
																								EVENT
																						);
		my $sth=$self->{PREPARE}->{QUERY_FIND_PENDING}->get_sth;
		$self->_bind($sth,%p,SESSION_TOKEN => $params{SESSION_TOKEN});

		$sth->bind_param_inout(':QUERY',\$query,4000);
		my @ret=$self->_execute($sth,%params);
		#$ret[0]=$self->{SESSION_TOKEN}=$query;
		$ret[0]=$query;
#		$t1	= gettimeofday() - $t0;
#		print STDERR "BENCHMARK: " . $t1  .	"\n";
#		print STDERR	"%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%\n\n\n";
		return @ret	if wantarray;
		return $params{NO_RAISE_EXCEPTION} ? \@ret : $query;
}


=pod

=head2 ignore_event_pending( SESSION_TOKEN => I<SCALAR>, EVENT_ID => I<SCALAR> [, REASON => I<SCALAR>])

Esegue lookup sull'evento

=over 4

=item * SESSION_TOKEN

Identificativo della sessione

=item * EVENT_ID

Id dell'evento

=item * REASON

Eventuale motivazione

=back

=cut

sub ignore_event_pending {
	my ($self,%params)=@_;
	$params{SESSION_TOKEN}=$self->{SESSION_TOKEN} unless defined 	$params{SESSION_TOKEN};
	$self->{PREPARE}->{IGNORE_PENDING}=$self->{DB}->create_prepare("
		begin
			rm_activity.ignore_pending(
				:SESSION_TOKEN
				,:EVENT_ID
				,:REASON
			);
		".EXCEPTIONS."
		end;
	") unless defined $self->{PREPARE}->{IGNORE_PENDING};

	my %p=map {  ($_,$params{$_})      } qw(
												REASON
												EVENT_ID
												SESSION_TOKEN
											);
	my $sth=$self->{PREPARE}->{IGNORE_PENDING}->get_sth;
	$self->_bind($sth,%p,SESSION_TOKEN => $params{SESSION_TOKEN});
	my @ret=$self->_execute($sth,%params);
	$ret[0]=$self;
	return @ret if wantarray;
	return $params{NO_RAISE_EXCEPTION} ? \@ret : $ret[0];
}

=pod

=head2 event_fetch( SESSION_TOKEN => I<SCALAR>, EVENT_ID => I<SCALAR> , TARGET_REF => I<SCALAR>, STATUS => I<SCALAR>, REASON => I<SCALAR>)

Esegue fetch dell'evento

=over 4

=item * SESSION_TOKEN

Identificativo della sessione

=item * EVENT_ID

Id dell'evento

=item * TARGET_REF

Riferimento del TARGET

=item * STATUS

Codice numerico del fetch (0=OK)

=item * REASON

Eventuale motivazione

=back

=cut

sub event_fetch {
	my ($self,%params)=@_;
	$params{SESSION_TOKEN}=$self->{SESSION_TOKEN} unless defined 	$params{SESSION_TOKEN};
	$self->{PREPARE}->{SET_DEVELOPED}=$self->{DB}->create_prepare("
		begin
			:N := rm_activity.event_fetch(
				:SESSION_TOKEN
				,:EVENT_ID
				,:TARGET_REF
				,:STATUS
				,:REASON
			);
		".EXCEPTIONS."
		end;
	") unless defined $self->{PREPARE}->{SET_DEVELOPED};

	my %p=map {  ($_,$params{$_})      } qw(
												REASON
												EVENT_ID
												TARGET_REF
												STATUS 
											);
	my $sth=$self->{PREPARE}->{SET_DEVELOPED}->get_sth;
	$self->_bind($sth,%p,SESSION_TOKEN => $params{SESSION_TOKEN});
	$sth->bind_param_inout(':N',\my $n,18);
	my @ret=$self->_execute($sth,%params);
	$ret[0]=$n;
	return @ret if wantarray;
	return $params{NO_RAISE_EXCEPTION} ? \@ret : $ret[0];
}

=pod

=head2 find_event_ignored( SESSION_TOKEN => I<SCALAR>, EVENT_ID => I<SCALAR>)

Restituisce un hash con le informazioni di lookup sull'evento

=over 4

=item * SESSION_TOKEN

Identificativo della sessione

=item * EVENT_ID

Id dell'evento

=back

=cut

sub find_event_ignored {
	my ($self,%params)=@_;
	$params{SESSION_TOKEN}=$self->{SESSION_TOKEN} unless defined 	$params{SESSION_TOKEN};
	$self->{PREPARE}->{FIND_IGNORED}=$self->{DB}->create_prepare("
		begin
			rm_activity.find_ignored(
				:CUR
				,:SESSION_TOKEN
				,:EVENT_ID
			);
		".EXCEPTIONS."
		end;
	") unless defined $self->{PREPARE}->{FIND_IGNORED};

	my %p=map {  ($_,$params{$_})      } qw(
												EVENT_ID
											);
	my $sth=$self->{PREPARE}->{FIND_IGNORED}->get_sth;
	$self->_bind($sth,%p,SESSION_TOKEN => $params{SESSION_TOKEN});
	$sth->bind_param_inout(':CUR',\my $cur,0, {ora_type => ORA_RSET });
	my @ret=$self->_execute($sth,%params);
	(delete  $self->{PREPARE}->{FIND_IGNORED})->finish;
	$ret[0]=$cur;
	return @ret if wantarray;
	return $params{NO_RAISE_EXCEPTION} ? \@ret : $ret[0];
}

=pod

=head2 cancel_pending_event( SESSION_TOKEN => I<SCALAR>, EVENT_ID => I<SCALAR> [,REASON => I<SCALAR>])

Annulla evento pending

=over 4

=item * SESSION_TOKEN

Identificativo della sessione

=item * EVENT_ID

Id dell'evento

=item * EVENT_ID

Eventuale motivazione

=back

=cut

sub cancel_pending_event {
	my ($self,%params)=@_;
	$params{SESSION_TOKEN}=$self->{SESSION_TOKEN} unless defined 	$params{SESSION_TOKEN};
	$self->{PREPARE}->{CANCEL_PENDING_EVENT}=$self->{DB}->create_prepare("
		begin
			:N := rm_activity.cancel_pending_event(
				:SESSION_TOKEN
				,:EVENT_ID
				,:REASON
			);
		".EXCEPTIONS."
		end;
	") unless defined $self->{PREPARE}->{CANCEL_PENDING_EVENT};

	my %p=map {  ($_,$params{$_})      } qw(
												REASON
												EVENT_ID
											);
	my $sth=$self->{PREPARE}->{CANCEL_PENDING_EVENT}->get_sth;
	$self->_bind($sth,%p,SESSION_TOKEN => $params{SESSION_TOKEN});
	$sth->bind_param_inout(':N',\my $n,18);
	my @ret=$self->_execute($sth,%params);
	$ret[0]=$n;
	return @ret if wantarray;
	return $params{NO_RAISE_EXCEPTION} ? \@ret : $ret[0];
}

=pod

=head2 find_pending_ack( SESSION_TOKEN => I<SCALAR> [, SOURCE_REF => I<SCALAR>] [, EVENT => I<SCALAR>])

Restituisce un array di id di ack da lavorare

=over 4

=item * SESSION_TOKEN

Identificativo della sessione

=item * SOURCE_REF

Riferimento del Source relativo all'evento

=item * EVENT

Tipo di evento

=back

=cut

sub find_pending_ack {
	my ($self,%params)=@_;
	$params{SESSION_ID}=$self->{SESSION_ID} unless defined 	$params{SESSION_ID};
	$self->{PREPARE}->{FIND_PENDING_ACK}=$self->{DB}->create_prepare("
		begin
			rm_activity.find_pending_ack(
				:CUR
				,:SESSION_TOKEN
				,:SOURCE_REF
				,:EVENT
			);
		".EXCEPTIONS."
		end;
	") unless defined $self->{PREPARE}->{FIND_PENDING_ACK};

	my %p=map {  ($_,$params{$_})      } qw(
												SESSION_TOKEN
												SOURCE_REF
												EVENT
											);
	my $sth=$self->{PREPARE}->{FIND_PENDING_ACK}->get_sth;
	$self->_bind($sth,%p);
	$sth->bind_param_inout(':CUR',\my $cur,0, {ora_type => ORA_RSET });
	my @ret=$self->_execute($sth,%params);
	$ret[0]=$cur;
	return @ret if wantarray;
	return $params{NO_RAISE_EXCEPTION} ? \@ret : $ret[0];
}

=pod

=head2 fetch_ack( SESSION_TOKEN => I<SCALAR>, EVENT_ID => I<SCALAR> [, NOTE => I<SCALAR>])

Esegue fetch dell'ack

=over 4

=item * SESSION_TOKEN

Identificativo della sessione

=item * EVENT_ID

Id dell'evento

=item * NOTE

Eventuale nota

=back

=cut

sub fetch_ack {
	my ($self,%params)=@_;
	$params{SESSION_ID}=$self->{SESSION_ID} unless defined 	$params{SESSION_ID};
	$self->{PREPARE}->{FETCH_ACK}=$self->{DB}->create_prepare("
		begin
			rm_activity.fetch_ack(
				:SESSION_TOKEN
				,:EVENT_ID
				,:NOTE
			);
		".EXCEPTIONS."
		end;
	") unless defined $self->{PREPARE}->{FETCH_ACK};

	my %p=map {  ($_,$params{$_})      } qw(
												SESSION_TOKEN
												EVENT_ID
												NOTE
												
											);
	my $sth=$self->{PREPARE}->{FETCH_ACK}->get_sth;
	$self->_bind($sth,%p);
	my @ret=$self->_execute($sth,%params);
	$ret[0]=1;
	return @ret if wantarray;
	return $params{NO_RAISE_EXCEPTION} ? \@ret : $ret[0];
}

=pod

=head2 load_ack( SESSION_TOKEN => I<SCALAR>, EVENT_ID => I<SCALAR>)

Restituisce le informazioni sull'ack

=over 4

=item * SESSION_TOKEN

Identificativo della sessione

=item * EVENT_ID

Id dell'evento

=back

=cut

sub load_ack {
	my ($self,%params)=@_;
	$self->{PREPARE}->{LOAD_ACK}=$self->{DB}->create_prepare("
		begin
			rm_activity.load_ack(
				:CUR
				,:SESSION_TOKEN
				,:EVENT_ID
			);
		".EXCEPTIONS."
		end;
	") unless defined $self->{PREPARE}->{LOAD_ACK};

	my %p=map {  ($_,$params{$_})      } qw(
												EVENT_ID
												SESSION_TOKEN
											);
	my $sth=$self->{PREPARE}->{LOAD_ACK}->get_sth;
	$self->_bind($sth,%p);
	$sth->bind_param_inout(':CUR',\my $cur,0, {ora_type => ORA_RSET });
	my @ret=$self->_execute($sth,%params);
	$ret[0]=$cur;
	return @ret if wantarray;
	return $params{NO_RAISE_EXCEPTION} ? \@ret : $ret[0];
}

=pod

=head2 find_pending_target_ack( SESSION_TOKEN => I<SCALAR> [, SOURCE_REF => I<SCALAR>] [, EVENT => I<SCALAR>])

Restituisce un array di id di ack target da lavorare

=over 4

=item * SESSION_TOKEN

Identificativo della sessione

=item * SOURCE_REF

Riferimento del Source relativo all'evento

=item * EVENT

Tipo di evento

=back

=cut

sub find_pending_target_ack {
	my ($self,%params)=@_;
	$params{SESSION_ID}=$self->{SESSION_ID} unless defined 	$params{SESSION_ID};
	$self->{PREPARE}->{FIND_PENDING_TARGET_ACK}=$self->{DB}->create_prepare("
		begin
			rm_activity.find_pending_target_ack(
				:CUR
				,:SESSION_TOKEN
				,:SOURCE_REF
				,:EVENT
			);
		".EXCEPTIONS."
		end;
	") unless defined $self->{PREPARE}->{FIND_PENDING_TARGET_ACK};

	my %p=map {  ($_,$params{$_})      } qw(
												SESSION_TOKEN
												SOURCE_REF
												EVENT
											);
	my $sth=$self->{PREPARE}->{FIND_PENDING_TARGET_ACK}->get_sth;
	$self->_bind($sth,%p);
	$sth->bind_param_inout(':CUR',\my $cur,0, {ora_type => ORA_RSET });
	my @ret=$self->_execute($sth,%params);
	$ret[0]=$cur;
	return @ret if wantarray;
	return $params{NO_RAISE_EXCEPTION} ? \@ret : $ret[0];
}

=pod

=head2 get_children_id( SESSION_TOKEN => I<SCALAR>, EVENT_ID => I<SCALAR>)

Restituisce un array di id figlio dell'id dato

=over 4

=item * SESSION_TOKEN

Identificativo della sessione

=item * EVENT_ID

Id dell'evento

=back

=cut

sub get_children_id {
	my ($self,%params)=@_;
	$params{SESSION_TOKEN}=$self->{SESSION_TOKEN} unless defined 	$params{SESSION_TOKEN};
	$self->{PREPARE}->{GET_CHILDREN_ID}=$self->{DB}->create_prepare("
		begin
			rm_activity.get_children_id(
				:CUR
				,:SESSION_TOKEN
				,:EVENT_ID
			);
		".EXCEPTIONS."
		end;
	") unless defined $self->{PREPARE}->{GET_CHILDREN_ID};

	my %p=map {  ($_,$params{$_})      } qw(
												SESSION_TOKEN
												EVENT_ID
											);
	my $sth=$self->{PREPARE}->{GET_CHILDREN_ID}->get_sth;
	$self->_bind($sth,%p);
	$sth->bind_param_inout(':CUR',\my $cur,0, {ora_type => ORA_RSET });
	my @ret=$self->_execute($sth,%params);
	my $data=();
	while (my $r = $cur->fetchrow_hashref){
		push (@{$data},$r->{ID});
	}
	$cur->finish;
	$ret[0]=$data;
	return @ret if wantarray;
	return $params{NO_RAISE_EXCEPTION} ? \@ret : $ret[0];
}

=pod

=head2 check_session_exists( SESSION_TOKEN => I<SCALAR>, SESSION_ID => I<SCALAR>)

Verifica se una session esiste

=over 4

=item * SESSION_TOKEN

Identificativo della sessione

=item * SESSION_ID

Id della sessione

=back

=cut

sub check_session_exists {
	my ($self,%params)=@_;
	#$self->unload_session if defined $self->{SESSION_ID};
	$self->{PREPARE}->{CHECK_SESSION_EXISTS}=$self->{DB}->create_prepare("
		begin
			:CHECK_SESSION_ID := rm_activity.check_session_exists (
					:SESSION_TOKEN
					,:SESSION_ID         
			);
		".EXCEPTIONS."
		end;
	") unless defined $self->{PREPARE}->{CHECK_SESSION_EXISTS};
	my $check_Session_id=undef;
	my $sth=$self->{PREPARE}->{CHECK_SESSION_EXISTS}->get_sth;
	$sth->bind_param_inout(':CHECK_SESSION_ID',\$check_Session_id,255);
	$self->_bind($sth, SESSION_TOKEN => $params{SESSION_TOKEN}, SESSION_ID => $params{SESSION_ID});
	my @ret=$self->_execute($sth,%params);
	$ret[0]=$self->{CHECK_SESSION_ID}=$check_Session_id;
	return @ret if wantarray;
	return $params{NO_RAISE_EXCEPTION} ? \@ret : $check_Session_id;
}

=pod

=head2 finish()

=cut

sub finish {	
	my ($self,%params)=@_;
	my @ret=($self,EXCP_OK,'');
	delete  $self->{PREPARE};
	return @ret if wantarray;
	return $params{NO_RAISE_EXCEPTION} ? \@ret : $ret[0];
}



sub DESTROY { local $@; eval { $_[0]->finish; } }

=pod

=head1 NOTES

=over 4

=item Nessuna nota.

=back

=cut


=pod

=head1 BUGS

Eventuali bug riscontrati dovranno essere segnalati su B<ART - Global Services>: L<https://www.artnet.sirtisistemi.net/ARTIT/art> aprendo un apposito DEV-TICKET.

=head1 HISTORY

=over

=item Ver. 0.01

Prima release del modulo

=back

=head1 AUTHOR

Rizzardo Belotti <<EMAIL>>

=cut

if ($0 eq __FILE__) {
	eval('use Data::Dumper;use SIRTI::DB::Oracle');
	if ($@) {
		print STDERR $@;
		exit 1;
	}

	my $sqlid=$ENV{SQLID_DRAULL};
	$sqlid=$ENV{SQLID} unless defined $sqlid;
	my $db=SIRTI::DB::Oracle->new($sqlid, DEBUG => 0,DBMS_OUTPUT => 0);

	$db->set_session_parameters(NLS_DATE_FORMAT => 	'YYYYMMDDHH24MISS');

	my $rm =  SIRTI::PLSQL::RM_ACTIVITY->new(DB => $db,DBLINK => undef,DBMS_OUTPUT => 1);

	my ($service_id,$context_id)=("service_$$","context_$$");

	$rm->lookup_operation(
		LOOKUP		=> 	'SERVICE'
		,OPERATION 	=>  'INSERT'
		,DATA		=>  {
							ID 				=> $service_id
							,DESCRIPTION	=> "description $service_id"
							,ENABLED			=> 1
						}
	);


	$rm->lookup_operation(
		LOOKUP		=> 	'CONTEXT'
		,OPERATION 	=>  'INSERT'
		,DATA		=>  {
							ID 				=> $context_id
							,DESCRIPTION	=> "description $context_id"
							,ENABLED			=> 1
						}
	);


	my $id_session=$rm->create_session(
		DATA	=> {
					SESSION_DESCRIPTION    => 'prova'    
					,SESSION_CMDLINE		=> $0         
					,SOURCE_SERVICE			=>  $service_id
					,SOURCE_CONTEXT			=>  $context_id
					,TARGET_SERVICE			=>  $service_id
					,TARGET_CONTEXT			=>  $context_id
					,USER_ID				=>  0
					,USER_NAME				=> 'root'
		}
	);

	print STDERR "session created: $id_session\n";



	$rm->store_session;
	print STDERR "session stored \n";

	my $r = $rm->load_session(SESSION_ID => $id_session);
	my $id_res=$r->{ID};
	croak "session not loaded " if !defined $id_res || $id_res ne $id_session; 
	print STDERR "session reloaded \n";

	my $event_id = $rm->event_operation(
							OP_INSERT
							,SOURCE_REF => 'prova source'
							,EVENT		=> 'event'
							,SCHEDULE_DATE		 => '20101221000000'
	);
	

	$rm->data_event_operation(OP_INSERT,$event_id,DATA => { nome1  => 'valore1',nome2  => 'valore2',nome3 => 34,nome4 => [ 12,45,67] });
	

	my $cur=$rm->event_operation(OP_QUERY,ID => $event_id,WITH_LOCK => 1,NO_WAIT => 1);
	while(my $row=$cur->fetchrow_hashref) {
		print Dumper($row),"\n";
	}
	$cur->finish;
	
		
	$cur=$rm->data_event_operation(OP_QUERY,$event_id);
	while(my $row=$cur->fetchrow_hashref) {
		print Dumper($row),"\n";
	}
	$cur->finish;


	$rm->ignore_event_pending(REASON => 'prova ignore',EVENT_ID => $event_id)	 ;

	$rm->set_event_developed(
									REASON  => 'elaborato'
									,EVENT_ID => $event_id
									,TARGET_REF => 'target ref'
									,STATUS   => 0
							);

		
	$cur=$rm->find_event_ignored(EVENT_ID => $event_id);

	while(my $row=$cur->fetchrow_hashref) {
		print Dumper($row),"\n";
	}
	$cur->finish;

	my $n=$rm->cancel_pending_event(EVENT_ID => $event_id);

	$rm->finish;

	$db->rollback;
	$db->disconnect;


}
	

1;
