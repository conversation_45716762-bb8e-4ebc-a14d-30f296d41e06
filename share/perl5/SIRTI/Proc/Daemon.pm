package SIRTI::Proc::Daemon;
use strict;
use warnings;
use File::Basename;
use File::Path;
use Carp;
use FindBin;
use File::Spec::Functions qw(catfile devnull catdir tmpdir );
use Ubic::Daemon;
use utf8;

our $VERSION = '0.02';


my 	@DEFAULT_ORDER	= qw(  PROGRAM_NAME LOGGER MKMODE LOCK_DIR EXEC STDOUT STDERR LOG_CONTROLLER TERM_TIMEOUT);

my %DEFAULT_VALUE	= (
	PROGRAM_NAME		=>   sub {   return  $FindBin::Script; }
	,LOCK_DIR			=>   sub {	
									 my %params=@_;
									 my @dirs=(
												$ENV{LOCK_PATH}
												,catdir($ENV{HOME},qw( var lock ))
												,catdir(qw( var lock))
												,tmpdir
									);
									for my $lock_dir(@dirs) {
										next unless defined $lock_dir;
										next unless -d $lock_dir;
										File::Path::mkpath($lock_dir,$params{MKMODE});
										return $lock_dir;
									}
									confess "nessuna directory per lock\n";
							}
	,EXEC				=>  sub {  return catfile($FindBin::Bin,$FindBin::Script);}
	,STDOUT				=>  sub {  return devnull; }
	,STDERR				=>  sub {  return devnull; }
	,TERM_TIMEOUT		=>  sub {  return 10; }
);
									 


sub _convert_params {
	my ($self,%params)=@_;
	if  (scalar(keys %params)) {
		my %out=map {   
			my @out=();
			push @out,('bin',$params{$_}) if /EXEC/ && ref($params{$_}) ne 'CODE';
			push @out,('function',$params{$_}) if /EXEC/ && ref($params{$_}) eq 'CODE';
			push @out,('name',$params{$_}) if /PROGRAM_NAME/;
			push @out,('pidfile',catdir($params{$_},$params{PROGRAM_NAME})) if /LOCK_DIR/;
			push @out,(lc($_),$params{$_}) if /^(STDOUT|STDERR|TERM_TIMEOUT|TIMEOUT)$/;
			push @out,('ubic_log',$params{$_}) if /LOG_CONTROLLER/;
		   @out;
		} keys %params;
		return \%out;
	}
	return {} unless keys %$self; 
	return $self->_convert_params(%$self);
}
	
	
sub new {
	my ($class,%params)=@_;
	for my $k(@DEFAULT_ORDER) {
		next if defined $params{$k};
		my $f=$DEFAULT_VALUE{$k};
		next unless defined $f;
		$params{$k}=$f->(%params);
	}
	my $self=bless \%params,$class;
	$self->{ubic_start_daemon_params}=$self->_convert_params;
	File::Path::mkpath($self->{ubic_start_daemon_params}->{pidfile},$params{MKMODE});
	return $self;
}


sub start_daemon {
	my ($self,%params)=@_;
	local $@;
	eval { Ubic::Daemon::start_daemon($self->{ubic_start_daemon_params}); };
	if ($@) {
		if ($@=~/already running/) {
			if ($self->{LOGGER}) {
				my $pid=$self->check_daemon(NULLVALUE => 0);
				$self->{LOGGER}->error($self->{PROGRAM_NAME}.": gia in esecuzione ($pid)");
			}
			return undef;
		}
		croak $@; 
	}
	elsif ($self->{LOGGER}) {
		my $pid=$self->check_daemon(NULLVALUE => 0);
		$self->{LOGGER}->info($self->{PROGRAM_NAME}.": ($pid)");
	}
	return $self;
}


sub stop_daemon {
	my ($self,%params)=@_;
	my %p=map { ($_,$params{$_ }) } qw(	TIMEOUT );
	$p{TIMEOUT}=$self->{TERM_TIMEOUT} unless defined $p{TIMEOUT};  
	my $p=$self->_convert_params(%p);
	my $r=Ubic::Daemon::stop_daemon($self->{ubic_start_daemon_params}->{pidfile},$p);
	if ($r=~/not running/i) {
		$self->{LOGGER}->warn($self->{PROGRAM_NAME}.': non in esecuzione')
			if $self->{LOGGER};
		return 0;
		
	}
	else {
		return 1;
	}	
}


sub Init {
	local $@;
	eval('use SIRTI::Proc::Proc_Daemon');
	croak $@ if $@;
	shift;
	return SIRTI::Proc::Proc_Daemon->Init(@_);
}

sub Instances {
	local $@;
	eval('use SIRTI::Proc::Proc_Daemon');
	croak $@ if $@;
	shift;
	return SIRTI::Proc::Proc_Daemon->Instances(@_); 
}


sub check_daemon  {
	my ($self,%params)=@_;
	my $status=Ubic::Daemon::check_daemon($self->{ubic_start_daemon_params}->{pidfile});
	return $params{NULLVALUE} unless defined $status;
	my $pid=$status->pid;
	$pid=$params{NULLVALUE} unless defined $pid;
	return $pid;
}

sub send_signal  {
	my ($self,$signal,$ref_err_str)=@_;
	unless (defined $signal) {
		$$ref_err_str = 'invalid signal';
		return 0;
	}
	my $status=Ubic::Daemon::check_daemon($self->{ubic_start_daemon_params}->{pidfile});
	unless ($status) {
		$$ref_err_str = 'not running';
		return 0;
	}
	eval { kill $signal, $status->pid };
	if ($@) {
		$$ref_err_str = "unable to send signal - $@";
		chomp $$ref_err_str;
		return 0;
	}
	return 1;
}



if (__FILE__ eq $0) {

	my @cmd=("/bin/sleep",10);

	my $d=SIRTI::Proc::Daemon->new(
		PROGRAM_NAME => 'prova_sleep'
		,EXEC	=> \@cmd
	);

	$d->start_daemon || print STDERR "gia in esecuzione\n";
	sleep 2;
	print STDERR $d->check_daemon(NULLVALUE => 'non in esecuzione'),"\n";
	sleep 2;
	$d->stop_daemon || print STDERR "stop senza esecuzione\n";
	sleep 2;
	print STDERR $d->check_daemon(NULLVALUE => 'ok - fermato'),"\n";
	
}

1;



__END__

=head1 NAME

B<SIRTI::Proc::Daemon> - Consente di 'trasformare' lo script chiamante in un I<demone>

=head1 SYNOPSIS

use SIRTI::Proc::Daemon;
SIRTI::Proc::Daemon->new->start_daemon || die "gia attivo";

=head1 DESCRIPTION

Incapsulamento del package B<Ubic::Daemon>
I metodi statici  B<Init> e  B<Instances>  sono mantenuti per compatibilita con la versione 0.01


=head1 FUNCTIONS 

questo modulo definisce le seguenti funzioni


new - costruttore 

    PARAMETRI:

        PROGRAM_NAME   =>     tag - default basename $0
        LOCK_DIR       =>     directory per contere i files di controllo  - vengono ricercate:
                                   la variabile d' ambiente LOCK_DIR
                                   la dir. $HOME/var/lock 
                                   la dir  /var/lock 
                                   la dir  $HOME/tmp 
                                   la dir  /tmp
                                   i file pid e lock  vengono creati nella directory $LOCK_DIR/$PROGRAM_NAME
        EXEC           =>     oggetto da demonizzare - puo essere:
                               un path di un eseguibile 
                               un array di parametri come nella chiamata system (ES:   [ qw( path_eseguibile -opt1 -opt2 arg1 arg2) ]    )
                               un puntatore ad una funzione
                               per default viene preso $0
        STDOUT         =>        nome file per redirezione stdout - per default stdout viene rediretto su /dev/null
        STDERR         =>      nome file per redirezione stderr - per default stderr viene rediretto su /dev/null
        LOG_CONTROLLER =>      nome file per log del processo controllore demoni; da usare per debug - per default nessuno    (nessun log prodotto)
        LOGGER         =>      oggetto logger tipo Log::Log4perl - per default nessuno    (il logger non e' usato)
        MKMODE         =>      permessi in caso di creazione di directory (tipicamente lock dir) - per default nessuno (viene assunto dal sistema)
        TERM_TIMEOUT   =>      numero secondi di attesa prima di inviare un sigTerm ed eventualmente un sigKill alla terminazione
                               default 10 sec

   RITORNO
    
        l' oggetto costruito ($self)



start_daemon - esegue lo start di EXEC in modalita demone
    
    RITORNO
            il metodo ritorna undef in caso esista gia una istanza in esecuzione 
            altrimenti ritorna $self



stop_daemon - esegue lo stop del demone

    PARAMETRI: 
        
        TIMEOUT - numero secondi di attesa prima di inviare un sigTerm ed eventualmente un sigKill
        default il parametro TERM_TIMEOUT nel costruttore
    
    RITORNO
    
        vero se ha effettuato lo stop altrimenti falso



check_daemon - esegue la verifica del demone in esecuzione

    PARAMETRI:

        NULLVALUE - stringa di ritorno in caso di fallimento - default undef

    RITORNO

        il pid del demone in esecuzione altrimenti il valore del parametro NULLVALUE
    


Init - va in modalita demone - metodo  legacy (usa SIRTI::Proc::Proc_Daemon)



Instances -  ritorna il numero di istanza - metodo legacy  (usa SIRTI::Proc::Proc_Daemon)



=cut

