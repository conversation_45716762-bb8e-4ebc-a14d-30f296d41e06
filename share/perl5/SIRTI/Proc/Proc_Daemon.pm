################################################################################
#
# SIRTI::Proc::Proc_Daemon
#
################################################################################

=head1 NAME

B<SIRTI::Proc::Daemon> - Consente di 'trasformare' lo script chiamante in un I<demone>

=head1 SYNOPSIS

  use SIRTI::Proc::Daemon;
  
  # Diventa un daemon
  SIRTI::Proc::Daemon->Init()

  # Testa la presenza di un altra istanza dello stesso script (nome 
  # script!!) prima di diventare un daemon
  SIRTI::Proc::Daemon->Init() unless SIRTI::Proc::Daemon->Instances();

=head1 DESCRIPTION

Subclassamento del package B<Proc::Daemon> (di cui espone tutti i metodi) con
l'aggiunta di un unico metodo B<Instances> che tenta di determinare se ci sono
altre istanze dello stesso script gia' in esecuzione.



=cut

	package SIRTI::Proc::Proc_Daemon;

	use strict;
	use warnings;
	use File::Basename;

	use base 'Proc::Daemon';



################################################################################
#  P R O P R I E T A '   P U B B L I C H E
################################################################################

	our $VERSION = '0.01';



################################################################################
#  M E T O D I   P R I V A T I
################################################################################




################################################################################
#  M E T O D I   P U B L I C I
################################################################################

=pod

=head1 METHODS

=over 4

=cut


=pod

=item Instances

Determina se ci sono altre istanze dello script in esecuzione

=cut
	sub Instances {
		my $ps_switches = 'auxwwwwwwwwwwwwww';
		my $check_running = qq{ps $ps_switches	\\
			| grep -v $ps_switches				\\
			| sed -e 's/ \\{2,\\}/ /g'			\\
			| cut -d ' ' -f 11-					\\
			| grep -v 'grep '					\\
			| sort								\\
			| uniq -c							\\
			| grep ${ \&basename($0) }			\\
			| sed -e 's/^ *\\([0-9]\\).*\$/\\1/'
		};
		my $instances = `$check_running`;
		chomp $instances;
		return ($instances - 1); # -1 in modo da eliminare l'istanza corrente
	}



=back

=head1 NOTES

=over 4

=item LIMITATIONS

Il metodo I<Instances> si limita a cercare in memoria un processo la cui descrizione 
contenga il nome dello script in esecuzione.

=item ENHANCEMENTS

E' da prevedere la specializzazione e/o parametrizzazione del metodo I<Instances> in 
modo da consentire l'individuazione di eventuali altre istanze attraverso meccanismi piu'
affidabili quali: file di lock, hashing, IPC, ecc.

=item DEPENDENCIES

  Proc::Daemon
  File::Basename

=back

=cut



###################################
#
# Testing the module...
#
###################################
if (__FILE__ eq $0) {

	####  test ###  
	package main;
	use strict;
	use warnings;
	use SIRTI::Proc::Daemon;
	# Testa la presenza di altre istanze dello script
	print "Checking for previous instances... ";
	my $inst = SIRTI::Proc::Daemon->Instances();
	if ( $inst > 0 ) {
		print "Found $inst instance(s)", "\n";
		print "I exit without becoming a daemon!", "\n";
		exit 1;
	} else {
		print "No instances found", "\n";
	}

	# Diventa un daemon
	print "I become a daemon (for 10 seconds)!\n";
	SIRTI::Proc::Daemon->Init();

	my $i = 0;
	while ($i++ < 10) {
		sleep(1);
	}

	exit 0;

} else {

	1;

}


__END__

=pod

=head1 BUGS

Eventuali bug riscontrati dovranno essere segnalati su B<ART - Global Services>: L<https://www.artnet.sirtisistemi.net/ARTIT/art> aprendo un apposito DEV-TICKET.

=head1 HISTORY

=over

=item Ver. 0.01

Prima release del modulo

=back

=head1 AUTHOR

Alvaro Livraghi <<EMAIL>>

=cut
