################################################################################
#
# SIRTI::Queue::Event
#
################################################################################
=pod

=head1 NAME

B<SIRTI::Queue::Event> - Oggetto che rappresenta un attivita' pendente (o evento) che richiede elaborazione
  
=head1 DESCRIPTION

L'oggetto SIRTI::Queue::Event viene creato dal processo B<ramq> e inviato ad un generico consumer
SIRTI::Queue::EventConsumer tramite il metodo B<consume_event( )>

=head1 METHODS

=head2 get_event_name( ) 

ritorna il tipo di evento che dobbiamo elaborare

=head2 get_source_ref( )

ritorna un riferimento (univoco o meno) utile al destinatario del messaggio per identificare l'oggetto del messaggio

=head2 get_status( ) 

ritorna l'esito della lavorazione; per convenzione, 0 indica che l'event/messaggio 
e' stato correttamente processato

=head2 get_target_ref( )

ritorna un riferiemto "chiave" inserito dal destinatario

=head2 get_reason( )

ritorna la "reason" inserita dal destinatario

=head2 get_info( ) 

ritorna un hashref con le info relative all'evento

=head2 get_data( )

ritorna un hashref con i dati associati all'evento

=head2 get_lookup( )

ritorna un arrayref con i dati dei vari lookup

=over 4

    [
        {
            'REASON' => '__000501256291__',
            'LOOKUP_DATE' => '20190206141127',
            'SESSION_ID' => '80384'
        }
    ]

=back
  
=cut

package SIRTI::Queue::Event;

use strict;
use warnings;
use Carp;
use Log::Log4perl qw(get_logger :levels :nowarn);

use base 'SIRTI::Base';

sub new {

    my $this = shift;
    my $class = ref($this) || $this;
    my %params = @_;
    my $self = bless( {}, $class );
    my $errmsg = '';

    $self->clear_error();

    croak $errmsg unless $self->check_named_params(
         ERRMSG     => \$errmsg
        ,PARAMS     => \%params
        ,MANDATORY  => {
             INFO                     => { isa => 'HASH' }
            ,DATA                     => { isa => 'HASH' }
            ,LOOKUP                   => { isa => 'ARRAY' }
        }
        ,OPTIONAL   => {
        }
    );

	$self->{__LOGGER__} = Log::Log4perl->get_logger( 'COMMON::LIB::' . __PACKAGE__ );
	$self->{__LOGGER__}->trace( __PACKAGE__ . '->new' );

	$self->{_INFO} = $params{INFO};
	$self->{_DATA} = $params{DATA};
	$self->{_LOOKUP} = $params{LOOKUP};

	return $self;
}

sub get_info { shift->{_INFO}; }

sub get_data { shift->{_DATA}; }

sub get_lookup { shift->{_LOOKUP}; }

sub get_event_name { shift->get_info()->{EVENT} }

sub get_source_ref { shift->get_info()->{SOURCE_REF} }

sub get_target_ref { shift->get_info()->{TARGET_REF} }

sub get_status { shift->get_info()->{STATUS} }

sub get_reason { shift->get_info()->{REASON} }

sub logger { shift->{__LOGGER__}; }

############################################################################
# UNIT TEST
############################################################################

if ( __FILE__ eq $0 ) {
}

1;
