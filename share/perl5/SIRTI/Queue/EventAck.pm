################################################################################
#
# SIRTI::Queue::EventAck
#
################################################################################
=pod

=head1 NAME

B<SIRTI::Queue::EventAck> - Oggetto che rappresenta un attivita' (o evento) che richiede la conferma di acknowledge

=head1 DESCRIPTION

L'oggetto SIRTI::Queue::EventAck viene creato dal processo B<ramq> e inviato ad un generico consumer
SIRTI::Queue::EventAckConsumer tramite il metodo B<consume_event_ack( EVENT_ACK => $evObj )>
 
=head1 METHODS

=head2 is_global( )

ritorna true se l'ack è di tipo globale, altrimenti ritorna false se l'ack è di tipo target

=head2 get_ack_data( )

se l'ack è di tipo target (non global) ritorna un array reference contenente gli ack data 
eventualmente prodotti dal target 

=head2 get_event( )

ritorna l'oggetto event (SIRTI::Queue::Event) che richiede acknowledge

 
=cut

package SIRTI::Queue::EventAck;

use strict;
use warnings;
use Carp;
use Log::Log4perl qw(get_logger :levels :nowarn);

use Scalar::Util qw(blessed);
use base 'SIRTI::Base';


sub new {

    my $this = shift;
    my $class = ref($this) || $this;
    my %params = @_;
    my $self = bless( {}, $class );
    my $errmsg = '';

    $self->clear_error();

    croak $errmsg unless $self->check_named_params(
         ERRMSG     => \$errmsg
        ,PARAMS     => \%params
        ,MANDATORY  => {
		     #reference a SIRTI::Queue::Event
             EVENT                    => { isa => 'SIRTI::Queue::Event' },
			 IS_GLOBAL                => { isa => 'SCALAR', list => [ 1, 0 ] }
        }
        ,OPTIONAL   => {
		     DATA                     => { isa => 'HASH' }
        }
    );

	unless( blessed( $params{EVENT} ) && $params{EVENT}->isa("SIRTI::Queue::Event") ) {
	    croak "BUG: Il parametro EVENT deve essere una reference all'oggetto SIRTI::Queue::Event";
	}
	
	$self->{__LOGGER__} = Log::Log4perl->get_logger( 'COMMON::LIB::' . __PACKAGE__ );
	$self->{__LOGGER__}->trace( __PACKAGE__ . '->new' );
	
	$self->{_EVENT} = $params{EVENT};
	$self->{_IS_GLOBAL} = $params{IS_GLOBAL};
	$self->{_DATA} = $params{DATA} if( exists $params{DATA} );

	return $self;
}

sub get_event { shift->{_EVENT}; }

sub is_global { shift->{_IS_GLOBAL}; }

sub get_ack_data  { 
    my $self = shift; 
	return exists $self->{_DATA} ? $self->{_DATA} : undef;
}
 
sub logger { shift->{__LOGGER__}; }

############################################################################
# UNIT TEST
############################################################################

if ( __FILE__ eq $0 ) {
}

1;
