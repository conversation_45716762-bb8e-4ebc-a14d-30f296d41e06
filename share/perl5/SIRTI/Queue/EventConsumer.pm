################################################################################
#
# SIRTI::Queue::EventConsumer
#
################################################################################
=pod

=head1 NAME

B<SIRTI::Queue::EventConsumer> - Interfaccia da implementare per la gestione di eventi con C<ramq>

=head1 SYNOPSIS

  package MyConsumer;

  use strict;
  use warnings;
  use Carp;
  use Data::Dumper;
  
  use base 'SIRTI::Queue::EventConsumer';
  
  #
  # override
  #
  sub get_managed_event_types { return []; }
  
  #
  # override
  #
  sub get_managed_source_refs { return []; }
  
  #
  # override
  #
  sub consume_event {
  	my $self = shift;
  	my %params = @_
  	my $event = $params{EVENT};
  	my $session = $params{SESSION};
  	my $run_test = $params{RUN_TEST};
  	
  	$self->logger()->info( "Elaboro un evento..." );
  	$self->logger()->info( "EVENT NAME: " . $event->get_event_name() );
  	$self->logger()->info( "INFO: " . Dumper( $event->get_info() ) );
  	$self->logger()->info( "DATA: " . Dumper( $event->get_data() ) );
  
  	# croak "STOP!!!" per fermare l'elaborazione
  
  	if ( int ( rand 2 ) ) {
  		return $self->consume(
  			REASON => 'test fetch',
  			STATUS => 0,
  			TARGET_REF => 'OK',
  			ACK_DATA => { NOME => 'Pippo' }
  		);
  	} else {
  		return $self->skip( REASON => 'test skip' );
  	}
  }
  
  1;

=head1 DESCRIPTION

La classe B<SIRTI::Queue::EventConsumer> descrive l'interfaccia che un consumer di eventi deve implementare per
poter essere utlizzato con C<ramq>.

Il nome della classe che implementa il consumer deve essere indicato, insieme ad altri parametri, in un file di
configurazione da passare come argomento a C<ramq> (vedi documentazione di C<ramq>). 

Segue lo schema di funzionamento di C<ramq> (tra parentesi il metodo del consumer che viene invocato nei vari step):

=over 12

=item 1. lettura e validazione del file di configurazione

=item 2. istanziazione dell'oggetto consumer

=item 3. inizializzazione (B<C<$consumer-E<gt>init()>>)

=item 4. impostazione dei filtri sugli eventi (B<C<$consumer-E<gt>get_managed_event_types()>> e B<C<$consumer-E<gt>get_managed_source_refs()>>)

=item 5. recupero degli eventi pending (B<C<$consumer-E<gt>before_consuming_events()>>)

=item 6. invocazione della procedura di elaborazione degli eventi (B<C<$consumer-E<gt>consume_event()>>)

=item 7. fetch o skip dell'evento

=item 8. commit se definito a livello di B<messaggio> nel file di configurazione

=item 9. ritorno al punto 6 finchè non sono stati lavorati tutti i messaggi recuperati

=item 10. uscita dal ciclo di elaborazione degli eventi (B<C<$consumer-E<gt>after_consuming_events()>>)

=item 11. commit se definito a livello di B<loop> nel file di configurazione

=item 12. ritorno al punto 5 se lanciato come demone

=item 13. esecuzione delle procedure di conclusione del processo di elaborazione degli eventi (B<C<$consumer-E<gt>finish()>>)

=item 14. commit se definito a livello di B<sessione> nel file di configurazione

=back
 
=cut

package SIRTI::Queue::EventConsumer;

use strict;
use warnings;
use Carp;
use Scalar::Util qw(blessed);
use Log::Log4perl qw(get_logger :levels :nowarn);

use API::ART;
use SIRTI::Queue::Response;

use base 'SIRTI::Base';

our $VERSION = '0.1';

=pod

=head1 METHODS

=cut

#
# should not be overriden
#
# new( DB => <SIRTI::DB>, WORK_CONTEXT => <HASHREF> )
#
# params:
# * DB : oggetto di tipo SIRTI::DB
# * WORK_CONTEXT : hashref di variabili in formato chiave valore che sono presenti nel file di
#     configurazione di ramq tramite la chiave WORK_CONTEXT
#

sub new {
    my $this = shift;
    my $class = ref($this) || $this;
    my %params = @_;
    my $self = bless( {}, $class );
    my $errmsg = '';

	$self->clear_error();

	croak $errmsg unless $self->check_named_params(
         ERRMSG              => \$errmsg
		,IGNORE_EXTRA_PARAMS => 1
        ,PARAMS     => \%params
        ,MANDATORY  => {
             DB                     => { isa => 'SIRTI::DB' }
            ,WORK_CONTEXT			=> { isa => 'HASH' }
        }
		,OPTIONAL => {
		     ART                    => { isa => 'API::ART' }
		}
    );

	my @parts = split( '::', $class );
	my $logger_name = $parts[0] . '::LIB::' . join( '::', @parts );

	$self->{__LOGGER__} = Log::Log4perl->get_logger( $logger_name );
	$self->{__LOGGER__}->trace( __PACKAGE__ . '->new' );

	$self->{_DB} = $params{DB};
	$self->{_ART} = $params{ART};
	$self->{_WORK_CONTEXT} = $params{WORK_CONTEXT};

	return $self;
}

=pod

=head2 init( )

Questo metodo viene invocato da C<ramq> subito dopo aver istanziato l'oggetto consumer. B<Può> essere sovrascritto
per effettuare delle routine di inizializzazione del consumer.

Da notare che questo è il punto giusto dove creare dei singleton che potranno essere mantenuti nella sessione tramite
la definizione di una chiave dell'oggetto. Attenzione: al fine di evitare la sovrascrittura accidentale delle chiavi
utilizzate dalla classe base C<SIRTI::Queue::EventConsumer>, non utilizzare chiavi che iniziano per doppio underscore
(C<__>).

=cut

#
# optionally override
#
sub init {
	return;
}

=pod

=head2 get_managed_event_types( )

Questo metodo B<DEVE> essere sovrascritto e ritornare un arrayref con l'elenco dei tipi di eventi che devono essere passati
al consumer. Nel caso in cui non debba essere applicato alcun filtro sugli eventi della coda, deve ritornare una
reference a un array vuoto.

=cut

#
# override
#
sub get_managed_event_types { croak "This method must be overridden!\n"; }

=pod

=head2 get_managed_source_refs( )

Questo metodo B<DEVE> essere sovrascritto e ritornare un arrayref con l'elenco dei SOURCE_REF che devono essere passati
al consumer. Nel caso in cui non debba essere applicato alcun filtro sui SOURCE_REF, deve ritornare una
reference a un array vuoto.

=cut

#
# override
#
sub get_managed_source_refs { croak "This method must be overridden!\n"; }

=pod

=head2 before_consuming_events( NUM_EVENTI => C<SCALAR> )

=over

=item * B<NUM_EVENTI> : numero di eventi pending trovati che verranno inviati uno per volta alla funzione
C<consume_event()>

=back


Questo metodo viene invocato dopo aver recuperato gli eventi pending e prima di iniziare a inviarli uno alla volta al
consumer per implementare delle logiche di apertura del blocco delle lavorazioni.

Questo metodo B<può> essere sovrascritto.

=cut

#
# optionally override
#
sub before_consuming_events {
	return;
}

=pod

=head2 consume_event( EVENT => C<SIRTI::Queue::Event>, SESSION => <HASHREF>, RUN_TEST => <BOOLEAN> )

=over

=item * B<EVENT> : oggetto di tipo C<SIRTI::Queue::Event>

=item * B<SESSION> : HASHREF con le informazioni relative alla sessione

=item * B<RUN_TEST> : true se modalita' commit

=back


Questo metodo, che B<DEVE> essere sovrascritto, viene invocato una volta per ciascun evento che viene recuperato
dalla coda.

Deve ritornare un oggetto l'esito del metodo di utilità C<consume()> (es. C<return consume( ... )>)
per indicare a C<ramq> che la remote activity deve essere marcata come lavorata, oppure l'esito del metodo di utilità
C<skip()> (es. C<return skip( ... )>) per indicare a C<ramq> che la remote activity non deve essere marcata come
lavorata, e quindi riproposta al prossimo giro. Può inoltre lanciare una eccezione in caso di problemi fatali per
comunicare C<ramq> di interrompere le elaborazioni.

L'oggetto di tipo C<SIRTI::Queue::Event> espone i seguenti metodi:

=over

=item * get_event_name() : ritorna il tipo di evento che dobbiamo elaborare

=item * get_source_ref() : ritorna un riferimento (univoco o meno) utile al destinatario del 
messaggio per identificare l'oggetto del messaggio

=item * get_info() : ritorna un hashref con le info relative all'evento

=item * get_data() : ritorna un hashref con i dati associati all'evento
 
=back


=cut

#
# override
#
sub consume_event { croak "This method must be overridden!\n"; }

=pod

=head2 after_consuming_events( )

Questo metodo viene invocato dopo aver elaborato tutti gli eventi pending e prima di effettuare una nuova ricerca.

Questo metodo B<può> essere sovrascritto per implementare delle logiche di chiusura su un blocco eventi elaborati.

Può lanciare una eccezione in caso di problemi fatali per comunicare C<ramq> di interrompere le elaborazioni.

=cut

#
# optionally override
#
sub after_consuming_events {
	return;
}

=pod

=head2 finish( )

Questo metodo viene invocato da C<ramq> prima di terminare. B<Può> essere sovrascritto per
effettuare delle routine di conclusione del processo di elaborazione degli eventi.

Può lanciare una eccezione in caso di problemi fatali per comunicare C<ramq> di interrompere le elaborazioni.

=cut

#
# optionally override
#
sub finish {
    return;
}

=pod

=head1 UTILITY METHODS

In questa sezione vengono elencate dei metodi di utilità che possono essere utlizzati dal consumer.

=cut

#
# utilities
#

=pod

=head2 consume( REASON => C<SCALAR>, STATUS => C<SCALAR>, TARGET_REF => C<SCALAR>, ACK_DATA => C<HASHREF> )

=over

=item * B<REASON> : un commento sulla lavorazione effettuata

=item * B<STATUS> : numero intero non negativo che indica l'esito della lavorazione (per convenzione 0 viene utilizzato
per indicare che la lavorazione ha avuto successo, un numero positivo per indicare che la lavorazione non è andata a
buon fine)

=item * B<TARGET_REF> : stringa che indica un riferimento nel contesto del servizio target

=item * B<ACK_DATA> : opzionale. hashref chiave valore che può essere utilizzato per comunicare informazioni al servizio sorgente
dell'evento

=back

Questo metodo viene invocato al termine dell'override del metodo C<consume_event()> (es. C<return consume( ... )>)
per indicare a C<ramq> che la remote activity deve essere marcata come lavorata.

=cut

sub consume { shift->_response( FETCH_RESULT => 1, @_ ) }
	
=pod

=head2 skip( REASON => C<SCALAR> )

=over

=item * B<REASON> : il motivo per cui non si è lavorato l'evento

=back


Questo metodo viene invocato al termine dell'override del metodo C<consume_event()> (es. C<return skip( ... )>)
per indicare a C<ramq> che la remote activity non deve essere marcata come lavorata, e quindi riproposta al prossimo
giro.

=cut

sub skip { shift->_response( FETCH_RESULT => 0, @_ ) }
	
#
# metodo privato
#
# params: vedi SIRTI::Queue::Response::set_fetch_result()
#
# restituisce un oggetto di tipo SIRTI::Queue::Response
#

sub _response {
	my $self = shift;
	my %params = @_;
	
	$self->clear_error();
	my $response = eval { SIRTI::Queue::Response->new( @_ ); };
	$self->last_error( $@ )
		&& return undef
			if $@;
	
	return $response;
}

=pod

=head2 db( )

Questo metodo restituisce un oggetto di tipo C<SIRTI::DB> con la connessione corrente al database.

=cut

sub db { shift->{_DB} }


=pod

=head2 art( )

Questo metodo restituisce un oggetto API::ART nel caso in cui C<ramq> sia stato
configurato per accedere al DB utilizzando ART. Diversamente viene restituito undef
 
=cut

sub art { shift->{_ART} }


=pod

=head2 logger( )

Questo metodo restituisce un oggetto logger che può essere utilizzato per effettuare il logging del consumer
con i metodi C<trace()>, C<debug()>, C<info()>, C<warn()>, C<error()> e C<fatal()>.
Nota: il logger restituito è di tipo Log::Log4perl::Logger. Se nome del package è ad esempio PIPPO::Pluto::Paperino
la categoria del logger instanziato sarà PIPPO::LIB::PIPPO::Pluto::Paperino (vedi http://search.cpan.org/~mschilli/Log-Log4perl/)

=cut

sub logger { shift->{__LOGGER__} }

=pod

=head2 work_context( $name )

=over

=item * B<$name> : C<SCALAR>, nome della properties che deve essere restituita, opzionale

=back


Questo metodo permette di recuperare le properties che sono state caricate dal file di configurazione di C<ramq>
tramite la chiave C<WORK_CONTEXT>. Se viene invocata senza parametro viene restuito un hashref con tutte le properties
definite.

=cut

sub work_context {
	my $self = shift;
	my $name = shift;
	if ( defined $name ) {
	    return 
		   ( not exists $self->{_WORK_CONTEXT}->{$name} ) ? undef :
		       $self->{_WORK_CONTEXT}->{$name};
	}		    
	
	return $self->{_WORK_CONTEXT};
}

=pod

=head2 last_error( $msg )

=over

=item * B<$msg> : C<SCALAR>, messaggio di errore, opzionale

=back


Se viene invocata con il parametro $msg viene settato il messaggio di errore.
Ritorna sempre l'ultimo messaggio di errore settato. 

=cut
 
=pod

=head1 SEE ALSO

Script perl B<ramq>, package B<SIRTI::Queue::Event>, B<SIRTI::Queue::EventAckConsumer>, B<SIRTI::Queue::EventAck>


=head1 BUGS

Eventuali bug riscontrati dovranno essere segnalati su B<ART - Global Services>: L<https://www.artnet.sirtisistemi.net/ARTIT/art> aprendo un apposito DEV-TICKET.

=head1 HISTORY

=over

=item Ver. 0.1

Prima release del modulo

=back


=head1 AUTHOR

Gruppo OLO :)

=cut

1;
