################################################################################
#
# SIRTI::Queue::EventConsumerStepper
#
################################################################################
=pod

=head1 NAME

B<SIRTI::Queue::EventConsumerStepper> - Interfaccia da implementare per la gestione di eventi con C<ramq>

=head1 SYNOPSIS
 
=head1 DESCRIPTION
 
=cut

package SIRTI::Queue::EventConsumerStepper;

use strict;
use warnings;
use Carp;
use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

# uso il backend JSON::PP per supportare i metodi
# allow_barekey e allow_singlequote
BEGIN { $ENV{PERL_JSON_BACKEND} = 'JSON::PP' }
use JSON -support_by_pp;

use SIRTI::DB::Oracle;
use SIRTI::Queue::Response;
use API::ART;
use API::ART::Activity::Factory;
use SIRTI::ART::RemoteActivity::Source; 
 
use SIRTI::Queue::EventConsumerStepper::ErrorException;
use SIRTI::Queue::EventConsumerStepper::SkipException;
use SIRTI::Queue::EventConsumerStepper::OperationDoneException; 

use API::ART::Collection::Activity;

use File::Basename;

use constant STEPPER_RESULT_SKIP    => '0';
use constant STEPPER_RESULT_CONSUME => '1';
 
use constant STEPPER_EVENT_OK       => '0';
use constant STEPPER_EVENT_KO       => '1';


our $VERSION = '0.1';

use base 'SIRTI::Queue::EventConsumer';


=pod

=head1 METHODS

=cut

sub new {
    my $this = shift;
    my $class = ref($this) || $this;
   
    my $self = $class->SUPER::new( @_ );
    bless ( $self, $class );

    $self->logger()->info( "Inizializzazione STEPPER" );   
    $self->read_config_file( );
    $self->validate_config( );
    return $self;
}

sub init {
    my $self = shift;
    $self->{__API_ART_COLLECTION_ACTIVITY__} = API::ART::Collection::Activity->new(ART => $self->art);

    my $stepEngineConf = $self->get_stepper_config()->{STEP_ENGINE};

    if( (ref($stepEngineConf) eq 'HASH') && defined($stepEngineConf->{CLASS})  ) {

        if( ref( \$stepEngineConf->{CLASS} ) ne 'SCALAR' ) {
            croak "ERRORE: configurazione non valida: parametro CONF->STEP_ENGINE->CLASS, atteso scalare";
        }

        my $package = $stepEngineConf->{CLASS};

        $self->logger()->info("Caricamento ENGINE stepper package '" . $package . "'");
      
        eval "use $package;"; 
        if( $@ ) {
            croak "ERRORE: '" . $package . "': $@";
        }

        my $engineObj = eval { $package->new( ART => $self->art( ), BINDING => 1 ) };
        if( $@ ) {
            croak "ERRORE: creando istanza ENGINE stepper class '" . $package . "': $@";
        }

        if( ! UNIVERSAL::can( $engineObj, "handle" ) ) { 
            croak "ERRORE: la classe ENGINE non contiene il metodo 'handle'";   
        } 

        $self->{__ENGINE_STEPPER_CLASS__} = $engineObj;
    }
    else {
        $self->{__ENGINE_STEPPER_CLASS__} = undef;
    }

    return
}

sub _get_collection_activity {
	shift->{__API_ART_COLLECTION_ACTIVITY__};
}

sub _get_engine_stepper {
        shift->{__ENGINE_STEPPER_CLASS__};
}


# Override SIRTI::Queue::EventConsumer
sub get_managed_event_types {  shift->get_stepper_config( )->{MANAGED_EVENT_TYPES} }


# Override SIRTI::Queue::EventConsumer
sub get_managed_source_refs {  return [ ]  }


=pod

=head2 read_config( )

Questo metodo viene invocato dal metodo new C<EventConsumerStepper> per leggere la configurazione
del procedura di step automatica da file esterno.
Il nome del file di configurazione si trova nel parametro STEPPER_CONFIG_FILE
inserito nel WORK_CONTEXT del file di configurazione principale del consumer

Il formato del file di configurazione dello stepper e' JSON

croak in caso di errore

=cut

sub read_config_file {
   my ( $self ) = @_;
  
   $self->logger()->info( "Lettura configurazione STEPPER" );
  
   my $filename = $self->work_context("STEPPER_CONFIG_FILE");

   croak "Parametro STEPPER_CONFIG_FILE mancante o non valido"
       if (!defined $filename or ref(\$filename) ne 'SCALAR' );

   $filename =~ s/^\s+//;
   
   eval {
      my $json = new JSON;  
      $json->allow_barekey( 1 );
      $json->allow_singlequote( 1 );
   
      #lettura file di conf
      local $/;
      croak "impossibile aprire il file : $!"
          if !open( my $fin, "<$filename" );
          
      my $json_text = <$fin>;
      close( $fin );
      
      #parse json
      my $conf = eval { $json->decode( $json_text ) };
      croak $@ if( $@ );
     
      $json_text = undef;
      $self->{STEPPER_CONFIG} = $conf;     
   };
   if( $@ ) {
      croak "Errore leggendo file di configurazione '$filename' : $@"
   } 
   return;
}


=pod

=head2 validate_config( )

Questo metodo viene invocato dal metodo new C<EventConsumerStepper> per validare e completare
la configurazione, letta in precedenza da read_config_file e memorizzato 
come hash reference in $self->{STEPPER_CONF}
 
croak in caso di errore, altimenti il metodo ritorna 'undef'.
 
=cut

sub validate_config {
    my ($self) = @_;
    my $conf = $self->get_stepper_config( );
   
    croak "BUG: configurazione mancante . Parametro \$self->{STEPPER_CONFIG} deve contenere un hash reference valida"
        if( ref($conf) ne 'HASH' );
   
    $self->logger()->info( "Validazione configurazione" );

    $self->validate_and_setup_MANAGED_EVENTS_TYPES( );    
    $self->validate_and_setup_SKIP_ON_ERROR( );
    #should be a module
    $self->validate_and_setup_ACK_BARRIER( );
    #should be a module
    $self->validate_and_setup_EVENT_SKIP_ZONE_MAP( ); 
    $self->validate_and_setup_RA_DATA_KEY_FOR_ACTIVITY_ID( );
    #should be a module
    $self->validate_and_setup_ACTION_MAP( );
    $self->validate_and_setup_DTA_LENGTH_LIMIT_MAP( );
    $self->validate_and_setup_EVENT_DATA_TO_STEP_PROPERTIES_MAP( );
    $self->validate_and_setup_PRE_STEP_HOOK_MODULES( );
    $self->validate_and_setup_POST_STEP_HOOK_MODULES( );
    
    return;
}


sub validate_and_setup_MANAGED_EVENTS_TYPES {
    my ($self) = shift;
    my $conf = $self->get_stepper_config( );
    
    if( exists $conf->{MANAGED_EVENT_TYPES} || ref($conf->{MANAGED_EVENT_TYPES}) eq 'ARRAY' ) {
        my $elist = $conf->{MANAGED_EVENT_TYPES};
        my $idx = 0;
        for( ; $idx < scalar( @$elist ); $idx++ ) {
             last if !defined $elist->[$idx] 
                     or
                     ( ref(\$elist->[$idx]) ne 'SCALAR' );
             exp_envstr( \$elist->[$idx] );
        }
        return if $idx == scalar( @$elist ); 
    }
    
    croak "Parametro MANAGED_EVENT_TYPES mancante o in formato non corretto";
}

sub validate_and_setup_SKIP_ON_ERROR {
    my ($self) = @_;
    my $conf = $self->get_stepper_config( );
   
    if( !defined $conf->{SKIP_ON_ERROR} ) {
        $conf->{SKIP_ON_ERROR} = 0;
    }
    else {
        exp_envstr( \$conf->{SKIP_ON_ERROR} );

        if( $conf->{SKIP_ON_ERROR} =~ /^\s*(TRUE|YES|1|ENABLE)\s*$/i ) {
            $conf->{SKIP_ON_ERROR} = 1;
        }
        elsif( $conf->{SKIP_ON_ERROR} =~ /^\s*(FALSE|NO|NONE|DISABLED?|0)\s*$/i ) {
               $conf->{SKIP_ON_ERROR} = 0;
        }
        else {
            croak "Valore parametro SKIP_ON_ERROR non valido";
        }
    }
    return;
}

sub validate_and_setup_ACK_BARRIER {
    my ($self) = @_;
    my $conf = $self->get_stepper_config( );
   
    if( ! exists $conf->{ACK_BARRIER} || ref($conf->{ACK_BARRIER}) ne 'HASH' ) {
        croak "Parametro ACK_BARRIER mancante o non valido";
    }
    if( ! exists $conf->{ACK_BARRIER}->{DB} || ref($conf->{ACK_BARRIER}->{DB}) ne 'HASH' ) {
        croak "Parametro ACK_BARRIER DB mancante o non valido";
    }
    if( ! exists $conf->{ACK_BARRIER}->{QUEUE} || ref($conf->{ACK_BARRIER}->{QUEUE}) ne 'HASH' ) {
        croak "Parametro ACK_BARRIER QUEUE mancante o non valido";
    }
   
    my $dbMap = $conf->{ACK_BARRIER}->{DB};
    my $qMap = $conf->{ACK_BARRIER}->{QUEUE};
   
    #
    # PARSE DB LIST
    #
   
    for my $dbId ( keys %{$dbMap} ) {
   
        $dbMap->{$dbId}->{__HANDLE__} = undef;
        $dbMap->{$dbId}->{__CONNECTOR__} = undef;   
   
        #connessione via SIRTI::DB ?
        if( exists $dbMap->{$dbId}->{CONNECT_STRING} ) {
            
            my $connect_string = $dbMap->{$dbId}->{CONNECT_STRING};
            if( !defined $connect_string || ref(\$connect_string) ne 'SCALAR' ) {
                croak "Parametro ACK_BARRIER, DB '$dbId' : CONNECT_STRING non valida";
            }
            exp_envstr( \$connect_string );
            $dbMap->{$dbId}->{__CONNECTOR__} = sub { 
                SIRTI::DB::Oracle->new( $connect_string ) 
                  or
                 croak "Impossibile instanziare oggetto SIRTI::DB::Oracle (metodo new() restituisce undef)";
            }
        }
        #connessione via ART ?
        elsif ( exists $dbMap->{$dbId}->{ART} ) {
   
            my $artConf = $dbMap->{$dbId}->{ART};
            if( !defined $artConf || ref($artConf) ne 'HASH' ) {
                croak "Parametro ACK_BARRIER, DB '$dbId' : oggetto ART non valido";
            }
        
            for( qw/ARTID USER PASSWORD AUTOSAVE DEBUG/ ) {
                 exp_envstr( \$artConf->{$_} ) if exists $artConf->{$_};
            }

            $dbMap->{$dbId}->{__CONNECTOR__} =
               sub {
                   my $aConf = $artConf;
                   my @args = ( );
                   
                   map {
                      push @args, ( $_, $aConf->{$_} ) if exists $aConf->{$_};
                   } qw( ARTID USER PASSWORD AUTOSAVE DEBUG );
                   
                   my $artObj = API::ART->new( @args );
                                  
                   croak "Impossibile instanziare oggetto API::ART"
                       if !defined $artObj;
                 
                   my $connect_string = $artObj->info( "CONNECTSTRING" );
                   croak "Impossibile ottenere la stringa di connessione al DB tramite oggetto API::ART (restituito undef)"
                       if !defined $connect_string;
                 
                   return SIRTI::DB::Oracle->new( $connect_string )
                       or croak "Impossibile instanziare oggetto SIRTI::DB::Oracle (metodo new() restituisce undef)";
               };
        }
        #errore
        else {
            croak "Parametro ACK_BARRIER, DB '$dbId' : modalita' di connessione al DB mancante";
        }
    }
   
    #
    # PARSE QUEUES
    #
    for my $qid ( keys %{$qMap} )
    {
        my $qDef = $qMap->{$qid};
        my $idx;
       
        if( ref($qDef) ne 'HASH' ) {
            croak "Parametro ACK_BARRIER, CODA '$qid' : formato configurazione non valido";
        }
       
        if(
            ( !exists $qDef->{DB_ID} || ref(\$qDef->{DB_ID}) ne 'SCALAR' )
             or
            ( !exists $dbMap->{$qDef->{DB_ID}} )
          )
        {
            croak "Parametro ACK_BARRIER, CODA '$qid' : DB reference mancante o non valida";
        }
       
        $qDef->{__HANDLE__} = undef;
        $qDef->{__CONNECTOR__} = undef;   
   
        if( !exists $qDef->{EVENTS} || ref( $qDef->{EVENTS} ) ne 'ARRAY' ) {
            croak "Parametro ACK_BARRIER, CODA '$qid' : valore per chiave EVENTS non valido";
        }
   
        for( $idx = 0; $idx < scalar(@{$qDef->{EVENTS}}); $idx++ ) {
             if( !defined $qDef->{EVENTS}->[$idx] || ref(\$qDef->{EVENTS}->[$idx]) ne 'SCALAR' ) {
               croak "Parametro ACK_BARRIER, CODA '$qid' : valore per chiave EVENTS non valido";
             }    
             $qDef->{EVENTS}->[$idx] = uc ${ exp_envstr( \$qDef->{EVENTS}->[$idx] ) };
        }
   
        if( exists $qDef->{ON_EVENTS} ) {
   
             if( ref( $qDef->{ON_EVENTS} ) ne 'ARRAY' ) {
                 croak "Parametro ACK_BARRIER, CODA '$qid' : valore per chiave ON_EVENTS non valido";
             }
             my $on_events = { };
             for( $idx = 0; $idx < scalar(@{$qDef->{ON_EVENTS}}); $idx++ ) {
                  if( !defined $qDef->{ON_EVENTS}->[$idx] || ref(\$qDef->{ON_EVENTS}->[$idx]) ne 'SCALAR' ) {
                      croak "Parametro ACK_BARRIER, CODA '$qid' : valore per chiave ON_EVENTS non valido";
                  }
                  $on_events->{ uc ${ exp_envstr( \$qDef->{ON_EVENTS}->[$idx] ) } } = undef;
             }
             $qDef->{ON_EVENTS} = $idx ? $on_events : undef;
        }
        else {
             $qDef->{ON_EVENTS} = undef;
        }

        $qDef->{__CONNECTOR__} = sub {  
              my $qD = $qDef;
              my @params = ( 
                   'DB', shift,
                   'SESSION_DESCRIPTION', "EventConsumerStepper - PID: $$",
              );
              
              map {
                 push @params, $_, $qD->{$_} if exists $qD->{$_};
              } qw( SOURCE_SERVICE SOURCE_CONTEXT TARGET_SERVICE TARGET_CONTEXT TARGET );
               
              SIRTI::ART::RemoteActivity::Source->new( @params )
                 or
              croak "SIRTI::ART::RemoteActivity::Source : metodo new() restuisce undef";
        };
    }
    return;
}
    
sub validate_and_setup_EVENT_SKIP_ZONE_MAP {
    my ($self) = shift;
    my $conf = $self->get_stepper_config( );
    my $map = $conf->{EVENT_SKIP_ZONE_MAP} if exists $conf->{EVENT_SKIP_ZONE_MAP};
    
    if( ref($map) ne 'HASH' ) {
        croak "Parametro EVENT_SKIP_ZONE_MAP mancante o non valido";
    }
   
    my $eventSkipZoneMap = { };
    for my $aid ( sort keys %$map ) {

        my $ACTIVITY_TYPE_ID;
        if( $aid =~ /^\s*ACTIVITY_TYPE\s*:\s*([^\s]+)$/i ) {
            $ACTIVITY_TYPE_ID = $1;
            $ACTIVITY_TYPE_ID =~ s/\s+$//;
        }       

        if( !defined $ACTIVITY_TYPE_ID or ( $ACTIVITY_TYPE_ID eq '' ) or (ref($map->{$aid}) ne 'HASH') ) {
            croak "Parametro EVENT_SKIP_ZONE_MAP, ACTIVITY_TYPE_ID '$aid' non valido";
        }

        for my $eid ( sort keys %{$map->{$aid}} ) {
                
                my $EVENT_ID;
                if( $eid =~ /^\s*EVENT\s*:\s*([^\s]+)$/i ) {
                    $EVENT_ID = $1;
                    $EVENT_ID =~ s/\s+$//;
                }
                if( !defined $EVENT_ID or ($EVENT_ID eq '') or (ref($map->{$aid}->{$eid}) ne 'HASH') ) {
                    croak "Parametro EVENT_SKIP_ZONE_MAP, ACTIVITY_TYPE_ID '$aid', EVENTO '$eid' non valido";
                }
               
                
                my $zoneMap = $map->{$aid}->{$eid}; 
                my $skipZone = { };
                for my $zid ( sort keys %$zoneMap ) {
                    my ($ZONE_ID);
                    if( $zid =~ /^\s*ZONE\s*:\s*([^\s]+)$/i ) {
                        $ZONE_ID = $1;
                        $ZONE_ID =~ s/\s+$//;
                    }
                    if( !defined $ZONE_ID or ( $ZONE_ID eq '' ) ) {
                        croak "Parametro EVENT_SKIP_ZONE_MAP, ACTIVITY_TYPE_ID '$aid', EVENTO '$eid': formato nome zona '$zid' non corretto";
                    }
                    my $zDef = $zoneMap->{$zid};
                    if( ( ref($zDef) ne 'HASH' )
                        or
                        ( !exists $zDef->{START_STATUS} or (ref($zDef->{START_STATUS}) ne 'ARRAY') )
                        or
                        ( !exists $zDef->{END_STATUS} or (ref($zDef->{END_STATUS}) ne 'ARRAY') )
                       )
                    {
                       croak "Parametro EVENT_SKIP_ZONE_MAP, ACTIVITY_TYPE_ID '$aid', EVENTO '$eid', formato zona '$zid' non valido";
                    }
                    
                    my $idx;
                    my $sList = $zDef->{START_STATUS};
                    for( $idx = 0; $idx < scalar(@$sList); $idx++ ) {
                         last if !defined $sList->[$idx] 
                                or 
                                ( ref(\$sList->[$idx]) ne 'SCALAR' );
                    }
                    if( !$idx or $idx != scalar(@$sList) ) {
                        croak "Parametro EVENT_SKIP_ZONE_MAP, ACTIVITY_TYPE_ID '$aid', EVENTO '$eid', zona '$zid', formato parametro 'START_STATUS' non valido";
                    }
            
                    $sList = $zDef->{END_STATUS};
                    for( $idx = 0; $idx < scalar(@$sList); $idx++ ) {
                         last if !defined $sList->[$idx] 
                                or 
                                ( ref(\$sList->[$idx]) ne 'SCALAR' );
                    }
                    if( !$idx or $idx != scalar(@$sList) ) {
                        croak "Parametro EVENT_SKIP_ZONE_MAP, ACTIVITY_TYPE_ID '$aid', EVENTO '$eid', zona '$zid', formato parametro 'END_STATUS' non valido";
                    }

                    $skipZone->{$ZONE_ID}->{START_STATUS} = $zDef->{START_STATUS};
                    $skipZone->{$ZONE_ID}->{END_STATUS} =  $zDef->{END_STATUS};
                }
                
                $eventSkipZoneMap->{$ACTIVITY_TYPE_ID}->{$EVENT_ID} = $skipZone;
            }    
    }

    $conf->{EVENT_SKIP_ZONE_MAP} = $eventSkipZoneMap;
    return;
}    

     
sub validate_and_setup_RA_DATA_KEY_FOR_ACTIVITY_ID {
    my ( $self ) = @_;
    my $conf = $self->get_stepper_config( );
    my $val = $conf->{RA_DATA_KEY_FOR_ACTIVITY_ID};
    
    if( !defined $val || ( ref(\$val) ne 'SCALAR' ) ) {
    	croak "Parametro RA_DATA_KEY_FOR_ACTIVITY_ID || ACTIVITY_BY_COLLECTION_FIND mancante o in formato non valido"
    		if (!exists $conf->{ACTIVITY_BY_COLLECTION_FIND});
    	return;
    }
    exp_envstr( \$conf->{RA_DATA_KEY_FOR_ACTIVITY_ID} );
    return;
}

sub validate_and_setup_ACTION_MAP {
    my ( $self ) = @_;
    my $conf = $self->get_stepper_config( );

    my $map = $conf->{ACTION_MAP} if exists $conf->{ACTION_MAP};
    
    if( ref($map) ne 'HASH' ) {
        croak "Parametro ACTION_MAP mancante o non valido";
    }
   
    my $tids_count = 0;   
    my $act_map = { };
  
    for my $tid ( keys %$map ) {
   
        my $tid_actions_count = 0;
        if( $tid !~ /^\s*ACTIVITY_TYPE_NAME\s*:\s*([^\s]+)$/i ) {
            croak "Parametro ACTION_MAP, il tipo attivita' '$tid', format nome del 'tipo attivita' non valido";
        }
        my $ACT_TYPE_ID = $1;

        my $tidMap = $map->{$tid};
        if( ref($tidMap) ne 'HASH' ) {
            croak "Parametro ACTION_MAP, tipo attivita' '$tid', mappa evento/azione mancante o in formato non corretto";
        }      
        for my $eid ( keys %$tidMap ) {
            my ($e, $a);
            if( $eid =~ /^\s*EVENT\s*:\s*([^\s]+)$/i  ) {
                $e = $1;
                $e =~ s/\s+$//;
            }
            if( !defined $e or ( $e eq '' ) ) {
                croak "Parametro ACTION_MAP, tipo attivita' '$tid', formato nome evento '$eid' non corretto";
            }
          
            my $aid = $tidMap->{$eid};
            if( defined $aid && ( $aid =~ /^\s*ACTION\s*:\s*([^\s]+)$/i ) ) {
                $a = $1;
                $a =~ s/\s+$//;
            }        
            if( !defined $a or ( $a eq '' ) ) {
                croak "Parametro ACTION_MAP, tipo attivita' '$tid', evento '$eid' : nome attivita' '$aid' non valido";
            }
             
            $act_map->{$ACT_TYPE_ID}->{$e} = $a;  
            $tid_actions_count++;
        } 
      
        croak "Parametro ACTION_MAP, tipo attivita' '$tid' : mappa evento/azione vuota"
            if !$tid_actions_count;
        $tids_count++;
    }
    
    croak "Parametro ACTION_MAP non valido: mappa eventi/azioni vuota"
        if !$tids_count;
   
    $conf->{ACTION_MAP} = $act_map;
    return;
}


sub validate_and_setup_DTA_LENGTH_LIMIT_MAP {
    my ($self) = @_;
    my $conf = $self->get_stepper_config( );
   
    if( exists( $conf->{DTA_LENGTH_LIMIT_MAP} ) ) {

        croak "Formato parametro DTA_LENGTH_LIMIT_MAP non valido." 
                . "Atteso HASH_REF"
            if ref($conf->{DTA_LENGTH_LIMIT_MAP}) ne 'HASH';

        for( keys %{$conf->{DTA_LENGTH_LIMIT_MAP}} ) {
             croak "Formato parametro DTA_LENGTH_LIMIT_MAP non valido."
                     . "Chiave '$_': atteso come valore un HASHREF"
                 if ref($conf->{DTA_LENGTH_LIMIT_MAP}->{$_}) ne 'HASH';
        }
    }
    else {
        $conf->{DTA_LENGTH_LIMIT_MAP} = { };
    }

    return;
}

sub validate_and_setup_EVENT_DATA_TO_STEP_PROPERTIES_MAP {
    my ( $self ) = @_;
    my $conf = $self->get_stepper_config( );

    my $map = exists $conf->{EVENT_DATA_TO_STEP_PROPERTIES_MAP} 
                ? $conf->{EVENT_DATA_TO_STEP_PROPERTIES_MAP} : { };
              
    if( ref($map) ne 'HASH' ) {
        croak "Parametro EVENT_DATA_TO_STEP_PROPERTIES_MAP mancante o non valido";
    }
   
    my $tMap = { };
  
    for my $tid ( keys %$map ) {
        my ($ACTIVITY_TYPE_ID);
        if( $tid =~ /^\s*ACTIVITY_TYPE\s*:\s*(.+)$/i ) {
            $ACTIVITY_TYPE_ID = $1;
            $ACTIVITY_TYPE_ID =~ s/\s+$//;
        }
        if( !defined $ACTIVITY_TYPE_ID or ($ACTIVITY_TYPE_ID eq '') ) {
            croak "Parametro EVENT_DATA_TO_STEP_PROPERTIES_MAP, tipo attivita' '$tid', con formato nome non valido";
        }
        
        my $eventMap = $map->{$tid};
        if( ref($eventMap) ne 'HASH' ) {
            croak "Parametro EVENT_DATA_TO_STEP_PROPERTIES_MAP, tipo attivita' '$tid', non valido. Atteso hash reference";
        }      
        for my $eid ( keys %$eventMap ) {
            my ($EVENT_ID);
            if( $eid =~ /^\s*EVENT\s*:\s*(.+)$/i  ) {
                $EVENT_ID = $1;
                $EVENT_ID =~ s/\s+$//;
            }
            if( !defined $EVENT_ID or ( $EVENT_ID eq '' ) ) {
                croak "Parametro EVENT_DATA_TO_STEP_PROPERTIES_MAP, tipo attivita' '$tid', formato nome evento '$eid' non corretto";
            }
         
            my $propMap = $eventMap->{$eid};
            if( ref($propMap) ne 'HASH' ) {
                croak "Parametro EVENT_DATA_TO_STEP_PROPERTIES_MAP, tipo attivita' '$tid', evento '$eid', non valido: atteso hash reference";
            }
    
            for my $rid ( keys %$propMap ) {
                my ($RA_DATA_KEY, $PROP_DATA_KEY);
                
                if( $rid =~ /^\s*RA_DATA\s*:\s*(.+)$/i ) {
                    $RA_DATA_KEY = $1;
                    $RA_DATA_KEY =~ s/\s+$//;
                }
                if( defined $RA_DATA_KEY and ( $RA_DATA_KEY ne '' ) 
                    and defined $propMap->{$rid} 
                    and (ref(\$propMap->{$rid}) eq 'SCALAR' ) 
                  ) 
                {
                    if( $propMap->{$rid} =~ /^\s*PROPERTY\s*:\s*(.+)$/i ) {
                        $PROP_DATA_KEY = $1;
                        $PROP_DATA_KEY =~ s/\s+$//;
                    }
                }
                
                if( !defined $RA_DATA_KEY || !defined $PROP_DATA_KEY ||
                    ($RA_DATA_KEY eq '') || ( $PROP_DATA_KEY eq '') 
                  )
                {
                    croak "Parametro EVENT_DATA_TO_STEP_PROPERTIES_MAP, tipo attivita' '$tid', evento '$eid', RA DATA '$rid' non valida";
                }
                
                $tMap->{$ACTIVITY_TYPE_ID}->{$EVENT_ID}->{$RA_DATA_KEY} = $PROP_DATA_KEY;
            }
        }
    }
  
    $conf->{EVENT_DATA_TO_STEP_PROPERTIES_MAP} = $tMap;
    return;
}


sub validate_and_setup_PRE_STEP_HOOK_MODULES
{
    my ( $self ) = @_;
    my $conf = $self->get_stepper_config( );
    my $hlist = [ ];
   
    #TODO: intanziare oggetti PRE_STEP_HOOK_MODULES
    #
    #   in [ nome, nome2, nome3 ]
    #  out [ [ nome, objRef ], [ nome2, objRef2 ] ]
   
    $conf->{PRE_STEP_HOOK_MODULES} = $hlist;
    return;
}

sub validate_and_setup_POST_STEP_HOOK_MODULES
{
    my ( $self ) = @_;
    my $conf = $self->get_stepper_config( );
    my $hlist = [ ];
   
    #TODO: intanziare oggetti POST_STEP_HOOK_MODULES
    #
    #   in [ nome, nome2, nome3 ]
    #  out [ [ nome, objRef ], [ nome2, objRef2 ] ]
    
    $conf->{POST_STEP_HOOK_MODULES} = $hlist;
    return;
}

sub setup_work_area {
    my ($self, $event) = @_;
  
    my $woa = { EVENT => $event };  
    return $self->{__STEPPER_WORK_DATA__} = $woa;
}

sub get_work_area { shift->{__STEPPER_WORK_DATA__} }
 
sub clear_work_area { shift->{__STEPPER_WORK_DATA__} = { } }

 
#
# Override SIRTI::Queue::EventConsumer
#
sub consume_event {
    my $self = shift;
    my %params = @_;
    my $event = $params{EVENT};
 
    $self->logger()->info( "Elaborazione EVENTO" );   
    $self->logger()->info( "EVENT_NAME: '" . $event->get_event_name() . "'" );
     
    $self->setup_work_area( $event );
   
    eval {
       $self->do_get_ART_activity_id( );   
       $self->do_check_ack_barrier( );
       $self->do_create_ART_activity( );
       $self->do_check_ART_action_name( );
       $self->do_check_skip_zone( );
       $self->do_check_ART_action_permission( );
       $self->do_savepoint( );
       $self->do_set_ART_step_properties( );
       $self->do_run_pre_ART_step_hook_chain( );
       $self->do_ART_step( );
       $self->do_run_post_ART_step_hook_chain( );
    };
    
    my $except = $@;
    
    $self->logger( )->debug( "Elaborazione terminata" );
    
    if( $except ) 
    {  
        if( UNIVERSAL::isa( $except, 'SIRTI::Queue::EventConsumerStepper::ErrorException' ) ) {

            $self->logger( )->info( "Segnalato errore" );
        
            $self->do_savepoint_rollback( );
            $self->clear_work_area( );
            
            return $self->get_response_error(
                      REASON     => $except->get_reason( ),
                      TARGET_REF => $except->get_target_ref( )
            );
        }
        elsif ( UNIVERSAL::isa( $except , 'SIRTI::Queue::EventConsumerStepper::SkipException' ) ) {
    
            $self->logger( )->info( "Segnalata richiesta di skip evento " );
    
            $self->do_savepoint_rollback( );
            $self->clear_work_area( );
            
            return $self->get_response_skip( REASON => $except->get_reason( ) );
            
        }
        elsif ( UNIVERSAL::isa( $except , 'SIRTI::Queue::EventConsumerStepper::OperationDoneException' ) ) {

            $self->logger( )->info( "Segnalato evento di fine elaborazione" );
        
            $self->clear_work_area( );
            
            return $self->get_response_done( 
                      REASON     => $except->get_reason( ),
                      TARGET_REF => $except->get_target_ref( )
            );
            
        }
        else {
            croak $except;
        }
    }
  
    my $artActivityId = $self->get_work_area( )->{ART_ACTIVITY_ID};
    $artActivityId = 'activity ID mancante' if !defined $artActivityId;
    $self->clear_work_area( );    
    return $self->get_response_done( REASON => 'Ok', TARGET_REF => $artActivityId );
}

=pod

=head2 finish( )

Questo metodo viene invocato da C<ramq> prima di terminare. B<Puo'> essere sovrascritto per
effettuare delle routine di conclusione del processo di elaborazione degli eventi.

Puo' lanciare una eccezione in caso di problemi fatali per comunicare C<ramq> di interrompere le elaborazioni.

=cut

#
# optionally override
#
sub finish {
    return;
}

=pod

=head1 UTILITY METHODS

In questa sezione vengono elencate dei metodi di utilita'  che possono essere utlizzati dal consumer.

=cut

sub get_stepper_config { shift->{STEPPER_CONFIG} }

   
sub do_check_ack_barrier {
    my ( $self ) = @_;
   
    $self->logger()->trace( "Check ACK_BARRIER: controllo ACK pendenti" );
   
    my $work_area = $self->get_work_area( );
    my $conf = $self->get_stepper_config( );
    my $event_name = uc( $work_area->{EVENT}->get_event_name( ) );
    my $event_source_ref = $work_area->{EVENT}->get_source_ref( );
   
    my $ack_barrier = $conf->{ACK_BARRIER};
    my $dbMap = $ack_barrier->{DB};
    my $qMap = $ack_barrier->{QUEUE};
   
    $self->logger()->trace( "EVENTO entrante: '$event_name'" );
   
    my ( $dbh, $sqh, $ackPendingCount );
    
    for my $qId ( sort keys %{$qMap} ) {
   
        my $qDef = $qMap->{$qId};
        if( defined $qDef->{ON_EVENTS} && !exists $qDef->{ON_EVENTS}->{$event_name} ) {
            $self->logger()->trace("skip CODA '$qId' - evento non filtrato" );
            next;
        }
         
        $self->logger()->trace("verifica ACK pendenti su coda '$qId'"); 
         
        $dbh = $dbMap->{$qDef->{DB_ID}}->{__HANDLE__}; 
        if( ! defined $dbh ) {
            $self->logger()->trace( "connessione al DB '$qDef->{DB_ID}'..." );
            eval {       
                $dbh = $dbMap->{$qDef->{DB_ID}}->{__CONNECTOR__}->( );
            };
            if( $@ || !defined $dbh ) {
                my $exception = $@;
                $self->logger()->fatal( "connessione al DB '$qDef->{DB_ID}' non riuscita: $exception" );
                croak "connessione al DB '$qDef->{DB_ID}' non riuscita : $exception";
            }
            $self->logger()->trace( "connessione OK" );
            $dbMap->{$qDef->{DB_ID}}->{__HANDLE__} = $dbh;
        }
       
        $sqh = $qDef->{__HANDLE__};
        if( ! defined $sqh ) {  
            $self->logger()->trace( "connessione alla coda RA_SOURCE:'$qId' @ DB_ID '$qDef->{DB_ID}'..." );
            eval {
                $sqh = $qDef->{__CONNECTOR__}->( $dbh );
            };
            if( $@ || !defined $sqh ) {
                my $exception = $@;
                $self->logger()->fatal( "connessione alla coda '$qId' tramite DB tag '$qDef->{DB_ID}' non riuscita: $exception" );
                croak "Connessione alla coda '$qId' tramite DB tag '$qDef->{DB_ID}' non riuscita : $exception";
            }
           
            $self->logger()->trace( "connessione OK" );
            $qDef->{__HANDLE__} = $sqh;          
        }
      
        my $artActivityId = $self->get_work_area( )->{ART_ACTIVITY_ID};
      
        $self->logger()->trace( "ricerca ACK pendendi su coda id '$qId', SOURCE_REF:${artActivityId} ..." );
        $ackPendingCount = scalar($sqh->find_pending_target_ack(
                 EVENT       => $qDef->{EVENTS}
                ,SOURCE_REF  => $artActivityId
        ));
        if( !defined $ackPendingCount ) {
            my $exception = $sqh->last_error || "restituito undef";
            $self->logger()->fatal( "errore leggendo la coda RA '$qId': $exception" );
            croak "errore leggendo la coda RA '$qId': $exception";
        }
       
        $self->logger()->trace( "numero ACK pendenti $ackPendingCount" );
       
        if( $ackPendingCount > 0 ) {
            $self->logger()->info( "ACK_BARRIER: skip evento causa ACK pending su coda '$qId'" );
            __croak_skip_exception__ ( REASON => "skip evento causa ACK pending su coda '$qId'" );
        }
    }
    return;
}
      
sub do_get_ART_activity_id {
    my ($self) = @_;
    my $event = $self->get_work_area( )->{EVENT};
    my $eventData = $event->get_data( );
    my $keyId = $self->get_stepper_config( )->{RA_DATA_KEY_FOR_ACTIVITY_ID};
    my $conf = $self->get_stepper_config( );
   
    if( ref( $eventData ) eq 'HASH' ) {
        if( exists $conf->{ACTIVITY_BY_COLLECTION_FIND} || exists $eventData->{$keyId} ) {
            my $idVal;
            if ( exists $conf->{ACTIVITY_BY_COLLECTION_FIND} ) {
            	$self->logger()->trace( "Ricerca ART_ACTIVITY_ID attraverso API::ART::Collection::Activity" );
            	my %find_p;
				$find_p{ACTIVITY_TYPE_NAME} = $conf->{ACTIVITY_BY_COLLECTION_FIND}->{ACTIVITY_TYPE_NAME} if exists $conf->{ACTIVITY_BY_COLLECTION_FIND}->{ACTIVITY_TYPE_NAME};
				$find_p{ACTIVE}             = $conf->{ACTIVITY_BY_COLLECTION_FIND}->{ACTIVE}             if exists $conf->{ACTIVITY_BY_COLLECTION_FIND}->{ACTIVE};
				$find_p{STATUS}             = $conf->{ACTIVITY_BY_COLLECTION_FIND}->{STATUS}             if exists $conf->{ACTIVITY_BY_COLLECTION_FIND}->{STATUS};

	            my $propMap = $conf->{ACTIVITY_BY_COLLECTION_FIND}->{PROPERTIES};
	            if( ref($propMap) ne 'HASH' ) {
	                croak "Parametro ACTIVITY_BY_COLLECTION_FIND->PROPERTIES, non valido: atteso hash reference";
	            }
	    
	            for my $rid ( keys %$propMap ) {
	                my ($PROP_DATA_KEY, $RA_DATA_KEY);
	                
	                if( $rid =~ /^\s*PROPERTY\s*:\s*(.+)$/i ) {
	                    $PROP_DATA_KEY = $1;
	                    $PROP_DATA_KEY =~ s/\s+$//;
	                }
	                if( defined $PROP_DATA_KEY and ( $PROP_DATA_KEY ne '' ) 
	                    and defined $propMap->{$rid} 
	                    and (ref(\$propMap->{$rid}) eq 'SCALAR' ) 
	                  ) 
	                {
	                    if( $propMap->{$rid} =~ /^\s*RA_DATA\s*:\s*(.+)$/i ) {
	                        $RA_DATA_KEY = $1;
	                        $RA_DATA_KEY =~ s/\s+$//;
	                    }
	                }
	                if( !defined $PROP_DATA_KEY || !defined $RA_DATA_KEY ||
	                    ($PROP_DATA_KEY eq '') || ( $RA_DATA_KEY eq '') 
	                  )
	                {
	                    croak "Parametro ACTIVITY_BY_COLLECTION_FIND->PROPERTIES, RA DATA '$rid' non valida";
	                }
	                
	                unless (defined $eventData->{$RA_DATA_KEY}){
	                	$self->logger()->error( "ART_ACTIVITY_ID non trovato, eseguendo la find con i parametri:\n".Dumper(\%find_p) );
    					__croak_error_exception__ ( REASON => "ART_ACTIVITY_ID non trovato", TARGET_REF => 'KO' );
	                }
	                $find_p{PROPERTIES}->{$PROP_DATA_KEY} = $eventData->{$RA_DATA_KEY};
	            }
               
	            my $ca = $self->_get_collection_activity->find_id( %find_p );
	            
				if (!scalar( @$ca )){
					$self->logger()->error( "ART_ACTIVITY_ID non trovato, eseguendo la find con i parametri:\n".Dumper(\%find_p) );
    				__croak_error_exception__ ( REASON => "ART_ACTIVITY_ID non trovato", TARGET_REF => 'KO' );
				}
				elsif (scalar( @$ca > 1 )){
					$self->logger()->error( "ART_ACTIVITY_ID non identificato correttamente, eseguendo la find con i parametri:\n".Dumper(\%find_p) );
    				__croak_error_exception__ ( REASON => "ART_ACTIVITY_ID non identificato correttamente", TARGET_REF => 'KO' );
				}
				$idVal = $ca->[0];
            }
            else{
            	$self->logger()->trace( "Ricerca ART_ACTIVITY_ID in dati evento: chiave '$keyId'" );
            	$idVal = $eventData->{$keyId};
            }
            
            $self->logger()->trace(
                  sprintf("Trovato ART ACTIVITY ID '%s'", nvl_string($idVal) )
            );
            $self->get_work_area( )->{ART_ACTIVITY_ID} = $idVal;
            return;
        }
    }
    $self->logger()->error( "ART_ACTIVITY_ID non trovato" );
    __croak_error_exception__ ( REASON => "ART_ACTIVITY_ID non trovato", TARGET_REF => 'KO' );
}      
      
      
sub do_create_ART_activity {
    my ($self) = @_; 
    my $aid = $self->get_work_area( )->{ART_ACTIVITY_ID};
    
    $self->logger()->info( "Crezione instanza ART_ACTIVITY ID:'" . (defined $aid ? $aid : "UNDEF") ."'" );
    

    if( !defined $aid ) {
        $self->logger()->error( "errore ART ACTIVITY ID non definito (UNDEF)" );
        __croak_error_exception__ ( 
                    REASON     => "Creazione oggetto ART ACTIVITY fallita ID non valorizzato (UNDEF)",
                    TARGET_REF => "ART_ID_UNDEF" );
                                  
    }
 
    my $artObj = $self->art( );
    if( !defined $artObj ) {
        croak "BUG ? Oggetto ART non definito (metodo art() restituisce 'undef')" .
              " Verificare che il consumer di eventi sia configurato per connettersi al DB tramite un oggetto ART valido";
    }
           
    my $artActivityObj = eval {
              API::ART::Activity::Factory->new( ART => $artObj, ID  => $aid)
    };
    if( $@ || !defined $artActivityObj ) {
        my $exception = $@ 
                      || ( length($artObj->last_error) ? $artObj->last_error : 0 )
                      || "API::ART::Activity::Factory : metodo new() restituisce undef";

        $self->logger()->error( "errore creando oggetto ART ACTIVITY (ID:'$aid'): $exception" );
        __croak_error_exception__ ( REASON => "Creazione oggetto ART ACTIVITY (ID:'$aid') fallita", TARGET_REF => $aid );
    }
           
    $self->get_work_area( )->{ART_ACTIVITY_OBJ} = $artActivityObj;
    return;
}
       
sub do_check_ART_action_name {
    my ($self) = @_;
    
    $self->logger( )->trace( "Ricerca ART_ACTION" );
    
    my $artActionMap = $self->get_stepper_config()->{ACTION_MAP};
    my $eventName = $self->get_work_area( )->{EVENT}->get_event_name( );
    my $artActivityObj = $self->get_work_area( )->{ART_ACTIVITY_OBJ};
    my $artActivityTypeName;
        
    $artActivityTypeName = $artActivityObj->type_name( );
      
    if( ! defined $artActivityTypeName ) {
          my $exception = $artActivityObj->last_error || "risultato undef";
          $self->logger()->error( "errore leggendo nome tipo attivita' ART : " . $exception );
          __croak_error_exception__( 
                     REASON     => "errore leggendo nome del tipo attivita' ART", 
                     TARGET_REF => $self->get_work_area( )->{ART_ACTIVITY_ID} 
          );
    }
        
    $self->logger()->trace( "Ricerca ART_ACTION ( ART_ACTIVITY_TYPE_NAME:'$artActivityTypeName', RA_EVENT:'$eventName' )" ); 
   
    # cerca azione nelle mappe degli stati/flussi configurati e poi in quella di
    # default
    for( $artActivityTypeName, '*' ) {        
         if( exists $artActionMap->{$_} ) {
             if( exists $artActionMap->{$_}->{$eventName} ) {
                 my $artActionName = $artActionMap->{$_}->{$eventName};
                 $self->logger()->trace( "<----- trovato ART_ACTION_NAME : '$artActionName'" );
                 $self->get_work_area( )->{ART_ACTION_NAME} = $artActionName;
                 return;
             }   
         }
    }
    
    $self->logger()->error( "ART_ACTION_NAME non trovata" );   
    __croak_error_exception__ (
               REASON => "ART_ACTION_NAME non trovata", 
               TARGET_REF => $self->get_work_area( )->{ART_ACTIVITY_ID}
    );
}

sub do_check_skip_zone {
    my ($self) = @_;
   
    $self->logger()->trace( "Verifica EVENT_SKIP_ZONE" );
 
    my $skipMap = $self->get_stepper_config( )->{EVENT_SKIP_ZONE_MAP};
    my $eventName = $self->get_work_area( )->{EVENT}->get_event_name( );
    my $artActivityObj = $self->get_work_area( )->{ART_ACTIVITY_OBJ}; 
    
    my $activityTypeName = eval { $artActivityObj->type_name( ) };   
    if( $@ || !defined $activityTypeName ) {
        croak "BUG: oggetto ART_ACTIVITY e/o ART_ACTIVITY_TYPE_NAME mancanti";
    }

    $self->logger()->trace( "ART_ACTIVITY_TYPE_ID:'$activityTypeName', EVENT:'$eventName'" );   
       
    if( !exists $skipMap->{$activityTypeName} || !exists $skipMap->{$activityTypeName}->{$eventName} ) {
        $self->logger()->trace("<----- verifica EVENT_SKIP_ZONE: l'evento '$eventName', su flusso '$activityTypeName', puo' essere elaborato (non presente in mappa di skip)" );
        return 1;       
    }
    
    
    $self->logger()->trace( "verifica SKIP_ZONE: flusso '$activityTypeName'");
   
    for my $zid ( sort keys %{$skipMap->{$activityTypeName}->{$eventName}} ) {
        my $zStart = $skipMap->{$activityTypeName}->{$eventName}->{$zid}->{START_STATUS};
        my $zEnd = $skipMap->{$activityTypeName}->{$eventName}->{$zid}->{END_STATUS};
        
        $self->logger()->trace( "zona '$zid' : START(" . join(",", @$zStart) . ")  END(" . join(",",@$zEnd) . ")" );

        if( $artActivityObj->is_in_zone( $zStart, $zEnd ) ) {
            $self->logger()->warn( "<----- verifica SKIP_ZONE: SKIP evento '$eventName', causa ACTIVITY STATUS in SKIP ZONE '$zid'" ); 
            __croak_skip_exception__ (            
                     REASON => "skip evento '$eventName', causa ACTIVITY STATUS in SKIP ZONE '$zid'"
            );           
        }
    }
    return;
}

        
sub do_check_ART_action_permission {
    my ($self) = @_;
 
    $self->logger()->trace( "Verifica permessi di esecuzione ART ACTION" );
    
    my $artActivityObj = $self->get_work_area( )->{ART_ACTIVITY_OBJ};
    my $artActionName = $self->get_work_area( )->{ART_ACTION_NAME};
    
    my $res = $artActivityObj->can_do_action( NAME => $artActionName );
    
    if( !$res ) {
         my $errMsg = "ART ACTION ('$artActionName') non consentita";
         $errMsg .= ": " . $artActivityObj->last_error
              if $artActivityObj->last_error;
             
         $self->logger()->error( $errMsg );
         __croak_error_exception__ ( 
                    REASON => $errMsg,
                    TARGET_REF => $self->get_work_area( )->{ART_ACTIVITY_ID}
         );
    }
    return;
}
          
sub do_savepoint {
    my ($self) = @_;   
    
    if( defined $self->db() ) {   
        $self->logger()->trace( "Creazione DB SAVEPOINT 'EVENTSTEPPER_SAVEPOINT'" );    
        $self->db( )->do(q{ savepoint EVENTSTEPPER_SAVEPOINT });
        $self->get_work_area( )->{DB_SAVEPOINT_DONE} = 1;
    }
    else {
        $self->logger->warn( "DB SAVEPOINT non creato: DB REFRENCE mancante" );
    }
    return;
}


sub do_set_ART_step_properties {
    my ($self) = @_;
  
    $self->logger( )->trace( "Definizione ART step properties" );
  
    my $conf = $self->get_stepper_config( );
    my $artActivityObj = $self->get_work_area( )->{ART_ACTIVITY_OBJ};
    my $artActionName = $self->get_work_area( )->{ART_ACTION_NAME};
    my $eventName = $self->get_work_area( )->{EVENT}->get_event_name( );
    my $eventData = $self->get_work_area( )->{EVENT}->get_data( );
    
    my $activityType = $artActivityObj->type_name( );
    my $tMap = $conf->{EVENT_DATA_TO_STEP_PROPERTIES_MAP};
  
    # conversione nomi dati tecnici RA_DATA_KEY vs PROPERTIES_NAME  
    if( defined $eventData 
         && defined $activityType 
         && exists $tMap->{$activityType} 
         && exists $tMap->{$activityType}->{$eventName} ) 
         {
            my $newD = { };
            my $subs = 0;
            for( keys %$eventData ) {
                   if( exists $tMap->{$activityType}->{$eventName}->{$_} ) {
                       $self->logger( )->trace( "sostituisco nome dato tecnico '$_' con '$tMap->{$activityType}->{$eventName}->{$_}'" );
                       $newD->{ $tMap->{$activityType}->{$eventName}->{$_} } = $eventData->{$_};                       
                       $subs++;
                   }
                   else {
                       $newD->{$_} = $eventData->{$_};
                   }
            }
            $eventData = $newD if $subs;  
         }
    
    $self->get_work_area( )->{ART_STEP_PROPERTIES} = $eventData;
    return;
}


sub do_run_pre_ART_step_hook_chain {
    my ($self) = @_;
   
    $self->logger()->trace( "-----> avvio hook pre ART_STEP" );
    #TODO:
    $self->logger()->trace( "<----- fine hook pre ART_STEP" );
   
    return 1;
}

sub _check_DTA_length_limit {
    my ($self, $artActivityObj, $dtaProps) = @_;

    my $dta_limit_map = $self->get_stepper_config( )->{DTA_LENGTH_LIMIT_MAP};
    my $actTypeName   = $artActivityObj->type_name();
    my $actId         = $artActivityObj->id();

    if( exists $dta_limit_map->{$actTypeName} ) {
        
        for( keys %{$dtaProps} ) {
             next if !defined $dtaProps->{$_};
             next if !defined $dta_limit_map->{$actTypeName}->{$_};
             next if $dta_limit_map->{$actTypeName}->{$_} < 1;
  
             my $maxLen = $dta_limit_map->{$actTypeName}->{$_};

             if( $maxLen < length($dtaProps->{$_}) ) {
                 $self->logger->warn(
                            "($actTypeName: $actId) troncamento lunghezza dato tecnico '$_' " 
                          . "da " . length($dtaProps->{$_}) 
                          . " a $maxLen ch."
                 );
                 $dtaProps->{$_} = substr( $dtaProps->{$_}, 0, $maxLen ); 
             }
        }
    }
    return;
}
   
sub do_ART_step {
    my ($self) = @_;

    $self->logger()->info( "Esecuzione ART_STEP filtered" );   
    
    my $artActivityObj = $self->get_work_area( )->{ART_ACTIVITY_OBJ};
    my $artActionName = $self->get_work_area( )->{ART_ACTION_NAME};
    my $stepProps = $self->get_work_area( )->{ART_STEP_PROPERTIES};
    
    $self->logger()->info( "Nome ACTION '$artActionName'" );

    $self->_check_DTA_length_limit( $artActivityObj, $stepProps );

    my $engineStep = $self->_get_engine_stepper();
    my $res;

    if( defined $engineStep ) {
      $res = $engineStep->handle(
             ACTIVITY_ID      => $artActivityObj->id()
            ,ACTION_NAME      => [$artActionName]
            ,STEP_DESCRIPTION => "Azione automatica eseguita con script '${\&basename($0)}'"
            ,PROPERTIES       => $stepProps
      );
    }
    else {
      $res = $artActivityObj->step_filtered (
                    ACTION     => $artActionName,
                    PROPERTIES => $stepProps
      );
    } 
     
    if( !$res ) {
       my $except = $self->art()->last_error || "";
       $self->logger()->error( "errore eseguendo lo step: " . $except );
       __croak_error_exception__ (
                REASON => "esecuzione step fallita: " . $except, 
                TARGET_REF => $self->get_work_area( )->{ART_ACTIVITY_ID}
       );       
    }
    else {
      $self->logger()->info("Step Ok");
    }
    return;
}
      
sub do_run_post_ART_step_hook_chain {
    my ($self) = @_;
    
    $self->logger()->trace( "-----> avvio hook post ART_STEP" );
    #TODO:
    $self->logger()->trace( "<----- fine hook post ART_STEP" );
  
    return;
}


sub do_savepoint_rollback {
    my ($self) = @_;
    my $workArea = $self->get_work_area( );
    if( $workArea->{DB_SAVEPOINT_DONE} ) {   
            $self->logger()->trace( "Esecuzione ROLLBACK SAVEPOINT" );
            $self->db( )->do(q{ rollback to EVENTSTEPPER_SAVEPOINT });
            $workArea->{DB_SAVEPOINT_DONE} = undef;
    }  
    return;
}        


sub get_response_error {
    my ($self) = shift;
    my %param = @_;
    return $self->_get_response( %param,
                RESULT => STEPPER_RESULT_CONSUME,
                STATUS => STEPPER_EVENT_KO
    );
}

sub get_response_skip {
    my ($self) = shift;
    my %param = @_; 
    return $self->_get_response( %param, RESULT => STEPPER_RESULT_SKIP );
}


sub get_response_done {
    my ($self) = shift;
    my %param = @_;
    return $self->_get_response ( %param,
            RESULT => STEPPER_RESULT_CONSUME,
            STATUS => STEPPER_EVENT_OK
    );
}

        
sub _get_response {
    my ($self) = shift;
    my %param = @_;
    
    my $conf = $self->get_stepper_config( );
 
    $self->logger()->trace( "RESPONSE:" );
    $self->logger()->trace( "   RESULT: " .     nvl_string($param{RESULT}) );
    $self->logger()->trace( "   STATUS: " .     nvl_string($param{STATUS}) );
    $self->logger()->trace( "   REASON: " .     nvl_string($param{REASON}) );
    $self->logger()->trace( "   TARGET_REF: " . nvl_string($param{TARGET_REF}) );
    $self->logger()->trace( "   ACK_DATA: " .   nvl_string($param{DATA}) );
   
    if( $param{RESULT} eq STEPPER_RESULT_CONSUME ) {
        if( $param{STATUS} eq STEPPER_EVENT_KO ) {
       
            if( $conf->{SKIP_ON_ERROR} ) {
                $self->logger()->info( "Risultato: SKIP EVENTO (flag SKIP_ON_ERROR)" );
                return $self->skip( REASON => $param{REASON} );
            }    
            $self->logger()->info( "Risultato: EVENTO KO" );           
        }
        else {
            $self->logger()->info( "Risultato: EVENTO OK" );
        }
 
        my @args = ( );
        push @args, 'STATUS', ( $param{STATUS} eq STEPPER_EVENT_OK ? 0 : 1 );
        push @args, 'REASON', $param{REASON};
        push @args, 'TARGET_REF', $param{TARGET_REF};
        push (@args, 'ACK_DATA', $param{ACK_DATA}) if defined $param{DATA};
        return $self->consume( @args );
    }
    elsif( $param{RESULT} eq STEPPER_RESULT_SKIP ) {
        
        $self->logger()->info( "Risultato: SKIP EVENTO" );
        return $self->skip( REASON => $param{REASON} );
    }  
    croak "BUG: _get_response: ciclo di lavorazione terminato senza restiture un formato risposta valido";
    return;   
}
 
#
# utilities (static methods)
#   

sub __croak_error_exception__ {
    my %param = @_;
    croak ( SIRTI::Queue::EventConsumerStepper::ErrorException->new( @_ ) );
    die "Method " . __PACKAGE__ . "::__croak_error_exception__ FAILED";
    exit 99;
}

sub __croak_skip_exception__ {
    my %param = @_;
    croak ( SIRTI::Queue::EventConsumerStepper::SkipException->new( @_ ) );
    die "Method " . __PACKAGE__ .  "::__croak_skip_exception__ FAILED";
    exit 99;
}

sub __croak_operation_done_exception__ {
    my %param = @_;
    croak ( SIRTI::Queue::EventConsumerStepper::OperationDoneException->new( @_ ) );
    die "Method " . __PACKAGE__ . "::__croak_operation_done_exception__ FAILED";
    exit 99;
}



sub nvl_string { defined $_[0] ? $_[0] : "undef" }

#
# espande il valore di  eventuali variabili esterne (${})
#
# (IN/OUT) stringa(SCALAR REF),  (IN) getter( CODEREF )  [ getter = sub( nome_parametro ) { return value } ]
# (OUT) numero di sostituzioni
#
sub expand_string
{
    my $str = shift;
    my $getter = shift;
    my $result = undef;
    my $start = 0;
    my $reps = 0;
    
    for( ;; $start = pos( $$str ) )
    {
   	    if( $$str =~ /(\$+)\{([^\}]*)\}/gc ) {
                my $pre = $1;     
                my $key = $2;
                my $end = pos( $$str );

                $result .= substr( $$str, $start, $end - 2 - length($pre) - length($key) - $start );
               
                if( length($pre) & 1 ) {
                    chop $pre;
                    $pre =~ s/\$\$/\$/g;
                    $result .= $pre;
                    
                    $key =~ s/^\s+//;
                    $key =~ s/\s+$//;
                    my $dieflag = !( $key =~ s/^\?// ); 
                    my $pre = &$getter( $key );
                    
                    croak( "la variabile '$key' non è presente nell'environment" )
                      if !defined( $pre ) && $dieflag;
                    $result .= (defined $pre ? $pre : '');
                    $reps++;           
                }
                else {
                    $pre =~ s/\$\$/\$/g;
                    $result .= $pre;
                    $result .= substr( $$str, $end - 2 - length($key), length($key) + 2 );
                    $reps++;
                }
                next;
        }
	    
		$result .= substr( $$str, $start );
		last;
	}	
    $$str = $result if $reps;
	return $reps;
}

 
 
sub exp_envstr
{
   my ($refPar) = @_;
   expand_string( $refPar, sub { my $v = shift; exists $ENV{$v} ? $ENV{$v} : undef } );
   return $refPar;
}


=pod

=head1 SEE ALSO

Script perl B<ramq>, package B<SIRTI::Queue::Event>, B<SIRTI::Queue::EventAckConsumer>, B<SIRTI::Queue::EventAck>


=head1 BUGS

Eventuali bug riscontrati dovranno essere segnalati su B<ART - Global Services>: L<https://www.artnet.sirtisistemi.net/ARTIT/art> aprendo un apposito DEV-TICKET.

=head1 HISTORY

=over

=item Ver. 0.1

Prima release del modulo

=back


=head1 AUTHOR

Gruppo OLO :)

=cut


if( __FILE__ eq $0 ) {

{
 package mylogger;
 sub info { shift; print "INFO: ", @_, "\n" }
 sub warn { shift; print "WARN: ", @_, "\n" }
 sub error { shift; print "ERROR: ", @_, "\n" }
 sub fatal { shift; print "FATAL: ", @_, "\n" } 
}

{
 package myArtObj;
 sub info { return "Ok" }
}

 package main;

 my $cstring = 'remote_activity/remote_activity@dleadst2';
 
 my $logger = bless( {}, 'mylogger' );

 
 my $obj = bless ( { }, 'SIRTI::Queue::EventConsumer' );
 
 $obj = bless ( $obj, 'SIRTI::Queue::EventConsumerStepper' );
 $obj->{__LOGGER__} = $logger;
 $obj->{_WORK_CONTEXT}->{STEPPER_CONFIG_FILE} = shift || "./stepper.conf";
 
 $obj->{_DB} = SIRTI::DB::Oracle->new( $cstring );
 $obj->{_ART} = API::ART->new( 
                       ARTID    => $ENV{ARTID},
                       USER     => $ENV{ART_USER},
                       PASSWORD => $ENV{ART_USER_PASSWORD}
 );
  
 $obj->read_config_file( );
 $obj->validate_config( );
 
 print $obj->get_stepper_config,"\n";
 
 print ":)\n";

 sleep 1;
 
 print "", Dumper( $obj->get_stepper_config( ) ), "\n";
 
 my $conf = $obj->get_stepper_config( );
 
 if( exists $conf->{ACK_BARRIER}->{DB}->{CSTR1} ) {
 
      print "connessione al DB CSTR1...\n";

      my $dbh1 = $conf->{ACK_BARRIER}->{DB}->{CSTR1}->{__CONNECTOR__}->( );
 
 }
 
 if( exists $conf->{ACK_BARRIER}->{DB}->{CSTR2} ) {
 
      print "connessione al DB CSTR2...\n";
 
      my $dbh2 = $conf->{ACK_BARRIER}->{DB}->{CSTR2}->{__CONNECTOR__}->( );
 
 }
 
 print "start consume event...\n";
 
 {
    package FakeEvent;

    sub get_event_name { shift; "EVENTO_FAKE"; }
    sub get_info       { shift; { COLORE => "GIALLO" } }
    sub get_data       { shift; { ACT_ID_KEY => "890" } }
    sub get_source_ref { shift; "SOURCE_REF_$$" }
    sub new            { bless( {}, "FakeEvent" ) }
    1;
 }
 
 $obj->consume_event( EVENT => new FakeEvent( ) );
 
 print "end consume event...\n";
 
 print "\n";
 print "*_*\n";
 
 exit 0;

}

1;
