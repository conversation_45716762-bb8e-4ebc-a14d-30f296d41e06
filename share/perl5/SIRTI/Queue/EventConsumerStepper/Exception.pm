package SIRTI::Queue::EventConsumerStepper::Exception;

use Carp qw/croak/;

sub new {
    my $this = shift;
    my $class = ref($this) || $this;
    my %param = @_;

    if ( !defined $param{REASON} || ( ref(\$param{REASON}) ne 'SCALAR' ) ) {
         croak "Parametro REASON mancante o non valido. Usage:  " . __PACKAGE__ . " new( REASON => [scalar] )";
    }

    $self->{__EXCEPTION_REASON__} = $param{REASON};
    return bless( $self, $class );
}

sub get_reason {
   shift->{__EXCEPTION_REASON__};
}

1;

