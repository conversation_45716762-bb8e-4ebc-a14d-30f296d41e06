package SIRTI::Queue::EventConsumerStepper::Hook::Context;

use base 'SIRTI::Base';

 
sub new {
    my $this = shift;
    my $class = ref($this) || $this;
     
    my $self = $class->SUPER::new( );
     
    my $data;
    if( @_ == 1 && ref($_[0]) eq 'HASH' ) { $data = $_[0] }
	elsif( @_ ) { $data = { @_ } }
	else { $data = { } }
    $self->{__DATA__} = $data;
    
    return bless( $self , $class );
}
 
sub get_typed_property {
    my ($self,$key) = (shift, shift);
    my $dta = $self->_get_data( );
    if( defined $key && exists $dta->{$key} && scalar(@_) ) {
      for( @_ ) {
		 next if( $_ eq 'SCALAR' && ref(\$dta->{$key}) eq $_ );
		 next if( $_ eq 'REF'    && ref($dta->{$key}) );
         return undef if( ! UNIVERSAL::isa( $dta->{$key}, $_ ) );
      }
      return $dta->{$key};
    }
    return undef;
}
 
sub exists_typed_property {
	my ($self,$key) = (shift, shift);
    my $dta = $self->_get_data( );
    if( defined $key && exists $dta->{$key} && scalar(@_) ) {
      for( @_ ) {
		 return 0 if( $_ eq 'SCALAR' && ref(\$dta->{$key}) ne $_ )
		            or
		            ( $_ eq 'REF'    && ref($dta->{$key}) ne 'REF' )
		            or
                    ( ! UNIVERSAL::isa( $dta->{$key}, $_ ) );
      }
      return 1;
    }
    return 0;
}

sub get_property {
	my ($self, $key ) = @_;
    my $dta = $self->_get_data( );
	return
	 ( defined $key && exists $dta->{$key} ) ? $dta->{$key} : undef;
}

sub set_property {
	my ($self, $key, $val ) = @_;
    my $dta = $self->_get_data( );
	$dta->{$key} = $val if defined $key;
	return $self;
}

sub exists_property {
	my ($self, $key) = @_;
    my $dta = $self->_get_data( );
	return defined $key && exists $dta->{$key};
}

sub delete_property {
	my ($self, $key) = @_;
    my $dta = $self->_get_data( );
	delete $dta->{$key} if defined $key;
	return $self;
}

sub get_property_names {
	my $self = shift;
	return ( keys %{$self->_get_data( )} );
}

sub clear {
	my $self = shift;
    $self->{__DATA__} = { };
	return $self;
}

sub _get_data { 
    shift->{__DATA__} 
}

1;
