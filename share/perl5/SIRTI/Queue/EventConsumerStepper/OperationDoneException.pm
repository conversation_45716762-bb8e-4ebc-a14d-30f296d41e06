package SIRTI::Queue::EventConsumerStepper::OperationDoneException;

use Carp qw(croak);

use base 'SIRTI::Queue::EventConsumerStepper::Exception';


my $usageString = "new( REASON => [scalar], TARGET_REF => [scalar] [ , DATA => [hashref] ] )";

sub new {
    my $this = shift;
    my $class = ref($this) || $this;
    my %param = @_;

    my $self = $class->SUPER::new( %param );  

    if( !defined $param{TARGET_REF} ) {
        croak "Parametro TARGET_REF mancante " . $usageString;
    }
    if( exists $param{DATA} && ( ref($param{DATA}) ne 'HASH' ) ) {
        croak "Parametro DATA mancante o non valido " . $usageString;
    }
   
    $self->{__EXCEPTION_TARGET_REF__} = $param{TARGET_REF};
    $self->{__EXCEPTION_DATA__} = $param{DATA} if exists $param{DATA};
    return bless( $self, $class ); 
}

sub get_target_ref {
    shift->{__EXCEPTION_TARGET_REF__};
}

sub get_data {
    shift->{__EXCEPTION_DATA__};
}

1;

