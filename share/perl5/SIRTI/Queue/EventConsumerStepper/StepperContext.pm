package SIRTI::Queue::EventConsumerStepper::StepperContext;

use Scalar::Util;
use Carp  qw(croak);

use SIRTI::Queue::EventConsumerStepper::ErrorException;
use SIRTI::Queue::EventConsumerStepper::SkipException;
use SIRTI::Queue::EventConsumerStepper::HookBreakException;
use SIRTI::Queue::EventConsumerStepper::OperationDoneException;
  
use base 'SIRTI::Queue::EventConsumerStepper::Hook::Context';


sub new {
    my $this = shift;
    my $class = ref($this) || $this;
    my %params = @_;
    
    my $self = $class->SUPER::new( );
      
    if( ( !exists $params{LOGGER} or !Scalar::Util::blessed($params{LOGGER}) )
        or 
        ( !exists $params{STEPPER_CONFIG} or ( defined $params{STEPPER_CONFIG} && (ref($params{STEPPER_CONFIG}) ne 'HASH') ) )
        or
        ( !exists $params{EVENT} or ( defined $params{EVENT} &&  ! UNIVERSAL::isa( $params{EVENT}, 'SIRTI::Queue::Event' ) ) )
        or
        ( !exists $params{ART_OBJ} or ( defined $params{ART_OBJ} &&  ! UNIVERSAL::isa( $params{ART_OBJ}, 'API::ART' ) ) )
        or
        ( !exists $params{ART_ACTIVITY_OBJ} or ( defined $params{ART_ACTIVITY_OBJ} &&  ! UNIVERSAL::isa( $params{ART_ACTIVITY_OBJ}, 'API::ART::Activity' ) ) )
        or
        ( !exists $params{ART_STEP_PROPERTIES} or ( defined $params{ART_STEP_PROPERTIES} && (ref($params{ART_STEP_PROPERTIES}) ne 'HASH' ) ) )
      )     
    {
      croak "Parametri di costruzione oggetto "  . __PACKAGE__ . " non validi";
    }
  
    $self->{__LOGGER__}              = $params{LOGGER};
    $self->{__STEPPER_CONFIG__}      = $params{STEPPER_CONFIG};
    $self->{__EVENT__}               = $params{EVENT};
    $self->{__ART_OBJ__}             = $params{ART_OBJ};
    $self->{__ART_ACTIVITY_OBJ__}    = $params{ART_ACTIVITY_OBJ};
    $self->{__ART_STEP_PROPERTIES__} = $params{ART_STEP_PROPERTIES};
    
    return bless( $self, $class );
}

sub get_logger {
    shift->{__LOGGER__};
}

sub get_event {
    shift->{__EVENT__};
}

sub get_art_obj {
    shift->{__ART_OBJ__};
}

sub get_art_activity_obj {
    shift->{__ART_ACTIVITY_OBJ__};
}

sub get_stepper_config {
    shift->{__STEPPER_CONFIG__};
}

sub get_step_properties {
    shift->{__STEP_PROPERTIES__};
}

sub set_step_properties {
    my $self = shift;
    $self->{__STEP_PROPERTIES__} = @_ ? shift : { };
}


sub croak_error_exception {
    my ($self,$reason) = @_;
    croak ( SIRTI::Queue::EventConsumerStepper::ErrorException->new( REASON => $reason, TARGET => 'ko' ) );
    die "Method " . __PACKAGE__ . "::croak_error_exception FAILED";
    exit 99;
}


sub croak_skip_exception {
    my ($self,$reason) = @_;
    croak ( SIRTI::Queue::EventConsumerStepper::SkipException->new( REASON => $reason ) );
    die "Method " . __PACKAGE__ . "::croak_skip_exception FAILED";
    exit 99;
}


sub croak_break_exception {
    my ($self,$reason) = @_;
    croak ( SIRTI::Queue::EventConsumerStepper::HookBreakException->new( REASON => $reason ) );
    die "Method " . __PACKAGE__ . "::croak_break_exception FAILED";
    exit 99;
}

sub croak_operation_done_exception {
    my ($self,$reason) = @_;
    croak ( SIRTI::Queue::EventConsumerStepper::OperationDoneException->new( REASON => $reason, TARGET => 'ok' ) );
    die "Method " . __PACKAGE__ . "::croak_operation_done_exception FAILED";
    exit 99
}

1;

