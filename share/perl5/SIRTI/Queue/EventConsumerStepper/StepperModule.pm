package SIRTI::Queue::EventConsumerStepper::StepperModule;

use Scalar::Util;
use Carp qw(croak);

use base 'SIRTI::Queue::EventConsumerStepper::Hook::Module';


sub new {
    my $this = shift;
    my $class = ref($this) || $this;
    my %params = @_;
    my $self = $class->SUPER::new( %params );
    
    if( ( exists $params{CONFIG} and (ref($params{CONFIG}) ne 'HASH') ) 
        or
        ( !exists $params{LOGGER} or !Scalar::Util::blessed($params{LOGGER}) ) 
      )
    {
      croak "Parametri di construzione oggetto " . __PACKAGE__ . " non validi";
    }
    
    return bless( $self, $class );
}

sub validate_and_setup {
    my ($self ) = shift;
    my %param = @_;    
    if ( exists $param{CONFIG} and ( ref($param{CONFIG}) ne 'HASH' ) ) {
         croak "Parametro CONFIG in formato non valido. Atteso hash reference";
    }
    if ( !exists $param{LOGGER} or !Scalar::Util::blessed($param{LOGGER}) ) {
         croak "Parametro LOGGER mancante o non valido";
    }
    return;
}

sub do {
    my ($self) = shift;
    my %param = @_;
    
    if( ! UNIVERSAL::isa( $param{STEPPER_CONTEXT}, 
          'SIRTI::Queue::EventConsumerStepper::Hook::StepperContext' ) 
      )
    {
        croak "Parametro STEPPER_CONTEXT mancante o non valido";
    }
    
    return;
}

1;

