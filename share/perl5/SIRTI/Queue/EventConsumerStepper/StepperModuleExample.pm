package SIRTI::Queue::EventConsumerStepper::StepperModuleExample;

use Carp qw(croak);

use base 'SIRTI::Queue::EventConsumerStepper::StepperModule';


sub new {
    my $this = shift;
    my $class = ref($this) || $this;
    my $self = $class->SUPER::new( @_ );
    return bless( $self, $class );
}

# Override 
sub validate_and_setup {
    my ($self ) = shift;
    return;
}

# Override
sub do {
    my ($self) = shift;
    my %param = @_;
    if( 
        exists $param{STEPPER_CONTEXT} 
          and 
        ! UNIVERSAL::isa($param{STEPPER_CONTEXT}, 'SIRTI::Queue::EventConsumerStepper::StepperContext' ) 
      )
    {
        croak "Parametro STEPPER_CONTEXT non valido";
    }
    my $ctx = $param{STEPPER_CONTEXT};

    $ctx->get_logger( )->info( __PACKAGE__ . "::do( " . join(",", keys %param ), " )" );
    
    return;
}

1;
