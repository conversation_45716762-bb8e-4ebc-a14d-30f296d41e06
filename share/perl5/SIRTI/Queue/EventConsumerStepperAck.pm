################################################################################
#
# SIRTI::Queue::EventConsumerStepperAck
#
################################################################################
=pod

=head1 NAME

B<SIRTI::Queue::EventConsumerStepperAck> - Interfaccia da implementare per la gestione di eventi ACK con C<ramq>

=head1 SYNOPSIS
 
=head1 DESCRIPTION
 
=cut

package SIRTI::Queue::EventConsumerStepperAck;

use strict;
use warnings;
use Carp;
use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

# uso il backend JSON::PP per supportare i metodi
# allow_barekey e allow_singlequote
BEGIN { $ENV{PERL_JSON_BACKEND} = 'JSON::PP' }
use JSON -support_by_pp;

use SIRTI::DB::Oracle;
use SIRTI::Queue::Response;
use API::ART;
use API::ART::Activity::Factory;
use SIRTI::ART::RemoteActivity::Source;
use File::Basename;
 
use constant STEPPER_RESULT_SKIP    => '0';
use constant STEPPER_RESULT_CONSUME => '1';
 
use constant STEPPER_EVENT_OK       => '0';
use constant STEPPER_EVENT_KO       => '1';


our $VERSION = '0.1';

use base 'SIRTI::Queue::EventAckConsumer';


=pod

=head1 METHODS

=cut

sub new {
    my $this = shift;
    my $class = ref($this) || $this;
   
    my $self = $class->SUPER::new( @_ );
    bless ( $self, $class );

    $self->logger()->info( "Inizializzazione STEPPER ACK" );
    $self->read_config_file( );
    $self->validate_config( );
    return $self;
}

sub init {
    my $self = shift;

    my $stepEngineConf = $self->get_stepper_config()->{STEP_ENGINE};

    if( (ref($stepEngineConf) eq 'HASH') && defined($stepEngineConf->{CLASS})  ) {

        if( ref( \$stepEngineConf->{CLASS} ) ne 'SCALAR' ) {
            croak "ERRORE: configurazione non valida: parametro CONF->STEP_ENGINE->CLASS, atteso scalare";
        }

        my $package = $stepEngineConf->{CLASS};

        $self->logger()->info("Caricamento ENGINE stepper package '" . $package . "'");
      
        eval "use $package;"; 
        if( $@ ) {
            croak "ERRORE: '" . $package . "': $@";
        }

        my $engineObj = eval { $package->new( ART => $self->art( ), BINDING => 1 ) };
        if( $@ ) {
            croak "ERRORE: creando istanza ENGINE stepper class '" . $package . "': $@";
        }

        if( ! UNIVERSAL::can( $engineObj, "handle" ) ) { 
            croak "ERRORE: la classe ENGINE non contiene il metodo 'handle'";   
        } 

        $self->{__ENGINE_STEPPER_CLASS__} = $engineObj;
    }
    else {
        $self->{__ENGINE_STEPPER_CLASS__} = undef;
    }

    return
}

sub _get_engine_stepper {
        shift->{__ENGINE_STEPPER_CLASS__};
}

# Override SIRTI::Queue::EventAckConsumer
sub get_managed_event_types {  shift->get_stepper_config( )->{MANAGED_EVENT_TYPES} }


# Override SIRTI::Queue::EventAckConsumer
sub get_managed_source_refs {  return->get_stepper_config( )->{MANAGED_SOURCE_REF_TYPES}  }


=pod

=head2 read_config_file( )

Questo metodo viene invocato dal metodo new C<EventConsumerStepper> per leggere la configurazione
del procedura di step automatica da file esterno.
Il nome del file di configurazione si trova nel parametro STEPPER_ACK_CONFIG_FILE
inserito nel WORK_CONTEXT del file di configurazione principale del consumer

Il formato del file di configurazione dello stepper e' JSON

croak in caso di errore

=cut

sub read_config_file {
   my ( $self ) = @_;
  
   $self->logger()->info( "Lettura configurazione stepper" );
  
   my $filename = $self->work_context("STEPPER_ACK_CONFIG_FILE");
  
   croak "Parametro STEPPER_ACK_CONFIG_FILE mancante o non valido"
       if (!defined $filename or ref(\$filename) ne 'SCALAR' );

   $filename =~ s/^\s+//;
   
   eval {
      my $json = new JSON;  
      $json->allow_barekey( 1 );
      $json->allow_singlequote( 1 );
   
      #lettura file di conf
      local $/;
      croak "impossibile aprire il file : $!"
          if !open( my $fin, "<$filename" );
          
      my $json_text = <$fin>;
      close( $fin );
      
      #parse json
      my $conf = eval { $json->decode( $json_text ) };
      croak $@ if( $@ );
     
      $json_text = undef;
      $self->{STEPPER_ACK_CONFIG} = $conf;     
   };
   if( $@ ) {
      croak "Errore leggendo file di configurazione '$filename' : $@"
   }  
   return;
}


=pod

=head2 validate_config( )

Questo metodo viene invocato dal metodo new C<EventConsumerStepperAck> per validare la configurazione
letto in precedenza da read_config_file e memorizzato in $self->{STEPPER_CONF}
del procedura di step automatica letta da file esterno.

croak in caso di errore
 
=cut

sub validate_config {
    my ($self) = @_;
    my $conf = $self->get_stepper_config( );
   
    croak "BUG: configurazione mancante . Parametro \$self->{STEPPER_ACK_CONFIG} deve contenere un hash reference valida"
        if( ref($conf) ne 'HASH' );
   
    $self->logger()->info( "Validazione configurazione" );

    $self->validate_and_setup_SESSION_TYPE( );
    $self->validate_and_setup_MANAGED_EVENT_TYPES( );    
    $self->validate_and_setup_TARGET_REF_TO_TDTA( );
    $self->validate_and_setup_SKIP_ON_ERROR( );
    #should be a module 
    #$self->validate_and_setup_ACTION_SKIP_ZONE_MAP( );
    $self->validate_and_setup_RA_DATA_KEY_FOR_ACTIVITY_ID( );
    #should be a module
    $self->validate_and_setup_ACTION_MAP( );
    $self->validate_and_setup_DTA_LENGTH_LIMIT_MAP( );
    $self->validate_and_setup_PRE_STEP_HOOK_MODULES( );
    $self->validate_and_setup_POST_STEP_HOOK_MODULES( );
    
    $conf->{MANAGED_SOURCE_REF_TYPES} = [ ];    
    return;
}

sub validate_and_setup_SESSION_TYPE {
    my $self = shift;
    my $conf = $self->get_stepper_config( );
    
    if( 
        !exists $conf->{SESSION_TYPE} 
         or 
        ( ref(\$conf->{SESSION_TYPE}) ne 'SCALAR' )
         or
        ( uc($conf->{SESSION_TYPE}) ne 'ACK' )
      )
    {
        croak "Parametro SESSION_TYPE mancante o non valido";
    }
    $conf->{SESSION_TYPE} = uc( $conf->{SESSION_TYPE} );
    return;
}


sub validate_and_setup_MANAGED_EVENT_TYPES {
    my ($self) = shift;
    my $conf = $self->get_stepper_config( );
    
    if( exists $conf->{MANAGED_EVENT_TYPES} || ref($conf->{MANAGED_EVENT_TYPES}) eq 'ARRAY' ) {
        my $elist = $conf->{MANAGED_EVENT_TYPES};
        my $idx = 0;
        for( ; $idx < scalar( @$elist ); $idx++ ) {
             last if !defined $elist->[$idx] 
                     or
                     ( ref(\$elist->[$idx]) ne 'SCALAR' );
             exp_envstr( \$elist->[$idx] );
        }
        return if $idx == scalar( @$elist ); 
    }
    
    croak "Parametro MANAGED_EVENT_TYPES mancante o in formato non corretto";
}

sub validate_and_setup_TARGET_REF_TO_TDTA {
    my $self = shift;
    my $conf = $self->get_stepper_config( );
    
    if( ! exists $conf->{TARGET_REF_TO_TDTA} ) {
        $conf->{TARGET_REF_TO_TDTA} = undef;
        return 1;
    }
    
    if( ( ref( \$conf->{TARGET_REF_TO_TDTA} ) ne 'SCALAR' ) 
        or 
        ( ${ exp_envstr( \$conf->{TARGET_REF_TO_TDTA} ) } eq '' )
      )
    {
        croak "Parametro TARGET_REF_TO_TDTA non valido";
    }    
    
    return 1;
}

sub validate_and_setup_SKIP_ON_ERROR {
    my ($self) = @_;
    my $conf = $self->get_stepper_config( );
   
    if( !defined $conf->{SKIP_ON_ERROR} ) {
        $conf->{SKIP_ON_ERROR} = 0;
    }
    else {
        exp_envstr( \$conf->{SKIP_ON_ERROR} );
        if( $conf->{SKIP_ON_ERROR} =~ /^\s*(TRUE|YES|1|ENABLE)\s*$/i ) {
            $conf->{SKIP_ON_ERROR} = 1;
        }
        elsif( $conf->{SKIP_ON_ERROR} =~ /^\s*(FALSE|NO|NONE|DISABLED?|0)\s*$/i ) {
               $conf->{SKIP_ON_ERROR} = 0;
        }
        else {
            croak "Valore parametro SKIP_ON_ERROR non valido";
        }
    }
    return;
}
     
sub validate_and_setup_RA_DATA_KEY_FOR_ACTIVITY_ID {
    my ( $self ) = @_;
    my $conf = $self->get_stepper_config( );
   
    my $val = $conf->{RA_DATA_KEY_FOR_ACTIVITY_ID};
    if( !defined $val || ( ref(\$val) ne 'SCALAR' ) ) {
        croak "Parametro RA_DATA_KEY_FOR_ACTIVITY_ID mancante o in formato non valido";
    }
    exp_envstr( \$conf->{RA_DATA_KEY_FOR_ACTIVITY_ID} );
    return;
}


sub validate_and_setup_DTA_LENGTH_LIMIT_MAP {
    my ($self) = @_;
    my $conf = $self->get_stepper_config( );
   
    if( exists( $conf->{DTA_LENGTH_LIMIT_MAP} ) ) {

        croak "Formato parametro DTA_LENGTH_LIMIT_MAP non valido." 
                . "Atteso HASH_REF"
            if ref($conf->{DTA_LENGTH_LIMIT_MAP}) ne 'HASH';

        for( keys %{$conf->{DTA_LENGTH_LIMIT_MAP}} ) {
             croak "Formato parametro DTA_LENGTH_LIMIT_MAP non valido."
                     . "Chiave '$_': atteso come valore un HASHREF"
                 if ref($conf->{DTA_LENGTH_LIMIT_MAP}->{$_}) ne 'HASH';
        }
    }
    else {
        $conf->{DTA_LENGTH_LIMIT_MAP} = { };
    }
    return;
}



sub validate_and_setup_ACTION_MAP {
    my ( $self ) = @_;
    my $conf = $self->get_stepper_config( );

    my $map = $conf->{ACTION_MAP} if exists $conf->{ACTION_MAP}; 
    if( ref($map) ne 'HASH' ) {
        croak "Parametro ACTION_MAP mancante o non valido";
    }
   
    my $act_map = { };
  
    for my $tid ( keys %$map ) {
        my $ACT_TYPE_ID = undef;
        if( $tid =~ /^\s*ACTIVITY_TYPE_NAME\s*:\s*([^\s]+)$/i ) {
            $ACT_TYPE_ID = $1;
            $ACT_TYPE_ID =~ s/\s+$//;
        }       
        if( !defined $ACT_TYPE_ID or ( $ACT_TYPE_ID eq '' ) ) {
            croak "Parametro ACTION_MAP, il tipo attivita' '$tid', format nome del 'tipo attivita' non valido";
        }
        
        my $tidMap = $map->{$tid};
        if( ref($tidMap) ne 'HASH' ) {
            croak "Parametro ACTION_MAP, tipo attivita' '$tid', mappa evento/azione mancante o in formato non corretto";
        }

        for my $eid ( keys %$tidMap ) {
            my $EVENT_ID = undef;    
            if( $eid =~ /^\s*EVENT\s*:\s*([^\s]+)$/i  ) {
                $EVENT_ID = $1;
                $EVENT_ID =~ s/\s+$//;
            }
            if( !defined $EVENT_ID or ( $EVENT_ID eq '' ) ) {
                croak "Parametro ACTION_MAP, tipo attivita' '$tid', formato nome evento '$eid' non corretto";
            }
          
            my $aidMap = $tidMap->{$eid};
            if( ref($aidMap) ne 'HASH' ) {
                croak "Parametro ACTION_MAP, tipo attivita' '$tid', configurazione per evento '$eid' non valida";
            }

            my $aMap = { };
            for my $sid ( map { uc($_) } keys %$aidMap ) {
                if( $sid eq 'STATUS_OK' || $sid eq 'STATUS_KO' ) {
                    my $ACTION_ID = undef;
                    if( defined $aidMap->{$sid} && $aidMap->{$sid} =~ /\s*ACTION\s*:\s*([^\s]+)$/i ) {
                        $ACTION_ID = $1;
                        $ACTION_ID =~ s/\s+$//;
                    }
                    if( !defined $ACTION_ID or ( $ACTION_ID eq '' ) ) {
                        croak "Parametro ACTION_MAP, tipo attivita' '$tid', configurazione per evento '$eid', statuo '$sid' non valido";
                    }
                    $aMap->{$sid} = $ACTION_ID;
                }
            }
            
            if( scalar( keys %$aMap ) < 1 ) {
                croak "Parametro ACTION_MAP, tipo attivita' '$tid', nessuna azione associata all' evento '$eid'";
            }
            
            $act_map->{$ACT_TYPE_ID}->{$EVENT_ID} = $aMap;
        }
        
        croak "Parametro ACTION_MAP non valido: mappa eventi/azioni per flusso '$ACT_TYPE_ID' vuota"
            if !scalar( keys %{$act_map->{$ACT_TYPE_ID}} );
    }
    
    croak "Parametro ACTION_MAP non valido: mappa eventi/azioni vuota"
       if !scalar( keys %$act_map );
    
    $conf->{ACTION_MAP} = $act_map;
    return;
}


sub validate_and_setup_PRE_STEP_HOOK_MODULES
{
    my ( $self ) = @_;
    my $conf = $self->get_stepper_config( );
    my $hlist = [ ];
   
    #TODO: intanziare oggetti PRE_STEP_HOOK_MODULES
    #
    #   in [ nome, nome2, nome3 ]
    #  out [ [ nome, objRef ], [ nome2, objRef2 ] ]
   
    $conf->{PRE_STEP_HOOK_MODULES} = $hlist;
    return;
}

sub validate_and_setup_POST_STEP_HOOK_MODULES
{
    my ( $self ) = @_;
    my $conf = $self->get_stepper_config( );
    my $hlist = [ ];
   
    #TODO: intanziare oggetti POST_STEP_HOOK_MODULES
    #
    #   in [ nome, nome2, nome3 ]
    #  out [ [ nome, objRef ], [ nome2, objRef2 ] ]
    
    $conf->{POST_STEP_HOOK_MODULES} = $hlist;
    return;
}


#
# Stepper consume() work area methods...
#

sub setup_work_area {
    my ($self, $event) = @_;
    
    my $woa = { };
    $self->logger()->info( "-----> inizializzazione work data" );
 
    $woa->{EVENT_ACK} = $event;
    $woa->{DB_SAVEPOINT_DONE} = 0;
    $woa->{DB_ROLLBACK_NEEDED} = 1;
    $woa->{RESPONSE} = {
            RESULT      => undef,
            REASON      => undef,
            STATUS      => undef,
            ACK_NOTE    => undef
    };
    return $self->{__STEPPER_WORK_DATA__} = $woa;
}

sub get_work_area { shift->{__STEPPER_WORK_DATA__} }
 
sub clear_work_area { shift->{__STEPPER_WORK_DATA__} = { } }

 
#
# override
#
sub consume_event_ack {
    my $self = shift;
    my %params = @_;
    my $event = $params{EVENT_ACK};
    my $response;
 
    $self->logger()->info( "Elaborazione EVENTO ACK" );   
    $self->logger()->info( "EVENT_NAME: '" . $event->get_event->get_event_name() . "'" );
     
    $self->setup_work_area( $event );
   
    do {{
        last if !$self->do_create_ART_activity( );
       
        last if !$self->do_check_ART_action_name( );
      
        last if !$self->do_check_ART_action_permission( );
          
        last if !$self->do_savepoint( );
   
        last if !$self->do_run_pre_ART_step_hook_chain( );
   
        last if !$self->do_ART_step( );
      
        last if !$self->do_run_post_ART_step_hook_chain( );
       
        $self->set_response_done( REASON => 'step eseguito' ); 
    }};   
     
    $self->check_rollback( );
    $response = $self->get_response( );
    $self->clear_work_area( );
    return $response;
}

=pod

=head2 finish( )

Questo metodo viene invocato da C<ramq> prima di terminare. B<PuÃ²> essere sovrascritto per
effettuare delle routine di conclusione del processo di elaborazione degli eventi.

PuÃ² lanciare una eccezione in caso di problemi fatali per comunicare C<ramq> di interrompere le elaborazioni.

=cut

#
# optionally override
#
sub finish {
    return;
}

=pod

=head1 UTILITY METHODS

In questa sezione vengono elencate dei metodi di utilitÃ  che possono essere utlizzati dal consumer.

=cut

sub get_stepper_config { shift->{STEPPER_ACK_CONFIG} }

         
sub do_create_ART_activity {
    my ($self) = @_;
  
    $self->logger()->trace( "Creazione ART_ACTIVITY" );
   
    my $eventAck = $self->get_work_area( )->{EVENT_ACK};
    my $eventData = $eventAck->get_event( )->get_data( );
    my $keyId = $self->get_stepper_config( )->{RA_DATA_KEY_FOR_ACTIVITY_ID};
   
    $self->logger()->trace( "ricerca ART_ACTIVITY_ID in dati evento: chiave '$keyId'" );
   
    if( ref( $eventData ) eq 'HASH' ) {
        if( exists $eventData->{$keyId} && defined $eventData->{$keyId} ) {          
            my $idVal = $eventData->{$keyId};
            $self->logger()->trace(
                  sprintf("trovato ART ACTIVITY ID '%s'", nvl_string($idVal) )
                 );
   
            $self->get_work_area->{ART_ACTIVITY_ID} = $idVal;
   
            my $artObj = $self->art( );
            if( !defined $artObj ) {
                croak "BUG ? Oggetto ART non definito (metodo art() restituisce 'undef')" .
                      " Verificare che il consumer di eventi sia configurato per connettersi al DB tramite un oggetto ART valido";
            }

            $self->logger()->info( "creazione oggetto ART ACTIVITY (ID='".nvl_string($idVal)."')" );
           
            my $artActivityObj = eval {
                API::ART::Activity::Factory->new( ART => $artObj, ID  => $idVal)
            };
            if( $@ || !defined $artActivityObj ) {
                my $exception = $@ || "API::ART::Activity::Factory : metodo new() restituisce undef";
                $self->logger()->error( "errore creando oggetto ART ACTIVITY (ID:'$idVal'): $exception" );
                $self->set_response_error( REASON => "Creazione ART ACTIVITY (ID:'$idVal') fallita" );                                                            
                return 0;
            }
           
            $self->get_work_area( )->{ART_ACTIVITY_OBJ} = $artActivityObj;
            $self->logger()->trace( "Instanziato ART ACTIVITY ID '" . nvl_string($idVal) ."'" );
            return 1;
        }
    }
   
    $self->logger()->fatal( "ART_ACTIVITY_ID non trovato o non definito" );
    $self->set_response_error( REASON => "ART ACTIVITY_ID non trovato o non definito" );
    return 0;
}
       
sub do_check_ART_action_name {
    my ($self) = @_;
   
    $self->logger()->trace( "Ricerca ART_ACTION" );
   
    my $artActionMap = $self->get_stepper_config()->{ACTION_MAP};
    my $eventName = $self->get_work_area( )->{EVENT_ACK}->get_event( )->get_event_name( );
    my $artActivityObj = $self->get_work_area( )->{ART_ACTIVITY_OBJ};
    my $artActivityTypeName;
    
    if( !defined $artActivityObj ) {
        $self->logger()->fatal( "BUG ? ACT_ACTIVITY_OBJ non presente in work area" );
        croak "BUG ? ART_ACTIVITY_OBJ non presente in work area";
    }
   
    $self->logger()->trace( "lettura nome tipo attivita' ART" );
      
    $artActivityTypeName = $artActivityObj->type_name( );
    if( ! defined $artActivityTypeName ) {
        my $exception = $artActivityObj->last_error || "risultato undef";
        $self->logger()->error( "errore leggendo nome tipo attivita' ART : " . $exception );
        $self->set_response_error( REASON  => "errore leggendo nome del tipo attivita' ART" );
        return 0;
    }
    
    my $fetchStatus = $self->get_work_area( )->{EVENT_ACK}->get_event( )->get_status( );
    $fetchStatus = $fetchStatus ? 'STATUS_KO' : 'STATUS_OK';
        
    $self->logger()->trace( "ricerca ART_ACTION ( ART_ACTIVITY_TYPE_NAME:'$artActivityTypeName', RA_EVENT:'$eventName', RA_STATUS:'$fetchStatus' )" ); 

    # cerca azione nelle mappe degli stati/flussi configurati e poi in quella di
    # default
    for( $artActivityTypeName, '*' ) {        
         if( exists $artActionMap->{$_} ) {
             if( exists $artActionMap->{$_}->{$eventName} && exists $artActionMap->{$_}->{$eventName}->{$fetchStatus} ) {
                 my $artActionName = $artActionMap->{$_}->{$eventName}->{$fetchStatus};
                 $self->logger()->trace( "<----- trovato ART_ACTION_NAME : '$artActionName'" );
                 $self->get_work_area( )->{ART_ACTION_NAME} = $artActionName;
                 return 1;
             }   
         }
    }
  
    $self->logger()->fatal( "ART ACTION per evento '$eventName' in stato '$fetchStatus', non trovata" );
    croak "ART ACTION per evento '$eventName' in stato '$fetchStatus', non trovata";
    return 0;
}
      
sub do_check_skip_zone {
    my ($self) = @_;
   
    $self->logger()->trace( "Verifica SKIP_ZONE: (EVENT vs ACTIVITY_STATUS)" );
 
    my $skipMap = $self->get_stepper_config( )->{ACTION_SKIP_ZONE_MAP};
    my $artActivityObj = $self->get_work_area( )->{ART_ACTIVITY_OBJ};
    my $artActionName = $self->get_work_area( )->{ART_ACTION_NAME};
    my $eventName = $self->get_work_area( )->{EVENT_ACK}->get_event( )->get_event_name( );
 
    if( ! defined $artActivityObj ) {
        $self->logger()->fatal( "BUG ? ART_ACTIVITY_OBJ non presente in work area" );
        croak "BUG ? ART_ACTIVITY_OBJ non presente in work area";
    }
 
    my $artActivityStatusName = eval {
              $artActivityObj->get_current_status_name( );
    };
    if( $@ || !defined $artActivityStatusName ) {
        my $exception = $@ || $artActivityObj->last_error || "restituito valore undef";
        $self->logger()->fatal( "impossibile determinare lo stato ART_ACTIVITY" );
        croak "Impossibile determinare lo stato di ART_ACTIVITY : $exception";
    }
   
    $self->logger()->trace( "verifica SKIP_ZONE per EVENTO:'$eventName', ACTIVITY_STATUS:'CURRENT_ACTIVITY_STATUS:'$artActivityStatusName' ");
   
    if( exists $skipMap->{$eventName} ) {
        if( exists $skipMap->{$eventName}->{$artActivityStatusName} ) {
              $self->logger()->info( "<----- skip evento, SKIP_ZONE  attiva (EVENTO:'$eventName', ACTIVITY_STATUS:'$artActivityStatusName')" );
               $self->set_response_skip( 
                               REASON => "skip evento, SKIP_ZONE (EVENTO:'$eventName', ACTIVITY_STATUS:'$artActivityStatusName')" 
                              );
                return 0;        
        }
    }
     
    $self->logger()->trace( "SKIP_ZONE: l'evento puo' essere elaborato" );
    return 1;
}

        
sub do_check_ART_action_permission {
    my ($self) = @_;
 
    $self->logger()->trace( "Verifica permessi di esecuzione ART ACTION" );
    
    my $artActivityObj = $self->get_work_area( )->{ART_ACTIVITY_OBJ};
    my $artActionName = $self->get_work_area( )->{ART_ACTION_NAME};
    
    my $res = eval { $artActivityObj->can_do_action( NAME => $artActionName ) };
    if ( $@ ) {
         $self->logger()->fatal( "Errore verificando permessi ACTION '$artActionName' su ACTIVITY ART" );
         croak $@;
    };
    if( !$res ) {
         my $errMsg = "ART ACTION ('$artActionName') non consentita";
         if( $artActivityObj->last_error ) {
             $errMsg .= ": " . $artActivityObj->last_error;
         }
             
         $self->logger()->error( $errMsg );
         $self->set_response_error( REASON => $errMsg );
         return 0;
    }
   
    return 1;
}
          
sub do_savepoint {
    my ($self) = @_;   

    if( defined $self->db() ) {   
        $self->logger()->trace( "Creazione DB SAVEPOINT 'EVENTSTEPPER_SAVEPOINT'" );   
        $self->db( )->do(q{ savepoint EVENTSTEPPER_SAVEPOINT });
        $self->get_work_area( )->{DB_SAVEPOINT_DONE} = 1;
    }
    else {
        $self->logger->trace( "DB REFRENCE mancante : SAVEPOINT NON CREATO" );
    }
    return 1;
}

sub do_run_pre_ART_step_hook_chain {
    my ($self) = @_;
   
    $self->logger()->trace( "-----> avvio hook pre ART_STEP" );
    #TODO:
    $self->logger()->trace( "<----- fine hook pre ART_STEP" );
   
    return 1;
}


sub _check_DTA_length_limit {
    my ($self, $artActivityObj, $dtaProps) = @_;

    my $dta_limit_map = $self->get_stepper_config( )->{DTA_LENGTH_LIMIT_MAP};
    my $actTypeName   = $artActivityObj->type_name();
    my $actId         = $artActivityObj->id();

    if( exists $dta_limit_map->{$actTypeName} ) {
        
        for( keys %{$dtaProps} ) {
             next if !defined $dtaProps->{$_};
             next if !defined $dta_limit_map->{$actTypeName}->{$_};
             next if $dta_limit_map->{$actTypeName}->{$_} < 1;
  
             my $maxLen = $dta_limit_map->{$actTypeName}->{$_};

             if( $maxLen < length($dtaProps->{$_}) ) {
                 $self->logger->warn(
                            "($actTypeName: $actId) troncamento lunghezza dato tecnico '$_' " 
                          . "da " . length($dtaProps->{$_}) 
                          . " a $maxLen ch."
                 );
                 $dtaProps->{$_} = substr( $dtaProps->{$_}, 0, $maxLen ); 
             }
        }
    }
    return;
}
 
   
sub do_ART_step {
    my ($self) = @_;

    $self->logger()->trace( "Avvio ART_STEP" );   
    
    my $conf = $self->get_stepper_config( );
    my $artActivityObj = $self->get_work_area( )->{ART_ACTIVITY_OBJ};
    my $artActionName = $self->get_work_area( )->{ART_ACTION_NAME};
    
    my $eventData = $self->get_work_area( )->{EVENT_ACK}->get_ack_data( );

    $eventData = { } if !defined $eventData;
    
    
    if( defined $conf->{TARGET_REF_TO_TDTA} ) {
        
        my $eventTargetRef = eval { $self->get_work_area( )->{EVENT_ACK}->get_event( )->get_target_ref( ) };
        if( $@ || !defined $eventTargetRef ) {
            croak "Impossibile ottenere il valore TARGET_REF associato all'evento RA";
        }
        
        my $tdta = $conf->{TARGET_REF_TO_TDTA};
        $self->logger()->trace( "Aggiungo TARGET_REF '$eventTargetRef' al tipo dato tecnico '$tdta'" );
        $eventData->{$tdta} = $eventTargetRef;
    }
   
    $eventData->{RESULT_MESSAGE} = $self->get_work_area( )->{EVENT_ACK}->get_event( )->get_info( )->{REASON};
 
    my $res = 0;
    eval {
       $self->logger()->trace( "nome ACTION '$artActionName'" );

       $self->_check_DTA_length_limit( $artActivityObj, $eventData );
	    my $engineStep = $self->_get_engine_stepper();

	    if( defined $engineStep ) {
	      $res = $engineStep->handle(
	             ACTIVITY_ID      => $artActivityObj->id()
	            ,ACTION_NAME      => [$artActionName]
	            ,STEP_DESCRIPTION => "Azione automatica eseguita con script '${\&basename($0)}'"
	            ,PROPERTIES       => $eventData
	      );
	    }
	    else {
	      $res = $artActivityObj->step_filtered (
	                    ACTION     => $artActionName,
	                    PROPERTIES => $eventData
	      );
	    }
    };
    if( $@ ) {
       my $exception = $@;
       $self->logger()->fatal( "errore eseguendo STEP ACTIVITY: $exception" );
       croak $exception;
    }
    
    if( !$res ) {
       my $except = $self->art->last_error;
       $except = " - messaggio di errore mancante... -" if !defined $except;
       $self->logger()->error( "errore eseguendo lo step: " . $except );
       $self->set_response_error( REASON => "esecuzione step fallita: " . $except );                              
       return 0;
    }
    return 1;
}
      
sub do_run_post_ART_step_hook_chain {
    my ($self) = @_;
    
    $self->logger()->trace( "-----> avvio hook post ART_STEP" );
    #TODO:
    $self->logger()->trace( "<----- fine hook post ART_STEP" );
  
    return 1;
}

        
sub set_response_done {
    my ($self) = shift;
    my %param = @_;
   
    $self->logger()->trace( "Preparo RESPONSE per 'EVENTO OK'" );
   
  
    my $response = $self->get_work_area( )->{RESPONSE};
   
    $self->get_work_area( )->{DB_ROLLBACK_NEEDED} = 0;
  
    $response->{RESULT} = STEPPER_RESULT_CONSUME;
    $response->{STATUS} = STEPPER_EVENT_OK;
    $response->{REASON} = $param{REASON};
    return;  
}        

sub set_response_error {
    my ($self) = shift;
    my %param = @_;
   
    $self->logger()->trace( "Preparo RESPONSE per 'EVENTO KO'" );
   
    my $conf = $self->get_stepper_config( );
    my $workArea = $self->get_work_area( );
    my $response = $workArea->{RESPONSE};
   
    $workArea->{DB_ROLLBACK_NEEDED} = 1;
      
    $response->{RESULT} = STEPPER_RESULT_CONSUME;
    $response->{STATUS} = STEPPER_EVENT_KO;
    $response->{REASON} = $param{REASON};
    return;
}

sub set_response_skip {
    my ($self) = shift;
    my %param = @_;
   
    $self->logger()->trace( "Preparo RESPONSE per 'SKIP EVENTO'" );
   
    my $conf = $self->get_stepper_config( );
    my $workArea = $self->get_work_area( );
    my $response = $workArea->{RESPONSE};
   
    $workArea->{DB_ROLLBACK_NEEDED} = 1;
    $response->{RESULT} = STEPPER_RESULT_SKIP;
    $response->{REASON} = $param{REASON};
    return;  
}

 
sub check_rollback {
    my ($self) = @_;
    
    my $workArea = $self->get_work_area( );
    if( $workArea->{DB_ROLLBACK_NEEDED} ) {
        if( $workArea->{DB_SAVEPOINT_DONE} ) {
       
            $self->logger()->info( "Effettuo ROLLBACK SAVEPOINT" );
            $self->db( )->do(q{ rollback to EVENTSTEPPER_SAVEPOINT });
            $workArea->{DB_SAVEPOINT_DONE} = undef;
        }
        $workArea->{DB_ROLLBACK_NEEDED} = undef;
    }
    return 1;
}        
        
sub get_response {
    my ($self) = @_;
   
    $self->logger()->info( "Fine elaborazione" );
   
    my $conf = $self->get_stepper_config( );
    my $workArea = $self->get_work_area( );
    my $response = $workArea->{RESPONSE};
    my $prefix = "";
 
    $self->logger()->trace( "RESPONSE:" );
    $self->logger()->trace( "   RESULT: " . nvl_string($response->{RESULT}) );
    $self->logger()->trace( "   STATUS: " . nvl_string($response->{STATUS}) );
    $self->logger()->trace( "   REASON: " . nvl_string($response->{REASON}) );
   
    $response->{REASON} = '' if !defined $response->{REASON};
   
    if( $response->{RESULT} eq STEPPER_RESULT_CONSUME ) {
        if( $response->{STATUS} eq STEPPER_EVENT_KO ) {
        
            if( $conf->{SKIP_ON_ERROR} ) {
                $self->logger()->info( "risultato: SKIP EVENTO (flag SKIP_ON_ERROR)" );
                return $self->skip( NOTE => "Step SKIPPED: " . $response->{REASON} );
            }    
            $self->logger()->info( "risultato: EVENTO KO" );
            $prefix = "Step ERROR:";
        }
        else {
            $self->logger()->info( "risultato: EVENTO OK" );
            $prefix = "Step DONE:";
        }
 
        return $self->consume( NOTE => $prefix . " " . $response->{REASON} );
    }
    elsif( $response->{RESULT} eq STEPPER_RESULT_SKIP ) {
        
        $self->logger()->info( "risultato: SKIP EVENTO" );
        return $self->skip( NOTE => "Step SKIPPED: " . $response->{REASON} );
    }  
    croak "BUG: get_response: ciclo di lavorazione terminato senza restiture un oggetto response valido";
    return;   
}
 

#
# utilities (static methods)
#   

sub nvl_string { defined $_[0] ? $_[0] : "undef" }

#
# espande il valore di  eventuali variabili esterne (${})
#
# IN:  stringa(SCALAR),  getter( CODEREF )  [ getter = sub( nome_parametro ) { return value } ]
# OUT: stringa(SCALAR)
#
sub expand_string
{
    my $str = shift;
    my $getter = shift;
    my $result = undef;
    my $start = 0;
    my $reps = 0;
    
    for( ;; $start = pos( $$str ) )
    {
   	    if( $$str =~ /(\$+)\{([^\}]*)\}/gc ) {
                my $pre = $1;     
                my $key = $2;
                my $end = pos( $$str );

                $result .= substr( $$str, $start, $end - 2 - length($pre) - length($key) - $start );
               
                if( length($pre) & 1 ) {
                    chop $pre;
                    $pre =~ s/\$\$/\$/g;
                    $result .= $pre;
                    
                    $key =~ s/^\s+//;
                    $key =~ s/\s+$//;
                    my $dieflag = !( $key =~ s/^\?// ); 
                    my $pre = &$getter( $key );
                    
                    croak( "la variabile '$key' non è presente nell'environment" )
                      if !defined( $pre ) && $dieflag;
                    $result .= (defined $pre ? $pre : '');
                    $reps++;           
                }
                else {
                    $pre =~ s/\$\$/\$/g;
                    $result .= $pre;
                    $result .= substr( $$str, $end - 2 - length($key), length($key) + 2 );
                    $reps++;
                }
                next;
        }
	    
		$result .= substr( $$str, $start );
		last;
	}	
    $$str = $result if $reps;
	return $reps;
}

sub exp_envstr
{
   my ($refPar) = @_;
   expand_string( $refPar, sub { my $v = shift; exists $ENV{$v} ? $ENV{$v} : undef } );
   return $refPar;
}


=pod

=head1 SEE ALSO

Script perl B<ramq>, package B<SIRTI::Queue::Event>, B<SIRTI::Queue::EventAckConsumer>, B<SIRTI::Queue::EventAck>


=head1 BUGS

Eventuali bug riscontrati dovranno essere segnalati su B<ART - Global Services>: L<https://www.artnet.sirtisistemi.net/ARTIT/art> aprendo un apposito DEV-TICKET.

=head1 HISTORY

=over

=item Ver. 0.1

Prima release del modulo

=back


=head1 AUTHOR

Gruppo OLO :)

=cut


if( __FILE__ eq $0 ) {

{
 package mylogger;
 sub info { shift; print "INFO: ", @_, "\n" }
 sub warn { shift; print "WARN: ", @_, "\n" }
 sub error { shift; print "ERROR: ", @_, "\n" }
 sub fatal { shift; print "FATAL: ", @_, "\n" } 
}

{
 package myArtObj;
 sub info { return "Ok" }
}

 package main;

 my $cstring = 'remote_activity/remote_activity@dleadst2';
 
 my $logger = bless( {}, 'mylogger' );

 
 my $obj = bless ( { }, 'SIRTI::Queue::EventAckConsumer' );
 
 $obj = bless ( $obj, 'SIRTI::Queue::EventConsumerStepperAck' );
 $obj->{__LOGGER__} = $logger;
 $obj->{_WORK_CONTEXT}->{STEPPER_ACK_CONFIG_FILE} = shift || "./stepper.conf";
 
 $obj->{_DB} = SIRTI::DB::Oracle->new( $cstring );
 $obj->{_ART} = API::ART->new( 
                       ARTID    => 'T2_OM_DEV',
                       USER     => 'OM_SCRIPT',
                       PASSWORD => 'OM_SCRIPT'
 );
  
 $obj->read_config_file( );
 $obj->validate_config( );
 
 print $obj->get_stepper_config,"\n";
 
 print ":)\n";

 sleep 1;
 
 print "", Dumper( $obj->get_stepper_config( ) ), "\n";
 
 my $conf = $obj->get_stepper_config( );
  
 
 print "start consume event...\n";
 
 {
    package FakeEvent;

    sub get_event_name { shift; "EVENTO_FAKE"; }
    sub get_info       { shift; { COLORE => "GIALLO" } }
    sub get_data       { shift; { ACT_ID_KEY => "890" } }
    sub get_source_ref { shift; "SOURCE_REF_$$" }
    sub new            { bless( {}, "FakeEvent" ) }
    sub get_event      { return shift; }
    1;
 }
 
 $obj->consume_event_ack( EVENT_ACK => new FakeEvent( ) );
 
 print "end consume event...\n";
 
 print "\n";
 print "*_*\n";
 
 exit 0;

}

1;
