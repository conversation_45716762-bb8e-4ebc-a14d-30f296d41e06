package SIRTI::Queue::ExampleAckConsumer;

use strict;
use warnings;
use Carp;
use Data::Dumper;

use base 'SIRTI::Queue::EventAckConsumer';

#
# override
#
sub get_managed_event_types { return []; }

#
# override
#
sub get_managed_source_refs { return []; }

#
# override
#
sub consume_event_ack {
	my $self = shift;
	my %params = @_;
	
	# eventAck  SIRTI::Queue::EventAck
	my $eventAck = $params{EVENT_ACK};
	
	$self->logger()->info( "Elaboro un evento ack..." );
	$self->logger()->info( "TIPO: " . ( $eventAck->is_global() ? "GLOBALE" : "TARGET ACK" ) );
	$self->logger()->info( "EVENT INFO: " . Dumper( $eventAck->get_event()->get_info() ) );
	$self->logger()->info( "EVENT DATA: " . Dumper( $eventAck->get_event()->get_data() ) );
	$self->logger()->info( "ACK DATA:   " . Dumper( $eventAck->get_ack_data() ) );
	
	# croak "STOP!!!" per fermare l'elaborazione
	
	if ( int ( rand 2 ) ) {
		return $self->consume( NOTE => 'Pippo' )
	} else {
		return $self->skip(  );
	}
}

1;
