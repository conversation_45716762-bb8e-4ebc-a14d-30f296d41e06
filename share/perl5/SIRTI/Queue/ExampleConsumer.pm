package SIRTI::Queue::ExampleConsumer;

use strict;
use warnings;
use Carp;
use Data::Dumper;

use base 'SIRTI::Queue::EventConsumer';

#
# override
#
sub get_managed_event_types { return []; }

#
# override
#
sub get_managed_source_refs { return []; }

#
# override
#
sub consume_event {
	my $self = shift;
	my %params = @_;
	my $event = $params{EVENT};
	
	$self->logger()->info( "Elaboro un evento..." );
	$self->logger()->info( "EVENT NAME: " . $event->get_event_name() );
	$self->logger()->info( "INFO: " . Dumper( $event->get_info() ) );
	$self->logger()->info( "DATA: " . Dumper( $event->get_data() ) );

	# croak "STOP!!!" per fermare l'elaborazione

	if ( int ( rand 2 ) ) {
		return $self->consume(
			REASON => 'test fetch',
			STATUS => 0,
			TARGET_REF => 'OK',
			ACK_DATA => { NOME => 'Pippo' }
		);
	} else {
		return $self->skip( REASON => 'test skip' );
	}
}

1;
