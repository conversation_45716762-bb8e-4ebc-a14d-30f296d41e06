################################################################################
#
# SIRTI::Queue::Response
#
################################################################################
=pod

=head1 NAME

B<SIRTI::Queue::Response> - Oggetto che indica al processo B<ramq> l'esito dell'elaborazione di un evento
  
=head1 DESCRIPTION

L'oggetto viene restituito dal consumer di eventi (SIRTI::Queue::EventConsumer) al processo B<ramq>
per comunicare l'esito dell' elaborazione di un evento.
  
=cut


package SIRTI::Queue::Response;

use strict;
use warnings;
use Carp;
use Log::Log4perl qw(get_logger :levels :nowarn);

use base 'SIRTI::Base';

=pod

=head1 METHODS

=head2 new( FETCH_RESULT => C<SCALAR>, REASON => C<SCALAR>, STATUS => C<SCALAR>, TARGET_REF => C<SCALAR>, ACK_DATA => C<HASHREF> )

Metodo costruttore dell'oggetto. Per i parametri vedi il metodo C<set_fetch_result()>


=cut

sub new {

    my $this = shift;
    my $class = ref($this) || $this;
    my %params = @_;
    my $self = bless( {}, $class );
    my $errmsg = '';

	$self->{__LOGGER__} = Log::Log4perl->get_logger( 'COMMON::LIB::' . __PACKAGE__ );
	$self->{__LOGGER__}->trace( __PACKAGE__ . '->new' );

    $self->clear_error();
    
    croak $self->last_error()
    	unless defined $self->set_fetch_result( @_ );
    
	return $self;
}

=pod

=head2 set_fetch_result( FETCH_RESULT => C<SCALAR>, REASON => C<SCALAR>, STATUS => C<SCALAR>, TARGET_REF => C<SCALAR>, ACK_DATA => C<HASHREF> )

Configura l'oggetto risposta specificando il risultato dell'elaborazione tramite i seguenti
parametri:

=over

=item * B<FETCH_RESULT> : se valorizzato a 1 indica a C<ramq> di eseguire il fetch dell'evento, se vale 0 indica 
di non eseguire il fetch e di inserire l'evento in tabella di LOOKUP

=item * B<REASON> : stringa che descrive l'esito dell'elaborazione

=item * B<STATUS> : obbligatorio solo nel caso in cui B<FETCH_RESULT> sia uguale a 1. Indica un codice
numerico che rapprenta l'esito della lavorazione. Per convenzione 0 indica l'esito positivo.

=item * B<TARGET_REF> : obbligatorio solo nel caso in cui B<FETCH_RESULT> sia uguale a 1. Permette di 
speificare una chiave di riferimento nel contesto di elaborazione del destinatario.

=item * B<ACK_DATA> : opzionale. hashref chiave valore che può essere utilizzato per comunicare 
informazioni al servizio sorgente dell'evento

=back

=cut

sub set_fetch_result {
	
	my $self = shift;
	my %params = @_;
	
	$self->clear_error();
	my $errmsg;
	$self->last_error( $errmsg )
		&& return undef
			unless $self->check_named_params(
		         ERRMSG     => \$errmsg
		        ,PARAMS     => \%params
		        ,MANDATORY  => {
					 FETCH_RESULT              => { isa => 'SCALAR', list => [ 0, 1 ] }
		            ,REASON                    => { isa => 'SCALAR', pattern => qr/^.+$/m }
		        }
		        ,OPTIONAL   => {
		             STATUS                    => { isa => 'SCALAR', pattern => qr/^\d+$/ }
		            ,TARGET_REF                 => { isa => 'SCALAR', pattern => qr/^.+$/ }
		            ,ACK_DATA                      => { isa => 'HASH' }
		        }
			);

	$self->last_error( "STATUS must be defined!" )
		&& return undef
			if ( $params{FETCH_RESULT} == 1 && !exists $params{STATUS} );
	$self->last_error( "TARGET_REF must be defined!" )
		&& return undef
			if ( $params{FETCH_RESULT} == 1 && !exists $params{TARGET_REF} );

	$self->{_FETCH_RESULT} = $params{FETCH_RESULT};
	$self->{_REASON}       = $params{REASON};

	$self->{_STATUS}    = exists $params{STATUS} ? $params{STATUS} : undef;
	$self->{_TARGET_REF} = exists $params{TARGET_REF} ? $params{TARGET_REF} : undef;
	$self->{_ACK_DATA}      = exists $params{ACK_DATA} ? $params{ACK_DATA} : {}; 

	return 1;
}

=pod

=head2 get_status( )

Ritorna il valore della proprietà STATUS, impostato dal metodo C<set_fetch_result( )>

=cut

sub get_status { shift->{_STATUS} }

=pod

=head2 get_target_ref( )

Ritorna il valore della proprietà TARGET_REF, impostato dal metodo C<set_fetch_result( )>

=cut

sub get_target_ref { shift->{_TARGET_REF} }

=pod

=head2 get_reason( )

Ritorna il valore della proprietà REASON, impostato dal metodo C<set_fetch_result( )>

=cut

sub get_reason { shift->{_REASON} }

=pod

=head2 get_ack_data( )

Ritorna il valore della proprietà ACK_DATA, impostato dal metodo C<set_fetch_result( )>

=cut

sub get_ack_data { shift->{_ACK_DATA} }

=pod

=head2 get_fetch_result( )

Ritorna il valore della proprietà FETCH_RESULT, impostato dal metodo C<set_fetch_result( )>

=cut

sub get_fetch_result { shift->{_FETCH_RESULT} }

=pod

=head2 logger( )

Questo metodo restituisce un oggetto logger che può essere utilizzato per effettuare il logging del consumer
con i metodi C<trace()>, C<debug()>, C<info()>, C<warn()>, C<error()> e C<fatal()>.

=cut

sub logger { shift->{__LOGGER__}; }

############################################################################
# UNIT TEST
############################################################################

if ( __FILE__ eq $0 ) {
}

1;
