################################################################################
#
# SIRTI::Queue::ResponseAck
#
################################################################################
=pod

=head1 NAME

B<SIRTI::Queue::ResponseAck> - Oggetto che indica al processo B<ramq> l'esito dell'elaborazione di un evento di acknowlege
  
=head1 DESCRIPTION

L'oggetto viene restituito dal consumer di eventi che richiedono acknowledge (SIRTI::Queue::EventAckConsumer) 
al processo B<ramq> per comunicare l'esito dell' elaborazione di un evento.
  
=cut

package SIRTI::Queue::ResponseAck;

use strict;
use warnings;
use Carp;
use Log::Log4perl qw(get_logger :levels :nowarn);

use base 'SIRTI::Base';


=pod

=head1 METHODS

=head2 new( FETCH_RESULT => C<SCALAR>, NOTE => C<SCALAR> )

Metodo costruttore dell'oggetto. Per il significato dei parametri vedi il metodo C<set_fetch_result()>

=cut

sub new {

    my $this = shift;
    my $class = ref($this) || $this;
    my %params = @_;
    my $self = bless( {}, $class );
    my $errmsg = '';

	$self->{__LOGGER__} = Log::Log4perl->get_logger( 'COMMON::LIB::' . __PACKAGE__ );
	$self->{__LOGGER__}->trace( __PACKAGE__ . '->new' );

    $self->clear_error();
    
    croak $self->last_error()
    	unless defined $self->set_fetch_result( @_ );
    
	return $self;
}


=pod

=head2 set_fetch_result( FETCH_RESULT => C<SCALAR>, NOTE => C<SCALAR> )

Configura l'oggetto risposta specificando il risultato dell'elaborazione tramite i seguenti
parametri:

=over

=item * B<FETCH_RESULT> : se valorizzato a 1 indica a C<ramq> di eseguire il fetch dell'ack, se vale 0 indica 
di non eseguire il fetch

=item * B<NOTE> : stringa opzionale che descrive l'esito dell'elaborazione

=back

=cut

sub set_fetch_result {
	
	my $self = shift;
	my %params = @_;
	my $errmsg;
	
	$self->last_error( $errmsg )
		&& return undef
			unless $self->check_named_params(
		         ERRMSG     => \$errmsg
		        ,PARAMS     => \%params
		        ,MANDATORY  => {
					 FETCH_RESULT              => { isa => 'SCALAR', list => [ 0, 1 ] }
		        }
		        ,OPTIONAL   => {
		             NOTE                      => { isa => 'SCALAR', pattern => qr/^.+$/m }
		        }
			);
		
	$self->{_FETCH_RESULT} = $params{FETCH_RESULT};	
	$self->{_FETCH_ACK_NOTE} = exists $params{NOTE} ? $params{NOTE} : undef;
 		
	return 1;
}

=pod

=head2 get_ack_note( )

Ritorna il valore della proprietà NOTE, impostato dal metodo C<set_fetch_result( )>

=cut

sub get_ack_note { shift->{_FETCH_ACK_NOTE}; }


=pod

=head2 get_fetch_result( )

Ritorna il valore della proprietà FETCH_RESULT, impostato dal metodo C<set_fetch_result( )>

=cut

sub get_fetch_result { shift->{_FETCH_RESULT}; }

=pod

=head2 logger( )

Questo metodo restituisce un oggetto logger che può essere utilizzato per effettuare il logging del consumer
con i metodi C<trace()>, C<debug()>, C<info()>, C<warn()>, C<error()> e C<fatal()>.

=cut


sub logger { shift->{__LOGGER__}; }

############################################################################
# UNIT TEST
############################################################################

if ( __FILE__ eq $0 ) {
}

1;
