package SIRTI::REST;

use strict;
use warnings FATAL => 'all';
use Carp;
use utf8;
use Digest::MD5 qw(md5_hex);
use DateTime::Format::ISO8601;
use DateTime::Format::Strptime;
use YAML qw'freeze';
$YAML::UseBlock = 1;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{COM}/share/locale" );
use POSIX; 

use Dancer2;
use Dancer2::FileUtils qw(read_file_content read_glob_content);
use HTTP::Status qw(:constants :is status_message);

my $prefix = defined $ENV{WS_ROUTES_PREFIX} ? $ENV{WS_ROUTES_PREFIX} : '/rest';
$prefix =~ s#/+$##;

sub _is_cors { return defined request->header('origin') ? request->header('origin') : undef }

sub is_options { return lc(request->method()) eq 'options' }

sub handle_cors_request {
	
	# ref http://www.html5rocks.com/en/tutorials/cors/
	
	my %params = @_;
	
	debug "Sono in handle_cors_request";
	debug "Headers:\n" . to_dumper request->headers();

	my $origin = _is_cors();
	
	debug "Non e' una CORS"
		&& return
			unless defined $origin;

	# e' una CORS
	debug "E' una CORS con Origin: $origin";

	my $acrm = request->header('access-control-request-method') ? lc(request->header('access-control-request-method')) : undef;

	debug "E' una CORS con ACRM: " . ($acrm || '');

	if ( is_options() && $acrm ) {
		
		# e' una preflight
		
		my $methods = $params{METHODS};
		
		return
			unless scalar grep { lc($_) eq $acrm } @$methods;
		
		debug "ACRM $acrm consentito";
		
		my %headers = (
			 'Access-Control-Allow-Origin' => $origin
			,'Access-Control-Allow-Methods' => uc(join(', ', @$methods))
			,'Access-Control-Allow-Headers' => 'accept, content-type, content-length'
		);
		
		if( config->{environment} ne 'production' ) {
			$headers{'Access-Control-Allow-Headers'} .= ', x-process-pid';
		}
		
		response_headers %headers;
		
	} else {
		
		my %headers = (
			 'Access-Control-Allow-Origin' => $origin
			,'Access-Control-Expose-Headers' => 'accept, content-type, content-length'
		);
		
		if( config->{environment} ne 'production' ) {
			$headers{'Access-Control-Expose-Headers'} .= ', x-process-pid';
		}
		
		response_headers %headers;
		
	}

};

sub send_ok {
	
	# FIXME: capire come mandare l'header content-type corretto, ammesso che sia possibile
	
	my %params = @_;
	$params{CODE} = HTTP_OK unless $params{CODE};
	
	debug "Entro in send_ok";
	
	status($params{CODE});
	return defined $params{MSG} ? $params{MSG} : undef;
	
}

sub send_file_ok {
	
	send_file(@_);
}

sub send_ko {
	my %params = @_;
	
	$params{CODE} = HTTP_INTERNAL_SERVER_ERROR unless $params{CODE};
	$params{ERROR_MSG} = status_message($params{CODE}) unless $params{ERROR_MSG};
	$params{INTERNAL_ERROR_MSG} = $params{INTERNAL_ERROR_MSG} ? $params{INTERNAL_ERROR_MSG} : $params{ERROR_MSG};
	my $return = {
		 UUID		=> vars->{uuid}
		,message	=> $params{ERROR_MSG}
		,internalMessage => $params{INTERNAL_ERROR_MSG}
	};
	info "Error:\n" . to_dumper $return;
	delete $return->{internalMessage}
		if config->{environment} eq 'production';
	status($params{CODE});
	# introduco gestione con serializer e lascio al vecchia per retrocompatibilita
	if (!$params{OUTPUT_TYPE} && ref response->serializer ne 'Dancer2::Serializer::RAW') {
		return $return;
	} else {
		# FIXME: capire come mandare l'header content-type corretto, ammesso che sia possibile
		$params{OUTPUT_TYPE} = 'RAW' unless $params{OUTPUT_TYPE}; # RAW|JSON
		if($params{OUTPUT_TYPE} eq 'RAW') {
			$return = freeze($return);
		} else { # $params{OUTPUT_TYPE} eq 'JSON'
			$return = to_json($return);
		}
		return $return;
	}
}

sub request_payload {
	# il serializzatore RAW ritorna una struttura nel formato BODY => <raw body>
	return request->params('body')->{BODY};
}

sub db {
	croak __("WS_SUPPORT_DB not enabled")
		unless $ENV{WS_SUPPORT_DB};

	return vars->{_db};
}

sub do_db_save {
	croak __("WS_SUPPORT_DB not enabled")
		unless $ENV{WS_SUPPORT_DB};

	my %query_params = defined request->params('query') ? request->params('query') : ();
	
	my $db = db();

	# NB: se l'environment è diverso da "production"" possiamo utilizzare il GET param "rollback" per evitare il commit	
	if ( $query_params{rollback} && config->{environment} !~ /^production/ ) {
		debug "do rollback";
		$db->rollback;
	} else {
		debug "do commit";
		$db->commit;
	}

}

sub do_db_cancel {
	croak __("WS_SUPPORT_DB not enabled")
		unless $ENV{WS_SUPPORT_DB};

	my $db = db();
	debug "do rollback";
	$db->rollback;

}

sub remove_in_hook_after {
	
	my $file = shift;

	croak __("You must invoke this function with a valid filename")
		unless defined $file && -f $file;

	my $to_remove_in_hook_after = vars->{_to_remove_in_hook_after};
	push @$to_remove_in_hook_after, $file;
	
	var _to_remove_in_hook_after => $to_remove_in_hook_after;
	
}

sub force_file_content_length {
	
	my $size = shift;
	var _attachment_size => $size;
	
}

my $DEFAULT_TIME_ZONE = 'Europe/Rome';
my $DEFAULT_ISO_DATE_FORMAT = DateTime::Format::Strptime->new(pattern   => '%FT%T.%N');

sub format_iso_date {
	my ($date, $referror) = @_;
	
	$date =~ s/^(.{4})(.{2})(.{2})(.{2})(.{2})(.{2})$/$1-$2-$3T$4:$5:$6/;
	
	my $dt = eval{DateTime::Format::ISO8601->parse_datetime( $date )};
	
	if ($@) {
		$$referror = __x("Not a valid ISO Date: {errmsg}", errmsg => $@);
		return undef;
	}
	
	my $offset = $dt->set_time_zone($DEFAULT_TIME_ZONE)->offset(); # 7200
	my ($hour, $min) = (int($offset/(60*60)), int($offset%(60*60)));

	return $dt->set_time_zone($DEFAULT_TIME_ZONE)->set_formatter($DEFAULT_ISO_DATE_FORMAT).sprintf("%+03d:%02d", $hour, $min);
}

sub _normalize_locale {
	# FIXME: per ora il rimappamento tra il language nel formato http (it, it-IT) e quello in formato locale
	# viene fatto cercando la prima occorrenza di un valore nel formato xx-YY nell’header http e tradotto nel
	# formato xx_YY accettato da locale. Se ad esempio si riceve un’header nel formato "it,en-US;q=0.8,en;q=0.6,fr;q=0.4"
	# verrà utilizzato l’inglese. Tutto ciò è decisamente tricky, bisogna indagare su un metodo più corretto...
	my $str = shift;
	my $code = 'en_US';
	for my $c (map { s/;.*//; s/-/_/; $_ } split(/,/, $str)) {
		if(length($c) > 2) {
			$code = $c;
			last;
		}
	}
	return $code;
}

############ HOOKS ################

hook before => sub {
	
	setlocale (LC_ALL, 'en_US');
	setlocale (LC_ALL, _normalize_locale($ENV{HTTP_ACCEPT_LANGUAGE})) if defined $ENV{HTTP_ACCEPT_LANGUAGE};
	
	var uuid => md5_hex(rand());
		
	debug "Sono in ".__PACKAGE__."::hook before";

	var _to_remove_in_hook_after => [];
	
	return
		if is_options();
	
	handle_cors_request();

	if($ENV{WS_SUPPORT_DB}) {

		debug "Istanzio il DB in ".__PACKAGE__."::hook before";
		var _db => SIRTI::DB->new($ENV{SQLID});

	}

};

hook after => sub {
	
	debug "Sono in hook after";
	
	if( config->{environment} ne 'production' ) {
		response_headers 'X-Process-Pid' => $$;
	}
		
	my $to_remove_in_hook_after = vars->{_to_remove_in_hook_after};
	
	for my $file (@$to_remove_in_hook_after) {
		debug "Unlinking file $file";
		unlink $file;
	}
	
	$ENV{_WS_UUID} = vars->{uuid};
	
};

hook after_file_render => sub {
	
	my $response = shift;
	
	debug "Sono in hook after_file_render";
	response_headers 'Content-Length' => vars->{_attachment_size} if defined vars->{_attachment_size};

};

=pod
@apiDefine ContentTypeJsonHeader
@apiHeader {String} Content-Type It must be set to <code>application/json</code>. 
=cut

prefix $prefix;

######## Definizione rotte ###########

any qr{.*} => sub {
	return send_ko(CODE => HTTP_NOT_FOUND);
};


if (__FILE__ eq $0) {
	set port => 10082;
	setting log_path => $ENV{HOME}."/tmp/dancer_log";
	set logger => "console";
	set log => 'debug';
   	set serializer => 'JSON';
	dance;
}





1;
