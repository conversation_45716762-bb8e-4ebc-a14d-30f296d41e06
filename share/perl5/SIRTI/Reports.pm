################################################################################
#
# SIRTI::Reports
#
################################################################################
=pod

=head1 NAME

B<SIRTI::Reports> - Generatore semi-automatico di report basati su query Oracle

=head1 SYNOPSIS

  use SIRTI::DB;
  use SIRTI::Reports;
  
  my $sr = SIRTI::Reports->instance(DB => SIRTI::DB->new($ENV{SQLID}));
  
  my $report = $sr->report(
     QUERY => 'select * from v_attivita'
    ,OUTPUT_FORMAT => 'json'
  );

=head1 DESCRIPTION

Classe singleton per la generazione in modo semi-automatico di report basati su query Oracle.

=cut

package SIRTI::Reports;

use strict;
use warnings;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);
use File::Temp;
use Tie::IxHash;
use Excel::Writer::XLSX;
use JSON;
use DateTime::Format::ISO8601;
use DateTime::Format::Strptime;
use Text::CSV::Encoded;
use DBI qw(:sql_types);
use DBD::Oracle qw(:ora_types);

use SIRTI::DB;

use base 'SIRTI::Base::Singleton';

#override metodi Class::Singleton
sub _new_instance {
	my $class = shift;
	my $self  = bless { }, $class;
	my %params = @_;
	
	die 'Missing mandatory param DB' unless $params{DB};
	
	$self->{DB} = $params{DB};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'COMMON::LIB::' . __PACKAGE__ );
	
	$self->{NLS_TIMESTAMP_TZ_FORMAT} = 'YYYY-MM-DD"T"hh24:mi:ss.fftzh:tzm';
	$self->{DEFAULT_TIME_ZONE} = 'Europe/Rome';	
	$self->{DEFAULT_ISO_DATE_FORMAT} = DateTime::Format::Strptime->new(
		pattern   => '%FT%T.%N'
	);
	
	return $self;
}

=head1 METHODS

=head2 instance( %params )

Restituisce un'istanza della classe

=over

=item * B<C<DB>> <C<SIRTI::DB>>

Obbligatorio, connessione al database

=back

=cut
sub instance{
	shift->Class::Singleton::instance(@_);
}

sub has_instance{
	shift->Class::Singleton::has_instance(@_);
}

=pod

=head2 last_error()

Ritorna l'ultimo messaggio di errore settato

=cut

#fine override metodi Class::Singleton

sub _db { shift->{DB} };

sub _logger { shift->{LOGGER} };

sub _get_table_or_view {
	
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->last_error($errmsg)
		and return undef
			unless $self->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 TABLE_OR_VIEW_NAME				=> { isa => 'SCALAR', pattern => qr{^[^\.@]+$} }
				}
				,OPTIONAL   => {
					 TABLE_OR_VIEW_OWNER			=> { isa => 'SCALAR', pattern => qr{^[^\.@]+$} }
					,TABLE_OR_VIEW_DBLINK			=> { isa => 'SCALAR', pattern => qr{^[^\.@]+$} }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	return
		  (defined $params{TABLE_OR_VIEW_OWNER} ? $params{TABLE_OR_VIEW_OWNER} . '.' : '')
		. $params{TABLE_OR_VIEW_NAME}
		. (defined $params{TABLE_OR_VIEW_DBLINK} ? '@' . $params{TABLE_OR_VIEW_DBLINK} : '')
	;
	
}

sub _get_query {
	
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->last_error($errmsg)
		and return undef
			unless $self->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 SQL_DATE_FORMAT_STRING	=> { isa => 'SCALAR' }
				}
				,OPTIONAL   => {
					 QUERY					=> { isa => 'SCALAR' }
					,COLUMNS				=> { isa => 'ARRAY' }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	my $dbh = $self->_db();
	my $logger = $self->_logger();
	
	my $text;
	
	my $column_types = $self->_get_columns( %params );
	return undef
		unless defined $column_types;
	
	my @c_types = ();
	for my $col (keys %$column_types) {
		if($column_types->{$col}->{TYPE} eq 'timestamp_with_time_zone') {
			push @c_types, 'to_char(to_timestamp_tz("'.$col.'",'.$self->_db()->quote($self->{NLS_TIMESTAMP_TZ_FORMAT}).') at time zone '.$self->_db()->quote($self->{DEFAULT_TIME_ZONE}).','.$self->_db()->quote($params{SQL_DATE_FORMAT_STRING}).') "'.$col.'"';
		} else {
			push @c_types, '"'.$col.'"';
		}
	}
	my $sql = 'select ' . join( ',', @c_types ) . ' from ';
	if(defined $params{QUERY}) {
		$sql .= "( $params{QUERY} )";
	} else {
		my $table_or_view = $self->_get_table_or_view(%params);
		return undef
			unless defined $table_or_view;
		$sql .= $table_or_view;
	}
	
	$logger->debug("QUERY: ".$sql);
	
	return $sql;
}

sub _get_columns {
	
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->last_error($errmsg)
		and return undef
			unless $self->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
				}
				,OPTIONAL   => {
					 QUERY					=> { isa => 'SCALAR' }
					,DATA					=> { isa => 'ARRAY' }
					,TABLE_OR_VIEW_NAME		=> { isa => 'SCALAR', pattern => qr{^[^\.@]+$} }
					,TABLE_OR_VIEW_OWNER	=> { isa => 'SCALAR', pattern => qr{^[^\.@]+$} }
					,TABLE_OR_VIEW_DBLINK	=> { isa => 'SCALAR', pattern => qr{^[^\.@]+$} }
					,COLUMNS				=> { isa => 'ARRAY' }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	$self->last_error("One between params QUERY, TABLE_OR_VIEW_NAME or DATA must be defined!")
		and return undef
			unless defined $params{QUERY} or defined $params{TABLE_OR_VIEW_NAME} or defined $params{DATA};
	
	$self->last_error("Param COLUMNS must be defined if you use param DATA!")
		and return undef
			unless defined $params{QUERY} or defined $params{TABLE_OR_VIEW_NAME} or defined $params{DATA};
	
	$self->last_error("Param COLUMNS must be an array of at least 1 value!")
		and return undef
			if( exists $params{COLUMNS} && scalar(@{$params{COLUMNS}}) == 0 );
	
	my $logger = $self->_logger();
	
	my $position = 0;
	tie my %ct, 'Tie::IxHash', ();
	
	if(exists $params{COLUMNS}) {
		
		my $columns_with_position = 0;
		my $used_positions = {};
		
		for my $col (@{$params{COLUMNS}}) {
			$self->last_error("Param COLUMNS values must be HASHREF!")
				and return undef
					unless ref $col eq 'HASH';
			
			$self->last_error("Key NAME must be defined for param COLUMNS values!")
				and return undef
					unless defined $col->{NAME};
			
			$ct{$col->{NAME}} = {};
			
			$ct{$col->{NAME}}->{TYPE} = defined $col->{TYPE} ? $col->{TYPE} : "string";
			$ct{$col->{NAME}}->{HEADER} = $col->{HEADER} if defined $col->{HEADER};
			
			$self->last_error("If denfined param DATA key COLUMNS.PATH must be defined!")
				and return undef
					if(defined $params{DATA} && !defined $col->{PATH});
			$ct{$col->{NAME}}->{PATH} = $col->{PATH} if defined $col->{PATH};
			
			if(defined $col->{POSITION}) {
				$columns_with_position++;
				$self->last_error("Param COLUMNS.POSITION must be unique!")
					and return undef
						if exists $used_positions->{$col->{POSITION}};
				$used_positions->{$col->{POSITION}} = 1;
				$ct{$col->{NAME}}->{POSITION} = $col->{POSITION} ;
			} else {
				$ct{$col->{NAME}}->{POSITION} = ++$position;
			}
			
			$ct{$col->{NAME}}->{JSON_CHECKBOX_FILTER} = $col->{JSON_CHECKBOX_FILTER} if defined $col->{JSON_CHECKBOX_FILTER};
			$ct{$col->{NAME}}->{JSON_HIDDEN} = $col->{JSON_HIDDEN} if defined $col->{JSON_HIDDEN};
			$ct{$col->{NAME}}->{JSON_DESCRIPTION} = $col->{JSON_DESCRIPTION} if defined $col->{JSON_DESCRIPTION};
			$ct{$col->{NAME}}->{JSON_EXCLUDE} = $col->{JSON_EXCLUDE} if defined $col->{JSON_EXCLUDE};
			
			$ct{$col->{NAME}}->{XLSX_DATE_FORMAT} = $col->{XLSX_DATE_FORMAT} if defined $col->{XLSX_DATE_FORMAT};
			$ct{$col->{NAME}}->{XLSX_EXCLUDE} = $col->{XLSX_EXCLUDE} if defined $col->{XLSX_EXCLUDE};
			
			$ct{$col->{NAME}}->{CSV_EXCLUDE} = $col->{CSV_EXCLUDE} if defined $col->{CSV_EXCLUDE};
		}
		
		$self->last_error("If you use param COLUMNS.POSITION you must use it for every column!")
			and return undef
				if $columns_with_position && $columns_with_position != scalar@{$params{COLUMNS}}
		
	} elsif (defined $params{TABLE_OR_VIEW_NAME}) {
	
		my $dbh = $self->_db();
	
		my $table_or_view = $self->_get_table_or_view(%params);
		return undef
			unless defined $table_or_view;
		
		my $owner;
		my $dblink = '';
		if(defined $params{TABLE_OR_VIEW_DBLINK}) {
			my $tmp = $dbh->fetch_minimalized("select upper(username) from user_db_links where db_link = upper(".$dbh->quote($params{TABLE_OR_VIEW_DBLINK}).")");
			unless (defined $tmp) {
				$tmp = $dbh->fetch_minimalized("select upper(username) from all_db_links where owner = 'PUBLIC' and db_link = upper(".$dbh->quote($params{TABLE_OR_VIEW_DBLINK}).")");
				$self->last_error("DBLINK $params{TABLE_OR_VIEW_DBLINK} not found!")
					&& return undef
						unless defined $tmp;
			}
			$dblink = '@'.$params{TABLE_OR_VIEW_DBLINK};
			$owner = defined $params{TABLE_OR_VIEW_OWNER} ? $params{TABLE_OR_VIEW_OWNER} : $tmp;
		} else {
			$owner = defined $params{TABLE_OR_VIEW_OWNER} ? $params{TABLE_OR_VIEW_OWNER} : $dbh->fetch_minimalized("select sys_context('USERENV', 'SESSION_USER') from dual");
		}
		
		my $rows = $dbh->fetchall_hashref("
			select column_name,DECODE(data_type, 'VARCHAR2', 'string', 'CHAR', 'string', lower(data_type)) data_type --,table_name,column_id,data_length,data_precision,data_scale,char_length
			from all_tab_cols".$dblink."
			where 1=1
				and owner = upper(".$dbh->quote($owner).")
				and table_name = upper(".$dbh->quote($params{TABLE_OR_VIEW_NAME}).")
			order by column_id
		");
		
		$self->last_error("Table or view $table_or_view not found!")
			and return undef
				unless scalar @$rows;
		
		for my $r (@$rows) {
			$ct{$r->{COLUMN_NAME}} = {
				 TYPE => $r->{DATA_TYPE}
				,POSITION => ++$position
			}
		}
	
	} else {
		
		$self->{PREPARE}->{GET_COLUMNS}=$self->{DB}->create_prepare("
DECLARE
  insql VARCHAR2(32767) := :INSQL;
  cur SYS_REFCURSOR := :CUR;
  refcur SYS_REFCURSOR;
  h   NUMBER;
  cnt NUMBER;
  ret dbms_sql.desc_tab;
  q VARCHAR2(32767);
  FUNCTION get_query_desc_tab(
      desctab IN sys.dbms_sql.desc_tab)
    RETURN VARCHAR2
  IS
    q     VARCHAR2(32767);
    dtype VARCHAR2(30);
  BEGIN
    q := 'select col_name,col_type from (';
    FOR i IN 1 .. desctab.count
    LOOP
      -- type  1: VARCHAR2
      -- type  2: NUMBER
      -- type 12: DATE
      -- type 96: CHAR
      -- type 181: TIMESTAMP WITH TIME ZONE
      SELECT DECODE(TO_CHAR(desctab(i).col_type), 1, 'string', 2, 'number', 12, 'date', 96, 'string', 181, 'timestamp_with_time_zone', 'NOT_SUPPORTED')
      INTO dtype
      FROM dual;
      -- dbms_output.put_line('COL_NUMBER: '|| i ||', COL_TYPE: ' || dtype || ', COL_NAME: ' || desctab(i).col_name);
      IF i <> 1 THEN
        q  := q || 'union ';
      END IF;
      q := q || 'select '||i||' num,'''|| desctab(i).col_name ||''' col_name,'''||dtype|| ''' col_type from dual ';
    END LOOP;
    q := q || ') order by num';
    RETURN q;
  END get_query_desc_tab;
BEGIN
  OPEN refcur FOR insql;
  h := dbms_sql.to_cursor_number(refcur);
  dbms_sql.describe_columns(h, cnt, ret);
  q := get_query_desc_tab(ret);
  -- dbms_output.put_line('Query: ' || q);
  dbms_sql.close_cursor(h);
  OPEN cur FOR q;
END;
		") unless defined $self->{PREPARE}->{GET_COLUMNS};
	
		my $sth=$self->{PREPARE}->{GET_COLUMNS}->get_sth;
		$sth->bind_param(':INSQL' => $params{QUERY});
		$sth->bind_param_inout(':CUR',\my $cur,0, {ora_type => ORA_RSET });
		eval{$sth->execute()};
		$self->last_error($@)
				&& return undef
						if $@;
		
		while (my $r = $cur->fetchrow_hashref()) {
			$ct{$r->{COL_NAME}} = {
				 TYPE => $r->{COL_TYPE}
				,POSITION => ++$position
			};
		};
		$self->last_error("Unable to fetch GET_COLUMNS cursor: " . SIRTI::DB::get_errorcode . " - " . SIRTI::DB::get_errormessage)
			&& return undef
				if SIRTI::DB::iserror();
		
		$sth->finish();
		
	}
	
	my @ordered_col_names = sort { $ct{$a}->{POSITION} <=> $ct{$b}->{POSITION} } keys %ct;
	
	tie my %ordered_ct, 'Tie::IxHash', ();
	for my $col_name (@ordered_col_names) {
		$ordered_ct{$col_name} = $ct{$col_name};
	}
	
	return \%ordered_ct;
	
}

sub _transform_data_row {
	my $self = shift;
	my $row = shift;
	my $column_types = shift;
	
	my $logger = $self->_logger();
	
	$logger->trace("Row da trasformare: ".Dumper($row));
	$logger->trace("column_types: ".Dumper($column_types));
	
	my $ret = {};
	
	for my $k (keys %$column_types) {
		my $path = $column_types->{$k}->{PATH};
		
		$path =~ s/^\[//;
		$path =~ s/\]$//;
		
		my @parts = split(/\]\[/, $path);
		
		my $transformed = '';
		
		for my $p (@parts){
			if ($p =~ /^\d+$/){
				$transformed.='->['.$p.']';
			} else {
				if ($p =~ /^'.*'$/ || $p =~ /^".*"$/){
					$transformed.='->{'.$p.'}';
				} else {
					$self->last_error("Bad path ".$column_types->{$k}->{PATH});
					return undef;
				}
			}
		}
		
		$logger->trace("eval: ".'$row'.$transformed);
		$ret->{$k} = eval '$row'.$transformed;
	}
	
	return $ret;
}

sub _get_data_from_row {
	my $self = shift;
	my $row = shift;
	my $column_types = shift;
	
	my $logger = $self->_logger();
	
	my @cols = ();
	
	for my $k (keys %$column_types) {
		# se la colonna e' da escludere passo all'elemento successivo
		next if $column_types->{$k}->{JSON_EXCLUDE};
		
		if($column_types->{$k}->{TYPE} eq 'string') {
			push @cols, $row->{$k};
		} elsif ($column_types->{$k}->{TYPE} eq 'date' || $column_types->{$k}->{TYPE} eq 'timestamp_with_time_zone') {
			push @cols, defined $row->{$k} ? $self->_format_iso_date($row->{$k}) : undef;
		} elsif ($column_types->{$k}->{TYPE} eq 'number') {
			if(defined $row->{$k}) {
				$row->{$k} =~ s/,/./g;
				# FIXME: effettuo il casting a number solo su numeri interi. Si è verificato che nell'API::ART::REST
				# la seconda volta che viene fatto questo casting la trasformazione in json sostituisce il punto con
				# virgola: problemi di locale?
				$row->{$k} *= 1
					unless $row->{$k} =~ /\./;
			}
			push @cols, $row->{$k};
		} else {
			# custom type: trattato come string
			push @cols, $row->{$k};
		}
	}
	return \@cols;
}

sub _create_xlsx {
	
	my $self = shift;
	my $sth  = shift;
	my %params = @_;
	
	my $errmsg;
	$self->last_error($errmsg)
		and return undef
			unless $self->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
				}
				,OPTIONAL   => {
					 XLSX_SHEET_NAME				=> { isa => 'SCALAR' }
					,XLSX_FONT						=> { isa => 'SCALAR' }
					,XLSX_STRING_HORIZONTAL_ALIGN	=> { isa => 'SCALAR' }
					,XLSX_NUMBER_HORIZONTAL_ALIGN	=> { isa => 'SCALAR' }
					,XLSX_DATE_HORIZONTAL_ALIGN		=> { isa => 'SCALAR' }
					,DIR							=> { isa => 'SCALAR' }
					,DATA							=> { isa => 'ARRAY' }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	$self->last_error("Undefined statement!")
		and return undef
			if !defined $params{DATA} and !defined $sth;
	
	my $logger = $self->_logger();
	
	my %file_tmp_params = (UNLINK => 0, SUFFIX => '.xlsx');
	$file_tmp_params{DIR} = $params{DIR} if exists $params{DIR};
	
	my $file_tmp = File::Temp->new(%file_tmp_params);
	
	my $workbook = Excel::Writer::XLSX->new($file_tmp);
	
	$self->last_error('Unable to create excel file '.$file_tmp->filename.': '.$!)
		and return undef
			unless defined $workbook;
	
	my $sheet_name = defined $params{XLSX_SHEET_NAME} ? $params{XLSX_SHEET_NAME} : "Report";
	
	my $worksheet = $workbook->add_worksheet($sheet_name);
	$worksheet->set_landscape();
	$worksheet->center_horizontally();
	$worksheet->set_margins_LR(0.2);
	
	$worksheet->set_header($sheet_name);
	$worksheet->set_footer('&CPage &P of &N');
	
	my $column_types = $self->_get_columns( %params );
	return undef
		unless defined $column_types;
	$logger->trace("Column Types: " . Dumper $column_types);
	
	my %formats = ();
	
	$formats{intestazione} = $workbook->add_format();
	$formats{intestazione}->set_font($params{XLSX_FONT}) if defined $params{XLSX_FONT};
	$formats{intestazione}->set_bold();
	$formats{intestazione}->set_color('black');
	$formats{intestazione}->set_align('center');
	$formats{intestazione}->set_bg_color('#BFBFBF');
	$formats{intestazione}->set_size(9);
	$formats{intestazione}->set_border(1);
	
	$formats{cella_generica} = $workbook->add_format();
	$formats{cella_generica}->set_font($params{XLSX_FONT}) if defined $params{XLSX_FONT};
	$formats{cella_generica}->set_text_wrap();
	$formats{cella_generica}->set_align($params{XLSX_STRING_HORIZONTAL_ALIGN}) if defined $params{XLSX_STRING_HORIZONTAL_ALIGN};
	$formats{cella_generica}->set_align('top');
	$formats{cella_generica}->set_size(8);
	$formats{cella_generica}->set_border(1);
	
	$formats{cella_numero} = $workbook->add_format();
	$formats{cella_numero}->set_font($params{XLSX_FONT}) if defined $params{XLSX_FONT};
	$formats{cella_numero}->set_text_wrap();
	$formats{cella_numero}->set_align($params{XLSX_NUMBER_HORIZONTAL_ALIGN}) if defined $params{XLSX_NUMBER_HORIZONTAL_ALIGN};
	$formats{cella_numero}->set_align('top');
	$formats{cella_numero}->set_size(8);
	$formats{cella_numero}->set_border(1);
	
	$formats{cella_data} = $workbook->add_format();
	$formats{cella_data}->set_font($params{XLSX_FONT}) if defined $params{XLSX_FONT};
	$formats{cella_data}->set_text_wrap();
	$formats{cella_data}->set_align($params{XLSX_DATE_HORIZONTAL_ALIGN}) if defined $params{XLSX_DATE_HORIZONTAL_ALIGN};
	$formats{cella_data}->set_align('top');
	$formats{cella_data}->set_size(8);
	$formats{cella_data}->set_border(1);
	$formats{cella_data}->set_num_format($params{XLSX_DATE_FORMAT});
	
	my $c = 0;
	my $r = 0;
	for my $k (keys %$column_types) {
		# se la colonna e' da escludere passo all'elemento successivo
		next if $column_types->{$k}->{XLSX_EXCLUDE};
		$worksheet->write_string( $r, $c++, defined $column_types->{$k}->{HEADER} ? $column_types->{$k}->{HEADER} : $k, $formats{intestazione} );
		
		if($column_types->{$k}->{TYPE} eq 'date' || $column_types->{$k}->{TYPE} eq 'timestamp_with_time_zone') {
			if(defined $column_types->{$k}->{XLSX_DATE_FORMAT}) {
				$formats{"cella_data_".$k} = $workbook->add_format();
				$formats{"cella_data_".$k}->set_font($params{XLSX_FONT}) if defined $params{XLSX_FONT};
				$formats{"cella_data_".$k}->set_text_wrap();
				$formats{"cella_data_".$k}->set_align($params{XLSX_DATE_HORIZONTAL_ALIGN}) if defined $params{XLSX_DATE_HORIZONTAL_ALIGN};
				$formats{"cella_data_".$k}->set_align('top');
				$formats{"cella_data_".$k}->set_size(8);
				$formats{"cella_data_".$k}->set_border(1);
				$formats{"cella_data_".$k}->set_num_format($column_types->{$k}->{XLSX_DATE_FORMAT});
			}
		}
	}
	
	if(defined $params{DATA}) {
		for my $row_tmp (@{$params{DATA}}) {
			my $row = $self->_transform_data_row($row_tmp, $column_types);
			return undef
				unless defined $row;
			$r++;
			my $c = 0;
			for my $k (keys %$column_types) {
				# se la colonna e' da escludere passo all'elemento successivo
				next if $column_types->{$k}->{XLSX_EXCLUDE};
				if($column_types->{$k}->{TYPE} eq 'string') {
					$worksheet->write_string( $r, $c++, defined $row->{$k} ? $row->{$k} : "", $formats{cella_generica} );
				} elsif ($column_types->{$k}->{TYPE} eq 'date' || $column_types->{$k}->{TYPE} eq 'timestamp_with_time_zone') {
					$row->{$k} = $self->_db()->fetch_minimalized("select to_char(to_timestamp_tz(".$self->_db()->quote($row->{$k}).",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm') at time zone 'Europe/Rome','YYYY-MM-DD\"T\"hh24:mi:ss') from dual");
					my $format = $formats{cella_data};
					if(defined $column_types->{$k}->{XLSX_DATE_FORMAT}) {
						$format = $formats{"cella_data_".$k};
					}
					$worksheet->write_date_time( $r, $c++, defined $row->{$k} ? $row->{$k} : "", $format );
				} elsif ($column_types->{$k}->{TYPE} eq 'number') {
					if (defined $row->{$k}) {
						$row->{$k} =~ s/,/./g;
						$worksheet->write_number( $r, $c++, $row->{$k}, $formats{cella_numero} );
					} else {
						$worksheet->write_string( $r, $c++, "", $formats{cella_numero} );
					}
				} elsif ($column_types->{$k}->{TYPE} eq 'boolean') {
					if (defined $row->{$k}) {
						$worksheet->write_string( $r, $c++, $row->{$k} ? 1 : 0, $formats{cella_generica} );
					} else {
						$worksheet->write_string( $r, $c++, "", $formats{cella_generica} );
					}
				} else {
					# custom type: trattato come string
					$worksheet->write_string( $r, $c++, defined $row->{$k} ? $row->{$k} : "", $formats{cella_generica} );
				}
			}
		}
	} else {
		while( my $row = $sth->fetchrow_hashref()) {
			$r++;
			my $c = 0;
			for my $k (keys %$column_types) {
				# se la colonna e' da escludere passo all'elemento successivo
				next if $column_types->{$k}->{XLSX_EXCLUDE};
				if($column_types->{$k}->{TYPE} eq 'string') {
					$worksheet->write_string( $r, $c++, defined $row->{$k} ? $row->{$k} : "", $formats{cella_generica} );
				} elsif ($column_types->{$k}->{TYPE} eq 'date' || $column_types->{$k}->{TYPE} eq 'timestamp_with_time_zone') {
					my $format = $formats{cella_data};
					if(defined $column_types->{$k}->{XLSX_DATE_FORMAT}) {
						$format = $formats{"cella_data_".$k};
					}
					$worksheet->write_date_time( $r, $c++, defined $row->{$k} ? $row->{$k} : "", $format );
				} elsif ($column_types->{$k}->{TYPE} eq 'number') {
					if (defined $row->{$k}) {
						$row->{$k} =~ s/,/./g;
						$worksheet->write_number( $r, $c++, $row->{$k}, $formats{cella_numero} );
					} else {
						$worksheet->write_string( $r, $c++, "", $formats{cella_numero} );
					}
				} else {
					# custom type: trattato come string
					$worksheet->write_string( $r, $c++, defined $row->{$k} ? $row->{$k} : "", $formats{cella_generica} );
				}
			}
		}
		$self->last_error("Unable to fetch _create_xlsx cursor: " . SIRTI::DB::get_errorcode . " - " . SIRTI::DB::get_errormessage)
			&& return undef
				if SIRTI::DB::iserror();
	}
	
	$workbook->close();
	
	if ($params{EXTENDED_REPORT}){
		my $ret = {};
		$ret->{count} = $r if $params{EXTENDED_REPORT}->{COUNT};
		$ret->{result} = $file_tmp->filename if $params{EXTENDED_REPORT}->{RESULT};
		return $ret;
	} else {
		return $file_tmp->filename;
	}
}

sub _create_json {
	
	my $self = shift;
	my $sth  = shift;
	my %params = @_;
	
	my $errmsg;
	$self->last_error($errmsg)
		and return undef
			unless $self->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 ENCODING						=> { isa => 'SCALAR' }
				}
				,OPTIONAL   => {
					 DIR									=> { isa => 'SCALAR' }
					,DATA									=> { isa => 'ARRAY' }
					,JSON_RETURN_STRING						=> { isa => 'SCALAR', list => [ 0, 1 ] }
					,JSON_DATA_AS_ARRAY						=> { isa => 'SCALAR', list => [ 0, 1 ] }
					,JSON_USE_ID_INSTEAD_OF_NAME			=> { isa => 'SCALAR', list => [ 0, 1 ] }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	$self->last_error("Undefined statement!")
		and return undef
			if !defined $params{DATA} and !defined $sth;
	
	$params{JSON_RETURN_STRING} = defined $params{JSON_RETURN_STRING} ? $params{JSON_RETURN_STRING} : 0;
	$params{JSON_DATA_AS_ARRAY} = defined $params{JSON_DATA_AS_ARRAY} ? $params{JSON_DATA_AS_ARRAY} : 0;
	$params{JSON_USE_ID_INSTEAD_OF_NAME} = defined $params{JSON_USE_ID_INSTEAD_OF_NAME} ? $params{JSON_USE_ID_INSTEAD_OF_NAME} : 0;
	
	my $logger = $self->_logger();
	
	my $out = {
		 header => []
		,data => []
	};
	
	my $column_types = $self->_get_columns( %params );
	return undef
		unless defined $column_types;
	$logger->trace("Column Types: " . Dumper $column_types);
	
	for my $k (keys %$column_types) {
		
		# se la colonna e' da escludere passo all'elemento successivo
		next if $column_types->{$k}->{JSON_EXCLUDE};
		
		my $tmp = {
			  name => defined $column_types->{$k}->{HEADER} ? $column_types->{$k}->{HEADER} : $k
			 ,type => $column_types->{$k}->{TYPE} eq 'timestamp_with_time_zone' ? 'date' : $column_types->{$k}->{TYPE}
			 ,hidden => $column_types->{$k}->{JSON_HIDDEN} ? $JSON::true : $JSON::false
			 ,checkboxFilter => $column_types->{$k}->{JSON_CHECKBOX_FILTER} ? $JSON::true : $JSON::false
		};
		$tmp->{path} = $column_types->{$k}->{PATH}
			if defined $column_types->{$k}->{PATH};
		$tmp->{position} = $column_types->{$k}->{POSITION}*1
			if defined $column_types->{$k}->{POSITION};
		$tmp->{description} = $column_types->{$k}->{JSON_DESCRIPTION}
			if defined $column_types->{$k}->{JSON_DESCRIPTION};
		$tmp->{id} = $k
			if $params{JSON_USE_ID_INSTEAD_OF_NAME};
		push @{$out->{header}}, $tmp;
		
	}
	
	if(defined $params{DATA}) {
		for my $row (@{$params{DATA}}) {
			my $data_row = $self->_transform_data_row($row, $column_types);
			return undef
				unless defined $data_row;
			my $cols = $self->_get_data_from_row($data_row, $column_types);
			return undef
				unless defined $cols;
			push @{$out->{data}}, $cols;
		}
	} else {
		while( my $row = $sth->fetchrow_hashref()) {
			my $cols = $self->_get_data_from_row($row, $column_types);
			return undef
				unless defined $cols;
			push @{$out->{data}}, $cols;
		}
		$self->last_error("Unable to fetch _create_json cursor: " . SIRTI::DB::get_errorcode . " - " . SIRTI::DB::get_errormessage)
			&& return undef
				if SIRTI::DB::iserror();
	}
	
	unless($params{JSON_DATA_AS_ARRAY}) {	
		$out->{data2} = [];
		for my $d (@{$out->{data}}) {
			my $obj = {};
			for(my $i=0; $i<@{$out->{header}}; $i++) {
				if($params{JSON_USE_ID_INSTEAD_OF_NAME}) {
					$obj->{$out->{header}->[$i]->{id}} = $d->[$i];
				} else {
					$obj->{$out->{header}->[$i]->{name}} = $d->[$i];
				}
			}
			push @{$out->{data2}}, $obj;
		}
		$out->{data} = $out->{data2};
		delete $out->{data2};
	}
	
	# se mi è stato passato un array DATA inserisco anche la chiave raw con l'array di oggetti che ho avuto in input
	$out->{raw} = $params{DATA} if defined $params{DATA};
	
	if($params{JSON_RETURN_STRING}) {
		
		if ($params{EXTENDED_REPORT}){
			my $ret = {};
			$ret->{count} = scalar @{$out->{data}} if $params{EXTENDED_REPORT}->{COUNT};
			$ret->{result} = to_json $out if $params{EXTENDED_REPORT}->{RESULT};
			return $ret;
		} else {
			return to_json $out;
		}
		
	} else {
		my %file_tmp_params = (UNLINK => 0, SUFFIX => '.json');
		$file_tmp_params{DIR} = $params{DIR} if exists $params{DIR};
		
		my $file_tmp = File::Temp->new(%file_tmp_params);
		
		$self->last_error($!)
			&& return undef
				unless binmode $file_tmp, ':'.$params{ENCODING};
		
		print $file_tmp to_json $out;
		
		if ($params{EXTENDED_REPORT}){
			my $ret = {};
			$ret->{count} = scalar @{$out->{data}} if $params{EXTENDED_REPORT}->{COUNT};
			$ret->{result} = $file_tmp->filename if $params{EXTENDED_REPORT}->{RESULT};
			return $ret;
		} else {
			return $file_tmp->filename;
		}
		
	}
}

sub _write_csv_output_line {
	
	my ($self, $OUTF, $csv, $outStuff) = @_;
	my $res = 1;
	
	if( ref($outStuff) eq 'ARRAY' ) {
		my $v = [ map { defined $_ ? $_ :  '' } @$outStuff ];
		$res = eval { $csv->print( $OUTF, $v ) };
		$self->last_error($@)
			&& return undef
				if $@;
	}
	else {
		$res = print $OUTF $outStuff  if defined $outStuff;
	}
	
	$res = print $OUTF "\n" if $res;
	$self->last_error($!)
		&& return undef
			unless $res;
	
	return $res;
	
}

sub _create_csv {
	
	my $self = shift;
	my $sth  = shift;
	my %params = @_;
	
	my $errmsg;
	$self->last_error($errmsg)
		and return undef
			unless $self->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 ENCODING						=> { isa => 'SCALAR' }
				}
				,OPTIONAL   => {
					 DIR							=> { isa => 'SCALAR' }
					,CSV_SEP_CHAR					=> { isa => 'SCALAR' }
					,CSV_QUOTE_CHAR					=> { isa => 'SCALAR' }
					,CSV_ALWAYS_QUOTED				=> { isa => 'SCALAR', list => [0,1] }
					,CSV_NO_HEADER					=> { isa => 'SCALAR', list => [0,1] }
					,CSV_MAX_ROWS					=> { isa => 'SCALAR', pattern => qr{^\d+$} }
					,DATA							=> { isa => 'ARRAY' }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	$self->last_error("Undefined statement!")
		and return undef
			if !defined $params{DATA} and !defined $sth;
	
	$params{CSV_NO_HEADER} = 0
		unless defined $params{CSV_NO_HEADER};
	
	my $logger = $self->_logger();
	
	my %file_tmp_params = (UNLINK => 0, SUFFIX => '.csv');
	$file_tmp_params{DIR} = $params{DIR} if exists $params{DIR};
	
	my $file_tmp = File::Temp->new(%file_tmp_params);
	
	my %opts = ( );
	
	$opts{sep_char} = defined $params{'CSV_SEP_CHAR'} ? $params{'CSV_SEP_CHAR'} : ';';
	$opts{quote_char} = defined $params{'CSV_QUOTE_CHAR'} ? $params{'CSV_QUOTE_CHAR'} : '"';
	$opts{always_quote} = defined $params{'CSV_ALWAYS_QUOTED'} ? $params{'CSV_ALWAYS_QUOTED'} : 1;
	
	$opts{encoding_out} = $params{ENCODING};
	
	my $csv = Text::CSV::Encoded->new( \%opts );
	
	my $column_types = $self->_get_columns( %params );
	return undef
		unless defined $column_types;
	$logger->trace("Column Types: " . Dumper $column_types);
	
	unless($params{CSV_NO_HEADER}) {
		my @header = ();
		for (keys %$column_types){
			# se la colonna e' da escludere non la inserisco
			push @header, (defined $column_types->{$_}->{HEADER} ? $column_types->{$_}->{HEADER} : $_ )
				unless $column_types->{$_}->{CSV_EXCLUDE};
		}
		return undef
			unless $self->_write_csv_output_line( $file_tmp, $csv, \@header );
	}
	
	my $row_count = 0;
	if(defined $params{DATA}) {
		
		my $nls_date_format = $self->_db()->fetch_minimalized("SELECT value FROM nls_session_parameters WHERE parameter = 'NLS_DATE_FORMAT'");
		for my $row_tmp (@{$params{DATA}}) {
			$row_count++;
			last if defined $params{CSV_MAX_ROWS} && $row_count > $params{CSV_MAX_ROWS};
			
			my $row = $self->_transform_data_row($row_tmp, $column_types);
			return undef
				unless defined $row;
			
			my @tmp = ();
			
			for my $k (keys %$column_types){
				if ($column_types->{$k}->{TYPE} eq 'date' || $column_types->{$k}->{TYPE} eq 'timestamp_with_time_zone') {
					$row->{$k} = $self->_db()->fetch_minimalized("select to_char(to_timestamp_tz(".$self->_db()->quote($row->{$k}).",'YYYY-MM-DD\"T\"hh24:mi:ss.fftzh:tzm') at time zone 'Europe/Rome',".$self->_db()->quote($nls_date_format).") from dual");
				} elsif ($column_types->{$k}->{TYPE} eq 'boolean') {
					if (defined $row->{$k}) {
						$row->{$k} = $row->{$k} ? 1 : 0;
					}
				}  elsif ($column_types->{$k}->{TYPE} eq 'number') {
					if (defined $row->{$k}) {
						$row->{$k} =~ s/,/./g;
						$row->{$k} = $self->_db()->fetch_minimalized("select to_number(".$self->_db()->quote($row->{$k}).", '9999999999D9999', 'NLS_NUMERIC_CHARACTERS=''.,''') from dual");
					}
					
				}
				# se la colonna e' da escludere non la inserisco
				push @tmp, $row->{$k} unless $column_types->{$k}->{CSV_EXCLUDE};
			}
			
			return undef
				unless $self->_write_csv_output_line( $file_tmp, $csv, \@tmp );
		}
	} else {
		while( my $row = $sth->fetchrow_hashref()) {
			$row_count++;
			last if defined $params{CSV_MAX_ROWS} && $row_count > $params{CSV_MAX_ROWS};
			
			my @tmp = ();
			
			for my $k (keys %$column_types){
				# se la colonna e' da escludere non la inserisco
				push @tmp, $row->{$k} unless $column_types->{$k}->{CSV_EXCLUDE};
			}
			
			return undef
				unless $self->_write_csv_output_line( $file_tmp, $csv, \@tmp );
		}
	}
	
	if ($params{EXTENDED_REPORT}){
		my $ret = {};
		$ret->{count} = $row_count if $params{EXTENDED_REPORT}->{COUNT};
		$ret->{result} = $file_tmp->filename if $params{EXTENDED_REPORT}->{RESULT};
		return $ret;
	} else {
		return $file_tmp->filename;
	}
	
}

sub _format_iso_date {
	my ($self, $date) = @_;
	
	my $dt = eval{DateTime::Format::ISO8601->parse_datetime( $date )};
	$self->last_error("Not a valid ISO Date: ".$@)
		&& return undef
			if ($@);
	
	my $offset = $dt->set_time_zone($self->{DEFAULT_TIME_ZONE})->offset(); # 7200
	my ($hour, $min) = (int($offset/(60*60)), int($offset%(60*60)));
	
	return $dt->set_time_zone($self->{DEFAULT_TIME_ZONE})->set_formatter($self->{DEFAULT_ISO_DATE_FORMAT}).sprintf("%+03d:%02d", $hour, $min);
}

=head2 report( %params )

Funzione da invocare per generare il report.

Per default ritorna un nome file dove è stato memorizzato il report, a meno che vengano settati i parametri B<C<EXTENDED_REPORT>> o C<B<JSON_RETURN_STRING>>.
In caso di errore ritorna C<undef> e setta C<last_error>

=over

=item B<I<Global parameters>>

=item * B<C<OUTPUT_FORMAT>> <C<json|xlsx|csv>>

Obbligatorio, formato del report

=item * B<C<I<EXTENDED_REPORT>>> <C<HASHREF>>

Se definito non viene restituito il nome file che contiene il report ma un C<HASHREF> le cui chiavi dipendono dai valori impostati per le seguenti chiavi del parametro:

=over

=item * B<C<I<COUNT>>> <C<0|1>>

Se I<true> verrà valorizzata la chiave C<B<count>> con il numero di entry trovate

=item * B<C<I<RESULT>>> <C<0|1>>

Se I<true> verrà valorizzata la chiave C<B<result>> secondo il seguente criterio:

=over

=item 1. se il formato di output è Excel o CSV allora contiene il nome file che contiene il report

=item 2. se il formato di output è JSON allora contiene il nome file che contiene il report a meno che sia impostata I<true> la chiave C<B<JSON_RETURN_STRING>>, nel qual caso
contiene la stringa JSON che rappresenta il report

=back

=back

=item * C<B<I<ENCODING>>> <C<SCALAR>>

Encoding del file di output, applicabile ai report in formato I<json> e I<csv>; default C<utf8>

=item * C<B<I<DIR>>> <C<SCALAR>>

Directory dove salvare il file contentente il report; default C<$TMPDIR> o C</tmp>

=back

=over

=item B<I<Data source parameters>>

I seguenti parametri sono tutti opzionali, ma è obbligatorio che uno tra C<B<QUERY>> e C<B<TABLE_OR_VIEW_NAME>> sia valorizzato

=back

=over

=item * C<B<I<QUERY>>> <C<SCALAR>>

Query da eseguire per reperire i dati

=item * C<B<I<TABLE_OR_VIEW_NAME>>> <C<SCALAR>>

Nome dell'oggetto Oracle da cui reprire i dati

=item * C<B<I<TABLE_OR_VIEW_OWNER>>> <C<SCALAR>>

Schema in cui è presente l'oggetto Oracle da cui reperire i dati, utilizzato solo se il parametro C<B<TABLE_OR_VIEW_NAME>> è valorizzato

=item * C<B<I<TABLE_OR_VIEW_DBLINK>>> <C<SCALAR>>

Database link da utilizzare per riferire l'oggetto Oracle da cui reperire i dati, utilizzato solo se il parametro C<B<TABLE_OR_VIEW_NAME>> è valorizzato

=back

=over

=item B<I<JSON specific parameters>>

=back

=over

=item * C<B<I<JSON_RETURN_STRING>>> <C<0|1>>

Anziché restituire il nome file del report, restituisce la stringa JSON che rappresenta il report; default C<0>

=item * C<B<I<JSON_USE_ID_INSTEAD_OF_NAME>>> <C<0|1>>

Se I<true> nella chiave C<B<header>> del JSON viene inserito il campo C<B<id>> e negli oggetti che rappresentano i dati (oggetti della sezione C<B<data>>) la chiave è il valore di C<B<id>> (e non di C<B<name>> come per default), vedi parametro C<B<COLUMNS>>; default C<0>

=item * C<B<I<JSON_DATA_AS_ARRAY>>> <C<0|1>>

Anziché restituire i dati del report come array di oggetti, li restituisce come array di array; default C<0>

=back

=over

=item B<I<CSV specific parameters>>

=back

=over

=item * C<B<I<SQL_DATE_FORMAT_STRING>>> <C<SCALAR>>

Formato Oracle per la conversione delle colonne di tipo I<DATE>; se non valorizzato viene utilizzato il formato impostato dalle variabili C<NLS_> di connessione

=item * C<B<I<CSV_SEP_CHAR>>> <C<SCALAR>>

Imposta il carattere separatore di colonne; default C<;>

=item * C<B<I<CSV_QUOTE_CHAR>>> <C<SCALAR>>

Imposta il carattere utilzzato per quotare le stringhe vuote; default C<">

=item * C<B<I<CSV_ALWAYS_QUOTED>>> <C<0|1>>

Quota le stringhe anche se non vuote; default C<1>

=item * C<B<I<CSV_NO_HEADER>>> <C<0|1>>

Non inserisce i nomi dei campi come prima riga del report; default C<0>

=item * C<B<I<CSV_MAX_ROWS>>> <C<SCALAR>>

Numero massimo di righe da inserire nel report; default illimitato

=back

=over

=item B<I<Excel specific parameters>>

=back

=over

=item * C<B<I<XLSX_DATE_FORMAT>>> <C<SCALAR>>

Formato in cui vengono visualizzate le date in Excel; default C<dd/mm/yyyy hh:mm:ss>

=item * C<B<I<XLSX_SHEET_NAME>>> <C<SCALAR>>

Nome dello sheet Excel contenente il report; default C<Report>

=item * C<B<I<XLSX_FONT>>> <C<SCALAR>>

Font utilizzato per i caratteri del report; default C<Calibri>

=item * C<B<I<XLSX_STRING_HORIZONTAL_ALIGN>>> <C<left|center|right|fill|justify|center_across>>

Allinemento orizzontale delle stringhe nella cella; default C<left>

=item * C<B<I<XLSX_NUMBER_HORIZONTAL_ALIGN>>> <C<left|center|right|fill|justify|center_across>>

Allinemento orizzontale dei numeri nella cella; default C<left>

=item * C<B<I<XLSX_DATE_HORIZONTAL_ALIGN>>> <C<left|center|right|fill|justify|center_across>>

Allinemento orizzontale delle date nella cella; default C<left>

=back

=over

=item B<I<Columns definition>>

=back

=over

=item * C<B<I<COLUMNS>>> <C<ARRAYREF>>

Caratterizza le colonne del report con un C<ARRAYREF> di C<HASHREF> così specificati (se non presente le caratteristiche delle colonne verranno ricavate dalla query):

=over

=item * C<B<NAME>> <C<SCALAR>>

Obbligatorio, identificativo della colonna come ritornato dalla query; nei report Excel o CSV viene utilizzato come intestazione di colonna, nel report JSON valorizza la chiave C<name> nella sezione C<B<header>>
e le chiavi degli oggetti della sezione C<B<data>> (a meno che sia impostato il parametro globale C<B<JSON_USE_ID_INSTEAD_OF_NAME>>)

=item * C<B<I<TYPE>>> <C<string|number|date|timestamp_with_time_zone>>

Tipo di dato contenuto nella colonna; default C<string>

=item * C<B<I<HEADER>>> <C<SCALAR>>

Se presente, nei report Excel o CSV viene utilizzato come intestazione di colonna, nel report JSON sovrascrive la chiave C<B<name>> nella sezione C<B<header>> e le chiavi degli
oggetti della sezione C<B<data>> (a meno che sia impostato il parametro globale C<B<JSON_USE_ID_INSTEAD_OF_NAME>>)

=item * C<B<I<JSON_DESCRIPTION>>> <C<SCALAR>>

Se presente nell'header JSON viene utilizzato per valorizzare la chiave C<B<description>> che rappresenta una descrizione estesa dei dati contenuti nella colonna

=item * C<B<I<JSON_HIDDEN>>> <C<0|1>>

Imposta a C<false> o C<true> la chiave booleana C<B<hidden>> nell'header del JSON per indicare che tale colonna non deve essere mostrata; default C<0>

=item * C<B<I<JSON_EXCLUDE>>> <C<0|1>>

Esclude la specifica colonna dal report JSON; default C<0>

=item * C<B<I<JSON_CHECKBOX_FILTER>>> <C<0|1>> - B<I<DEPRECATED>>

Imposta a C<false> o C<true> la chiave booleana C<B<checkboxFilter>> nell'header del JSON per indicare che tale colonna  contiene un set finito di valori; default C<0>

=item * C<B<I<XLSX_EXCLUDE>>> <C<0|1>>

Esclude la specifica colonna dal report Excel; default C<0>

=item * C<B<I<XLSX_DATE_FORMAT>>> <C<SCALAR>>

Formato in cui vengono visualizzate le date in Excel nella specifica colonna: sovrascrive il parametro globale C<B<XLSX_DATE_FORMAT>>

=item * C<B<I<CSV_EXCLUDE>>> <C<0|1>>

Esclude la specifica colonna dal report CSV; default C<0>

=back

=back

=cut
sub report {
	
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->last_error($errmsg)
		and return undef
			unless $self->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					OUTPUT_FORMAT => { isa => 'SCALAR', list => ['xlsx','csv','json'] }
				}
				,OPTIONAL   => {
					 SQL_DATE_FORMAT_STRING         => { isa => 'SCALAR' }
					,XLSX_DATE_FORMAT				=> { isa => 'SCALAR' }
					,DIR							=> { isa => 'SCALAR' }
					,ENCODING						=> { isa => 'SCALAR' }
					,EXTENDED_REPORT				=> { isa => 'HASH' }
					,DATA							=> { isa => 'ARRAY' }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	$self->last_error("Param EXTENDED_REPORT cannot be used with param DATA")
		and return undef
			if defined $params{EXTENDED_REPORT} && defined $params{DATA};
	
	if (defined $params{EXTENDED_REPORT}){
		
		$self->last_error("At least one key must be defined for EXTENDED_REPORT")
			and return undef
				unless scalar keys %{$params{EXTENDED_REPORT}};
		
		$self->last_error($errmsg)
			and return undef
				unless $self->check_named_params(
					 ERRMSG     => \$errmsg
					,PARAMS     => \%{$params{EXTENDED_REPORT}}
					,MANDATORY => {}
					,OPTIONAL  => {
						COUNT => { isa => 'SCALAR', list => [0,1] },
						RESULT => { isa => 'SCALAR', list => [0,1] }
					}
					,IGNORE_EXTRA_PARAMS => 0
		);
	}
	
	my $dbh = $self->_db();
	my $logger = $self->_logger();
	
	if(exists $params{DIR}) {
		$self->last_error("Param DIR is not a directory")
			and return undef
				unless -d $params{DIR};
		$self->last_error("Param DIR is not writable")
			and return undef
				unless -w $params{DIR};
	}	
	
	$params{ENCODING} = defined $params{ENCODING} ? $params{ENCODING} : "utf8";
	
	if($params{OUTPUT_FORMAT} =~ /^(xlsx|json)$/) {
	
		# formato data in input per Excel::Writer::XLSX, si cambia la visualizzazione con set_num_format
		# utilizzato anche per json
		$params{SQL_DATE_FORMAT_STRING} = 'YYYY-MM-DD"T"HH24:MI:SS';
		
		$params{XLSX_DATE_FORMAT} = 'dd/mm/yyyy hh:mm:ss'
			unless defined $params{XLSX_DATE_FORMAT};
	}
	
	my ($PREV_DATE_FORMAT, $PREV_NLS_TIMESTAMP_TZ_FORMAT);
	
	my $sth;
	
	unless(defined $params{DATA}) {
		
		my $DEFAULT_DATE_FORMAT = $PREV_DATE_FORMAT = $dbh->fetch_minimalized("select value from nls_session_parameters where parameter='NLS_DATE_FORMAT'");
		$logger->debug( "Previous SQL DATE format: '$PREV_DATE_FORMAT'" );
		
		#setup default SQL date format
		if( defined $params{'SQL_DATE_FORMAT_STRING'} ) {
		my $res = eval {
			$dbh->do("alter session set nls_date_format = "
				. $dbh->quote($params{'SQL_DATE_FORMAT_STRING'})
			)
		};
		if( !$res ) {
				my $except = $@ || 'DBI do() - return value undef/0';
				$self->last_error("Unable to modify SQL default date format by alter session: $except")
					and return undef;
		}
		$DEFAULT_DATE_FORMAT = $params{'SQL_DATE_FORMAT_STRING'};
		}
	
		$params{'SQL_DATE_FORMAT_STRING'} = $DEFAULT_DATE_FORMAT;
		$logger->debug( "Default SQL DATE format: '$DEFAULT_DATE_FORMAT'" );
		
		$PREV_NLS_TIMESTAMP_TZ_FORMAT = $dbh->fetch_minimalized("select value from nls_session_parameters where parameter='NLS_TIMESTAMP_TZ_FORMAT'");
		$logger->debug( "Previous NLS_TIMESTAMP_TZ_FORMAT format: '$PREV_NLS_TIMESTAMP_TZ_FORMAT'" );
		
		#setup default SQL NLS_TIMESTAMP_TZ_FORMAT format
		{
				my $res = eval {
					$dbh->do("alter session set NLS_TIMESTAMP_TZ_FORMAT = "
						. $dbh->quote($self->{NLS_TIMESTAMP_TZ_FORMAT})
					)
				};
				if( !$res ) {
						my $except = $@ || 'DBI do() - return value undef/0';
						$self->last_error("Unable to modify SQL NLS_TIMESTAMP_TZ_FORMAT by alter session: $except")
							and return undef;
				}
		}
		
		my $query = $self->_get_query(%params);
		return undef
			unless defined $query;
		
		eval {
			$sth = $dbh->create_cursor( $query )
		};
		$self->last_error("Unable to create cursor: " . $@)
			and return undef
				if $@;
		
	}
	
	my $func = "_create_" . $params{OUTPUT_FORMAT};
	my $file_name = $self->$func($sth, %params);
	return undef
		unless defined $file_name;
	
	unless(defined $params{DATA}) {
		{
			my $res = eval {
				$dbh->do("alter session set nls_date_format = "
					. $dbh->quote($PREV_DATE_FORMAT)
				)
			};
			if( !$res ) {
				my $except = $@ || 'DBI do() - return value undef/0';
				$self->last_error("Unable to restore SQL default date format by alter session: $except")
					and return undef;
			}
		}
		
		{
			my $res = eval {
				$dbh->do("alter session set NLS_TIMESTAMP_TZ_FORMAT = "
					. $dbh->quote($PREV_NLS_TIMESTAMP_TZ_FORMAT)
				)
			};
			if( !$res ) {
				my $except = $@ || 'DBI do() - return value undef/0';
				$self->last_error("Unable to restore SQL default NLS_TIMESTAMP_TZ_FORMAT by alter session: $except")
					and return undef;
			}
		}
	}
	
	return $file_name;
	
}

# FIXME: fare in modo che quando esce per errore ripristini la sessione db originale (cosa che già fa in caso di OK)

=head1 JSON REPORT

Il report è una stringa JSON che rappresenta un oggetto Javascript con le seguenti chiavi:

=over

=item C<B<header>> <C<Object[]>>

Descrive gli oggetti che compongono il record:

=over

=item * C<B<name>> <C<string>>

Etichetta della colonna

=item * B<C<I<id>>> <C<string>>

Identificativo della colonna (presente solo se il parametro globale B<C<JSON_USE_ID_INSTEAD_OF_NAME>> è I<true>)

=item * C<B<I<description>>> <C<string>>

Descrizione estesa della colonna (presente solo se il parametro di colonna B<C<JSON_DESCRIPTION>> è valorizzato)

=item * C<B<type>> <C<string>>

Tipo dato della colonna; può valere C<string>, C<number> o C<date> (se vale C<date> la data è una stringa nel formato RFC 3339, section 5.6 - L<http://xml2rfc.tools.ietf.org/public/rfc/html/rfc3339.html#anchor14>)

=item * C<B<hidden>> <C<boolean>>

Se I<true> indica che la colonna deve essere nascosta in fase di rendering

=item * C<B<checkboxFilter>> <C<boolean>> - B<I<DEPRECATED>>

Se I<true> indica che la colonna contiene un set finito di valori

=back

=item C<B<data>> <C<Object[]>>

Array di oggetti chiave valore la cui chiave è il contenuto della chiave C<B<name>> (o C<B<id>>, vedi parametro globale B<C<JSON_USE_ID_INSTEAD_OF_NAME>>) dell'oggetto C<B<header>>

=back

=head2 JSON Example

La seguente chiamata:

  my $report = $sr->report(
    QUERY => 'select cast(sysdate AS TIMESTAMP WITH TIME ZONE) NOW, a.* from v_attivita a',
    OUTPUT_FORMAT => 'json',
    JSON_USE_ID_INSTEAD_OF_NAME => 1,
    COLUMNS => [
      {
        HEADER => "Now",
        NAME => "NOW",
        TYPE => "timestamp_with_time_zone"
      },
      {
        HEADER => "Id",
        NAME => "ID",
        TYPE => "string",
        JSON_DESCRIPTION => "Id dell'attivita"
      },
      {
        HEADER => "Id Sistema",
        NAME => "ID_SISTEMA",
        TYPE => "number",
        JSON_DESCRIPTION => "Id del sistema"
      },
      {
        HEADER => "ID Sottosistema",
        NAME => "ID_SOTTOSISTEMA",
        TYPE => "number",
        JSON_HIDDEN => 1
      },
      {
        HEADER => "Nome Tipo Attivita",
        NAME => "NOME_TIPO_ATTIVITA",
        TYPE => "string"
      },
      {
        HEADER => "Nome Tipo Sottoattivita",
        NAME => "NOME_TIPO_SOTTOATTIVITA",
        TYPE => "string",
        JSON_HIDDEN => 1
      },
      {
        HEADER => "Descrizione",
        NAME => "DESCRIZIONE",
        TYPE => "string"
      },
      {
        HEADER => "Stato Corrente",
        NAME => "STATO_CORRENTE",
        TYPE => "string"
      },
      {
        HEADER => "Data Ultima Variazione",
        NAME => "DATA_ULT_VARSTAT",
        TYPE => "date"
      },
      {
        HEADER => "Operatore Ultima Variazione",
        NAME => "OPERATORE_ULT_VARSTAT",
        TYPE => "string"
      },
      {
        HEADER => "Operatore Corrente",
        NAME => "OPERATORE_CORRENTE",
        TYPE => "string"
      },
      {
        HEADER => "Data di Lock",
        NAME => "DATA_DI_LOCK",
        TYPE => "date"
      },
      {
        HEADER => "Operatore di Lock",
        NAME => "OPERATORE_DI_LOCK",
        TYPE => "string"
      },
      {
        HEADER => "Sessione di Lock",
        NAME => "SESSIONE_DI_LOCK",
        TYPE => "string"
        JSON_EXCLUDE => 1
      }
    ]
  );

restituisce il seguente report:

  {
    "header": [
      {
        "type": "date",
        "checkboxFilter": false,
        "id": "NOW",
        "hidden": false,
        "name": "Now"
      },
      {
        "name": "Id",
        "id": "ID",
        "checkboxFilter": false,
        "hidden": false,
        "type": "string",
        "description": "Id dell'attivita"
      },
      {
        "name": "Id Sistema",
        "id": "ID_SISTEMA",
        "checkboxFilter": false,
        "hidden": false,
        "type": "number",
        "description": "Id del sistema"
      },
      {
        "name": "ID Sottosistema",
        "id": "ID_SOTTOSISTEMA",
        "checkboxFilter": false,
        "hidden": true,
        "type": "number"
      },
      {
        "name": "Nome Tipo Attivita",
        "id": "NOME_TIPO_ATTIVITA",
        "checkboxFilter": false,
        "hidden": false,
        "type": "string"
      },
      {
        "name": "Nome Tipo Sottoattivita",
        "id": "NOME_TIPO_SOTTOATTIVITA",
        "checkboxFilter": false,
        "hidden": true,
        "type": "string"
      },
      {
        "name": "Descrizione",
        "id": "DESCRIZIONE",
        "checkboxFilter": false,
        "hidden": false,
        "type": "string"
      },
      {
        "name": "Stato Corrente",
        "id": "STATO_CORRENTE",
        "checkboxFilter": false,
        "hidden": false,
        "type": "string"
      },
      {
        "name": "Data Ultima Variazione",
        "id": "DATA_ULT_VARSTAT",
        "checkboxFilter": false,
        "hidden": false,
        "type": "date"
      },
      {
        "name": "Operatore Ultima Variazione",
        "id": "OPERATORE_ULT_VARSTAT",
        "checkboxFilter": false,
        "hidden": false,
        "type": "string"
      },
      {
        "name": "Operatore Corrente",
        "id": "OPERATORE_CORRENTE",
        "checkboxFilter": false,
        "hidden": false,
        "type": "string"
      },
      {
        "name": "Data di Lock",
        "id": "DATA_DI_LOCK",
        "checkboxFilter": false,
        "hidden": false,
        "type": "date"
      },
      {
        "name": "Operatore di Lock",
        "id": "OPERATORE_DI_LOCK",
        "checkboxFilter": false,
        "hidden": false,
        "type": "string"
      }
    ],
    "data": [
      {
        "ID": "7713",
        "NOME_TIPO_ATTIVITA": "AS_BUILT",
        "DATA_ULT_VARSTAT": "2017-11-15T11:14:38.000000000+01:00",
        "OPERATORE_ULT_VARSTAT": "ROOT",
        "STATO_CORRENTE": "ATTESA_CHIUSURA_TT",
        "DESCRIZIONE": "test 1",
        "OPERATORE_DI_LOCK": null,
        "ID_SOTTOSISTEMA": null,
        "NOME_TIPO_SOTTOATTIVITA": null,
        "ID_SISTEMA": 3591,
        "DATA_DI_LOCK": null,
        "OPERATORE_CORRENTE": null,
        "NOW": "2017-12-06T23:59:19.000000+01:00"
      },
      {
        "ID": "7722",
        "NOME_TIPO_ATTIVITA": "LAVORO",
        "DATA_ULT_VARSTAT": "2017-12-04T14:57:54.000000000+01:00",
        "OPERATORE_ULT_VARSTAT": "ROOT",
        "STATO_CORRENTE": "ESPLETATO_SENZA_AS_BUILT",
        "DESCRIZIONE": "Splice-1005-giu 1",
        "OPERATORE_DI_LOCK": null,
        "ID_SOTTOSISTEMA": null,
        "NOME_TIPO_SOTTOATTIVITA": null,
        "ID_SISTEMA": 4890,
        "DATA_DI_LOCK": null,
        "OPERATORE_CORRENTE": null,
        "NOW": "2017-12-06T23:59:19.000000+01:00"
      }
    ]
  }

=cut

if (__FILE__ eq $0) {

	use SIRTI::DB;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $db;
	eval {
		$db = SIRTI::DB->new( $ENV{SQLID_WWW}, {} );
	};
	if ($@) {
		die "Login error: $@";
	}
	
	my $output_format = 'csv';
	
	my $utils = SIRTI::Reports->instance(DB => $db);
	
	my $report = $utils->report(
		# TABLE_OR_VIEW_NAME => 'v_pte_management_report'
		#TABLE_OR_VIEW_NAME => 'tipi_dati_tecnici_attivita'
		#,TABLE_OR_VIEW_OWNER => 'dii'
		#,TABLE_OR_VIEW_DBLINK => 'ppreord'
		 QUERY => '
select cast(sysdate AS TIMESTAMP WITH TIME ZONE) NOW, a.* from v_attivita a
		'
		# QUERY => "SELECT * FROM tipi_dati_tecnici_attivita"
		,SQL_DATE_FORMAT_STRING => 'DD/MM/YYYY HH24:MI:SS'
		,OUTPUT_FORMAT => $output_format
		,ENCODING => 'utf8'
		,XLSX_SHEET_NAME => 'Report INNOVATION'
		,XLSX_DATE_FORMAT => 'dd/mm/yyyy'
		,XLSX_FONT => 'Calibri'
		,XLSX_STRING_HORIZONTAL_ALIGN => 'left'
		,XLSX_NUMBER_HORIZONTAL_ALIGN => 'right'
		,XLSX_DATE_HORIZONTAL_ALIGN => 'right'
		,JSON_RETURN_STRING => 1
		,JSON_USE_ID_INSTEAD_OF_NAME => 1
#		,JSON_DATA_AS_ARRAY => 1
#		,CSV_SEP_CHAR => ';'
#		,CSV_QUOTE_CHAR => '"'
#		,CSV_ALWAYS_QUOTED => 0
#		,CSV_NO_HEADER => 1
#		,CSV_MAX_ROWS => 100
		,COLUMNS => [
			{
				HEADER => "Now",
				NAME => "NOW",
				JSON_CHECKBOX_FILTER => 0,
				TYPE => "timestamp_with_time_zone",
				JSON_DESCRIPTION => "Sysdate",
				XLSX_DATE_FORMAT => "dd/mm/yyyy hh:mm:ss"
			},
			{
				HEADER => "Id",
				NAME => "ID",
				JSON_CHECKBOX_FILTER => 0,
				TYPE => "activityId",
				JSON_DESCRIPTION => "Id dell'attivita"
			},
			{
				HEADER => "Id Sistema",
				NAME => "ID_SISTEMA",
				JSON_CHECKBOX_FILTER => 0,
				TYPE => "number",
				JSON_DESCRIPTION => "Id del sistema"
			},
			{
				HEADER => "ID Sottosistema",
				NAME => "ID_SOTTOSISTEMA",
				JSON_CHECKBOX_FILTER => 0,
				TYPE => "number",
				JSON_HIDDEN => 1
			},
			{
				HEADER => "Nome Tipo Attivita",
				NAME => "NOME_TIPO_ATTIVITA",
				JSON_CHECKBOX_FILTER => 1,
				TYPE => "string"
			},
			{
				HEADER => "Nome Tipo Sottoattivita",
				NAME => "NOME_TIPO_SOTTOATTIVITA",
				JSON_CHECKBOX_FILTER => 0,
				TYPE => "string",
				JSON_HIDDEN => 1
			},
			{
				HEADER => "Descrizione",
				NAME => "DESCRIZIONE",
				JSON_CHECKBOX_FILTER => 0,
				TYPE => "string"
			},
			{
				HEADER => "Stato Corrente",
				NAME => "STATO_CORRENTE",
				JSON_CHECKBOX_FILTER => 1,
				TYPE => "string"
			},
			{
				HEADER => "Data Ultima Variazione",
				NAME => "DATA_ULT_VARSTAT",
				JSON_CHECKBOX_FILTER => 0,
				TYPE => "date",
				XLSX_DATE_FORMAT => "dd/mm/yyyy hh:mm"
			},
			{
				HEADER => "Operatore Ultima Variazione",
				NAME => "OPERATORE_ULT_VARSTAT",
				JSON_CHECKBOX_FILTER => 0,
				TYPE => "string"
			},
			{
				HEADER => "Operatore Corrente",
				NAME => "OPERATORE_CORRENTE",
				JSON_CHECKBOX_FILTER => 0,
				TYPE => "string",
				JSON_EXCLUDE => 1
			},
			{
				HEADER => "Data di Lock",
				NAME => "DATA_DI_LOCK",
				JSON_CHECKBOX_FILTER => 0,
				TYPE => "date",
				XLSX_DATE_FORMAT => 'dd/mm/yyyy hh:mm:ss'
			},
			{
				HEADER => "Operatore di Lock",
				NAME => "OPERATORE_DI_LOCK",
				JSON_CHECKBOX_FILTER => 0,
				TYPE => "string",
				XLSX_EXCLUDE => 1
			},
			{
				HEADER => "Sessione di Lock",
				NAME => "SESSIONE_DI_LOCK",
				JSON_CHECKBOX_FILTER => 0,
				TYPE => "string",
				CSV_EXCLUDE => 1
			}
		]
	);

	if( defined $report) {
		get_logger()->info("OK: ".$report);
	} else {
		get_logger()->error("Error: " . $utils->last_error());
		die;
	}
	
	get_logger()->info("************* DATA ****************");

	my $data = from_json('[{"info":{"closureDate":null,"isLockedByMyself":false,"status":"ASSEGNATA","children":[],"currentUserObj":{"email":"<EMAIL>","firstName":"FC_AT","disabled":false,"serviceUser":false,"groups":["USER01","AT"],"mobilePhone":null,"lastName":"FC_AT","username":"FC_AT"},"isClosed":false,"owner":"ROOT","currentUser":"FC_AT","attachments":[{"DUMMY":"1"},{"DUMMY":"1"}],"lastVarDate":"2018-11-08T15:27:20.000000000+01:00","agingObj":{"lastVarDate":null,"interval":null,"calendarDescription":null,"seconds":null,"sla":[],"calendarId":null,"update":false,"calendarLabel":null},"systemType":"NETWORK","isLocked":false,"description":"000290fjd909","properties":["cartographicUpdate","networkInventoryUpdate","forecastStartDate","networkDesc","workOrderId","networkType","AOL","networkId","customerWBE","estimatedCost","networkTypeDesc","workType","forecastEndDate","centralGeoLocation","RO","customerTechnicalAssistant","cancellationDate","customerAssignmentDate","workingGroupCode","customerWorkingArea","centralId"],"creationDate":"2018-10-31T13:33:53.000000000+01:00","parentId":null,"creationUser":"ROOT","dateNextStep":"2018-11-08T16:56:22.000000000+01:00","systemId":4381,"type":"NETWORK","ownerObj":{"email":"<EMAIL>","firstName":"ROOT","disabled":false,"serviceUser":false,"groups":["ROOT"],"mobilePhone":null,"lastName":"ROOT","username":"ROOT"},"creationUserObj":{"email":"<EMAIL>","firstName":"ROOT","disabled":false,"serviceUser":false,"groups":["ROOT"],"mobilePhone":null,"lastName":"ROOT","username":"ROOT"}},"system":{"info":{"disabled":false,"disablingDate":null,"endingDate":null,"active":true,"description":"TIM-CREATION-000290fjd909","properties":["businessProject","businessProjectDesc","buyer","buyerDesc","centralId","contractId","customerGoodsTotalAmount","customerId","customerProjectType","customerProjectTypeDesc","customerTechnicalAssistant","customerTechnicalAssistantDesc","customerWBE","customerWBEDesc","customerWorkingArea","enhancementFactorDesc","forecastEndDate","forecastStartDate","goodsRecipient","goodsSupplier","goodsSupplierDesc","messageId","MOSTotalAmount","networkDesc","networkId","networkStatus","operation","operationDesc","projectId","propositionContractId","propositionCurrency","propositionDate","propositionNumber","requestorContext","resourcesTotalAmount","SAPDivision","SAPDivisionDesc","servicesTotalAmount","supplier","supplierDesc","supplierGoodsTotalAmount","technicalSite","technicalSiteDesc","totalAmount","workOrderId"],"ended":false,"creationDate":"2018-10-31T13:33:53.000000000+01:00","groups":["ADMIN","PM_TIM_CREATION","ROOT"],"type":"NETWORK"},"id":4381,"properties":{"forecastEndDate":"2017-06-26T02:00:00.000000000+02:00","customerGoodsTotalAmount":null,"workOrderId":"8","propositionCurrency":null,"enhancementFactorDesc":null,"customerTechnicalAssistantDesc":null,"technicalSite":null,"buyerDesc":null,"servicesTotalAmount":null,"buyer":null,"propositionContractId":null,"technicalSiteDesc":null,"supplierDesc":null,"customerWBE":"3","networkDesc":"9","contractId":"CREATION","goodsRecipient":null,"customerId":"TIM","goodsSupplierDesc":null,"MOSTotalAmount":null,"forecastStartDate":"2017-06-25T02:00:00.000000000+02:00","goodsSupplier":null,"messageId":null,"totalAmount":null,"networkId":"000290fjd909","businessProjectDesc":null,"resourcesTotalAmount":null,"customerWBEDesc":null,"centralId":"1","networkStatus":null,"projectId":"000290fjd909","supplier":null,"customerWorkingArea":"4","operation":null,"SAPDivisionDesc":null,"propositionNumber":null,"customerProjectTypeDesc":null,"supplierGoodsTotalAmount":null,"operationDesc":null,"propositionDate":null,"requestorContext":null,"businessProject":null,"customerTechnicalAssistant":"5","customerProjectType":null,"SAPDivision":null}},"id":6332,"properties":{"forecastEndDate":"2017-06-26T02:00:00.000000000+02:00","workOrderId":"8","AOL":"AOL_DTA","workingGroupCode":"workingGroupCode_DTA","networkTypeDesc":null,"customerWBE":"3","networkDesc":"9","centralGeoLocation":"centralGeoLocation_DTA","forecastStartDate":"2017-06-25T02:00:00.000000000+02:00","customerAssignmentDate":"2017-06-24T02:00:00.000000000+02:00","networkId":"000290fjd909","cancellationDate":null,"cartographicUpdate":null,"centralId":"1","customerWorkingArea":"4","networkType":"REACTIVE","RO":"RO_DTA","estimatedCost":"estimatedCost_DTA","customerTechnicalAssistant":"customerTechnicalAssistant_DTA","workType":null,"networkInventoryUpdate":null}}]');
	
	my $report_data = $utils->report(
		 DATA => $data
		,OUTPUT_FORMAT => $output_format
		,ENCODING => 'utf8'
		,JSON_RETURN_STRING => 1
		,JSON_USE_ID_INSTEAD_OF_NAME => 1
		,COLUMNS => [
			{
				NAME => 1,
				HEADER => "Id",
				PATH => "[\"id\"]",
				TYPE => "activityId",
				JSON_DESCRIPTION => "Id dell'attivita",
				POSITION => 1
			},
			{
				NAME => 2,
				HEADER => "Data Ultima Variazione",
				PATH => "[\"info\"][\"lastVarDate\"]",
				TYPE => "timestamp_with_time_zone",
				JSON_DESCRIPTION => "Data Ultima Variazione dell'attivita",
				XLSX_DATE_FORMAT => "dd/mm/yyyy hh:mm:ss",
				POSITION => 3
			},
			{
				NAME => 3,
				HEADER => "Descrizione sistema",
				PATH => "[\"system\"][\"info\"][\"description\"]",
				TYPE => "string",
				POSITION => 2
			},
			{
				NAME => 4,
				HEADER => "Tipo di network",
				PATH => "[\"properties\"][\"networkType\"]",
				TYPE => "string",
				JSON_DESCRIPTION => "Tipo di network tra quelli possibili",
				POSITION => 4
			},
			{
				NAME => 5,
				HEADER => "System Active",
				PATH => "[\"system\"][\"info\"][\"active\"]",
				TYPE => "boolean",
				POSITION => 5
			},
			{
				NAME => 6,
				HEADER => "Closed",
				PATH => "[\"info\"][\"isClosed\"]",
				TYPE => "boolean",
				POSITION => 6
			},
			{
				NAME => 7,
				HEADER => "Gruppo principale",
				PATH => '["system"]["info"]["groups"][0]',
				TYPE => "string",
				POSITION => 7
			},
#			FIXME: tipo array NYI
#			{
#				HEADER => "Gruppi sistema",
#				NAME => "['system']['info']['groups']",
#				TYPE => "string[]",
#				JSON_DESCRIPTION => "Elenco gruppi utenti che hanno visibilita sul sistema"
#			}
		]
	);

	if( defined $report_data) {
		if ($output_format eq 'json') {
			my $json = JSON->new();
			$json->pretty(['indent']);
			my $print = {
				 count => scalar @$data
				,results => $json->decode($report_data)
			};
			get_logger()->info("OK\n".$json->encode($print));
		} else {
			get_logger()->info("OK: ".$report);
		}
	} else {
		get_logger()->error("Error: " . $utils->last_error());
		die;
	}

}

1;
