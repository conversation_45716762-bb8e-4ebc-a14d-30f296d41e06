################################################################################
#
# SIRTI::SMS::Client
#
################################################################################

=head1 NAME

B<SIRTI::SMS::Client> - Package per l'invio centralizzato degli SMS

=head1 SYNOPSIS

  my $sms = SIRTI::SMS::Client->new( 
    SENDER_TEXT	=> 'Sirti Service Desk', 
    SERVICENAME	=> 'HDA',                # Optional
    USERNAME => 'pippo'                  # Optional if defined $ENV{USER}
  ) || die;
  my $status = $sms->send(
    SENDER_TEXT      => 'Service Desk',  # Overwrite
    SERVICENAME      => 'ISS',           # Overwrite
    RECIPIENT_NUMBER => '3331234567',
    BODY             => 'This is an SMS 
  );
  if ( $status ) {
	  print "SMS sent!", "\n";
  } else {
	  print "SMS not sent: ", $sms->last_error(), "\n";
  }

=head1 DESCRIPTION

Questo package rappresenta l'interfaccia I<client> verso il proxy SMS di Sirti.

=cut

package SIRTI::SMS::Client;

use strict;
use warnings;
use Carp qw{verbose croak};
use File::Basename;
use LWP::UserAgent;
use HTTP::Headers;
use Text::Iconv;
use Encode;
use JSON;

use constant {
	 ERR_OK					=> 1
	,ERR_KO					=> 0

	,ERR_BAD_PARAMETERS		=> -1001
	,ERR_POST				=> -1002
	,ERR_RESPONSE			=> -1003
};

use base 'SIRTI::Base';


################################################################################
#  P R O P R I E T A '   P U B B L I C H E
################################################################################

our $VERSION = '0.02';



################################################################################
#  M E T O D I   P R I V A T I
################################################################################

sub _warn {
	my $self = shift;
	my $msgid = shift || '';
	my $msg = shift || '';
	print STDERR "$msgid $msg", "\n";
	return 1;
}

sub _servicename { $_[0]->{__SERVICENAME__} }
sub _test { $_[0]->{__TEST__} }
sub _debug { $_[0]->{__DEBUG__} }
sub _proxy { $_[0]->{__PROXY__} }
sub _session { $_[0]->{__SESSION__} }
sub _user_agent { $_[0]->{__USER_AGENT__} }

sub _url {
	my $self = shift;
	my $p = $self->_proxy(); 
	return $p->{URL} if defined $p->{URL};
	return $p->{PROTOCOL} . '://' . $p->{HOST} . ':' . $p->{PORT} . '/' . $p->{CONTEXT} . '/' . $p->{SERVICE};
}


sub _is_utf8 {
	my ($self, $s) = @_;
	my $iconv = Text::Iconv->new('utf-8', 'utf-8');
	my $dummy = $iconv->convert($s);
	my $retval = $iconv->retval();
	undef $iconv;
	return undef unless defined($retval);
	return ( $retval == 0 ? 1 : 0 );
}
sub _to_utf8 {
	my ($self, $s) = @_;
	my $iconv = Text::Iconv->new('iso-8859-15', 'utf-8');
	return $iconv->convert($s);
}

sub _send {
	my $self = shift;
    my %params = @_;
    my $errmsg = '';

	$self->last_error(ERR_BAD_PARAMETERS, $errmsg)
		&& return ERR_KO
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					 SENDER_TEXT		=> { isa =>	'SCALAR' , pattern => qr/^[A-Z0-9_ ]+$/i }
					,RECIPIENT_NUMBER	=> { isa => 'SCALAR' , pattern => qr{^3[0-9]+$} }
					,BODY				=> { isa =>	'SCALAR' }
					,SERVICENAME		=> { isa =>	'SCALAR' , pattern => qr/^[A-Z0-9_ ]+$/i }
				}
				,OPTIONAL	=> {
				}
			);

	# Converto il corpo del messaggio in UTF-8
	unless ( $self->_is_utf8($params{BODY}) ) {
		$self->_warn( "INPUT....: '$params{BODY}' is not utf8..." );
		$params{BODY} = $self->_to_utf8($params{BODY});
		$self->_warn( "OUTPUT...:'$params{BODY}'" );
		$self->_warn( "is utf8? ", $self->_is_utf8($params{BODY}) );
	}

	# Determino lunghezza massima del body
	#print STDERR "Body length...........: ", length($params{BODY}), "\n";
	if ( length($params{BODY}) > 160 ) {
		$params{BODY} = substr($params{BODY}, 0, 157) . '...';
	}

	# Invio la richiesta al proxy
	my $response = $self->_user_agent()->post(
		$self->_url(),
		{ 
			%{$self->_session()}, 
			%params,
			TEST		=> $self->_test(),
			DEBUG		=> $self->_debug(),
		}
	);
	my $rc = $response->is_success();
	if ( $rc ) {
		my $content = $response->decoded_content();
		my $json = JSON->new();
		if ( $json->can('utf8') ) {
			$json->utf8(1);
		} else {
			eval '$JSON::UTF8 = 1';
		}
		$self->{__RESPONSE__} = $json->can('decode') 
			? $json->decode($content)
			: $json->jsonToObj($content);
		if ( $self->{__RESPONSE__}->{STATUS} eq '0' ) {
			$rc = $self->{__RESPONSE__}->{MSGID}; # (ex ERR_OK) ora ritorna il contenuto della chiave MSGID
		} else {
			$rc = ERR_KO;
			$self->last_error(ERR_RESPONSE, "Response fault #" . $self->{__RESPONSE__}->{STATUS});
		}
	} else {
		$rc = ERR_KO;
		$self->last_error($rc, ERR_POST, $response->status_line());
	}
	return $rc;
}




################################################################################
#  M E T O D I   P U B L I C I
################################################################################

=pod

=head1 METHODS

=over 4

=item new( I<SENDER_TEXT>, [I<SERVICENAME>, I<TEST>, I<DEBUG>, I<USERNAME>] )

Costruttore di classe.

Parametri:

=over 8

=item * SENDER_TEXT : I<SCALAR> che rappresenta l'iIDentificativo che verra' usato come B<mittente> degli SMS

=item * TEST : I<SCALAR> che può valere 0|1 (default=0): se impostato ad 1 non invia l'SMS (non viene inserita 
la riga in SMS_BUFFR)

=item * DEBUG : I<SCALAR> che può valere 0|1 (default=0): se impostato ad 1 emette informazioni di debug

=item * SERVICENAME : I<SCALAR> che rappresenta il nome del servizio (istanza ART o altro) abilitata all'invio 
di SMS; se omesso sara' necessario specificare tale informazione in fase di invio dell'SMS

=item * USERNAME : I<SCALAR> che rappresenta il nome dell'utente richiedente l'invio; se omesso verrà utilizzato
il valore dell'ENVironment $USER (NOTA: B<obbligatorio se $USER non è definito>)

=back

=cut
sub new {
    my $this = shift;
    my $class = ref($this) || $this;
    my %params = @_;
    my $self = bless( {}, $class );
    my $errmsg = '';
	croak $errmsg
		unless $self->check_named_params(
			 ERRMSG	=> \$errmsg
			,PARAMS	=> \%params
			,MANDATORY	=> {
				 SENDER_TEXT	=> { isa =>	'SCALAR' , pattern => qr/^[A-Z0-9_ ]+$/i }
			}
			,OPTIONAL	=> {
				 TEST			=> { isa => 'SCALAR' , list => [0, 1] }
				,DEBUG			=> { isa => 'SCALAR' , list => [0, 1] }
				,SERVICENAME	=> { isa =>	'SCALAR' , pattern => qr/^[A-Z0-9_ ]+$/i }
				,USERNAME		=> { isa => 'SCALAR' , pattern => qr/^([a-z_][a-z0-9_]{0,30})$/i }
			}
		);

	# Impostazione proprietà mediante metodi pubblici
	$self->set_sender_text( $params{SENDER_TEXT} );

	# Impostazione proprietà private
	$self->{__SERVICENAME__}	= $params{SERVICENAME} || '';
	$self->{__TEST__}			= ( defined($params{TEST}) ? $params{TEST} : 0 );
	$self->{__DEBUG__}			= ( defined($params{DEBUG}) ? $params{DEBUG} : 0 );
	$self->{__USER_AGENT__}	= LWP::UserAgent->new( timeout => 10 );
	$self->{__USERNAME__}		= $params{USERNAME} || $ENV{USER};
	croak 'Missing USERNAME parameter (or set $ENV{USER})'
		unless $self->{__USERNAME__};

	# Configurazione proxy
	$self->{__PROXY__} = {};
	$self->{__RESPONSE__} = undef;

	# Verifico esistenza variabile d'ambiente PROXYSMS_URL
	if ( defined($ENV{"PROXYSMS_URL"}) ) {
		 $self->{__PROXY__}->{"URL"} = $ENV{"PROXYSMS_URL"};
	} else {
		# Alternativamente verifico esistenza variabili d'ambiente
		for my $env (qw/PROTOCOL HOST PORT CONTEXT SERVICE/) {
			croak "Missing ENVironment settings PROXYSMS_* (Please, refer to " . __PACKAGE__ . " pod documentation)."
				unless defined($ENV{"PROXYSMS_${env}"});
			$self->{__PROXY__}->{"${env}"} = $ENV{"PROXYSMS_${env}"};
		}
	}

	my $hostname = (defined($ENV{HOSTNAME}) ? $ENV{HOSTNAME} : `hostname -s`);
	chomp $hostname;
	# Informazioni per il tracciamento della sessione corrente
	$self->{__SESSION__} = {
		 SESSION		=> '' . basename($0) . ' ' . join(" ", map { /\s/ ? "'$_'" : $_ } @ARGV)
		,PROGRAMNAME	=> basename($0)
		,HOST_IP		=> eval { `nslookup $hostname | grep -i address | tail -1 | cut -d ' ' -f 2 2>/dev/null | xargs echo -n` } || ''
		,HOSTNAME		=> $hostname
		,USERNAME		=> $self->{__USERNAME__}
		#,SSH_CONNECTION	=> (defined $ENV{SSH_CONNECTION} ? $ENV{SSH_CONNECTION} : '')
	};
	for my $key (keys %{$self->{__SESSION__}}) {
		$self->{__SESSION__}->{$key} = $self->_to_utf8($self->{__SESSION__}->{$key})
			unless $self->_is_utf8($self->{__SESSION__}->{$key});
	}


	return $self;
}



=pod

=item response()

Ritorna una struttura dati che rappresenta la response JSON ricevuta dal proxy.

=cut
sub get_response { $_[0]->{__RESPONSE__} }


=pod

=item get_sender_text()

Ritorna uno I<SCALAR> con l'iIDentificativo attualmente impostato come B<mittente> degli SMS

=cut
sub get_sender_text { $_[0]->{SENDER_TEXT} }

=pod

=item set_sender_text( I<SCALAR> )

Imposta (o reimposta) l'iIDentificativo che verra' usato come B<mittente> degli SMS

=cut
sub set_sender_text { 
    my $self = shift;
    my $sender_text = shift;
    my $errmsg = '';
	croak $errmsg
		unless $self->check_named_params(
			 ERRMSG		=> \$errmsg
			,PARAMS		=> { SENDER_TEXT => $sender_text }
			,MANDATORY	=> {
				SENDER_TEXT	=> { isa =>	'SCALAR' , pattern => qr/^[A-Z0-9_ ]+$/i }
			}
			,OPTIONAL	=> {
			}
		);
	$self->{SENDER_TEXT} = $sender_text;
} 


=pod

=item send( I<BODY>, I<RECIPIENT_NUMBER>, [ I<SENDER_TEXT> ] )

Invia al PROXY una richiesta di invio di un SMS.

Parametri:

=over 8

=item * BODY : I<SCALAR> che rappresenta il testo del SMS da inviare

=item * RECIPIENT_NUMBER : I<SCALAR> che indica il numero del destinatario del messaggio

=item * SENDER_TEXT : I<SCALAR> rappresenta il mittente del SMS; se omesso verra'
utilizzato il SENDER_TEXT impostato nella creazione dell'oggetto

=item * SERVICENAME : I<SCALAR> che rappresenta il nome del servizio (istanza ART o altro) abilitata all'invio 
di SMS; se omesso verra' utilizzato (se indicato) il valore specificato nella creazione dell'oggetto.
B<ATTENZIONE! Nel caso in cui non venisse specificato ne' nel metodo new() ne' nel metodo send(), l'invio 
dell'SMS fallira' (nel metodo privato _send())>

=back

B<send()> ritorna I<1> se la richiesta e' stata presa in carico correttamente dal proxy. In caso di errore
viene ritornato I<0> e viene impostato B<last_error()>

=cut
sub send {
	my $self = shift;
    my %params = @_;
    my $errmsg = '';

	$self->clear_error();
	$self->{__RESPONSE__} = undef;
	$self->last_error(ERR_BAD_PARAMETERS, $errmsg)
		&& return ERR_BAD_PARAMETERS
			unless $self->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					 BODY				=> { isa =>	'SCALAR' }
					,RECIPIENT_NUMBER	=> { isa => 'SCALAR', pattern => qr{^3[0-9]+$} }
				}
				,OPTIONAL	=> {
					 SENDER_TEXT	=> { isa =>	'SCALAR' , pattern => qr/^[A-Z0-9_ ]+$/i }
					,SERVICENAME	=> { isa =>	'SCALAR' , pattern => qr/^[A-Z0-9_ ]+$/i }
				}
			);
	# Invio l'sms
	return $self->_send(
		 SENDER_TEXT		=> $params{SENDER_TEXT} || $self->get_sender_text()
		,RECIPIENT_NUMBER	=> $params{RECIPIENT_NUMBER}
		,BODY				=> $params{BODY}
		,SERVICENAME		=> (
			defined($params{SERVICENAME}) 
				? $params{SERVICENAME}		# se specificato utilizza il parametro del metodo ->send()
				: $self->_servicename()		# fallback sul metodo privato 
		)
	);
}


=pod

=item last_error()

Ritorna l'ultimo messaggio di errore riscontrato dall'oggetto.


=back

=head1 NOTES

=over 4

=head2 ENVIRONMENT

E' B<MANDATORIA> la definizione della seguente variabile d'ambiente che rappresenta le 
coordinate del proxy SMS di Sirti:

=over 8

=item * B<PROXYSMS_URL> (i.e.: "https://iss.sirti.it:10080/sms/proxy.cgi")

=back

Alternativamente, B<SIRTI::SMS::Client> verifica la definizione di tutte le seguenti variabili d'ambiente:

=over 8

=item * B<PROXYSMS_PROTOCOL> (i.e.: "http", "https")

=item * B<PROXYSMS_HOST> (i.e.: "iss.sirti.it")

=item * B<PROXYSMS_PORT> (i.e.: "80", "10080")

=item * B<PROXYSMS_CONTEXT> (i.e.: "sms")

=item * B<PROXYSMS_SERVICE> (i.e.: "proxy.cgi")

=back

=back

=head1 BUGS

Eventuali bug riscontrati dovranno essere segnalati aprendo un apposito DEV-TICKET.

=head1 HISTORY

=over

=item Ver. 0.01

Prima release del modulo

=back

=head1 AUTHOR

Alvaro Livraghi 

=cut


#
####################################
##
## Testing the module...
##
####################################
#if (__FILE__ eq $0) {
#
#	####  test ###  
#	package main;
#
#	use strict;
#	use warnings;
#	use Carp 'verbose';
#	use Data::Dumper;
#	$Data::Dumper::Terse = 1;
#
#	my $sms = SIRTI::SMS::Client->new( 
#		SENDER_TEXT	=> 'Sirti Service Desk',
#		#SERVICENAME	=> 'Order Management',
#		TEST		=> 1,
#		DEBUG		=> 1,
#	);
##	print 'Mittente....: ', $sms->get_sender_text(), "\n";
##	print 'Test........: ', $sms->_test(), "\n";
##	print 'Debug.......: ', $sms->_debug(), "\n";
##	print 'Proxy.......: ', Dumper($sms->_proxy());
##	print 'Session.....: ', Dumper($sms->_session());
##	print 'UserAgent...: ', Dumper($sms->_user_agent());
#
#
#	my $r = 0;
#	while ( <DATA> ) { # usare <DATA> per unit test
#	#while ( <STDIN> ) {
#		++$r;
#		chomp;
#		my $line = $_;
#		my @sms = split(/\|/, $line);
#		my $status = $sms->send(
#			 SERVICENAME		=> $sms[0]
#			,SENDER_TEXT		=> $sms[1]
#			,RECIPIENT_NUMBER	=> $sms[2]
#			,BODY				=> $sms[3]
#		);
#		print STDOUT 
#			sprintf("send(%05d) : ", $r)
#			,"status=$status", ', '
#			,"LastError=", ($sms->last_error() || "''"), ', '
#			,"test #1=", (($status > 0 ? 1 : 0) == $sms[4] ? "PASSED" : "***NOT PASSED***"), ', '
#			#,"test #2=", ($sms->get_response()->{PARAMS}->{BODY} eq $sms[3] ? "PASSED" : "***NOT PASSED***")
#			,"\n";
#		#print STDOUT $sms[3], "\n";
#		#print STDOUT (defined($sms->get_response()) ? $sms->get_response()->{PARAMS}->{BODY} : ''), "\n";
#	}
#
#	exit 0;
#
#} else {
#
	1;
#
#}
#
## SERVICENAME | SENDER_TEXT | RECIPIENT_NUMBER | BODY | EXPECTED_STATUS
#__END__
#WEEK|BenH|3477872079|001 - Oggi è mercoledì e tutto va' bene|1
#WEEK|BenW|335496201|002 - Oggi non è giovedì e tutto va' male|1
#WEEK|TeleTu Info|a3123456789|003 - Oggi non è giovedì e tutto va' male|0
#WEEK|AlH|3389824906|004 - Vorrei tanti €€€€€ ma anche tanti $$$$$ andrebbero comunque bene!!|1
#WEEK||31234567890121212|005 - Vorrei tanti €€€€€ ma anche tanti $$$$$ andrebbero comunque bene!!|0
#WEEK|Pippo|56789|006 - Vorrei tanti €€€€€ ma anche tanti $$$$$ andrebbero comunque bene!!|0
#WEEK|Pippo|555556789|007 - Vorrei tanti €€€€€ ma anche tanti $$$$$ andrebbero comunque bene!!|0
#WEEK|AlW|3316662980|008 - Vorrei tanti €€€€€ ma anche tanti $$$$$ andrebbero comunque bene!!|1
#WEEK|Al3|3929072604|009 - Vorrei tanti €€€€€ ma anche tanti $$$$$ andrebbero comunque bene!!|1
#|Al3|3929072604|010 - Vorrei tanti €€€€€ ma anche tanti $$$$$ andrebbero comunque bene!!|0
#WEEK||3929072604|011 - Vorrei tanti €€€€€ ma anche tanti $$$$$ andrebbero comunque bene!!|0
#WEEK|Al3||012 - Vorrei tanti €€€€€ ma anche tanti $$$$$ andrebbero comunque bene!!|0
#WEEK|Al3||013 - Vorrei tanti EURI ma anche tanti DOLLARI andrebbero comunque bene!!|0
