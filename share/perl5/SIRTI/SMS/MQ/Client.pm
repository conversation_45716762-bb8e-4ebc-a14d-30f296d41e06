################################################################################
#
# SIRTI::SMS::MQ::Client
#
################################################################################
=head1 NAME

B<SIRTI::SMS::MQ::Client> - Package per l'invio centralizzato degli SMS tramite Remote Activity

=head1 SYNOPSIS

  my $sms = SIRTI::SMS::MQ::Client->new( 
    SENDER_TEXT	=> 'Sirti Service Desk', # Optional
    SERVICENAME	=> 'HDA',                # Optional
    QUEUE       => {                     # Optional
                       SOURCE_SERVICE => 'SMS_S1',     #Optional 
                       SOURCE_CONTEXT => 'SMS_SC',     #Optional
                       TARGET_CONTEXT => 'SMS_TC',     #Optional
                       TARGET_SERVICE => 'SMS_T1',     #Optional
                       DB             => $sirtiDbObj   #Optional
                       SESSION_DESCRIPTION => 'test'   #Optional
                       SOURCE_REF          => 'test'   #Optional
                   }
  ) || die;
  
  $sms->change_queue_config( { SOURCE_REF => __PACKAGE__ . " PID:$$" } );
  
  my $status = $sms->send(
    SENDER_TEXT      => 'Service Desk',  # Overwrite
    SERVICENAME      => 'ISS',           # Overwrite
    RECIPIENT_NUMBER => '3331234567',
    BODY             => 'This is an SMS 
  );
  if ( $status ne SIRTI::SMS::Client::ERR_KO && 
       $status ne SIRTI::SMS::Client::ERR_BAD_PARAMETERS ) 
  {
	  print "SMS sent!", "\n";
  } else {
	  print "SMS not sent: ", $sms->last_error(), "\n";
  }

=head1 DESCRIPTION

Il modulo consente di inviare una richiesta di inivio SMS tramite Remote Activity.
Questa possibilita' permette di legare l'effettivo invio di messaggi SMS all'esito di 
una transazione su DB, in modo trasparente al package stesso; effettuando successivamente 
una commit o una rollback si conferma o meno l'invio delle richieste SMS.

Internamente il package utilizza un instanza del modulo SIRTI::SMS::Client, creato in modalita' 'TEST'.
Tale oggetto, viene utilizzato SOLO per validare una richiesta di inoltro SMS, che di fatto avviene tramite
l'invio di un messaggio di remote activity.
Il modulo SIRTI::SMS::MQclient, espone gli stessi metodi presenti nel modulo SIRTI::SMS::Client, mantenendone
la stessa sintassi e semantica.
A differenza di tale modulo, B<MQClient> espone anche metodi necessari a configurare la coda e i messaggi delle
Remote Activity.
A tal fine il metodo costruttore a differenza di quello SIRTI::SMS::Client accetta un parametro aggiuntivo, facoltativo,
chiamato QUEUE che permette di configurare i parametri di interfacciamento alle Remote Activity.
Tali parametri possono essere modificati anche successivamente.

La coda Remote Activity (sessione aperta in modalita' source), viene aperta in modalita' "lazy", cioe' a seguito
del primo tentativo di inoltro messaggio.  
La coda (sessione) resta aparta fino a quando non viene seguita un riconfigurazione dei parametri legati alla coda stessa
o quando viene finalizzata l'instanza dell'oggetto B<MQClient>.
 
=cut


package SIRTI::SMS::MQ::Client;

use strict;
use warnings;
use Carp qw{verbose croak};

use SIRTI::SMS::Client;
use SIRTI::MQ::Notify::SmsDataHandler;
use SIRTI::ART::RemoteActivity::Source;

use base 'SIRTI::Base';


use constant {
  RA_SEND_SMS_EVENT     => 'SEND_SMS'
};

my @MQ_QUEUE_CONFIG_PARAMS = qw(
    SOURCE_SERVICE
    SOURCE_CONTEXT
    TARGET_SERVICE
    TARGET_CONTEXT
    SESSION_DESCRIPTION
    DB
);

my @MQ_EVENT_CONFIG_PARAMS = qw(
    SCHEDULE_DATE
    EXPIRY_DATE
    EVENT
    SOURCE_REF
);

our $VERSION = '0.01';



=pod

=head1 METHODS

=over 4

=item new( I<SENDER_TEXT>, [I<SERVICENAME>, I<DEBUG>, I<QUEUE>] )

Costruttore di classe.

Parametri:

=over 8

=item * SENDER_TEXT : I<SCALAR> che rappresenta l'identificativo che verra' usato come B<mittente> degli SMS

=item * SERVICENAME : I<SCALAR> che rappresenta il nome del servizio (istanza ART o altro) abilitata all'invio 
di SMS; se omesso sara' necessario specificare tale informazione in fase di invio dell'SMS
 
=item * DEBUG : I<SCALAR> che può valere 0|1 (default=0): se impostato ad 1 emette informazioni di debug

=item * QUEUE : I<HASHREF> che contiene la configurazione della coda remote activity a cui inviare la richiesta
di inoltro SMS, e alcune proprieta' di default da applicare ai messaggio ramote activity inviati sulla coda.
La configurazione è opzionale dato che può essere modificata successivamente, utilizzando i metodi
B<set_queue_config()> e B<change_queue_config()> per modificare uno o più parametri.

Di seguito sono elencati i prametri (tutti facoltativi) previsti dal package:

coda:
   SOURCE_SERVICE       [scalar]           stringa utilizzata per identificare una coda RA
   SOURCE_CONTEXT       [scalar]           stringa utilizzata per identificare una coda RA
   TARGET_SERVICE       [scalar]           stringa utilizzata per identificare una coda RA
   TARGET_CONTEXT       [scalar]           stringa utilizzata per identificare una coda RA
   DB                   [obj SIRTI::DB]    database in cui inserire le richieste di inivio SMS (RA EVENT) 
   SESSION_DESCRIPTION  [scalar]           descrizione di default della sessione RA_SOURCE
   
messaggio:
   SOURCE_REF           [scalar] source ref applicato per default ai messaggi in uscita
   SCHEDULE_DATE        [scalar] data di attivazione della richiesta di invio SMS
                                 Parametro non ancora utilizzato
   EXIPRY_DATE          [scalar] data di scadenza della richiesta di invio SMS
                                 Parametro non ancora utilizzato
   
=back

=cut

sub new {
    my $this = shift;
    my $class = ref($this) || $this;
    my %params = @_;
      
    my $self = { 
	     QUEUE       => { },
	     MQ_HANDLE   => undef,
	     MQ_RESPONSE => undef 
    };
     
    my %argv = ( );
    for( qw/SENDER_TEXT DEBUG SERVICENAME/) {
         $argv{$_} = $params{$_} if exists $params{$_};
    }

    $self->{NOTIFY_HANDLER} = SIRTI::MQ::Notify::SmsDataHandler->new;

    $self->{SERVICENAME} = $params{SERVICENAME} 
         if exists $params{SERVICENAME};
    
    $self->{SMS_CLIENT} = eval { SIRTI::SMS::Client->new( %argv, TEST => 1 ) }; 
#    if( $@ || !defined $self->{SMS_CLIENT} ) {
#         my $except = $@ || "dannazione: l'istruzione \"local \$@\"; nel metodo della classe SIRTI::SMS::Client->new(), non consente di intercettare gli errori";
#         croak "Errore instanziando SIRTI::SMS::Client in modalita' test: $except";
#    }
    
    croak "Errore instanziando SIRTI::SMS::Client in modalita' test: ".$@ if $@;
  
    bless( $self, $class );    
 
    if( exists $params{QUEUE} ) {
		if( ! $self->set_queue_config( $params{QUEUE} ) ) {
			croak "Configurazione coda RemoteActivity::SOURCE non valida: " . $self->last_error;
		}
	}
    return $self;
}


sub _notify_handler { shift->{NOTIFY_HANDLER} }
    

=pod

=item set_sender_text( I<scalar> )

Permette di specificare una stringa di testo che rappresenta l'identificativo del mittente
del messaggio SMS.

Parametri:  I<SCALAR> stringa da utilizzare come Identificativo del mittente SMS
  
=cut

sub set_sender_text {
	my $self = shift;
    my $val = $self->_sms_client( )->set_sender_text( @_ );
    $self->last_error( $self->_sms_client( )->last_error( ) );
    return $val;
}

=pod

Ritorna una stringa che identifica il mittente del messaggio SMS

=item get_sender_text( )
 
Ritorna:  I<scalar> 
 
=cut

sub get_sender_text {
    my $self = shift;
	$self->_sms_client( )->get_sender_text( );
}

=pod

Ritorna l'ultima risposta valida ottenuta dopo una richiesta di invio o test del messaggio. 
(metodi B<send()>, B<send_unchecked_message()> e B<check_message()> ).
La risposta è codificata come hash refrence. L'hash contiene le seguenti chiavi:
 STATUS e MSGID.
STATUS rappresenta l'esito di invio o check del messaggio è può valore 0 in caso positivo.
MSGID contiene l'identificativo del messaggio, o un codice di errore nel caso in cui STATUS sia diverso da '0'.
      Nel caso di invio tramite remote activity , MSGID contiene l'ID dell'evento trasmesso.

=item get_response( )
 
=cut

sub get_response {
	shift->{MQ_RESPONSE};
}

=pod

Permette di modificare uno i più parametri di default relativi alla configurazione della coda e dei messaggi 
delle remote activity.
La coda di remote activity viene aperta in modalita' source, al primo tentativo di send() e viene chiusa
solo quando viene distrutto l'oggetto MQClient o quando viene modificato un parametro di configurazione
della coda stessa.
La funzione si aspetta in ingresso un hash reference, le chiavi presenti, rappresentano uno o più parametri
di configurazione, nel formato indicato nel meotodo B<new()>.

Parametri: I<hashref>

Uscita:    I<scalar>,  != undef in caso di successo.

es:
      change_queue_config( { SESSION_DESCRIPTION: "test-pid-$$" } );
   
=item change_queue_config( I<hashref> )
 
=cut

sub change_queue_config {
	my ($self, $qDef) = @_;
	
	if( ! $self->_validate_queue( $qDef ) ) {
		return undef;
	}
	
	my $q_changed = 0;
	for( @MQ_QUEUE_CONFIG_PARAMS ) {
		 if( exists $qDef->{$_} ) {
		     $self->{QUEUE}->{$_} = $qDef->{$_};
		     $q_changed = 1;
		 }
	}
	$self->_set_mq_handle( undef ) if $q_changed;
	
    for( @MQ_EVENT_CONFIG_PARAMS ) {
		 $self->{QUEUE}->{$_} = $qDef->{$_} if exists $qDef->{$_};
	}
	return 1;
}

=pod

Permette di modificare tutti i parametri di default della coda e dei messaggi delle remote activity.
La coda di remote activity viene aperta in modalita' source, al primo tentativo di send() e viene chiusa
solo quando viene distrutto l'oggetto MQClient o quando viene modificato un parametro di configurazione
della coda stessa.
La funzione si aspetta in ingresso un hash reference, le chiavi presenti, rappresentano uno o più parametri
di configurazione, nel formato indicato nel meotodo B<new()>.

Parametri: I<hashref>

Uscita:    I<scalar>,  != undef in caso di successo.

es:
      set_queue_config( 
                  { 
                     SOURCE_CONTEXT : 'TEST_INVIO_SMS'
                     TARGET_CONTEXT : 'TEST_INVIO_SMS',
                     SESSION_DESCRIPTION: "test-pid-$$",
                     SOURCE_REF     : 'INIVIO_MESSAGGIO_DI_TEST'
                  } 
               );

=item set_queue_config( )
 
=cut

sub set_queue_config {
	my ($self, $qDef ) = @_;

	return undef
	   if ! $self->_validate_queue( $qDef );
	   
	my $qConf = { };
	for( @MQ_QUEUE_CONFIG_PARAMS, @MQ_EVENT_CONFIG_PARAMS ) {
		$qConf->{$_} = $qDef->{$_} if exists $qDef->{$_};
	}
	$self->{QUEUE} = $qConf;
	$self->_set_mq_handle( undef );
	return 1;
}


=pod

Restituisce una reference ad un hash che contiene i parametri di default utilizzati per configurare l'interfaccia
verso le remote activity: coda e messaggi.
Ciasuna chiave presente nell'hash,  rappresenta un parametro di configurazione.
Si veda la documentazione del metodo B<new()> per avere l'elenco completo dei parametri.

Uscita:    I<hashref>
 
=item get_queue_config( )
 
=cut

sub get_queue_config {	
	return shift->{QUEUE};
}


sub _set_mq_handle {
    my $self = shift;
	$self->{MQ_HANDLE} = shift;
}

sub _get_mq_handle {
	my $self = shift;
	
    return $self->{MQ_HANDLE} if defined $self->{MQ_HANDLE};
      
	my $qDef = $self->get_queue_config( );
	my %argv = ( );
	for( @MQ_QUEUE_CONFIG_PARAMS ) {
	     $argv{$_} = $qDef->{$_} if exists $qDef->{$_};
	}
			
	$argv{SESSION_DESCRIPTION} = __PACKAGE__ . " PID: $$"
  	    if !exists $argv{SESSION_DESCRIPTION}; 
	
	my $mqh = eval { 
          SIRTI::ART::RemoteActivity::Source->new( %argv )
	};
	if( $@ || !defined $mqh ) {
		my $except = $@ || "SIRTI::ART::RemoteActivity::Source::new() restituisce undef";
		croak $except;
	}
    
	return $self->{MQ_HANDLE} = $mqh;
}

sub _validate_queue {
	my ($self, $qDef) = @_;
	
	if( ref($qDef) eq 'HASH' ) {
		
	    for( @MQ_QUEUE_CONFIG_PARAMS, @MQ_EVENT_CONFIG_PARAMS ) {
             if( $_ eq 'DB' ) {
                 if ( exists $qDef->{$_} && ! UNIVERSAL::isa( $qDef->{$_}, 'SIRTI::DB' ) ) {
	                  $self->last_error("Configurazione coda RemoteActivity::SOURCE non valida: parametro DB non è un oggetto SIRTI::DB");
                      return undef;
                 }
             }
             elsif( exists $qDef->{$_} && ref(\$qDef->{$_}) ne 'SCALAR' ) {
				 $self->last_error("Configurazione coda RemoteActivity::SOURCE non valida: parametro '$_' non è SCALAR");
                 return undef;
             }
        } 
        return 1;
 	}
 	
 	$self->last_error("Definizione coda RemoteActivity::SOURCE in formato non valido (non è un hashref)" );	
 	return undef;
}


=pod

Invia una richiesta di inoltro SMS, validando prima il messaggio e successivamente inoltrando la richiesta 
tramite remote activity al server centralizzato di competenza.
Il metodo B<send()> valida il messaggio, utilizzando un instanza dell'oggetto SMS::Client creato 
in modalita' TEST.
Se la validazione ha esito positivo, la richiesta di inivio viene inoltrata tramite remote activity,
aperendo la coda messaggi in modalita' source, nel caso in cui non sia ancora stata aperta, e specificando i
i parametri di invio SMS all'interno dei RA DATA del messaggio Remote Activity.

Se non viene indicato diversamente, il nome dell'evento RA associato alla richiesta di inoltro messaggio SMS
e 'SEND_SMS'.

=item send( I<BODY>, I<RECIPIENT_NAME>, [ I<SENDER_TEXT>, I<SERVICENAME> ] )
 
Parametri:  codificati come hash, nome_parametro => valore

            BODY                 => [scalare] : testo del messaggio
            RECIPIENT_NUMBER     => [scalare] : numero di telefono del destinatario SMS
            SENDER_TEXT          => [scalare] : stringa identificati mittente SMS (opzionale)
                                                se manca viene utilizzato il default
            SERVICENAME          => [scalare] : nome del servizio (instanza ART) che effettua la richiesta di inivio
 
Ritorna:    [scalar]
            in caso di successo una stringa alfanumerica diverso da 
                  SIRTI::SMS::Client::ERR_KO 
                  SIRTI::SMS::Client::ERR_BAD_PARAMETERS
 
=cut

sub send {
    my $self = shift;
    my %params = @_;
    
    my $res = $self->_check_message( %params );
    if( ( $res ne SIRTI::SMS::Client::ERR_KO ) && ( $res ne SIRTI::SMS::Client::ERR_BAD_PARAMETERS ) ) {
        $res = $self->_send_message( %params );
    } 
    return $res;
}


=pod

Invia una richiesta di inoltro SMS, senza validare preventivamente il messaggio ma inoltrando la richiesta 
direttamente al server centralizzato di competenza tramite remote activity.
La sintassi risulta uguale a quella prevista per il metodo B<send()>
 
=item send( I<BODY>, I<RECIPIENT_NAME>, [ I<SENDER_TEXT>, I<SERVICENAME> ] )
 
Parametri:  codificati come hash, nome_parametro => valore

            BODY                 => [scalare] : testo del messaggio
            RECIPIENT_NUMBER     => [scalare] : numero di telefono del destinatario SMS
            SENDER_TEXT          => [scalare] : stringa identificati mittente SMS (opzionale)
                                                se manca viene utilizzato il default
            SERVICENAME          => [scalare] : nome del servizio (instanza ART) che effettua la richiesta di inivio
 
Ritorna:    [scalar]
            in caso di successo una stringa alfanumerica diverso da 
                  SIRTI::SMS::Client::ERR_KO 
                  SIRTI::SMS::Client::ERR_BAD_PARAMETERS

 
=item send_unchecked_message( )
 
=cut

sub send_unchecked_message {
    my $self = shift;
    my %params = @_;     
    my $res = $self->_send_message( %params );
    return $res;
}
 
 
=pod

Restituisce l'esito di validazione di una richiesta di invio SMS. 
I metodo valida la richiesta utilizzando un instanza dell'oggetto SIRTI::SMS::Client 
creato in modalita' TEST. (metodo SIRTI::SMS::Client->send() )
La sintassi d'uso è uguale a quella prevista per i metodi B<send()> e B<send_unchecked_message()>.

=item send( I<BODY>, I<RECIPIENT_NAME>, [ I<SENDER_TEXT>, I<SERVICENAME> ] )
 
Parametri:  codificati come hash, nome_parametro => valore

            BODY                 => [scalare] : testo del messaggio
            RECIPIENT_NUMBER     => [scalare] : numero di telefono del destinatario SMS
            SENDER_TEXT          => [scalare] : stringa identificati mittente SMS (opzionale)
                                                se manca viene utilizzato il default
            SERVICENAME          => [scalare] : nome del servizio (instanza ART) che effettua la richiesta di inivio
 
Ritorna:    [scalar]
            in caso di successo una stringa alfanumerica diverso da 
                  SIRTI::SMS::Client::ERR_KO 
                  SIRTI::SMS::Client::ERR_BAD_PARAMETERS

=item check_message( )
 
=cut

sub check_message {
    my $self = shift;
    my %params = @_;   
    my $res = $self->_check_message( %params );
    return $res;
}


sub _sms_client { 
    shift->{SMS_CLIENT};
}


sub _check_message {
    my ($self) = shift;
    my %params = @_;
   
	my %args = ( );
	for( qw/BODY RECIPIENT_NUMBER SENDER_TEXT SERVICENAME/ ) {
		 $args{$_} = $params{$_} if exists $params{$_};
	}
    
    $args{SENDER_TEXT} = $self->_sms_client( )->get_sender_text( )
        if !exists $args{SENDER_TEXT};
    $args{SERVICENAME} = $self->{SERVICENAME} 
        if !exists $args{SERVICENAME} && exists $self->{SERVICENAME};

    my $res = $self->_sms_client( )->send( %args );
    $self->last_error( $self->_sms_client( )->last_error( ) );
    $self->{MQ_RESPONSE} = $self->_sms_client( )->get_response;
    return $res;
}

sub _send_message {
    my ($self) = shift;
    my %params = @_;

    my $ra_data = { };
    for( qw/BODY RECIPIENT_NUMBER SENDER_TEXT SERVICENAME/ ) {
		 $ra_data->{$_} = $params{$_} if exists $params{$_};
	}  
    $ra_data->{SENDER_TEXT} = $self->_sms_client( )->get_sender_text( )
        if !exists $ra_data->{SENDER_TEXT};
    $ra_data->{SERVICENAME} = $self->{SERVICENAME} 
        if !exists $ra_data->{SERVICENAME} && exists $self->{SERVICENAME};

    if( ref($params{NOTIFY_INFO}) eq 'HASH' ) {
        $self->_notify_handler->encode_notify_data( $params{NOTIFY_INFO}, $ra_data );
    }

    my $mqh = eval { $self->_get_mq_handle };
   if( $@ ) {
		$self->last_error( "Errore durante la connessione alla coda RA/SOURCE: $@" );
		$self->{MQ_RESPONSE} = {
			         STATUS => '-1',
			         MSGID  => undef
		};
		return SIRTI::SMS::Client::ERR_KO;
	}
   
    my $qDef = $self->get_queue_config( );
    my %event = ( );
    for( @MQ_EVENT_CONFIG_PARAMS ) {
		 $event{$_} = $qDef->{$_} if exists $qDef->{$_};
	}
 
    $event{SOURCE_REF} = __PACKAGE__ . ": PID:$$" if !exists $event{SOURCE_REF};   
    $event{EVENT} = RA_SEND_SMS_EVENT             if !exists $event{EVENT};       
    $event{DATA} = $ra_data;
    
    my $mid = eval { $mqh->insert( %event ) };
	if( $@ || !defined $mid ) {
		my $excep = $@ || $mqh->last_error || "evento ID mancante (SIRTI::ART::RemoteActivity:::Source->insert() restitusice 'undef')";
		$self->last_error( "Errore durante l'invio evento sulla coda RA/SOURCE: $excep" );
		$self->{MQ_RESPONSE} = {
			          STATUS => '-1',
			          MSGID  => undef
		};
		return SIRTI::SMS::Client::ERR_KO;
	}

    $self->{MQ_RESPONSE}->{STATUS} = '0';
    $self->{MQ_RESPONSE}->{MSGID} = $mid;
    return ($mid eq SIRTI::SMS::Client::ERR_KO) ? SIRTI::SMS::Client::ERR_OK : $mid;
}


#------------------------------------------------------------
# TEST MODULO
#------------------------------------------------------------

if( __FILE__ eq $0 ) {

   my $qDef = {
           SOURCE_CONTEXT => 'SMS_CLIENT',
           TARGET_CONTEXT => 'SMS_SENDER'
   };

   my $connect_string = shift;
   my %params = @ARGV;
   my $except;
   
   my $client = SIRTI::SMS::MQ::Client->new( %params, QUEUE => $qDef );
   
   #$client->change_queue_config( { SESSION_DESCRIPTION => 'TEST_invio_SMS_$$' } );
   #$client->change_queue_config( { EVENT => 'SEND_SMS' } );
   
   my $dbh = new SIRTI::DB( $connect_string );
   
   print "config: ", $client->change_queue_config( { DB => $dbh } ),"\n";
   
   #print "send: ", $client->send_unchecked_message( %params ),"\n";
   #$except = $client->last_error || ""; 
   #print "send-last-error: ", $except ,"\n";
   
   #$dbh->commit( );
   
   for(;;) {
        print "# ";
        my $line = <STDIN>;
    
        last if !defined $line;
        next if ($line =~ /^\s*$/ );
        
        if( $line =~ /^\s*commit\s*$/i ) {
            print "COMMIT...\n";
            $dbh->commit;
        }
        elsif( $line =~ /^\s*rollback\s*$/i ) {
            print "ROLLBACK...\n";
            $dbh->rollback;
        }
        elsif( $line =~ /^\s*unchecked_send\s*:(.+)$/i ) {
            my %v = ( eval "$1"  );
            if( $@ ) { print "EVAL_ERROR: $@\n"; next }
        
            print "unchecked-send: ", $client->send_unchecked_message( %v ),"\n";
            $except = $client->last_error || "";
            print "last-error: ", $except, "\n";
        }
        elsif( $line =~ /^\s*schedule-date\s*:\s*(\d{14})\s*$/ ) {
            my $date = $1;

            print "set schedule date to '$date'...\n";
            print "RET: ", $client->change_queue_config( { SCHEDULE_DATE => $date } ), "\n";
        }
        elsif( $line =~ /^\s*send\s*:(.+)$/i ) {
            my %v = ( eval "$1"  );
            if( $@ ) { print "EVAL_ERROR: $@\n"; next }
        
            print "send: ", $client->send( %v, NOTIFY_INFO => { CUSTOMER_NUMBER => 12 } ),"\n";
            $except = $client->last_error || "";
            print "last-error: ", $except, "\n";
        }
        elsif( $line =~ /^\s*check_message\s*:(.+)$/i ) {
            my %v = ( eval "$1"  );
            if( $@ ) { print "EVAL_ERROR: $@\n"; next }
        
            print "send: ", $client->check_message( %v ),"\n";
            $except = $client->last_error || "";
            print "last-error: ", $except, "\n";
        }
        elsif( $line =~ /^\s*quit\s*$/i ) {
            last;
        }
        else {
            print "?\n";
        }
   }
   print "Ok\n";  
}

1;
