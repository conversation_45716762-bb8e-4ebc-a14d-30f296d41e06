package SIRTI::SOAP::Anagrafica;
use SOAP::Lite;
use SIRTI::Err;

use constant {
	DEFAULT_USERID				=>  'SISTEMIADM'
	,DEFAULT_LEGALENTITY		=>  '23'
};


sub _get_from_file {
	my ($self,$accn)=@_;
	my ($f,$fl)=(undef,0);
	my %h=();
	delete $self->{ERR};
	if ($self->{SYM} eq '-' || open($f,'<',$self->{SYM})) {
		$f=*STDIN if $self->{SYM} eq '-';
		binmode ($f,':utf8');
		while(<$f>) {
			chop;
			next if /^\s*#/;
			my ($k,$v)=/\s*(\w+)\s+(.*)$/;
			next if !defined($k);
			$fl=1 if $k eq 'primeAccountName' && $v eq $accn;
			last if $fl &&  $k eq 'primeAccountName' && $v ne $accn;
			$h{$k}=$v if $fl;
		}
		close($f) if $f ne *STDIN;
		$self->{ERR}->{MSG}="$accn: non trovato in file ".$self->{SYM} if scalar(keys(%h)) == 0;
	}
	else {
		$self->{ERR}->{SYSERR}=$self->{SYM}.": errore apertura $!";
	}
	return defined($self->{ERR}) ? undef : \%h;
}

sub new {
	# parametri:
	#   SOAP_CONNECTION {			- obbligatorio
	#			HTTP_PROXY			- opzionale
	#   		HTTP_TIMEOUT		- opzionale	
	#			SOAP_NAME_SPACE		- obbligatorio
	#   		SOAP_PROXY			- opzionale
	#	}
	#   LEGALENTITY   (default costante DEFAULT_LEGALENTITY)
	#   USERID		  (default costante DEFAULT_USERID)
	# 	SYM 	=> nomefile			- simula  leggendo i valori non da remoto ma da nomefile 
	#									  ogni riga e' composta dalla coppia chiave <spazi> valore e la prima coppia deve essere primeAccountName <spazi> valore 
	#										le righe che iniziano con <spazi># sono ignorate 
	#										le righe non valide sono ignorate

	
	my $classname = shift;
	my %param=@_;
	my $self = \%param;
	
	SIRTI::Err::prec(defined($self->{SOAP_CONNECTION}) && ref($self->{SOAP_CONNECTION}) eq 'HASH',"parametro SOAP CONNECTION indefinito o non definito correttamente");
	SIRTI::Err::prec(defined($self->{SOAP_CONNECTION}->{SOAP_NAME_SPACE}),"parametro  SOAP_NAME_SPACE di SOAP_CONNECTION non definito");
	SIRTI::Err::prec(defined($self->{SOAP_CONNECTION}->{SOAP_NAME_SPACE}),"parametro  SOAP_NAME_SPACE di SOAP_CONNECTION non definito");
	$self->{LEGALENTITY}=DEFAULT_LEGALENTITY if (!defined($self->{LEGALENTITY}));
	$self->{USERID}=DEFAULT_USERID if (!defined($self->{USERID}));
	$self->{SOAP_DATATYPE}->{LEGALENTITY}=SOAP::Data->type('string')->name('legalEntity')->value($self->{LEGALENTITY});
	$self->{SOAP_DATATYPE}->{USERID}=SOAP::Data->type('string')->name('csrUserId')->value($self->{USERID});
	delete $self->{ERR};
	$ENV{HTTP_proxy} = $self->{SOAP_CONNECTION}->{HTTP_PROXY} if defined($self->{SOAP_CONNECTION}->{HTTP_PROXY});

	$self->{SOAPCLIENT}=
			SOAP::Lite
				  -> ns($self->{SOAP_CONNECTION}->{SOAP_NAME_SPACE})
				  -> proxy(
				  		$self->{SOAP_CONNECTION}->{SOAP_PROXY}
				  		,timeout => $self->{SOAP_CONNECTION}->{HTTP_TIMEOUT} 
				  		,proxy => ['http' => $self->{SOAP_CONNECTION}->{HTTP_PROXY}]
				  	);
	bless $self,$classname;
	return $self;
}



sub get_from_accn($$) {
	my ($self,$accn)=@_;
	return $self->_get_from_file($accn) if defined($self->{SYM});

	my $h=undef;
	delete $self->{ERR};
		
	my $resultdata = eval {
		my $customerDataRequest=SOAP::Data->type('customerDataRequest')->name('customerDataRequest')->value(
			[	
			$self->{SOAP_DATATYPE}->{LEGALENTITY}
			,SOAP::Data->type('string')->name('primeAccountName')->value($self->{LEGALENTITY}.$accn)
			,$self->{SOAP_DATATYPE}->{USERID}	
			] 
		);
		$self->{SOAPCLIENT}->getCustomerData2($customerDataRequest);
	};

	if ($@)  {
		$self->{ERR}->{SYSERR}=$@;
	}
	elsif (defined($resultdata))  {
		my $result=$resultdata->result();
		if (defined($result)) {
			if($result->{status} eq 'Success') {
				$h=$result->{customerData};
			}
			else {
				$self->{ERR}->{MSG}=$result->{message};
			}
		}
		else {
			$self->{ERR}->{SYSERR}="errore SOAP sconosciuto - 'SOAP::Lite' non ha ritornato i dati richiesti e non ha segnalato nessun errore"; 
		}
	}
	else {
		$self->{ERR}->{SYSERR}="errore SOAP sconosciuto - 'SOAP::Lite' ha ritornato un valore indefinito"; 	
	}
	return $h;
}


sub get_dataerror($) {
	my $self=shift;
	return undef if (!defined($self->{ERR}));
	return $self->{ERR}->{MSG};
}


sub get_systemerror($) {
	my $self=shift;
	return undef if (!defined($self->{ERR}));
	return $self->{ERR}->{SYSERR};
}



if (__FILE__ eq $0) {
################## per test #############

	package main;
	use strict;
	use warnings;
	
	binmode(STDOUT,':utf8');
	
	my $conn_sviluppo = {
		HTTP_TIMEOUT			=> 10
		,HTTP_PROXY				=> 'http://10.1.5.80:8080'
		,SOAP_NAME_SPACE		=> 'http://193.12.60.10/tele2itafullfileinterface.svInterface'
		,SOAP_PROXY				=> '**************************************/soap/rpc'
	};


	my $conn_produzione_del_bon = {
		HTTP_TIMEOUT			=> 10
		,HTTP_PROXY				=> 'http://10.1.5.80:8080'
		,SOAP_NAME_SPACE		=>  'http://193.12.60.11/tele2itafullfileinterface.svInterface'
		,SOAP_PROXY				=>  '**************************************/soap/rpc'
	};


	my $conn_produzione_daniela = {
		HTTP_TIMEOUT			=> 10
		,HTTP_PROXY				=> 'http://10.1.5.80:8080'
		,SOAP_NAME_SPACE		=>  'http://193.12.60.11/tele2ItaCoverage.Interface'
		,SOAP_PROXY				=>  '**************************************/soap/rpc'
	};



	my $sa = new SIRTI::SOAP::Anagrafica( 
		SOAP_CONNECTION => $conn_produzione_del_bon
	);

	use Data::Dumper;


	if (scalar(@ARGV) > 0) {
		for my $acc(@ARGV) {
			my $h=$sa->get_from_accn($acc);
			print Dumper(defined($h) ? $h : $sa->get_dataerror()),"\n";
		}
	}
	else {
		my $h=$sa->get_from_accn('*********');
		SIRTI::Err::check(defined($h),SIRTI::Err::nvl($sa->get_dataerror()).' ',SIRTI::Err::nvl($sa->get_systemerror()));
		print Dumper($h),"\n";

		$h=$sa->get_from_accn('xx*********');
		SIRTI::Err::check(!defined($h));
		SIRTI::Err::check(!defined($sa->get_systemerror()),$sa->get_systemerror());
		print $sa->get_dataerror()."\n";

		
		my ($t,$tmpfile)=(undef,"/tmp/$$");
		SIRTI::Err::check(open($t,'>'.$tmpfile),"$tmpfile: errore apertura $!");
		
		print $t <<end_cards
                 primeAccountName   ***********
 				legalProvince   AL
                 primeAccountName   23*********
 				legalProvince   MI
                 legalAddressLine1   VIA DELL ANNUNCIATA 27
                 postalProvince   MI
                 lastName   DI PIERRI
                 firstName   GIANFRANCO
                 primeAccountName   ***********
 				legalProvince   NO
end_cards
;

		close($t);

		my $sa1 = new SIRTI::SOAP::Anagrafica( 
			SOAP_CONNECTION => $conn_sviluppo
			,SYM	=> $tmpfile
		);

		$h=$sa1->get_from_accn('*********');
		SIRTI::Err::check(defined($h));
		print Dumper($h),"\n";
	}
}
else {
1;
}




		



