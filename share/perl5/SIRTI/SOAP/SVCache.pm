package SIRTI::SOAP::SVCache;
use strict;
use warnings;


# public 

use constant {
	RETRY_ERRCOUNT	=> 3
	,RETRY_SLEEP	=> 1
};


# private 

use MIME::Base64 qw(encode_base64 decode_base64);
use Storable qw(freeze thaw);        


use SIRTI::Err;
use SIRTI::SOAP::Anagrafica;
use SIRTI::DB;


sub _convert_from($$) { #static - converte da utf8 a db codifica
	my ($db,$s)=@_;
	return $db->get_params()->{NLS_LANG}=~/utf8/i 
		? $s
		: SIRTI::Nice::UTF8_to_nice($s);
}

sub _convert_to($$) { #static - converte da db codifica a utf8 
	my ($db,$s)=@_;
	return $db->get_params()->{NLS_LANG}=~/utf8/i
		? $s
		: SIRTI::Nice::nice_to_UTF8($s);
}


sub _is_header_from_cache($$$$) {
	my ($self,$dbaccn,$rdays,$compared_date)=@_;
	return 0 if defined($rdays) && $rdays eq '-2';
	print STDERR "DEBUG $dbaccn: test esistenza  header da cache\n" if $self->{DEBUG};
	my $sql="select count(*) from sv_cache where account_number=$dbaccn";
	$sql.=" and data_aggiornamento_remoto >= (sysdate - $rdays) " if defined($rdays);
	$sql.=" and data_aggiornamento_remoto > to_date(".$self->{DB}->quote($compared_date).",'YYYYMMDDHH24MISS')" if defined($compared_date);
	my $db=$self->{DB};
	my $rows=$db->fetchall($sql) || SIRTI::Err::db_fatal_error($db);
	return $rows->[0]->[0];
}


sub _get_data_from_cache($$) {
	my ($self,$dbaccn)=@_;
	print STDERR "DEBUG $dbaccn: lettura dati da cache\n" if $self->{DEBUG};
	my $sql="
		select 
			nome
			,valore 
		from 
			sv_cache_dati d
			,sv_cache     c
		where
			d.id = c.id 
			and c.account_number = $dbaccn
	";
	my $h={};
	my $db=$self->{DB};
	my $c = $db->create_cursor($sql) || SIRTI::Err::db_fatal_error($db);
	while(1) {
		my $row = $c->fetchrow_arrayref();
		last if (!defined($row));
		$h->{$row->[0]}= $row->[0] eq 'services' ? thaw(decode_base64($row->[1])) : _convert_to($db,$row->[1]);
	}
	SIRTI::Err::db_fatal_error($db) if SIRTI::DB::iserror();
	$c->finish();
	if (scalar keys %$h  == 0) {  # non esiste o e' vuoto ?
		return undef unless $self->_is_header_from_cache($dbaccn,undef,undef);
	}
	return $h;
}


sub _insert_cache($$$) {
	my ($self,$dbaccn,$h)=@_;
	return 0 if keys(%$h) == 0 && defined($self->{IGNORE_EMPTYACCS});
	print STDERR "DEBUG $dbaccn: nuovo elemento in cache\n" if $self->{DEBUG};
	my $sql="
		insert into sv_cache (
			id 								  
			,account_number										
			,data_aggiornamento_remoto 					
		)
		values (
			sv_cache_seq.nextval
			,$dbaccn
			,sysdate
		)
	";
	my $db=$self->{DB};
	my $n=$db->do($sql) || SIRTI::Err::db_fatal_error($db);
	for my $k(keys %$h) {
		my $dbk=$db->quote($k);
		my $dbv=$db->quote($k eq 'services' 
				? encode_base64(freeze($h->{$k})) 
				: _convert_from($db,$h->{$k}));			
		$sql="
			insert into sv_cache_dati (
				id
				,nome
				,valore
			)
			values (
				sv_cache_seq.currval
				,$dbk
				,$dbv
			)
		";
		$n=$db->do($sql) || SIRTI::Err::db_fatal_error($db);
	}
	$self->{CACHE_UPDATE}=1;
	return 1;
}


sub _update_cache($$$) {
	my ($self,$dbaccn,$h)=@_;
	return 0 if keys(%$h) == 0 && defined($self->{IGNORE_EMPTYACCS});
	print STDERR "DEBUG $dbaccn: aggiorna elemento da cache\n" if $self->{DEBUG};
	my $sql="
		update sv_cache
		set data_aggiornamento_remoto=sysdate
		where account_number=$dbaccn
	";
	my $db=$self->{DB};
	my $n=$db->do($sql) || SIRTI::Err::db_fatal_error($db);
	$n=0 if ($n eq '0E0');
	SIRTI::Err::check($n == 1,"$n: righe aggiornate != 1");
	
	$sql="select id from sv_cache where account_number=$dbaccn";
	my $rows=$db->fetchall($sql) ||  SIRTI::Err::db_fatal_error($db);
	SIRTI::Err::check(scalar(@$rows) == 1);
	my $id=$rows->[0]->[0];
	$sql="
		update sv_cache_dati
		set mark=null
		where id = $id
	";
	$n=$db->do($sql) || SIRTI::Err::db_fatal_error($db);
	for my $k(keys %$h) {
		my $dbk=$db->quote($k);
		my $dbv=$db->quote($k eq 'services' ? encode_base64(freeze($h->{$k})) : _convert_from($db,$h->{$k})); 
		$sql="update sv_cache_dati 
			set  valore=$dbv
				,mark='1'
			where 
				id = $id
				and nome=$dbk
		";
		$n=$db->do($sql) || SIRTI::Err::db_fatal_error($db);
		$n=0 if ($n eq '0E0');
		SIRTI::Err::check($n  <= 1,"$n: righe aggiornate > 1");
		if ($n == 0) {
			$sql="
				insert into sv_cache_dati (
					id
					,nome
					,valore
					,mark
				)
				values (
					$id
					,$dbk
					,$dbv
					,'1'
				)
			";
			$n=$db->do($sql) || SIRTI::Err::db_fatal_error($db);
		}
	} # for
		
	$sql="
		delete sv_cache_dati
		where id = $id
		and mark is null
	";
	$n=$db->do($sql) || SIRTI::Err::db_fatal_error($db);
	
	$self->{CACHE_UPDATE}=1;
	return undef;
}


sub _get_remote($$) {
	my ($self,$accn)=@_;
	print STDERR "DEBUG ".$self->{DB}->quote($accn).": lettura remota\n" if $self->{DEBUG};
	my $sa = $self->{ANAGRAFICA};
	return {} unless defined($sa);
	my $c=$self->{RETRY_ERRCOUNT};
	my $h=undef;
	$self->{STATS}->{ACC_REMOTE}+=1;
	while($c-- > 0) {
		$h=$sa->get_from_accn($accn);
		$h={} if defined($sa->get_dataerror());
		last if defined($h);
		$self->{STATS}->{REMOTE_TERR}+=1;
		sleep($self->{RETRY_SLEEP});
	}
	$self->{STATS}->{REMOTE_ERR}+=1 if !defined($h);
	$self->{SOAP_DATAERR}=$sa->get_dataerror();
	$self->{SOAP_SYSERR}=$sa->get_systemerror();
	$self->{REMOTE_ACCESS}=1;
	return $h;
}

# public 
sub new {
	# parametri:
	#	DB 							- obbligatorio - istanza di SIRTI::DB
	#   ANAGRAFICA             		- istanza di SIRTI::SOAP::Anagrafica - se non specificato una lettura in remoto ritorna un hash vuoto
	#   RETRY_ERRCOUNT				- facoltativo - numero di tentativi di lettura remota per errore - default RETRY_ERRCOUNT	
	#   RETRY_SLEEP					- facoltativo - secondi di attesa prima di un retry per errore - default RETRY_SLEEP
	#   FORCE_REMOTE_DAYS			- facoltativo - accedi in remoto se il dato in cache e' piu vecchio di FORCE_REMOTE_DAYS 
	#   								se e' -1  non viene mai fatto un accesso in remoto ma solo in cache
	#									se e' -2  viene fatto sempre un accesso in remoto  
	#	DEBUG						- attiva modalita di debug (log su STDERR)
	#   IGNORE_EMPTYACCS			- se definito non inserisce in cache gli account non esistenti o vuoti

	my $classname = shift;
	my %param=@_;
	my $self = \%param;
	SIRTI::Err::prec(defined($self->{DB}),'parametro DB non definito');
	
	$self->{RETRY_ERRCOUNT}=RETRY_ERRCOUNT if !defined($self->{RETRY_ERRCOUNT});
	$self->{RETRY_SLEEP}=RETRY_SLEEP if !defined($self->{RETRY_SLEEP});
	
	$self->{STATS} = {
		ACC				=> 0  		# numero di accessi
		,ACC_REMOTE		=> 0  		# numero di accessi remoti
		,REMOTE_ERR		=> 0		# numero errori remoto (senza contare le correzioni per retry)
		,REMOTE_TERR	=> 0		# totale numero errori remoto (contanto le correzioni per retry)
	};
	
	$self->{STATS_LABEL} = {
		ACC				=> 'numero totale di accessi'
		,ACC_REMOTE		=> 'numero di accessi remoti'
		,REMOTE_ERR		=> 'numero errori remoto (senza retry) '
		,REMOTE_TERR	=> 'numero errori remoto (con retry)   '
	};
	
	$self->{REMOTE_ACCESS}=0; 
	$self->{CACHE_UPDATE}=0;
	bless $self,$classname;
	return $self;
}


sub get_from_accn($$$) {
	my ($self,$accn,$comparedate)=@_;
	SIRTI::Err::prec(defined($accn),'account number non definito');
	my $db = $self->{DB};
	my $dbaccn=$db->quote($accn);
	$self->{STATS}->{ACC}+=1;
	delete $self->{SOAP_DATAERR};
	delete $self->{SOAP_SYSERR};
	### elimino lettura dati da Sev Cache
	#my $h=$self->_get_data_from_cache($dbaccn);
	my $h={};
	my $rdays = $self->{FORCE_REMOTE_DAYS};
	$self->{REMOTE_ACCESS}=0; 
	$self->{CACHE_UPDATE}=0;
	if (defined($rdays) && $rdays eq '-1') {
		$h={} if !defined($h);
		return bless $h;
	}
	if (!defined($h)) {  # il dato non esiste - accede in remoto
		$h=$self->_get_remote($accn);
		# non scrive nella cache
		#if (defined($h)) {  
		#	$self->_insert_cache($dbaccn,$h);
		#}
		#else { # errore di sistema
		#	print STDERR "DEBUG ".$self->{DB}->quote($accn).": errore systema SOAP \n" if $self->{DEBUG};
		#	$h=undef;
		#}
	}
	elsif (defined($rdays)) {   # il dato esiste e rdays e' definito
		unless ($self->_is_header_from_cache($dbaccn,$rdays,undef)) { # il dato in cache e' piu vecchio di $rdays
			my $newh=$self->_get_remote($accn);			
			if (defined($newh)) {
				$h=$newh;
				#$self->_update_cache($dbaccn,$h);
			}
			else { # errore di sistema
				print STDERR "DEBUG ".$self->{DB}->quote($accn).": errore systema SOAP \n" if $self->{DEBUG};
				$h=undef if $rdays eq '-2';
			}
		}	
	}
	elsif (defined($comparedate)) {
		unless ($self->_is_header_from_cache($dbaccn,undef,$comparedate)) { # il dato in cache e' piu vecchio di comparedate
			my $newh=$self->_get_remote($accn);			
			if (defined($newh)) {
				$h=$newh;
				#$self->_update_cache($dbaccn,$h);
			}
			else { # errore di sistema
				print STDERR "DEBUG ".$self->{DB}->quote($accn).": errore systema SOAP \n" if $self->{DEBUG};
				# $h=undef;
			}				
		}	
	}
	return $h;
}


sub get_db($) {  return $_[0]->{DB};}

sub get_soap_anagrafica($) { return $_[0]->{ANAGRAFICA};}

sub get_soap_dataerror($) { return $_[0]->{SOAP_DATAERR};}

sub get_soap_systemerror($) { return $_[0]->{SOAP_SYSERR};}

sub get_stats($) { return $_[0]->{STATS};}

sub get_stats_label($) { return $_[0]->{STATS_LABEL};}

sub set_debug($$) { 
	my ($self,$v)=@_;
	my $old=$self->{DEBUG};
	$self->{DEBUG}= $v ? 1 : 0; 
	return $old;
}

sub is_debug($) { return $_[0]->{DEBUG}; }

sub set_retry_errcount { 
	my ($self,$v)=@_;
	$v=RETRY_ERRCOUNT if (!defined($v));
	my $old=$self->{RETRY_ERRCOUNT};
	$self->{RETRY_ERRCOUNT} = $v;
	return $old;
}

sub get_retry_errcount($) { return $_[0]->{RETRY_ERRCOUNT};}

sub set_retry_sleep {
	my ($self,$v)=@_;
	$v=RETRY_SLEEP if (!defined($v));
	my $old=$self->{RETRY_SLEEP};
	$self->{RETRY_SLEEP} = $v;
	return $old;
}	


sub get_retry_sleep($) {  return $_[0]->{RETRY_SLEEP}; }


sub set_force_remote_days {
	my ($self,$v)=@_;
	my $old=$self->{FORCE_REMOTE_DAYS};
	$self->{FORCE_REMOTE_DAYS}=$v;
	return $old;
}

sub get_force_remote_days($) { return $_[0]->{FORCE_REMOTE_DAYS};}

sub is_remote_access($) { return $_[0]->{REMOTE_ACCESS};}


sub is_cache_updated($) { return $_[0]->{CACHE_UPDATE};}


sub clear_cache($) {
	my $self=shift;
	my $db=$self->{DB};
	my $r=$db->do("delete sv_cache_dati") || SIRTI::Err::db_fatal_error($db);
	$r=$db->do("delete sv_cache_attivita") || SIRTI::Err::db_fatal_error($db);
	$r=$db->do("delete sv_cache") || SIRTI::Err::db_fatal_error($db);
	$self->{CACHE_UPDATE}=1 if $r ne '0E0';
}

if (__FILE__ eq $0) {


############ TEST ################
package main;
use strict;
use warnings;
#use Devel::Cycle;
binmode(STDOUT,':utf8');

my $conn_sviluppo = {
	HTTP_TIMEOUT			=> 10
	,HTTP_PROXY				=> 'http://10.1.5.80:8080'
	,SOAP_NAME_SPACE		=> 'http://193.12.60.10/tele2itafullfileinterface.svInterface'
	,SOAP_PROXY				=> '**************************************/soap/rpc'
};

my $conn_produzione_del_bon = {
	HTTP_TIMEOUT			=> 10
	,HTTP_PROXY				=> 'http://10.1.5.80:8080'
	,SOAP_NAME_SPACE		=>  'http://193.12.60.11/tele2itafullfileinterface.svInterface'
	,SOAP_PROXY				=>  '**************************************/soap/rpc'
};

my $db = new SIRTI::DB($ENV{SQLID_DBU},{ DEBUG => undef});

my $sv = new SIRTI::SOAP::SVCache(
			DB 					=> $db
			,ANAGRAFICA 		=> new SIRTI::SOAP::Anagrafica( SOAP_CONNECTION => $conn_produzione_del_bon )
			,DEBUG				=> 1
);


print STDERR "svuota la cache\n";
$sv->clear_cache();

print STDERR "prima lettura - da remoto\n";
my $h=$sv->get_from_accn('550003608');
SIRTI::Err::check(defined($h) && scalar(keys(%$h)) > 0);
SIRTI::Err::check($sv->is_remote_access(),' non  ha acceduto da remoto ');
SIRTI::Err::check($sv->is_cache_updated(),' non  ha aggiornato la cache ');
my $n=$db->do("update sv_cache set data_aggiornamento_remoto = data_aggiornamento_remoto - 10") || SIRTI::Err::db_fatal_error($db);
$n=0 if ($n eq '0E0');
SIRTI::Err::check($n > 0);


print STDERR "\n\nseconda lettura - da cache\n";
$sv->set_force_remote_days(12);				
$h=$sv->get_from_accn('550003608');
SIRTI::Err::check(defined($h) && scalar(keys(%$h)) > 0);
SIRTI::Err::check(!$sv->is_remote_access(),' ha acceduto da remoto ');
SIRTI::Err::check(!$sv->is_cache_updated(),' ha aggiornato la cache ');


print STDERR "\n\nterza lettura - da remoto\n";
$sv->set_force_remote_days(2);				
$h=$sv->get_from_accn('550003608');
SIRTI::Err::check(defined($h) && scalar(keys(%$h)) > 0);
SIRTI::Err::check($sv->is_remote_access(),' non  ha acceduto da remoto ');
SIRTI::Err::check($sv->is_cache_updated(),' non  ha aggiornato la cache ');

print STDERR "\n\nquarta lettura - da cache\n";
$h=$sv->get_from_accn('550003608');			
SIRTI::Err::check(defined($h) && scalar(keys(%$h)) > 0);
SIRTI::Err::check(!$sv->is_remote_access(),' ha acceduto da remoto ');
SIRTI::Err::check(!$sv->is_cache_updated(),' ha aggiornato la cache ');

for my $k(sort keys %$h) {
	print "$k => ";
	if ($k eq 'services') {
		use Data::Dumper;
		print Dumper($h->{$k});
	}
	else  {
		print $h->{$k};
	}
	print "\n";
}


print STDERR "\n\nquinta lettura - da remoto\n";
$h=$sv->get_from_accn('X50003608');
SIRTI::Err::check(defined($h) && scalar(keys(%$h)) == 0);
SIRTI::Err::check($sv->is_remote_access(),' non  ha acceduto da remoto ');
SIRTI::Err::check($sv->is_cache_updated(),' non  ha aggiornato la cache ');

print STDERR "\n\nsesta lettura - da cache\n";
$h=$sv->get_from_accn('X50003608');
SIRTI::Err::check(defined($h) && scalar(keys(%$h)) == 0);
SIRTI::Err::check(!$sv->is_remote_access(),' ha acceduto da remoto ');
SIRTI::Err::check(!$sv->is_cache_updated(),' ha aggiornato la cache ');


$sv->set_force_remote_days(undef);


print STDERR "settima lettura - da remoto\n";
$h=$sv->get_from_accn('550003608',$db->get_sysdate('YYYYMMDDHH24MISS'));
SIRTI::Err::check(defined($h) && scalar(keys(%$h)) > 0);
SIRTI::Err::check($sv->is_remote_access(),' non  ha acceduto da remoto ');
SIRTI::Err::check($sv->is_cache_updated(),' non  ha aggiornato la cache ');

print STDERR "ottava lettura - da cache\n";
$h=$sv->get_from_accn('550003608','19700101000000');
SIRTI::Err::check(defined($h) && scalar(keys(%$h)) > 0);
SIRTI::Err::check(!$sv->is_remote_access(),' ha acceduto da remoto ');
SIRTI::Err::check(!$sv->is_cache_updated(),' ha aggiornato la cache ');

print STDERR "ottava lettura - da cache\n";
$h=$sv->get_from_accn('550003608',undef);
SIRTI::Err::check(defined($h) && scalar(keys(%$h)) > 0);
SIRTI::Err::check(!$sv->is_remote_access(),' ha acceduto da remoto ');
SIRTI::Err::check(!$sv->is_cache_updated(),' ha aggiornato la cache ');




$db->commit();
#find_weakened_cycle($db);
#find_weakened_cycle($sv);

}
else { 1; }








