package SIRTI::SOAP::SVNI;

use strict;
use warnings;
use integer;
use English '-no_match_vars';
use Carp;
use Data::Dumper;
use SOAP::Lite;

use base qw(Exporter);


use constant {
	RC_FAILURE						=> 'Failure'   # status di ritorno per parametri non validi
	,RC_SUCCESS						=> 'Success'  # status di ritorno per parametri validi
};


use constant {
	 DEFAULT_SOAP_NAMESPACE			=> 'http://91.80.45.34/tele2itafullfileinterface.svInterface'
	,DEFAULT_SOAP_PROXY				=> '*************************************/soap/rpc'
};


use constant {
	DEFAULT_PHONE_PREFIX			=> '39'
};

use constant {
	STATUS_IDLE 			=> 0  
	,STATUS_INUSE		 	=> 3   
	,STATUS_CLOSED			=> 9
	,STATUS_QUARANTINED 	=> 12
	,STATUS_BOOKED			=> 94
	,STATUS_PTS_OPEN		=> 52
};


our %STATUS=( 
	&STATUS_IDLE			=> 'Idle'
	,&STATUS_INUSE			=> 'In-use'
	,&STATUS_CLOSED			=> 'Closed'
	,&STATUS_QUARANTINED	=> 'Quarantined'
	,&STATUS_BOOKED			=> 'Booked'
	,&STATUS_PTS_OPEN		=> 'PTS-Open'
);


my %t=( 
		status_code => [ qw ( 
										STATUS_IDLE 
										STATUS_INUSE     
										STATUS_CLOSED
										STATUS_QUARANTINED
										STATUS_BOOKED
										STATUS_PTS_OPEN
							) 
		]
		,default_soap_connection	=> [ qw (  DEFAULT_SOAP_NAMESPACE 	 DEFAULT_SOAP_PROXY )]
		,rc_code	=> [ qw ( RC_FAILURE RC_SUCCESS )]
		,mix		=> [ qw ( DEFAULT_PHONE_PREFIX )]
);

our %EXPORT_TAGS=( all => [ map { @{$t{$_}} } keys %t ],%t); 
our @EXPORT_OK=( @{$EXPORT_TAGS{all}} );
our @EXPORT=();








my %PARAMS=(
			LEGALENTITY	=> {
								SOAP_TYPE 			=> 'string'
								,DEFAULT_VALUE		=> '23'
								,SOAP_NAME			=> 'legalEntity'
								,CHECK_VALUE		=> sub { $_[0]=~/^\d{2}$/ ? 1 : 0; }
								,PARAM_ORDER		=> 0
			}
			,PHONENUMBER => {
			
								SOAP_TYPE			=> 'string'
								,SOAP_NAME			=> 'phoneNumber'
								,CHECK_VALUE		=> sub { my $prefix=DEFAULT_PHONE_PREFIX; $_[0]=~/^$prefix\d{5,13}$/ ? 1 : 0; }
								,PARAM_ORDER		=> 1
			}
			,STATUS		=>  {
								SOAP_TYPE			=> 'string'
								,SOAP_NAME			=> 'status'
								,CHECK_VALUE		=> sub { exists $STATUS{$_[0]} ? 1 : 0; }
								,PARAM_ORDER		=> 2

			}
			,STARTDATE	=>  {
								SOAP_TYPE			=> 'string'
								,SOAP_NAME			=> 'startDate'
								,CHECK_VALUE		=> sub { $_[0]=~/^\d{4}-\d{2}-\d{2}$/ ? 1 : 0; }
								,DEFAULT_VALUE		=> undef
								,PARAM_ORDER		=> 3
			}
			,USERID		=> {
								SOAP_TYPE			=> 'string'
								,SOAP_NAME			=> 'csrUserId'
								,DEFAULT_VALUE		=> 'SIRTISISTEMI'
								,PARAM_ORDER		=> 4
			}
);

sub _check_params {
	my %params=@_;
	my @errs=();
	for my $p(keys %params) {
		my $ck=$PARAMS{$p};
		next unless defined $ck;
		my $value=$params{$p};
		if (defined $value) {
			if ($ck->{CHECK_VALUE}) {
				push @errs,{ 
							status		=>  RC_FAILURE
							,message 	=>  "'$value':  valore per chiave $p non valida"
				}  unless $ck->{CHECK_VALUE}->($value);
			}
		}
		else {
				push @errs,{
							status		=> RC_FAILURE
							,message	=> "$p: valore non definito per questa chiave"
				}  unless exists $ck->{DEFAULT_VALUE};
		}
	}
	return \@errs;
}

sub _prepare_params{
	my %params=@_;
	for my $p(keys %PARAMS) {
		$params{$p}=$PARAMS{$p}->{DEFAULT_VALUE} unless defined $params{$p} 
	}
	if (defined $params{PHONENUMBER} && defined $params{ADD_AUTOMATIC_PREFIX}) {
		my $pr=$params{ADD_AUTOMATIC_PREFIX};
		$pr=DEFAULT_PHONE_PREFIX if $pr=~/^\s*$/;
		$params{PHONENUMBER}=$pr.$params{PHONENUMBER} unless $params{PHONENUMBER}=~/^$pr/;
	}
	unless ($params{NO_CHECK_VALUES}) {
		my $errs=_check_params(%params);
		return { ERRS => $errs } if scalar(@$errs) > 0;
	}
	my @soap_data=();
	for my $p(keys %params) {
		my $ck=$PARAMS{$p};
		next unless defined $ck;
		my $value=$params{$p};
		print STDERR "costruito parametro soap ",$ck->{SOAP_NAME},' => (',$ck->{SOAP_TYPE},") '",$value,"'\n"
			if $params{DEBUG};
		push @soap_data,SOAP::Data->type($ck->{SOAP_TYPE})->name($ck->{SOAP_NAME})->value($value);
	}
	my $struct=SOAP::Data->type('changePhoneNumberStatusRequest')->name('changePhoneNumberStatusRequest')->value(\@soap_data);

	return { ERRS => [],DATA => $struct };
}


sub _fusion {
	my ($self,%params)=@_;
	my %p=%$self;
	for my $k(keys %params) {
		$p{$k}=$params{$k};
	}
	return wantarray ? %p : \%p;
}



sub _install_soap_client {
	my ($self,%p)=@_;
	if ($p{USE_SOAP_DEFAULT_CONNECTION}) {
		$p{SOAP_PROXY}=DEFAULT_SOAP_PROXY;
		$p{SOAP_NAMESPACE}=DEFAULT_SOAP_NAMESPACE;
	}
	return 0 unless $p{SOAP_PROXY} && $p{SOAP_NAMESPACE}; 
	if (!defined $self->{soap_changePhoneNumberStatus} || $p{SOAP_PROXY} ne $self->{SOAP_PROXY} || $p{SOAP_NAMESPACE} ne $self->{SOAP_NAMESPACE})  {
		$self->{soap_changePhoneNumberStatus}=SOAP::Lite->ns($p{SOAP_NAMESPACE})->proxy($p{SOAP_PROXY});
		$self->{SOAP_PROXY}=$p{SOAP_PROXY};
		$self->{SOAP_NAMESPACE}=$p{SOAP_NAMESPACE};
		return  1;
	}
	return 0;
}


sub new {
	my ($class,%params)=@_;
	my $self=bless \%params,$class;
	$self->_install_soap_client(%params);
	return $self;
}


sub changePhoneNumberStatus {
	my ($self,%params)=@_;
	my %p=$self->_fusion(%params);
	my $h=_prepare_params(%p);
	if (scalar(@{$h->{ERRS}})) {
		return wantarray ? @{$h->{ERRS}} : $h->{ERRS};
	}
	$self->_install_soap_client(%p);
	croak "parametri SOAP_PROXY e/o SOAP_NAMESPACE non specificati" unless defined $self->{soap_changePhoneNumberStatus};

	my $r=$self->{soap_changePhoneNumberStatus}->changePhoneNumberStatus($h->{DATA});
	unless (defined $r) {
		$self->{soap_last_result}=undef;
		my @resp=( { 	status		=>  RC_FAILURE
						,message 	=>  "il server remoto non ha risposto correttamente"
 					}
		);
		return wantarray ? @resp : \@resp; 
	}
	my $result=$r->result;
	print STDERR "risposta dal server remoto: ",Dumper($result),"\n" if $params{DEBUG};
	$self->{soap_last_result}=$result;
	my @out=$result->{status} eq RC_SUCCESS
		? () 
		: ref $result eq 'ARRAY'
			? @$result
			: ($result);
	return wantarray ? @out : \@out;
}


sub get_SOAP_last_result {
	my ($self,%params)=@_;
	return $self->{soap_last_result};
}

sub finish {
	my ($self,%params)=@_; 
	delete $self->{soap_changePhoneNumberStatus};
	delete $self->{soap_last_result}; 
}



__END__


=head1  NAME

SIRTI::SOAP::SVNI -  classe gestione SIRTI::SOAP::SVNI - (S)ingle (V)iew (Number) (I)nventory 

=cut

=head1 SYNOPSIS

use  SIRTI::SOAP::SVNI

=cut


=head1 DESCRIPTION


questo package e' una classe - istanziare con il metodo new


=head1 FUNCTIONS

questo modulo definisce le seguenti funzioni

new - costruttore 

	parametri:
			USE_SOAP_DEFAULT_CONNECTION => se vero usa i parametri di default per la connessione a SOAP
			SOAP_NAMESPACE		=>  soap namespace
			SOAP_PROXY			=>  soap proxy 		
			LEGALENTITY			=>  codice  regionale (default codice per italia)
			PHONENUMBER			=>  numero telefonico con prefisso internazione (39)
			STATUS				=>  codice di cambiamento (i codici validi sono implementati nell' hash esportato %STATUS)
			STARTDATE			=>  opzionale: data movimento nel formato YYYY-MM-DD
			USERID				=>  codice utente che effettua la variazione (se non  specificicato usa un suo default)
			NO_CHECK_VALUES		=>  se vero non effettua il check sui valori passati 
			ADD_AUTOMATIC_PREFIX => aggiunge,se non presente, il prefisso internazione specificato a PHONENUMBER
									se il valore associato e' una stringa nulla o a spazi viene aggiunto il default (costante DEFAULT_PHONE_PREFIX)

changePhoneNumberStatus - comunica  la variazione di stato su un numero telefonico
			i parametri solo gli stessi del costruttore ed hanno la precedenza se specificati
			il metodo ritorna un array di hash con chiavi 'status' e 'message' 	in caso di errore, altrimenti un array vuoto


get_SOAP_last_result - ritorna l'ultima risposta soap  avvenuta 


finish  - elimina gli oggetti interni istanziati 


=head1 EXPORT 

nessuno


=head1 EXPORT_OK 

	status_code - esporta le costanti relative al parametro STATUS:		
		STATUS_IDLE 		
		STATUS_INUSE		
		STATUS_CLOSED		
		STATUS_QUARANTINED 
		STATUS_BOOKED		
		STATUS_PTS_OPEN	

	default_soap_connection - esporta le costanti relative alla connessione SOAP  di default
		DEFAULT_SOAP_NAMESPACE				
		DEFAULT_SOAP_PROXY						

	rc_code	 - esporta le costanti per i codici di ritorno (chiave 'status') di changePhoneNumberStatus
		RC_FAILURE	
		RC_SUCCESS

	mix - esporta costanti non raggruppabili
		DEFAULT_PHONE_PREFIX   	 - prefisso telefonico internazionale di default

	all - esporta tutte le costanti esportabili




=head1 SEE ALSO 

nessuno


=head1 AUTHOR

	lorenzo bellotti, E<lt><EMAIL><gt>


=cut




