################################################################################
#
# SIRTI::Shell
#
################################################################################

=head1 NOME

B<SIRTI::Shell> - Classe base per l'implementazione di shell interattive

=head1 DIPENDENZE

=over

=item * Shell::Base v0.05+

=item * Text::Table v1.125+

=item * JSON        v2.21+

=back
 
=head1 UTILIZZO
  
  #!/usr/bin/env perl
  
  package HelloShell;
  
  use strict;
  
  use base qw(SIRTI::Shell);
  
  my $config = {
      commands => {
          hello => {
              help => "Say hello.",
              params => {
                  MANDATORY => {
                      TO => { help => "The recipient of hello" },
                  },
                  OPTIONAL => {
                      OTHER => { help => "Another recipient of hello" },
                  },
              },
              callback => sub {
                  my ( $self, $cmd, $params, $line ) = @_;
                  unless( $self->merge_params( $cmd, $params ) ) {
                      return $self->manage_error_msg( "ERROR: missing mandatory params" );
                  }
                  return $self->manage_error_msg(
                       "ERROR: you can't say hello to him"
                  ) if $params->{TO} eq 'Christian';
                  return $self->manage_success_msg( "Hello $params->{TO}" . ( defined $params->{OTHER} ? " and $params->{OTHER}" : "" ) . "!" );
              },
          },
      },
      prompt => 'helloshell> ',
  };
  
  sub intro { "\nhelloshell -- say hello to everyone\n\n" }
  
  sub outro { "Thanks for using helloshell!" }
  
  # rendo possibile usare la shell sia in maniera interattiva che in pipe da STDIN
  my $input = -t STDIN ? undef : \*STDIN;
  
  my %params = (
       HISTFILE             => glob("~/.helloshell_history")
      ,SHELL_INPUT_STREAM   => $input
      ,SHELL_BLOCK_ON_ERROR => 0 # se true interrompe l'elaborazione in caso di errore
  );
  $params{SHELL_OUTPUT_TO_FILE} = "$ENV{HOME}/helloshell.txt"; # salva la sessione sul file indicato
  
  my $sh = eval { HelloShell->new( $config, %params ); };
  if ( $@ ) {
      die "ERROR: unable to start helloshell: $@";
  }
  $sh->run( );
  
  exit 0;
  
=head1 ESEMPIO

  [longhi@dvmas007 ~]$ perl helloshell.pl
  
  helloshell -- say hello to everyone
  
  helloshell> hello { TO : 'Christian' }
  ERROR: you can't say hello to him
  helloshell> hello { TO : 'Rizzardo' }
  Hello Rizzardo!
  helloshell> hello { TO : 'Rizzardo', OTHER : "Ivan" }
  Hello Rizzardo and Ivan!
  helloshell> quit
  Thanks for using helloshell!

=head1 TODO

- (DONE) set/unset gestione parametri di default

- (DONE) lettura parametri da standard input

- (DONE) -  aggiungere flag validate nella funzione merge_params e validare con check_named_params.
  se manca isa il default e' scalar. merge params ritorna 1 in caso di successo, 0 se trova dei parametri mandatory
  mancanti o non validi se fa anche la validazione 

- (DONE) - paginazione orizzontale nell'help

- (DONE) - override della print, redirezione output e err su file esterno. nel metodo new puo'essere passato il
  parametro SHELL_OUTPUT_TO_FILE con il nome del file da aprire. in caso non si possa aprire il file viene lanciata
  una eccezione. l'output su STDOUT e' sempre presente. sul file di output viene inserito anche l'echo del comando

- (DONE) - rappresentazione output tipo array in formato tabellare 
     es. $self->array_to_table( \@header_array, \@value_matrix, $options )
     DONE AS:    $self->print_array_as_table( \@header, \@value, $options )
                 $options x il momento non utilizzato.
     
- (DONE) - intercettare errore ritornato dalla callback e implemntare il parametro STOP_ON_ERROR nella ->run()
	che se true ferma l'elaborazione in caso di errrore, default continua
     
- (DONE) - verificare se possibile ordinare l'output del comando help

- (OPTIONAL) - logging/trace utilizzando log4perl e flag messaggi estesi/brevi
          
- (OPTIONAL) sintassi shortpath per insere i parametri di un comando 
   es.  new_target  @-                [ richiede i singoli parametri da stdin ]
        new_target  @/tmp/test.txt    [ legge i parameri da un file esterno ]

- (OPTIONAL) gestione lettura string tramite flag (-e string ) da comando linea
   es.  ./rashell -e 'connect { db_string : "pippo" }' -e 'insert { name : "minnie" }'

- (OPTIONAL) problema sintattico dovuto alla possibilta' di assegnare valori a varabile nell'env tramite linea di comando
  es. n=9  connect { db_string : "pippo" }
  attualmente questa sintassi produce un errore gestito dal parseline
  (Shell::Base gestisce correttamente questa sintassi)

- (OPTIONAL) valutare gestione sub command 


=cut

package SIRTI::Shell;

use strict;
use Carp  qw/carp croak/;
use SIRTI::Base;
# uso il backend JSON::PP per supportare i metodi
# allow_barekey e allow_singlequote
BEGIN { $ENV{PERL_JSON_BACKEND} = 'JSON::PP' }
use JSON -support_by_pp;
use Data::Dumper;
use Text::Wrap;
use Text::Table;
use IO::Handle;

use base qw( Shell::Base SIRTI::Base );

our $VERSION = '0.1';

#Override
sub new 
{
    my $this    = shift;
    my $class   = ref($this) || $this;

    my $config = shift;
    my $json = new JSON;
    my $self = {};
	my %hparam = @_;
		
	return undef unless defined( $json );
	$json->allow_barekey( 1 );
	$json->allow_singlequote( 1 );
	
	$Text::Wrap::columns = 120;
	
    no strict "refs";
    for my $cmd ( keys %{$config->{commands}} ) {

            next if !exists $config->{commands}->{$cmd}->{callback};

	    my @strlines = ( );
	    my $tmp = wrap( '', '', ( defined $config->{commands}->{$cmd}->{help} ? $config->{commands}->{$cmd}->{help} : "" ) );
		
		if ( defined $config->{commands}->{$cmd}->{params} ) {
		    $tmp .= "\nparams:\n";
		    # calcolo il parametro con il nome piu' lungo
		    my $offset = 0;
			my $x = $config->{commands}->{$cmd}->{params}->{MANDATORY};
			while ( my ($k, $v) = each %{$x} ) {
				$offset = length($k)+4 > $offset ? length($k)+4 : $offset;
			}
			$x = $config->{commands}->{$cmd}->{params}->{OPTIONAL};
			while ( my ($k, $v) = each %{$x} ) {
				$offset = length($k)+4 > $offset ? length($k)+6 : $offset;
			}
			# scrivo l'help per i parametri
			$x = $config->{commands}->{$cmd}->{params}->{MANDATORY};
			while ( my ($k, $v) = each %{$x} ) {
				$tmp .= wrap( '  ', ' 'x($offset), sprintf( "%s:%s %s\n", $k, ' 'x($offset - length($k) - 4), $v->{help} ) );
			}
			$x = $config->{commands}->{$cmd}->{params}->{OPTIONAL};
			while ( my ($k, $v) = each %{$x} ) {
				$tmp .= wrap( '  ', ' 'x($offset), sprintf( "[%s]:%s %s\n", $k, ' 'x($offset - length($k) - 6), $v->{help} ) );
			}
		}
        *{'do_' . $cmd} = $config->{commands}->{$cmd}->{callback};
        *{'help_' . $cmd} = sub { $tmp; };
    }
 
    my $fout = undef;
    if( exists $hparam{SHELL_OUTPUT_TO_FILE} ) {
		  open( $fout, ">>", $hparam{SHELL_OUTPUT_TO_FILE} )
		    or die "ERROR: impossibile aprire il file di output comandi in '" .
		           $hparam{SHELL_OUTPUT_TO_FILE} . "'";	
                  $fout->autoflush( 1 );
	}
 
    $self = $class->SUPER::new( @_ );
	
    $self->{SIRTI_SHELL_CONFIG} = $config;
	$self->{SIRTI_SHELL_JSON} = $json;
	$self->{SIRTI_SHELL_CTX} = { 
	                    PARAMS                => { },
						SHELL_DEBUG           => 0,
						SHELL_BLOCK_ON_ERROR  => $hparam{SHELL_BLOCK_ON_ERROR},
						INPUT_STREAM          => $hparam{SHELL_INPUT_STREAM},
				      };
	if( $fout ) {
	    $self->{SIRTI_SHELL_CTX}->{SHELL_OUTPUT_FILENAME} = $hparam{SHELL_OUTPUT_TO_FILE},
		$self->{SIRTI_SHELL_CTX}->{SHELL_OUTPUT_FILE} = $fout;
	}
	return $self;
}

#-----------------------------------------------------------
# gestione messaggi di risposta della shell e trace/log file
#-----------------------------------------------------------

sub _manage_msg
{
	my $self = shift;
	my $result_code = shift() ? 1 : 0;
	my $short_message = shift;
	my $long_message = shift;
	if ( $self->{SIRTI_SHELL_CTX}->{SHELL_DEBUG} && defined $long_message ) {
		return [ $result_code, $long_message ];
	} else {
		return [ $result_code, $short_message ];
	}
}

sub manage_error_msg
{
	shift->_manage_msg( 1, @_ );
}

sub manage_success_msg 
{
	shift->_manage_msg( 0, @_ );
}

sub use_trace_extended
{
    my ( $self, $flag ) = @_;
	$self->{SIRTI_SHELL_CTX}->{SHELL_DEBUG} = $flag;
	return $self;
}

sub config 
{ 
    shift->{SIRTI_SHELL_CONFIG};
}

sub set_prompt
{
    my ($self, $prompt ) = @_;
	$self->config->{prompt} = $prompt;
	return;
}
 
 

#-----------------------------------------------------
# gestione lettura linea di comando da canale di input
#----------------------------------------------------- 
#
# le callback ricevono com input questo formato:
#
#    cb( $self, @args )
#
#  args ha il seguente formato:
#
#    args[0] : scalare nome del comando
#	 args[1] : hash parametri (tradotti dal json )
#	 args[2] : linea di comando grezza
# 
#Override
sub parseline
{
    my ($self, $line ) = @_;


    if( defined $line && ( $line =~ /^\s*(#|$)/ ) ) {
        return ('', {}  );
    }

    my ( $cmd, $env, @args ) = $self->Shell::Base::parseline( $line );
	
    if( !defined $cmd || $cmd eq '' ) {

        $self->print("Linea comando non valida o incompleta");
        return ('', $env, @args );
    }

 
	if( $cmd =~ /^(help|quit|exit|$)/i ) {
            my $l = $line;
            $l =~ s/^\s*\w+\s*//;
            $self->{SIRTI_SHELL_STRAIGHT_LINE} = $l;
	    return ( $cmd, $env, @args );
	}
	
	{ 
	   my $tmd = lc($cmd);
       
       if( exists $self->config->{commands}->{$cmd}->{preparse} &&
              ! $self->config->{commands}->{$cmd}->{preparse} )
       {
           $line =~ s/^\s*([^\s]+)\s*//;
           return ( $cmd, $self->{SIRTI_SHELL_CTX}, $cmd, { }, $line );
       }
       
	   return ( $cmd, $env, @args )
	          if( $tmd eq 'show_var' || $tmd eq 'unset_var' || $tmd eq 'unset_all' ||
	              $tmd eq 'extended_msg' || $tmd eq 'block_on_error' ||
				  $tmd eq 'shell_output_file' );
	}
	
	@args = ( );
	$args[0] = $cmd;
	$args[2] = $line;
	
	$line =~ s/^\s*([^\s]+)\s*//;
	
	if( $line eq '' ) {
	   $args[1] = { };
	}
	else {
	   $args[1] = eval { $self->{SIRTI_SHELL_JSON}->decode( $line ) };
	   
	   if( $@ ) {
	      $self->print( "ERROR: invalid data param format\n" );
		  return ( "", { } );
	   }
	   $args[1] = { } unless defined( $args[1] );
	}

	return ($cmd, $self->{SIRTI_SHELL_CTX}, @args );
}

#Override
sub readline 
{
    my ($self, $prompt) = @_;
    my $line;
	
    my $input = $self->{SIRTI_SHELL_CTX}->{INPUT_STREAM};
		
    if( defined( $input ) ) {
          my $useSO = $self->{SIRTI_SHELL_CTX}->{SHELL_OUTPUT_FILE};
	   
 
          if( $useSO ) {
			  $useSO->autoflush( 1 );
              local $| = 1;
              print $useSO $prompt;
          }
          else {
             $self->print( $prompt );
          }
         
          $line = <$input>;
          chomp $line if defined $line;

          if( $useSO ) {
              local $| = 1;
              print $useSO $line,"\n";
          }
          else {
             $self->print( $line );
          }
    }
    else {
	  $line = $self->Shell::Base::readline( $prompt );	
    }
    return $line;
}

#Override
sub precmd
{
   my ($self, $line) = @_;
   
   return $line;
}


#Override
sub prompt
{
    my $v = shift->config->{prompt};
    return defined($v) ? $v : '> ';
}

#Override
sub print
{
    my ($self, @lines ) = @_;
	
	if( $self->{SIRTI_SHELL_CTX}->{SHELL_OUTPUT_FILE} ) {
           local $| = 1;
           $self->{SIRTI_SHELL_CTX}->{SHELL_OUTPUT_FILE}->autoflush( 1 );
	   print {$self->{SIRTI_SHELL_CTX}->{SHELL_OUTPUT_FILE}} @lines ;
	}
	
	push @{ $self->{ TIEFILE } }, @lines;
    return $self->SUPER::print(@lines);
}

#Override
 sub postcmd 
 {
    my ($self, $output) = @_;
   
    if( ref( $output ) eq 'ARRAY' ) {
       
	    if( ( $output->[0] ne 0 ) && $self->{SIRTI_SHELL_CTX}->{SHELL_BLOCK_ON_ERROR} ) {

	        if( $output->[1] ) {
                  $self->print( $output->[1], "\n" );
		    }
		    else {
		          $self->print( "ERROR: comando non riuscito, senza messaggio di errore\n" );
		    }
   	        $self->quit;
		   
		    #this lines should not be reached...
		    $self->print( "Warn: block on error enabled, exiting...\n" );
		    exit( 31 );
	    }
	    $output = $output->[1];
   }
   return $output;
 }

 
sub merge_params
{
    my ($self, $cmd, $params, $validate ) = @_; 
	my $mcmd = $self->{SIRTI_SHELL_CONFIG};
	
	#no mandatory parameters are given...
	return 1 if not exists $mcmd->{commands}->{$cmd}->{params}->{MANDATORY};
	  
	#step1: merge or die ... :o)
    my $href = $mcmd->{commands}->{$cmd}->{params}->{MANDATORY};
		
	foreach my $cmd ( keys %{$href} ) {
	        if( not exists $params->{$cmd} ) {
			     unless( $self->env_var_exists( $cmd ) ) {
				        #error: missed parameters;
				        return 0;		
				 }
				 $params->{$cmd} = $self->env_var_get( $cmd )   
			}
	}
	
	#step2: try to validate params
	if( $validate ) {
	        my $validator_hash = {  };
			my $errMsg;
			
	        foreach my $cmd ( keys %{$href} ) {
			     $validator_hash->{$cmd} = { %{$href->{$cmd}} };
				 $validator_hash->{$cmd}->{isa} = 'SCALAR'
				      unless exists $validator_hash->{$cmd}->{isa};
			}
	        
			return  $self->check_named_params(
			                    ERRMSG                => \$errMsg,
					            IGNORE_EXTRA_PARAMS   => 0,
						        PARAMS                => $params,
						        MANDATORY             => $validator_hash,
						        OPTIONAL              => { } );	
	}
	
	return 1;
}


sub get_param
{
    my ($self, $key, $href ) = @_;
    my $var_env = $self->{SIRTI_SHELL_CTX}->{PARAMS};
	
	return 
	   exists $href->{$key} ? 
	            $href->{$key} : ( exists $var_env->{$key} ? $var_env->{$key} : undef );              
}

  
#--------------------------------------------
# gestione hash map dei parametri di default
#--------------------------------------------

sub do_show_var
{
    my ($self, @args ) = @_;
	
	if( !scalar(@args) ) {
	   if( scalar( %{$self->{SIRTI_SHELL_CTX}->{PARAMS}} ) ) {
	       my $dumper = Data::Dumper->new(  [ $self->{SIRTI_SHELL_CTX}->{PARAMS} ]  ); $dumper->Varname( " " );
	       $self->print( $dumper->Dump );
	   }
	}
	else {
	   my $href = $self->{SIRTI_SHELL_CTX}->{PARAMS};
	   
	   while( my ( $k, $v ) = each %{$href} ) {
	        print $k, " = ";
	        if( ref($v) ) {
			     my $dumper = Data::Dumper->new( [ $v ] ); $dumper->Varname( " " );
			     print $dumper->Dump, "\n";
		    }
			else {
			     print $v, "\n";
			}
	   }
	}
    return ;
}

sub help_show_var
{
    return "visualizza il valore di un parametro di default (definito nel hash map di enviroment)\n" .
	       "o l'interno hash map se il comando viene utilizzato senza argomenti opzionali\n" .
	       "  es.  show_var";
}

sub do_set_var
{
    my ( $self, $cmd, $params, $line ) = @_;
	
	my $env = $self->{SIRTI_SHELL_CTX}->{PARAMS};
	while( my ($k, $v ) = each %{$params} ) {
	    $self->print( "Setting var $k\n" );
	    $env->{$k} = $v;	
	}
    return;
}

sub help_set_var
{
    return "aggiunge un parametro di default nel hash map di environment\n" .
	 	   '  es.  set_var { CONNECT_STRING : "remote_act/remote_act@dbdev" }';
}

sub do_unset_var
{
    my ($self, @args ) = @_;
	my $href = $self->{SIRTI_SHELL_CTX}->{PARAMS};
	
    foreach my $k ( @args ) {
	    if( exists $href->{$k} ) {
		    $self->print( "Removing var $k\n" );
			delete $href->{$k};
		}
	}
	
    return ;
}

sub help_unset_var
{
    return "rimuove un parametro di default dal hash map di enviroment\n" .
	 	   "  es.  unset  CONNECT_STRING [ parN.. ]\n" .
		   "       unset  CONNECT_STRING USER_ID";
}

sub do_unset_all
{
    my( $self, @args ) = @_;
	my $href = $self->{SIRTI_SHELL_CTX}->{PARAMS};
	
	foreach my $k ( keys %{$href} ) {
	   $self->print( "Remove var $k\n" );
	}
	%{$href} = ( );
    return;
}

sub help_unset_all
{
    return "rimuove tutti i parametri di default definiti nel hash map di environment\n" .
    		"  es.  unset_all";
}

sub env_var_exists
{
    my ( $self, $name ) = @_;
	return exists $self->{SIRTI_SHELL_CTX}->{PARAMS}->{$name};
}

sub env_var_get
{
    my ( $self, $name ) = @_;
	return ( exists $self->{SIRTI_SHELL_CTX}->{PARAMS}->{$name} ?
	          $self->{SIRTI_SHELL_CTX}->{PARAMS}->{$name} : undef );
}

sub do_block_on_error
{
    my ($self, @args ) = @_;
	
	do {{
	
	  last if( scalar( @args ) < 1 );
	 
	  return "ERROR: valore non consentito"
	         if( $args[0] ne '0' && $args[0] ne '1' );
	
	  $self->{SIRTI_SHELL_CTX}->{SHELL_BLOCK_ON_ERROR} = $args[0];
	}};
	
	return "block on error flag " . 
	  ( $self->{SIRTI_SHELL_CTX}->{SHELL_BLOCK_ON_ERROR} ? "abilitato" : "disabilitato" );
}


sub help_block_on_error
{
    return "abilita/disabilita la terminazione automatica della Shell quando un comando restituisce errore\n" .
			"  es.  block_on_error                 # visualizza l'impostazione corrente\n" .
			"  es.  block_on_error 1               # abilita la terminazione automatica\n" .
			"  es.  block_on_error 0               # disabilita la terminazione automatica";
}


sub do_shell_output_file
{
    my ($self, @args ) = @_;
	my $fout = $self->{SIRTI_SHELL_CTX}->{SHELL_OUTPUT_FILE};
	
	do {{
	
	  last if( scalar( @args ) < 1 );
	 
	  if( $args[0] eq "-" ) {
	      if( $fout ) {
		      close( $self->{SIRTI_SHELL_CTX}->{SHELL_OUTPUT_FILE} );
			  $self->{SIRTI_SHELL_CTX}->{SHELL_OUTPUT_FILE} = $fout = undef;
			  $self->{SIRTI_SHELL_CTX}->{SHELL_OUTPUT_FILENAME} = undef;
			  last;
		  }
	  }

	  if( !open( $fout, ">$args[0]" ) ) {
	      return [ 1, "ERROR: impossibile aprire file '". $args[0] . "'" ] ;
	  }
	  else {
		  $self->{SIRTI_SHELL_CTX}->{SHELL_OUTPUT_FILE} = $fout;
		  $self->{SIRTI_SHELL_CTX}->{SHELL_OUTPUT_FILENAME} = $args[0];
	  }
	   
	}};
	
	return "shell output rediretto " . 
	    ( defined $fout ? 
	           "sul file '". $self->{SIRTI_SHELL_CTX}->{SHELL_OUTPUT_FILENAME}."'"  :  "su standard output"  );
}


sub help_shell_output_file
{
    return "permette la redirezione dei messaggi della Shell su file/dispositivo esterno\n" .
	       "  es.  shell_output_file                 # visualizza l'impostazione corrente\n".
		   "  es.  shell_output_file -               # imposta come canale di output lo standard OUTPUT\n" .
		   "  es.  shell_output_file /tmp/trace.txt  # redirige i messaggi prodotti dalla shell nel file /tmp/trace.txt";
}

sub do_extended_msg
{
    my ($self, @args) = @_;
	
	do {{
	
	  last if( scalar( @args ) < 1 );
	 
	  return "ERROR: valore non consentito"
	         if( $args[0] ne '0' && $args[0] ne '1' );
	
	  $self->{SIRTI_SHELL_CTX}->{SHELL_DEBUG} = $args[0];
	}};
	
	return "trace/log esteso " . 
	  ( $self->{SIRTI_SHELL_CTX}->{SHELL_DEBUG} ? "abilitato" : "disabilitato" );
}

sub help_quit
{
    return "termina la shell.Utilizzando il flag 'forced' (quit forced) viene forzata l'uscita annullando eventuali modifiche in corso";
}


sub quit
{
    my ( $self ) = @_;

    if( exists $self->{SIRTI_SHELL_CONFIG}->{commands}->{quit} ) {
        my $res = &{$self->{SIRTI_SHELL_CONFIG}->{commands}->{quit}->{callback}}( $self, 'quit', { }, $self->{SIRTI_SHELL_STRAIGHT_LINE} );
        if( defined $res && $res->[0] ) {
            $self->print( $res->[1] . "\n" );
            return; 
        } 
    }

    $self->SUPER::quit();
    exit( 0 );
}


sub help_extended_msg
{
    return "abilita/disabilita i messaggi di trace esteso\n".
	       "  es.  extended_msg 0   # disabilita messaggi estesi\n".
		   "       extended_msg 1   # abilita trace dettagliato\n".
		   "       extended_msg     # restituisce l'impostazione in uso";
}

#Override
sub helps
{
	return sort shift->SUPER::helps( @_ );	 
}

sub print_array_as_table
{
    my ( $self, $header, $data, $opts ) =  @_;
	
	my $tt = Text::Table->new( @{$header} );
	
	my $headerCols = scalar( @{$header} );
	my $dataCount = scalar( @{$data} );
	
	for( my ($n,$inc) = (0,0); $n < $dataCount; $n+=$inc ) {
	     $inc = $n+$headerCols > $dataCount ? $dataCount-$n : $headerCols;
		 $tt->add( @{$data}[$n..($n+$inc-1)] );
	}
	
	$self->print( $tt->table, "\n" );
	return;
}


1;
