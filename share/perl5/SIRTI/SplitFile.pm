package SIRTI::SplitFile;

use File::Temp qw/ tempfile /;


sub new($$$$) {
	my ($classname,$prefix,$digits,$handlers)=@_;
	my $self={
		PREFIX	=> $prefix
		,DIGITS => $digits
		,H		=> $handlers
	};
	bless $self,$classname;
	$self->set_destroy_on_close(1) if $self->is_temporary_file();
	return $self;
}

sub set_params($@) {
	my $self=shift;
	my %params=@_;
	my %old=%$self;
	for my $k(%params) {
		$self->{$k}=$params{$k};
	}
	return \%old;
}

sub open($) {
	my $self=shift;
	my $count=$self->{CURRENTCOUNT};
	my $digits=$self->{DIGITS};
	my $prefix=$self->{PREFIX};
	my $h=$self->{H};
	$count=0 if (!defined($count));
	$self->close() if ($count > 0);
	$count++;
	if (defined($prefix)) {
		if (length($prefix) == 0) { # temp file
			my %param=();
			$param{SUFFIX}=defined($digits) ? sprintf("-%0${digits}d",$count) : '';
			$param{SUFFIX}.=$self->{SUFFIX} if defined($self->{SUFFIX});
			($self->{FD},$self->{CURRENT_NAME}) = tempfile(%param);
		}
		else {
			$self->{CURRENT_NAME}= defined($digits) ? sprintf("$prefix-%0${digits}d",$count) : $prefix;
			my $f=undef;
			$self->{FD}=$f if (open($f,">".$self->{CURRENT_NAME}));
		}
	}
	else {
		$self->{FD}=*STDOUT;	
		$self->{CURRENT_NAME}='-';
	}
	if (defined($self->{FD})) {
		$self->{CURRENTCOUNT}=$count;
		&{$h->{AFTER_OPEN}}($self) if (defined($h) && defined($h->{AFTER_OPEN}));
	}
	return $self->{FD};
}


sub close($) {
	my $self=shift;
	my $fd=$self->{FD};
	return if (!defined($fd));
	my $h=$self->{H};
	&{$h->{BEFORE_CLOSE}}($self) if (defined($h) && defined($h->{BEFORE_CLOSE}));
	if ($fd ne *STDOUT) {
		close $fd;
		unlink $self->getcurrentfilename() if $self->is_destroy_on_close();
	}
	$self->{FD}=undef;
	&{$h->{AFTER_CLOSE}}($self) if (defined($h) && defined($h->{AFTER_CLOSE}));
}


sub set_destroy_on_close($$) { 
	my ($self,$flag)=@_;
	if ($flag) {
		$self->{DESTROY_ON_CLOSE}=1; 
	}
	else {
		delete $self->{DESTROY_ON_CLOSE};
	}
}

sub is_temporary_file($) { 
	my $prefix=$_[0]->{PREFIX};
	return defined($prefix) && length($prefix) == 0;
}

sub is_destroy_on_close($) { return exists  $_[0]->{DESTROY_ON_CLOSE}; }

sub getfd($) { return $_[0]->{FD};}

sub getprefix($) { return $_[0]->{PREFIX};}

sub getdigits($) {return $_[0]->{DIGITS};}


sub getcurrentcount($) {return $_[0]->{CURRENTCOUNT}; }

sub gethandler($$) {
	my ($self,$name)=@_;
	my $h=$self->{H};
	return undef if(!defined($h));
	return $h->{$name};
}


sub sethandler($$$) {
	my ($self,$name,$value)=@_;
	$self->{H}->{$name}=$value;
	return undef;
}

sub getcurrentfilename($) { return $_[0]->{CURRENT_NAME}; }


sub DESTROY {
	my $self=shift;
	$self->close();
}


if (__FILE__ eq $0) {
	####  test ###  
	package main;
	
	my $f = new SIRTI::SplitFile('',1);  # crea un file temporaneo
	$f->set_destroy_on_close(0);
	$f->set_params(SUFFIX	=> '.xml');
	$f->open();
	my $name=$f->getcurrentfilename();
	print "nome: ",$name,"\n";
	my $fd=$f->getfd();
	print $fd  "prova\n";
	$f->close();
	print `/bin/cat $name`;
}
else {1;}
