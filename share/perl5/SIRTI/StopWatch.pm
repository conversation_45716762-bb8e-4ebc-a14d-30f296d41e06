################################################################################
#
# SIRTI::StopWatch
#
################################################################################
=pod

=head1 NAME

B<SIRTI::StopWatch> - Cronometro

=head1 SYNOPSIS

  use SIRTI::StopWatch;
  use Data::Dumper;
  
  my $t = SIRTI::StopWatch->new();
  do_something();
  $t->lap( 'DO_SOMETHING' );
  do_something_other();
  $t->lap();
  do_something_more();
  print Dumper( $t->report() );

  $VAR1 = {
    '_LAP_0' => '0.169912',
    'DO_SOMETHING' => '0.00034',
    '_LAP_2' => '0.001416',
    '_TOTAL' => '0.171668'
  };

=head1 DESCRIPTION

Utility per calcolare tempi parziali e totali delle elaborazioni.

=cut

package SIRTI::StopWatch;

use strict;
use warnings;
use Time::HiRes qw(gettimeofday tv_interval);
use Tie::IxHash;
use Data::Dumper;

=head1 METHODS

=head2 new( [$label] )

Crea e fa partire un nuovo cronometro. Se viene passata una label il primo parziale avrà tale nome,
altrimenti C<_LAP_0>.

In alternativa è possibile specificare un B<HASHREF> con le seguenti chiavi:

=over 4

=item B<RACE_NAME>: indica il nome della gara

=item B<FILE>: il nome di un file a cui accodare i conteggi

=item B<ENABLED>: se valorizzato ad 1 abilita tutti i conteggi, altrimenti non viene fatto nulla (utile in ambiente di produzione)

=back

=cut
sub new {
	my $this = shift;
	my $class = ref($this) || $this;
	my $self = bless( {}, $class );
	my $label = '_LAP_0';
	my $params = shift;
	$self->{ENABLED} = 1;
	if (ref($params) eq 'HASH') {
		$label = $params->{LABEL} if defined $params->{LABEL};
		$self->{ENABLED} = $params->{ENABLED}
			if exists($params->{ENABLED});
		if ($self->{ENABLED}) {
			$self->{RACE_NAME} = $params->{RACE_NAME} || qx/echo -n \$(uuidgen -r)/ || 'unknown';
			if ($params->{FILE}) {
				my $old_umask = umask(0111);
				if (open($self->{FH}, '>>'.$params->{FILE})) {
					# Non bufferizzo l'output sul filehandle
					my $old_fh = select($self->{FH});
					$! = 1;
					select($old_fh);
				} else {
					$self->{FH_ERROR} = $!;
				}
				umask($old_umask);
			}
		}
	} else {
		$label = $params if defined $params;
	}
	$self->{_TIMERS} = [ { label => $label, start => [gettimeofday] } ];
	#print STDERR Dumper($self);
	return $self;
}

sub DESTROY {
	my $self = shift;
	close $self->{FH} if defined $self->{FH};
}


sub _write {
	my $self = shift;
	my $label = shift || '';
	my $elapsed = shift || -1;
	if ($self->{FH}) {
		print { $self->{FH} } sprintf("%s;%s;%f\n", $self->{RACE_NAME}, $label, $elapsed);
	}
}


=head2 lap( [$label] )

Chiude il parziale precedente e fa partire un nuovo parziale. Se viene passata una label il parziale avrà tale nome,
altrimenti C<_LAP_N>.
Restituisce il valore ritornato da C<Time::HiResr-E<gt>gettimeofday()>.
Se viene passata una label ed esiste un parziale con lo stesso nome non viene fatto nulla, viene restituito undef
e viene stampato un warning su STDERR. La stessa cosa succede se viene utilizzata la label riservata C<_TOTAL>.   

=cut
sub lap {
	my $self = shift;
	return '' unless $self->{ENABLED};
	my $label = shift || '_LAP_' . scalar ( @{$self->{_TIMERS}} );
	print STDERR "WARNING: _TOTAL is a reserved word, nothing has been done\n"
		and return undef
			if $label eq '_TOTAL';
	print STDERR "WARNING: label already set, nothing has been done\n"
		and return undef
			if grep { $_->{label} eq $label } @{$self->{_TIMERS}};
	my $time = [gettimeofday];
	my $cur = pop @{$self->{_TIMERS}};
	$cur->{stop} = $time;
	$cur->{elapsed} = tv_interval( $cur->{start}, $time );
	$self->_write($cur->{label}, $cur->{elapsed});
	push @{$self->{_TIMERS}}, ( $cur, { label => $label, start => $time } );
	return $time;
}

=head2 report( )

Restituisce un HASHREF ordinato (tied) che ha come chiavi le label e come valori i tempi intermedi.
La chiave aggiuntiva C<_TOTAL> contiene il tempo totale trascorso dalla creazione del cronometro.

=cut
sub report {
	my $self = shift;
	return {} unless $self->{ENABLED};
	tie my %report, 'Tie::IxHash', ();
	my $i = 0;
	for ( $i=0; $i<scalar(@{$self->{_TIMERS}})-1; $i++ ) {
		$report{$self->{_TIMERS}->[$i]->{label}} = $self->{_TIMERS}->[$i]->{elapsed};
	}
	my $time = [gettimeofday];
	$report{$self->{_TIMERS}->[$i]->{label}} = tv_interval( $self->{_TIMERS}->[$i]->{start}, $time );
	$self->_write($self->{_TIMERS}->[$i]->{label}, $report{$self->{_TIMERS}->[$i]->{label}});
	$report{_TOTAL} = tv_interval( $self->{_TIMERS}->[0]->{start}, $time );
	$self->_write('_TOTAL', $report{_TOTAL});
	return \%report;
}

if ( __FILE__ eq $0 ) {	
	my $t = SIRTI::StopWatch->new();
	for ( my $i=0; $i<1000000; $i++ ) {};
	$t->lap();
	for ( my $i=0; $i<1000; $i++ ) {};
	$t->lap( 'PIPPO' );
	for ( my $i=0; $i<2000; $i++ ) {};
	$t->lap( '_TOTAL' ); # reserved word
	for ( my $i=0; $i<2000; $i++ ) {};
	$t->lap( 'PIPPO' ); # label already set
	for ( my $i=0; $i<2000; $i++ ) {};
	$t->lap();
	for ( my $i=0; $i<2000; $i++ ) {};
	print Dumper $t->report();
	$t = undef;

	$t = SIRTI::StopWatch->new({
		 ENABLED => 1
		,FILE => '/tmp/StopWatch.log'
		,RACE_NAME => 'Wackey Race'
	});
	for ( my $i=0; $i<1000000; $i++ ) {};
	$t->lap();
	for ( my $i=0; $i<1000; $i++ ) {};
	$t->lap( 'PIPPO' );
	for ( my $i=0; $i<2000; $i++ ) {};
	$t->lap( '_TOTAL' ); # reserved word
	for ( my $i=0; $i<2000; $i++ ) {};
	$t->lap( 'PIPPO' ); # label already set
	for ( my $i=0; $i<2000; $i++ ) {};
	$t->lap();
	for ( my $i=0; $i<2000; $i++ ) {};
	print Dumper $t->report();


} else {
	1;
}
