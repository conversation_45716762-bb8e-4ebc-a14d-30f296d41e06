package SIRTI::TOOLS::Driver;

use strict;
use warnings;
use Carp;

my $last_error = '';

my $warn = sub {
	my $msg = shift;
	last_error($msg);
	warn $msg;
	return undef; 
};

sub last_error {
	my $msg = shift;
	$last_error = $msg if defined $msg;
	return $last_error; 
}

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	# Parameters:
	my $params = {@_};
	# scalar	DRIVER
	# hashref	NEW_PARAMS
	# arrayref	METHODS
	# scalar	USELIB
	# scalar	DEBUG
	
	# Sintassi
	my $usage = sub {
		my $errmsg = shift;
		my $msg = "";
		$msg .= "$class : $errmsg\n\n" if defined $errmsg;
		$msg .= "Usage:\n";
		$msg .= "\t$class";
		$msg .= '->new( DRIVER => $driver, NEW_PARAMS => \%params, METHODS => \@methods [, USELIB => $path [, DEBUG => $debug ]] )';
		$msg .= "\n\n";
		return $warn->($msg);
	};

	# Controlli sui parametri
	return $usage->('Missing DRIVER parameter!') 
		unless defined $params->{DRIVER};
	return $usage->('Bad type for DRIVER: must be SCALAR!') 
		unless ref($params->{DRIVER}) eq '';
	return $usage->('Missing NEW_PARAMS parameter!') 
		unless defined $params->{NEW_PARAMS};
	return $usage->('Bad type [' . ref($params->{NEW_PARAMS}) . '] for NEW_PARAMS: must be HASHREF!') 
		unless ref($params->{NEW_PARAMS}) eq 'HASH';
	return $usage->('Missing METHODS parameter!') 
		unless defined $params->{METHODS};
	return $usage->('Bad type for METHODS: must be ARRAYREF!') 
		unless ref($params->{METHODS}) eq 'ARRAY';
	return $usage->('Unknown path specified by USELIB!') 
		if defined($params->{USELIB}) && !-d $params->{USELIB};
	return $usage->('Bad type for USELIB: must be SCALAR!') 
		unless ref($params->{USELIB}) eq '';
	return $usage->('Bad value for DEBUG!') 
		if defined $params->{DEBUG} && !grep /^$params->{DEBUG}$/, qw(0 1);
	$params->{DEBUG} = 0 unless defined $params->{DEBUG};
	
	# Aggiunta path di ricerca
	eval "use lib $params->{USELIB}" if $params->{USELIB};
	return $warn->("USELIB error:\n$@")
		if $@;
		
	# Caricamento DRIVER
	eval "use $params->{DRIVER}";
	return $warn->("USE DRIVER error:\n$@")
		if $@;

	# Istanziazione oggetto
	my $self = undef;
	eval {
		#$self = $params->{DRIVER}->new(@{$params->{NEW_PARAMS}});
		$self = $params->{DRIVER}->new( $params->{NEW_PARAMS} );
	};
	return $warn->("DRIVER NEW error:\n$@")
		if $@;
	return $warn->("DRIVER NEW returns undefined object")
		unless defined($self);
	
	# Verifica presenza METHODS
	push @{$params->{METHODS}}, 'new';
	foreach my $method (@{$params->{METHODS}}) {
		#print STDERR $class . " : testing method $method : ";
		return $warn->("Unavailable METHOD ->'$method'")
			unless $self->can($method);
		#print STDERR "\n";
	}
	
	return $self;
}

if ( __FILE__ eq $0 ) {
	
	use strict;
	use warnings;
	use Data::Dumper;
	
	die "Test usage: $0 PACKAGE PARAM1:PARAM2:PARAM3:... METHOD1:METHOD2:METHOD3:...\n" unless scalar(@ARGV);
	
	my $d = SIRTI::TOOLS::Driver->new(
		 DRIVER		=>	$ARGV[0]
		,NEW_PARAMS	=>	[ split(/:/, ( $ARGV[1] || '' ) ) ]
		,METHODS	=>	[ split(/:/, ( $ARGV[2] || '' ) ) ] 
		 
	);
	if ( defined $d ) {
		print Dumper($d);
	} else {
		print SIRTI::TOOLS::Driver::last_error();
	}
		
	
	
} else {
	1;
}
