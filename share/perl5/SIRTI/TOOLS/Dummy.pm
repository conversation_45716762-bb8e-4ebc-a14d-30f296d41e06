package SIRTI::TOOLS::Dummy;

use strict;
use warnings;
use Carp;
use Data::Dumper;

sub new() {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = shift;
	#print STDERR $class . "->" . "new() => " . Dumper($params) . "\n\n";
	return bless { CLASS => $class, INFO => [] }, $class;
}

sub get_data {
	$_[0]->{INFO} = [ $_[0]->{CLASS} , 'Invocato metodo ->get_data()'];
	return {
		 INDIRIZZO	=> 'VIA EDOARDO PETTINARRI 14'
		,FRAZIONE	=> 'MELEGNANELLO'
		,CAP		=> '26828'
		,COMUNE		=> 'TURANO LODIGIANO'
		,PROVINCIA	=> 'LO'
		,NUMTELCOMPL	=> '0377909090'
	};
}
sub set_data {
	my ($self, $params) = @_;
	$self->{INFO} = [ ];
	foreach my $key (keys %{$params}) {
		push @{$_[0]->{INFO}}, $key . ': ' . $params->{$key};
	}
	return [];
}
sub check_data {
	$_[0]->{INFO} = [ $_[0]->{CLASS} , 'Invocato metodo ->check_data()'];
	return [];
}
sub get_info { $_[0]->{INFO} }
sub get_title { "Dummy driver for testing SIRTI::TOOLS::Driver" }

1;
