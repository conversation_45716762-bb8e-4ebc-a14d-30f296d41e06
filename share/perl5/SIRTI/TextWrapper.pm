################################################################################
#
# SIRTI::TextWrapper
#
################################################################################

=head1 NAME

B<SIRTI::TextWrapper> - Modulo per la formattazione di un generico testo

=head1 SYNOPSIS

  ##### Impostazione durante l'istanziazione dell'oggetto
  my $w = SIRTI::TextWrapper->new(
                                    columns   => 30
                                 );

  ### oppure....
  
  ##### Impostazione attraverso i metodi dell'oggetto
  my $w = SIRTI::TextWrapper->new();
  $w->columns(30);
  $w->forceWrap(0);
  $w->wrapChar('#');

  ##### Formatta un testo
  print $w->wrap($text);

=head1 DESCRIPTION

Questo modulo consente di formattare un testo facendolo rientrare nei margini impostati dall'attributo/metodo B<columns> .

=cut

	package SIRTI::TextWrapper;

	use strict;
	use warnings;
	use Carp 'verbose';
	use Text::Wrapper;
	use SIRTI::Err;


################################################################################
#  P R O P R I E T A '   P U B B L I C H E
################################################################################

	our $VERSION = '0.01';


################################################################################
#  M E T O D I   P R I V A T I
################################################################################

	sub _init_wrapper {
		my $self = shift;
		$self->{_wrapper_}	= Text::Wrapper->new( columns => $self->{columns} );
	}

	sub _init_wrapChar {
		my $self = shift;
		$self->{_wrapChar_}			= ($self->{wrapChar} ? $self->{wrapChar} . ' ' : '');
		$self->{_wrapCharLength_}	= length($self->{_wrapChar_});
		$self->{_wrapMargin_}		= (' ' x $self->{_wrapCharLength_});
	}


################################################################################
#  M E T O D I   P U B L I C I
################################################################################

=pod

=head1 METHODS

=over 4

=cut

=pod


=item new

E' possibile creare un'istanza dell'oggetto inizializzando le seguenti proprieta' (relative agli omonimi metodi pubblici):

=over 8

=item * columns

=item * wrapChar

=item * forceWrap

=item * body_start

=back

=cut
	sub new {
		my $this = shift;
		my $class = ref($this) || $this;
		my %self =	(
						 'columns'		=> 80
						,'wrapChar'		=> '...'
						,'forceWrap'	=> 1
						,'body_start'	=> '' ##### ONLY FOR RETRO-COMPATIBILITY WITH Text::Wrapper
					);
		my $self = bless \%self, $class;
		while (@_) {
			my $METHOD = shift;
			SIRTI::Err::system_error("Unknown parameter '$METHOD'") unless $self->can($METHOD);
			$self->$METHOD(shift);
		}
		$self->_init_wrapper() unless exists $self->{_wrapper_};
		$self->_init_wrapChar() unless exists $self->{_wrapChar_};
		return $self;
	}


=pod

=item columns

Imposta l'ampiezza che dovra' avere il testo formattato (default: 80)

=cut
	sub columns {
		my $self = $_[0];
		$self->{columns} = $_[1] if defined $_[1] && $_[1] =~ m/^\d+$/;
		$self->_init_wrapper();
		return $self->{columns};
	}


=pod

=item forceWrap

Definisce se le righe lunghe devono essere splittate in modo forzato (default: 1)

=cut
	sub forceWrap {
		my $self = $_[0];
		$self->{forceWrap} = $_[1] if defined $_[1] && $_[1] =~ m/^[01]$/;
		return $self->{forceWrap};
	}


=pod

=item wrapChar

Imposta il carattere che indica le righe lunghe splittate forzatamente (default: '...')

=cut
	sub wrapChar {
		my $self = $_[0];
		$self->{wrapChar} = $_[1] if defined $_[1];
		$self->_init_wrapChar();
		return $self->{wrapChar};
	}


=pod

=item body_start

Solo per compatibilita' col package text::Wrapper ma non ha nessuna funzionae in SIRTI::TextWrapper.

=cut
	sub body_start {
		my $self = $_[0];
		return $_[1];
	}


=pod

=item wrap

Restituisce il testo passato formattato secondo le impostazioni dell'oggetto

=cut
	sub wrap {
		my $self = shift;
		my $text = shift;
		return '' unless defined $text;
		$text = $self->{_wrapper_}->wrap($text);
		return $text unless $self->{forceWrap};
		my $forced = 0;
		my @text =	map	{	
							my $r = $_;
							if ( length($r) > $self->{columns} ) {
								my @r = ();
								while ( length($r) > 0 ) {
									push @r, substr($r, 0, $self->{columns});
									substr($r, 0, $self->{columns}) = '';
								}
								$r = join("\n".$self->{_wrapChar_}, @r);
								$forced++;
							}
							$r;
						} split(/\n/, $text);
		if ( $forced ) {
			@text = map	{
							my $x = $_;
							$x = $self->{_wrapMargin_} . $x if $self->{_wrapChar_} ne substr($x, 0, $self->{_wrapCharLength_});
							$x;
						} @text;
		}
		return join("\n", @text); # . "\n";
	}

=pod

=back

=head1 NOTES

=over 4

=item Ritoni a capo

I ritorni a capo presenti nel testo originale vengono preservati.

=item Righe lunghe non interrompibili

Le righe non interrompibili (in quanto mancanti di spazi) che eccedono in lunghezza il limite imposto da B<columns> possono essere interrotte impostando ad '1' il l'attributo B<forceWrap>.
In questo caso, sulla sinistra di tali, righe verra' posto il carattere di segnalazione '...'; tale carattere puo' essere modificato attraverso l'attributo B<wrapChar>

=back

=cut


###################################
#
# Testing the module...
#
###################################
if (__FILE__ eq $0) {

	####  test ###  
	package main;

	use strict;
	use warnings;
	use Carp 'verbose';
	use Data::Dumper;

	my $columns = shift(@ARGV) || 60;
	my $wrapChar = shift(@ARGV);

#	my $w = SIRTI::TextWrapper->new();
#	$w->columns( $columns );
#	$w->forceWrap( 1 );
#	$w->wrapChar( $wrapChar );

	my $w = SIRTI::TextWrapper->new(
		 columns	=> $columns
		,forceWrap	=> 1
		,wrapChar	=> $wrapChar
	);

	my $text;
	$text = qq(<pre>Lorem ipsum dolor amet sit amet ipso facto.</pre>
<pre>Lorem ipsum dolor <b>amet</b> sit amet ipso facto.</pre>
<pre>Lorem ipsum dolor amet sit amet ipso facto.</pre>
<pre>Lorem ipsum dolor amet sit amet ipso facto.</pre>
	);
#<pre/>aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa</pre>

	$text = qq(<span class"pippo">  <pre>NUMTEL: CAP non congruente con il prefisso telefonico (NUMTELCOMPL => '0131226128' CAP_CORRETTI => '15010,15013,15014,15016,15022,15023,15024,15026,15028,15029,15040,15042,15043,15044,15045,15046,15048,15050,15051,15052,15053,15054,15055,15056,15057,15058,15059,15062,15065,15070,15072,15073,15077,15079,15100' PREFISSO => '0131' CAP => '99999' )
COMUNARIO: cap  non congruente con il comune (CAP_CORRETTI => '15100' COMUNE => 'ALESSANDRIA' CAP => '99999' )</pre></span>);

	print $w->wrap($text);

	exit 0;

} else {

	1;

}

__END__

=pod

=head1 BUGS

Eventuali bug riscontrati dovranno essere segnalati su B<ART - Global Services>: L<https://www.artnet.sirtisistemi.net/ARTIT/art> aprendo un apposito DEV-TICKET.

=head1 HISTORY

=over

=item Ver. 0.01

Prima release del modulo

=back

=head1 AUTHOR

Alvaro Livraghi <<EMAIL>>

=cut
