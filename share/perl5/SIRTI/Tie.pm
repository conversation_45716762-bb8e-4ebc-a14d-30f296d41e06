package SIRTI::Tie;



package SIRTI::Tie::Hash;
	use base SIRTI::Tie;

#	TIEHASH nomeclasse, LISTA
#    FETCH this, chiave
#    STORE this, chiave, valore
#    DELETE this, chiave
#    CLEAR this
#    EXISTS this, chiave
#    FIRSTKEY this
#    NEXTKEY this, ultimachiave
#    DESTROY this
#    UNTIE this
    
	sub TIEHASH($@) {
		my $classname = shift;
		my %bind=();
		my $self={ BIND	=> \%bind,PARAMS => \@_ };
		return bless $self,$classname;
	}
   
    sub FETCH($$) { return $_[0]->{BIND}->{$_[1]} }
    	
    sub STORE($$$) { $_[0]->{BIND}->{$_[1]}=$_[2];}
    
    sub DELETE($$) {  delete $_[0]->{BIND}->{$_[1]}}
    
    sub CLEAR($) {  %{$_[0]->{BIND}}=();}
    
   	sub EXISTS($$)  { return exists $_[0]->{BIND}->{$_[1]}  }
   	
   	sub FIRSTKEY($) { 
   		my @keys = keys %{$_[0]->{BIND}}; 
   		my $k=shift @keys; 
   		$_[0]->{KEYS}=\@keys; 
   		return $k;
   	}
   	
    sub NEXTKEY($$) { return shift @{$_[0]->{KEYS}} }
    
    sub DESTROY($) { $_[0]->UNTIE(); }
        
    sub UNTIE($) { %{$_[0]->{BIND}}=(); }


package SIRTI::Tie::Array;
#	TIEARRAY nomeclasse, LISTA
#    FETCH this, chiave
#    STORE this, chiave, valore
#    FETCHSIZE this
#    STORESIZE this, conteggio
#    CLEAR this
#    PUSH this, LISTA
#    POP this
#    SHIFT this
#    UNSHIFT this, LISTA
#    SPLICE this, scostamento, lunghezza, LISTA
#    EXTEND this, conteggio
#    DESTROY this
#    UNTIE this
    

package SIRTI::Tie::Handle;
#	TIEHANDLE nomeclasse, LISTA
#    READ this, scalare, lunghezza, scostamento
#    READLINE this
#    GETC this
#    WRITE this, scalare, lunghezza, scostamento
#    PRINT this, LISTA
#    PRINTF this, formato, LISTA
#    BINMODE this
#    EOF this
#    FILENO this
#    SEEK this, posizione, dadove
#    TELL this
#    OPEN this, modo, LISTA
#    CLOSE this
#    DESTROY this
#    UNTIE this

package SIRTI::Tie::Scalar;
#	TIESCALAR nomeclasse, LISTA
#    FETCH this,
#    STORE this, valore
#    DESTROY this
#    UNTIE this

if (__FILE__ eq $0) {
	package main;
	use strict;
	use warnings;
	use Data::Dumper;
	my %h=('a',1,'b',2);
	tie  %h,'SIRTI::Tie::Hash';
	$h{'c'}=3;
	$h{'d'}=4;
	print STDERR join(",",keys %h),"\n";
	print STDERR join(",",values %h),"\n";
	print STDERR exists $h{'c'},"\n";
	print STDERR (exists($h{'x'}) ? 1 : 0),"\n";
	print STDERR $h{'c'},"\n";
	delete $h{'c'};
	print STDERR join(",",keys %h),"\n";
	@h{qw(a b c d)}=qw(A B C D);
	
	print STDERR Dumper(\%h),"\n";
	
	print STDERR "each\n";
	while(my ($k,$v)=each %h) {
		print  STDERR $k,' => ',$v,"\n";
	}
	untie %h;
	print STDERR Dumper(\%h),"\n";
	
}


1;
