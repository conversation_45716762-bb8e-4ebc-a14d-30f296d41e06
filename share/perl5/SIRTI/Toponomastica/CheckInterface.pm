package SIRTI::Toponomastica::CheckInterface;

use strict;
use warnings;
use SIRTI::Err;


#use constant {
#	ATTR_PROP_VALUE_OPTIONAL		=>  0
#	,ATTR_PROP_NAME_DEFINED			=>  1
#	,ATTR_PROP_VALUE_DEFINED		=>  2
#};



sub _do_check() {
	SIRTI::Err::internal_error('metodo astratto');
	return [];
}


sub _filter_msg {
	my ($self,$h)=@_;
	return $h;
}

sub get_checktype() {
	SIRTI::Err::internal_error('metodo astratto');
	return '';
}


#sub set_attrs_property() {
#	my $self=shift;
#	my $prop=defined($_[0]) && length(ref($_[0])) ? shift : undef;
#	for my $n(@_) {
#		if (ref($n) eq 'HASH') {
#			for my $k(keys %$n) {
#				my $prop=$n->{$k};
#				$self->{ATTR_PROP}->{$k}=$prop if $prop >  SIRTI::Err::nvl($self->{ATTR_PROP}->{$k},ATTR_PROP_VALUE_OPTIONAL);		
#			}
#		}
#		elsif (length(ref($n)) == 0) {
#			SIRTI::Err::internal_error("2# parametro (prop) non definito");
#			$self->{ATTR_PROP}->{$n}=$prop if $prop  >  SIRTI::Err::nvl($self->{ATTR_PROP}->{$n},ATTR_PROP_VALUE_OPTIONAL);
#		}
#	}
#}
#
#
#
#sub get_attrs_property() { 
#	my $self=shift;
#	return %{$self->{ATTR_PROP}}  if scalar(@_) == 0;
#	my $props=\@_;
#	return grep($_,map { 
#			$self->{ATTR_PROP}->{$_} == $props->[0] ? 1 : 0;       
#	}  keys(%{$self->{ATTR_PROP}}))  if (scalar(@$props) == 1);
#	my %h=();
#	for my $k(keys  %{$self->{ATTR_PROP}}) {
#		$h{$k}=$self->{ATTR_PROP}->{$k} if grep($_ ==  $self->{ATTR_PROP}->{$k},@$props);
#	}
#	return %h;
#}


sub new {
	my $class=shift;
	my %params=@_;
	#  NO_INPUT_FIELDS_INTO_MSG   => nei messaggi di errori non emette i campi di input
	#  EMIT_MSG_ON_OK			  => aggiunge il messaggio in caso di ok
	#  FORCE_CHECK_CLASS_TYPE     => forza le classi di check
	$params{MSGS}=[];
	my $self=\%params;
#	$self->{ATTR_PROP}={};
	my $c = bless $self,$class;
	
#	for my $prop( qw(ATTR_VALUE_OPTIONAL ATTR_NAME_DEFINED ATTR_VALUE_DEFINED)) {
#		if (defined($c->{$prop})) {
#			$c->set_attrs_property(eval {$prop},@{$c->{$prop}});
#			delete $c->{$prop};
#		}
#	}	

	return $c;
}




#  VALUES	=>   { keys:  ID COMUNE PROVINCIA CAP PARTTOPON DESCINDIRIZZO NUMCIVICO PARITA ESPONENTE }
#  FORCE_CHECK_CLASS_TYPE  []   => forza le classi di check

sub do_check($$@) { 
	my $self=shift;
	my %params=@_;
	my $msgs=[];
	$params{VALUES}={} if ref($params{VALUES}) ne 'HASH';
	push @$msgs,@{$self->_do_check(%params)};
	for my $k(@$msgs) {
#		$self->_filter_msg($k);
#		$k->{CHECKTYPE}=$self->get_checktype() if !defined($k->{CHECKTYPE});
		$k->{ID}=$params{VALUES}->{ID} if !defined($k->{ID}) && defined($params{VALUES}->{ID});
	}
	if ($self->{EMIT_MSG_ON_OK}  && scalar(@$msgs) == 0) {
		my $k={};
#		$k->{CHECKTYPE}=$self->get_chektype();
		$k->{ID}=$params{VALUES}->{ID} if defined($params{VALUES}->{ID});
		$k->{MSG}=$self->{EMIT_MSG_ON_OK};
		push @$msgs,$k;
	}
	$self->{MSGS}=$msgs;
	return $msgs;
}

sub get_messages($) { return $_[0]->{MSGS};}

sub get_string_messages($)  {
	my $self=shift;
	my @a=();
	map {
		my $c=$_;
		my $s='';
		$s.=$c->{ID}.':' if defined($c->{ID}); 
		$s.=$c->{CHECKTYPE}.':' if defined($c->{CHECKTYPE}); 
		$s.=SIRTI::Err::nvl($c->{MSG},$c->{MSG},'<NOMSG>');
		my ($fl,$s1)=(0,' (');
		for my $k(keys %$c) {
			next if grep /^$k$/,('CHECKTYPE','MSG','ID');
			$s1.="$k => ";
			$s1.=(defined($c->{$k}) ? "'".$c->{$k}."'" : '<UNDEF>');
			$s1.=' ';
			$fl=1;
		}
		$s1.=")";
		$s.=$s1 if $fl;
		push @a,$s;
	} @{$self->{MSGS}};
	return \@a;
}

sub print_messages($$$) {
	my ($self,$file,$subst)=@_;
	$file=*STDOUT if !defined($file);
	if (ref(SIRTI::Err::nvl($subst)) eq 'HASH') {
		for my $x(@{$self->get_messages()}) {
			for my $k(keys (%$x)) {
				$x->{$k} = $subst->{$k} if exists $subst->{$k};
			}
		}
	}	
	my $msgs=$self->get_string_messages();
	if (scalar(@$msgs) > 0) {
		my @m=ref($subst) eq 'CODE' ? map { $subst->($_) } @$msgs : @$msgs;
#		print $file join("\n",@$msgs),"\n";
		print $file join("\n",@m),"\n";
	}
}


sub reset_messages($) { $_[0]->{MSGS}=[];}


sub get_data() {
	SIRTI::Err::internal_error('metodo astratto');
	return {};
}


sub free() {}   #libera le risorse occupate non di memoria 


1;

