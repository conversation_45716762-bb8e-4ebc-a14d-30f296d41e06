package SIRTI::Toponomastica::Factory;

use strict;
use warnings;
use SIRTI::Err;

use base 'SIRTI::Base';

sub new {
	my $class=shift;
	my %params=@_;
	#  CLASS_SPACE	
	my $self=\%params;
	return bless $self,$class;
}



sub factory_check { # fabbrica un oggetto per check
	my $self=shift;
	my $class='SIRTI::Toponomastica::Plugs::'.$self->{CLASS_SPACE}."::CheckImpl";
	eval "use $class";
	SIRTI::Err::internal_error($@) if $@;
	return $class->new(@_); 
}


sub factory_splitter {  #fabbrica un oggetto splitter
	my $self=shift;
	my $class='SIRTI::Toponomastica::Plugs::'.$self->{CLASS_SPACE}."::SplitterImpl";
	eval "use $class";
	SIRTI::Err::internal_error($@) if $@;
	return $class->new(@_); 
}


sub factory_TICentrali {  #fabbrica un oggetto TICentrali
	my $self=shift;
	my $class='SIRTI::Toponomastica::Plugs::'.$self->{CLASS_SPACE}."::TICentraliImpl";
	eval "use $class";
	SIRTI::Err::internal_error($@) if $@;
	return $class->new(@_); 
}


sub iserror {   #metodo statico - rileva errori nell' array di messaggi
	for my $e(@_) {
		return 1 if ref($e) eq 'HASH' && defined($e->{MSG});
		return 1 if ref($e) eq 'ARRAY' && scalar(grep(defined($_->{MSG}),@{$e}) > 0);
	}
	return 0;
}


if (__FILE__ eq $0) {
	package main;
	
	my $fact_obj = new SIRTI::Toponomastica::Factory(CLASS_SPACE	=> 'CapProfessional');
	
	exit  1 if scalar(@ARGV) == 0;

	eval 'use Data::Dumper';
	SIRTI::Err::internal_error($@) if $@;

	
	my $db = sub {
		my $class='SIRTI::DB';
		eval "use $class";
		SIRTI::Err::internal_error($@) if $@;
		unless (defined($ENV{SQLID})) {
			print STDERR "var. SQLID non definita\n";
			exit 1;
		}
		return $class->new($ENV{SQLID});
		
	}->();
	

	
	sub {
		my $check_obj = $fact_obj->factory_check(
				DB => $db
				,NO_INPUT_FIELDS_INTO_MSG => 0
				#,EMIT_MSG_ON_OK			  => 'check ok'
		);


		my $msgs=$check_obj->do_check(
					VALUES	=> {
						ID		=> 1
						,COMUNE	=> 'milano'
						,PROVINCIA	=> 'mi' 
						,CAP		=> '20128'
						,PARTTOPON  => 'VIA'
						,DESCINDIRIZZO => 'VALTORTA'
						,NUMCIVICO   => '42'
						,NUMTELCOMPL	=> undef
					}
		);

		print "Dump...\n";
		print Dumper($msgs);


		print "\nPrint msg...\n";

		$check_obj->print_messages();


		print "\nget_data...\n";

		print Dumper($check_obj->get_data());
	}->() if $ARGV[0] eq 't1';
	
	
	
	sub {
			my $splitter_obj = $fact_obj->factory_splitter(
					DB => $db
					,NO_INPUT_FIELDS_INTO_MSG => 0
					#,EMIT_MSG_ON_OK			  => 'check ok'
			);
	
			my $msgs=$splitter_obj->do_split_free_address(
						ADDR =>  'via valtorta 42'
						,ADDR1 => 'via valtorta 45/a '
			);
			
			print Dumper($msgs);
	}->() if $ARGV[0] eq 't2';
	

	sub {
			my $splitter_obj = $fact_obj->factory_splitter(
					DB => $db
					,NO_INPUT_FIELDS_INTO_MSG => 0
					#,EMIT_MSG_ON_OK			  => 'check ok'
			);
	
			my $msgs=$splitter_obj->do_split_sv(
						ADDR => 'via valtorta 45/a '
			);
			
			print Dumper($msgs);
	}->() if $ARGV[0] eq 't3';
	

	sub {
			my $obj = $fact_obj->factory_TICentrali(
					DB => $db
					#,NO_INPUT_FIELDS_INTO_MSG => 0
					#,EMIT_MSG_ON_OK			  => 'check ok'
			);
	
			my $msgs=$obj->get_distance(
				VALUES	=> {
					IDCOMUNE		=> 6607
					,IDSTRADA		=> 559
					,DESCINDIRIZZO => 'BIAGIO ACCOLTI GIL'
					,NUMCIVICO		=> 16
					#,NUMCIVICO		=> undef
				}
						
			);
			
			print Dumper($msgs);
	}->() if $ARGV[0] eq 't4';
	
	
	sub {
			my $splitter_obj = $fact_obj->factory_splitter(
					DB => $db
					,NO_INPUT_FIELDS_INTO_MSG => 0
					#,EMIT_MSG_ON_OK			  => 'check ok'
			);

			my $msgs=$splitter_obj->do_split_numtel(
						NUMTELCOMPL 	=> '0131226101'
						,NUMTELCOMPL1   => '999999999' 
			);

			print Dumper($msgs);
	}->() if $ARGV[0] eq 't5';
	
	
	
	sub {
	
	
			my $check_obj = $fact_obj->factory_check(
					DB => $db
					,NO_INPUT_FIELDS_INTO_MSG => 0
					#,EMIT_MSG_ON_OK			  => 'check ok'
			);
	

			my $splitter_obj = $fact_obj->factory_splitter(
					DB => $db
					,NO_INPUT_FIELDS_INTO_MSG => 0
					#,EMIT_MSG_ON_OK			  => 'check ok'
			);

	
			my $msgs=$check_obj->do_check(
						VALUES	=> {
							ID		=> 1
							,COMUNE	=> 'milano'
							,PROVINCIA	=> 'mi' 
							,PARTTOPON  => 'VIA'
							,DESCINDIRIZZO => 'VALTORTA'
							,NUMCIVICO   => '42'
						}
			);

			if (SIRTI::Toponomastica::Factory::iserror($msgs)) {
				print STDERR "errore in check\n";
				print STDERR Dumper($msgs);
				exit 1;
			}
			
			my $data = $check_obj->get_data();				
			print Dumper($data),"\n";		
			
			$msgs=$splitter_obj->join_TI_address(
				VALUES =>  {
						IDCOMUNE	=> $data->{COMUNE}->{ID}
						,IDSTRADA	=> $data->{STRADA}->{ID}
						,NUMCIVICO	=> '59'					
				}			
			);
			if (SIRTI::Toponomastica::Factory::iserror($msgs)) {
				print STDERR "errore  join_TI_address\n";
				print STDERR Dumper($msgs);
				exit 1;
			}
			print Dumper($msgs);

			$msgs=$splitter_obj->join_TI_address(
				VALUES =>  {
						IDCOMUNE	=> 7489
						,IDSTRADA	=> 20902  #strada non esistente in TOPONOMASTICA
						,NUMCIVICO	=> '60'	
				}			
			);

			if (SIRTI::Toponomastica::Factory::iserror($msgs)) {
				print STDERR "errore  join_TI_address\n";
				print STDERR Dumper($msgs);
				exit 1;
			}
			
			print Dumper($msgs);

			$msgs=$splitter_obj->join_TI_address(
				VALUES =>  {
						IDCOMUNE	=> $data->{COMUNE}->{ID}
						,DESCINDIRIZZO  => 'pinco pallo'
						,NUMCIVICO	=> '61'
				}			
			);

			if (SIRTI::Toponomastica::Factory::iserror($msgs)) {
				print STDERR "errore  join_TI_address\n";
				print STDERR Dumper($msgs);
				exit 1;
			}
			
			print Dumper($msgs);
			

			$msgs=$splitter_obj->join_TI_address(
				VALUES =>  {
						IDCOMUNE	=> $data->{COMUNE}->{ID}
						,PARTTOPON	=>  'via'
						,DESCINDIRIZZO  => 'pinco pallo'
						,NUMCIVICO	=> '62'	
				}			
			);

			if (SIRTI::Toponomastica::Factory::iserror($msgs)) {
				print STDERR "errore  join_TI_address\n";
				print STDERR Dumper($msgs);
				exit 1;
			}
			
			print Dumper($msgs);
			
	}->() if $ARGV[0] eq 't6';
	
	sub {
		my $check_obj = $fact_obj->factory_check(
				DB => $db
				,NO_INPUT_FIELDS_INTO_MSG => 0
				#,EMIT_MSG_ON_OK			  => 'check ok'
		);


		my $msgs=$check_obj->do_check(
					VALUES	=> {
						ID		=> 1
						,COMUNE	=> 'milano'
						,PROVINCIA	=> 'mi' 
						,NUMTELCOMPL	=> '03216666'
					}
		);

		print "Dump...\n";
		print Dumper($msgs);


		print "\nPrint msg...\n";

		$check_obj->print_messages();


		print "\nget_data...\n";

		print Dumper($check_obj->get_data());
	}->() if $ARGV[0] eq 't7';

	sub {
		my $check_obj = $fact_obj->factory_check(
				DB => $db
				,NO_INPUT_FIELDS_INTO_MSG => 0
				#,EMIT_MSG_ON_OK			  => 'check ok'
		);


		my $msgs=$check_obj->do_check(
					VALUES	=> {
						ID		=> 1
						,COMUNE	=> 'm"'
						,PROVINCIA	=> 'm' 
				#		,CAP		=> '20128'
						,PARTTOPON  => 'VIA'
						,DESCINDIRIZZO => 'VALTORTA'
						,NUMCIVICO   => '42'
						,NUMTELCOMPL	=> undef
					}
		);

		print "Dump...\n";
		print Dumper($msgs);


		print "\nPrint msg...\n";

		$check_obj->print_messages();


		print "\nget_data...\n";

		print Dumper($check_obj->get_data());
	}->() if $ARGV[0] eq 't8';
	
	sub {
		my $check_obj = $fact_obj->factory_check(
				DB => $db
				,NO_INPUT_FIELDS_INTO_MSG => 0
				#,EMIT_MSG_ON_OK			  => 'check ok'
		);


		my $msgs=$check_obj->do_check(
					VALUES	=> {
						ID		=> 1
						,COMUNE	=> undef
						,PROVINCIA	=> 'mi' 
				#		,CAP		=> '20128'
						,PARTTOPON  => 'VIA'
						,DESCINDIRIZZO => 'VALTORTA'
						,NUMCIVICO   => '42'
						,NUMTELCOMPL	=> undef
					}
		);

		print "Dump...\n";
		print Dumper($msgs);


		print "\nPrint msg...\n";

		$check_obj->print_messages();


		print "\nget_data...\n";

		print Dumper($check_obj->get_data());
	}->() if $ARGV[0] eq 't9';

	sub {
		my $data={
		   'ACTION' => 'CHECK'
		   ,'CAP' => 00000
		   ,'COMUNE' => 'BERTONICI'
		   ,'DEBUG' => 1
		   ,'FRAZIONE' => undef
		   ,'FREE_ADDR' => undef
		   ,'INDIRIZZO' => undef
		   ,'NUMTELCOMPL' => undef
		   ,'PROVINCIA' => 'LO'
			,'	SPEED' => 500
		};

		$data={
		   'COMUNE' => 'BERTONICI'
		   ,'PROVINCIA' => 'LO'
		};
		
		my $check_obj = $fact_obj->factory_check(
				DB => $db
				,NO_INPUT_FIELDS_INTO_MSG => 0
				#,EMIT_MSG_ON_OK			  => 'check ok'
		);

   		my $msgs=$check_obj->do_check(VALUES => $data);
		print "Dump...\n";
		print Dumper($msgs);
   	}->() if $ARGV[0] eq 't10';

	sub {
		my $data={
		    'CAP' => '20141'
		   ,'COMUNE' => 'MIGLIANICO'
		   ,'FREE_ADDR' => 'via francesco brioschi,65'
		   ,'PROVINCIA' => 'MI'
		};
		
		my $check_obj = $fact_obj->factory_check(
				DB => $db
				,NO_INPUT_FIELDS_INTO_MSG => 0
				#,EMIT_MSG_ON_OK			  => 'check ok'
		);

   		my $msgs=$check_obj->do_check(VALUES => $data);
		print "Dump...\n";
		print Dumper($msgs);
   	}->() if $ARGV[0] eq 't11';
   	
   	
   	sub {
		my $splitter_obj = $fact_obj->factory_splitter(
				DB => $db
				,NO_INPUT_FIELDS_INTO_MSG => 0
				#,EMIT_MSG_ON_OK			  => 'check ok'
		);

		if (defined($ARGV[1])) {
			
			my $msgs=$splitter_obj->do_split_free_address(
						FORMAT_TYPE	=> 'TI'
						,ADDR =>  $ARGV[1]
						,ID	  =>  5
			);

			print Dumper($msgs);

			$msgs=$splitter_obj->do_split_free_address(
						ADDR =>  $ARGV[1]
			);

			print Dumper($msgs);
		}
		else {
			print STDERR "usage: $0 t12 <free address>\n"
		}
	}->() if $ARGV[0] eq 't12';
   
}


1;
