Progetto di un insieme di classi che servono per gestire  la toponomastica 



Elenco delle classi:
	//SIRTI::Checks::CheckAttrsMandatory			- classe per verificare la disponibilita/valore  di attributi 	
	SIRTI::Toponomastica::CheckInterface     	    	-  interfaccia per le validazioni 
	SIRTI::Toponomastica::QueryInterface               	-  interfaccia per effettuare query su toponomastica
	SIRTI::Toponomastica::SplitterInterface            	-  interfaccia per splittare un indirizzo nelle componenti fondamentali
	SIRTI::Toponomastica:: TICentraliInterface              -  interfaccia per toponomastica/centrali telecom  
	Plugs/                   - directory plugin 
	Plugs/CapProfessional/   - directory plugin CapProfessional
	SIRTI::Toponomastica::Plugs::CapProfessional::CheckImpl  -   classe check comunario e viario	
	SIRTI::Toponomastica::Plugs::CapProfessional::QueryImpl  -   interrogazione 
	SIRTI::Toponomastica::Plugs::CapProfessional::TICentraliImpl  -    toponomastica/centrali telecom  
	
	SIRTI::Toponomastica::Plugs::CapProfessional::Splitter    -   splitta un indirizzo nelle componenti fondamentali 
	SIRTI::Toponomastica::Factory	 
	
	









