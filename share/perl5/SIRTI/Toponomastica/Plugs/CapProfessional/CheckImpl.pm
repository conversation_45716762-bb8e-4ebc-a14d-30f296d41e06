package SIRTI::Toponomastica::Plugs::CapProfessional::CheckImpl;

use strict;
use warnings;

use base qw(SIRTI::Toponomastica::CheckInterface);

use SIRTI::Checks::GenericCheck;


my %CLASSES=(  #classi da caricare e creare una istanza
	VIARIO		=> "SIRTI::Checks::CheckViario"
	,NUMTEL		=> "SIRTI::Checks::CheckNumtel"
);


sub new {
	my $class=shift;
	my %params = @_;
	my $self = \%params;
	for my $k(keys(%CLASSES)) {
		eval "use ".$CLASSES{$k};
		SIRTI::Err::internal_error($@) if ($@);
		$self->{CLASSES}->{$k}=$CLASSES{$k}->new(%params);
	}

	$self->{CLASSES}->{COMUNARIO}=$self->{CLASSES}->{VIARIO}->{CHECK_COMUNARIO};
							
	$self->{ATTRS}->{VIARIO}=sub {  #imposta gli attributi di viario (obbligatori e non)
			my @attrs_viario =  grep(defined($_), map {
								my $vatt=$_;
								grep($_ eq $vatt,@{$self->{CLASSES}->{COMUNARIO}->get_mandatory_attrs()})  ? undef : $vatt;
							}  @{$self->{CLASSES}->{VIARIO}->get_mandatory_attrs()}
			
			);
			
			my @attrs_opt_viario =  grep(defined($_), map {
								my $vatt=$_;
								grep($_ eq $vatt,@{$self->{CLASSES}->{COMUNARIO}->get_optional_attrs()})  ? undef : $vatt;
							}  @{$self->{CLASSES}->{VIARIO}->get_optional_attrs()}
			
			);
			push @attrs_viario,@attrs_opt_viario;
			return \@attrs_viario;
	}->();  
	
	$self->{ATTRS}->{COMUNARIO}=$self->{CLASSES}->{COMUNARIO}->get_mandatory_attrs();
	push @{$self->{ATTRS}->{COMUNARIO}},@{$self->{CLASSES}->{COMUNARIO}->get_optional_attrs()};
	
	$self->{ATTRS}->{NUMTEL}=$self->{CLASSES}->{NUMTEL}->get_mandatory_attrs();
	push  @{$self->{ATTRS}->{NUMTEL}},@{$self->{CLASSES}->{NUMTEL}->get_optional_attrs()};

	return bless $self,$class;
}


sub get_checktype { return $_[0]->{CLASSES}->{CURRENT_CLASS}->get_chektype(); }


sub get_data {  return $_[0]->{CLASSES}->{CURRENT_CLASS}->get_data(); }



sub _filter_msg {
	my ($self,$h)=@_;
	return undef unless defined($h);
	my $fh =  $self->{NO_INPUT_FIELDS_INTO_MSG} 
				?   $self->{CLASSES}->{CURRENT_CLASS}->_filter_msg($h)
				:   $h;
	return $fh;
}

sub _get_check_class_type  {
	my($self,$values)=@_;
	my @attrs = keys(%$values);
	my @class_types=();
	for my $a(@attrs) {
		if (grep($_ eq $a,@{$self->{ATTRS}->{NUMTEL}})) {
			push @class_types,'NUMTEL';
			last;
		}
	}
			
	for my $a(@attrs) {
		if (grep($_ eq $a,@{$self->{ATTRS}->{VIARIO}})) {
			push @class_types,'VIARIO';
			last;
		}
	}
	
	unless (grep( $_ eq 'VIARIO',@class_types)) {
		for my $a(@attrs) {
			if (grep($_ eq $a,@{$self->{ATTRS}->{COMUNARIO}})) {
				push @class_types, 'COMUNARIO';
				last;
			}
		}
	}
	
	return @class_types;	
}

sub _do_check {
	my $self=shift;
	my %params=@_;
	my @check_class_type=defined $self->{FORCE_CHECK_CLASS_TYPE} 
		? @{$self->{FORCE_CHECK_CLASS_TYPE}}
		: ();
	@check_class_type=@{$params{FORCE_CHECK_CLASS_TYPE}} if defined $params{FORCE_CHECK_CLASS_TYPE}; 
	@check_class_type=$self->_get_check_class_type($params{VALUES}) if  scalar(@check_class_type) == 0;
	return  [] if scalar(@check_class_type) == 0;
	my @msgs=();
	for my $ct(@check_class_type) {
		$self->{CLASSES}->{CURRENT_CLASS}= $self->{CLASSES}->{$ct};
		SIRTI::Err::internal_error($ct.": non istanziato") unless defined $self->{CLASSES}->{CURRENT_CLASS};
		sub { #inizializza gli attributi obbligatori non esistenti
			my ($attrs,$v)=@_;
			for my $a(@$attrs) {
				$v->{$a}=undef unless exists $v->{$a};
			}
		}->($self->{CLASSES}->{CURRENT_CLASS}->get_mandatory_attrs(),$params{VALUES});
		$self->{CLASSES}->{CURRENT_CLASS}->get_chektype();
		my $m=$self->{CLASSES}->{CURRENT_CLASS}->do_check($params{VALUES},%params);
		for my $x(@$m) {
			$x->{CHECKTYPE}=$ct unless defined $x->{CHECKTYPE};
		}
		push @msgs,grep(defined($self->_filter_msg($_)),@$m);
	}
	return \@msgs;
}


sub free() {  #libera le risorse
	my $self=shift;
	for my $k(keys %{$self->{CLASSES}}) {
		$self->{CLASSES}->{$k}->free();
	}


}


1;
