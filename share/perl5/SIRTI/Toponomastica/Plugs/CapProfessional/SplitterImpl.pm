package SIRTI::Toponomastica::Plugs::CapProfessional::SplitterImpl;

use strict;
use warnings;

use base qw(SIRTI::Toponomastica::SplitterInterface);



my %CLASSES=(  #classi da caricare e creare una istanza
	TOPO	=> "SIRTI::Toponomastica"
	,NUMTEL  => "SIRTI::Checks::CheckNumtel"
);


sub new {
	my $class=shift;
	my %params = @_;
	my $self = \%params;
	for my $k(keys(%CLASSES)) {
		eval "use ".$CLASSES{$k};
		SIRTI::Err::internal_error($@) if ($@);
		$self->{CLASSES}->{$k}=$CLASSES{$k}->new(%params);
	}
	return bless $self,$class;
}


sub get_data {  return $_[0]->{CLASSES}->{CURRENT_CLASS}->get_data(); }


my %_SPLIT=();

%_SPLIT=(
	TD	=>  sub {
		my ($self,$params,$a)=@_;
		my @r=$self->{CLASSES}->{CURRENT_CLASS}->split_from_sv($a->{ADDR});
		my $msgs=$r[0];
		$msgs->{FREE_ADDR}=$a->{ADDR};
		$msgs->{ID} = $a->{ID};
		$msgs->{FORMAT_TYPE}='TD';
		return $msgs;
	}
	,TI	=> {
			IS_TI_TOPON	=> sub { #esiste la particella topo di TI ?
				my ($self,$p)=@_;
				my $sql="select count(*) from  dugstd_ti 
						where
							dugstd_ti=".$self->{DB}->quote($p)."					
				";
				my $cont = $self->{DB}->fetch_minimalized($sql);
				SIRTI::Err::db_fatal_error() if $self->{DB}->iserror();
				return $cont;					
			}
			,GET_TI_TOPON	=> sub { #converte una particella topo di capprof o TI in formato TI
									 # da la precedenza a quelle gia in  formato TI
				my ($self,$p)=@_;
				my $sql="select 
							dugstd_ti 
						from 
							dugstd_ti 
						where
							dugstd=".$self->{DB}->quote($p)."
							or dugstd_ti=".$self->{DB}->quote($p)."
				";
				my $cur=$self->{DB}->create_cursor($sql);
				SIRTI::Err::db_fatal_error() if $self->{DB}->iserror();
				my $d=undef;
				while(my $r=$cur->fetchrow_hashref()) {
					if ($r->{DUGSTD_TI} eq $p) { #ha la precedenza
						$cur->finish();
						return $p;
					}
					$d=$r->{DUGSTD_TI} unless defined $d;
				}
				SIRTI::Err::db_fatal_error() if $self->{DB}->iserror();
				$cur->finish();
				return $d;
			}
			,MAIN =>  sub { #procedura di conversione 
				my ($self,$params,$a)=@_;
				my ($p,$i,$n,$e);
				if ($a->{ADDR}=~/^\s*([^.\s]+\.[^.\s]*)\s+(.*),\s*(.*)$/) {  #match parttopon, indirizzo e numerocivico  (1)
					($p,$i,$n)=(uc($1),$2,$3);
					$n=~s/\s+$//;
				}	
				elsif ($a->{ADDR}=~/^\s*([^.\s]+\.[^.\s]*)\s+(.*)$/) { #match parttopon, indirizzo  (2)
					($p,$i,$n)=(uc($1),$2,undef);
				}

				my $msgs = {
						ID				=>  $a->{ID}
						,FREE_ADDR		=>  $a->{ADDR}
						,PARTTOPON		=>  $p
						,DESCINDIRIZZO  =>  $i
						,NUMCIVICO		=>  $n
						,FORMAT_TYPE	=>  'TI'
				};


				my $r=($self->{CLASSES}->{CURRENT_CLASS}->split_from_sv($a->{ADDR}))[0];			

				unless (defined($msgs->{PARTTOPON})) {  # l' utente non ha specificato una particella topo contenente un punto (std TI)
														# su suppone che sia una particella secondo lo std capprof oppure  TI senza punto
					my $dugti = $_SPLIT{TI}->{GET_TI_TOPON}->($self,$r->{PARTTOPON}); #cerca la particella toponomastica tipo capprof o TI e ricava lo standard di TI
					if (defined ($dugti)) {
						$msgs->{PARTTOPON}=$dugti;
					}
					elsif ($a->{ADDR}=~/^\s*(\S+)\s+([^,]+),/) { #se non trova la particella in formato capprof o TI si ritenta la ricerca 
														 		 # supponendo una particella gia secondo lo std TI
						my ($pt,$ind)=($1,$2);
						$ind=~s/\s+$//;
						$msgs->{PARTTOPON}=$pt;
						$msgs->{DESCINDIRIZZO}=$ind;
						$n=''; #evita la (4)
						my $cont = $_SPLIT{TI}->{IS_TI_TOPON}->($self,$pt); #verifica che $pt sia una particella TI 
						if ($cont == 0) { #non e' una particella TI
										  #quindi suppone che sia una particella capprof
							$dugti = $_SPLIT{TI}->{GET_TI_TOPON}->($self,uc($pt)); #ricava la particella secondo lo standard di TI
							if (defined ($dugti)) {
								$msgs->{PARTTOPON}=$dugti;								
							}
							else {
								$msgs->{MSG}="particella toponomastica '".$pt."' non corretta";
							}
						}
					}
					else { #l'utente ha immesso una particella che non appartiene a nessuno dei 2 std
						$msgs->{MSG}="particella toponomastica '".$r->{PARTTOPON}."' non corretta";
					}
				}
				else {  #l' utente ha immesso una particella secondo lo std TI
						#viene verificata la sua esistenza
					my $cont = $_SPLIT{TI}->{IS_TI_TOPON}->($self,$msgs->{PARTTOPON});
					$msgs->{MSG}="particella toponomastica '".$msgs->{PARTTOPON}."' non corretta" if $cont == 0;
				}
				
				
				while(1) {
					if (length(SIRTI::Err::nvl($r->{NUMCIVICO})) > 0) {  #split_from_sv ha precedenza sullo split effettuato in (1)
						$msgs->{NUMCIVICO}=$r->{NUMCIVICO};
						my $exp=SIRTI::Err::nvl($r->{ESPONENTE});
						$msgs->{NUMCIVICO}.='/'.$exp if $exp !~ /^\s*$/;
						my $note=SIRTI::Err::nvl($r->{NOTE});
						$msgs->{NOTE}=$note if $note !~/^\s*$/;
						last;
					}
					else { #split_from_sv non ha trovato un  numero civico 
						   #puo anche darsi che sia dovuto alla presenza di caratteri 'strani'
						my $free_addr=undef;
						if ($a->{ADDR}=~/^\s*([^.\s]+\.[^.\s]*\s+.*),\s*(.*)$/) { #la virgola di separazione tra indirizzi e civico e' presente 
																					   #quindi viene fatto un tentativo di eliminare i caratteri 'strani'
																					   #dalla parte indirizzo
																					   #e di rifare lo splitting 
							($free_addr,my $numciv)=($1,$2);
							$free_addr=~s/[^\w\s'.,]//g;  #elimina i caratteri strani
							
							$free_addr.=', '.$numciv; #ricompone l' indirizzo
						}					   
						else { #viene fatto un tentativo su tutto l' indirizzo prima eliminando i caratteri speciali
								$free_addr=$a->{ADDR};
								$free_addr=~s/[^\w\s'.,]//g;
						}
						my $t=($self->{CLASSES}->{CURRENT_CLASS}->split_from_sv($free_addr))[0];
						last if length(SIRTI::Err::nvl($t->{NUMCIVICO})) == 0; # e' andata male
																				#si suppone che il civico non  sia stato specificato

						map { $r->{$_} = $t->{$_} } qw ( NUMCIVICO ESPONENTE COLORE NOTE );
					}
				} #while
				$msgs->{DESCINDIRIZZO}=$r->{DESCINDIRIZZO} unless defined $n;   # (4) 
																				# ha fatto il match con  (2) quindi l' indirizzo non e' sicuro
																				# e lo prende dallo splitter
				return $msgs;
			}
	}
	,GET_OLO_SUB	=> sub {
		my $ft=shift;
		$ft='TD' unless defined $ft;  #default
		my $split=$_SPLIT{$ft};
		my $r=ref($split);
		return $split if $r eq 'CODE'; 
		return $split->{MAIN} if $r eq 'HASH';
		return undef;			
	}
					
);


sub _do_split {
	my $self=shift;
	my $format=shift;
	my ($params,$values)=@_;	
	my @msgs=();
	my $m=undef;
	
	if ($format == SIRTI::Toponomastica::SplitterInterface::SPLIT_FREE_ADDRESS) {
		my $split_sub=$_SPLIT{GET_OLO_SUB}->($params->{FORMAT_TYPE});
		SIRTI::Err::prec(defined($split_sub),"valore parametro FORMAT_TYPE non corretto");
		
		$self->{CLASSES}->{CURRENT_CLASS}= $self->{CLASSES}->{TOPO};
		for my $a(@$values) {
			my $m=$split_sub->($self,$params,$a);
			push @msgs,$m;
		}
	}
	elsif ($format == SIRTI::Toponomastica::SplitterInterface::SPLIT_SV) {
		$self->{CLASSES}->{CURRENT_CLASS}= $self->{CLASSES}->{TOPO};
		for my $a(@$values) {
			my @r = $self->{CLASSES}->{CURRENT_CLASS}->split_from_sv($a->{ADDR});
			push @msgs,$r[0];
		}
	}
	elsif ($format == SIRTI::Toponomastica::SplitterInterface::SPLIT_NUMTEL) {
		$self->{CLASSES}->{CURRENT_CLASS}= $self->{CLASSES}->{NUMTEL};
		for my $a(@$values) {
			my @n=$self->{CLASSES}->{CURRENT_CLASS}->split_numtel($a->{NUMTELCOMPL});
			if (defined($n[0]) && defined($n[1])) {
				push @msgs , { 
					PREFIX 		=>   $n[0]
					,NUMTEL		=>   $n[1]
				}
			}
			elsif (!(defined($n[0]))) {
				push @msgs , { 
					NUMTELCOMPL =>   $a->{NUMTELCOMPL}
					,MSG		=>   'PREFISSO NON TROVATO O NON VALIDO'
				}
			}
			else {
				push @msgs , { 
					NUMTELCOMPL =>   $a->{NUMTELCOMPL}
					,MSG		=>   'NUMERO NON VALIDO'
				}
			}
		}
	}
	return \@msgs;

}

#
#   VALUES { keys: IDCOMUNE IDFRAZIONE IDSTRADA PARTTOPON DESCINDIRIZZO NUMCIVICO ESPONENTE FREE_ADDR  }
#
#
#

sub _join_TI_address  {
	my $self=shift;
	my %params=@_;
	my $values=$params{VALUES};
	my @msgs=();


	if (defined($values->{FREE_ADDR}))  {
		my $ind=$values->{FREE_ADDR};
		my $numciv=undef;
		$numciv=$1 if $ind=~s/,([^,]+)$//;  #elimina numero civico
		$numciv=$values->{NUMCIVICO}.(defined($values->{ESPONENTE}) ? '/'.$values->{ESPONENTE} : '')   unless defined $numciv;
		my $sql="
				with m as (
					select 
						min(id) id
					from
						TI_TOPONOMASTICA t
					where 
						idcomune=".$values->{IDCOMUNE}."
						and idfrazione".(
								defined($values->{IDFRAZIONE}) 
									? ' ='.$values->{IDFRAZIONE} 
									: ' is NULL'
						)."
						and 	
						regexp_replace(ind_copertura_rete_sec,',[^,]+\$','') = ".$self->{DB}->quote($ind)."
				)
				select 
					t.id
					,t.localita
					,t.ind_copertura_rete_sec
					,d.dugstd_ti    dugstd_ti
					,substr(t.ind_copertura_rete_sec,1,length(d.dugstd_ti)) dugstd_eff_ti
					,ltrim(substr(t.ind_copertura_rete_sec,length(d.dugstd_ti) + 1)) ind_eff_ti
					,t.parttopon  	dugstd_eff_cp
					,t.descindirizzo ind_eff_cp
					,t.error_count
				from 
					TI_TOPONOMASTICA t
					left join dugstd_ti d
						on decode(error_count || nvl2(t.checked,'1','0'),'01',t.parttopon,null) = d.dugstd  
					inner join m 
						on t.id = m.id 
		";

		my $r = $self->{DB}->fetchall_hashref($sql) || SIRTI::Err::db_fatal_error();
		if (scalar(@$r) > 0) {
			my $h=$r->[0];			
			
			my @split=$ind=~/^(\S+)\s+(.*)$/;  #suddivide la parte toponomastica dalla via
			
			if (scalar(@split) == 0) {
				$h->{IND_EFF_TI}=~s/,[^,]+$//;  #elimina l' eventuale numero civico ed esponente
			}
			else {
				$h->{DUGSTD_EFF_TI}=$split[0];
				$h->{IND_EFF_TI}=$split[1];			
			}
			
			push @msgs,{
				LOCALITA			=>   $h->{LOCALITA}
				,ADDRESS			=>   $values->{FREE_ADDR}
				,MATCH_NUMBER		=>   5
				,DUGSTD_EFF_TI		=>   $h->{DUGSTD_EFF_TI}
				,IND_EFF_TI			=>   $h->{IND_EFF_TI}
				,DUGSTD_EFF_CP		=>   $h->{DUGSTD_EFF_CP}
				,IND_EFF_CP			=>   $h->{IND_EFF_CP}
				,ID_TI_TOPONOMASTICA =>  $h->{ID}
				,DESCINDIRIZZO		=>   $h->{IND_EFF_TI}
				,PARTTOPON			=>   $h->{DUGSTD_EFF_TI}		
			};

		
			return \@msgs;
		}
	}
	if (defined($values->{IDSTRADA})) {
		my $sql="
			with m as (
				select 
					min(t.id)  id 
				from 
					TI_TOPONOMASTICA t
				where	
					checked is not null
					and error_count = 0
					and idcomune=".$values->{IDCOMUNE}."
					and idfrazione".(
							defined($values->{IDFRAZIONE}) 
								? ' ='.$values->{IDFRAZIONE} 
								: ' is NULL'
					)."
					and idstrada=".$values->{IDSTRADA}."
			)						
			select
				t.id
				,t.localita
				,t.ind_copertura_rete_sec
				,d.dugstd_ti    dugstd_ti
				,substr(t.ind_copertura_rete_sec,1,length(d.dugstd_ti)) dugstd_eff_ti
				,ltrim(substr(t.ind_copertura_rete_sec,length(d.dugstd_ti) + 1)) ind_eff_ti
				,t.parttopon  	dugstd_eff_cp
				,t.descindirizzo ind_eff_cp
			from 
				TI_TOPONOMASTICA t
				,dugstd_ti d
				,m
			where	
				t.id = m.id
				and t.parttopon = d.dugstd (+)
		";

#print $sql,"\n";

		my $r = $self->{DB}->fetchall_hashref($sql) || SIRTI::Err::db_fatal_error();
		if (scalar(@$r) > 0) {
			my $h=$r->[0];
			my $addr=$h->{IND_COPERTURA_RETE_SEC};
			$addr=~s/,[^,]+$//; #elimina l' eventuale numero civico ed esponente
			
			if (defined($values->{NUMCIVICO})) {
				$addr.=','.$values->{NUMCIVICO};
				$addr.='/'.$values->{ESPONENTE} if defined $values->{ESPONENTE};
			}
			
			my $addr_splitt=$h->{IND_COPERTURA_RETE_SEC};
			$addr_splitt=~s/,[^,]+$//; #elimina l' eventuale numero civico ed esponente
			
			my @split=$addr_splitt=~/^(\S+)\s+(.*)$/;  #suddivide la parte toponomastica dalla via
			
			if (scalar(@split) == 0) {
				$h->{IND_EFF_TI}=~s/,[^,]+$//;#elimina l' eventuale numero civico ed esponente
			}
			else {
				$h->{DUGSTD_EFF_TI}=$split[0];
				$h->{IND_EFF_TI}=$split[1];			
			}

			$h->{IND_EFF_CP}=~s/,[^,]+$//;#elimina l' eventuale numero civico ed esponente
			
			push @msgs,{
				LOCALITA			=>   $h->{LOCALITA}
				,ADDRESS			=>   $addr
				,MATCH_NUMBER		=>   2
				,DUGSTD_EFF_TI		=>   $h->{DUGSTD_EFF_TI}
				,IND_EFF_TI			=>   $h->{IND_EFF_TI}
				,DUGSTD_EFF_CP		=>   $h->{DUGSTD_EFF_CP}
				,IND_EFF_CP			=>   $h->{IND_EFF_CP}
				,ID_TI_TOPONOMASTICA =>  $h->{ID}
			};

			if (defined($h->{DUGSTD_EFF_TI}) && defined($h->{IND_EFF_TI})   && $addr=~/^$h->{DUGSTD_EFF_TI}/) {
				$msgs[-1]->{PARTTOPON} = $h->{DUGSTD_EFF_TI};
				my $ind = $h->{IND_EFF_TI};
				$ind=~s/,[^,]+$//; #elimina l' eventuale numero civico ed esponente
				$msgs[-1]->{DESCINDIRIZZO} = $ind;
			}
			else { #dugstd_eff_ti non e' corretto quindi non emette le parte splittate 
				   # si potrebbe tentare con dugstd_eff_cp
				   # ma probabilmente non e' corretto 
					
					
				
			
			}
				
			return \@msgs;
		}
		
		
		#ritenta chiamando capprof 
		
		$sql = qq{
			select 
				c.denominazionestd    localita
				,v.dugstd             parttopon
				,d.dugstd_ti		  parttopon_ti
				,v.nomestd			  descindirizzo
			from
				comune c
				,strada v 
				,dugstd_ti    		   d
			where 
				c.id = $values->{IDCOMUNE}
				and v.id = $values->{IDSTRADA}
				and v.dugstd  = d.dugstd (+)  	
		};
		$r = $self->{DB}->fetchall_hashref($sql) || SIRTI::Err::db_fatal_error();
		if (scalar(@$r) > 0) {
			my $h=$r->[0];
			my $parttopon=defined($h->{PARTTOPON_TI}) ?  $h->{PARTTOPON_TI} : $h->{PARTTOPON};
			my $addr=$parttopon.' '.$h->{DESCINDIRIZZO};
			if (defined($values->{NUMCIVICO})) {
				$addr.=','.$values->{NUMCIVICO}; 
				$addr.='/'.$values->{ESPONENTE} if defined $values->{ESPONENTE};
			}
			push @msgs,{
				LOCALITA			=> $h->{LOCALITA}
				,ADDRESS			=> $addr
				,MATCH_NUMBER		=> 3
				,PARTTOPON			=> $parttopon
				,DESCINDIRIZZO		=> $h->{DESCINDIRIZZO}
				,ID_TI_TOPONOMASTICA => undef
			};
			return \@msgs;
		}
		
		push @msgs, {
						IDCOMUNE 			=> $values->{IDCOMUNE}
						,IDSTRADA			=>  $values->{IDSTRADA}
						,MSG 				=> 'coppia id_comune/id_strada non trovati'
						,MATCH_NUMBER		=>   4

					};
		return \@msgs;
	}  # if (defined($values->{IDSTRADA}))	
	
	
	# non e' stato impostato IDSTRADA quindi prende PARTTOPON e DESCINDIRIZZO
			
	my $sql="
	   		select distinct localita,null localita_std
			from 
					TI_TOPONOMASTICA  
			where 
					idcomune = ".$values->{IDCOMUNE}."
					and idstrada is not null  /* per gli indici */
					and numcivico is not null  /* per gli indici */
	    	union 
			select  null localita,denominazionestd localita_std 
			from
				comune 
			where id = ".$values->{IDCOMUNE}."
     ";                                   
                                                                           
	my $r = $self->{DB}->fetchall_hashref($sql) || SIRTI::Err::db_fatal_error();
	my %loc=();
	for my $h(@$r) {
		$loc{LOCALITA_TI}=$h->{LOCALITA_TI} if defined($h->{LOCALITA_TI});
		$loc{LOCALITA_STD}=$h->{LOCALITA_STD} if defined($h->{LOCALITA_STD});
	}
	
	my $localita=defined($loc{LOCALITA_TI}) ?   $loc{LOCALITA_TI} :  $loc{LOCALITA_STD};  
	unless (defined($localita)) {
		push @msgs,{
						IDCOMUNE	=>   $values->{IDCOMUNE}
						,MSG 		=>   'idcomune non trovato ' 
						,MATCH_NUMBER		=>   5
		};
		return \@msgs;
	}
	
	$sql="
		select 
			dugstd_ti 
		from 
			dugstd_ti 
		where 
			dugstd = ".uc($self->{DB}->quote($values->{PARTTOPON}))."
	";
	
	#print $sql,"\n";
	
	$r = $self->{DB}->fetchall_hashref($sql) || SIRTI::Err::db_fatal_error();
	my $parttopon = scalar(@$r) > 0 ? $r->[0]->{DUGSTD_TI} : $values->{PARTTOPON};
	my $addr=defined($parttopon) ?  $parttopon.' ' : ''; 
	$addr.=uc($values->{DESCINDIRIZZO});

	if (defined($values->{NUMCIVICO})) {
		$addr.=','.$values->{NUMCIVICO}; 
		$addr.='/'.$values->{ESPONENTE} if defined $values->{ESPONENTE};
	}

	push @msgs,{
		LOCALITA			=> $localita
		,ADDRESS			=> $addr
		,MATCH_NUMBER		=>   6
		,PARTTOPON			=> $parttopon 
		,DESCINDIRIZZO		=> uc($values->{DESCINDIRIZZO})
		,ID_TI_TOPONOMASTICA => undef
	};	
	return \@msgs;
}




1;
