package SIRTI::Toponomastica::Plugs::CapProfessional::TICentraliImpl;

use strict;
use warnings;

use base qw(SIRTI::Toponomastica::TICentraliInterface);


my %CLASSES=(  #classi da caricare e creare una istanza
	CK_COMUNARIO	=> 'SIRTI::Checks::CheckComunario'
);


sub new {
	my $class=shift;
	my %params = @_;
	my $self = \%params;
	for my $k(keys(%CLASSES)) {
		eval "use ".$CLASSES{$k};
		SIRTI::Err::internal_error($@) if ($@);
		$self->{CLASSES}->{$k}=$CLASSES{$k}->new(%params);
	}
	return bless $self,$class;
}


#  VALUES { COMUNE IDCOMUNE FRAZIONE IDFRAZIONE IDSTRADA PARTTOPON DESCINDIRIZZO NUMCIVICO ESPONENTE COLORE PARITA }
 
sub _get_distance {
	my $self=shift;
	my %params=@_;
	my @msg=();
	

	my ($v,$db)=($params{VALUES},$self->{DB});
	
	push @msg,{
		MSG => 'chiave VALUES non esistente o non corretta'
	} if !defined($v) || ref($v) ne 'HASH' || scalar(keys(%$v)) == 0;
	
	return \@msg 	if scalar(@msg) > 0;
	
	
	unless (defined($v->{IDCOMUNE})) {
		my $m = $self->{CLASSES}->{CK_COMUNARIO}->do_check($v);
		my $data = $self->{CLASSES}->{CK_COMUNARIO}->get_data();
		unless (defined($data) && defined($data->{COMUNE}->{ID})) {
			push @msg, {
				MSG		=> 'comune non trovato'
				,COMUNE	=> $v->{COMUNE}
				,PROVINCIA => $v->{PROVINCIA}
			};
			return \@msg;			
		}
		$v->{IDCOMUNE}=$data->{COMUNE}->{ID};		
	}

	
	my $r=undef;
	
	if (defined($v->{NUMCIVICO})) {
		my $key = "SQL1_" . (defined($v->{IDSTRADA}) ? 'IDSTRADA' : 'NOMESTRADA');
		unless (defined($self->{PREPARE}->{$key})) {
			$self->{PREPARE}->{$key}=$self->{DB}->create_prepare(
				qq{
					select
						'N' distanza_stimata  
						,codice_gat
						,avg(to_number(lungh_tratta_prim,'999999999.99'))   lungh_tratta_prim
						,avg(to_number(lungh_secondaria,'999999999.99'))   lungh_secondaria
						,numcivico  
						,nvl(to_number( replace( numcivico, ltrim( numcivico, '0123456789'))),'0') order_numcivico
					from 
						RETEPRIMESEC
					where 
						idcomune = ?
						and 
				}
				.(defined($v->{IDSTRADA})  
					? "  idstrada= ?  " 
					: " descindirizzo= ? "
				) . 
				qq {
						and numcivico  = ?
					group  by 
						codice_gat,numcivico
					order by 
						order_numcivico,codice_gat
				}
			)  || SIRTI::Err::db_fatal_error($db);	
		}
		
		$r = $self->{PREPARE}->{$key}->fetchall_hashref(
					$v->{IDCOMUNE}
					,(defined($v->{IDSTRADA}) ?  $v->{IDSTRADA} :  $v->{DESCINDIRIZZO})
					,$v->{NUMCIVICO}
		) || SIRTI::Err::db_fatal_error($db);	

		if (scalar(@$r) == 0) {
			my $key='SQL2_'.(defined($v->{IDSTRADA}) ? 'IDSTRADA' : 'NOMESTRADA');
			unless (defined($self->{PREPARE}->{$key})) {
				$self->{PREPARE}->{$key}=$self->{DB}->create_prepare(
					qq{
						select
							'Y' distanza_stimata  
							,cod_sede_gat codice_gat
							,avg(to_number(lungh_tratta,'9999999.99')) lungh_tratta_prim
							,avg(decode(sign(to_number(lungh_tratta,'9999999.99')  - 3000),1,700,500))   lungh_secondaria 							
							,numcivico
							,nvl(to_number( replace( numcivico, ltrim( numcivico, '0123456789'))),'0') order_numcivico
						from 
							TI_TOPONOMASTICA
						where 	
							idcomune = ?
							and 
					}
					.(defined($v->{IDSTRADA})  
							? "  idstrada= ?  " 
							: " descindirizzo= ? "
					) . 
					qq {

							and numcivico = ? 
						group  by 
							cod_sede_gat,numcivico
						order by 
							order_numcivico,cod_sede_gat
					}
				) || SIRTI::Err::db_fatal_error($db);	
			}		
			$r = $self->{PREPARE}->{$key}->fetchall_hashref(
								$v->{IDCOMUNE}
								,(defined($v->{IDSTRADA}) ?  $v->{IDSTRADA} :  $v->{DESCINDIRIZZO})
								,$v->{NUMCIVICO}
			)  || SIRTI::Err::db_fatal_error($db);	
		}
	}
	else {  # !defined($v->{NUMCIVICO}
		my $key='SQL3_'.(defined($v->{IDSTRADA}) ? 'IDSTRADA' : 'NOMESTRADA');	
		unless (defined($self->{PREPARE}->{$key})) {	
			$self->{PREPARE}->{$key}=$self->{DB}->create_prepare(
				qq{
					select
						'N' distanza_stimata  
						,codice_gat
						,avg(to_number(lungh_tratta_prim,'9999999.99'))     lungh_tratta_prim 
						,avg(to_number(lungh_secondaria,'9999999.99'))      lungh_secondaria
						,numcivico  
						,nvl(to_number( replace( numcivico, ltrim( numcivico, '0123456789'))),'0') order_numcivico
					from 
						RETEPRIMESEC
					where 
						idcomune = ?
						and 
				}
				. 
				(defined($v->{IDSTRADA})  
							? "  idstrada= ?  " 
							: " descindirizzo= ? "
				) .
				qq {
					group  by 
						codice_gat,numcivico
					union 
					select
						'Y' distanza_stimata  
						,cod_sede_gat 						codice_gat
						,avg(to_number(lungh_tratta,'9999999.99')) 						lungh_tratta_prim
						,avg(decode(sign(to_number(lungh_tratta,'9999999.99')  - 3000),1,700,500))   lungh_secondaria 
						,numcivico
						,nvl(to_number( replace( numcivico, ltrim( numcivico, '0123456789'))),'0') order_numcivico
					from 
						TI_TOPONOMASTICA
					where 	
						idcomune = ?
						and 
				}
				.(defined($v->{IDSTRADA})  
						? "  idstrada= ?  " 
						: " descindirizzo= ? "
				) . 
				qq {
					group  by 
						cod_sede_gat,numcivico
					order by 
						order_numcivico,codice_gat
				}
			)  || SIRTI::Err::db_fatal_error($db);	
		}

		$r = $self->{PREPARE}->{$key}->fetchall_hashref(
							$v->{IDCOMUNE}
							,(defined($v->{IDSTRADA}) ?  $v->{IDSTRADA} :  $v->{DESCINDIRIZZO})
							,$v->{IDCOMUNE}
							,(defined($v->{IDSTRADA}) ?  $v->{IDSTRADA} :  $v->{DESCINDIRIZZO})							
		)  || SIRTI::Err::db_fatal_error($db);	

	}
	
	push @msg, { 
			RESULT		=>  $r
	};
	
	
	return \@msg;
}


sub DESTROY {
	my $self=shift;
	if (ref($self->{PREPARE}) eq 'HASH') {      #chiude le prepare
		for my $v(values %{$self->{PREPARE}}) {
			eval { $v->finish() };
			print STDERR $@ if $@;
		}
	}
}





1;
