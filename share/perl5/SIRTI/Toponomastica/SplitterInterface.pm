package SIRTI::Toponomastica::SplitterInterface;

use strict;
use warnings;
use SIRTI::Err;

use base 'SIRTI::Base';

use constant {
				SPLIT_FREE_ADDRESS        =>  1           #split indirizzo in formato libero
				,SPLIT_SV                 =>  2           #split indirizzo in formato singleview
				,SPLIT_NUMTEL			  =>  3  		  #split numero telefonico (prefisso + resto) 
             };
                        
                        


sub new {
	my $class=shift;
	my %params=@_;
	#  NO_INPUT_FIELDS_INTO_MSG   => nei messaggi di errori non emette i campi di input
	#  EMIT_MSG_ON_OK			  => aggiunge il messaggio in caso di ok
	$params{MSGS}=[];
	my $self=\%params;
	my $c = bless $self,$class;
	return $c;
}



# SPLIT_FREE_ADDRESS|SPLIT_SV  ADDR  => value | ADDR<n> => <value> ...
# SPLIT_NUMTEL                 NUMTELCOMPL => value | NUMTELCOMPL<n> => <value>
# 							   GLOBALID  => <n> - imposta ID in output a partire da <n> - default 0	
# FORMAT_TYPE                  formatta secondo lo std dell' olo specificato
#
sub do_split {
	my $self=shift;
	my $format=shift;
	my $msgs=[];
	push @$msgs,@{$self->_do_split($format,$self->_do_pre(@_))};
	if ($self->{EMIT_MSG_ON_OK}  && scalar(@$msgs) == 0) {
		push @$msgs,$self->{EMIT_MSG_ON_OK};
	}
	$self->{MSGS}=$msgs;
	return $msgs;
}



# ADDR  => value | ADDR<n> => <value> ...
# FORMAT_TYPE		=>  TD|TI|...
#                    formatta secondo lo standard dell' olo richiesto 
#                    attualmente gli olo supporttati sono TD - tele2
#                                                          TI - telecom
#
sub do_split_free_address { 
	my $self=shift;
	return $self->do_split(SPLIT_FREE_ADDRESS,@_);
}


# ADDR  => value | ADDR<n> => <value> ...

sub do_split_sv  {
	my $self=shift;
	return $self->do_split(SPLIT_SV,@_);
}


# NUMTELCOMPL => value | NUMTELCOMPL<n> => <value>

sub do_split_numtel {
	my $self=shift;
	return $self->do_split(SPLIT_NUMTEL,@_);

}

#   VALUES { keys: IDCOMUNE IDFRAZIONE IDSTRADA PARTTOPON DESCINDIRIZZO NUMCIVICO ESPONENTE COLORE PARITA }
#   return {
#				LOCALITA		=> ...
#               ,ADDRESS   		=> ...
#          }
#
#   
sub join_TI_address {
	my $self=shift;
	my %params=@_;
	SIRTI::Err::check(defined($params{VALUES}),'chiave VALUES non definita');
	SIRTI::Err::check(defined($params{VALUES}->{IDCOMUNE}),'chiave IDCOMUNE non definita');
	SIRTI::Err::check(defined($params{VALUES}->{IDSTRADA}) || defined($params{VALUES}->{DESCINDIRIZZO}),'chiave IDSTRADA || DESCINDIRIZZO  non definite');
	my $msgs = $self->_join_TI_address(%params);
	$self->{MSGS}=$msgs;
	return $msgs;
}

sub get_messages($) { return $_[0]->{MSGS};}


sub get_string_messages($)  {
	my $self=shift;
	my @a=();
	map {
		my $c=$_;
		my $s='';
		$s.=$c->{ID}.':' if defined($c->{ID}); 
		$s.=SIRTI::Err::nvl($c->{MSG},$c->{MSG},'<NOMSG>');
		my ($fl,$s1)=(0,' (');
		for my $k(keys %$c) {
			next if grep /^$k$/,('MSG','ID');
			$s1.="$k => ";
			$s1.=(defined($c->{$k}) ? "'".$c->{$k}."'" : '<UNDEF>');
			$s1.=' ';
			$fl=1;
		}
		$s1.=")";
		$s.=$s1 if $fl;
		push @a,$s;
	} @{$self->{MSGS}};
	return \@a;
}

sub print_messages($$$) {
	my ($self,$file,$subst)=@_;
	$file=*STDOUT if !defined($file);
	if (ref(SIRTI::Err::nvl($subst)) eq 'HASH') {
		for my $x(@{$self->get_messages()}) {
			for my $k(keys (%$x)) {
				$x->{$k} = $subst->{$k} if exists $subst->{$k};
			}
		}
	}	
	my $msgs=$self->get_string_messages();
	if (scalar(@$msgs) > 0) {
		my @m=ref($subst) eq 'CODE' ? map { $subst->($_) } @$msgs : @$msgs;
		print $file join("\n",@m),"\n";
	}
}


sub reset_messages($) { $_[0]->{MSGS}=[];}


sub _do_split() {
#	my ($params,$values)=@_;
	SIRTI::Err::internal_error('metodo astratto');
	return [];
}


sub _join_TI_address  {
	SIRTI::Err::internal_error('metodo astratto');
	return undef;
}


sub _do_pre {
	my $self=shift;
	my %params=@_;
	my %outpar=();
	my @addrs=();
	my $globalid=delete  $params{GLOBALID}; 
	$globalid=0 unless defined($globalid);
	SIRTI::Err::check($globalid =~/^\d+$/,$globalid.': parametro GLOBALID non numerico');
	for my $k(keys %params) {
		if ($k eq 'ADDR') {
			push @addrs,{ ID => (0 + $globalid),ADDR => $params{$k}};
		}
		elsif ($k =~ /^ADDR(\d+)$/) {
			push @addrs, { ID => ($1 + $globalid), ADDR => $params{$k}};
		}
		elsif ($k eq 'NUMTELCOMPL') {
			push @addrs,{ ID => (0 + $globalid),NUMTELCOMPL => $params{$k}};
		}		
		elsif ($k =~ /^NUMTELCOMPL(\d+)$/) {
			push @addrs, { ID => ($1 + $globalid), NUMTELCOMPL => $params{$k}};
		}
		else {
			$outpar{$k}=$params{$k};	
		}
	}
	return ( \%outpar , \@addrs );
}
			

sub free() {}

1;

