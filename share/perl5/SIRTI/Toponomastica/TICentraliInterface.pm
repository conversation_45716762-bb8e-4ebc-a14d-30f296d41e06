package SIRTI::Toponomastica::TICentraliInterface;

use strict;
use warnings;
use SIRTI::Err;

sub new {
	my $class=shift;
	my %params=@_;
	#  NO_INPUT_FIELDS_INTO_MSG   => nei messaggi di errori non emette i campi di input
	#  EMIT_MSG_ON_OK			  => aggiunge il messaggio in caso di ok
	$params{MSGS}=[];
	my $self=\%params;
	my $c = bless $self,$class;
	return $c;
}


#   VALUES { keys: COMUNE IDCOMUNE FRAZIONE IDFRAZIONE IDSTRADA PARTTOPON DESCINDIRIZZO NUMCIVICO ESPONENTE COLORE PARITA }
#

sub get_distance {
	my $self=shift;
	$self->{MSGS} = $self->_get_distance(@_);
	return $self->{MSGS};
}

sub get_messages($) { return $_[0]->{MSGS};}


sub get_string_messages($)  {
	my $self=shift;
	my @a=();
	map {
		my $c=$_;
		my $s='';
		$s.=$c->{ID}.':' if defined($c->{ID}); 
		$s.=SIRTI::Err::nvl($c->{MSG},$c->{MSG},'<NOMSG>');
		my ($fl,$s1)=(0,' (');
		for my $k(keys %$c) {
			next if grep /^$k$/,('MSG','ID');
			$s1.="$k => ";
			$s1.=(defined($c->{$k}) ? "'".$c->{$k}."'" : '<UNDEF>');
			$s1.=' ';
			$fl=1;
		}
		$s1.=")";
		$s.=$s1 if $fl;
		push @a,$s;
	} @{$self->{MSGS}};
	return \@a;
}

sub print_messages($$$) {
	my ($self,$file,$subst)=@_;
	$file=*STDOUT if !defined($file);
	if (ref(SIRTI::Err::nvl($subst)) eq 'HASH') {
		for my $x(@{$self->get_messages()}) {
			for my $k(keys (%$x)) {
				$x->{$k} = $subst->{$k} if exists $subst->{$k};
			}
		}
	}	
	my $msgs=$self->get_string_messages();
	if (scalar(@$msgs) > 0) {
		my @m=ref($subst) eq 'CODE' ? map { $subst->($_) } @$msgs : @$msgs;
		print $file join("\n",@m),"\n";
	}
}


sub reset_messages($) { $_[0]->{MSGS}=[];}

sub free() {}

1;

