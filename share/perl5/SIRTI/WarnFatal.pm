package SIRTI::WarnFatal;

require Exporter;
use Carp;
@ISA = ('Exporter');
@EXPORT_OK = qw(verbose);
@EXPORT_FAIL = qw(verbose);     # hook to enable verbose mode


$Verbose = 0; 

sub export_fail {
    shift;
    $Verbose = shift if $_[0] eq 'verbose';  
    return @_;
}


$SIG{__WARN__}=sub {
	print STDERR "WARNING is ERROR: ";
	if ($Verbose) { 
		confess @_;
	}
	else {
		croak @_;
	}
	$!=255;
	die;
};

1;
