package SIRTI::XMLParser::DataStyles::GenericDataStyle;

# public
sub new($) {
	my $class=shift;
	return bless {},$class;
}


sub start($$$$) {
	my ($self,$h,$p,$data)=@_;
	return undef;
}


sub end($$$$) {
	my ($self,$h,$p,$data)=@_;
	return undef;
}


sub bang_start($$$$) {
	my ($self,$h,$p,$data)=@_;
	return undef;
}


sub bang_end($$$$) {
	my ($self,$h,$p,$data)=@_;
	return undef;
}


sub get_data($) {
	my ($self)=@_;
	return undef;
}

1;

