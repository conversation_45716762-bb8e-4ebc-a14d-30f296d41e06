package SIRTI::XMLParser::DataStyles::Tree;

use base qw(SIRTI::XMLParser::DataStyles::GenericDataStyle);


sub _createtree($$) {
	my ($root,$context)=@_;
	my $p=$root;
	for my $n(@{$context}) {
		$$p->{$n}=undef if !exists($$p->{$n});
		$p=\$$p->{$n};
	}
	return $p;
}


# public 

sub new  {
	my $class=shift;
	my %self=@_;
	$self{LABELS}={} if !defined($self{LABELS});
	$self{IGNORENULLVALUES}=0 if (!defined($self{IGNORENULLVALUES}));
	$self->{DATA}={} if (!defined($self->{DATA}));
	return bless \%self,$class;
}


sub get_labels($) { return $_[0]->{LABELS};}

sub isignorenullvalues($) { return $_[0]->{IGNORENULLVALUES};}


sub start($$$$) {
	my ($self,$h,$p,$data)=@_;
	if ($h eq 'start') {		# inizio raccolta dati
		$self->{CDATA}='';
	}
	elsif ($h eq 'end') {		# fine raccolta dati
		my $ref=_createtree(\$self->{DATA},$p->{Context});
		if (!$self->{IGNORENULLVALUES} || $self->{CDATA} !~/^\s*$/) {
			if (defined($self->{LABELS}->{VALUE})) {
				$$ref->{$data}->{$self->{LABELS}->{VALUE}}=$self->{CDATA}
			}
			else {
				$$ref->{$data}=$self->{CDATA};			
			}
		}
		$self->{CDATA}='';
	}
	elsif ($h eq 'char') {		# raccolta dati	
		$self->{CDATA}.=$data;	
	}
	else {}
}


sub get_data($) { return $_[0]->{DATA};}

sub bang_end($$$$) {
	my ($self,$h,$p,$data)=@_;
	$self->{DATA}={};
}


### test 
if (__FILE__ eq $0) {
	package main;
	use Data::Dumper;
	use SIRTI::XMLParser::Triggers::BreakLevel;
	# use SIRTI::XMLParser::DataStyles::Tree;
	use SIRTI::XMLParser::Parser;
	
	push @ARGV,'-' if (scalar(@ARGV) == 0);

	for my $f(@ARGV) {

		if (!open FH1,$f) {
			print STDERR "$f: $!\n";
			exit 1;
		}

		my $p=new SIRTI::XMLParser::Parser(	
				TRIGGER	=>   new SIRTI::XMLParser::Triggers::BreakLevel(LEVEL => 1)	#  condizioni per la chiamata dell' handler
				,HANDLERS		=> {		
					TRIGGER	=>	sub {
						my $tree=shift;
						print Dumper($tree),"\n";			
					}
					,INIT		=> sub {
						print "Init\n"
					}
					,XMLDECL	=> sub {
						print "XmlDecl\n"

					}
					,FINAL	=> sub {
						print "Final\n";

					}
				}
				,DATASTYLE	=>	new SIRTI::XMLParser::DataStyles::Tree(	# struttura ad albero
									LABELS			=> {
										VALUE		=> undef
										,PARAM		=> 'parammmm'
									}
									,IGNORENULLVALUES	=> 1
								)
		);

		$p->parse(*FH1);

		close (FH1);
	}

	exit 0;
}
else {
1;
}

