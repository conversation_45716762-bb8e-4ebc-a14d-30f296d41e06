package SIRTI::XMLParser::DataStyles::XPath;

use base qw(SIRTI::XMLParser::DataStyles::GenericDataStyle);


sub _get_xpath($$) {
	my ($ctx,$sep)=@_;
	my $s=$sep;
	for my $x(@{$ctx}) {
		$s.=$x;
		$s.=$sep;
	}
	return $s;
}

# public 

sub new  {
	my $class=shift;
	my %self=@_;
	$self{SEPARATOR}='/' if !defined($self{SEPARATOR});
	$self{IGNORENULLVALUES}=0 if (!defined($self{IGNORENULLVALUES}));
	$self->{DATA}=[] if (!defined($self->{DATA}));
	return bless \%self,$class;
}


sub get_labels($) { return $_[0]->{LABELS};}

sub isignorenullvalues($) { return $_[0]->{IGNORENULLVALUES};}

sub get_separator($) { return $_[0]->{SEPARATOR};}

sub start($$$$) {
	my ($self,$h,$p,$data)=@_;
	if ($h eq 'start') {			# inizio raccolta dati
		$self->{CDATA}='';
	}
	elsif ($h eq 'end') {			# fine raccolta dati
		if (!$self->{IGNORENULLVALUES} || $self->{CDATA} !~/^\s*$/) {
				my $s=_get_xpath($p->{Context},$self->{SEPARATOR});
				$s.=$data;
				$s.=$self->{SEPARATOR};
				if (defined($self->{LABELS}->{VALUE})) { 
					$s.=$self->{LABELS}->{VALUE};
					$s.= $self->{SEPARATOR};
				}
				$s.= $self->{CDATA};
				push @{$self->{DATA}},$s;
		}
		$self->{CDATA}='';
	}
	elsif ($h eq 'char') {			# raccolta dati
		$self->{CDATA}.=$data;	
	}
	else {}
}


sub get_data($) { 
	my $data=$_[0]->{DATA};
	return undef if scalar(@{$data}) == 0 ;
	return $data->[0] if scalar(@{$data}) == 1;
	return $data;
}

sub bang_end($$$$) {
	my ($self,$h,$p,$data)=@_;
	$self->{DATA}=[];
}


### test 
if (__FILE__ eq $0 ) {
	print $0,"\n";
	use Data::Dumper;
	use SIRTI::XMLParser::Triggers::BreakLevel;
	use SIRTI::XMLParser::Parser;
	
	push @ARGV,'-' if (scalar(@ARGV) == 0);

	for my $f(@ARGV) {

		if (!open FH1,$f) {
			print STDERR "$f: $!\n";
			exit 1;
		}

		my $p=new SIRTI::XMLParser::Parser(	
				TRIGGER	=>   new SIRTI::XMLParser::Triggers::BreakLevel(LEVEL => 1)	#  condizioni per la chiamata dell' handler
				,HANDLERS		=> {		
					TRIGGER	=>	sub {
						my ($d,$self)=@_;
						print Dumper($d),"\n";			
					}
					,INIT		=> sub {
						print "Init\n"
					}
					,XMLDECL	=> sub {
						print "XmlDecl\n"

					}
					,FINAL	=> sub {
						print "Final\n";

					}
				}
				,DATASTYLE	=>	new DBU::XMLParser::DataStyles::XPath(	# struttura a percorso
									LABELS			=> {
										VALUE		=> 'valore'
									}
									,IGNORENULLVALUES	=> 1
									,SEPARATOR			=> ':'
								)
		);

		$p->parse(*FH1);

		close (FH1);
	}

	exit 0
}
else {
1;
}

