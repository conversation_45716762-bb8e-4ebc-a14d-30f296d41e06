package SIRTI::XMLParser::Parser;


#
# attraversa un file xml e in base al trigger impostato produce una stuttura dati dipendente dal   datastyle impostato   
# BUGS - i parametri dei nodi non sono implementati
#        l' oggetto in input deve essere l' handler di file 

use strict;
use warnings;

use XML::Parser;

sub _genhandler($$$) {
	my ($h,$p,$data)=@_;
	my $self=$p->{THISCLASS};
	my ($tr,$ds,$hn)=($self->{TRIGGER},$self->{DATASTYLE},$self->{HANDLERS}->{TRIGGER});
	return undef if !defined($tr) || !defined($ds) || !defined($hn);
	$ds->start($h,$p,$data);
	if ($tr->bang($h,$p,$data)) { 
		$ds->bang_start($h,$p,$data);
		&{$hn}($ds->get_data(),$self);
		$ds->bang_end($h,$p,$data);
	}
	$ds->end($h,$p,$data);

}

sub _init {
	my $self=$_[0]->{THISCLASS};
	$self->{TREEDATA}=undef;
	&{$self->{HANDLERS}->{INIT}}($self)  if defined($self->{HANDLERS}->{INIT})	
}

sub _start {
	my ($p,$data)=@_;
	_genhandler('start',$p,$data);

}

sub _end {
	my ($p,$data)=@_;
	_genhandler('end',$p,$data);
}

sub _char {
	my ($p,$data)=@_;
	_genhandler('char',$p,$data);
	
}


sub _xmldecl {
	my ($p,$version,$encoding,$standalone)=@_;
	my $self=$p->{THISCLASS};
	$self->{XMLDECL}={
		VERSION			=> $version
		,ENCODING		=>	$encoding
		,STANDALONE		=> $standalone
	};
	&{$self->{HANDLERS}->{XMLDECL}}($self)  if defined($self->{HANDLERS}->{XMLDECL})	
}

sub _final($$) {
	my ($p,$data)=@_;
	my $self=$p->{THISCLASS};
	&{$self->{HANDLERS}->{FINAL}}($self)  if defined($self->{HANDLERS}->{FINAL})	
}

# public 
sub new {
	my $class=shift;
	my %self=@_;
	
	$self{PARSER} = new XML::Parser(
					THISCLASS	=>  \%self
					,Handlers => {
									Init		=> \&_init
									,Start 		=> \&_start
									,End   		=> \&_end
									,Char  		=> \&_char
									,XMLDecl	=> \&_xmldecl
									,Final		=> \&_final
					}
					
		);
	
	$self{HANDLERS}={} if !exists($self{HANDLERS});
	return bless \%self,$class;

}

sub parse($$) {
	my ($self,$obj)=@_;
	$self->{PARSER}->parse($obj);
}

sub get_trigger($) {return $_[0]->{TRIGGER};}

sub get_handlers($) { return $_[0]->{HANDLERS};}

sub get_parser($)	{ return $_[0]->{PARSER};}

sub get_datastyle($) { return $_[0]->{DATASTYLE};}

sub get_userdata($) { return $_[0]->{USERDATA};}

sub get_xmldecl($) { return $_[0]->{XMLDECL};}

##### test  #####

if (__FILE__ eq 'Parser.pm') {

	package main;
	use Data::Dumper;

	push @ARGV,'-' if (scalar(@ARGV) == 0);

	for my $f(@ARGV) {

		if (!open FH1,$f) {
			print STDERR "$f: $!\n";
			exit 1;
		}

		my $p=new SIRTI::XMLParser::Parser(	
				TRIGGER	=>  undef
				,HANDLERS		=> {		
					TRIGGER	=>	sub {
						my $tree=shift;
						print Dumper($tree),"\n";			
					}
					,INIT		=> sub {
						print "Init\n"
					}
					,XMLDECL	=> sub {
						my $self=shift;
						print "XmlDecl: ",Dumper($self->get_xmldecl()),"\n";
					}
					,FINAL	=> sub {
						print "Final\n";

					}
				}
				,DATASTYLE	=>	undef
		);

		$p->parse(*FH1);  # non chiama TRIGGER

		close (FH1);
	}

	exit 0
}
else {
	1;
}



