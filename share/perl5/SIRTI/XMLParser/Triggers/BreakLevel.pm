package SIRTI::XMLParser::Triggers::BreakLevel;

use base qw(SIRTI::XMLParser::Triggers::GenericTrigger);


sub new($) {
	my $class=shift;
	my %self=@_;
	$self{LEVEL}=0 if !defined($self{LEVEL});
	return bless \%self,$class;
}


sub get_level($) { return $_[0]->{LEVEL}; }

sub bang($$$$) {
	my ($self,$h,$p,$data)=@_;
	return $h eq 'end' && scalar(@{$p->{Context}}) == $self->{LEVEL} ? 1 : 0;
}


### test
if (__FILE__ eq $0) {
	package main;
	my	$x = new  SIRTI::XMLParser::Triggers::BreakLevel(LEVEL => 1);
	print $x->get_level(),"\n";

}
else {
	1;
}

