package SIRTI::syslog;
use strict;
use warnings;


use Sys::Syslog  qw/:standard  :macros/;
use Carp;

use constant {
	DEFAULT_SYSLOG_IDENT   			=> ''
	,DEFAULT_SYSLOG_OPT				=> ''
	,DEFAULT_SYSLOG_FACILITY		=> ''
};

use base qw/Exporter/;

our @EXPORT= qw (
	DEFAULT_SYSLOG_IDENT  
	DEFAULT_SYSLOG_OPT  
	DEFAULT_SYSLOG_FACILITY
);


sub new {
	my $class=shift;
	my %params=@_;
	$params{SYSLOG_IDENT}=DEFAULT_SYSLOG_IDENT unless defined $params{SYSLOG_IDENT};
	$params{SYSLOG_OPT}=DEFAULT_SYSLOG_OPT unless defined $params{SYSLOG_OPT};
	$params{SYSLOG_FACILITY}=DEFAULT_SYSLOG_FACILITY unless defined $params{SYSLOG_FACILITY};
	return bless \%params,$class;
}

sub open { 
	my $self=shift;
	my %params=@_;
	$self->close;
	for my $k (qw/SYSLOG_IDENT SYSLOG_OPT  SYSLOG_FACILITY/) {
		$params{$k}=$self->{$k} unless defined $params{$k};
	}
	my $r=undef;
	if (length($params{SYSLOG_IDENT}) > 0) {
		$r=openlog($params{SYSLOG_IDENT},$params{SYSLOG_OPT},$params{SYSLOG_FACILITY}) || croak $!;
		$self->{__OPENED__}=1;
	}
	return undef;
}

sub log {
	my $self=shift;
	my $pri=shift;
	return $self->{__OPENED__} ? syslog($pri,@_) : undef;  
}

sub close {
	my $self=shift;
	if ($self->{__OPENED__}) {
		closelog; 
		delete $self->{__OPENED__};
	}
}

sub  DESTROY {
	my $self=shift;
	$self->close;
}


if ($0 eq __FILE__) {
	my $syslog=SIRTI::syslog->new(SYSLOG_IDENT => 'prova',SYSLOG_OPT => 'pid,cons');
	$syslog->open;
	$syslog->log(LOG_INFO,'prrrrrrrrr!');


}


1;




